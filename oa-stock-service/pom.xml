<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>oa-stock</artifactId>
        <groupId>com.jiuji.oa.stock</groupId>
        <version>1.1.2</version>
    </parent>
    <artifactId>oa-stock-service</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>
    <dependencies>

        <!-- 9机的工具包合集 -->
        <dependency>
            <groupId>com.ch999.common</groupId>
            <artifactId>utils</artifactId>
            <version>1.7-SNAPSHOT</version>
        </dependency>

        <!--延迟队列接入-->
        <dependency>
            <groupId>com.jiuji.infra</groupId>
            <artifactId>common-delay-queue</artifactId>
        </dependency>

        <!--九机cloud调用-->
        <dependency>
            <groupId>com.jiuji.oa.logistics</groupId>
            <artifactId>logistics-cloud</artifactId>
            <version>1.0.3-SNAPSHOT</version>
        </dependency>

        <!--回收cloud调用-->
        <dependency>
            <groupId>com.jiuji.huishou</groupId>
            <artifactId>huishou-cloud</artifactId>
            <version>0.0.9-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.jiuji.oa</groupId>
            <artifactId>oa-finance-stub</artifactId>
            <version>1.1-SNAPSHOT</version>
        </dependency>
        <!--人力cloud调用-->
        <dependency>
            <groupId>com.jiuji.cloud</groupId>
            <artifactId>org-cloud</artifactId>
            <version>0.0.18-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.jiuji.oaapi</groupId>
            <artifactId>oaapi-cloud</artifactId>
            <version>0.1.7-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.jiuji.oa</groupId>
            <artifactId>oa-order-stub</artifactId>
            <version>1.0.4-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.jiuji.oa</groupId>
            <artifactId>nc-segments-stub</artifactId>
            <version>1.6-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.jiuji.cloud</groupId>
            <artifactId>after-cloud</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>

        <!--wms远程调用-->
        <dependency>
            <groupId>com.jiuji.oa.stock</groupId>
            <artifactId>logistics-api</artifactId>
            <version>1.1-SNAPSHOT</version>
        </dependency>
        <!--office api 调用-->
        <dependency>
            <groupId>com.jiuji.oa.office</groupId>
            <artifactId>oa-office-api</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.jiuji.cloud</groupId>
            <artifactId>after-cloud</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
        <groupId>com.jiuji.cloud</groupId>
        <artifactId>oa-office-cloud</artifactId>
        <version>1.0.7-SNAPSHOT</version>
        </dependency>
        <!--Geek+接口平台SDK-->
        <dependency>
            <groupId>com.geekplus</groupId>
            <artifactId>artemis-sdk</artifactId>
            <version>1.0.0</version>
        </dependency>
        <!--easyexcl引入以及所需要使用的东西-->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>3.17</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>3.17</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>2.2.6</version>
        </dependency>
        <!--easyexcl引入以及所需要使用的东西-->
        <dependency>
            <groupId>com.zto.zop</groupId>
            <artifactId>zopsdk</artifactId>
            <version>0.6</version>
        </dependency>

        <dependency>
            <artifactId>orginfo-stub</artifactId>
            <groupId>com.jiuji.oa</groupId>
            <version>1.2.30-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.alicp.jetcache</groupId>
            <artifactId>jetcache-starter-redis</artifactId>
            <version>2.5.14</version>
        </dependency>
        <dependency>
            <groupId>com.alicp.jetcache</groupId>
            <artifactId>jetcache-starter-redis-lettuce</artifactId>
            <version>2.5.11</version>
        </dependency>
        <dependency>
            <artifactId>foundation-rabbitmq-starter</artifactId>
            <groupId>com.jiuji.tc</groupId>
            <version>${jiuji.foundation.version}</version>
        </dependency>
        <dependency>
            <artifactId>foundation-message-starter</artifactId>
            <groupId>com.jiuji.tc</groupId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
            <version>3.8.2</version>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
            <version>3.8.2</version>
        </dependency>
        <!--京东Jackson-->
        <dependency>
            <groupId>org.codehaus.jackson</groupId>
            <artifactId>jackson-mapper-asl</artifactId>
            <version>1.9.13</version>
        </dependency>
        <!--京东sdk-->
        <dependency>
            <groupId>com.jd.open.api</groupId>
            <artifactId>open-api-sdk</artifactId>
            <version>2.0</version>
        </dependency>
        <dependency>
            <groupId>com.jiuji.oa.stock</groupId>
            <artifactId>oa-stock-api</artifactId>
            <version>1.2.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.jiuji.oa.stock</groupId>
            <artifactId>baozun-util</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
        </dependency>
        <dependency>
            <groupId>dom4j</groupId>
            <artifactId>dom4j</artifactId>
            <version>1.6.1</version>
        </dependency>
        <dependency>
            <artifactId>oa-stock-cloud</artifactId>
            <groupId>com.jiuji.stock</groupId>
            <version>1.2.0</version>
        </dependency>
        <dependency>
            <groupId>com.jiuji.infra</groupId>
            <artifactId>common-utils</artifactId>
            <version>0.0.6-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.jiuji.oa</groupId>
            <artifactId>loginfo-stub</artifactId>
            <version>1.2-SNAPSHOT</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.github.rholder/guava-retrying -->
        <dependency>
            <groupId>com.github.rholder</groupId>
            <artifactId>guava-retrying</artifactId>
            <version>2.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.framework.apollo</groupId>
            <artifactId>apollo-client</artifactId>
            <version>1.7.0</version>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-ui</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jiuji.stock</groupId>
            <artifactId>oa-stock-cloud</artifactId>
            <version>1.2.1-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <!-- 太力商城 -->
        <dependency>
            <groupId>tlmall</groupId>
            <artifactId>ka-http-sdk</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.jiuji.oa.stock</groupId>
            <artifactId>logistics-vo</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
    </dependencies>

    <build>
        <!-- service作为应用提供服务。不需要引入版本号。-->
        <finalName>${project.artifactId}</finalName>
        <plugins>

            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <!-- 跳过单元测试 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <!-- Docker maven plugin -->
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <version>1.2.2</version>
                <configuration>
                    <image>harbor.saas.ch999.cn:1088/java/${project.artifactId}</image>
                    <dockerDirectory>src/main/docker</dockerDirectory>
                    <newName>harbor.saas.ch999.cn:1088/java/${project.artifactId}</newName>
                    <resources>
                        <resource>
                            <targetPath>/</targetPath>
                            <directory>${project.build.directory}</directory>
                            <include>${project.build.finalName}.jar</include>
                            <include>classes/configTemplate/</include>
                        </resource>
                    </resources>
                </configuration>
                <!-- Docker maven plugin -->
            </plugin>
        </plugins>
    </build>
</project>
