<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.predistribution.mapper.DiaoboPredistributionConfigMapper">

    <select id="getConfigListPage" resultType="com.jiuji.oa.stock.predistribution.vo.res.DiaoboPredistributionConfigPageRes">
        SELECT
        b.predistribution_config_id,b.detail_ppriceid, b.detail_product_name, b.detail_product_color, b.detail_cid, b.detail_amount,
        a.id, a.main_ppriceid, a.main_product_name, a.main_product_color,a.main_cid,b.id as detailId
        FROM diaobo_predistribution_config_detail b with (nolock)
        left join diaobo_predistribution_config a with (nolock) on a.id = b.predistribution_config_id
        <where> b.is_deleted = 0 and a.is_deleted = 0
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==1">
                and a.main_ppriceid = #{req.searchValue}
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==2">
                and b.detail_ppriceid = #{req.searchValue}
            </if>
            <if test="req.cid != null and req.cid.size()>0">
                AND a.main_cid in
                <foreach item="item" index="index" collection="req.cid" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by a.id desc,b.id desc
    </select>

    <select id="getConfigCommentListPage" resultType="com.jiuji.oa.stock.predistribution.vo.res.DiaoboPredistributionConfigCommentPageRes">
        SELECT id, predistribution_config_id, inuser_id, inuser, comment, operation_type,
               create_time, update_time,is_deleted
        FROM diaobo_predistribution_config_comment a with(nolock)
        <where> a.is_deleted = 0
            <if test="req.comment!=null and req.comment!=''">
                and a.comment like CONCAT('%',#{req.comment},'%')
            </if>
            <if test="req.predistributionConfigId!=null and req.predistributionConfigId!=''">
                and a.predistribution_config_id = #{req.predistributionConfigId}
            </if>
        </where>
        order by a.create_time desc
    </select>

    <select id="getProductGiftList" resultType="java.lang.String">
        SELECT ppriceids as ppriceid from ProductGift with(nolock)
    </select>

    <select id="getVirtualCidList" resultType="java.lang.String">
        SELECT id from Category with(nolock) where isVirtualGoods = 1
    </select>

    <select id="selectRepairCategoryChildren" resultType="java.lang.String">
        SELECT ID
        from f_category_children('23')
    </select>

</mapper>
