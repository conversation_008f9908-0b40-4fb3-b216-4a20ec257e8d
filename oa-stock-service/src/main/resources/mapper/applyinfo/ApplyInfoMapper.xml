<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~
  ~      Copyright © 2006 - 2020 九机网 All Rights Reserved
  ~
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.applyinfo.mapper.ApplyInfoMapper">

    <resultMap id="tApplyinfoMap" type="com.jiuji.oa.applyinfo.entity.ApplyInfo">
        <id property="applyid" column="ApplyId"/>
        <result property="categoryid" column="CategoryId"/>
        <result property="scategoryid" column="sCategoryId"/>
        <result property="inuser" column="InUser"/>
        <result property="inusername" column="InUserName"/>
        <result property="checker" column="Checker"/>
        <result property="checkername" column="CheckerName"/>
        <result property="area" column="Area"/>
        <result property="applytime" column="ApplyTime"/>
        <result property="remark" column="Remark"/>
        <result property="currentstatus" column="CurrentStatus"/>
        <result property="inuserdepartment" column="InUserDepartment"/>
        <result property="attachfiles" column="AttachFiles"/>
        <result property="applytitle" column="ApplyTitle"/>
        <result property="isdel" column="IsDel"/>
        <result property="id" column="Id"/>
        <result property="amount" column="amount"/>
        <result property="relatedapply" column="RelatedApply"/>
        <result property="haspreload" column="HasPreLoad"/>
        <result property="stime" column="sTime"/>
        <result property="etime" column="eTime"/>
        <result property="attchfiles1" column="AttchFiles1"/>
        <result property="oldinuserdepartment" column="oldInUserDepartment"/>
        <result property="zhichuid" column="zhichuid"/>
        <result property="acceptuser" column="acceptuser"/>
        <result property="acceptuserid" column="acceptuserid"/>
        <result property="areaid" column="AreaId"/>
        <result property="attchfiles1tmp" column="AttchFiles1tmp"/>
        <result property="attachfilestmp" column="AttachFilestmp"/>
        <result property="isup" column="isup"/>
        <result property="kfly" column="kfly"/>
        <result property="kfyjxg" column="kfyjxg"/>
        <result property="kfmd" column="kfmd"/>
        <result property="wishtime" column="WishTime"/>
        <result property="planfinishtime" column="PlanFinishTime"/>
        <result property="usehours" column="UseHours"/>
        <result property="priority" column="Priority"/>
        <result property="notifyceo" column="NotifyCEO"/>
        <result property="daipeiuser" column="daipeiUser"/>
        <result property="zhiji" column="zhiji"/>
        <result property="kaoqintype" column="kaoqinType"/>
        <result property="stime0" column="sTime0"/>
        <result property="etime0" column="eTime0"/>
        <result property="lastapplytime" column="lastApplyTime"/>
        <result property="lastcount" column="lastCount"/>
        <result property="curcount" column="curCount"/>
        <result property="mobile" column="mobile"/>
        <result property="usertype" column="userType"/>
        <result property="processstats" column="processStats"/>
        <result property="gytype" column="gyType"/>
        <result property="businessdestination" column="businessDestination"/>
        <result property="buyticket" column="buyTicket"/>
        <result property="tickettype" column="ticketType"/>
        <result property="relatedids" column="relatedids"/>
        <result property="dailizhiwu" column="dailizhiwu"/>
        <result property="isnewflag" column="isNewFlag"/>
        <result property="zhichuids" column="zhichuids"/>
        <result property="isapplyforsomeone" column="IsApplyForSomeone"/>
        <result property="realapplicant" column="RealApplicant"/>
        <result property="realapplicantid" column="RealApplicantId"/>
        <result property="qudaochannelid" column="QudaoChannelId"/>
        <result property="inuserdepartcode" column="inuserDepartCode"/>
        <result property="departbianzhi" column="departBianZhi"/>
        <result property="giftcash" column="giftCash"/>
        <result property="flightnumber" column="flightNumber"/>
        <result property="takeofftime" column="takeoffTime"/>
        <result property="flightnumber2" column="flightNumber2"/>
        <result property="takeofftime2" column="takeoffTime2"/>
        <result property="arrivaltime" column="ArrivalTime"/>
        <result property="areaorganizekind" column="areaOrganizeKind"/>
        <result property="togther" column="togther"/>
        <result property="inuserdepartmentbak" column="InUserDepartmentBak"/>
        <result property="inuserdepartcodebak" column="inuserDepartCodeBak"/>
    </resultMap>


    <select id="maxIdOfDate" resultType="java.lang.Long">
        select max(ID) from T_ApplyInfo where ID like CONCAT  ('',#{date},'%')
    </select>
    <select id="getByChannelId" resultType="java.lang.Long">
        select MAX(ID) id from T_ApplyInfo where IsDel = 0 and QudaoChannelId = #{id} ORDER BY Id desc
    </select>
</mapper>
