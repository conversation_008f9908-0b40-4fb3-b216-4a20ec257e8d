<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.logistics.order.mapper.AbnormalMonitorFlowMapper">

    <select id="getDuidiaoCount" resultType="java.lang.Integer">
        SELECT
            sum(case when operation_type = 1 then 1 else 0 end) as ddcount
        FROM
            mkc_operation_records m with(nolock)
            left join ch999_user cu WITH(NOLOCK) on cu.ch999_name= m.employee_name
        WHERE
            m.area_id not in (16, 113)
          AND m.create_time >= format(DATEADD(day,-90,GETDATE()),'yyyy-MM-dd')
          AND mkc_id = #{mkcId}
          and cu.area1id not in (16, 113)
        GROUP BY
            mkc_id
    </select>
    <select id="getSuodingCount" resultType="java.lang.Integer">
        SELECT
            sum(case when operation_type = 2 then 1 else 0 end) as sdcount
        FROM
            mkc_operation_records m with(nolock)
        WHERE
            m.create_time >= format(DATEADD(day,-90,GETDATE()),'yyyy-MM-dd')
          AND mkc_id = #{mkcId}
        GROUP BY
            mkc_id
    </select>
    <select id="getDaodianCount" resultType="java.lang.Integer">
        select COUNT(1) as daodianCount from mkc_toarea mt with(nolock)
        where toareaid = #{areaId} and dtime >= format(DATEADD(day,-90,GETDATE()),'yyyy-MM-dd')
          and mkc_id = #{mkcId}
        GROUP by mkc_id
    </select>
</mapper>
