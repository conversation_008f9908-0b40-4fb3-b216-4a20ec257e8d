<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.stock.logistics.order.mapper.AbnormalMonitorMapper">
  <sql id="withSql">
    with _z as (
        SELECT
        a.id,
        a.mkc_id,
        a.deal_flag,
        a.monitor_type,
        a.business_day,
        a.create_time,
        a.deal_user_id,
        a.deal_user_name
        from
        abnormal_monitor_flow a WITH(NOLOCK)
        where a.del_flag = 0
        <if test="param.dealFlag!=null and param.dealFlag!='0'">
            and a.deal_flag = #{param.dealFlag}
        </if>
        <if test="param.monitorType != null and param.monitorType.size() > 0">
             and a.monitor_type in
            <foreach collection="param.monitorType" index="index" item="it" separator="," open="(" close=")">
              #{it}
            </foreach>
        </if>
        <if test="param.mkcId!=null and param.mkcId!=0">
            and a.mkc_id = #{param.mkcId}
        </if>
        <if test="param.startTime!=null and param.endTime!=null">
            and a.create_time between #{param.startTime} and #{param.endTime}
        </if>
    ),_a as ( SELECT
        _z.id,
        _z.deal_flag,
        _z.monitor_type,
        _z.business_day,
        _z.create_time,
        _z.deal_user_id,
        _z.deal_user_name,
        m.mkc_id,
        m.area_id,
    case when m.operation_type = 1 then 1 else 0 end as ddcount,
    case when m.operation_type = 2 then 1 else 0 end as sdcount
    FROM
        _z,
        mkc_operation_records m WITH(NOLOCK)
    where m.area_id not in (16, 113)
    and m.mkc_id = _z.mkc_id ), _b as ( select
      _a.mkc_id,
      _a.id,
      _a.deal_flag,
      _a.monitor_type,
      _a.business_day,
      _a.create_time,
      _a.deal_user_id,
      _a.deal_user_name,
      sum(ddcount) dd,
      sum(sdcount) sd
    from
    _a, product_mkc b WITH(NOLOCK)
    <where>
      _a.mkc_id=b.id
      <if test="param.imei!=null and param.imei!=''">and b.imei = #{param.imei}</if>
    </where>
    group by
    _a.mkc_id,
    _a.id,
    _a.deal_flag,
    _a.create_time,
    _a.monitor_type,
    _a.business_day,
    _a.deal_user_id,
    _a.deal_user_name
    )
  </sql>
  <select id="listAbnormalMonitor"
    resultType="com.jiuji.oa.stock.logistics.order.vo.res.AbnormalMonitorRes">
      SELECT
      a.id,
      a.deal_flag,
      a.monitor_type,
      a.business_day,
      a.create_time,
      a.deal_user_id,
      a.deal_user_name,
      a.mkc_id,
      c.ppriceid as ppid,
      c.productid AS productId,
      a.area_id as ddAreaId,
      b.imeidate as instockTime,
      b.transfer_in_datetime,
      b.kc_check,
      b.orderid,
      ISNULL((SELECT
      sum(case when operation_type = 1 then 1 else 0 end) as ddcount
      FROM
      mkc_operation_records m WITH(NOLOCK)
      left join ch999_user cu WITH(NOLOCK) on cu.ch999_name= m.employee_name
      WHERE
      m.area_id not in (16, 113)
      and m.mkc_id = a.mkc_id
      and m.create_time >= format(DATEADD(day,-90,a.create_time),'yyyy-MM-dd')
      and cu.area1id not in (16, 113)
      GROUP BY
      m.mkc_id),0) as d_count,
      ISNULL(( SELECT
      sum(case when operation_type = 2 then 1 else 0 end) as sdcount
      FROM
      mkc_operation_records m WITH(NOLOCK)
      WHERE
      m.mkc_id = a.mkc_id and m.create_time >= format(DATEADD(day,-90,a.create_time),'yyyy-MM-dd')
      GROUP BY
      m.mkc_id),0) as l_count,
      b.imei,
      c.product_name,
      c.product_color,
      c.cid,
      cg.Name as cidName,
      bd.name as brandName,
      c.brandid,
      d.area,
      d.id as areaId,
      ISNULL((select COUNT(1) as daodianCount from mkc_toarea mt WITH(NOLOCK)
      where mt.toareaid = a.area_id
      and mt.dtime >= format(DATEADD(day,-90,a.create_time),'yyyy-MM-dd')
      and mt.stats in (3)
      and mt.mkc_id = a.mkc_id
      GROUP by mt.mkc_id),0) as dd_count
      FROM
      abnormal_monitor_flow a WITH(NOLOCK),
      product_mkc b WITH(NOLOCK),
      productinfo c WITH(NOLOCK),
      areainfo d WITH(NOLOCK),
      category cg WITH (NOLOCK),
      brand bd with (nolock)
      where
      a.mkc_id = b.id
      and b.ppriceid = c.ppriceid
      and c.cid = cg.ID
      and bd.id = c.brandID
      and b.areaid = d.id
      and isnull(b.mouldFlag, 0) = 0
      and not exists( select 1 from xc_mkc x with(nolock) where x.mkc_id = b.id )
      <if test="param.imei!=null and param.imei!=''">and b.imei = #{param.imei}</if>
      <if test="param.dealFlag!=null and param.dealFlag==0">
          and a.deal_flag = 0
      </if>
      <if test="param.dealFlag!=null and param.dealFlag==1">
          and a.deal_flag = 1
      </if>
      <if test="param.kcCheckList != null and param.kcCheckList.size > 0 ">
          and b.kc_check in
          <foreach collection="param.kcCheckList" item="it" separator="," open="(" close=")">
              #{it}
          </foreach>
      </if>
      <if test="param.monitorType != null and param.monitorType.size() > 0">
          and a.monitor_type in
          <foreach collection="param.monitorType" index="index" item="it" separator="," open="(" close=")">
              #{it}
          </foreach>
      </if>
      <if test="param.mkcId!=null and param.mkcId!=0">
          and a.mkc_id = #{param.mkcId}
      </if>
      <if test="param.startTime!=null and param.endTime!=null">
          and a.create_time between #{param.startTime} and #{param.endTime}
      </if>
    <if test="param.areaIdList!=null and param.areaIdList.size > 0">
         and b.areaid in
          <foreach collection="param.areaIdList" item="it" separator="," open="(" close=")">
              #{it}
          </foreach>
    </if>
    <if test="param.brandIds != null and param.brandIds.size() > 0">
        and c.brandid in
        <foreach collection="param.brandIds" index="index" item="it" separator="," open="(" close=")">
            #{it}
        </foreach>
    </if>
    <if test="param.dCount!=null and param.dCount!=0">and ISNULL((SELECT
        sum(case when operation_type = 1 then 1 else 0 end) as ddcount
        FROM
        mkc_operation_records m WITH(NOLOCK)
        left join ch999_user cu WITH(NOLOCK) on cu.ch999_name= m.employee_name
        WHERE
        m.area_id not in (16, 113)
        and m.mkc_id = a.mkc_id
        and m.create_time >= format(DATEADD(day,-90,a.create_time),'yyyy-MM-dd')
        and cu.area1id not in (16, 113)
        GROUP BY
        m.mkc_id),0) &gt;= #{param.dCount}</if>
    <if test="param.lCount!=null and param.lCount!=0">and ISNULL(( SELECT
        sum(case when operation_type = 2 then 1 else 0 end) as sdcount
        FROM
        mkc_operation_records m WITH(NOLOCK)
        WHERE
        m.mkc_id = a.mkc_id and m.create_time >= format(DATEADD(day,-90,a.create_time),'yyyy-MM-dd')
        GROUP BY
        m.mkc_id),0) &gt;= #{param.lCount}</if>
    <if test="param.ddCount!=null and param.ddCount!=0">and ISNULL((select COUNT(1) as daodianCount from mkc_toarea mt WITH(NOLOCK)
        where mt.toareaid = a.area_id
        and mt.stats in (3)
        and mt.dtime >= format(DATEADD(day,-90,a.create_time),'yyyy-MM-dd')
        and mt.mkc_id = a.mkc_id
        GROUP by mt.mkc_id),0) &gt;= #{param.ddCount}</if>
    order by 1 desc
  </select>
  <select id="countAbnormalMonitor" resultType="java.lang.Integer">
    <include refid="withSql"/>
    select count(1) FROM _b
    <where>
      <if test="param.dCount!=null and param.dCount!=0">and dd &gt;= #{param.dCount}</if>
      <if test="param.lCount!=null and param.lCount!=0">and sd &gt;= #{param.lCount}</if>
    </where>
  </select>
</mapper>
