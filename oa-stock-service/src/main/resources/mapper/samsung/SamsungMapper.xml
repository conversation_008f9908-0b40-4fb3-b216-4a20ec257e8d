<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.stock.samsung.mapper.SamsungMapper">
    <select id="getSftpInfo" resultType="com.jiuji.oa.stock.samsung.dto.SftpInfoDto">
        select top 1 sakc.appapiUrl appApiUrl,sakc.appkey appKey,sakc.appsecret appSecret from sysAppkeyConfig sakc with(nolock)
        where sakc.appkeyType = #{appkeyType}
    </select>
    <sql id="inAreaIdForEach">
        <foreach collection="uploadInBalance.areaIdShopIdMap.keySet()" item="areaId" open="(" separator="," close=")">
            #{areaId}
        </foreach>
    </sql>
    <sql id="weiXiuPjCids">
        (select f.id from f_category_children(23) f)
    </sql>
    <sql id="samsungBrankPpid">
        (select p.ppriceid from dbo.productinfo p with(nolock) where p.brandID=2 and p.cid not in <include refid="weiXiuPjCids"></include>)
    </sql>

    <select id="listInboundDataBySKUAndType" resultType="java.util.Map">
        <!--大件采购入库，包括样机-->
        select FORMAT(k.imeidate, 'yyyyMMdd') "GR_YMD", k.areaid "SHOP_ID",k.ppriceid "SKU", count(1) "GR_QTY", sum(k.inbeihuoprice) "GR_AMT",
               case when k.mouldFlag = 1 then 2 else 1 end "GR_TYPE"
        from dbo.product_mkc k with(nolock)
        where k.imeidate &gt;= #{uploadInBalance.geStartTime} and k.imeidate &lt; #{uploadInBalance.ltEndTime}
          and k.imei is not null and not exists(select 1 from dbo.mkc_toarea m with(nolock) where m.toareaid=k.areaid and k.id=m.mkc_id and m.stats in(0,1,2))
          and k.ppriceid in <include refid="samsungBrankPpid"></include>
          and k.areaid in <include refid="inAreaIdForEach"></include>
        group by FORMAT(k.imeidate, 'yyyyMMdd'), k.areaid, k.ppriceid,case when k.mouldFlag = 1 then 2 else 1 end
        union all
        <!--小件采购入库-->
        select FORMAT(rs.dtime, 'yyyyMMdd') "GR_YMD", s.areaid "SHOP_ID",rb.ppriceid "SKU", sum(rb.lcount) "GR_QTY", sum(rb.lcount*b.inprice) "GR_AMT",
        1 "GR_TYPE"
        from dbo.caigouInputBasket rb with(nolock)
            left join dbo.caigouInputSub rs with(nolock) on rb.inputId=rs.id
            left join dbo.caigou_sub s with(nolock) on rs.caigouId=s.id
            left join dbo.caigou_basket b with(nolock) on s.id=b.sub_id and b.id = rb.cgBasketId
        where rs.dtime &gt;= #{uploadInBalance.geStartTime} and rs.dtime &lt; #{uploadInBalance.ltEndTime}
            and rb.ppriceid in <include refid="samsungBrankPpid"></include>
            and s.areaid in <include refid="inAreaIdForEach"></include>
        group by FORMAT(rs.dtime, 'yyyyMMdd'), s.areaid, rb.ppriceid
        union all
        <!--大件调拨入库-->
        select "GR_YMD","SHOP_ID","SKU", sum("GR_QTY") "GR_QTY", sum("GR_AMT") "GR_AMT","GR_TYPE"
        from
            (
                select FORMAT(m.recivedtime, 'yyyyMMdd') "GR_YMD", m.toareaid "SHOP_ID",k.ppriceid "SKU", 1 "GR_QTY", k.inbeihuoprice "GR_AMT",
                    3 "GR_TYPE",ROW_NUMBER() OVER (PARTITION BY m.mkc_id ORDER BY m.recivedtime DESC) AS RowNum
                from dbo.mkc_toarea m with(nolock)
                    left join dbo.product_mkc k with(nolock) on m.mkc_id=k.id
                where m.stats in (3)
                    and m.recivedtime &gt;= #{uploadInBalance.geStartTime} and m.recivedtime &lt; #{uploadInBalance.ltEndTime}
                    and k.ppriceid in <include refid="samsungBrankPpid"></include>
                    and m.toareaid in <include refid="inAreaIdForEach"></include>
            ) a where a.RowNum = 1
        group by GR_YMD, SHOP_ID, SKU, GR_TYPE
        union all
        <!--大件调拨出库-->
        select "GR_YMD","SHOP_ID","SKU", sum("GR_QTY") "GR_QTY", sum("GR_AMT") "GR_AMT","GR_TYPE"
        from
            (
                select FORMAT(m.sendtime, 'yyyyMMdd') "GR_YMD", m.areaid "SHOP_ID",k.ppriceid "SKU", 1 "GR_QTY", k.inbeihuoprice "GR_AMT",
                4 "GR_TYPE",ROW_NUMBER() OVER (PARTITION BY m.mkc_id ORDER BY m.recivedtime DESC) AS RowNum
                from dbo.mkc_toarea m with(nolock)
                left join dbo.product_mkc k with(nolock) on m.mkc_id=k.id
                where m.stats in (2,3)
                and m.sendtime &gt;= #{uploadInBalance.geStartTime} and m.sendtime &lt; #{uploadInBalance.ltEndTime}
                and k.ppriceid in <include refid="samsungBrankPpid"></include>
                and m.areaid in <include refid="inAreaIdForEach"></include>
            ) a where a.RowNum = 1
        group by GR_YMD, SHOP_ID, SKU, GR_TYPE
        union all
        <!--小件调拨入库-->
        select FORMAT(s.ruku_dtime, 'yyyyMMdd') "GR_YMD", s.toareaid "SHOP_ID",b.ppriceid "SKU", sum(b.lcount) "GR_QTY", sum(b.lcount*b.inprice) "GR_AMT",
        3 "GR_TYPE"
        from dbo.diaobo_basket b with(nolock)
        left join dbo.diaobo_sub s with(nolock) on s.id=b.sub_id
        where s.stats in (4)
            and s.ruku_dtime &gt;= #{uploadInBalance.geStartTime} and s.ruku_dtime &lt; #{uploadInBalance.ltEndTime}
            and b.ppriceid in <include refid="samsungBrankPpid"></include>
            and s.toareaid in <include refid="inAreaIdForEach"></include>
        group by FORMAT(s.ruku_dtime, 'yyyyMMdd'), s.toareaid, b.ppriceid
        union all
        <!--小件调拨出库-->
        select FORMAT(s.send_dtime, 'yyyyMMdd') "GR_YMD", s.areaid "SHOP_ID",b.ppriceid "SKU", sum(b.lcount) "GR_QTY", sum(b.lcount*b.inprice) "GR_AMT",
        4 "GR_TYPE"
        from dbo.diaobo_basket b with(nolock)
        left join dbo.diaobo_sub s with(nolock) on s.id=b.sub_id
        where s.stats in (3,4)
            and s.send_dtime &gt;= #{uploadInBalance.geStartTime} and s.send_dtime &lt; #{uploadInBalance.ltEndTime}
            and b.ppriceid in <include refid="samsungBrankPpid"></include>
            and s.areaid in <include refid="inAreaIdForEach"></include>
        group by FORMAT(s.send_dtime, 'yyyyMMdd'),s.areaid, b.ppriceid
    </select>
    <select id="listOutboundDataBySKUAndType" resultType="java.util.Map">
        <!--大件销售-->
        select FORMAT(s.tradeDate1, 'yyyyMMdd') "SO_YMD", s.areaid "SHOP_ID",b.ppriceid "SKU", count(1) "SO_QTY", sum(p.memberprice) "SO_AMT",
        case when k.mouldFlag = 1 then 2 else 1 end "SO_TYPE"
        from dbo.basket b with(nolock)
            left join dbo.sub s with(nolock) on s.sub_id = b.sub_id
             left join dbo.product_mkc k with(nolock) on b.basket_id=k.basket_id and k.basket_id is not null and k.kc_check=5
             left join dbo.productinfo p with(nolock) on p.ppriceid=b.ppriceid
        where isnull(b.isdel,0)=0 and b.ismobile=1 and s.sub_check in (3,9) and p.brandID=2
            and s.tradeDate1 &gt;= #{uploadInBalance.geStartTime} and s.tradeDate1 &lt; #{uploadInBalance.ltEndTime}
            and s.areaid in <include refid="inAreaIdForEach"></include>
        group by FORMAT(s.tradeDate1, 'yyyyMMdd'),s.areaid, b.ppriceid,case when k.mouldFlag = 1 then 2 else 1 end
        union all
        <!--小件销售-->
        select FORMAT(s.tradeDate1, 'yyyyMMdd') "SO_YMD", s.areaid "SHOP_ID",b.ppriceid "SKU", sum(b.basket_count) "SO_QTY", sum(p.memberprice*b.basket_count) "SO_AMT",
        1 "SO_TYPE"
        from dbo.basket b with(nolock)
            left join dbo.sub s with(nolock) on s.sub_id = b.sub_id
            left join dbo.productinfo p with(nolock) on p.ppriceid=b.ppriceid
        where isnull(b.isdel,0)=0 and b.ismobile=0 and s.sub_check in (3,9) and p.brandID=2
            and s.tradeDate1 &gt;= #{uploadInBalance.geStartTime} and s.tradeDate1 &lt; #{uploadInBalance.ltEndTime}
            and s.areaid in <include refid="inAreaIdForEach"></include>
        group by FORMAT(s.tradeDate1, 'yyyyMMdd'),s.areaid, b.ppriceid

        <!--退货数量-->
        union all
        <!--大件退货-->
        select FORMAT(s.finishTime, 'yyyyMMdd') "SO_YMD", s.areaid "SHOP_ID",p.ppriceid "SKU", count(1) "SO_QTY", sum(b.price) "SO_AMT",
        case when pm.mouldFlag = 1 then 4 else 3 end "SO_TYPE"
        from dbo.return_basket b with(nolock)
            left join dbo.return_sub s with(nolock) on b.sub_id=s.id
            left join product_mkc pm with(nolock) on pm.id = b.ppriceid
            left join dbo.productinfo p with(nolock) ON p.ppriceid=pm.ppriceid
        where s.type_ = 3 and s.states=3 and p.brandID=2
                and s.finishTime &gt;= #{uploadInBalance.geStartTime} and s.finishTime &lt; #{uploadInBalance.ltEndTime}
                and s.areaid in <include refid="inAreaIdForEach"></include>
        group by FORMAT(s.finishTime, 'yyyyMMdd'),s.areaid, p.ppriceid,case when pm.mouldFlag = 1 then 4 else 3 end
        union all
        <!--小件退货-->
        select FORMAT(s.finishTime, 'yyyyMMdd') "SO_YMD", s.areaid "SHOP_ID",b.ppriceid "SKU", sum(b.lcount) "SO_QTY", sum(b.price*b.lcount) "SO_AMT",
        3 "SO_TYPE"
        from dbo.return_basket b with(nolock)
            left join dbo.return_sub s with(nolock) on b.sub_id=s.id
        where s.type_ = 1 and s.states=3
            and s.finishTime &gt;= #{uploadInBalance.geStartTime} and s.finishTime &lt; #{uploadInBalance.ltEndTime}
            and s.areaid in <include refid="inAreaIdForEach"></include>
            and  b.ppriceid in <include refid="samsungBrankPpid"></include>
        group by FORMAT(s.finishTime, 'yyyyMMdd'),s.areaid, b.ppriceid

    </select>
    <select id="listKcDataBySKUAndType" resultType="java.util.Map">
        <!--库存数据-->

        <!--大件-->
        select FORMAT(CONVERT(DATETIME, #{uploadInBalance.geStartTime}, 126), 'yyyyMMdd') "INV_YMD", c.areaid "SHOP_ID",c.ppriceid "SKU", count(1) "INV_QTY", sum(c.inbeihuoprice) "INV_AMT",
        case when c.mouldFlag = 1 then 2 else 1 end "INV_TYPE"
        from (
                 select  k.id,k.inbeihuoprice,k.ppriceid,k.mouldFlag,k.areaid
                 from dbo.product_mkc k with(nolock)
                    where k.kc_check=3 and  imei is not null
                    and k.areaid in <include refid="inAreaIdForEach"></include>
                    and k.ppriceid in <include refid="samsungBrankPpid"></include>
                 union
                 select  k.id,k.inbeihuoprice,k.ppriceid,k.mouldFlag,m.areaid
                    from dbo.product_mkc k with(nolock)
                        left join dbo.mkc_toarea m with(nolock) on m.toareaid=k.areaid and k.id=m.mkc_id
                        where k.kc_check=10 and imei is not null
                    and k.ppriceid in <include refid="samsungBrankPpid"></include>
                    and m.areaid in <include refid="inAreaIdForEach"></include> and m.stats in (0,1)
                union
                select  k.id,k.inbeihuoprice,k.ppriceid,k.mouldFlag,k.areaid
                from dbo.product_mkc k with(nolock)
                where k.kc_check=10 and imei is not null
                and not exists(select 1 from dbo.mkc_toarea m with(nolock) where m.toareaid=k.areaid and k.id=m.mkc_id)
                and k.ppriceid in <include refid="samsungBrankPpid"></include>
                and k.areaid in <include refid="inAreaIdForEach"></include>
            ) c
        group by c.areaid,c.ppriceid,case when c.mouldFlag = 1 then 2 else 1 end
        union all
        <!--大件退货-->
        select FORMAT(s.outTime, 'yyyyMMdd') "INV_YMD", s.areaid "SHOP_ID",p.ppriceid "SKU", count(1) "INV_QTY", sum(b.price) "INV_AMT",
        case when pm.mouldFlag = 1 then 2 else 1 end "INV_TYPE"
        from dbo.return_basket b with(nolock)
        left join dbo.return_sub s with(nolock) on b.sub_id=s.id
        left join product_mkc pm with(nolock) on pm.id = b.ppriceid
        left join dbo.productinfo p with(nolock) ON p.ppriceid=pm.ppriceid
        where s.type_ = 3 and s.states=2 and p.brandID=2
        and s.outTime &gt;= #{uploadInBalance.geStartTime} and s.outTime &lt; #{uploadInBalance.ltEndTime}
        and s.areaid in <include refid="inAreaIdForEach"></include>
        group by FORMAT(s.outTime, 'yyyyMMdd'),s.areaid, p.ppriceid,case when pm.mouldFlag = 1 then 2 else 1 end
        union all
        <!--小件退货-->
        select FORMAT(s.outTime, 'yyyyMMdd') "INV_YMD", s.areaid "SHOP_ID",b.ppriceid "SKU", sum(b.lcount) "INV_QTY", sum(b.price*b.lcount) "INV_AMT",
        1 "INV_TYPE"
        from dbo.return_basket b with(nolock)
        left join dbo.return_sub s with(nolock) on b.sub_id=s.id
        where s.type_ = 1 and s.states=2
        and s.outTime &gt;= #{uploadInBalance.geStartTime} and s.outTime &lt; #{uploadInBalance.ltEndTime}
        and s.areaid in <include refid="inAreaIdForEach"></include>
        and  b.ppriceid in <include refid="samsungBrankPpid"></include>
        group by FORMAT(s.outTime, 'yyyyMMdd'),s.areaid, b.ppriceid
        union all
        <!--小件库存-->
        select FORMAT(CONVERT(DATETIME, #{uploadInBalance.geStartTime}, 126), 'yyyyMMdd') "INV_YMD", k.areaid "SHOP_ID",k.ppriceid "SKU", sum(k.lcount) "INV_QTY", sum(k.inprice*k.lcount) "INV_AMT",
        1 "INV_TYPE"
        from dbo.product_kc k with(nolock)
        where k.lcount>0
          and k.ppriceid in <include refid="samsungBrankPpid"></include>
          and k.areaid in <include refid="inAreaIdForEach"></include>
        group by k.areaid,k.ppriceid
        union all
        <!--小件调拨未发出的商品-->
        select FORMAT(s.check_dtime, 'yyyyMMdd') "INV_YMD", s.areaid "SHOP_ID",b.ppriceid "SKU", sum(b.lcount) "INV_QTY",sum(b.lcount*b.inprice) "INV_AMT",
        1 "INV_TYPE"
        from dbo.diaobo_basket b with(nolock)
            left join dbo.diaobo_sub s with(nolock) on s.id=b.sub_id
        where s.stats in (2,5)
            and s.check_dtime &gt;= #{uploadInBalance.geStartTime} and s.check_dtime &lt; #{uploadInBalance.ltEndTime}
            and b.ppriceid in <include refid="samsungBrankPpid"></include>
            and s.areaid in <include refid="inAreaIdForEach"></include>
        group by FORMAT(s.check_dtime, 'yyyyMMdd'),s.areaid,b.ppriceid
    </select>
    <select id="queryProduct" resultType="cn.hutool.core.lang.Dict">
        SELECT p.ppriceid ppriceid,isnull(p.product_name, '') product_name,isnull(p.product_color, '') product_color
        from productinfo p with(nolock)
        where p.brandID = 2 and p.cid not in (select f.id from f_category_children(23) f)
    </select>
</mapper>
