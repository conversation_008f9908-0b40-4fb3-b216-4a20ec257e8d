<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.hotsale.mapper.HotSaleMapper">


    <sql id="sql2_tt">
        <if test="conn.act=='search' and conn.onlyJh !='1'.toString()">
            <if test="conn.distributionStore!=null and conn.distributionStore == '1'.toString()">
                and s.subtype = 10
            </if>
            <if test="conn.distributionStore!=null and conn.distributionStore == '2'.toString()">
                and s.subtype <![CDATA[ <> ]]> 10
            </if>
            <if test="conn.area != null and conn.area!=''">
                and s.areaid=#{conn.area}
            </if>
            <if test="conn.getAreaCode() != null and conn.getAreaCode().size()>0">
                and s.areaid in
                <foreach item="item" index="index" collection="conn.areaCode" open="(" separator="," close=")">
                    ${item}
                </foreach>
            </if>
            <if test="conn.getWebArea() != null and conn.getWebArea().size()>0">
                and s.areaid in
                <foreach item="item" index="index" collection="conn.webArea" open="(" separator="," close=")">
                    ${item}
                </foreach>
            </if>
            <if test="conn.areaLevel != null and conn.areaLevel!=''">
                and exists(select 1 from areainfo a with(nolock) where ispass=1 and level1=#{conn.areaLevel} and
                s.areaid = a.id)
            </if>
            <if test="conn.getKind1() != null and conn.getKind1().size()>0">
                and exists(select id from areainfo a with(nolock) where ispass=1 and kind1 in
                <foreach item="item" index="index" collection="conn.kind1" open="(" separator="," close=")">
                    #{item}
                </foreach>
                and s.areaid = a.id
                )
            </if>
            <if test="conn.areakind!=null and conn.areakind == '2'.toString()">
                and exists(select id from areainfo a with(nolock) where ispass=1 and authorizeid=#{conn.authorizeid}
                and s.areaid = a.id)
            </if>
            <if test="conn.areaRankKind !=null and conn.areaRankKind == '1'.toString()">
                <if test="conn.getAreaRankIds() != null and conn.getAreaRankIds().size()>0">
                    and s.areaid in
                    <foreach item="item" index="index" collection="conn.getAreaRankIds" open="(" separator="," close=")">
                        ${item}
                    </foreach>
                </if>
            </if>
            <if test="conn.key != null and conn.key != '' and conn.keyType=='1'.toString() ">
                and k_.ppriceid in(select ppriceid1 from productinfo with(nolock) where ppriceid = #{conn.key} or
                product_id=#{conn.key})
            </if>
            <if test="conn.key != null and conn.key != ''and conn.keyType=='2'.toString() ">
                and k_.ppriceid in(select ppriceid1 from productinfo with(nolock) where product_name like
                CONCAT('%',#{conn.key},'%'))
            </if>
        </if>
    </sql>
    <sql id="sql2">
        <if test="conn.act='search' and conn.onlyJh !='1'.toString()">
            <if test="conn.area != null and conn.area !=''">
                and areaid=#{conn.area}
            </if>

            <if test="conn.getAreaCode() != null and conn.getAreaCode().size()>0">
                and areaid in
                <foreach item="item" index="index" collection="conn.areaCode" open="(" separator="," close=")">
                    ${item}
                </foreach>
            </if>
            <if test="conn.getWebArea() != null and conn.getWebArea().size()>0">
                and areaid in
                <foreach item="item" index="index" collection="conn.webArea" open="(" separator="," close=")">
                    ${item}
                </foreach>
            </if>
            <if test="conn.areaLevel != null and conn.areaLevel !=''">
                and areaid in(select id from areainfo with(nolock) where ispass=1 and level1=#{conn.areaLevel})
            </if>
            <if test="conn.getKind1() != null and conn.getKind1().size()>0">
                and areaid in(select id from areainfo with(nolock) where ispass=1 and kind1 in
                <foreach item="item" index="index" collection="conn.kind1" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="conn.areakind!=null and  conn.areakind == '2'.toString()">
                and areaid in(select id from areainfo with(nolock) where ispass=1 and authorizeid=#{conn.authorizeid})
            </if>
            <if test="conn.areaRankKind !=null and conn.areaRankKind == '1'.toString()">
                and areaid in
                <foreach item="item" index="index" collection="conn.areaRankIds" open="(" separator="," close=")">
                    ${item}
                </foreach>
            </if>


            <if test="conn.key != null and conn.key != '' and conn.keyType=='1'.toString() ">
                and p1.ppriceid in(select ppriceid1 from productinfo with(nolock) where ppriceid = #{conn.key} or
                product_id=#{conn.key})
            </if>
            <if test="conn.key != null and conn.key != ''and conn.keyType=='2'.toString() ">
                and p1.ppriceid in(select ppriceid1 from productinfo with(nolock) where product_name like
                CONCAT('%',#{conn.key},'%'))
            </if>
        </if>
    </sql>

    <select id="selectHotProductNotJhData" resultType="com.jiuji.oa.stock.hotsale.vo.DataTable">
        select p.id as productid,
        isnull(b.days, 0) as days,
        isnull(x.xdays, 0) as xdays,
        isnull(b.avglirun, 0) as avglirun,
        isnull(b.counts, 0) as lirunCounts,
        p.name as product_name,
        isnull(bb.counts, 0) as counts,
        isnull(x.xcount, 0) as xcount,
        isnull(x.t1, 0) as t1,
        isnull(x.t2, 0) as t2,
        isnull(k1.kcount, 0) as kcount,
        isnull(xing.ingcount, 0) as ingcount,
        isnull(sh.shcount, 0) as shcount,
        p.viewsweek,
        p.rank1
        from (SELECT *, RANK() OVER (ORDER BY p.viewsWeek DESC) rank1
        FROM product p with (nolock)
        WHERE p.ismobile = 1) p
        left join (select p_.productid,
        count(1) as counts,
        convert(Decimal (10, 2), avg(datediff(d, inbeihuodate, tradedate) + 0.00),
        2) as days,
        convert(Decimal (10, 2), avg(b1.price2 - isnull(k_.staticPrice, b1.price2) + 0.00),
        2) as avglirun
        from basket b1 with (nolock)
        left join product_mkc k_
        with (nolock)
        on b1.basket_id = k_.basket_id
        left join productinfo p_
        with (nolock)
        on p_.ppriceid = k_.ppriceid
        LEFT JOIN sub s
        with (nolock)
        ON b1.sub_id = s.sub_id
        where
        s.tradeDate1 between #{conn.date1} and #{conn.date2}
        and sub_check in (3)
        and isnull(b1.ischu
        , 0) = 1
        and isnull(b1.isdel
        , 0) = 0
        and ismobile = 1
        and isnull(b1.type
        , 0) <![CDATA[ <> ]]> 22
        and not exists (select 1
        from dbo.xc_mkc xc with (nolock)
        where xc.mkc_id = k_.id
        and isnull(xc.isLock
        , 0) = 1)

        <include refid="sql2_tt"/>

        group by p_.productid

        ) b on b.productid = p.id
        left join (select p_.productid,
        count(1) as counts,
        convert(Decimal (10, 2), avg(datediff(d, inbeihuodate, tradedate) + 0.00), 2) as days
        from basket b1 with (nolock)
        left join product_mkc k_
        with (nolock)
        on b1.basket_id = k_.basket_id
        left join productinfo p_
        with (nolock)
        on p_.ppriceid = k_.ppriceid
        LEFT JOIN sub s
        with (nolock)
        ON b1.sub_id = s.sub_id
        where
        s.tradeDate between #{conn.date1} and #{conn.date2}
        and sub_check in (2
        , 3
        , 6)
        and isnull(b1.ischu
        , 0) = 1
        and isnull(b1.isdel
        , 0) = 0
        and ismobile = 1
        and not exists (select 1
        from dbo.xc_mkc xc with (nolock)
        where xc.mkc_id = k_.id
        and isnull(xc.isLock
        , 0) = 1)

        <include refid="sql2_tt"/>
        group by p_.productid
        ) bb on bb.productid = p.id
        left join (select productid,
        COUNT(1) as xcount,
        sum(case when kc_check = 10 then 1 else 0 end) as t1,
        sum(case when kc_check = 10 then 0 else 1 end) as t2,
        convert(Decimal (10, 2), avg(datediff(d, inbeihuodate, getdate()) + 0.00),
        2) as xdays
        from product_mkc k with (nolock)
        left join productinfo p1
        with (nolock)
        on k.ppriceid = p1.ppriceid
        where basket_id is null
        and kc_check in (2
        , 3
        , 10)
        and areaid <![CDATA[ <> ]]> 13
        and isnull(mouldFlag
        , 0) = 0
        and not exists (select 1 from dbo.xc_mkc x with (nolock) where k.id = x.mkc_id)

        <include refid="sql2"></include>
        group by p1.productid) x on x.productid = p.id
        left join (select productid, COUNT(1) as kcount
        from product_mkc k with (nolock)
        left join productinfo p1
        with (nolock)
        on k.ppriceid = p1.ppriceid
        where basket_id is not null
        and kc_check in (2
        , 3
        , 10)
        and isnull(mouldFlag
        , 0) = 0
        and not exists (select 1
        from dbo.xc_mkc xc with (nolock)
        where xc.mkc_id = k.id
        and isnull(xc.isLock
        , 0) = 1)

        <include refid="sql2"></include>
        group by productid) k1 on k1.productid = p.id
        left join (select productid, COUNT(1) as ingcount
        from product_mkc k with (nolock)
        left join productinfo p1
        with (nolock)
        on k.ppriceid = p1.ppriceid
        where basket_id is null
        and kc_check in (1)
        and not exists (select 1
        from dbo.xc_mkc xc with (nolock)
        where xc.mkc_id = k.id
        and isnull(xc.isLock
        , 0) = 1)

        <include refid="sql2"></include>
        group by productid) xing on xing.productid = p.id
        left join(select p1.productid, count(0) as shcount
        from shouhou h1 with (nolock)
        left join productinfo p1
        with (nolock)
        on h1.ppriceid = p1.ppriceid
        where tradedate
        > DATEADD(D
        , -7
        , GETDATE())
        and issoft = 0
        and baoxiu = 1
        and xianshi = 1
        group by p1.productid) sh on sh.productid = p.id
        where p.ismobile = 1
        and (x.xcount > 0 or isnull(bb.counts, 0) > 0 or isnull(k1.kcount, 0) > 0 or
        isnull(xing.ingcount, 0) > 0)

        <if test="conn.gaoji!=null and conn.gaoji!=''">
            <if test="conn.cids !=null and conn.cids !='' ">
                and exists( select 1 from f_category_children(#{conn.cids}) f where f.id=p.cid )
            </if>
            <if test="conn.getBrandid()!=null and conn.getBrandid().size()>0">
                and p.brandid in
                <foreach item="item" index="index" collection="conn.brandid" open="(" separator="," close=")">
                    ${item}
                </foreach>

            </if>
            <if test="conn.ishang!=null and conn.ishang=='1'">
                and p.product_color like CONCAT('%','行货','%')
            </if>
            <if test="conn.ishang!=null and conn.ishang=='2'">
                and p.product_color not like CONCAT('%','行货','%')
            </if>
        </if>
        <if test="conn.key ==null or conn.key==''">
            and (b.productid is not null or x.productid is not null or k1.productid is not null or xing.productid is not
            null)
        </if>
        <choose>
            <when test="conn.order==null or conn.order=='' ">
                <choose>
                    <when test="conn.proKc!=null and conn.proKc=='1'.toString()">
                        order by x.xcount desc
                    </when>
                    <otherwise>
                        order by bb.counts desc
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                order by bb.counts desc
            </otherwise>
        </choose>
    </select>


    <sql id="sql_tmp">
        <if test="conn.sql2==null or conn.sql2==''">
            <if test="conn.key!=null and conn.key!=''">
                and k.ppriceid in(select ppriceid from productinfo p with(nolock) where 1=1
                and  p.product_name like CONCAT('%',#{conn.key},'%'))
            </if>

        </if>
        <if test="conn.sql2!=null and conn.sql2!=''">
            and k.ppriceid in(select ppriceid from productinfo p with(nolock) where 1=1
            <if test="conn.key!=null and conn.key!=''">
                and  p.product_name like CONCAT('%',#{conn.key},'%')
            </if>
            <if test="conn.gaoji!=null and conn.gaoji !=''">
                <if test="conn.cids !=null and conn.cids !='' ">
                    and exists( select 1 from f_category_children(#{conn.cids}) f where f.id=p.cid )
                </if>
                <if test="conn.getBrandid()!=null and conn.getBrandid().size()>0">
                    and p.brandid in
                    <foreach item="item" index="index" collection="conn.brandid" open="(" separator="," close=")">
                        ${item}
                    </foreach>

                </if>
                <if test="conn.ishang!=null and conn.ishang=='1'.toString()">
                    and p.product_color like CONCAT('%','行货','%')
                </if>
                <if test="conn.ishang!=null and conn.ishang=='2'.toString()">
                    and p.product_color not like CONCAT('%','行货','%')
                </if>
            </if>
            )
        </if>
        <if test="conn.area!=null and conn.area!='' ">
            and areaid=#{conn.area}
        </if>
        <if test="conn.getAreaCode() != null and conn.getAreaCode().size()>0">
            and areaid in
            <foreach item="item" index="index" collection="conn.areaCode" open="(" separator="," close=")">
                ${item}
            </foreach>
        </if>
        <if test="conn.areaRankKind !=null and conn.areaRankKind == '1'.toString()">
            and areaid in
            <foreach item="item" index="index" collection="conn.areaRankIds" open="(" separator="," close=")">
                ${item}
            </foreach>
        </if>

        <if test="conn.getKind1() != null and conn.getKind1().size()>0">
            and areaid in(select id from areainfo with(nolock) where ispass=1 and kind1 in
            <foreach item="item" index="index" collection="conn.kind1" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
    </sql>
    <select id="selectHotProductJhData" resultType="com.jiuji.oa.stock.hotsale.vo.DataTable">
        seLect a.id areaid,
        a.area,
        isnull(k1.k1_count, 0) as k1_count,
        isnull(k1.jiangjia1, 0) as jiangjia1,
        isnull(k1.jiangjia2, 0) as jiangjia2,
        (isnull(k1.jiangjia1, 0) + isnull(k1.jiangjia2, 0)) as jiangjia,
        isnull(k2.k2_count, 0) as k2_count,
        isnull(k3.k3_count, 0) as k3_count,
        isnull(x.x_count, 0) as x_count,
        isnull(k1.days, 0) as days
        from areainfo a with (nolock)
        left join (select count(1) as k1_count,
        areaid,
        convert(Decimal(10, 2), avg(datediff(d, inbeihuodate, getdate()) + 0.00), 2) as days,
        sum(case
        when (k.staticPrice - p.costprice) > 0 then (k.staticPrice - p.costprice)
        else 0 end) as jiangjia1,
        sum(case
        when (k.staticPrice - p.costprice) <![CDATA[ < ]]> 0 then (k.staticPrice - p.costprice)
        else 0 end) as jiangjia2
        from product_mkc k with (nolock)
        left join productinfo p with (nolock) on k.ppriceid = p.ppriceid
        where kc_check in (2, 3, 10)
        and basket_id is null
        <include refid="sql_tmp"/>
        group by areaid) k1 on k1.areaid = a.id
        left join (select count(1) as k2_count, areaid
        from product_mkc k with (nolock)
        where kc_check in (2, 3, 10)
        and basket_id is not null
        <include refid="sql_tmp"/>
        group by areaid) k2 on k2.areaid = a.id
        left join (select count(1) as k3_count, areaid
        from product_mkc k with (nolock)
        where kc_check in (1)
        and basket_id is null
        <include refid="sql_tmp"/>
        group by areaid) k3 on k3.areaid = a.id
        left join (select isnull(sum(basket_count), 0) as x_count, areaid
        from basket k with (nolock)
        left join sub s with (nolock) on k.sub_id = s.sub_id
        where ismobile = 1
        and sub_check in (2, 3, 6)
        and k.ischu = 1
        and isnull(k.isdel, 0) = 0
        and tradedate between #{conn.date1} and #{conn.date2}
        <include refid="sql_tmp"/>
        group by areaid) x on x.areaid = a.id
        where ispass = 1

        <if test="conn.area!=null and conn.area!=''">
            and a.id=#{conn.area}
        </if>
        <if test="conn.getAreaCode() != null and conn.getAreaCode().size()>0">
            and a.id in
            <foreach item="item" index="index" collection="conn.areaCode" open="(" separator="," close=")">
                ${item}
            </foreach>
        </if>
        <if test="conn.areaRankKind !=null and conn.areaRankKind == '1'.toString()">
            and a.id in
            <foreach item="item" index="index" collection="conn.areaRankIds" open="(" separator="," close=")">
                ${item}
            </foreach>
        </if>

        <if test="conn.getKind1() != null and conn.getKind1().size()>0">
            and a.kind1 in
            <foreach item="item" index="index" collection="conn.kind1" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by rank
    </select>


    <select id="selectHotProductCdata" resultType="com.jiuji.oa.stock.hotsale.vo.ProductDetails">
        select p_.product_name, p_.productid, count(1) as counts
        from basket b1 with (nolock)
        left join product_mkc k_
        with (nolock)
        on b1.basket_id = k_.basket_id
            left join productinfo p_
        with (nolock)
        on p_.ppriceid = k_.ppriceid
            LEFT JOIN sub s
        with (nolock)
        ON b1.sub_id = s.sub_id
        where tradedate between #{conn.date1}
          and #{conn.date2}
          and sub_check in (2
            , 3
            , 6)
          and isnull(b1.ischu
            , 0) = 1
          and s.subtype <![CDATA[ <> ]]> 10
          and isnull(b1.isdel
            , 0) = 0
          and ismobile = 1
          and s.areaid in (#{conn.cAreaCode_})
        group by p_.productid, p_.product_name
        order by count (1) desc
    </select>
    <select id="getCh999UserRanks" resultType="com.jiuji.oa.stock.hotsale.vo.GetCh999UserRanksDto">
        select ch999_id, areaid, ranks
        from ch999Ranks with(nolock)
        where ch999_id=#{ch999_id}
          and ranks like CONCAT('%'
            , #{ranks}
            , '%')
    </select>
    <select id="getAreaDepartInfo" resultType="com.jiuji.oa.stock.hotsale.vo.AreaDepartInfo">
        select area, a.id, a.depart_id, kind2, kind1, a.depart_id
        from areainfo a with(nolock)
        where ispass=1
        <if test="areaIds!=null and areaIds.size>0">
            and a.id in
            <foreach item="item" index="index" collection="areaIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        order by kind1, rank asc
    </select>
    <select id="getXianhuokcJhhot" resultType="com.jiuji.oa.stock.hotsale.vo.InventoryAggregation">
        select p1.product_name,
        p1.product_color,
        k.ppriceid,
        k.lcount,
        isnull(k.jiangjia1, 0) as jiangjia1,
        isnull(k.jiangjia2, 0) as jiangjia2,
        (isnull(k.jiangjia1, 0) + isnull(k.jiangjia2, 0)) as jiangjia
        from (select k1.ppriceid,
        count(1) as lcount,
        sum(case
        <if test="conn.isJiuJi !=null and conn.isJiuJi !=''">
            when (k1.staticPrice - p.costprice) > 0
        </if>

        <if test="conn.isJiuJi ==null or conn.isJiuJi ==''">
            when (isnull(k1.transferPrice, k1.inbeihuoprice) - p.costprice) > 0
        </if>
        then (isnull(k1.transferPrice, k1.inbeihuoprice) - p.costprice)
        else 0 end) as jiangjia1,
        sum(case
        when (k1.inbeihuoprice - p.costprice) <![CDATA[ < ]]> 0 then (k1.inbeihuoprice - p.costprice)
        else 0 end) as jiangjia2
        from product_mkc k1 with(nolock)
        left join productinfo p with(nolock) on k1.ppriceid = p.ppriceid
        where kc_check in (2, 3, 10)
        and basket_id is null
        <if test="conn.isJiuJi !=null and conn.isJiuJi !=''">
            and isnull(k1.mouldFlag,0) =0
        </if>
        and areaid = #{conn.area}
        <include refid="sql3"/>
        group by k1.ppriceid) k left join productinfo p1 with(nolock) on p1.ppriceid=k.ppriceid order by k.lcount desc
    </select>
    <select id="getXianhuokcAreajhhot" resultType="com.jiuji.oa.stock.hotsale.vo.InventoryAggregation">
        select p1.product_name,
        p1.product_color,
        k.ppriceid,
        k.lcount,
        isnull(k.jiangjia1, 0) as jiangjia1,
        isnull(k.jiangjia2, 0) as jiangjia2,
        (isnull(k.jiangjia1, 0) + isnull(k.jiangjia2, 0)) as jiangjia
        from (select k1.ppriceid,
        count(1) as lcount,
        sum(case
        when (isnull(k1.transferPrice, k1.inbeihuoprice) - p.costprice) > 0
        then (isnull(k1.transferPrice, k1.inbeihuoprice) - p.costprice)
        else 0 end) as jiangjia1,
        sum(case
        when (k1.inbeihuoprice - p.costprice) <![CDATA[ < ]]>  0 then (k1.inbeihuoprice - p.costprice)
        else 0 end) as jiangjia2
        from product_mkc k1 with(nolock)
        left join productinfo p with(nolock) on k1.ppriceid=p.ppriceid where kc_check in(2,3,10) and basket_id is null
        <if test="conn.getAreaIds()!=null and conn.getAreaIds().size>0">
            and areaid in
            <foreach item="item" index="index" collection="conn.areaIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <include refid="sql3"/>
        group by k1.ppriceid) k left join productinfo p1 with(nolock) on p1.ppriceid=k.ppriceid order by k.lcount desc
    </select>
    <sql id="sql3">
        <if test="conn.gaoji!=null and conn.gaoji!=''">
            <if test="conn.cids !=null and conn.cids !='' ">
                and exists( select 1 from f_category_children(#{conn.cids}) f where f.id=p.cid )
            </if>
            <if test="conn.getBrandid()!=null and conn.getBrandid().size()>0">
                and p.brandid in
                <foreach item="item" index="index" collection="conn.brandid" open="(" separator="," close=")">
                    ${item}
                </foreach>

            </if>

        </if>
        <if test="conn.key !=null and conn.key!=''">
            and p.product_name like CONCAT('%',#{conn.key},'%'))
        </if>
        <if test="conn.dday !=null and conn.dday!=''">
            and imeidate<![CDATA[ <= ]]> #{conn.ddayValue}
        </if>
        <if test="conn.flag!=null and conn.flag!=''">
            and exists(select 1 from areainfo a with(nolock) where a.ispass=1 and a.authorizeid=#{conn.authorizeid} and a.id=k1.areaid )
        </if>
    </sql>
    <select id="getKkcJhhot" resultType="com.jiuji.oa.stock.hotsale.vo.InventoryAggregation">
        select p1.product_name, p1.product_color, k.ppriceid, k.lcount
        from (select ppriceid, count(1) as lcount
        from product_mkc with(nolock)
        where kc_check in (2, 3, 10) and basket_id is not null and areaid = #{conn.area}
        <include refid="sql4"/>
        group by ppriceid) k left join productinfo p1 with(nolock) on p1.ppriceid=k.ppriceid order by k.lcount desc
    </select>
    <select id="getKkcAreajhhot" resultType="com.jiuji.oa.stock.hotsale.vo.InventoryAggregation">
        select p1.product_name, p1.product_color, k.ppriceid, k.lcount
        from (select ppriceid, count(1) as lcount
        from product_mkc with(nolock)
        where kc_check in (2, 3, 10) and basket_id is not null
        <if test="conn.getAreaIds()!=null and conn.getAreaIds().size>0">
            and areaid in
            <foreach item="item" index="index" collection="conn.areaIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <include refid="sql4"/>
        group by ppriceid) k left join productinfo p1 with(nolock) on p1.ppriceid=k.ppriceid order by k.lcount desc
    </select>
    <select id="getXiJhhot" resultType="com.jiuji.oa.stock.hotsale.vo.InventoryAggregation">
        select p1.product_name, p1.product_color, k.ppriceid, k.lcount, k.avglirun
        from (select b.ppriceid,
        count(1) as lcount,
        convert(Decimal(10, 2), avg(b.price - isnull(k_.inbeihuoprice, b.price) + 0.00), 2) as avglirun
        from basket b with(nolock)
        left join product_mkc k_ with(nolock) on b.basket_id = k_.basket_id
        LEFT JOIN dbo.sub s with(nolock) ON b.sub_id = s.sub_id
        where b.ismobile = 1
        and b.ischu = 1
        and isnull(b.isdel, 0) = 0
        and sub_check in (2, 3, 6)
        and tradedate between #{conn.date1} and #{conn.date2}
        and s.areaid = #{conn.area}
        <include refid="sql4"/>
        group by b.ppriceid) k left join productinfo p1 with(nolock) on p1.ppriceid=k.ppriceid order by k.lcount desc

    </select>
    <select id="getXiAreajhhot" resultType="com.jiuji.oa.stock.hotsale.vo.InventoryAggregation">
        select p1.product_name, p1.product_color, k.ppriceid, k.lcount, k.avglirun
        from (select b.ppriceid,
        count(1) as lcount,
        convert(Decimal(10, 2), avg(b.price - isnull(k_.inbeihuoprice, b.price) + 0.00), 2) as avglirun
        from basket b with(nolock)
        left join product_mkc k_ with(nolock) on b.basket_id = k_.basket_id
        LEFT JOIN dbo.sub s with(nolock) ON b.sub_id = s.sub_id
        where b.ismobile = 1
        and b.ischu = 1
        and isnull(b.isdel, 0) = 0
        and sub_check in (2, 3, 6)
        and tradedate between #{conn.date1} and #{conn.date2}
        <if test="conn.getAreaIds()!=null and conn.getAreaIds().size>0">
            and s.areaid in
            <foreach item="item" index="index" collection="conn.areaIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <include refid="sql4"/>
        group by b.ppriceid) k left join productinfo p1 with(nolock) on p1.ppriceid=k.ppriceid order by k.lcount desc

    </select>
    <sql id="sql4">
        <if test="conn.sql2!=null and conn.sql2!=''">
            and ppriceid in(select ppriceid from productinfo p with(nolock) where 1=1
            <if test="conn.gaoji!=null and conn.gaoji!=''">
                <if test="conn.cids!=null and conn.cids!='' ">
                    and exists( select 1 from f_category_children(#{conn.cids}) f where f.id=p.cid )
                </if>
                <if test="conn.getBrandid()!=null and conn.getBrandid().size()>0">
                    and p.brandid in
                    <foreach item="item" index="index" collection="conn.brandid" open="(" separator="," close=")">
                        ${item}
                    </foreach>
                </if>

            </if>
            <if test="conn.key !=null and conn.key!=''">
                and p.product_name like CONCAT('%',#{conn.key},'%'))
            </if>
        </if>
    </sql>

</mapper>
