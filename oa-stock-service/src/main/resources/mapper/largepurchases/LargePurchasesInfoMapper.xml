<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.nc.largepurchasesStock.mapper.LargePurchasesInfoMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.nc.largepurchasesStock.po.LargePurchasesInfoStock">
        <id column="id" property="id"/>
        <result column="bid_name" property="bidName"/>
        <result column="bid_start_time" property="bidStartTime"/>
        <result column="bid_end_time" property="bidEndTime"/>
        <result column="bid_status" property="bidStatus"/>
        <result column="pro_kinds_num" property="proKindsNum"/>
        <result column="pro_total_num" property="proTotalNum"/>
        <result column="real_income_num" property="realIncomeNum"/>
        <result column="real_income_kinds_num" property="realIncomeKindsNum"/>
        <result column="update_time" property="updateTime"/>
        <result column="complete_time" property="completeTime"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="stock_check_user_id" property="stockCheckUserId"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="notice_flag" property="noticeFlag"/>
        <result column="create_time" property="createTime"/>
    </resultMap>
    <resultMap id="SupplyResultMap" type="com.jiuji.oa.nc.largepurchasesStock.res.SupplyChannelRes">
        <result column="id" property="id"/>
        <result column="isAuction" property="auction"/>
        <result column="insourceid" property="insourceId"/>
        <result column="userid" property="userId"/>
        <result column="company" property="company"/>
        <result column="username" property="userName"/>
        <result column="company_jc" property="companyJc"/>
    </resultMap>
    <resultMap id="ProductInfoResultMap" type="com.jiuji.oa.nc.largepurchasesStock.po.ProductInfo">
        <result column="productid" property="productId"/>
        <result column="ppriceid" property="ppid"/>
        <result column="product_name" property="productName"/>
        <result column="product_color" property="productColor"/>
        <result column="bpic" property="bPic"/>
    </resultMap>

    <select id="getLargePurchasesList" parameterType="com.jiuji.oa.nc.largepurchasesStock.po.LargePurchasesInfoStock"
            resultMap="BaseResultMap">
        select id,bid_name,bid_start_time,bid_end_time,bid_status,
        (case bid_status when 1 then "未开始" when 2 then "竞价中" when 3 then "已结束" when 4 then "已公示" when 5 then "已删除" end)
        as bid_status_msg ,
        pro_kinds_num,pro_total_num,real_income_num,real_income_kinds_num,
        update_time,complete_time,create_user_id,stock_check_user_id,update_user_id,notice_flag,create_time
        from large_purchases_info
        <where>
        <if test="lpInfo.id != null and lpInfo.id != 0">
            and id = #{lpInfo.id}
        </if>
        <if test="lpInfo.bidName != null and lpInfo.bidName != ''">
            and bid_name like CONCAT('%',#{lpInfo.bidName},'%')
        </if>
        <if test="lpInfo.bidStartTime != null">
            and bid_start_time = #{lpInfo.bidStartTime}
        </if>
        <if test="lpInfo.bidEndTime != null">
            and bid_end_time = #{lpInfo.bidEndTime}
        </if>
        <if test="lpInfo.sourceChannel != null and lpInfo.sourceChannel != ''">
            <if test="lpInfo.bidStatus != null and lpInfo.bidStatus != 0">
                <if test="lpInfo.bidStatus == 3">
                    and (bid_status = 3 or bid_status = 4)
                </if>
                <if test="lpInfo.bidStatus != 3">
                    and bid_status = #{lpInfo.bidStatus}
                </if>
            </if>
        </if>
        <if test="lpInfo.sourceChannel == null or lpInfo.sourceChannel == ''">
            <if test="lpInfo.bidStatus != null and lpInfo.bidStatus != 0">
                and bid_status = #{lpInfo.bidStatus}
            </if>
        </if>
        <if test="lpInfo.proKindsNum != null and lpInfo.proKindsNum != 0">
            and pro_kinds_num = #{lpInfo.proKindsNum}
        </if>
        <if test="lpInfo.proTotalNum != null and lpInfo.proTotalNum != 0">
            and pro_total_num = #{lpInfo.proTotalNum}
        </if>
        <if test="lpInfo.realIncomeNum != null and lpInfo.realIncomeNum != 0">
            and real_income_num = #{lpInfo.realIncomeNum}
        </if>
        <if test="lpInfo.updateTime != null">
            and update_time = #{lpInfo.updateTime}
        </if>
        <if test="lpInfo.completeTime != null">
            and complete_time = #{lpInfo.completeTime}
        </if>
        <if test="lpInfo.createUserId != null and lpInfo.createUserId != 0">
            and create_user_id = #{lpInfo.createUserId}
        </if>
        <if test="lpInfo.stockCheckUserId != null and lpInfo.stockCheckUserId != 0">
            and stock_check_user_id = #{lpInfo.stockCheckUserId}
        </if>
        <if test="lpInfo.updateUserId != null and lpInfo.updateUserId != 0">
            and update_user_id = #{lpInfo.updateUserId}
        </if>
        <if test="lpInfo.beginTime!= null">
            and create_time >= #{lpInfo.beginTime}
        </if>
        <if test="lpInfo.endTime!= null">
            <![CDATA[and create_time <= #{lpInfo.endTime}]]>
        </if>
        </where>
        order by id desc
    </select>
    <select id="getLargePurchasesListV2" parameterType="com.jiuji.oa.nc.largepurchasesStock.po.LargePurchasesInfoStock"
      resultMap="BaseResultMap">
        select id,bid_name,bid_start_time,bid_end_time,bid_status,
        (case bid_status when 1 then "未开始" when 2 then "竞价中" when 3 then "已结束" when 4 then "已公示" when 5 then "已删除" end)
        as bid_status_msg ,
        pro_kinds_num,pro_total_num,real_income_num,real_income_kinds_num,
        update_time,complete_time,create_user_id,stock_check_user_id,update_user_id,notice_flag,create_time
        from large_purchases_info
        <where>
            <if test="lpInfo.id != null and lpInfo.id != 0">
                and id = #{lpInfo.id}
            </if>
            <if test="lpInfo.bidName != null and lpInfo.bidName != ''">
                and bid_name like CONCAT('%',#{lpInfo.bidName},'%')
            </if>
            <if test="lpInfo.bidStartTime != null">
                and bid_start_time = #{lpInfo.bidStartTime}
            </if>
            <if test="lpInfo.bidEndTime != null">
                and bid_end_time = #{lpInfo.bidEndTime}
            </if>
            <if test="lpInfo.sourceChannel != null and lpInfo.sourceChannel != ''">
                <if test="lpInfo.bidStatus != null and lpInfo.bidStatus != 0">
                    <if test="lpInfo.bidStatus == 3">
                        and (bid_status = 3 or bid_status = 4)
                    </if>
                    <if test="lpInfo.bidStatus != 3">
                        and bid_status = #{lpInfo.bidStatus}
                    </if>
                </if>
            </if>
            <if test="lpInfo.sourceChannel == null or lpInfo.sourceChannel == ''">
                <if test="lpInfo.bidStatus != null and lpInfo.bidStatus != 0">
                    and bid_status = #{lpInfo.bidStatus}
                </if>
            </if>
            <if test="lpInfo.proKindsNum != null and lpInfo.proKindsNum != 0">
                and pro_kinds_num = #{lpInfo.proKindsNum}
            </if>
            <if test="lpInfo.proTotalNum != null and lpInfo.proTotalNum != 0">
                and pro_total_num = #{lpInfo.proTotalNum}
            </if>
            <if test="lpInfo.realIncomeNum != null and lpInfo.realIncomeNum != 0">
                and real_income_num = #{lpInfo.realIncomeNum}
            </if>
            <if test="lpInfo.updateTime != null">
                and update_time = #{lpInfo.updateTime}
            </if>
            <if test="lpInfo.completeTime != null">
                and complete_time = #{lpInfo.completeTime}
            </if>
            <if test="lpInfo.createUserId != null and lpInfo.createUserId != 0">
                and create_user_id = #{lpInfo.createUserId}
            </if>
            <if test="lpInfo.stockCheckUserId != null and lpInfo.stockCheckUserId != 0">
                and stock_check_user_id = #{lpInfo.stockCheckUserId}
            </if>
            <if test="lpInfo.updateUserId != null and lpInfo.updateUserId != 0">
                and update_user_id = #{lpInfo.updateUserId}
            </if>
            <if test="lpInfo.beginTime!= null">
                and create_time >= #{lpInfo.beginTime}
            </if>
            <if test="lpInfo.endTime!= null">
                <![CDATA[and create_time <= #{lpInfo.endTime}]]>
            </if>
        </where>
        order by id desc
    </select>

    <select id="getLargePurchasesListNoPage" parameterType="com.jiuji.oa.nc.largepurchasesStock.po.LargePurchasesInfoStock"
            resultMap="BaseResultMap">
        select id,bid_name,bid_start_time,bid_end_time,bid_status,
        (case bid_status when 1 then "未开始" when 2 then "竞价中" when 3 then "已结束" when 4 then "已公示" when 5 then "已删除" end)
        as bid_status_msg ,
        pro_kinds_num,pro_total_num,real_income_num,real_income_kinds_num,
        update_time,complete_time,create_user_id,stock_check_user_id,update_user_id,notice_flag,create_time
        from large_purchases_info
        where 1=1
        <if test="lpInfo.id != null and lpInfo.id != 0">
            and id = #{lpInfo.id}
        </if>
        <if test="lpInfo.bidName != null and lpInfo.bidName != ''">
            and bid_name = #{lpInfo.bidName}
        </if>
        <if test="lpInfo.bidStartTime != null">
            and bid_start_time = #{lpInfo.bidStartTime}
        </if>
        <if test="lpInfo.bidEndTime != null">
            and bid_end_time = #{lpInfo.bidEndTime}
        </if>
        <if test="lpInfo.bidStatus != null and lpInfo.bidStatus != 0">
            and bid_status = #{lpInfo.bidStatus}
        </if>
        <if test="lpInfo.proKindsNum != null and lpInfo.proKindsNum != 0">
            and pro_kinds_num = #{lpInfo.proKindsNum}
        </if>
        <if test="lpInfo.proTotalNum != null and lpInfo.proTotalNum != 0">
            and pro_total_num = #{lpInfo.proTotalNum}
        </if>
        <if test="lpInfo.realIncomeNum != null and lpInfo.realIncomeNum != 0">
            and real_income_num = #{lpInfo.realIncomeNum}
        </if>
        <if test="lpInfo.updateTime != null">
            and update_time = #{lpInfo.updateTime}
        </if>
        <if test="lpInfo.completeTime != null">
            and complete_time = #{lpInfo.completeTime}
        </if>
        <if test="lpInfo.createUserId != null and lpInfo.createUserId != 0">
            and create_user_id = #{lpInfo.createUserId}
        </if>
        <if test="lpInfo.stockCheckUserId != null and lpInfo.stockCheckUserId != 0">
            and stock_check_user_id = #{lpInfo.stockCheckUserId}
        </if>
        <if test="lpInfo.updateUserId != null and lpInfo.updateUserId != 0">
            and update_user_id = #{lpInfo.updateUserId}
        </if>
        order by id desc
    </select>

    <delete id="deleteLargePurchases">
        UPDATE large_purchases_info
        <set>
            bid_status = #{lpStatus}
        </set>
        <where>
            id = #{id}
        </where>
    </delete>

    <select id="getSupplyChannelStatus" resultMap="SupplyResultMap" parameterType="java.lang.Integer">
        SELECT top 1 q.id,
               q.isAuction,
               q.insourceid,
               q.userid,
               q.company,
               q.username,
               q.company_jc
        FROM
            Ok3w_qudao as q WITH(NOLOCK),
            channel_kind_link as c WITH(NOLOCK)
        WHERE
            q.id=c.channel_id
          and c.kind=3
          and c.channel_state=1
          AND q.userid=#{userId}
    </select>

    <select id="getSupplysUserId" resultType="java.lang.Integer">
        SELECT distinct  q.userid
        FROM
            Ok3w_qudao as q WITH(NOLOCK),
    channel_kind_link as c WITH(NOLOCK)
        WHERE
            q.id=c.channel_id
          and c.kind=3
          and c.channel_state=1
          and q.isAuction=1
          and q.userid is not null
    </select>

    <select id="getProductInfoByCon" resultMap="ProductInfoResultMap"
            parameterType="com.jiuji.oa.nc.largepurchasesStock.po.ProductInfo">
        select p.productid, p.ppriceid, p.product_name, p.product_color, p.bpic
        from dbo.productinfo p WITH (NOLOCK)
        left join category cg WITH (NOLOCK) on p.cid = cg.ID
        where p.ismobile1 = 1
        and cg.ParentID not in (23, 166)
        <if test="pInfo.ppid != null and pInfo.ppid != 0">
            and p.ppriceid=#{pInfo.ppid}
        </if>
        <if test="pInfo.productName != null and pInfo.productName != ''">
            and p.product_name like concat('%',#{pInfo.productName},'%')
        </if>
        <if test="pInfo.productId != null and pInfo.productId != 0">
            and p.productid = #{pInfo.productId}
        </if>

    </select>

    <update id="updateLpInfo" parameterType="com.jiuji.oa.nc.largepurchasesStock.po.LargePurchasesInfoStock">
        update large_purchases_info
        <set>
            <if test="lpInfo.bidName != null and lpInfo.bidName != ''">
                bid_name = #{lpInfo.bidName},
            </if>
            <if test="lpInfo.bidStartTime != null">
                bid_start_time = #{lpInfo.bidStartTime},
            </if>
            <if test="lpInfo.bidEndTime != null">
                bid_end_time = #{lpInfo.bidEndTime},
            </if>
            <if test="lpInfo.bidStatus != null and lpInfo.bidStatus != 0">
                bid_status = #{lpInfo.bidStatus},
            </if>
            <if test="lpInfo.proKindsNum != null and lpInfo.proKindsNum != 0">
                pro_kinds_num = #{lpInfo.proKindsNum},
            </if>
            <if test="lpInfo.proTotalNum != null and lpInfo.proTotalNum != 0">
                pro_total_num = #{lpInfo.proTotalNum},
            </if>
            <if test="lpInfo.realIncomeNum != null and lpInfo.realIncomeNum != 0">
                real_income_num = #{lpInfo.realIncomeNum},
            </if>
            <if test="lpInfo.updateTime != null">
                update_time = #{lpInfo.updateTime},
            </if>
            <if test="lpInfo.completeTime != null">
                complete_time = #{lpInfo.completeTime},
            </if>
            <if test="lpInfo.stockCheckUserId != null and lpInfo.stockCheckUserId != 0">
                stock_check_user_id = #{lpInfo.stockCheckUserId},
            </if>
            <if test="lpInfo.updateUserId != null and lpInfo.updateUserId != 0">
                update_user_id = #{lpInfo.updateUserId},
            </if>
            <if test="lpInfo.noticeFlag != null and lpInfo.noticeFlag != 0">
                notice_flag = #{lpInfo.noticeFlag},
            </if>
        </set>
        <where>
            id = #{lpInfo.id}
        </where>
    </update>

    <select id="getSupplyMobile" resultType="java.lang.String">
        select mobile
        from dbo.BBSXP_Users WITH(NOLOCK)
        where ID=#{userId}
    </select>

    <select id="listSupplyMobilesByUserIds" resultType="java.lang.String">
        select mobile
        from dbo.BBSXP_Users WITH(NOLOCK)
        where ID in
        <foreach collection="userIds" index="index" item="userId" open="(" close=")" separator=",">
            #{userId}
        </foreach>
    </select>

    <select id="listLargePurchasesSuppliers"
            resultType="com.jiuji.oa.nc.largepurchasesStock.vo.res.LargePurchasesSupplierRes">
        select qd.id,
               qd.company_jc as channel_name,
               ins.name      as channel_type,
               li.invoicing_flag as invoicingFlag
        from dbo.Ok3w_qudao qd WITH (NOLOCK)
         left join dbo.insource ins WITH (NOLOCK)
        on ins.id = qd.insourceid
            left join dbo.channel_kind_link li  WITH (NOLOCK) on qd.id = li.channel_id
        where qd.isAuction = 1
          and li.kind = 3
          and li.channel_state = 1
    </select>

    <select id="listLargePurchasesSuppliersOld"
            resultType="com.jiuji.oa.nc.largepurchasesStock.vo.res.LargePurchasesSupplierRes">
        select qd.id,
               qd.company_jc as channel_name,
               ins.name      as channel_type
        from dbo.Ok3w_qudao qd WITH(NOLOCK)
        left join insource ins with(nolock)
        on ins.id = qd.insourceid
        where qd.ispass = 1 and qd.isAuction = 1
    </select>

    <select id="listLargePurchasesSuppliers1"
            resultType="com.jiuji.oa.nc.largepurchasesStock.vo.res.LargePurchasesSupplierRes">
        select qd.id,
               qd.company_jc as channel_name,
               ins.name      as channel_type
        from dbo.Ok3w_qudao qd WITH(NOLOCK)
        left join dbo.insource ins with(nolock)
        on ins.id = qd.insourceid
        left join dbo.channel_kind_link li with(nolock) on qd.id = li.channel_id
        where  li.kind = 3 and li.channel_state =1
    </select>

    <select id="getSupplyIdByUserId" resultType="java.lang.Integer">
        SELECT top 1 q.id
        FROM
            Ok3w_qudao as q WITH(NOLOCK),
            channel_kind_link as c WITH(NOLOCK)
        WHERE
            q.id=c.channel_id
          and c.kind=3
          and c.channel_state=1
          and q.isAuction=1
          and q.userid=#{userId}
    </select>

    <select id="listUserIdsBySupplyIds" resultType="java.lang.Integer">
        SELECT q.userid
        FROM
        Ok3w_qudao as q WITH(NOLOCK),
        channel_kind_link as c WITH(NOLOCK)
        <where>
            q.id=c.channel_id
            and c.kind=3
            and c.channel_state=1
            and q.isAuction=1
            <if test="qudaoIds != null and qudaoIds.size != 0">
                and q.id in
                <foreach collection="qudaoIds" index="index" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>

    <select id="listLargePurchasesWithWhitelist" parameterType="com.jiuji.oa.nc.largepurchasesStock.po.LargePurchasesInfoStock"
            resultMap="BaseResultMap">
        select distinct
        li.id,
        li.bid_name,
        li.bid_start_time,
        li.bid_end_time,
        li.bid_status,
        ( CASE li.bid_status WHEN 1 THEN "未开始" WHEN 2 THEN "竞价中" WHEN 3 THEN "已结束" WHEN 4 THEN "已公示" WHEN 5 THEN "已删除"
        END ) AS bid_status_msg,
        li.pro_kinds_num,
        li.pro_total_num,
        li.real_income_num,
        li.real_income_kinds_num,
        li.update_time,
        li.complete_time,
        li.create_user_id,
        li.stock_check_user_id,
        li.update_user_id,
        li.notice_flag,
        li.create_time
        from large_purchases_info li
        left join large_purchases_info_whitelist wl on wl.large_purchases_id = li.id
        <where>
            <if test="supplyId == null or supplyId == 0">
                wl.qudao_id = -1
            </if>
            <if test="supplyId != null and supplyId != 0">
                and wl.qudao_id = #{supplyId}
            </if>
            <if test="lpInfo.id != null and lpInfo.id != 0">
                and li.id = #{lpInfo.id}
            </if>
            <if test="lpInfo.bidName != null and lpInfo.bidName != ''">
                and li.bid_name like #{lpInfo.bidName}
            </if>
            <if test="lpInfo.bidStartTime != null">
                and li.bid_start_time = #{lpInfo.bidStartTime}
            </if>
            <if test="lpInfo.bidEndTime != null">
                and li.bid_end_time = #{lpInfo.bidEndTime}
            </if>
            <if test="lpInfo.sourceChannel != null and lpInfo.sourceChannel != ''">
                <if test="lpInfo.bidStatus != null and lpInfo.bidStatus != 0">
                    <if test="lpInfo.bidStatus == 3">
                        and (li.bid_status = 3 or li.bid_status = 4)
                    </if>
                    <if test="lpInfo.bidStatus != 3">
                        and li.bid_status = #{lpInfo.bidStatus}
                    </if>
                </if>
            </if>
            <if test="lpInfo.sourceChannel == null or lpInfo.sourceChannel == ''">
                <if test="lpInfo.bidStatus != null and lpInfo.bidStatus != 0">
                    and li.bid_status = #{lpInfo.bidStatus}
                </if>
            </if>
            <if test="lpInfo.proKindsNum != null and lpInfo.proKindsNum != 0">
                and li.pro_kinds_num = #{lpInfo.proKindsNum}
            </if>
            <if test="lpInfo.proTotalNum != null and lpInfo.proTotalNum != 0">
                and li.pro_total_num = #{lpInfo.proTotalNum}
            </if>
            <if test="lpInfo.realIncomeNum != null and lpInfo.realIncomeNum != 0">
                and li.real_income_num = #{lpInfo.realIncomeNum}
            </if>
            <if test="lpInfo.updateTime != null">
                and li.update_time = #{lpInfo.updateTime}
            </if>
            <if test="lpInfo.completeTime != null">
                and li.complete_time = #{lpInfo.completeTime}
            </if>
            <if test="lpInfo.createUserId != null and lpInfo.createUserId != 0">
                and li.create_user_id = #{lpInfo.createUserId}
            </if>
            <if test="lpInfo.stockCheckUserId != null and lpInfo.stockCheckUserId != 0">
                and li.stock_check_user_id = #{lpInfo.stockCheckUserId}
            </if>
            <if test="lpInfo.updateUserId != null and lpInfo.updateUserId != 0">
                and li.update_user_id = #{lpInfo.updateUserId}
            </if>
            <if test="lpInfo.beginTime!= null">
                and li.create_time >= #{lpInfo.beginTime}
            </if>
            <if test="lpInfo.endTime!= null">
                <![CDATA[and li.create_time <= #{lpInfo.endTime}]]>
            </if>
        </where>
        order by id desc
    </select>

    <select id="doesItExistInWhitelist" resultType="java.lang.Integer">
        select count(*)
        from large_purchases_info_whitelist
        where large_purchases_id = #{purchasesId}
          and qudao_id = #{quDaoId}
    </select>
    <select id="getChannel" resultType="com.jiuji.oa.nc.largepurchasesStock.vo.res.LargePurchasesSupplierRes">
        select qd.id,
               qd.company_jc as channel_name
        from dbo.Ok3w_qudao qd WITH(NOLOCK)
        left join dbo.channel_kind_link li with(nolock) on qd.id = li.channel_id
        where li.channel_state = 1 and li.kind = #{kind}
    </select>

    <select id="getChannelold" resultType="com.jiuji.oa.nc.largepurchasesStock.vo.res.LargePurchasesSupplierRes">
        select qd.id,
               qd.company_jc as channel_name
        from dbo.Ok3w_qudao qd WITH(NOLOCK)
        where qd.ispass = 1 and qd.kinds = #{kind}
    </select>
    <select id="getChannelByKindAndInsurce"
            resultType="com.jiuji.oa.nc.largepurchasesStock.vo.res.LargePurchasesSupplierRes">
        select distinct qd.id,
        qd.company_jc as channel_name
        from dbo.Ok3w_qudao qd WITH(NOLOCK)
        left join dbo.channel_kind_link li with(nolock) on qd.id = li.channel_id
        where li.channel_state = 1
        <if test="kind != null and kind != '' " >
            and li.kind = #{kind}
        </if>
        <if test="insource != null and insource != '' " >
            and qd.insourceid = #{insource}
        </if>
        <if test="keyWord != null and keyWord.trim() != '' " >
            and (qd.company_jc like #{keyWord} or qd.id like #{keyWord})
        </if>
    </select>
    <select id="getQudaoByKindAndInsurce"
            resultType="com.jiuji.oa.nc.largepurchasesStock.vo.res.LargePurchasesSupplierRes">
        select distinct qd.id,
        qd.company_jc as channel_name
        from dbo.Ok3w_qudao qd WITH(NOLOCK)
        left join dbo.channel_kind_link li on qd.id = li.channel_id
        where qd.ispass = 1
        and qd.kinds = #{kind}
        <if test="insource != null and insource != '' " >
            and qd.insourceid = #{insource}
        </if>
        <if test="keyWord != null and keyWord.trim() != '' " >
            and (qd.company_jc like #{keyWord} or qd.id like #{keyWord})
        </if>
    </select>
    <select id="listUserIds" resultType="java.lang.String">
        SELECT q.userid
        FROM
        Ok3w_qudao as q WITH(NOLOCK),
        channel_kind_link as c WITH(NOLOCK)
        <where>
            q.id=c.channel_id
            and c.kind=3
            and c.channel_state=1
            and q.isAuction=1
            <if test="qudaoIds != null and qudaoIds.size != 0">
                and q.id in
                <foreach collection="qudaoIds" index="index" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>

    </select>
</mapper>
