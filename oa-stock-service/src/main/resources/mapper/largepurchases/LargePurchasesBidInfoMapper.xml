<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.nc.largepurchasesStock.mapper.LargePurchasesBidInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.nc.largepurchasesStock.po.LargePurchasesBidInfo">
        <id column="id" property="id"/>
        <result column="large_purchases_id" property="largePurchasesId"/>
        <result column="large_purchases_pro_id" property="largePurchasesProId"/>
        <result column="supply_num" property="supplyNum"/>
        <result column="purchases_price" property="purchasesPrice"/>
        <result column="purchases_price_type" property="purchasesPriceType"/>
        <result column="purchases_price_type_msg" property="purchasesPriceTypeMsg"/>
        <result column="quote_time" property="quoteTime"/>
        <result column="suppliers_id" property="suppliersId"/>
        <result column="user_id" property="userId"/>
        <result column="suppliers_name" property="suppliersName"/>
        <result column="bid_status" property="bidStatus"/>
        <result column="bid_result" property="bidResult"/>
        <result column="winning_bidder_num" property="winningBidderNum"/>
        <result column="delivery_num" property="deliveryNum"/>
        <result column="winning_bidder_time" property="winningBidderTime"/>
        <result column="winning_bidder_confirm_user" property="winningBidderConfirmUser"/>
        <result column="receipts_num" property="receiptsNum"/>
        <result column="receipts_num_confirm_user" property="receiptsNumConfirmUser"/>
        <result column="confirm_receipts_time" property="confirmReceiptsTime"/>
        <result column="winning_bidder_confirm_num" property="winningBidderConfirmNum"/>
        <result column="winning_bidder_confirm_num_time" property="winningBidderConfirmNumTime"/>
        <result column="yn_receipts_num" property="yunNanReceiptsNum"/>
        <result column="gz_receipts_num" property="guiZhouReceiptsNum"/>
    </resultMap>

    <select id="getLargePurchasesBidInfoList" parameterType="com.jiuji.oa.nc.largepurchasesStock.po.LargePurchasesBidInfo"
            resultMap="BaseResultMap">
        select id,large_purchases_id,large_purchases_pro_id,supply_num,purchases_price,purchases_price_type,
        (case purchases_price_type when 1 then "含税报价(原箱)" when 2 then "非含税报价(原箱)" when 3 then "含税报价(非原箱)" when 4 then
        "非含税报价(非原箱)" end) as purchases_price_type_msg,
        quote_time,suppliers_id,suppliers_id as user_id,suppliers_name,bid_status,bid_result,
        winning_bidder_num,delivery_num,winning_bidder_time,winning_bidder_confirm_user,receipts_num,receipts_num_confirm_user,
        confirm_receipts_time,winning_bidder_confirm_num,winning_bidder_confirm_num_time
        from large_purchases_bid_info
        where 1=1
        <if test="lpInfo.bidStatus != null and lpInfo.bidStatus != 0">
            <if test="lpInfo.bidStatus == 3">
                and (bid_status = 3 or bid_status = 4)
            </if>
            <if test="lpInfo.bidStatus != 3">
                and bid_status = #{lpInfo.bidStatus}
            </if>
        </if>
        <if test="lpInfo.bidStatus == null or lpInfo.bidStatus == 0">
            <![CDATA[ and bid_status <> 5 ]]>
        </if>

        <if test="lpInfo.id != null and lpInfo.id != 0">
            and id = #{lpInfo.id}
        </if>
        <if test="lpInfo.largePurchasesId != null and lpInfo.largePurchasesId != 0">
            and large_purchases_id = #{lpInfo.largePurchasesId}
        </if>
        <if test="lpInfo.largePurchasesProId != null and lpInfo.largePurchasesProId != 0">
            and large_purchases_pro_id = #{lpInfo.largePurchasesProId}
        </if>
        <if test="lpInfo.supplyNum != null and lpInfo.supplyNum != 0">
            and supply_num = #{lpInfo.supplyNum}
        </if>
        <if test="lpInfo.bidResult != null and lpInfo.bidResult != 0">
            and bid_result = #{lpInfo.bidResult}
        </if>
        <if test="lpInfo.purchasesPrice != null and lpInfo.purchasesPrice != 0">
            and purchases_price = #{lpInfo.purchasesPrice}
        </if>
        <if test="lpInfo.purchasesPriceType != null and lpInfo.purchasesPriceType != 0">
            and purchases_price_type = #{lpInfo.purchasesPriceType}
        </if>
        <if test="lpInfo.quoteTime != null">
            and quote_time = #{lpInfo.quoteTime}
        </if>
        <if test="lpInfo.suppliersId != null and lpInfo.suppliersId != 0">
            and suppliers_id = #{lpInfo.suppliersId}
        </if>
        <if test="lpInfo.suppliersName != null and lpInfo.suppliersName != ''">
            and suppliers_name = #{lpInfo.suppliersName}
        </if>
        <if test="lpInfo.winningBidderNum != null and lpInfo.winningBidderNum != 0">
            and winning_bidder_num = #{lpInfo.winningBidderNum}
        </if>
        <if test="lpInfo.deliveryNum != null and lpInfo.deliveryNum != 0">
            and delivery_num = #{lpInfo.deliveryNum}
        </if>
        <if test="lpInfo.winningBidderTime != null">
            and winning_bidder_time = #{lpInfo.winningBidderTime}
        </if>
        <if test="lpInfo.winningBidderConfirmUser != null and lpInfo.winningBidderConfirmUser != 0">
            and winning_bidder_confirm_user = #{lpInfo.winningBidderConfirmUser}
        </if>
        <if test="lpInfo.receiptsNum != null and lpInfo.receiptsNum != 0">
            and receipts_num = #{lpInfo.receiptsNum}
        </if>
        <if test="lpInfo.receiptsNumConfirmUser != null and lpInfo.receiptsNumConfirmUser != 0">
            and receipts_num_confirm_user = #{lpInfo.receiptsNumConfirmUser}
        </if>
        <if test="lpInfo.confirmReceiptsTime != null">
            and confirm_receipts_time = #{lpInfo.confirmReceiptsTime}
        </if>
        order by purchases_price desc
    </select>

    <select id="getLargePurchasesBidInfoListForOA"
            parameterType="com.jiuji.oa.nc.largepurchasesStock.po.LargePurchasesBidInfo" resultMap="BaseResultMap">
        select id,large_purchases_id,large_purchases_pro_id,supply_num,purchases_price,purchases_price_type,
        (case purchases_price_type when 1 then "国代(原箱)" when 2 then "非国代(原箱)" when 3 then "国代(非原箱)" when 4 then
        "非国代(非原箱)" end) as purchases_price_type_msg,
        quote_time,suppliers_id,suppliers_id as user_id,suppliers_name,bid_status,bid_result,
        winning_bidder_num,delivery_num,winning_bidder_time,winning_bidder_confirm_user,receipts_num,receipts_num_confirm_user,
        confirm_receipts_time,winning_bidder_confirm_num,winning_bidder_confirm_num_time, yn_receipts_num, gz_receipts_num
        from large_purchases_bid_info
        where 1=1
        <![CDATA[ and bid_status <> 5 ]]>
        <if test="lpInfo.id != null and lpInfo.id != 0">
            and id = #{lpInfo.id}
        </if>
        <if test="lpInfo.largePurchasesId != null and lpInfo.largePurchasesId != 0">
            and large_purchases_id = #{lpInfo.largePurchasesId}
        </if>
        <if test="lpInfo.largePurchasesProId != null and lpInfo.largePurchasesProId != 0">
            and large_purchases_pro_id = #{lpInfo.largePurchasesProId}
        </if>
        <if test="lpInfo.supplyNum != null and lpInfo.supplyNum != 0">
            and supply_num = #{lpInfo.supplyNum}
        </if>
        <if test="lpInfo.bidResult != null and lpInfo.bidResult != 0">
            and bid_result = #{lpInfo.bidResult}
        </if>
        <if test="lpInfo.purchasesPrice != null and lpInfo.purchasesPrice != 0">
            and purchases_price = #{lpInfo.purchasesPrice}
        </if>
        <if test="lpInfo.purchasesPriceType != null and lpInfo.purchasesPriceType != 0">
            and purchases_price_type = #{lpInfo.purchasesPriceType}
        </if>
        <if test="lpInfo.quoteTime != null">
            and quote_time = #{lpInfo.quoteTime}
        </if>
        <if test="lpInfo.suppliersId != null and lpInfo.suppliersId != 0">
            and suppliers_id = #{lpInfo.suppliersId}
        </if>
        <if test="lpInfo.suppliersName != null and lpInfo.suppliersName != ''">
            and suppliers_name = #{lpInfo.suppliersName}
        </if>
        <if test="lpInfo.winningBidderNum != null and lpInfo.winningBidderNum != 0">
            and winning_bidder_num = #{lpInfo.winningBidderNum}
        </if>
        <if test="lpInfo.deliveryNum != null and lpInfo.deliveryNum != 0">
            and delivery_num = #{lpInfo.deliveryNum}
        </if>
        <if test="lpInfo.winningBidderTime != null">
            and winning_bidder_time = #{lpInfo.winningBidderTime}
        </if>
        <if test="lpInfo.winningBidderConfirmUser != null and lpInfo.winningBidderConfirmUser != 0">
            and winning_bidder_confirm_user = #{lpInfo.winningBidderConfirmUser}
        </if>
        <if test="lpInfo.receiptsNum != null and lpInfo.receiptsNum != 0">
            and receipts_num = #{lpInfo.receiptsNum}
        </if>
        <if test="lpInfo.receiptsNumConfirmUser != null and lpInfo.receiptsNumConfirmUser != 0">
            and receipts_num_confirm_user = #{lpInfo.receiptsNumConfirmUser}
        </if>
        <if test="lpInfo.confirmReceiptsTime != null">
            and confirm_receipts_time = #{lpInfo.confirmReceiptsTime}
        </if>
        order by purchases_price
    </select>
    <select id="getMineConfirmBidListForWeb" resultType="com.jiuji.oa.nc.largepurchasesStock.res.LargePurchasesBidInfoRes">
        SELECT
        b.id,
        CONCAT(pp.pro_name,pp.pro_color) pro_name,
        p.bid_name,
        b.purchases_price_type,
        b.purchases_price,
        b.winning_bidder_num,
        b.winning_bidder_time,
        b.winning_bidder_confirm_num
        FROM
        large_purchases_bid_info b
        INNER JOIN large_purchases_pro_info pp ON b.large_purchases_pro_id = pp.id
        INNER JOIN large_purchases_info p ON pp.large_purchases_id = p.id
        WHERE
        b.suppliers_id = #{lpInfo.suppliersId}
        AND b.winning_bidder_num > 0
        AND b.winning_bidder_confirm_user IS NOT NULL
        <if test="lpInfo.start !=null and lpInfo.end !=null">
            and b.winning_bidder_time BETWEEN #{lpInfo.start} AND #{lpInfo.end}
        </if>
        ORDER BY
        b.winning_bidder_time DESC
    </select>

    <select id="getSupplyChannelNamesBySuppliersIds"
            resultType="com.jiuji.oa.nc.largepurchasesStock.po.SupplyChannelInfo">
        select distinct qd.userid userId, ins.name from Ok3w_qudao qd with(nolock)
        join insource ins with(nolock) on ins.id = qd.insourceid
        where qd.userid is not null and qd.userid in
        <foreach item="item" index="index" collection="suppliersIdList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <insert id="insertStock" parameterType="com.jiuji.oa.nc.largepurchasesStock.po.StockInfo">
        insert into dbo.product_mkc (
                                      ppriceid, inbeihuo, dtime, inbeihuodate, inbeihuoprice, inuser
                                    , kc_check, inprice, insourceid, insourceid2, areaid, origareaid, mouldFlag)
        values(
                  #{stockInfo.ppriceid},2,getdate(),getdate(),#{stockInfo.inbeihuoprice},#{stockInfo.inuser},1,#{stockInfo.inprice},
               #{stockInfo.insourceid},#{stockInfo.insourceid2},#{stockInfo.areaid},#{stockInfo.origareaid},0)
    </insert>

    <update id="updateBidInfo" parameterType="com.jiuji.oa.nc.largepurchasesStock.po.LargePurchasesBidInfo">
        update large_purchases_bid_info
        <set>
            <if test="lpInfo.supplyNum != null">
                supply_num = #{lpInfo.supplyNum},
            </if>
            <if test="lpInfo.bidStatus != null and lpInfo.bidStatus != 0">
                bid_status = #{lpInfo.bidStatus},
            </if>
            <if test="lpInfo.bidResult != null and lpInfo.bidResult != 0">
                bid_result = #{lpInfo.bidResult},
            </if>
            <if test="lpInfo.purchasesPrice != null and lpInfo.purchasesPrice != 0">
                purchases_price = #{lpInfo.purchasesPrice},
            </if>
            <if test="lpInfo.purchasesPriceType != null and lpInfo.purchasesPriceType != 0">
                purchases_price_type = #{lpInfo.purchasesPriceType},
            </if>
            <if test="lpInfo.quoteTime != null">
                quote_time = #{lpInfo.quoteTime},
            </if>
            <if test="lpInfo.suppliersName != null and lpInfo.suppliersName != ''">
                suppliers_name = #{lpInfo.suppliersName},
            </if>
            <if test="lpInfo.winningBidderNum != null">
                winning_bidder_num = #{lpInfo.winningBidderNum},
            </if>
            <if test="lpInfo.deliveryNum != null">
                delivery_num = #{lpInfo.deliveryNum},
            </if>
            <if test="lpInfo.winningBidderTime != null">
                winning_bidder_time = #{lpInfo.winningBidderTime},
            </if>
            <if test="lpInfo.winningBidderConfirmUser != null and lpInfo.winningBidderConfirmUser != 0">
                winning_bidder_confirm_user = #{lpInfo.winningBidderConfirmUser},
            </if>
            <if test="lpInfo.receiptsNum != null and lpInfo.receiptsNum >= 0">
                receipts_num = #{lpInfo.receiptsNum},
            </if>
            <if test="lpInfo.receiptsNumConfirmUser != null and lpInfo.receiptsNumConfirmUser > 0">
                receipts_num_confirm_user = #{lpInfo.receiptsNumConfirmUser},
            </if>
            <if test="lpInfo.confirmReceiptsTime != null">
                confirm_receipts_time = #{lpInfo.confirmReceiptsTime},
            </if>
            <if test="lpInfo.yunNanReceiptsNum != null and lpInfo.yunNanReceiptsNum != -1">
                yn_receipts_num = #{lpInfo.yunNanReceiptsNum},
            </if>
            <if test="lpInfo.guiZhouReceiptsNum != null and lpInfo.guiZhouReceiptsNum != -1">
                gz_receipts_num = #{lpInfo.guiZhouReceiptsNum}
            </if>
        </set>
        <where>
            id = #{lpInfo.id}
        </where>
    </update>

    <update id="updateStatus">
        update large_purchases_bid_info set bid_status=#{bidStatus} where id=#{id}
    </update>

    <update id="updateNumber">
        update large_purchases_bid_info set winning_bidder_confirm_num=#{number},winning_bidder_confirm_num_time=#{time} where id=#{id}
    </update>

</mapper>
