<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.nc.largepurchasesStock.mapper.LargePurchasesProInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.nc.largepurchasesStock.po.LargePurchasesProInfo">
        <id column="id" property="id"/>
        <result column="large_purchases_id" property="largePurchasesId"/>
        <result column="ppid" property="ppid"/>
        <result column="pro_id" property="proId"/>
        <result column="pro_name" property="proName"/>
        <result column="pro_color" property="proColor"/>
        <result column="pro_pic" property="proPic"/>
        <result column="pro_price" property="proPrice"/>
        <result column="pro_last_purchase_price" property="proLastPurchasePrice"/>
        <result column="plan_buy_num" property="planBuyNum"/>
        <result column="winning_bidder_num" property="winningBidderNum"/>
        <result column="winning_bidder_num_update_user" property="winningBidderNumUpdateUser"/>
        <result column="receipts_num" property="receiptsNum"/>
        <result column="receipts_num_update_user" property="receiptsNumUpdateUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_user" property="createUser"/>
        <result column="bid_name" property="bidName"/>
        <result column="bid_start_time" property="bidStartTime"/>
        <result column="bid_end_time" property="bidEndTime"/>
        <result column="bid_status" property="bidStatus"/>
        <result column="yn_plan_buy_num" property="yunNanPlanBuyNum"/>
        <result column="gz_plan_buy_num" property="guiZhouPlanBuyNum"/>
        <result column="is_change_yn_plan_buy_num" property="changeYnPlanBuyNum"/>
        <result column="is_change_gz_plan_buy_num" property="changeGzPlanBuyNum"/>
    </resultMap>

    <select id="getLargePurchasesProInfoList" parameterType="com.jiuji.oa.nc.largepurchasesStock.po.LargePurchasesProInfo"
            resultMap="BaseResultMap">
        select lp.whether_kuniyo as whetherKuniyo,
        lpp.id,lpp.large_purchases_id,lpp.ppid,pro_id,lpp.pro_name,lpp.pro_color,lpp.pro_pic,lpp.pro_price,lpp.pro_last_purchase_price,lpp.plan_buy_num,lpp.winning_bidder_num,
        lpp.winning_bidder_num_update_user,lpp.receipts_num,lpp.receipts_num_update_user,lpp.create_time,lpp.update_time,lpp.create_user,lp.bid_name,lp.bid_start_time,
        lpp.is_change_yn_plan_buy_num,lpp.is_change_gz_plan_buy_num,lp.bid_end_time,lp.bid_status
        from large_purchases_pro_info lpp left join large_purchases_info lp on lpp.large_purchases_id = lp.id
        where 1=1
        <if test="lpInfo.id != null and lpInfo.id != 0">
            and lpp.id = #{lpInfo.id}
        </if>
        <if test="lpInfo.largePurchasesId != null and lpInfo.largePurchasesId != 0">
            and lpp.large_purchases_id = #{lpInfo.largePurchasesId}
        </if>
        <if test="lpInfo.ppid != null and lpInfo.ppid != 0">
            and lpp.ppid = #{lpInfo.ppid}
        </if>
        <if test="lpInfo.proId != null and lpInfo.proId != 0">
            and lpp.pro_id = #{lpInfo.proId}
        </if>
        <if test="lpInfo.proName != null and lpInfo.proName != ''">
            and lpp.pro_name like concat('%',#{lpInfo.proName},'%')
        </if>
        <if test="lpInfo.proColor != null and lpInfo.proColor != ''">
            and lpp.pro_color = #{lpInfo.proColor}
        </if>
        <if test="lpInfo.proPrice != null and lpInfo.proPrice != 0">
            and lpp.pro_price = #{lpInfo.proPrice}
        </if>
        <if test="lpInfo.proLastPurchasePrice != null and lpInfo.proLastPurchasePrice != 0">
            and lpp.pro_last_purchase_price = #{lpInfo.proLastPurchasePrice}
        </if>
        <if test="lpInfo.planBuyNum != null and lpInfo.planBuyNum != ''">
            and lpp.plan_buy_num = #{lpInfo.planBuyNum}
        </if>
        <if test="lpInfo.winningBidderNum != null and lpInfo.winningBidderNum != 0">
            and lpp.winning_bidder_num = #{lpInfo.winningBidderNum}
        </if>
        <if test="lpInfo.winningBidderNumUpdateUser != null and lpInfo.winningBidderNumUpdateUser != 0">
            and lpp.winning_bidder_num_update_user = #{lpInfo.winningBidderNumUpdateUser}
        </if>
        <if test="lpInfo.receiptsNum != null and lpInfo.receiptsNum != 0">
            and lpp.receipts_num = #{lpInfo.receiptsNum}
        </if>
        <if test="lpInfo.receiptsNumUpdateUser != null and lpInfo.receiptsNumUpdateUser != 0">
            and lpp.receipts_num_update_user = #{lpInfo.receiptsNumUpdateUser}
        </if>
        <if test="lpInfo.createTime != null">
            and lpp.create_time = #{lpInfo.createTime}
        </if>
        <if test="lpInfo.updateTime != null">
            and lpp.update_time = #{lpInfo.updateTime}
        </if>
        <if test="lpInfo.createUser != null and lpInfo.createUser != 0">
            and lpp.create_user = #{lpInfo.createUser}
        </if>
    </select>

    <select id="getLargePurchasesProInfoListForOa"
            parameterType="com.jiuji.oa.nc.largepurchasesStock.po.LargePurchasesProInfo" resultMap="BaseResultMap">
        select lp.whether_kuniyo as whetherKuniyo,
        lpp.id,lpp.large_purchases_id,lpp.ppid,pro_id,lpp.pro_name,lpp.pro_color,lpp.pro_pic,lpp.pro_price,lpp.pro_last_purchase_price,lpp.plan_buy_num,lpp.winning_bidder_num,
        lpp.winning_bidder_num_update_user,lpp.receipts_num,lpp.receipts_num_update_user,lpp.create_time,lpp.update_time,lpp.create_user,lp.bid_name,lpp.yn_plan_buy_num, lpp.gz_plan_buy_num,
        lpp.is_change_yn_plan_buy_num,lpp.is_change_gz_plan_buy_num
        from large_purchases_pro_info lpp left join large_purchases_info lp on lpp.large_purchases_id = lp.id
        where 1=1
        <if test="lpInfo.id != null and lpInfo.id != 0">
            and lpp.id = #{lpInfo.id}
        </if>
        <if test="lpInfo.largePurchasesId != null and lpInfo.largePurchasesId != 0">
            and lpp.large_purchases_id = #{lpInfo.largePurchasesId}
        </if>
        <if test="lpInfo.ppid != null and lpInfo.ppid != 0">
            and lpp.ppid = #{lpInfo.ppid}
        </if>
        <if test="lpInfo.proId != null and lpInfo.proId != 0">
            and lpp.pro_id = #{lpInfo.proId}
        </if>
        <if test="lpInfo.proName != null and lpInfo.proName != ''">
            and lpp.pro_name like #{lpInfo.proName}
        </if>
        <if test="lpInfo.proColor != null and lpInfo.proColor != ''">
            and lpp.pro_color = #{lpInfo.proColor}
        </if>
        <if test="lpInfo.proPrice != null and lpInfo.proPrice != 0">
            and lpp.pro_price = #{lpInfo.proPrice}
        </if>
        <if test="lpInfo.proLastPurchasePrice != null and lpInfo.proLastPurchasePrice != 0">
            and lpp.pro_last_purchase_price = #{lpInfo.proLastPurchasePrice}
        </if>
        <if test="lpInfo.planBuyNum != null and lpInfo.planBuyNum != ''">
            and lpp.plan_buy_num = #{lpInfo.planBuyNum}
        </if>
        <if test="lpInfo.winningBidderNum != null and lpInfo.winningBidderNum != 0">
            and lpp.winning_bidder_num = #{lpInfo.winningBidderNum}
        </if>
        <if test="lpInfo.winningBidderNumUpdateUser != null and lpInfo.winningBidderNumUpdateUser != 0">
            and lpp.winning_bidder_num_update_user = #{lpInfo.winningBidderNumUpdateUser}
        </if>
        <if test="lpInfo.receiptsNum != null and lpInfo.receiptsNum != 0">
            and lpp.receipts_num = #{lpInfo.receiptsNum}
        </if>
        <if test="lpInfo.receiptsNumUpdateUser != null and lpInfo.receiptsNumUpdateUser != 0">
            and lpp.receipts_num_update_user = #{lpInfo.receiptsNumUpdateUser}
        </if>
        <if test="lpInfo.createTime != null">
            and lpp.create_time = #{lpInfo.createTime}
        </if>
        <if test="lpInfo.updateTime != null">
            and lpp.update_time = #{lpInfo.updateTime}
        </if>
        <if test="lpInfo.createUser != null and lpInfo.createUser != 0">
            and lpp.create_user = #{lpInfo.createUser}
        </if>
    </select>

    <select id="getLargePurchasesProInfoListBySuppliersNameForOa"
            parameterType="com.jiuji.oa.nc.largepurchasesStock.po.LargePurchasesProInfo" resultMap="BaseResultMap">
        select lp.whether_kuniyo as whetherKuniyo,
            lpp.id,lpp.large_purchases_id,lpp.ppid,pro_id,lpp.pro_name,lpp.pro_color,lpp.pro_pic,lpp.pro_price,lpp.pro_last_purchase_price,lpp.plan_buy_num,lpp.winning_bidder_num,
            lpp.winning_bidder_num_update_user,lpp.receipts_num,lpp.receipts_num_update_user,lpp.create_time,lpp.update_time,lpp.create_user,lp.bid_name,lpp.yn_plan_buy_num, lpp.gz_plan_buy_num,
            lpp.is_change_yn_plan_buy_num,lpp.is_change_gz_plan_buy_num
        from large_purchases_pro_info lpp
                 left join large_purchases_info lp on lpp.large_purchases_id = lp.id
                 left join large_purchases_bid_info lpbi on lpp.id = lpbi.large_purchases_pro_id
        where lpbi.suppliers_name = #{lpInfo.suppliersName} and lpbi.large_purchases_id = #{lpInfo.largePurchasesId}
    </select>

    <select id="getSalePriceByPPID" resultType="java.math.BigDecimal">
        select memberprice	from productinfo with(nolock) where dbo.productinfo.ppriceid=#{ppid}
    </select>

    <select id="getLastBuyPriceByPPID" resultType="java.math.BigDecimal">
        select inbeihuoprice from (
                                      select isnull(transferPrice, inbeihuoprice)  as inbeihuoprice
                                           , convert(char(19), inbeihuodate, 20) as inbeihuodate
                                           , ppriceid
                                           , row_number() over (partition by ppriceid order by inbeihuodate desc) rn
                                      from dbo.product_mkc with (nolock)
                                      where
        <![CDATA[ insourceid <> 5 ]]>
        and isnull(mouldFlag, 0) = 0
                                        and inbeihuoprice is not null
                                        and inbeihuodate is not null
                                        and inbeihuoprice > 0
                                        and ppriceid in ( #{ppid} ) )k where k.rn=1
    </select>

    <select id="getTopOne" resultType="java.lang.Integer">
        select ppid from large_purchases_pro_info where large_purchases_id=#{lpId} order by plan_buy_num desc limit 1
    </select>
    <select id="getMineConfirmBidListForWeb"
            resultType="com.jiuji.oa.nc.largepurchasesStock.res.LargePurchasesBidInfoRes">
        SELECT
            pp.pro_name,
            p.bid_name,
            b.purchases_price_type,
            b.purchases_price,
            b.winning_bidder_num,
            b.winning_bidder_time
        FROM
            large_purchases_bid_info b
                INNER JOIN large_purchases_pro_info pp ON b.large_purchases_pro_id = pp.id
                INNER JOIN large_purchases_info p ON pp.large_purchases_id = p.id
        WHERE
            b.suppliers_id = #{supplierId}
          AND b.winning_bidder_num > 0
          AND b.winning_bidder_confirm_user IS NOT NULL
          AND b.winning_bidder_time BETWEEN #{start} AND #{end}
        ORDER BY
            b.winning_bidder_time DESC
    </select>
    <select id="getLastBuyPrice" resultType="com.jiuji.oa.nc.largepurchasesStock.vo.res.LastBuyPrice">
        select ppriceid,inbeihuoprice from (
        select isnull(transferPrice, inbeihuoprice) as inbeihuoprice
        , convert(char(19), inbeihuodate, 20) as inbeihuodate
        , ppriceid
        , row_number() over (partition by ppriceid order by inbeihuodate desc) rn
        from dbo.product_mkc with (nolock)
        where
        <![CDATA[ insourceid <> 5 ]]>
        and isnull(mouldFlag, 0) = 0
        and inbeihuoprice is not null
        and inbeihuodate is not null
        and inbeihuoprice > 0

        <if test="ppidList != null and ppidList.size > 0 ">
            and ppriceid in
            <foreach collection="ppidList" item="it" separator="," open="(" close=")">
                #{it}
            </foreach>
        </if>
        )k where k.rn=1
    </select>

    <update id="updateProInfo" parameterType="com.jiuji.oa.nc.largepurchasesStock.po.LargePurchasesProInfo">
        update large_purchases_pro_info
        <set>
            <if test="lpInfo.proPrice != null and lpInfo.proPrice != 0">
                pro_price = #{lpInfo.proPrice},
            </if>
            <if test="lpInfo.proLastPurchasePrice != null and lpInfo.proLastPurchasePrice != 0">
                pro_last_purchase_price = #{lpInfo.proLastPurchasePrice},
            </if>
            <if test="lpInfo.planBuyNum != null and lpInfo.planBuyNum != ''">
                plan_buy_num = #{lpInfo.planBuyNum},
            </if>
            <if test="lpInfo.winningBidderNum != null and lpInfo.winningBidderNum != 0">
                winning_bidder_num = #{lpInfo.winningBidderNum},
            </if>
            <if test="lpInfo.winningBidderNumUpdateUser != null and lpInfo.winningBidderNumUpdateUser != ''">
                winning_bidder_num_update_user = #{lpInfo.winningBidderNumUpdateUser},
            </if>
            <if test="lpInfo.receiptsNum != null and lpInfo.receiptsNum != 0">
                receipts_num = #{lpInfo.receiptsNum},
            </if>
            <if test="lpInfo.receiptsNumUpdateUser != null and lpInfo.receiptsNumUpdateUser != ''">
                receipts_num_update_user = #{lpInfo.receiptsNumUpdateUser},
            </if>
            <if test="lpInfo.updateTime != null">
                update_time = #{lpInfo.updateTime}
            </if>

        </set>
        <where>
            id = #{lpInfo.id}
        </where>
    </update>

</mapper>
