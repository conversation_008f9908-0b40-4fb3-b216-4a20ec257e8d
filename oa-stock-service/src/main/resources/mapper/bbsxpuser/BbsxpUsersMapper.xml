<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.nc.bbsxpusers.mapper.BbsxpUsersMapper">
    <resultMap id="BbsxpUsersMap" type="com.jiuji.oa.nc.bbsxpusers.po.BbsxpUsers">
        <result property="id" column="id"/>
        <result property="hezuoName" column="hezuo_name"/>
        <result property="realName" column="realname"/>
        <result property="saveMoney" column="save_money"/>
        <result property="xtenant" column="xtenant"/>
        <result property="areaid" column="areaid"/>
    </resultMap>

    <select id="getSaveMoney" resultType="com.jiuji.oa.nc.bbsxpusers.vo.BbsxpUsersVO">
        SELECT opw.ID as xTenantId ,t.ID as userId, t.mobile, t.realname, t.save_money, opw.webKind, opw.webName
        FROM dbo.BBSXP_Users AS t WITH (nolock)
        LEFT JOIN OutPutWeb AS opw with(nolock) ON
        opw.userId = t.ID
        WHERE opw.xtenant = #{xTenant}
        <if test="webKind != null ">
            and opw.webKind = #{webKind}
        </if>

        <if test="nameSpaceId != null and nameSpaceId != ''">
            and opw.nameSpaceId = #{nameSpaceId}
        </if>
    </select>
    <select id="getNameById" resultType="com.jiuji.oa.nc.bbsxpusers.po.BbsxpUsers">
        SELECT u.UserName,
               u.hezuo_name,
               u.realname,
               u.wxHeadImg
        FROM dbo.BBSXP_Users as u WITH (nolock)
        WHERE u.ID = #{userID}
    </select>

    <select id="getWeixinUserName" resultType="com.jiuji.oa.nc.bbsxpusers.po.BbsxpUsers">
        SELECT top 1 u.id,
               u.UserName,
               wx.nickname as hezuo_name,
               u.realname,
               u.wxHeadImg
        FROM dbo.BBSXP_Users as u WITH (nolock)
        left join WeixinUser as wx WITH (nolock)
        on wx.userid = u.id
        WHERE u.mobile = #{mobile}
          AND wx.nickname is not NULL
        order by u.id DESC
    </select>


</mapper>
