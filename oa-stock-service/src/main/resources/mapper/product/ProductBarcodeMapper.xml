<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.nc.product.mapper.ProductBarcodeMapper">
    <update id="modifyCodeAndCount">
         update dbo.productprice set barCode=#{code},barCodeCount=#{count} where ppriceid=#{ppid}
    </update>

    <select id="listByPpid" resultType="com.jiuji.oa.nc.product.entity.ProductBarcodeEntity">
    SELECT a.id,
           a.ppriceid as ppid,
           a.barcode,
           a.isdefault as defaultFlag,
           a.operator,
           a.opxtenant,
           a.addTime,
           isnull(addTime,'1900-01-01') as time1,
           a.updatedate
    FROM productBarcode as a with(nolock)
    where a.ppriceid=#{ppid}
    order by a.isDefault desc, time1 desc
  </select>
    <select id="listByPpidList" resultType="com.jiuji.oa.nc.product.entity.ProductBarcodeEntity">
      SELECT a.id,
               a.ppriceid as ppid,
               a.barcode,
               a.isdefault as defaultFlag,
               a.operator,
               a.opxtenant,
               a.addTime,
               isnull(addTime,'1900-01-01') as time1,
               a.updatedate
        FROM productBarcode as a with(nolock)
        where a.ppriceid in
        <foreach collection="ppidList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by a.isDefault desc, time1 desc


    </select>
</mapper>
