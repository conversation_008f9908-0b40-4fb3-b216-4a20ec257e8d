<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.nc.product.mapper.ProductInfoMapper">
  <resultMap id="productListByPpidResultMap" type="com.jiuji.oa.nc.product.vo.ProductInfoWebBO">
    <result column="lcount" property="lCount" jdbcType="INTEGER"/>
    <result column="areaid" property="areaId" jdbcType="INTEGER"/>
    <result column="product_name" property="productName" jdbcType="VARCHAR"/>
    <result column="product_color" property="productColor" jdbcType="VARCHAR"/>
    <result column="ppriceid" property="ppid" jdbcType="INTEGER"/>
    <result column="barCode" property="barCode" jdbcType="VARCHAR"/>
    <result column="number" property="number" jdbcType="VARCHAR"/>
    <result column="pLabel" property="pLabelId" jdbcType="INTEGER"/>
    <result column="length" property="length" jdbcType="DECIMAL"/>
    <result column="width" property="width" jdbcType="DECIMAL"/>
    <result column="height" property="height" jdbcType="DECIMAL"/>
    <result column="weight" property="weight" jdbcType="INTEGER"/>
  </resultMap>
  <select id="productListByPpid" resultMap="productListByPpidResultMap">
    select *
    from (
    select k.lcount,
    k.areaid,
    p.product_name,
    p.product_color,
    k.ppriceid,
    p.cidFamily,
    p.barCode,
    k.number,
    p.pLabel,
    1 as rn,
    isnull(pc.length,0) as length,
    isnull(pc.width,0) as width,
    isnull(pc.height,0) as height,
    isnull(pc.weight,0) as weight
    from dbo.product_kc as k with(nolock) left join dbo.productinfo as p
    with (nolock)
    on p.ppriceid = k.ppriceid
    left join dbo.product_config as pc with (nolock)
    on p.ppriceid = pc.ppid
    <where>
      k.ppriceid = #{ppriceid}
      <if test="areaId!=null and areaId!=0">
        and areaid = #{areaId}
      </if>
    </where>
    union all
    select k.lcount, k.areaid, p.product_name, p.product_color, k.ppriceid, p.cidFamily, p.barCode,
    k.number, p.pLabel, 2 as rn,
    isnull(pc.length,0) as length,
    isnull(pc.width,0) as width,
    isnull(pc.height,0) as height,
    isnull(pc.weight,0) as weight
    from dbo.product_kc as k
    with (nolock) left join dbo.productinfo as p with (nolock)
    on p.ppriceid = k.ppriceid
    left join dbo.product_config as pc with (nolock)
    on p.ppriceid = pc.ppid
    <where>
      p.barCode like '%' + #{ppriceid} + '%'
      <if test="areaId!=null and areaId!=0">
        and areaid = #{areaId}
      </if>
    </where>
    ) as _aa
    order by rn
  </select>
  <select id="productListByBarcode" resultMap="productListByPpidResultMap">
    SELECT k.lcount,
    k.areaid,
    p.product_name,
    p.product_color,
    k.ppriceid,
    p.cidFamily,
    p.barCode,
    k.number,
    p.pLabel,
    isnull(pc.length,0) as length,
    isnull(pc.width,0) as width,
    isnull(pc.height,0) as height,
    isnull(pc.weight,0) as weight
    FROM dbo.product_kc as k WITH(NOLOCK)
    LEFT JOIN
    dbo.productinfo as p WITH(NOLOCK)
    ON
    p.ppriceid = k.ppriceid
    left join dbo.product_config as pc with (nolock)
    on p.ppriceid = pc.ppid
    <where>
      <if test="areaId!=null and areaId!=0">
        areaid = #{areaId}
      </if>
      AND EXISTS
      (
      SELECT
      1
      FROM
      dbo.productBarcode as b WITH(NOLOCK)
      WHERE
      p.ppriceid=b.ppriceid
      AND b.barCode LIKE '%' + #{ppriceid} + '%' )
    </where>
  </select>
  <select id="listByppids" resultType="com.jiuji.oa.nc.product.entity.ProductInfoEntity">
    SELECT
    a.productid,
    a.ppriceid as ppid,
    a.product_name,
    a.product_color,
    a.costprice,
    a.cid,
    a.ismobile1 as mobile,
    a.memberprice,
    a.isdel,
    a.config,
    a.brandid,
    a.cidfamily,
    a.bpic,
    a.barcode
    FROM
    productinfo as a with(nolock)
    <where>
      <if test="ppids!=null and ppids.size>0">
        a.ppriceid in
        <foreach collection="ppids" index="index" item="ppid" separator="," open="(" close=")">
          #{ppid}
        </foreach>
      </if>
    </where>
  </select>

    <select id="listByppidsWithXTenant" resultType="com.jiuji.oa.nc.product.entity.ProductInfoEntity">
    SELECT
    a.productid,
    a.ppriceid as ppid,
    a.product_name,
    a.product_color,
    a.costprice,
    a.cid,
    a.ismobile1,
    a.memberprice,
    a.isdel,
    a.config,
    a.brandid,
    a.cidfamily,
    a.barcode,
    isnull(pxi.product_label,0) as pLabel
    FROM
    productinfo as a with(nolock)
    left join product_xtenant_info pxi with(nolock) on
    pxi.ppriceid = a.ppriceid and xTenant = #{xTenant}
    <where>
      <if test="ppids!=null and ppids.size>0">
        a.ppriceid in
        <foreach collection="ppids" index="index" item="ppid" separator="," open="(" close=")">
          #{ppid}
        </foreach>
      </if>
    </where>
  </select>
  <select id="productListByPpidV2"
    resultType="com.jiuji.oa.nc.product.vo.ProductInfoWebBO">
    select *
    from (
           select p.ppriceid           as ppid,
                  p.product_name,
                  p.product_color,
                  p.cidFamily,
                  p.barCode,
                  p.pLabel,
                  p.ismobile1 mobile,
                  1                    AS rn,
                  isnull(pc.length, 0) as length,
                  isnull(pc.width, 0)  as width,
                  isnull(pc.height, 0) as height,
                  isnull(pc.weight, 0) as weight
           from productinfo as p with(nolock)
          left join product_config as pc
           with (nolock)
           on p.ppriceid = pc.ppid
           where
             p.ppriceid = #{ppriceid}
           union all
           SELECT
             p.ppriceid as ppid,
             p.product_name,
             p.product_color,
             p.cidFamily,
             p.barCode,
             p.pLabel,
             p.ismobile1 mobile,
             2 AS rn,
             ISNULL(pc.length, 0) AS LENGTH,
             ISNULL(pc.width, 0) AS width,
             ISNULL(pc.height, 0) AS height,
             ISNULL(pc.weight, 0) AS weight
           FROM
             productinfo AS p
           WITH (NOLOCK)
             LEFT JOIN product_config AS pc
           WITH (NOLOCK)
           ON
             p.ppriceid = pc.ppid
           WHERE
             p.barCode LIKE '%' + #{ppriceid} + '%'
         ) as _product
    order by rn
  </select>
  <select id="productListByBarcodeV2"
    resultType="com.jiuji.oa.nc.product.vo.ProductInfoWebBO">
    SELECT p.ppriceid           as ppid,
           p.product_name,
           p.product_color,
           p.cidFamily,
           p.barCode,
           p.pLabel,
           p.ismobile1 mobile,
           isnull(pc.length, 0) as length,
           isnull(pc.width, 0)  as width,
           isnull(pc.height, 0) as height,
           isnull(pc.weight, 0) as weight
    from productinfo as p with(NOLOCK)
    left join product_config as pc
    with (nolock)
    on p.ppriceid = pc.ppid
    where
      exists (select 1 FROM productBarcode as b WITH (NOLOCK)
      where p.ppriceid=b.ppriceid
      AND b.barCode LIKE concat('%'
        , #{barCode}
        , '%' ))
  </select>
  <select id="productListByPpidV3"
    resultType="com.jiuji.oa.nc.product.vo.ProductInfoWebBO">
    select top 1
    ppid,productid,product_name,product_color,cidFamily,barCode,pLabel,length,width,height,weight,lCount
    from (
           SELECT p.ppriceid   AS ppid,
                  p.productid,
                  p.product_name,
                  p.product_color,
                  p.cidFamily,
                  p.barCode,
                  p.pLabel,
                  ISNULL(pc.length, 0) AS length,
                  ISNULL(pc.width, 0)  AS width,
                  ISNULL(pc.height, 0) AS height,
                  ISNULL(pc.weight, 0) AS weight,
                  ISNULL(kc.lcount, 0) as lCount,
                  1 AS rank
           FROM
             productinfo as p with(nolock)
             LEFT JOIN
             product_config as pc with(nolock)
           ON
             p.ppriceid = pc.ppid
             LEFT JOIN
             product_kc as kc with(nolock)
           on p.ppriceid =kc.ppriceid
             and kc.areaid= #{areaId}
           WHERE
             p.ppriceid = #{ppid}
           union
           SELECT
             p.ppriceid as ppid,
             p.productid,
             p.product_name,
             p.product_color,
             p.cidFamily,
             p.barCode,
             p.pLabel,
             ISNULL(pc.length, 0) AS length,
             ISNULL(pc.width, 0) AS width,
             ISNULL(pc.height, 0) AS height,
             ISNULL(pc.weight, 0) AS weight,
             ISNULL(kc.lcount, 0) as lCount,
             2 AS rank
           FROM
             productinfo AS p with(nolock)
             LEFT JOIN product_config AS pc with(nolock)
           ON
             p.ppriceid = pc.ppid
             LEFT JOIN
             product_kc as kc with(nolock)
           on p.ppriceid =kc.ppriceid
             and kc.areaid= #{areaId}
           WHERE
             p.barCode LIKE '%' + convert(varchar,#{ppid}) + '%'
         ) as _product
    order by rank
  </select>
  <select id="productListByBarcodeV3"
          resultType="com.jiuji.oa.nc.product.vo.ProductInfoWebBO">
    SELECT p.ppriceid           as ppid,
           p.productid,
           p.product_name,
           p.product_color,
           p.cidFamily,
           p.barCode,
           p.pLabel,
           isnull(pc.length, 0) as length,
           isnull(pc.width, 0)  as width,
           isnull(pc.height, 0) as height,
           isnull(pc.weight, 0) as weight,
           ISNULL(kc.lcount, 0) as lCount
    from productinfo as p with(NOLOCK)
    left join product_config as pc with(nolock)
    on p.ppriceid = pc.ppid
    LEFT JOIN product_kc as kc with(nolock)
    on p.ppriceid =kc.ppriceid
      and kc.areaid= #{areaId}
    where
      exists (select 1 FROM productBarcode as b WITH(NOLOCK)
      where p.ppriceid=b.ppriceid
      AND b.barCode LIKE '%'+ #{barCode} + '%' )
  </select>

    <select id="isMobile" resultType="java.lang.Integer">
        SELECT ismobile1
        from productinfo with(nolock)
        where ppriceid=#{ppriceid}
    </select>

    <select id="getByPpidWithoutDel"
            resultType="com.jiuji.oa.nc.product.entity.ProductInfoEntity">
        SELECT a.productid,
               a.ppriceid as ppid,
               a.product_name,
               a.product_color,
               a.costprice,
               a.cid,
               a.ismobile1 as mobile,
               a.memberprice,
               a.isdel,
               a.config,
               a.brandid,
               a.cidfamily,
               a.barcode
        from productinfo a with(nolock)
        where a.ppriceid=#{ppriceid}
    </select>
    <select id="getVirtualCidList" resultType="java.lang.Integer">
        SELECT id from Category with(nolock) where isVirtualGoods = 1
    </select>
    <select id="listProdcutByProductIds" resultType="com.jiuji.oa.nc.product.entity.ProductInfoEntity">
        SELECT
        p.id as productId,
        p.name as product_name,
        p.cid,
        p.ismobile as mobile,
        p.brandID as brandid,
        p.cidFamily
        FROM
        product as p with(nolock)
        <where>
            <if test="productIds!=null and productIds.size>0">
                p.id in
                <foreach collection="productIds" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectCategoryChildrenByCid" resultType="java.lang.Long">
        select id from dbo.f_category_children (#{cid})
    </select>
    <select id="getProductStatisticsLabel" resultType="com.jiuji.oa.nc.product.vo.ProductStatisticsLabelBO">
         select info.ppriceid,label.label
        from dbo.product_label label with (nolock)
        left join dbo.productinfo info with (nolock) on info.product_id = label.product_id
        where isnull(label.is_del,0)=0
        <if test="ppidList!=null and ppidList.size>0">
            and info.ppriceid in
            <foreach collection="ppidList" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

    </select>
</mapper>
