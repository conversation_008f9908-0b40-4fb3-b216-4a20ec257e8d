<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.app.instock.mapper.PlaceLabelMapper">

  <select id="queryPageList"
    resultType="com.jiuji.oa.stock.app.instock.vo.res.InStockProInfoWebRes">
    SELECT p.ppriceid as ppid,
           p.product_name,
           p.product_color,
           p.viewsWeek,
           p.viewsWeekr,
           p.memberprice,
           l.PlaceCode as pLabel
    FROM dbo.productinfo p with (nolock)
           LEFT JOIN
         dbo.PlaceLabel l with (nolock)
         ON
           l.PPID = p.ppriceid
    WHERE p.ismobile1 = 1
      and p.product_name like '%' + #{searchKey} + '%'
  </select>
  <select id="queryProInfoByBarCode"
    resultType="com.jiuji.oa.stock.app.instock.vo.res.InStockProInfoWebRes">
    SELECT p.ppriceid as ppid,
           p.product_name,
           p.product_color,
           p.viewsWeek,
           p.viewsWeekr,
           p.memberprice,
           l.PlaceCode as pLabel
    FROM dbo.productinfo p with (nolock)
           LEFT JOIN
         dbo.PlaceLabel l with (nolock)
         ON
           l.PPID = p.ppriceid,
         productbarcode b
    WHERE p.ismobile1 = 1
      and p.ppriceid = b.ppriceid
      and b.barCode = #{searchKey}
  </select>
  <select id="queryProInfoByppid"
          resultType="com.jiuji.oa.stock.app.instock.vo.res.InStockProInfoWebRes">
    SELECT
      p.ppriceid as ppid,
      p.product_name,
      p.product_color,
      p.viewsWeek,
      p.viewsWeekr,
      p.memberprice,
      l.PlaceCode as pLabel
    FROM
      dbo.productinfo p with (nolock)
        LEFT JOIN
      dbo.PlaceLabel l with (nolock)
      ON
        l.PPID=p.ppriceid
    WHERE
      p.ismobile1=1
      and p.ppriceid = #{ppid}
  </select>

    <select id="queryProInfoBySubId" resultType="com.jiuji.oa.stock.app.instock.vo.res.InStockProInfoWebRes">
        SELECT *
        from (SELECT DISTINCT p.ppriceid  as ppid,
                              p.product_name,
                              p.product_color,
                              p.viewsWeek,
                              p.viewsWeekr,
                              p.memberprice,
                              l.PlaceCode as pLabel
              FROM dbo.productinfo p with (nolock)
                 LEFT JOIN
             dbo.PlaceLabel l
              with (nolock)
              ON
                  l.PPID = p.ppriceid
                  LEFT JOIN
                  dbo.product_mkc k
              with (nolock)
              ON
                  k.ppriceid = p.ppriceid
                  LEFT JOIN
                  dbo.mkcCaiGouBasket m
              with (nolock)
              ON
                  m.mkc_id = k.id
              WHERE p.ismobile1 = 1
                and k.kc_check =10
                and k.imei is null
                and k.ppriceid is not null
                and m.sub_id =#{searchKey}
             )temp
    </select>
</mapper>