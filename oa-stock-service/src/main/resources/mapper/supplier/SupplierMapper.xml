<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--suppress ALL -->
<mapper namespace="com.jiuji.oa.stock.supplier.mapper.SupplierMapper">
    <resultMap id="ShippingInfoVO" type="com.jiuji.oa.stock.supplier.vo.res.ShippingInfoVO">
        <id column="sub_id" property="subId"/>
        <result column="sub_date" property="subDate"/>
        <result column="sub_check" property="subCheck"/>
        <result column="sub_to" property="subTo"/>
        <result column="sub_mobile" property="subMobile"/>
        <result column="address" property="address"/>
        <result column="yingfuM" property="yingfuM"/>
        <result column="yifuM" property="yifuM"/>
        <result column="wuliucompany" property="wuliucompany"/>
        <result column="wuliuNo" property="wuliuNo"/>
        <result column="comment" property="comment"/>
    </resultMap>
    <resultMap id="SubProductVO" type="com.jiuji.oa.stock.supplier.vo.res.SubProductVO">
        <id column="sub_id" property="subId"/>
        <result column="product_name" property="productName"/>
        <result column="product_color" property="productColor"/>
        <result column="basket_count" property="basketCount"/>
    </resultMap>

    <select id="companyList" resultType="com.jiuji.oa.stock.supplier.vo.res.DropDownListVO">
        select id,company as name from dbo.Ok3w_qudao with(nolock) where kinds=10 and isnull(ispass,0)=1
    </select>

    <select id="getExpressEnum" resultType="com.jiuji.oa.stock.supplier.vo.res.DropDownListVO">
        select expressName as name,expressCode as id from dbo.expressEnum WITH(NOLOCK) where isdel=0 and expressName &lt;&gt;'美团'
    </select>

    <select id="getVendorByUserId" resultType="java.lang.Integer">
        select qudao.id as id
        from dbo.Ok3w_qudao qudao with (nolock)
         left join channel_kind_link link with (nolock) on link.channel_id = qudao.id
        where link.kind = 10 and qudao.userid= #{userId}
    </select>

    <select id="getVendorByUserIdTwo" resultType="java.lang.Integer">
        select id from dbo.Ok3w_qudao with(nolock) where kinds=10 and userid = #{userId}
    </select>


    <select id="listSubsByVendor" resultMap="ShippingInfoVO">
        select * from (
        SELECT
        ROW_NUMBER() over(order by s.sub_date DESC) as rowNum,
        s.sub_id,
        s.sub_date,
        s.sub_to,
        s.sub_mobile,
        isnull( dbo.getCityName ( sa.cityid ), '' ) + isnull( sa.Address, '' ) AS address,
        s.sub_check,
        s.yifuM,
        s.yingfuM,
        s.comment,
        sa.wuliucompany,
        sa.wuliuNo
        FROM
        dbo.thirdPartySub bb WITH(NOLOCK)
        LEFT JOIN dbo.sub s WITH(NOLOCK) ON s.sub_id = bb.sub_id
        LEFT JOIN dbo.SubAddress sa WITH(NOLOCK) ON sa.sub_id = s.sub_id
        WHERE
        bb.vendor = #{vendor}
        AND bb.subType= 1
        AND s.sub_check IN ( 1, 2 ,3)
        AND s.yifuM= s.yingfuM
        <if test="resVO.startDate!=null and resVO.endDate!=null">
            AND s.sub_date BETWEEN #{resVO.startDate} AND #{resVO.endDate}
        </if>
        <if test='resVO.orderStatus!=null and resVO.orderStatus=="1"'>
            AND isnull(sa.wuliucompany,'')!='' AND isnull(sa.wuliuNo,'')!=''
        </if>
        <if test='resVO.orderStatus!=null and resVO.orderStatus=="0"'>
            AND (isnull(sa.wuliucompany,'')='' OR isnull(sa.wuliuNo,'')='')
        </if>
        <if test="resVO.inputType==1 and resVO.intValue!=0">
            AND s.sub_id = #{resVO.intValue}
        </if>
        <if test="resVO.inputType==2 and resVO.intValue!=0">
        AND NOT EXISTS (
            SELECT
            1
            FROM
            basket bk WITH(NOLOCK)
            where isnull(bk.isdel,0)!=1
            and s.sub_id=bk.sub_id
            and bk.ppriceid=#{resVO.intValue}
            )
        </if>
        <if test="resVO.inputType==3 and resVO.inputValue!=null">
            AND s.sub_to like concat(#{resVO.inputValue},'%')
        </if>
        <if test="resVO.inputType==4 and resVO.inputValue!=null">
            AND s.sub_mobile = #{resVO.inputValue}
        </if>
        AND NOT EXISTS (
        SELECT
        1
        FROM
        dbo.basket b WITH(NOLOCK)
        LEFT JOIN dbo.productVendor p WITH(NOLOCK) ON p.ppriceid = b.ppriceid
        WHERE
        bb.sub_id= b.sub_id
        AND isnull( b.isdel, 0 ) = 0
        AND p.vendor != #{vendor}
        )
        ) as c
        where rowNum between #{startIndex} and #{endIndex}
    </select>

    <select id="getSubsByVendorCount" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM (
        SELECT
        s.sub_id,
        s.sub_date,
        s.sub_to,
        s.sub_mobile,
        isnull( dbo.getCityName ( sa.cityid ), '' ) + isnull( sa.Address, '' ) AS address,
        s.sub_check,
        s.yifuM,
        s.yingfuM,
        sa.wuliucompany,
        sa.wuliuNo
        FROM
        dbo.thirdPartySub bb WITH(NOLOCK)
        LEFT JOIN dbo.sub s WITH(NOLOCK) ON s.sub_id = bb.sub_id
        LEFT JOIN dbo.SubAddress sa WITH(NOLOCK) ON sa.sub_id = s.sub_id
        WHERE
        bb.vendor = #{vendor}
        AND bb.subType= 1
        AND s.sub_check IN ( 1, 2, 3 )
        AND s.yifuM= s.yingfuM
        <if test="resVO.startDate!=null and resVO.endDate!=null">
            AND s.sub_date BETWEEN #{resVO.startDate} AND #{resVO.endDate}
        </if>
        <if test='resVO.orderStatus!=null and resVO.orderStatus=="1"'>
            AND isnull(sa.wuliucompany,'')!='' AND isnull(sa.wuliuNo,'')!=''
        </if>
        <if test='resVO.orderStatus!=null and resVO.orderStatus=="0"'>
            AND (isnull(sa.wuliucompany,'')='' OR isnull(sa.wuliuNo,'')='')
        </if>
        <if test="resVO.inputType==1 and resVO.intValue!=0">
            AND s.sub_id = #{resVO.intValue}
        </if>
        <if test="resVO.inputType==2 and resVO.intValue!=0">
            AND NOT EXISTS (
            SELECT
            1
            FROM
            basket bk WITH(NOLOCK)
            where isnull(bk.isdel,0)!=1
            and s.sub_id=bk.sub_id
            and bk.ppriceid=#{resVO.intValue}
            )
        </if>
        <if test="resVO.inputType==3 and resVO.inputValue!=null">
            AND s.sub_to like concat(#{resVO.inputValue},'%')
        </if>
        <if test="resVO.inputType==4 and resVO.inputValue!=null">
            AND s.sub_mobile = #{resVO.inputValue}
        </if>
        AND NOT EXISTS (
        SELECT
        1
        FROM
        dbo.basket b WITH(NOLOCK)
        LEFT JOIN dbo.productVendor p WITH(NOLOCK) ON p.ppriceid = b.ppriceid
        WHERE
        bb.sub_id= b.sub_id
        AND isnull( b.isdel, 0 ) = 0
        AND p.vendor != #{vendor}
        )
        ) t
    </select>


    <select id="listSubProductsByVendor" resultMap="SubProductVO">
        select b.sub_id,p.product_name,p.product_color,b.basket_count from dbo.basket b with(nolock) left join
        dbo.productinfo p with(nolock) on p.ppriceid = b.ppriceid
        where isnull(b.isdel,0)=0 and b.sub_id in
        <foreach item="subId" collection="subIdList" open="(" separator="," close=")">
            #{subId}
        </foreach>
        and p.vendor = #{vendor}
    </select>

</mapper>
