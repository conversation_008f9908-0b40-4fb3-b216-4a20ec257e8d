<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.ershou.mapper.SalfgoodsMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.stock.ershou.entity.Salfgoods">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <id property="gisground" column="gisground" jdbcType="INTEGER"/>
            <result property="gname" column="gName" jdbcType="VARCHAR"/>
            <result property="gcolor" column="gColor" jdbcType="VARCHAR"/>
            <result property="goldprice" column="gOldPrice" jdbcType="DECIMAL"/>
            <result property="gsalepirce" column="gSalePirce" jdbcType="DECIMAL"/>
            <result property="gcostprice" column="gCostPrice" jdbcType="DECIMAL"/>
            <result property="gnumber" column="gNumber" jdbcType="INTEGER"/>
            <result property="gparms" column="gparms" jdbcType="VARCHAR"/>
            <result property="gdes" column="gdes" jdbcType="VARCHAR"/>
            <result property="diszk" column="DisZk" jdbcType="VARCHAR"/>
            <result property="gleavel" column="gleavel" jdbcType="VARCHAR"/>
            <result property="gissalf" column="gissalf" jdbcType="INTEGER"/>
            <result property="gishot" column="gishot" jdbcType="INTEGER"/>
            <result property="sort" column="sort" jdbcType="INTEGER"/>
            <result property="mkcId" column="mkc_id" jdbcType="INTEGER"/>
            <result property="imei" column="imei" jdbcType="VARCHAR"/>
            <result property="isdelete" column="isdelete" jdbcType="INTEGER"/>
            <result property="ppriceid" column="ppriceid" jdbcType="INTEGER"/>
            <result property="usestate" column="useState" jdbcType="VARCHAR"/>
            <result property="wgbz" column="wgbz" jdbcType="VARCHAR"/>
            <result property="fjqd" column="fjqd" jdbcType="VARCHAR"/>
            <result property="brandid" column="brandid" jdbcType="INTEGER"/>
            <result property="wangluozhishi" column="wangluozhishi" jdbcType="VARCHAR"/>
            <result property="pic" column="pic" jdbcType="VARCHAR"/>
            <result property="gispaying" column="gispaying" jdbcType="INTEGER"/>
            <result property="payingdate" column="payingDate" jdbcType="TIMESTAMP"/>
            <result property="subId" column="sub_id" jdbcType="VARCHAR"/>
            <result property="ppriceidZz" column="ppriceid_zz" jdbcType="INTEGER"/>
            <result property="checknumber" column="checkNumber" jdbcType="INTEGER"/>
            <result property="issalftime" column="issalfTime" jdbcType="VARCHAR"/>
            <result property="mpics" column="mPics" jdbcType="VARCHAR"/>
            <result property="beginsalftime" column="beginSalfTime" jdbcType="VARCHAR"/>
            <result property="categoryid" column="categoryId" jdbcType="INTEGER"/>
            <result property="lpsource" column="lpSource" jdbcType="VARCHAR"/>
            <result property="lpdirections" column="lpDirections" jdbcType="VARCHAR"/>
            <result property="exteriordirections" column="exteriorDirections" jdbcType="VARCHAR"/>
            <result property="supportzhishi" column="supportZhishi" jdbcType="VARCHAR"/>
            <result property="changepj" column="ChangePJ" jdbcType="VARCHAR"/>
            <result property="tosalftime" column="toSalfTime" jdbcType="VARCHAR"/>
            <result property="lpfrom" column="Lpfrom" jdbcType="INTEGER"/>
            <result property="newphoneurl" column="newPhoneUrl" jdbcType="VARCHAR"/>
            <result property="isjishi" column="IsJiShi" jdbcType="BIT"/>
            <result property="jishisalftime" column="JiShiSalfTime" jdbcType="TIMESTAMP"/>
            <result property="isyouhui" column="IsYouhui" jdbcType="INTEGER"/>
            <result property="youhuitime" column="YouhuiTime" jdbcType="VARCHAR"/>
            <result property="lptips" column="LpTips" jdbcType="VARCHAR"/>
            <result property="sjuser" column="sjUser" jdbcType="VARCHAR"/>
            <result property="productid" column="productId" jdbcType="INTEGER"/>
            <result property="orderid" column="orderId" jdbcType="INTEGER"/>
            <result property="jiancejieguo" column="jiancejieguo" jdbcType="VARCHAR"/>
            <result property="zhengjipinggu" column="zhengjipinggu" jdbcType="VARCHAR"/>
            <result property="gneicun" column="gNeicun" jdbcType="VARCHAR"/>
            <result property="gcd" column="gCd" jdbcType="VARCHAR"/>
            <result property="gyingpan" column="gYingpan" jdbcType="VARCHAR"/>
            <result property="gxianka" column="gXianka" jdbcType="VARCHAR"/>
            <result property="gcpu" column="gCpu" jdbcType="VARCHAR"/>
            <result property="hsproductid" column="hsproductid" jdbcType="INTEGER"/>
            <result property="peizhi" column="peizhi" jdbcType="VARCHAR"/>
            <result property="createdate" column="createDate" jdbcType="VARCHAR"/>
            <result property="baoxiudate" column="baoxiuDate" jdbcType="VARCHAR"/>
            <result property="baoxiuenddate" column="baoxiuEndDate" jdbcType="VARCHAR"/>
            <result property="weixiuls" column="weixiuls" jdbcType="VARCHAR"/>
            <result property="sjprice" column="sjprice" jdbcType="DECIMAL"/>
            <result property="lastpricedate" column="lastpricedate" jdbcType="TIMESTAMP"/>
            <result property="lastpriceuser" column="lastpriceuser" jdbcType="VARCHAR"/>
            <result property="gbiaoguanchicun" column="gBiaoGuanChiCun" jdbcType="VARCHAR"/>
            <result property="gpingmuchicun" column="gPingmuchicun" jdbcType="VARCHAR"/>
            <result property="xtenant" column="xtenant" jdbcType="INTEGER"/>
            <result property="totalviews" column="totalViews" jdbcType="INTEGER"/>
            <result property="totalviews1" column="totalViews1" jdbcType="INTEGER"/>
            <result property="limitpt" column="limitpt" jdbcType="VARCHAR"/>
            <result property="areaid" column="areaid" jdbcType="INTEGER"/>
            <result property="mkcCheck" column="mkc_check" jdbcType="SMALLINT"/>
            <result property="isareadispalymachine" column="isAreaDispalyMachine" jdbcType="BIT"/>
            <result property="firstbeginsalftime" column="firstBeginSalfTime" jdbcType="TIMESTAMP"/>
            <result property="tkcount" column="tkCount" jdbcType="INTEGER"/>
            <result property="ismobile" column="ismobile" jdbcType="BIT"/>
            <result property="cityid" column="cityId" jdbcType="INTEGER"/>
            <result property="createtime" column="createTime" jdbcType="TIMESTAMP"/>
            <result property="rentflag" column="rentFlag" jdbcType="BIT"/>
            <result property="returnflag" column="returnFlag" jdbcType="BIT"/>
            <result property="panicbuying" column="panicBuying" jdbcType="INTEGER"/>
            <result property="youhuiround" column="youhuiRound" jdbcType="INTEGER"/>
            <result property="snappedup" column="snappedUp" jdbcType="TINYINT"/>
            <result property="rushprice" column="rushPrice" jdbcType="DECIMAL"/>
            <result property="newmpics" column="newMpics" jdbcType="VARCHAR"/>
            <result property="evaresultid" column="evaResultId" jdbcType="BIGINT"/>
            <result property="secondcostprice" column="secondCostPrice" jdbcType="DECIMAL"/>
            <result property="auctionid" column="auctionId" jdbcType="INTEGER"/>
            <result property="defectoutattributes" column="defectOutAttributes" jdbcType="VARCHAR"/>
            <result property="endsalftime" column="endSalfTime" jdbcType="TIMESTAMP"/>
            <result property="u8VoucherId1" column="u8_voucher_id1" jdbcType="BIGINT"/>
            <result property="u8VoucherId2" column="u8_voucher_id2" jdbcType="BIGINT"/>
            <result property="zhuanPrice" column="zhuan_price" jdbcType="DECIMAL"/>
            <result property="goodsSource" column="goods_source" jdbcType="VARCHAR"/>
            <result property="sourceTag" column="source_tag" jdbcType="VARCHAR"/>
            <result property="afterDiscountPrice" column="after_discount_price" jdbcType="DECIMAL"/>
            <result property="discountFlag" column="discount_flag" jdbcType="BIT"/>
            <result property="checkDiff" column="check_diff" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,gisground,gName,
        gColor,gOldPrice,gSalePirce,
        gCostPrice,gNumber,gparms,
        gdes,DisZk,gleavel,
        gissalf,gishot,sort,
        mkc_id,imei,isdelete,
        ppriceid,useState,wgbz,
        fjqd,brandid,wangluozhishi,
        pic,gispaying,payingDate,
        sub_id,ppriceid_zz,checkNumber,
        issalfTime,mPics,beginSalfTime,
        categoryId,lpSource,lpDirections,
        exteriorDirections,supportZhishi,ChangePJ,
        toSalfTime,Lpfrom,newPhoneUrl,
        IsJiShi,JiShiSalfTime,IsYouhui,
        YouhuiTime,LpTips,sjUser,
        productId,orderId,jiancejieguo,
        zhengjipinggu,gNeicun,gCd,
        gYingpan,gXianka,gCpu,
        hsproductid,peizhi,createDate,
        baoxiuDate,baoxiuEndDate,weixiuls,
        sjprice,lastpricedate,lastpriceuser,
        gBiaoGuanChiCun,gPingmuchicun,xtenant,
        totalViews,totalViews1,limitpt,
        areaid,mkc_check,isAreaDispalyMachine,
        firstBeginSalfTime,tkCount,ismobile,
        cityId,createTime,rentFlag,
        returnFlag,panicBuying,youhuiRound,
        snappedUp,rushPrice,newMpics,
        evaResultId,secondCostPrice,auctionId,
        defectOutAttributes,endSalfTime,u8_voucher_id1,
        u8_voucher_id2,zhuan_price,goods_source,
        source_tag,after_discount_price,discount_flag,
        check_diff
    </sql>
    <select id="getPicByPpriceId" resultType="java.lang.String">
        select top 1 sg.pic from salfGoods sg with(nolock)
        where sg.ppriceid = #{ppid}
        order by sg.id desc
    </select>
</mapper>
