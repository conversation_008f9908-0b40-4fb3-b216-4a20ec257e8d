<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.nc.partner.mapper.PartnerInfoMapper">
  <resultMap id="partnerInfoMap" type="com.jiuji.oa.nc.partner.entity.PartnerInfo">
    <id property="id" column="id"/>
    <result property="companyName" column="company_name"/>
    <result property="imgFid" column="img_fid"/>
    <result property="linkman" column="linkman"/>
    <result property="duties" column="duties"/>
    <result property="phone" column="phone"/>
    <result property="email" column="email"/>
    <result property="areaCode" column="area_code"/>
    <result property="address" column="address"/>
    <result property="source" column="source"/>
    <result property="isListed" column="is_listed"/>
    <result property="stockCode" column="stock_code"/>
    <result property="marketValue" column="market_value"/>
    <result property="organization" column="organization"/>
    <result property="salaryStructure" column="salary_structure"/>
    <result property="businessScope" column="business_scope"/>
    <result property="activeSoftware" column="active_software"/>
    <result property="typeId" column="type_id"/>
    <result property="marketingAbility" column="marketing_ability"/>
    <result property="precautionsDescription" column="precautions_description"/>
    <result property="annualIncome" column="annual_income"/>
    <result property="memberNum" column="member_num"/>
    <result property="frontStaffNum" column="front_staff_num"/>
    <result property="endStaffNum" column="end_staff_num"/>
    <result property="shopNum" column="shop_num"/>
    <result property="largePurchaseMonthlySale" column="large_purchase_monthly_sale"/>
    <result property="onlinePercent" column="online_percent"/>
    <result property="retailPercent" column="retail_percent"/>
    <result property="profitAdded" column="profit_added"/>
    <result property="profitAddedPercent" column="profit_added_percent"/>
    <result property="industryRank" column="industry_rank"/>
    <result property="signedTime" column="signed_time"/>
    <result property="contractAmount" column="contract_amount"/>
    <result property="paymentMethod" column="payment_method"/>
    <result property="devopsCosts" column="devops_costs"/>
    <result property="contractDescription" column="contract_description"/>
    <result property="processPercent" column="process_percent"/>
    <result property="expansionStage" column="expansion_stage"/>
    <result property="status" column="status"/>
    <result property="tenantId" column="tenant_id"/>
    <result property="tenantScale" column="tenant_scale"/>
    <result property="isDeleted" column="is_deleted"/>
    <result property="createTime" column="create_time"/>
    <result property="updateTime" column="update_time"/>
    <result property="createUserId" column="create_user_id"/>
    <result property="updateUserId" column="update_user_id"/>
    <result property="timeOnline" column="time_online"/>
  </resultMap>
  <select id="listShortName" resultType="com.jiuji.oa.nc.partner.vo.ShortNameVO">
    select business.business_id, info.short_name
    from partner_business business
           left join partner_info info
                     on info.id = business.company_id
  </select>
</mapper>
