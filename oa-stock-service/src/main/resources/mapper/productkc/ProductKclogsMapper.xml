<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.productkc.mapper.ProductKclogsMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.stock.productkc.entity.ProductKclogs">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="count" column="count" jdbcType="INTEGER"/>
            <result property="lastcount" column="lastcount" jdbcType="INTEGER"/>
            <result property="inprice" column="inprice" jdbcType="OTHER"/>
            <result property="area" column="area" jdbcType="VARCHAR"/>
            <result property="inuser" column="inuser" jdbcType="VARCHAR"/>
            <result property="dtime" column="dtime" jdbcType="TIMESTAMP"/>
            <result property="insource" column="insource" jdbcType="VARCHAR"/>
            <result property="check1" column="check1" jdbcType="BIT"/>
            <result property="check1dtime" column="check1dtime" jdbcType="TIMESTAMP"/>
            <result property="check2" column="check2" jdbcType="BIT"/>
            <result property="check2dtime" column="check2dtime" jdbcType="TIMESTAMP"/>
            <result property="comment" column="comment" jdbcType="VARCHAR"/>
            <result property="basketId" column="basket_id" jdbcType="BIGINT"/>
            <result property="ppriceid" column="ppriceid" jdbcType="INTEGER"/>
            <result property="shouhouId" column="shouhou_id" jdbcType="BIGINT"/>
            <result property="areaid" column="areaid" jdbcType="INTEGER"/>
            <result property="inputprice" column="inputPrice" jdbcType="OTHER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,count,lastcount,
        inprice,area,inuser,
        dtime,insource,check1,
        check1dtime,check2,check2dtime,
        comment,basket_id,ppriceid,
        shouhou_id,areaid,inputPrice
    </sql>
    <select id="listProductKcLog" resultType="com.jiuji.oa.stock.productkc.vo.res.ProductKclogsVO">
        select k.id,
               k.ppriceid,
               k.areaid,
               a.area,
               k.count,
               k.lastcount,
               k.inprice,
               k.inuser,
               k.dtime,
               k.comment,
               isnull(k.basket_id,k.shouhou_id) basketId,
               p.productid,
               p.product_name productName,
               p.product_color productColor,
               p.barCode,
               p.barCodeCount
        from product_kclogs k with(nolock)
        left join productinfo p with(nolock) on k.ppriceid=p.ppriceid
        left join areainfo a with(nolock) on k.areaid = a.id
        <where>
            <include refid="whereSql"/>
        </where>
        order by k.id desc
    </select>
    <select id="listProductKcLogOnlyJh" resultType="com.jiuji.oa.stock.productkc.vo.res.ProductKclogsOnlyJhVO">
        select
            k.*,
            p.productid,
            p.product_name,
            p.product_color,
            p.barCode,
            0 as lastcount,
            0 as inprice,
            p.barCodeCount
        from
            (
                select
                    k.ppriceid,
                    SUM(count) as count
                from
                    product_kclogs k with(nolock)
                    left join productinfo p with(nolock) on
                    k.ppriceid = p.ppriceid
                <where>
                    <include refid="whereSql"/>
                </where>
                group by
                    k.ppriceid) k
                left join productinfo p with(nolock) on
            k.ppriceid = p.ppriceid
        order by count desc
    </select>
    <select id="selectNotChangeSku" resultType="com.jiuji.oa.stock.productkc.entity.ProductKc">
        select  k.areaid,k.ppriceid
        from dbo.product_kc k with (nolock)
        <where>
             k.lcount > 0
            and not exists(select 1
            from dbo.product_kclogs log with (nolock)
            where log.ppriceid = k.ppriceid
            and log.areaid = k.areaid
            and log.dtime between #{startTime} and #{endTime})
            <if test="areaIdList != null and areaIdList.size() > 0">
                and k.areaid in
                <foreach collection="areaIdList" item="areaId" open="(" separator="," close=")">
                    #{areaId}
                </foreach>
            </if>
        </where>


    </select>

    <sql id="whereSql">
        <if test="req.areaid != null and req.areaid != ''">
            k.areaid = #{req.areaid}
        </if>
        <if test="req.key != null and req.key != ''">
            <choose>
                <when test="req.way != null and req.way == 'inuser'">
                    and k.inuser like CONCAT('%',#{req.key},'%')
                </when>
                <when test="req.way != null and req.way == 'productid'">
                    and p.productid = #{req.key}
                </when>
                <when test="req.way != null and req.way == 'ppriceid'">
                    and p.ppriceid = #{req.key}
                </when>
                <when test="req.way != null and req.way == 'basket_id'">
                    and k.basket_id = #{req.key}
                </when>
                <when test="req.way != null and req.way == 'shouhou_id'">
                    and k.shouhou_id = #{req.key}
                </when>
                <when test="req.way != null and req.way == 'comment'">
                    and k.comment = like CONCAT('%',#{req.key},'%')
                </when>
                <when test="req.way != null and req.way == 'inprice>'">
                    and k.inprice &gt;= #{req.key}
                </when>
                <when test="req.way != null and req.way == 'product_name'">
                    and p.product_name like CONCAT('%',#{req.key},'%')
                </when>
                <when test="req.way != null and req.way == 'barCode'">
                    and exists(select 1 from dbo.productBarcode b with(nolock) where p.ppriceid=b.ppriceid and b.barCode like CONCAT('%',#{req.key},'%') )
                </when>
            </choose>
        </if>
        <choose>
            <when test="req.check != null and req.check == '0'">
                and k.check1=0
            </when>
            <when test="req.check != null and req.check == '1'">
                and k.check1=1 and k.check2=0
            </when>
            <when test="req.check != null and req.check == '2'">
                and k.check2=1
            </when>
        </choose>
        <if test="req.insource != null and req.insource != ''">
            and k.insource = #{req.insource}
        </if>
        <if test="req.isgaoji != null and req.isgaoji == 1">
            <choose>
                <when test="req.dateKind != null and req.dateKind == 'dtime'">
                    and k.dtime between #{req.date1} and #{req.date2}
                </when>
                <when test="req.dateKind != null and req.dateKind == 'check1dtime'">
                    and k.check1dtime between #{req.date1} and #{req.date2}
                </when>
                <when test="req.dateKind != null and req.dateKind == 'check2dtime'">
                    and k.check2dtime between #{req.date1} and #{req.date2}
                </when>
            </choose>
        </if>
        <choose>
            <when test="req.kinds != null and req.kinds == 'wx'">
                and exists( select 1 from f_category_children(23)  f where f.id=p.cid )
            </when>
            <otherwise>
                and not exists( select 1 from f_category_children(23)  f where f.id=p.cid )
            </otherwise>
        </choose>
        <if test="req.kind2 != null and req.kind2 == 'input'">
            and k.count &gt; 0
        </if>
        <if test="req.kind2 != null and req.kind2 == 'output'">
            and k.count &lt; 0
        </if>
        <if test="req.kind3 != null and req.kind3 == 'onlywx'">
            and k.shouhou_id is not null
        </if>
        <if test="req.kind3 != null and req.kind3 == 'onlysell'">
            and k.basket_id is not null
        </if>
        <if test="req.kind3 != null and req.kind3 == 'onlyin'">
            and k.basket_id is null and k.shouhou_id is null
        </if>
        <if test="req.showkind != null and req.showkind == 'jxc'">
            and exists(select 1 from dbo.areainfo a with(nolock) where a.id=k.areaid and a.swkind=1)
        </if>
    </sql>
</mapper>
