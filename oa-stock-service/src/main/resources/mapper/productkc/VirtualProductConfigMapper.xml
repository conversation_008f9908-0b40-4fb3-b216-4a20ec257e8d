<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.stock.productkc.mapper.VirtualProductConfigMapper">

    <select id="searchProduct" resultType="com.jiuji.oa.stock.productkc.vo.req.VirtualProductConfigVo">
        <choose>
            <when test="searchProductVo.configType == 1">
                select top 50 p.ppriceid configValue,1 configType,p.product_name productName,p.product_color productColor
                from productinfo p with(nolock)
                where p.ismobile1 = 0
                and p.cid not in (select f.id from f_category_children(23) f)
                <choose>
                    <when test="searchProductVo.keys != null and !searchProductVo.keys.isEmpty()">
                        and p.ppriceid in
                        <foreach collection="searchProductVo.keys" item="key" open="(" close=")" separator=",">
                            #{key}
                        </foreach>
                    </when>
                    <when test="searchProductVo.numberKey != null">
                        and p.ppriceid = #{searchProductVo.numberKey}
                    </when>
                    <otherwise>
                        and p.product_name like concat(#{searchProductVo.key},'%')
                    </otherwise>
                </choose>
            </when>
            <when test="searchProductVo.configType == 2">
                select top 50 p.id configValue,2 configType,p.name productName,null productColor
                from dbo.product p WITH(NOLOCK)
                where p.ismobile = 0
                and p.cid not in (select f.id from f_category_children(23) f)
                <choose>
                    <when test="searchProductVo.keys != null and !searchProductVo.keys.isEmpty()">
                        and p.id in
                        <foreach collection="searchProductVo.keys" item="key" open="(" close=")" separator=",">
                            #{key}
                        </foreach>
                    </when>
                    <when test="searchProductVo.numberKey != null">
                        and p.id = #{searchProductVo.numberKey}
                    </when>
                    <otherwise>
                        and p.name like concat(#{searchProductVo.key},'%')
                    </otherwise>
                </choose>
            </when>
        </choose>

    </select>
    <select id="listPage" resultType="com.jiuji.oa.stock.productkc.vo.req.VirtualProductConfigVo">
        select vpc.id, vpc.config_value configValue,vpc.config_type configType,vpc.create_time createTime,vpc.create_user createUser
                <choose>
                    <when test="query.configType == 1">
                        ,p.product_name productName,p.product_color productColor
                    </when>
                    <when test="query.configType == 2">
                        ,p.name productName, null productColor
                    </when>
                </choose>
        from virtual_product_config vpc with(nolock)
        <choose>
            <when test="query.configType == 1">
                left join productinfo p with(nolock) on p.ppriceid = vpc.config_value
            </when>
            <when test="query.configType == 2">
                left join dbo.product p WITH(NOLOCK) on p.id = vpc.config_value
            </when>
        </choose>
        <where>
            vpc.config_type = #{query.configType}
            <choose>
                <when test="query.configType == 1">
                    <if test="query.searchKey != null and query.searchKey.trim() != ''">
                        <choose>
                            <when test="query.searchType == 1">
                                <!--ppid-->
                                and p.ppriceid = #{query.searchKey}
                            </when>
                            <when test="query.searchType == 2">
                                <!--商品名称-->
                                and p.product_name like concat(#{query.searchKey},'%')
                            </when>
                            <when test="query.searchType == 3">
                                <!--商品id-->
                                and p.product_id = #{query.searchKey}
                            </when>
                        </choose>
                    </if>
                </when>
                <when test="query.configType == 2">
                    <if test="query.searchKey != null and query.searchKey.trim() != ''">
                        <choose>
                            <when test="query.searchType == 2">
                                <!--商品名称-->
                                and p.name like concat(#{query.searchKey},'%')
                            </when>
                            <when test="query.searchType == 3">
                                <!--商品id-->
                                and p.id = #{query.searchKey}
                            </when>
                        </choose>
                    </if>
                </when>
            </choose>
        </where>

    </select>
    <select id="isVirtualProduct" resultType="com.jiuji.cloud.stock.vo.response.IsVirtualProductResVo">
        select vpc.id configId,vpc.config_type configType,1 isVirtual
        <choose>
            <when test="query.type == 1">
                , p.ppriceid value
            </when>
            <when test="query.type == 2">
                , p.id value
            </when>
        </choose>
        from virtual_product_config vpc with(nolock)
        <choose>
            <when test="query.type == 1">
                left join productinfo p with(nolock) on (p.ppriceid = vpc.config_value and vpc.config_type = 1 or p.product_id = vpc.config_value and vpc.config_type = 2)
                where vpc.is_del=0 and p.ppriceid in
                <foreach collection="query.value" item="v" separator="," open="(" close=")">
                    #{v}
                </foreach>
            </when>
            <when test="query.type == 2">
                left join dbo.product p WITH(NOLOCK) on p.id = vpc.config_value and vpc.config_type = 2
                where vpc.is_del=0 and p.id in
                <foreach collection="query.value" item="v" separator="," open="(" close=")">
                    #{v}
                </foreach>
            </when>
        </choose>
    </select>
    <select id="listCidPage" resultType="com.jiuji.oa.stock.productkc.vo.req.VirtualProductConfigVo">
        select c.id, c.id configValue,3 configType,c.Name productName
            from category c with(nolock)
            <where>
                and c.isVirtualGoods = 1
                <if test="query.searchKey != null and query.searchKey.trim() != ''">
                    <choose>
                        <when test="query.searchType == 4">
                            <!--分类id-->
                            and c.id = #{query.searchKey}
                        </when>
                        <when test="query.searchType == 5">
                            <!--分配名称-->
                            and c.Name like concat(#{query.searchKey},'%')

                        </when>
                    </choose>
                </if>
            </where>
    </select>
    <sql id="productSelectConfigId">
        select distinct
        <choose>
            <when test="configType == 1">
                p.ppriceid
            </when>
            <when test="configType == 2">
                p.product_id
            </when>
        </choose>
        from productinfo p with(nolock)
    </sql>
    <sql id="productConfigIn">
        <choose>
            <when test="configType == 1">
                and p.ppriceid in
            </when>
            <when test="configType == 2">
                and p.product_id in
            </when>
        </choose>
        <foreach collection="configValues" item="configValue" separator="," open="(" close=")">
            #{configValue}
        </foreach>
    </sql>
    <select id="queryCaigouInfo" resultType="java.lang.Integer">
        <include refid="productSelectConfigId"></include>
        <where>
            and exists(select 1
            from caigou_sub cs with(nolock)
            left join caigou_basket cb with(nolock) on cb.sub_id = cs.id
            WHERE cs.stats !=-1 and cs.kinds = 'pj'
            and cb.ppriceid = p.ppriceid
            )
            <include refid="productConfigIn"></include>
        </where>

    </select>
    <select id="queryProductKcLog" resultType="java.lang.Integer">
        <include refid="productSelectConfigId"></include>
        <where>
            and exists(SELECT 1 from product_kclogs pk where pk.ppriceid = p.ppriceid)
            <include refid="productConfigIn"></include>
        </where>
    </select>
    <select id="queryProductKc" resultType="java.lang.Integer">
        <include refid="productSelectConfigId"></include>
        <where>
            and exists(SELECT 1 from product_kc pk where pk.ppriceid = p.ppriceid and pk.leftCount>0)
            <include refid="productConfigIn"></include>
        </where>
    </select>
    <select id="querySubInfo" resultType="java.lang.Integer">
        <include refid="productSelectConfigId"></include>
        <where>
            and exists(select 1
            from sub s with(nolock)
            left join basket b with(nolock) on b.sub_id = s.sub_id and isnull(b.isdel, 0)=0
            WHERE b.ppriceid = p.ppriceid and S.sub_check NOT IN (4)
            )
            <include refid="productConfigIn"></include>
        </where>
    </select>
    <select id="queryVirtualProductBySkuIds"
            resultType="com.jiuji.oa.stock.productkc.vo.res.VirtualProductInfo">
        select p.ppriceid skuId,c.isVirtualGoods
        from dbo.productinfo p with(nolock)
        left join dbo.category c with(nolock) on p.cid=c.ID
        where isnull(c.isVirtualGoods,0) = 1
        and p.ppriceid in
        <foreach collection="skuIdList" item="skuId" separator="," open="(" close=")">
            #{skuId}
        </foreach>
        union
        select p.ppriceid skuId,1 isVirtualGoods from virtual_product_config c with(nolock)
        inner join productinfo p with(nolock) on p.product_id = c.config_value
        left join virtual_product_cost_config v with(nolock) on v.ppriceid = p.ppriceid and v.is_enable = 1 and v.is_del = 0
        where c.is_del = 0 and c.config_type = 2
        and p.ppriceid in
        <foreach collection="skuIdList" item="skuId" separator="," open="(" close=")">
            #{skuId}
        </foreach>
        UNION
        select p.ppriceid skuId,1 isVirtualGoods from virtual_product_config c with(nolock)
        inner join productinfo p with(nolock) on p.ppriceid = c.config_value
        left join virtual_product_cost_config v with(nolock) on v.ppriceid = p.ppriceid and v.is_enable = 1 and v.is_del = 0
        where c.is_del = 0 and c.config_type = 1
        and p.ppriceid in
        <foreach collection="skuIdList" item="skuId" separator="," open="(" close=")">
            #{skuId}
        </foreach>
    </select>
</mapper>
