<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.jiuji.oa.stock.productkc.mapper.ProductKcMapper">
    <resultMap id="productKcMap" type="com.jiuji.oa.stock.productkc.entity.ProductKc">
        <id property="id" column="id"/>
        <result property="lcount" column="lcount"/>
        <result property="panUser" column="pan_user"/>
        <result property="panDtime" column="pan_dtime"/>
        <result property="panCount" column="pan_count"/>
        <result property="kinds" column="kinds"/>
        <result property="area" column="area"/>
        <result property="alarmCount" column="alarm_count"/>
        <result property="inprice" column="inprice"/>
        <result property="ppriceid" column="ppriceid"/>
        <result property="alarmCount1" column="alarm_count1"/>
        <result property="number" column="number"/>
        <result property="ordercount" column="orderCount"/>
        <result property="leftcount" column="leftCount"/>
        <result property="areaid" column="areaid"/>
        <result property="inprice1" column="inprice1"/>
        <result property="displaymaxcount" column="displayMaxCount"/>
    </resultMap>
    <update id="lockPkc">
        update product_kc
        set orderCount=isnull(orderCount, 0) + #{count}
        where areaId = #{areaId}
          and ppriceid = #{ppid}
    </update>

    <update id="lockTransferkc">
        update product_kc
        set orderCount=isnull(orderCount, 0) + #{count}
        where id = #{id}
          and (isnull(orderCount, 0) + #{count}) &lt;= lcount
    </update>
    <update id="unlockTransferkc">
        update product_kc
        set orderCount=isnull(orderCount, 0) - #{count}
        where id = #{id}
          and (isnull(orderCount, 0) - #{count}) >= 0
    </update>

    <select id="selectStockByPpidList" resultType="com.jiuji.oa.stock.productkc.vo.res.StockChwInfo">
        select kc.ppriceid, a.area, kc.leftCount
        from dbo.product_kc kc with (nolock)
        left join dbo.areainfo a with (nolock) on a.id = kc.areaid
        where a.authorizeid=75 and a.ispass=1
        and kc.ppriceid in
        <foreach collection="ppidList" index="index" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>

    </select>

    <select id="getBasketCount" resultType="com.jiuji.oa.stock.inventory.vo.DifferenceDetailVo">
        select o.ppriceid as skuId,
        ISNULL(sum(o.lcount),0) as basketCount
        from dbo.basket_other o with(nolock)
        left join dbo.basket b with (nolock)
        on
        b.basket_id = o.basket_id
        where
        isnull(b.isdel
        , 0)= 0
        and o.isDone = 0
        and o.areaid = #{areaId}
        and o.ppriceid in
        <foreach collection="ppidList" index="index" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>
        group by
        o.ppriceid
    </select>

    <select id="getDisplayCount" resultType="com.jiuji.oa.stock.inventory.vo.DifferenceDetailVo">
        select
        d.ppriceid as skuId,
        ISNULL(sum(d.count_),0) as displayCount
        from
        dbo.displayProductInfo d with(nolock)
        left join dbo.product_kc k with(nolock) on
        k.ppriceid = d.ppriceid and d.curAreaId = k.areaid
        where
        d.curAreaId = #{areaId}
        and d.ppriceid in
        <foreach collection="ppidList" index="index" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>
        and isnull(ischu,0)=1
        group by
        d.ppriceid
    </select>
    <select id="getNotZeroInPriceCountWithPpid" resultType="com.jiuji.oa.stock.productkc.vo.res.InpriceCount">
        SELECT k.ppriceid,
        COUNT(1) as COUNT
        from
        product_kc k
        with (nolock)
        <where> k.inprice != 0 and
            k.ppriceid in
            <foreach collection="ppidList" index="index" close=")" open="(" separator="," item="id">
                #{id}
            </foreach>
        </where>
        GROUP by
        k.ppriceid
    </select>
    <select id="getNotZeroInPriceCountWithCid" resultType="com.jiuji.oa.stock.productkc.vo.res.InpriceCount">
        SELECT
        p.cid,
        COUNT(1) as COUNT
        from
        product_kc k with(nolock)
        left join productinfo p with(nolock) on p.ppriceid = k.ppriceid
        <where> k.inprice != 0 and
            p.cid in
            <foreach collection="cidList" index="index" close=")" open="(" separator="," item="id">
                #{id}
            </foreach>
        </where>
        GROUP by
        p.cid
    </select>
    <select id="selectNoShipmentMap" resultType="com.jiuji.oa.stock.productkc.vo.res.NoShipmentVO">
     SELECT db.ppriceid as ppid,SUM(db.lcount) as count  FROM diaobo_sub d with(nolock) left join diaobo_basket db with(nolock) on d.id =db.sub_id
     WHERE d.stats =2
         and db.ppriceid in
        <foreach collection="ppidList" index="index" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>
         AND d.areaid =#{areaId}
     GROUP by db.ppriceid
    </select>
</mapper>
