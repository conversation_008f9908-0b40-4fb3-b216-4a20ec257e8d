<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.productkc.mapper.YjDepreciationConfigMapper">

    <select id="searchYjDepreciationPage" resultType="com.jiuji.oa.nc.stock.vo.res.YjDepreciationRes">
        SELECT p.cid,cg.Name as cidName,bd.name as brandName,p.brandid,k.areaid as areaid,k.id as mkcId,trim(p.product_name + ' ' + isnull(p.product_color,'')) as
        product_name,k.staticPrice,ISNULL(SUM(ydd.delta_fee),0 ) as
        alreadyDepreciationFee,ISNULL(MIN(ydd.after_update_fee),k.staticPrice ) as residualFee,
        ISNULL(ydc.depreciation_day_fee,0 ) depreciationDayFee,MAX(ydd.this_depreciation_end_period) as lastDepreciationTime
        FROM product_mkc k with(nolock)
        LEFT join areainfo area with(nolock) on k.areaid=area.id
        LEFT join productinfo p with(nolock) on k.ppriceid=p.ppriceid
        left join category cg WITH (NOLOCK) on p.cid = cg.ID
        left join brand bd with (nolock) on bd.id = p.brandID
        LEFT join yj_depreciation_detail ydd with(nolock) on ydd.mkc_id=k.id
        LEFT join yj_depreciation_config ydc with(nolock) on ydc.product_id=p.product_id
        <where>area.ispass =1 and (isnull(k.mouldFlag,0)=1 and k.kc_check in (2,3,10) )
            <!--商品id-->
            <if test="req.searchValue != null and req.searchValue != '' and req.searchType == 1 ">
                and k.id =#{req.searchValue}
            </if>
            <!--商品id-->
            <if test="req.searchValue != null and req.searchValue != '' and req.searchType == 2 ">
                and trim(p.product_name + ' ' + isnull(p.product_color,'')) like '%' + #{req.searchValue}+'%'
            </if>
            <if test="req.searchAreaIdList != null and req.searchAreaIdList.size()>0 ">
                and area.id in
                <foreach collection="req.searchAreaIdList" item="it" separator="," open="(" close=")">
                    #{it}
                </foreach>
            </if>
            <if test="req.brandIds != null and req.brandIds.size() > 0">
                and p.brandid in
                <foreach collection="req.brandIds" index="index" item="it" separator="," open="(" close=")">
                    #{it}
                </foreach>
            </if>
            <if test="req.categoryCharSeq != null and req.categoryCharSeq != '' ">
                and p.cid in (select id from f_category_children (#{req.categoryCharSeq}))
            </if>
        </where>
        GROUP BY p.cid,cg.Name,bd.name,p.brandid,k.areaid ,k.id ,p.product_name,k.staticPrice, ydc.depreciation_day_fee,p.product_color
        <if test="req.residualFlag != null and req.residualFlag  == 1">
            HAVING ISNULL(MIN(ydd.after_update_fee),k.staticPrice ) > 0.0000
        </if>
        <if test="req.residualFlag != null and req.residualFlag  == 0">
            HAVING ISNULL(MIN(ydd.after_update_fee),k.staticPrice ) = 0.0000
        </if>
    </select>

    <select id="searchYjDepreciationDetailPage" resultType="com.jiuji.oa.nc.stock.vo.res.YjDepreciationDetailRes">
        SELECT
        p.cid,cg.Name as cidName,bd.name as brandName,p.brandid,ydd.id,ydd.mkc_id,m.imei,ydd.ppriceid,trim(p.product_name + ' ' + isnull(p.product_color,'')) as product_name,area_id,this_depreciation_month,this_depreciation_begin_period,
        this_depreciation_end_period,this_depreciation_days,this_depreciation_day_fee,
        before_update_fee,delta_fee,after_update_fee
        FROM
        yj_depreciation_detail ydd with(nolock)
        LEFT join productinfo p with(nolock) on ydd.ppriceid=p.ppriceid
        left join category cg WITH (NOLOCK) on p.cid = cg.ID
        left join brand bd with (nolock) on bd.id = p.brandID
        LEFT join product_mkc m with(nolock) on ydd.mkc_id=m.id
        <where>ydd.is_del =0 and ydd.this_depreciation_days != 0
            <if test="req.searchValue != null and req.searchValue != '' and req.searchType == 1 ">
                and ydd.mkc_id =#{req.searchValue}
            </if>
            <!--商品id-->
            <if test="req.searchValue != null and req.searchValue != '' and req.searchType == 2 ">
                and trim(p.product_name + ' ' + isnull(p.product_color,''))  like '%' + #{req.searchValue}+'%'
            </if>
            <if test="req.searchAreaIdList != null and req.searchAreaIdList.size()>0 ">
                and ydd.area_id in
                <foreach collection="req.searchAreaIdList" item="it" separator="," open="(" close=")">
                    #{it}
                </foreach>
            </if>
            <if test="req.thisDepreciationMonth != null and req.thisDepreciationMonth != ''">
                and ydd.this_depreciation_month =#{req.thisDepreciationMonth}
            </if>
            <if test="req.brandIds != null and req.brandIds.size() > 0">
                and p.brandid in
                <foreach collection="req.brandIds" index="index" item="it" separator="," open="(" close=")">
                    #{it}
                </foreach>
            </if>
            <if test="req.categoryCharSeq != null and req.categoryCharSeq != '' ">
                and p.cid in (select id from f_category_children (#{req.categoryCharSeq}))
            </if>
        </where>
        order by ydd.id
    </select>

    <select id="searchYjDepreciationConfigPage" resultType="com.jiuji.oa.nc.stock.vo.res.YjDepreciationConfigRes">
        SELECT * from (
        SELECT distinct ydc.id, ydc.product_id, ydc.depreciation_day_fee,p.product_name
        FROM yj_depreciation_config ydc with(nolock)
        LEFT join productinfo p with(nolock) on ydc.product_id=p.product_id
        <where>ydc.is_del =0
            <!--商品id-->
            <if test="req.searchValue != null and req.searchValue != '' and req.searchType == 1 ">
                and ydc.product_id =#{req.searchValue}
            </if>
            <!--商品id-->
            <if test="req.searchValue != null and req.searchValue != '' and req.searchType == 2 ">
                and p.product_name like '%' + #{req.searchValue}+'%'
            </if>
            <if test="req.cids != null and req.cids.size()>0 ">
                and p.cid in
                <foreach collection="req.cids" item="it" separator="," open="(" close=")">
                    #{it}
                </foreach>
            </if>
            <if test="req.brandIds != null and req.brandIds.size()>0 ">
                and p.brandID in
                <foreach collection="req.brandIds" item="it" separator="," open="(" close=")">
                    #{it}
                </foreach>
            </if>

        </where>
        )temp
    </select>

    <select id="searchYjDepreciationConfigPageV2" resultType="com.jiuji.oa.nc.stock.vo.res.YjDepreciationConfigRes">
        select * from (
        SELECT
        MAX(k.id) as mkcId,
        ydc.id, p.product_id, ydc.depreciation_day_fee,p.product_name
        FROM product_mkc k with(nolock)
        LEFT join productinfo p with(nolock) on k.ppriceid = p.ppriceid
        LEFT join yj_depreciation_config ydc with(nolock) on ydc.product_id=p.product_id and ydc.is_del = 0
        <where>
            (isnull(k.mouldFlag,0)= 1 and k.kc_check in (2, 3, 10) )
            and p.product_id is not null
            <!--商品id-->
            <if test="req.searchValue != null and req.searchValue != '' and req.searchType == 1 ">
                and p.product_id =#{req.searchValue}
            </if>
            <!--商品id-->
            <if test="req.searchValue != null and req.searchValue != '' and req.searchType == 2 ">
                and p.product_name like '%' + #{req.searchValue}+'%'
            </if>
            <if test="req.cids != null and req.cids.size()>0 ">
                and p.cid in
                <foreach collection="req.cids" item="it" separator="," open="(" close=")">
                    #{it}
                </foreach>
            </if>
            <if test="req.brandIds != null and req.brandIds.size()>0 ">
                and p.brandID in
                <foreach collection="req.brandIds" item="it" separator="," open="(" close=")">
                    #{it}
                </foreach>
            </if>

            <if test="req.hasDepreciationDayFee != null and req.hasDepreciationDayFee == 0">
                AND NOT EXISTS (
                SELECT 1
                FROM yj_depreciation_config ydc with(nolock) where ydc.product_id = p.product_id and ydc.is_del = 0
                )

            </if>
            <if test="req.hasDepreciationDayFee != null and req.hasDepreciationDayFee == 1">
                AND EXISTS (
                SELECT 1
                FROM yj_depreciation_config ydc with(nolock) where ydc.product_id = p.product_id and ydc.is_del = 0
                )

            </if>

        </where>
        group by ydc.id, p.product_id, ydc.depreciation_day_fee,p.product_name

        union

        SELECT
        distinct  1 as mkcId, ydc.id, ydc.product_id, ydc.depreciation_day_fee,p.product_name
        FROM yj_depreciation_config ydc with(nolock)
        LEFT join productinfo p with(nolock) on ydc.product_id=p.product_id
        where ydc.is_del =0
        AND NOT EXISTS (
        SELECT 1 FROM product_mkc k with(nolock)
        LEFT join productinfo p with(nolock) on k.ppriceid = p.ppriceid
        where  (isnull(k.mouldFlag,0)= 1 and k.kc_check in (2, 3, 10) ) and ydc.product_id=p.product_id and ydc.is_del = 0 )
        <!--商品id-->
        <if test="req.searchValue != null and req.searchValue != '' and req.searchType == 1 ">
            and ydc.product_id =#{req.searchValue}
        </if>
        <!--商品id-->
        <if test="req.searchValue != null and req.searchValue != '' and req.searchType == 2 ">
            and p.product_name like '%' + #{req.searchValue}+'%'
        </if>
        <if test="req.cids != null and req.cids.size()>0 ">
            and p.cid in
            <foreach collection="req.cids" item="it" separator="," open="(" close=")">
                #{it}
            </foreach>
        </if>
        <if test="req.brandIds != null and req.brandIds.size()>0 ">
            and p.brandID in
            <foreach collection="req.brandIds" item="it" separator="," open="(" close=")">
                #{it}
            </foreach>
        </if>
        <!--查询未配置折旧费，清空这部分的查询-->
        <if test="req.hasDepreciationDayFee != null and req.hasDepreciationDayFee == 0">
            AND 1 != 1
        </if>
      )temp
    </select>

    <delete id="deleteConfig">
        delete
        from yj_depreciation_config
        where id = #{id}
    </delete>

</mapper>
