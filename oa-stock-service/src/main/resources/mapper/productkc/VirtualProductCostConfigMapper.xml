<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.stock.productkc.mapper.VirtualProductCostConfigMapper">
    <select id="listPage"  resultType="com.jiuji.oa.stock.productkc.vo.res.ProductCostInfo">
        select product_id productId,maxUpdateTime,maxId
        from (select distinct p.product_id,max(isnull(vpcc.update_time,CONVERT(datetime, '1970-01-02', 120))) over (partition by p.product_id) as maxUpdateTime,
                              max(isnull(vpcc.id,0)) over (partition by p.product_id) as maxId
        from productinfo p with(nolock)
        left join virtual_product_cost_config vpcc with(nolock) on vpcc.ppriceid = p.ppriceid and vpcc.is_del = 0
        <include refid="pageQueryCnd"></include>) a
    </select>
    <select id="listConfigInfo" resultMap="VirtualProductCostVoResultMap">
        select p.product_id,p.product_name,p.ppriceid,p.product_color,vpcc.id cost_config_id,vpcc.cost_price,vpcc.channel_id
             ,vpcc.in_kemu, vpcc.cost_kemu, isnull(vpcc.is_enable,0) is_enable, vpcc.cost_type
        from productinfo p with(nolock)
                 left join virtual_product_cost_config vpcc with(nolock) on vpcc.ppriceid = p.ppriceid and vpcc.is_del = 0
        <include refid="pageQueryCnd"></include>
        order by vpcc.id desc
    </select>
    <!-- 定义 resultMap -->
    <resultMap id="VirtualProductCostVoResultMap" type="com.jiuji.oa.stock.productkc.vo.res.VirtualProductCostVo">

        <!-- 映射基本属性 -->
        <id property="productId" column="product_id"/>
        <result property="productName" column="product_name"/>

        <!-- 映射复杂属性 costConfigs -->
        <collection property="costConfigs" ofType="com.jiuji.oa.stock.productkc.vo.res.VirtualProductCostVo$CostConfigVo">
            <id property="ppriceid" column="ppriceid"/>
            <result property="id" column="cost_config_id"/>
            <result property="productName" column="product_name"/>
            <result property="productColor" column="product_color"/>
            <result property="costPrice" column="cost_price"/>
            <result property="costType" column="cost_type"/>
            <result property="channelId" column="channel_id"/>
            <result property="kemu" column="in_kemu"/>
            <result property="costKemu" column="cost_kemu"/>
            <result property="isEnable" column="is_enable"/>
        </collection>

    </resultMap>
    <sql id="pageQueryCnd">
        <where>
            p.ismobile1 = 0
            and exists(select 1 from virtual_product_config vpc with(nolock)
            where vpc.is_del = 0 and (p.ppriceid = vpc.config_value and vpc.config_type = 1 or p.product_id = vpc.config_value and vpc.config_type = 2))
            <if test="query.searchKey != null and query.searchKey.trim() != ''">
                <choose>
                    <when test="query.searchType == 1">
                        <!--ppid-->
                        and p.ppriceid = #{query.searchKey}
                    </when>
                    <when test="query.searchType == 2">
                        <!--商品名称-->
                        and p.product_name like concat('%',#{query.searchKey},'%')
                    </when>
                    <when test="query.searchType == 3">
                        <!--商品id-->
                        and p.product_id = #{query.searchKey}
                    </when>
                    <when test="query.searchType == 6">
                        <!--商品id-->
                        and p.ppriceid in(select pb.ppriceid from productBarcode pb with(nolock) where pb.barCode like concat('%',#{query.searchKey},'%'))
                    </when>
                </choose>
            </if>
            <if test="query.isEnable != null">
                and vpcc.is_enable = #{query.isEnable}
            </if>
            <if test="query.channelId != null">
                and vpcc.channel_id = #{query.channelId}
            </if>
            <if test="query.kemu != null and !query.kemu.trim().equals('') ">
                and vpcc.in_kemu = #{query.kemu}
            </if>
            <if test="query.costKemu != null and !query.costKemu.trim().equals('') ">
                and vpcc.cost_kemu = #{query.costKemu}
            </if>
            <if test="productIds != null and !productIds.isEmpty()">
                <foreach collection="productIds" open="and p.product_id in(" close=")" item="productId" separator=",">
                    #{productId}
                </foreach>
            </if>
        </where>
    </sql>
</mapper>
