<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.productkc.mapper.YjstockMapper">

    <select id="searchYjProductName" resultType="com.jiuji.oa.nc.stock.vo.res.YjBrandNameRes">
        SELECT distinct p.product_name as productName
        FROM product_mkc k with(nolock)
        LEFT join productinfo p with(nolock) on k.ppriceid=p.ppriceid
        <where>
            isnull(k.mouldFlag,0)=1
            <if test="req.productName != null and req.productName != ''">
                and p.product_name like '%' + #{req.productName}+'%'
            </if>
        </where>
    </select>
</mapper>
