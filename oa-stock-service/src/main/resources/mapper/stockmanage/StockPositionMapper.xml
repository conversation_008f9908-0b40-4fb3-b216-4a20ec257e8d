<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.stockmanage.mapper.StockOutPositionMapper">

    <resultMap type="com.jiuji.oa.stock.stockmanage.entity.StockOutPosition" id="StockPositionMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="warehouse" column="warehouse" jdbcType="VARCHAR"/>
        <result property="stockroomCode" column="stockroom_code" jdbcType="VARCHAR"/>
        <result property="stockAreaCode" column="stock_area_code" jdbcType="VARCHAR"/>
        <result property="shelfType" column="shelf_type" jdbcType="VARCHAR"/>
        <result property="length" column="length" jdbcType="NUMERIC"/>
        <result property="width" column="width" jdbcType="NUMERIC"/>
        <result property="height" column="height" jdbcType="NUMERIC"/>
        <result property="abcClassify" column="abc_classify" jdbcType="VARCHAR"/>
        <result property="start" column="start" jdbcType="INTEGER"/>
        <result property="attribute" column="attribute" jdbcType="VARCHAR"/>
        <result property="roadway" column="roadway" jdbcType="VARCHAR"/>
        <result property="code" column="code" jdbcType="VARCHAR"/>
        <result property="stockroomName" column="stockroom_name" jdbcType="VARCHAR"/>
        <result property="stockAreaName" column="stock_area_name" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,warehouse,stockroom_code,
        stockroom_name,stock_area_code,stock_area_name,
        shelf_type,length,width,
        height,abc_classify,start,attribute,roadway,code,
        status,create_time,update_time,
        is_delete
    </sql>
    <sql id="whereSql">
        <if test="req.warehouse != null and req.warehouse !='' ">
            and sp.warehouse = #{req.warehouse}
        </if>
        <if test="req.stockroomCode != null and req.stockroomCode !='' ">
            and sp.stockroom_code = #{req.stockroomCode}
        </if>
        <if test="req.stockAreaCode != null and req.stockAreaCode !='' ">
            and sp.stock_area_code = #{req.stockAreaCode}
        </if>
        <if test="req.status == 0 ">
            and sp.status = 0
        </if>
        <if test="req.status == 1 ">
            and sp.status = 1
        </if>
        <if test="req.code != null and req.code !='' ">
            and sp.code like concat('%',#{req.code},'%')
        </if>
        <if test="req.stockAreaType != null and req.stockAreaType !='' ">
            and sa.stock_area_type = #{req.stockAreaCode}
        </if>
    </sql>
    <select id="findByPage" resultMap="StockPositionMap">
        select
        sp.id,sp.warehouse,sp.stockroom_code,
        sp.stockroom_name,sp.stock_area_code,sp.stock_area_name,
        sp.shelf_type,sp.length,sp.width,
        sp.height,sp.abc_classify,sp.start,sp.attribute,sp.roadway,sp.code,
        sp.status,sp.create_time,sp.update_time
        from stock_out_position sp
        left join stock_out_area sa on sp.stock_area_code = sa.code
        where sp.is_delete = 0
        <include refid="whereSql"></include>
    </select>

    <update id="enable">
        update stock_out_position
        set status = #{req.status},update_time = NOW()
        where id in
        <foreach collection="req.idList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
    <update id="disable">
        update stock_out_position
        set status = 0,update_time = NOW()
        where id in
        <foreach collection="idList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
    <update id="del">
        update stock_out_position
        set is_delete = 1
        where id in
        <foreach collection="idList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
    <insert id="insertBatch">
        insert into stock_out_position (
        warehouse,stockroom_code,
        stockroom_name,stock_area_code,stock_area_name,
        shelf_type,length,width,
        height,abc_classify,start,attribute,roadway,code
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.warehouse},
            #{item.stockroomCode},
            #{item.stockroomName},
            #{item.stockAreaCode},
            #{item.stockAreaName},
            #{item.shelfType},
            #{item.length},
            #{item.width},
            #{item.height},
            #{item.abcClassify},
            #{item.start},
            #{item.attribute},
            #{item.roadway},
            #{item.code}
            )
        </foreach>
    </insert>
</mapper>

