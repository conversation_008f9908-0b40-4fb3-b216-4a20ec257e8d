<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.stockmanage.mapper.StockMkcFlowMapper">

  <update id="createNewTableByMonth">
    CREATE TABLE IF NOT EXISTS `${tableName}`
    (
      `id`             bigint unsigned  NOT NULL AUTO_INCREMENT COMMENT 'ID',
      `fk_tenant_id` int unsigned NOT NULL COMMENT '平台Id',
      `mkc_id`         bigint unsigned                         DEFAULT NULL COMMENT 'mkcId',
      `imei`           varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'imei',
      `mkc_type`       tinyint unsigned NOT NULL               DEFAULT '1' COMMENT '大件库存类型：1-新机,2-样品,3-瑕疵机',
      `stock_change_type`       tinyint unsigned NOT NULL               DEFAULT '1' COMMENT '库存变动类型：1-流水,2-占用,3-释放',
      `area_id`        bigint unsigned  NOT NULL COMMENT '门店Id',
      `ppid`           bigint unsigned  NOT NULL COMMENT 'ppriceid',
      `count`          bigint           NOT NULL               DEFAULT '0' COMMENT '库存变动数量',
      `order_id`       bigint unsigned  NOT NULL COMMENT '订单Id',
      `order_type`     tinyint unsigned NOT NULL               DEFAULT '1' COMMENT '订单类型：1-订单,2-采购单,3-调拨单,4-退货单,5-报损单,6-转样机,7-样机解锁,8-转入瑕疵机,9-转出瑕疵机,10-订单创建并备货锁定,11-订单完成或转现',
      `in_source_id`   bigint                                  DEFAULT NULL COMMENT '供应商Id',
      `inv_status_code`   bigint                               DEFAULT NULL COMMENT '库存状态',
      `warehouse_code` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '宝尊中台门店Id',
      `upc`            varchar(50) COLLATE utf8mb4_unicode_ci  DEFAULT NULL COMMENT '接口中做为商品主键',
      `is_baozun`      tinyint unsigned NOT NULL               DEFAULT '0' COMMENT '是否属于宝尊的业务：0-否，1-是',
      `business_time`  datetime                                DEFAULT CURRENT_TIMESTAMP COMMENT '业务发生时间',
      `create_time`    datetime                                DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
      `update_time`    datetime                                DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
      `delete_flag`    tinyint unsigned NOT NULL               DEFAULT '0' COMMENT '逻辑删除标志：0-未删除，1-已删除',
      PRIMARY KEY (`id`),
      KEY `idx_stock_mkc_flow_fk_tenant_id` (`fk_tenant_id`) USING BTREE,
      KEY `idx_stock_mkc_flow_mkc_type` (`mkc_type`) USING BTREE,
      KEY `idx_stock_mkc_flow_in_source_id` (`in_source_id`) USING BTREE,
      KEY `idx_stock_mkc_flow_create_time` (`create_time`) USING BTREE
    ) ENGINE = InnoDB
      DEFAULT CHARSET = utf8mb4
      COLLATE = utf8mb4_unicode_ci COMMENT ='大件出入库记录表'
  </update>
</mapper>