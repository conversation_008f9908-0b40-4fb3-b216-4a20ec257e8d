<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.stockmanage.mapper.SystemRuleMapper">

    <!-- pageSystemRule -->
    <select id="pageSystemRule" resultType="com.jiuji.oa.stock.stockmanage.dto.SystemRuleDTO">
        SELECT id, name, warehouse_code, status
        FROM stock_out_sys_rule
        WHERE is_delete = 0 AND type = #{req.type}
        <if test="req.warehouseCode != null and req.warehouseCode != ''">
            AND warehouse_code = #{req.warehouseCode}
        </if>
        ORDER BY id
    </select>

</mapper>
