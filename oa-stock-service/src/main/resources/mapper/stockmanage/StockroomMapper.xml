<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.stockmanage.mapper.StockroomMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="StockroomPageMap" type="com.jiuji.oa.stock.stockmanage.dto.StockroomDTO">
        <id column="id" property="id" />
        <result column="warehouse_code" property="warehouseCode" />
        <result column="code" property="code" />
        <result column="name" property="name" />
        <result column="area" property="area" />
        <result column="type" property="type" />
        <result column="coordinate_address" property="coordinateAddress"/>
        <result column="downstream_system" property="downstreamSystem"/>
        <result column="status" property="status"/>
    </resultMap>

    <!-- pageStockroom -->
    <select id="pageStockroom" resultMap="StockroomPageMap">
        SELECT id, warehouse_code, code, `name`, area, `type`, coordinate_address, downstream_system, status
        FROM stock_out_room
        WHERE
            is_delete = 0
            <if test="req.warehouseCode != null and req.warehouseCode != ''">
                warehouse_code = #{req.warehouseCode}
            </if>
            <if test="req.stockroomCode != null and req.stockroomCode != ''">
                code = #{req.stockroomCode}
            </if>
        ORDER BY id
    </select>

    <!-- insertStockroom -->
    <insert id="insertStockroom" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO stock_out_room (
            code, name, warehouse_code, update_time
            <if test="stockroom.area != null">
                , area
            </if>
            <if test="stockroom.type != null">
                , type
            </if>
            <if test="stockroom.coordinateAddress != null and stockroom.coordinateAddress != ''">
                , coordinate_address
            </if>
            <if test="stockroom.downstreamSystem != null and stockroom.downstreamSystem != ''">
                , downstream_system
            </if> )
        VALUES (
            #{stockroom.code}, #{stockroom.name}, #{stockroom.warehouseCode}, CURRENT_TIMESTAMP
            <if test="stockroom.area != null">
               , #{stockroom.area}
            </if>
            <if test="stockroom.type != null">
               , #{stockroom.type}
            </if>
            <if test="stockroom.coordinateAddress != null and stockroom.coordinateAddress != ''">
                , #{stockroom.coordinateAddress}
            </if>
            <if test="stockroom.downstreamSystem != null and stockroom.downstreamSystem != ''">
                , #{stockroom.downstreamSystem}
            </if> )
    </insert>

    <!-- updateStockroom -->
    <update id="updateStockroom">
        UPDATE stock_out_room
        SET name = #{stockroom.name},
            update_time = CURRENT_TIMESTAMP
        <if test="stockroom.area != null">
            , area = #{stockroom.area}
        </if>
        <if test="stockroom.type != null">
            , type = #{stockroom.type}
        </if>
        <if test="stockroom.coordinateAddress != null and stockroom.coordinateAddress != ''">
            , coordinate_address = #{stockroom.coordinateAddress}
        </if>
        <if test="stockroom.downstreamSystem != null and stockroom.downstreamSystem != ''">
            , downstream_system = #{stockroom.downstreamSystem}
        </if>
        WHERE id = #{stockroom.id}
    </update>

    <!-- getStockroom -->
    <select id="getStockroom" resultMap="StockroomPageMap">
        SELECT id, warehouse_code, code, `name`, area, type, coordinate_address, downstream_system, status
        FROM stock_out_room
        WHERE is_delete = 0 AND id = #{id}
    </select>

    <!-- countActiveStockAreaByStockroom -->
    <select id="countActiveStockAreaByStockroom" resultType="int">
        SELECT COUNT(1)
        FROM stock_out_area
        WHERE status = 1 AND stockroom_code = #{stockroomCode} AND is_delete = 0
    </select>

</mapper>
