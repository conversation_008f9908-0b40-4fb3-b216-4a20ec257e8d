<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.stockmanage.mapper.OutStockStrategyMapper">

    <!-- OutStockStrategyPageMap -->
    <resultMap id="OutStockStrategyPageMap" type="com.jiuji.oa.stock.stockmanage.dto.OutStockStrategyDTO">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="volume" property="volume" />
        <result column="warehouse_code" property="warehouseCode" />
        <result column="pick_list_type" property="pickListType" />
        <result column="order_count_upper_limit" property="orderCountUpperLimit" />
        <result column="order_count_lower_limit" property="orderCountLowerLimit" />
        <result column="product_count_upper_limit" property="productCountUpperLimit" />
        <result column="product_count_lower_limit" property="productCountLowerLimit" />
        <result column="goods_count" property="goodsCount" />
        <result column="pick_container_type" property="pickContainerType" />
        <result column="large_quantity_pieces_count" property="largeQuantityPiecesCount" />
        <result column="status" property="status" />
    </resultMap>

    <!-- pageOutStockStrategy -->
    <select id="pageOutStockStrategy" resultMap="OutStockStrategyPageMap">
        SELECT id, name, volume, warehouse_code, pick_list_type, order_count_upper_limit, order_count_lower_limit,
               product_count_upper_limit, product_count_lower_limit, goods_count, pick_container_type,
               large_quantity_pieces_count, status
        FROM stock_out_stock_strategy
        WHERE is_delete = 0
        <if test="req.warehouseCode != null and req.warehouseCode != ''">
            AND warehouse_code = #{req.warehouseCode}
        </if>
        ORDER BY id
    </select>

    <!-- pageOutStockStrategy -->
    <update id="updateOutStockStrategy">
        UPDATE stock_out_stock_strategy
        SET name = #{req.name},
            update_time = CURRENT_TIMESTAMP
        <if test="req.volume != null">
            , volume = #{req.volume}
        </if>
        <if test="req.warehouseCode != null and req.warehouseCode != ''">
            , warehouse_code = #{req.warehouseCode}
        </if>
        <if test="req.pickListType != null">
            , pick_list_type = #{req.pickListType}
        </if>
        <if test="req.orderCountUpperLimit != null">
            , order_count_upper_limit = #{req.orderCountUpperLimit}
        </if>
        <if test="req.orderCountLowerLimit != null">
            , order_count_lower_limit = #{req.orderCountLowerLimit}
        </if>
        <if test="req.productCountUpperLimit != null">
            , product_count_upper_limit = #{req.productCountUpperLimit}
        </if>
        <if test="req.productCountLowerLimit != null">
            , product_count_lower_limit = #{req.productCountLowerLimit}
        </if>
        <if test="req.goodsCount != null">
            , goods_count = #{req.goodsCount}
        </if>
        <if test="req.pickContainerType != null">
            , pick_container_type = #{req.pickContainerType}
        </if>
        <if test="req.largeQuantityPiecesCount != null">
            , large_quantity_pieces_count = #{req.largeQuantityPiecesCount}
        </if>
        <if test="req.status != null">
            , status = #{req.status}
        </if>
        WHERE is_delete = 0 AND id = #{req.id}
    </update>

</mapper>
