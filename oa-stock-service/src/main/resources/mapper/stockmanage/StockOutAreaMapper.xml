<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.stockmanage.mapper.StockOutAreaMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.stock.stockmanage.entity.StockOutArea">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="warehouse" column="warehouse" jdbcType="VARCHAR"/>
            <result property="stockroomCode" column="stockroom_code" jdbcType="VARCHAR"/>
            <result property="stockroomName" column="stockroom_name" jdbcType="VARCHAR"/>
            <result property="code" column="code" jdbcType="VARCHAR"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="stockType" column="stock_type" jdbcType="TINYINT"/>
            <result property="stockAreaUse" column="stock_area_use" jdbcType="TINYINT"/>
            <result property="stockWay" column="stock_way" jdbcType="TINYINT"/>
            <result property="stockRule" column="stock_rule" jdbcType="TINYINT"/>
            <result property="ownerCode" column="owner_code" jdbcType="VARCHAR"/>
            <result property="stockAreaType" column="stock_area_type" jdbcType="TINYINT"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,warehouse,stockroom_code,
        stockroom_name,code,name,
        stock_type,stock_area_use,stock_way,
        stock_rule,owner_code,stock_area_type,
        status,create_time,update_time
    </sql>
    <sql id="whereSql">
        <if test="req.warehouse !=null and req.warehouse !='' ">
            and warehouse like concat('%',#{req.warehouse},'%')
        </if>
        <if test="req.stockroomCode !=null and req.stockroomCode !='' ">
            and stockroom_code like concat('%',#{req.stockroomCode},'%')
        </if>
        <if test="req.code !=null and req.code !='' ">
            and code like concat('%',#{req.code},'%')
        </if>
        <if test="req.stockAreaType !=null and req.stockAreaType !='' ">
            and stock_area_type = #{req.stockAreaType}
        </if>
    </sql>
    <insert id="insertBatch">
        insert into stock_out_area (
        warehouse,stockroom_code,
        stockroom_name,code,name,
        stock_type,stock_area_use,stock_way,
        stock_rule,owner_code,stock_area_type
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.warehouse},
            #{item.stockroomCode},
            #{item.stockroomName},
            #{item.code},
            #{item.name},
            #{item.stockType},
            #{item.stockAreaUse},
            #{item.stockWay},
            #{item.stockRule},
            #{item.ownerCode},
            #{item.stockAreaType}
            )
        </foreach>
    </insert>
    <update id="enable">
        update stock_out_area
        set status = #{req.status}
        where id in
        <foreach collection="req.idList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
    <update id="disable">
        update stock_out_area
        set status = 0
        where id in
        <foreach collection="idList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
    <update id="del">
        update stock_out_area
        set is_delete = 1
        where id in
        <foreach collection="idList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"></include>
        from stock_out_area
        where is_delete = 0
        <include refid="whereSql"></include>
    </select>
</mapper>
