<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.stockmanage.mapper.StockOutContainerMapper">

    <resultMap type="com.jiuji.oa.stock.stockmanage.entity.StockOutContainer" id="StockOutContainerMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="warehouse" column="warehouse" jdbcType="VARCHAR"/>
        <result property="code" column="code" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="useStatus" column="use_status" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="whereSql">
        <if test="req.warehouse != null and req.warehouse !='' ">
            and sc.warehouse = #{req.warehouse}
        </if>
        <if test="req.status == 0 ">
            and sc.status = 0
        </if>
        <if test="req.status == 1 ">
            and sc.status = 1
        </if>
        <if test="req.useStatus != null and req.useStatus !='' ">
            and sc.use_status = #{req.useStatus}
        </if>
        <if test="req.type != null and req.type !='' ">
            and sc.type = #{req.type}
        </if>
        <if test="req.code != null and req.code !='' ">
            and sc.code like concat('%',#{req.code},'%')
        </if>
    </sql>
    <select id="findByPage" resultMap="StockOutContainerMap">
        select
        sc.id,sc.warehouse,
        sc.code,sc.type,sc.use_status,
        sc.status,sc.create_time,sc.update_time
        from stock_out_container sc
        where sc.is_delete = 0
        <include refid="whereSql"></include>
    </select>

    <update id="enable">
        update stock_out_container
        set status = #{req.status}
        where id in
        <foreach collection="req.idList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
    <update id="disable">
        update stock_out_container
        set status = 0
        where id in
        <foreach collection="idList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
    <update id="del">
        update stock_out_container
        set is_delete = 1
        where id in
        <foreach collection="idList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
    <insert id="insertBatch">
        insert into stock_out_container (
        warehouse,
        code,type,use_status,
        operator
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.warehouse},
            #{item.code},
            #{item.type},
            #{item.useStatus},
            #{item.operator}
            )
        </foreach>
    </insert>
</mapper>

