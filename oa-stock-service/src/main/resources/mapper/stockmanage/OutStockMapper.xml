<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.stockmanage.mapper.OutStockMapper">

    <!-- OutStockPageMap -->
    <resultMap id="OutStockPageMap" type="com.jiuji.oa.stock.stockmanage.dto.OutStockDTO">
        <id column="id" property="id" />
        <result column="warehouse_code" property="warehouseCode" />
        <result column="stockroom_code" property="stockroomCode" />
        <result column="stock_area_code" property="stockAreaCode" />
        <result column="order_type" property="orderType" />
        <result column="product_code" property="productCode" />
        <result column="out_stock_order" property="outStockOrder" />
        <result column="out_stock_status" property="outStockStatus" />
        <result column="express_point_group" property="expressPointGroup" />
        <result column="product_attr" property="productAttr" />
        <result column="consignees_regions" property="consigneesRegions" />
        <result column="regions_type" property="regionsType" />
        <result column="priority" property="priority" />
        <result column="product_category" property="productCategory" />
        <result column="order_change" property="orderChange" />
        <result column="check_time" property="checkTime" />
        <result column="error_reason" property="errorReason" />
        <result column="picklist" property="picklist" />
        <result column="out_stock_wave" property="outStockWave" />
        <result column="operate_user" property="operateUser" />
        <result column="operate_time" property="operateTime" />
        <result column="status" property="status" />
    </resultMap>

    <!-- pageOutStock -->
    <select id="pageOutStock" resultMap="OutStockPageMap">
        SELECT id,  warehouse_code, stockroom_code, stock_area_code, order_type, product_code, out_stock_order,
               out_stock_status, express_point_group, product_attr, consignees_regions, regions_type, priority,
               product_category, order_change, check_time, error_reason, picklist, out_stock_wave, operate_user,
               operate_time, status
        FROM stock_out_stock
        WHERE is_delete = 0
        <if test="req.warehouseCode != null and req.warehouseCode != ''">
            AND warehouse_code = #{req.warehouseCode}
        </if>
        <if test="req.stockroomCode != null and req.stockroomCode != ''">
            AND stockroom_code = #{req.stockroomCode}
        </if>
        <if test="req.stockAreaCode != null and req.stockAreaCode != ''">
            AND stock_area_code = #{req.stockAreaCode}
        </if>
        <if test="req.orderType != null">
            AND order_type = #{req.orderType}
        </if>
        <if test="req.productCode != null and req.productCode != ''">
            AND product_code LIKE concat('%', #{req.productCode}, '%')
        </if>
        <if test="req.outStockOrder != null and req.outStockOrder != ''">
            AND out_stock_order LIKE concat('%', #{req.outStockOrder}, '%')
        </if>
        <if test="req.outStockStatus != null">
            AND out_stock_status = #{req.outStockStatus}
        </if>
        <if test="req.expressPointGroup != null">
            AND express_point_group = #{req.expressPointGroup}
        </if>
        <if test="req.productAttr != null">
            AND product_attr = #{req.productAttr}
        </if>
        <if test="req.consigneesRegions != null and req.consigneesRegions != ''">
            AND consignees_regions LIKE concat('%', #{req.consigneesRegions}, '%')
        </if>
        <if test="req.regionsType != null and req.regionsType != ''">
            AND regions_type = #{req.regionsType}
        </if>
        <if test="req.priority != null">
            AND priority = #{req.priority}
        </if>
        <if test="req.categoryId != null">
            AND category_id = #{req.categoryId}
        </if>
        <if test="req.orderChange != null">
            AND order_change = #{req.orderChange}
        </if>
        <if test="req.checkStartTime != null">
            AND check_time <![CDATA[ >= ]]> #{req.checkStartTime}
        </if>
        <if test="req.checkEndTime != null">
            AND check_time <![CDATA[ <= ]]> #{req.checkEndTime}
        </if>
        ORDER BY id
    </select>

</mapper>

