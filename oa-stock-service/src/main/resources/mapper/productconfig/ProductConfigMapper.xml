<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.productconfig.mapper.ProductConfigMapper">

    <insert id="saveBatch">
        insert into third_platform_product_config (tenant_code,plat_code,product_code,sku_id,ppriceid,
        price_split,sync_off,gift_off,sync_type,sync_ratio,sync_limit,sync_first,mkc_id,library_lock,type
        ,rule_code,coupon_price
        ) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.tenantCode},#{item.platCode}, #{item.productCode}, #{item.skuId}, #{item.ppriceid},
            #{item.priceSplit},#{item.syncOff},#{item.giftOff}, #{item.syncType}, #{item.syncRatio}, #{item.syncLimit}, #{item.syncFirst}, #{item.mkcId}, #{item.libraryLock}, #{item.type}
            ,#{item.ruleCode}, #{item.couponPrice}
            )
        </foreach>
    </insert>
</mapper>
