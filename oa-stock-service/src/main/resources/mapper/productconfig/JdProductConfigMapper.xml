<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.productconfig.mapper.JdProductConfigMapper">


    <insert id="saveBatch">
        insert into jingdong_product_config (tenant_code,product_code,sku_id,ppriceid,
        price_split,sync_off,sync_type,sync_ratio,sync_limit,sync_first
        ) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.tenantCode}, #{item.productCode}, #{item.skuId}, #{item.ppriceid},
            #{item.priceSplit},#{item.syncOff}, #{item.syncType}, #{item.syncRatio}, #{item.syncLimit}, #{item.syncFirst}
            )
        </foreach>
    </insert>


</mapper>
