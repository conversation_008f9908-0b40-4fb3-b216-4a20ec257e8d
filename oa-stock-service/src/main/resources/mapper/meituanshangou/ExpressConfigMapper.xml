<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.meituan.mapper.ExpressConfigMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.meituan.entity.ExpressConfig">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="xtenantId" column="xtenant_id" jdbcType="BIGINT"/>
        <result property="expressType" column="express_type" jdbcType="INTEGER"/>
        <result property="expressName" column="express_name" jdbcType="VARCHAR"/>
        <result property="expressMerchantId" column="express_merchant_id" jdbcType="VARCHAR"/>
        <result property="callbackUrl" column="callback_url" jdbcType="VARCHAR"/>
        <result property="appKey" column="app_key" jdbcType="VARCHAR"/>
        <result property="appSecret" column="app_secret" jdbcType="VARCHAR"/>
        <result property="appVersion" column="app_version" jdbcType="VARCHAR"/>
        <result property="appUrl" column="app_url" jdbcType="VARCHAR"/>
        <result property="appUsername" column="app_username" jdbcType="VARCHAR"/>
        <result property="appPassword" column="app_password" jdbcType="VARCHAR"/>
        <result property="monthlyCost" column="monthly_cost" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="deleteFlag" column="is_delete" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,xtenant_id,express_type,
        express_name,express_merchant_id,callback_url,
        app_key,app_secret,app_version,
        app_url,app_username,app_password,
        monthly_cost,create_time,update_time,
        is_delete
    </sql>
    <select id="getExpressCompanyList" resultType="com.jiuji.oa.meituan.vo.res.ExpressCompanyListVO">
        select DISTINCT express_type ,
        express_name ,
        xtenant_id
        from lc_express_config lec
        where xtenant_id = -1
        <if test="xTenantId != null">
            or xtenant_id = #{xTenantId}
        </if>
        and is_delete = 0
    </select>
</mapper>
