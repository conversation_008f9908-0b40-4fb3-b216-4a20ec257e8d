<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.meituan.mapper.MeituanShangouPeisongMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.meituan.entity.MeituanShangouPeisong">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="subId" column="sub_id" jdbcType="VARCHAR"/>
            <result property="thirdCarrierOrderId" column="third_carrier_order_id" jdbcType="VARCHAR"/>
            <result property="wuliuId" column="wuliu_id" jdbcType="VARCHAR"/>
            <result property="courierName" column="courier_name" jdbcType="VARCHAR"/>
            <result property="courierPhone" column="courier_phone" jdbcType="VARCHAR"/>
            <result property="logisticsProviderCode" column="logistics_provider_code" jdbcType="INTEGER"/>
            <result property="logisticsStatus" column="logistics_status" jdbcType="TINYINT"/>
            <result property="latitude" column="latitude" jdbcType="VARCHAR"/>
            <result property="longitude" column="longitude" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="deleteFlag" column="is_delete" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,order_id,third_carrier_order_id,
        wuliu_id,courier_name,courier_phone,
        logistics_provider_code,logistics_status,latitude,
        longitude,create_time,update_time,
        is_delete
    </sql>

    <sql id="whereSql">
        where s.subtype in (19,32,38)
        and s.sub_check in (2)
        and datediff(HH,s.sub_date,getdate()) <![CDATA[ <= ]]> 24
    </sql>

    <select id="selectMeituanOrderSubId" resultType="java.lang.Long">
        select s.sub_id from sub s with(nolock)
        <include refid="whereSql"></include>
    </select>
    <select id="selectMeituanOrder" resultType="com.jiuji.oa.meituan.vo.res.MeituanOrderVO">
        select s.sub_id subId,
               s.sub_check subCheck,
               w.id wuliuId,
               w.nu,
               w.com,
               s.delivery delivery,
               isnull(sa.psuser, w.paijianren) paijianren
        from sub s with(nolock)
        inner join SubAddress sa with(nolock) on s.sub_id = sa.sub_id
        left join wuliu w with(nolock) on w.danhaobind = s.sub_id and w.wutype = 4
        <include refid="whereSql"></include>
    </select>
    <select id="selectMeituanOrderByPaijianren" resultType="com.jiuji.oa.meituan.vo.res.MeituanOrderVO">
        SELECT s.sub_id subId,
               w.id wuliuId,
               20 logisticsStatus
        FROM sub s with(nolock)
            inner join SubAddress sa with(nolock) on s.sub_id = sa.sub_id
            left join wuliu w with(nolock) on w.danhaobind = s.sub_id and w.wutype = 4
        <include refid="whereSql"></include>
        and s.delivery = 2
        and (sa.psuser = #{paijianren} or w.paijianren = #{paijianren})
    </select>
    <select id="selectMeituanOrderCountByPaijianren" resultType="java.lang.Long">
        SELECT count(s.sub_id) FROM sub s with(nolock)
            inner join SubAddress sa with(nolock) on s.sub_id = sa.sub_id
            left join wuliu w with(nolock) on w.danhaobind = s.sub_id and w.wutype = 4
        <include refid="whereSql"></include>
        and s.delivery = 2
        and (sa.psuser = #{user.userName} or w.paijianren = #{user.userName})
    </select>
    <select id="selectAllMeituanOrder" resultType="com.jiuji.oa.meituan.vo.res.MeituanOrderVO">
        select s.sub_id subId,
               s.sub_check subCheck,
               w.id wuliuId,
               w.nu,
               w.com
        from sub s with(nolock)
        inner join SubAddress sa with(nolock) on s.sub_id = sa.sub_id
            left join wuliu w with(nolock) on w.danhaobind = s.sub_id and w.wutype = 4
        where s.subtype in (19,32,38)
        and datediff(HH,s.sub_date,getdate()) <![CDATA[ <= ]]> 24
    </select>
    <select id="selectThirdDeliveryOrderBySub"
            resultType="com.jiuji.oa.meituan.vo.res.ThirdDeliveryMeituanOrderVO">
        select top 1 s.sub_id subId,
                        s.sub_check subCheck,
                        STUFF((select ',' + b.seller
                               FROM basket b with (nolock)
                            WHERE b.sub_id = s.sub_id
                            FOR XML Path('')), 1, 1, '') as seller,
                        w.id wuliuId,
                        w.com,
                        w.nu,
                        w.sareaid,
                        w.saddress,
                        w.raddress,
                        w.pt_user_name ptUserName,
                        w.pt_user_mobile ptUserMobile,
                        tpo.buyer_position buyerPosition
        from sub s with(nolock)
        left join wuliu w with(nolock) on w.danhaobind = s.sub_id and w.wutype = 4
        left join areainfo a with(nolock) on a.id = w.sareaid
        left join third_platform_order tpo with(nolock) on s.sub_id = tpo.sub_id
        where s.sub_check = 3
        and s.subtype in (19,32,38)
        and s.sub_id = #{subId}
    </select>
    <select id="selectPeisongOrder" resultType="com.jiuji.oa.meituan.vo.res.PeisongOrderResVO">
        SELECT top 1 s.sub_id as subId,
               isnull(t.buyer_mobile, jo.buyer_mobile) as buyerMobile,
               s.subtype as subType,
               CASE WHEN s.subtype = 19 then '美团' WHEN s.subtype = 32 then '抖音小时达' WHEN s.subtype = 38 then '淘宝小时达' else '京东' end subTypeDes
        FROM sub s WITH(NOLOCK)
        INNER JOIN wuliu w WITH(NOLOCK) ON s.sub_id=w.danhaobind
            left JOIN third_platform_order t WITH(NOLOCK) ON s.sub_id=t.sub_id
            left JOIN jingdong_order jo WITH(NOLOCK) ON s.sub_id=jo.sub_id
        WHERE w.wutype IN(6,4)
          AND s.subtype IN (18,19,32,38)
          AND w.nu = #{nu}
    </select>
    <select id="queryCompleteThirdOrderId" resultType="com.jiuji.oa.meituan.vo.res.MeituanOrderVO">
        select s.sub_id subId,
               s.sub_check subCheck,
               w.id wuliuId,
               w.nu,
               w.com
        from sub s with(nolock)
        inner join SubAddress sa with(nolock) on s.sub_id = sa.sub_id
        left join wuliu w with(nolock) on w.danhaobind = s.sub_id and w.wutype = 4
        where s.subtype in (19,32,38)
        and s.sub_check in (3)
        and s.tradeDate1 >= DATEADD(MINUTE, -3, GETDATE())
    </select>
</mapper>
