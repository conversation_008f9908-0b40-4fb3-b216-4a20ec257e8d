<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.api.mapper.AreaDataClearMapper">
    <select id="countMobileKc" resultType="java.lang.Integer">
        select COUNT(1) AS amount from product_mkc k WITH (NOLOCK)
        where k.areaid = #{areaId} and k.kc_check in (0,1,2,3,10,8,14,6,7)
    </select>

    <select id="countShouhouKc" resultType="java.lang.Integer">
        select COUNT(1) AS amount from product_mkc k WITH (NOLOCK)
        where k.areaid = #{areaId} and k.kc_check in (6)
    </select>

    <select id="countAccessorsKc" resultType="java.lang.Integer">
        select COUNT(1) AS amount FROM product_kc k WITH (NOLOCK)
        left join productinfo AS p WITH (NOLOCK) on p.ppriceid = k.ppriceid
        where k.lcount > 0 and p.cid not in (select id from f_category_children('23'))
        AND k.areaid = #{areaId}
    </select>

    <select id="countRepairAccessorsKc" resultType="java.lang.Integer">
        select COUNT(1) AS amount FROM product_kc k WITH (NOLOCK)
        left join productinfo AS p WITH (NOLOCK) on p.ppriceid = k.ppriceid
        where k.lcount > 0 and p.cid in (select id from f_category_children('23'))
          AND k.inprice  > 0
          AND k.areaid = #{areaId}
    </select>

    <select id="countOldKc" resultType="java.lang.Integer">
        select COUNT(1) AS amount FROM shouhou_huishou hs WITH(NOLOCK)
        where ISNULL(hs.toareaid,hs.areaid) = #{areaId} and isnull(hs.isdel,0)= 0
          and isnull(hs.ishuanhuo,0) = 0 and isnull(hs.hsjj_saletype,0)= 0
          and ISNULL(hs.isfan,0)= 0
          and (
                (isnull(hs.issale,0)=0 and isnull(hs.issalecheck,0) = 0 and isfan =0)
                or (isnull(hs.issale,0)=0 and hs.isfan=0 and isnull(hs.issalecheck,0) = 1)
                or ( hs.confirm is null and hs.saledate is null and isnull(hs.issale,0) != 1 and hs.isfan = 0)
                or ( hs.confirm=1)
                or ( hs.confirm=1 and hs.complete is null)
            )
    </select>

    <select id="countHuanhuoKc" resultType="java.lang.Integer">
        select COUNT(1) AS amount FROM shouhou_huishou hs WITH(NOLOCK)
        where isnull(hs.toareaid,hs.areaid) = #{areaId}
        and isdel = 0 and isnull(hs.ishuanhuo,0) = 1
        and  huanhuoUser is null and hs.isfan=0
    </select>

    <select id="countDisplayKc" resultType="java.lang.Integer">
        select  COUNT(1) AS amount FROM displayProductInfo d WITH(NOLOCK)
        where d.areaid = #{areaId} and d.stats_ in (0,1)
    </select>

    <select id="countMobileDiaobo" resultType="com.jiuji.oa.api.vo.clear.DiaoboCountData">
        select  COUNT(1) AS kcCount,1 kind FROM mkc_toarea m  WITH(NOLOCK)
        where m.areaid = #{areaId} and m.stats in (0,1,2)
        union all
        select  COUNT(1) AS kcCount,2 kind FROM mkc_toarea m  WITH(NOLOCK)
        where m.toareaid = #{areaId} and m.stats in (0,1,2)
    </select>

    <select id="countAccessorsDiaobo" resultType="com.jiuji.oa.api.vo.clear.DiaoboCountData">
        select  COUNT(1) AS kcCount,1 kind FROM diaobo_sub ds  WITH(NOLOCK)
        where ds.areaid = #{areaId} and ds.stats in (1,2,3,5) and ds.kinds='pj'
        union all
        select  COUNT(1) AS kcCount,2 kind FROM diaobo_sub ds  WITH(NOLOCK)
        where ds.toareaid = #{areaId} and ds.stats in (1,2,3,5) and ds.kinds='pj'
    </select>

    <select id="countRepairAccessorsDiaobo" resultType="com.jiuji.oa.api.vo.clear.DiaoboCountData">
        select  COUNT(1) AS kcCount,1 kind FROM diaobo_sub ds  WITH(NOLOCK)
        where ds.areaid = #{areaId} and ds.stats in (1,2,3,5) and ds.kinds='wx'
        union all
        select  COUNT(1) AS kcCount,2 kind FROM diaobo_sub ds  WITH(NOLOCK)
        where ds.toareaid = #{areaId} and ds.stats in (1,2,3,5) and ds.kinds='wx'
    </select>

    <select id="countTradeSearch" resultType="java.lang.Integer">
        select COUNT(1) AS amount
        FROM sub s WITH(NOLOCK)
        left join dbo.basket B with (nolock)
        on b.sub_id = S.sub_id
        where s.areaid = #{areaId} and isnull(b.isdel, 0)=0
        and s.sub_check IN (1, 2, 5, 6, 7)
    </select>

    <select id="countRecoverTradeSearch" resultType="java.lang.Integer">
    select COUNT(1) AS amount  FROM dbo.recover_marketSubInfo B with(nolock)
    LEFT JOIN recover_marketInfo S with(nolock) ON B.sub_id=S.sub_id
    where s.areaid = #{areaId} and S.sub_check NOT IN (3,4,8,9) and isnull(b.isdel,0)!=1
    </select>

    <select id="countRepairSearch" resultType="java.lang.Integer">
        SELECT
            COUNT(1) AS amount
        FROM
            shouhou h with(nolock)
LEFT JOIN
    (
        SELECT
            tuihuan_kind,
            shouhou_id,
            check3
        FROM
            shouhou_tuihuan with(nolock)
        WHERE
            ISNULL( isdel, 0 ) = 0
            AND tuihuan_kind IN (
                1, 2, 3, 4, 5
            )
    ) t
        ON t.shouhou_id= h.id
            LEFT JOIN
            (
            SELECT
            DISTINCT shouhou_id
            FROM
            shouhou_huishou with(nolock)
            ) AS hs
            ON hs.shouhou_id= h.id
            LEFT JOIN
            BBSXP_Users u with(nolock)
        ON u.id= h.userid
        WHERE
            1 = 1
          and (
            h.areaid in (
            #{areaId}
            )
           or h.toareaid in (
            #{areaId}
            )
            )
          AND isnull( h.isquji, 0 ) = 0
          and h.xianshi=1;
    </select>

    <select id="countSmallRefundHuanhuoOld" resultType="java.lang.Integer">
        select COUNT(1) AS amount from shouhou_fanchang sf with(nolock)
        left join productinfo p with (nolock)
        on sf.ppid = p.ppriceid
        left join smallpro s with (nolock)
        on sf.smallproid = s.id
        left join wuliu w with (nolock)
        on s.wuliuid = w.id
        left join areainfo a with (nolock)
        on isnull(s.toareaid,s.areaid)=a.id
        where
          s.kind in (2, 3, 4)
          and isnull( s.isdel, 0 ) = 0
          and isnull(sf.rstats, 0) in (0, 1 )
          AND ((s.istoarea= 1AND s.toareaid IN ( #{areaId}))
          OR (s.istoarea= 0 AND ISNULL( s.toareaid, s.areaid ) IN (#{areaId})))
          and s.wuliuid is null;
    </select>

    <select id="countAccessorsPurchase" resultType="java.lang.Integer">
        select COUNT(1) AS amount from caigou_sub cs with(nolock)
        WHERE cs.areaid = #{areaId} and  cs.stats in (0,1,2) and cs.kinds = 'pj'
    </select>

    <select id="countRepairAccessorsPurchase" resultType="java.lang.Integer">
        select COUNT(1) AS amount from caigou_sub cs with(nolock)
        WHERE cs.areaid = #{areaId} and  cs.stats in (0,1,2) and cs.kinds = 'wx'
    </select>

    <select id="countAccessorsLose" resultType="java.lang.Integer">
        select COUNT(1) AS amount from return_sub rs with(nolock)
        WHERE rs.areaid = #{areaId} and  rs.states in (1,2) and rs.type_ = '2'
    </select>

    <select id="countRepairAccessorsLose" resultType="java.lang.Integer">
        select COUNT(1) AS amount from return_sub rs with(nolock)
        WHERE rs.areaid = #{areaId} and  rs.states in (1,2) and rs.type_ = '6'
    </select>

    <select id="countAccessorsReturn" resultType="java.lang.Integer">
        select COUNT(1) AS amount from return_sub rs with(nolock)
        WHERE rs.areaid = #{areaId} and  rs.states in (1,2) and rs.type_ = '1'
    </select>

    <select id="countRepairAccessorsReturn" resultType="java.lang.Integer">
        select COUNT(1) AS amount from return_sub rs with(nolock)
        WHERE rs.areaid = #{areaId} and  rs.states in (1,2) and rs.type_ = '5'
    </select>

    <select id="countSmallRefund" resultType="java.lang.Integer">
        select COUNT(1) AS amount from smallpro sp with(nolock)
        WHERE isnull(sp.toareaid,sp.areaid) = #{areaId} and sp.Stats in(0,3,5,6)
          and isnull(sp.isdel,0) = 0
    </select>

    <select id="countSmallRefundV2" resultType="java.lang.Integer">
        select COUNT(1) AS amount
        from smallpro sp with(nolock)
        left join shouhou_fanchang sf
        with (nolock)
        on sf.smallproid = sp.id
        WHERE ISNULL( sp.toareaid, sp.areaid )= #{areaId} and rstats = 0
    </select>

    <select id="countYuyueSearch" resultType="java.lang.Integer">
        select COUNT(1) AS amount from shouhou_yuyue sy with(nolock)
        where sy.stats not in (3,5,6,7) and isnull(sy.isdel,0)=0
        and sy.areaid = #{areaId}
    </select>

    <select id="countDaiyongji" resultType="java.lang.Integer">
        select COUNT(1) AS amount FROM (
                                           select COUNT(1) AS amount, d.areaid  from daiyongji d with(nolock)
                                           where d.stats not in (4)
                                             and d.isdel = 0
                                             and d.areaid = #{areaId}
                                           GROUP by d.areaid
                                           UNION ALL
                                           select COUNT(1) AS amount, dt.areaid   from daiyongji_toarea dt with(nolock) where dt.areaid = #{areaId} AND stats != 3
                                           GROUP by dt.areaid
                                           UNION ALL
                                           select COUNT(1) AS amount, dt.toareaid   from daiyongji_toarea dt with(nolock) where dt.toareaid = #{areaId} AND stats != 3
                                           GROUP by dt.toareaid
                                       ) as temp
        GROUP by temp.areaid
    </select>

    <select id="countSmallXcKc" resultType="java.lang.Integer">
        select sum(aa.counts) AS amount from (
        select  d.curAreaId areaid,sum(d.count_) counts,sum(d.count_*p.costprice) prices,sum(case when d.stats_=1 and isnull(d.isOnSell,0)=1 then d.count_ else 0 end ) as sellCount from displayProductInfo d with(nolock)
        left join dbo.productinfo p with(nolock) on d.ppriceid=p.ppriceid where 1=1 and p.ismobile1=0 and d.stats_!=2 and d.stats_= 1  and  isnull(d.isFlaw,0)= 1
        group by d.curAreaId
        union
        select  d.curAreaId areaid,sum(d.count_) counts,sum(d.count_*p.costprice) prices,sum(case when d.stats_=1 and isnull(d.isOnSell,0)=1 then d.count_ else 0 end ) as sellCount from displayProductInfo d with(nolock)
        join dbo.product_mkc k with(nolock) on k.id=d.mkc_id
        left join dbo.productinfo p with(nolock) on k.ppriceid=p.ppriceid where 1=1 and p.ismobile1=1 and d.stats_!=2 and d.stats_= 1   and  isnull(d.isFlaw,0)= 1
        group by d.curAreaId )  aa group by aa.areaid
        HAVING aa.areaid = #{areaId}
    </select>

    <select id="countDispalyManage" resultType="java.lang.Integer">
        select COUNT(1) AS amount
        from dp_fix_photo dfp
        where dfp.status = 0
        and exists(select 1 from dp_photo_product_relation dppr where dfp.id = dppr.fix_photo_id and dppr.status != 2)
        group by dfp.area_id
        HAVING dfp.area_id = #{areaId}
    </select>

</mapper>
