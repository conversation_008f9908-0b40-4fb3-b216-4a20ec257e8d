<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.api.mapper.DeviceMapper">
    <select id="queryDevice" resultType="com.jiuji.oa.api.vo.door.DeviceInfo">
        select d.ID as id,d.areaid as areaId,a.area,d.Ip as ip,d.MAC as mac,d.dataStatus from dbo.DeviceInfo  d with(nolock) left join dbo.areainfo a with(nolock) on a.id = d.areaid  where d.TypeID = 8 and isnull(d.isdel,0)=0
    </select>
    <select id="queryDoorAlarmConfig" resultType="com.jiuji.oa.api.vo.door.DoorAlarmConfig">
        select areaid as areaId,kind,volume,alarmTime,deviceId as id from dbo.doorAlarmConfig with(nolock) where isnull(isdel,0)=0
    </select>
    <select id="queryManagerArea" resultType="java.lang.Integer">
        select areaid from dbo.areaAlarm with(nolock)
    </select>
    <select id="queryBroadAccountInfo" resultType="com.jiuji.oa.api.vo.door.BroadAccountInfo">
        select d.ID as id,d.Adsl as account,d.AdslPwd as pwd,c.name as city from dbo.DeviceInfo d with(nolock)
        inner join dbo.areainfo a with(nolock) on d.areaid=a.id
            left join dbo.AreaList c with(nolock) on c.code=a.zid
        where d.TypeID=1 and d.broadType=1 and d.Adsl is not null
    </select>


</mapper>
