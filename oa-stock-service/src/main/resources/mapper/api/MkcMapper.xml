<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.api.mapper.MkcMapper">
    <select id="queryMkcTransTime" resultType="com.jiuji.oa.api.vo.mkc.MkcTransTimeVO">
        select m.mkc_id as mkcId,isnull(m.deliveryTime,getdate()) as sendTime,m.areaid as sendAreaId ,m.toareaid as receiveAreaId from dbo.mkc_toarea m with(nolock) left join dbo.product_mkc k with(nolock) on m.mkc_id=k.id
        where m.stats in (0,1,2) and m.toareaid = k.areaid and k.kc_check=10
        and m.mkc_id in
        <foreach collection="mkcIds" item="mkcId" open="(" separator="," close=")">
            #{mkcId}
        </foreach>
        order by m.mkc_id DESC
    </select>
    <select id="queryMkcTrans" resultType="com.jiuji.oa.api.vo.mkc.MkcTransVO">
        select k.id as mkcId,k.imei,k.areaId,a.zid, a.area,s.tradeDate1 as tradeDate, s.sub_date, s.sub_check,s.sub_id as orderId,k.insourceid2
        ,sa.cityid as receiveCityid, s.delivery
        from sub s with(nolock)
        left join SubAddress sa with(nolock) on s.sub_id =sa.sub_id
        left join basket b  with (nolock)
        on s.sub_id =b.sub_id
        left join product_mkc k with (nolock)
        on k.basket_id =b.basket_id
        left join areainfo a with (nolock)
        on a.id = k.areaId
        LEFT join productinfo p with(nolock) on k.ppriceid=p.ppriceid
        where k.id is not null
        and (
        (k.kc_check =3
        and isnull(s.yifuM,0) >0
        )or(
            s.sub_check in (2,6)
        )or(
            s.sub_check in (3)
            <choose>
                <when test="startTime != null and endTime != null">
                    and s.tradeDate1 between #{startTime} and #{endTime}
                </when>
                <otherwise>
                    and s.tradeDate1 >= dateadd( DAY,- 1, getdate() )
                </otherwise>
            </choose>
        )
        )
        and k.insourceid2 in (627,5758)
        and p.brandId = 1
        order by k.areaId,k.id DESC
    </select>
    <select id="queryMkcTransImei" resultType="java.lang.String">
        SELECT apsi.imei FROM  apple_purchase_store_inventory apsi
                                   left join product_mkc pm on apsi.mkc_id =pm.id
        WHERE isnull(apsi.is_del,0) =0 and apsi.channel_id in(1909) and pm.imei is not NULL
          AND pm.imeidate > DATEADD(HOUR, -#{hour}, GETDATE())
        order by apsi.imei DESC
    </select>
    <select id="getCaigouAreaIdByImei" resultType="com.jiuji.oa.nc.stock.vo.ShopAppleAreaInfoVO">
        SELECT TOP 1
             k.imei,
             isnull(s.purchase_area_id,0) as purchaseAreaId,
             isnull(k.origareaid,0) as origAreaId,
             isnull(k.areaid,0) AS areaId
        FROM
            dbo.product_mkc k WITH(NOLOCK)
        LEFT JOIN dbo.mkcCaiGouBasket b WITH(NOLOCK) ON
            b.mkc_id = k.id
            LEFT JOIN dbo.mkcCaiGouSub s WITH(NOLOCK) ON
            s.id = b.sub_id
        WHERE
            k.imei = #{imei}
        ORDER BY   k.id   DESC
    </select>
    <select id="isNationalSubsidy" resultType="com.jiuji.cloud.stock.vo.response.GuobuResultBo">
        select sub_id as orderId, count(1) as guobuFlag FROM subFlagRecord sr WITH(NOLOCK)
        where sr.flagType = 7 and sub_id in
        <foreach collection="subId" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP by sub_id
    </select>

</mapper>