<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.shouhou.mapper.DaiyongjiMapper">

    <update id="updateWuLiu">
        update wuliu set [stats] = 4 where id = #{req.wuliuid}
    </update>

    <delete id="delDaiyongjiToArea">
        delete from dbo.daiyongji_toarea where stats=1 and id = #{dyjToAreaId}
    </delete>

    <select id="daiyongjiPage" resultType="com.jiuji.oa.stock.shouhou.vo.res.DaiyongjiPageResVO">
        SELECT d.id,name,ISNULL(d.product_color,pro.product_color) as productColor,imei,d.ppriceid,jikuang,price,inprice, outprice as outprice,pos,beizhu,d.dtime,isnull(d.stats,0) stats
        ,pandian,pandiandate,pan_user,ISNULL(d.applyCount,0) as applyCount,d.applyDate,d.guihuanDate,
        ISNULL(d.isdel,0) as isdel,ISNULL(pandian,0) as ispandian,kc_check,d.leavel,ISNULL(dt.areaid,d.areaid) as areaid
        ,dt.toareaid,ISNULL(dt.stats,0) AS toareaStats,priceTime
        ,pro.product_id as productId,pro.brandId,d.grade
        ,d.wai_guan,d.test_result,d.is_continue_use,d.is_protective_shell,d.is_protective_film
        ,wxid,ISNULL(yjtab.total,0) AS yajin,paystate,y.areaCnt,d.outstock_way,d.instock_time,d.outstock_time
        FROM dbo.daiyongji d WITH(NOLOCK)
        LEFT JOIN
        (
        SELECT id,daiyongjiId,areaid,toareaid,stats, ROW_NUMBER() OVER(PARTITION BY daiyongjiId ORDER BY id DESC) rindex
        FROM dbo.daiyongji_toarea WITH(NOLOCK) WHERE stats != 3
        ) dt ON d.id = dt.daiyongjiId AND dt.rindex = 1
        LEFT JOIN
        (
        SELECT dyjid,wxid,yajin,total,paystate FROM
        (
        SELECT dyjid,wxid,yajin,total,paystate,ROW_NUMBER() OVER(PARTITION BY dyjid ORDER BY id DESC) rnum
        FROM daiyongji_yajin WITH(NOLOCK)
        ) AS yj
        WHERE yj.rnum = 1
        ) yjtab ON d.id = yjtab.dyjid
        LEFT JOIN areainfo a WITH(NOLOCK) ON d.areaid = a.id
        LEFT JOIN productinfo pro WITH(NOLOCK) on d.ppriceid = pro.ppriceid
        LEFT JOIN
        (
        SELECT y.dyjid,COUNT(DISTINCT s.id) areaCnt FROM dbo.daiyongji_yajin y WITH(NOLOCK)
        INNER JOIN dbo.shouhou s WITH(NOLOCK) ON y.wxid = s.id
        INNER JOIN dbo.daiyongji d WITH(NOLOCK) ON d.id = y.dyjid
        WHERE d.areaid = s.areaid
        GROUP BY y.dyjid
        ) y ON y.dyjid = d.id
        <where>
            <if test="req.searchType!=null and req.searchValue!='' and req.searchValue!=null and req.searchType==1">
                and d.imei like '%' + #{req.searchValue} + '%'
            </if>
            <if test="req.searchType!=null and req.searchValue!='' and req.searchValue!=null and req.searchType==2">
                and d.id = #{req.searchValue}
            </if>
            <if test="req.searchType!=null and req.searchValue!='' and req.searchValue!=null and req.searchType==3">
                and d.name like '%' + #{req.searchValue} + '%'
            </if>
            <if test="req.searchType!=null and req.searchValue!='' and req.searchType==4">
                and yjtab.wxid = #{req.searchValue}
            </if>
            <if test="req.searchType!=null and req.searchValue!='' and req.searchType==5">
                and d.ppriceid = #{req.searchValue}
            </if>
            <if test="req.isContinueUse == true">
                and d.is_continue_use = 1
            </if>
            <if test="req.isContinueUse == false">
                and d.is_continue_use = 0
            </if>
            <if test="req.isProtectiveShell == true">
                and d.is_protective_shell = 1
            </if>
            <if test="req.isProtectiveShell == false">
                and d.is_protective_shell = 0
            </if>
            <if test="req.isProtectiveFilm == true">
                and d.is_protective_film = 1
            </if>
            <if test="req.isProtectiveFilm == false">
                and d.is_protective_film = 0
            </if>
            <if test="req.yajinkind!=null and req.yajinkind==1">
                and yjtab.paystate=2 and ISNULL(yjtab.total,0)>0
            </if>
            <if test="req.yajinkind!=null and req.yajinkind==2">
                and yjtab.paystate=2 and ISNULL(yjtab.total,0)=0
            </if>
            <if test="req.yajinkind!=null and req.yajinkind==3">
                and yjtab.paystate=3 and ISNULL(yjtab.total,0)>0
            </if>
            <choose>
                <when test="req.isdel!=null and req.yajinkind=='1'">
                    and isnull(d.isdel,0)=1
                </when>
                <otherwise>
                    and isnull(d.isdel,0)=0
                    <choose>
                        <when test="req.stats!=null and req.stats!=''">
                            and d.stats = #{req.stats}
                        </when>
                        <otherwise>
                            and d.stats in (1,2,3,6,8,9,10)
                        </otherwise>
                    </choose>
                </otherwise>
            </choose>
            <if test="req.price30day!=null and req.price30day=='1'">
                and (priceTime IS NULL OR DATEDIFF(DAY,priceTime,GETDATE())>=30)
            </if>
            <if test="req.panstate!=null and req.panstate!=''">
                and ISNULL(d.pandian,0) = #{req.panstate}
            </if>
            <if test="req.outstockWay!=null and req.outstockWay!=''">
                and d.outstock_way = #{req.outstockWay}
            </if>
            <if test="req.startTime != null and req.endTime != null  ">
                <if test="req.searchTimeType!=null and req.searchTimeType==3">
                and d.instock_time between #{req.startTime} and #{req.endTime}
                </if>
                <if test="req.searchTimeType!=null and req.searchTimeType==4">
                and d.outstock_time between #{req.startTime} and #{req.endTime}
                </if>
                <if test="req.searchTimeType!=null and req.searchTimeType==1">
                and d.applyDate between #{req.startTime} and #{req.endTime}
                </if>
                <if test="req.searchTimeType!=null and req.searchTimeType==2">
                and d.guihuanDate between #{req.startTime} and #{req.endTime}
                </if>
            </if>
            <choose>
                <when test="req.areaIds != null and req.areaIds.size() > 0">
                    and ISNULL(dt.areaid,d.areaid) in (
                    <foreach item="item" index="index" collection="req.areaIds" separator=",">
                        #{item}
                    </foreach>
                    )
                </when>
            </choose>
            <if test="req.statsList != null and req.statsList.size() > 0">
                and d.stats in (
                <foreach item="item" index="index" collection="req.statsList" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="req.gradeList != null and req.gradeList.size() > 0">
                and d.grade in (
                <foreach item="item" index="index" collection="req.gradeList" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="req.areaKind!=null and req.areaKind!=0">
                <choose>
                    <when test="req.areaKind==1">
                        and a.kind1=1
                    </when>
                    <otherwise>
                        and a.kind1!=1
                    </otherwise>
                </choose>
            </if>
        </where>
        order by d.id DESC
    </select>
    <select id="checkPiqianExist" resultType="java.lang.Integer">
        select 1 from T_ApplyInfo WITH(NOLOCK) where Id=#{piqianid} and IsDel=0 and CurrentStatus=4
    </select>
    <select id="checkPunishSubExist" resultType="java.lang.Integer">
        select 1 from punishSub WITH(NOLOCK) where sub_id=#{punishSubId}
    </select>
    <select id="searchRecoverMarketInfo" resultType="com.jiuji.oa.stock.shouhou.vo.res.RecoverMarketInfo">
        select b.basket_id                                           as id
             , s.sub_id                                              as subId
             , pro.product_name                                      as name
             , b.ppriceid
             , pro.product_color
             , b.price                                             as inprice
             , ''                                                       price
             , k.imei,k.id as mkcId
             , isnull(s.tradeDate1, isnull(s.tradeDate, s.sub_date)) as orderDate
        from dbo.recover_mkc k with(nolock)
        left join dbo.recover_marketSubInfo b with (nolock) on k.to_basket_id = b.basket_id
        left join dbo.recover_marketInfo s with (nolock) on b.sub_id = s.sub_id
        LEFT JOIN productinfo pro WITH (NOLOCK) on b.ppriceid = pro.ppriceid
        where s.sub_id = #{req.recoverMarketInfoId} AND ISNULL(s.saleType, 0)=1
    </select>
    <select id="daiyongjiKcPage" resultType="com.jiuji.oa.stock.shouhou.vo.res.DaiyongjiKcPageResVO">
        SELECT d2.name as bigRegion,di.name as smallRegion, a.area as areaName,a.id as areaId,ISNULL(dyj.dyjcount,0) AS dyjUseCount, sho.monthlyAcceptanceCount, ISNULL(t.dyCnt,0)   AS  monthlyDaiyongCount,
        freeCount + daiyongCount AS dyjTotalCount,
        ISNULL(dyj.dyCnt,0) AS dyjMonthUseCount,e.yajin,e.dyjIdString,
        e.freeCount,e.daiyongCount,e.repairingCount,e.waitingScrapCount
        FROM ( SELECT areaid,STRING_AGG(d.id, ',') AS dyjIdString
        ,SUM(CASE WHEN ISNULL(d.stats,0)=1 THEN 1 ELSE 0 END) AS freeCount
        ,SUM(CASE WHEN ISNULL(d.stats,0)=2 THEN 1 ELSE 0 END) AS daiyongCount
        ,SUM(ISNULL(yj.total,0)) AS yajin
        ,SUM(CASE WHEN ISNULL(d.stats,0)=3 THEN 1 ELSE 0 END) AS repairingCount
        ,SUM(CASE WHEN ISNULL(d.stats,0)=6 THEN 1 ELSE 0 END) AS waitingScrapCount
        FROM dbo.daiyongji d WITH(NOLOCK)
        <if test="req.getBrandIds() != null and req.getBrandIds().size()>0">
            INNER JOIN dbo.productinfo p WITH(NOLOCK) ON d.ppriceid=p.ppriceid
        </if>
        LEFT JOIN dbo.daiyongji_yajin yj WITH(NOLOCK) ON yj.dyjid=d.id AND ISNULL(d.stats,0)=2 AND ISNULL(yj.cancel,0)=0
        AND ISNULL(yj.yajin,0)>0
        WHERE ISNULL(d.isdel,0)=0
        <if test="req.getBrandIds() != null and req.getBrandIds().size()>0">
            AND p.brandID IN(
            <foreach item="item" index="index" collection="req.brandIds" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="req.name != null and req.name!='' ">
            AND d.name = #{req.name}
        </if>
        AND d.stats IN(1,2,3,6,7)
        <if test="req.areaidList != null and req.areaidList.size() > 0">
            and d.areaid in (
            <foreach item="item" index="index" collection="req.areaidList" separator=",">
                #{item}
            </foreach>
            )
        </if>
        GROUP BY areaid ) AS e
        INNER JOIN dbo.areainfo a WITH(NOLOCK) ON e.areaid=a.id
        LEFT JOIN(
        SELECT sh.areaid, COUNT(1) dyjcount,SUM(CASE WHEN DATEDIFF(MONTH,y.applydate,GETDATE()) = 0 THEN 1 ELSE 0 END)
        dyCnt
        FROM dbo.daiyongji_yajin y WITH(NOLOCK) INNER JOIN dbo.shouhou sh WITH(NOLOCK) ON sh.id=y.wxid
        <where>
            <if test="req.startTime != null and req.endTime != null">
                AND y.applydate IS NOT NULL AND y.applydate BETWEEN #{req.startTime} and #{req.endTime}
            </if>
        </where>
        GROUP BY sh.areaid
        ) dyj ON dyj.areaid = a.id
        LEFT JOIN dbo.departInfo di WITH(NOLOCK) ON di.id = dbo.getDepartTypeId(a.depart_id,4)
        LEFT JOIN dbo.departInfo d2 WITH(NOLOCK) ON d2.id = dbo.getDepartTypeId(a.depart_id,3)
        LEFT JOIN
        (
        SELECT s.areaid,COUNT(1) cnt FROM dbo.shouhou s WITH(NOLOCK)
        WHERE DATEDIFF(MONTH,s.modidate,GETDATE()) = 0 and s.xianshi = 1 AND s.issoft = 0 AND s.userid != 76783
        AND NOT EXISTS(SELECT 1 FROM dbo.shouhou_tuihuan t WITH(NOLOCK) WHERE t.shouhou_id = s.id AND t.tuihuan_kind IN
        (1,3,4))
        GROUP BY s.areaid
        ) sh ON sh.areaid = a.id
        LEFT JOIN (SELECT s.areaid,COUNT(*) monthlyAcceptanceCount FROM dbo.shouhou s WITH (NOLOCK)
        WHERE ISNULL(xianshi, 0) = 1
        and ISNULL(s.issoft, 0) = 0
        and s.modidate between #{req.monthStartTime} and #{req.monthEndTime}
        and s.userid != 76783  GROUP BY s.areaid
        ) sho ON sho.areaid = e.areaid
        LEFT JOIN  (
        SELECT sh.areaid,
        COUNT(1) dyjcount,
        SUM(CASE
        WHEN DATEDIFF(MONTH,
        y.applydate,
        GETDATE()) = 0 THEN 1
        ELSE 0
        END) dyCnt
        FROM dbo.daiyongji_yajin y WITH (NOLOCK)
        INNER JOIN
        dbo.shouhou sh WITH (NOLOCK)
        ON sh.id = y.wxid
        where y.applydate between  #{req.monthStartTime} and #{req.monthEndTime}
        GROUP BY sh.areaid ) t on t.areaid=e.areaid
       <where>
            <if test="req.areaKind!=null and req.areaKind!=0">
                <choose>
                    <when test="req.areaKind==1">
                        and a.kind1=1
                    </when>
                    <otherwise>
                        and a.kind1!=1
                    </otherwise>
                </choose>
            </if>
        </where>
    </select>

    <select id="daiyongjiToAreaPage" resultType="com.jiuji.oa.stock.shouhou.vo.res.DaiyongjiToAreaPageResVO">
        select a.id,a.daiyongjiId,pro.product_color as productColor ,area.area AS areaName,toarea.area AS toareaName,d.ppriceid,a.dtime,a.sendtime,a.senduser,a.recivedtime,a.receiveruser
             ,a.stats,a.wuliuid,a.shibian,a.areaid,a.toareaid, d.name,d.imei,d.kc_check,isnull(a.stats,0) as toareaStats
        FROM dbo.daiyongji_toarea a with(nolock) left join dbo.daiyongji d with(nolock) on a.daiyongjiId = d.id
            LEFT JOIN dbo.areainfo area with(nolock) ON area.id = a.areaid
            LEFT JOIN dbo.areainfo toarea with(nolock) ON toarea.id = a.toareaid
            LEFT JOIN productinfo pro WITH(NOLOCK) on d.ppriceid = pro.ppriceid
        <where>
            <if test="req.areaidList != null and req.areaidList.size() > 0">
                and a.areaid in (
                <foreach item="item" index="index" collection="req.areaidList" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="req.toareaidList != null and req.toareaidList.size() > 0">
                and a.toareaid in (
                <foreach item="item" index="index" collection="req.toareaidList" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="req.startTime != null and req.endTime != null  ">
                <choose>
                    <when test="req.searchTimeType!=null and req.searchTimeType==1">
                        and a.dtime  between #{req.startTime} and #{req.endTime}
                    </when>
                    <when test="req.searchTimeType!=null and req.searchTimeType==2">
                        and a.sendtime between #{req.startTime} and #{req.endTime}
                    </when>
                    <otherwise>
                        and a.recivedtime between #{req.startTime} and #{req.endTime}
                    </otherwise>
                </choose>
            </if>
            <choose>
                <when test="req.stats!=null and req.stats!=''and req.stats!=0">
                    and isnull(a.stats,0)= #{req.stats}
                </when>
                <otherwise>
                    and a.stats in(1,2,3)
                </otherwise>
            </choose>
            <if test="req.searchType!=null and req.searchValue!='' and req.searchType==1">
                and d.id = #{req.searchValue}
            </if>
            <if test="req.searchType!=null and req.searchValue!='' and req.searchType==2">
                and d.name like '%' + #{req.searchValue} + '%'
            </if>
        </where>
        order by a.id desc
    </select>

    <select id="daiyonjidiaobo" resultType="com.jiuji.oa.stock.shouhou.vo.res.DaiyonjiDiaoboInfo">
        SELECT d.id,d.name,d.areaid,a.area FROM dbo.daiyongji d with(nolock)
        LEFT JOIN daiyongji_toarea t with(nolock) ON d.id=t.daiyongjiId  AND t.stats!=3
        LEFT JOIN dbo.areainfo a with(nolock) ON a.id=d.areaid
        WHERE t.id IS NULL AND ISNULL(d.stats,0)=1
        <if test="req.dyjIdList != null and req.dyjIdList.size() > 0">
            AND d.id IN (
            <foreach item="item" index="index" collection="req.dyjIdList" separator=",">
                #{item}
            </foreach>
            )
        </if>
    </select>

    <select id="daiyongjiWuliu" resultType="com.jiuji.oa.stock.shouhou.vo.res.DaiyonjiWuliuInfo">
        select a.id,a.daiyongjiId,a.areaid,a.toareaid,d.imei from dbo.daiyongji_toarea a with(nolock)
        left join dbo.daiyongji d with(nolock) on a.daiyongjiId=d.id
        <where>
            <if test="daiyonjiAreads != null and daiyonjiAreads.size() > 0">
                AND a.id IN (
                <foreach item="item" index="index" collection="daiyonjiAreads" separator=",">
                    #{item}
                </foreach>
                )
            </if>
        </where>
    </select>

    <select id="searchRecoverMkcMarketInfo" resultType="com.jiuji.oa.stock.shouhou.vo.res.RecoverMkcMarketInfoResVO">
        select DISTINCT b.product_name as lable ,b.product_name as value from recover_mkc a WITH(NOLOCK)
        join productinfo b WITH(NOLOCK) on a.ppriceid = b.ppriceid
        <where>
            <if test="req.name != null and req.name!='' ">
                and b.product_name like '%' + #{req.name} + '%'
            </if>
        </where>
    </select>

    <select id="searchLastWxId" resultType="String">
        SELECT top 1 y.wxid
        FROM dbo.daiyongji_yajin y WITH(NOLOCK) left JOIN dbo.shouhou sh
        WITH (NOLOCK)
        ON sh.id=y.wxid
        where y.dyjid =  #{dyjid}
        order by y.applydate desc
    </select>

    <select id="checkRecoverMarketInfoExist" resultType="java.lang.Integer">
        select 1
        from recover_marketInfo WITH(NOLOCK)
        where sub_id=#{recoverMarketInfoId} and sub_check in (3)
    </select>


</mapper>
