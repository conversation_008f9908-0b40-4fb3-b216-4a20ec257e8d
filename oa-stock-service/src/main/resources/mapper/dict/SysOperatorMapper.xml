<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.nc.dict.mapper.SysOperatorMapper">
    <select id="getValueByCode" resultType="java.lang.String">
        select value from sysConfig with(nolock) where code = #{code};
    </select>
    <select id="getValueAndNameByCode" resultType="java.lang.String">
        select CONCAT(value,'(',name,')') as info from sysConfig with(nolock) where code = #{code};
    </select>

</mapper>