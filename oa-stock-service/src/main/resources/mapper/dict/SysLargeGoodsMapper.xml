<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.nc.dict.mapper.SysLargeGoodsMapper">
    <select id="getAreaById" resultType="java.lang.String">
        select CONCAT(area,'(',area_name,')') as info from
                    areainfo with(nolock) where id in (
        <foreach item="item" index="index" collection="idList" separator=",">
            #{item}
        </foreach>
        )
    </select>
    <select id="getAreaCodeById" resultType="java.lang.String">
        select area from areainfo with(nolock) where id in (
        <foreach item="item" index="index" collection="idList" separator=",">
            #{item}
        </foreach>
        )
    </select>
</mapper>
