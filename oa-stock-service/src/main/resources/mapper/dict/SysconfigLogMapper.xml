<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.nc.dict.mapper.SysconfigLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.nc.dict.entity.SysconfigLog">
        <id column="id" property="id" />
        <result column="config_id" property="configId" />
        <result column="code" property="code" />
        <result column="comment" property="comment" />
        <result column="in_user_id" property="inUserId" />
        <result column="in_user_name" property="inUserName" />
        <result column="operation_type" property="operationType" />
        <result column="is_del" property="del" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, config_id, code, comment, in_user_id, in_user_name, operation_type, is_del, create_time, update_time
    </sql>
    <insert id="sqlServerBatchInsert" useGeneratedKeys="false">
        insert into sysconfig_log ([config_id], [code], [comment], [in_user_id], [in_user_name], [operation_type], [is_del], [create_time], [update_time])
        values
        <foreach collection="list" item="item" index="index" open="(" separator="),(" close=")">
        #{item.configId},#{item.code},#{item.comment},#{item.inUserId},#{item.inUserName},#{item.operationType},#{item.del},#{item.createTime},#{item.updateTime}
        </foreach>
    </insert>

</mapper>
