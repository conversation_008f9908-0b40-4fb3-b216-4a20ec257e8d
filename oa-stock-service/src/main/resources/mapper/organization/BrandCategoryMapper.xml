<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.stock.organization.mapper.BrandCategoryMapper">

  <resultMap id="brandcategoryMap" type="com.jiuji.oa.stock.organization.entity.BrandCategory">
    <id property="id" column="id1"/>
    <result property="categoryid" column="categoryID"/>
    <result property="brandid" column="brandID"/>
    <result property="rank" column="Rank"/>
  </resultMap>
  <select id="getBrandIdsByCid" resultType="java.lang.Long">
    select distinct brandID
    from brandCategory with(nolock)
    <where>
      categoryID in
      <foreach collection="ids" index="index" close=")" open="(" separator="," item="id">
        #{id}
      </foreach>
    </where>
  </select>
</mapper>
