<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.authorizationtransfer.mapper.CrossAuthTransferSubMapper">

    <sql id="selectCommon">
        select distinct
        s.id,
        s.from_area_id,
        s.from_auth_id,
        s.to_area_id,
        s.to_auth_id,
        s.title,
        s.total_cost,
        s.total_transfer_price,
        s.status,
        s.create_time,
        s.create_user,
        s.check_user,
        s.check_time,
        s.handle_time,
        <if test="req.exportType != null and req.exportType == 1">
            m.imei,
            p.ppriceid,
            p.product_name,
            p.product_color,
            d.count,
            d.detail_id,
            d.transfer_price,
            c.name as categoryName,
        </if>
        s.handle_user
        from dbo.cross_auth_transfer_sub s with (nolock)
        left join dbo.cross_auth_transfer_detail d with(nolock ) on d.transfer_id=s.id
        left join dbo.product_mkc m with (nolock) on m.id = d.mkc_id
        left join dbo.productinfo p with(nolock ) on d.ppid=p.ppriceid
        <if test="req.exportType != null and req.exportType == 1">
            left join  dbo.category c  with(nolock ) on c.id = p.cid
        </if>

        where s.is_del=0
        <if test="req.title != null and req.title != ''">
            and s.title like CONCAT('%',#{req.title},'%')
        </if>
<!--        <if test="req.isHeadquarters != null and req.isHeadquarters == false ">-->
<!--            and (s.from_auth_id = #{req.currentAuthorizeId} or s.to_auth_id=#{req.currentAuthorizeId})-->
<!--        </if>-->
        <if test="req.selectTimeType !=null ">
            <choose>
                <when test="req.selectTimeType == 0">
                    <if test="req.subTimeStart != null">
                        and s.create_time &gt; #{req.subTimeStart}
                    </if>
                    <if test="req.subTimeEnd != null">
                        and s.create_time &lt; #{req.subTimeEnd}
                    </if>
                </when>
                <when test="req.selectTimeType == 1">
                    <if test="req.subTimeStart != null ">
                        and s.check_time &gt; #{req.subTimeStart}
                    </if>
                    <if test="req.subTimeEnd != null">
                        and s.check_time &lt; #{req.subTimeEnd}
                    </if>
                </when>
                <when test="req.selectTimeType == 2">
                    <if test="req.subTimeStart != null ">
                        and s.handle_time &gt; #{req.subTimeStart}
                    </if>
                    <if test="req.subTimeEnd != null ">
                        and s.handle_time &lt; #{req.subTimeEnd}
                    </if>
                </when>
            </choose>
        </if>
        <if test="req.statusList != null and req.statusList.size != 0">
            and s.status in
            <foreach collection="req.statusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.queryType !=null and req.key !=null and req.key !=''">
            <choose>
                <when test="req.queryType == 0 ">
                    and  p.product_name like CONCAT('%',#{req.key},'%')
                </when>
                <when test="req.queryType == 1 ">
                    and p.product_id = #{req.key}
                </when>
                <when test="req.queryType == 2 ">
                    and p.ppriceid = #{req.key}
                </when>
                <when test="req.queryType == 3 ">
                    and m.imei = #{req.key}
                </when>
                <when test="req.queryType == 4 ">
                    and (s.create_staff_id = #{req.key} or exists(select 1 from dbo.ch999_user u with(nolock ) where u.ch999_name = s.create_user and u.ch999_id = #{req.key}))
                </when>
                <when test="req.queryType == 5 ">
                    and s.check_staff_id = #{req.key}
                </when>
                <when test="req.queryType == 6 ">
                    and s.handle_staff_id = #{req.key}
                </when>
                <when test="req.queryType == 7 ">
                    and s.id = #{req.key}
                </when>
                <when test="req.queryType == 8 ">

                    <if test="req.categoryCharSeq != null and req.categoryCharSeq != '' ">
                        and p.cid in (select id from f_category_children (#{req.categoryCharSeq}))
                    </if>

                </when>
            </choose>
        </if>
    </sql>

    <select id="selectPageSubInfo" resultType="com.jiuji.oa.stock.authorizationtransfer.vo.PageSubInfoVO">
        select  * from (
        <if test="req.selectToArea == true">
            <include refid="selectCommon"/>
            <if test="req.toAreaId != null and req.toAreaId.size != 0  ">
                and s.to_area_id in
                <foreach collection="req.toAreaId" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.fromAreaId != null and req.fromAreaId.size != 0 and req.selectToFromArea == true ">
                and s.from_area_id in
                <foreach collection="req.fromAreaId" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="req.selectFromArea == true">
            <if test="req.selectToArea == true">
                UNION
            </if>
            <include refid="selectCommon"/>
            <if test="req.fromAreaId != null and req.fromAreaId.size != 0 ">
                and s.from_area_id in
                <foreach collection="req.fromAreaId" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </if>
        ) t  order by t.id desc
    </select>

    <sql id="selectKcInfo">
        select p.ppriceid as ppid,
               p.product_name,
               p.product_color,
               k.id        as mkcId,
               k.leftCount as kcCount,
               k.inprice  as costPrice,
               ''         as imei,
               0          as mouldFlag,
               0           as ismobile
        FROM dbo.productinfo p with (nolock)
         left join product_kc k with (nolock) on k.ppriceid = p.ppriceid
        where k.areaid = #{req.transferAreaId}
          and k.leftCount > 0
    </sql>
    <select id="selectProductKcList" resultType="com.jiuji.oa.stock.authorizationtransfer.vo.ProductSubmitInfo">
        <include refid="selectKcInfo"/>
        <if test="req.ppidList != null and req.ppidList.size != 0">
            and p.ppriceid in
            <foreach collection="req.ppidList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="selectProductSubmitInfo" resultType="com.jiuji.oa.stock.authorizationtransfer.vo.ProductSubmitInfo">
        select *
        from (
        <include refid="selectKcInfo"></include>
        <if test="req.queryType !=null and req.queryType == 3">
            and 1 = 2
        </if>
        <if test="req.queryType !=null and req.queryType == 4">
            and 1 = 2
        </if>
        <if test="req.imeiList != null and req.imeiList.size != 0  ">
            and 1 = 2
        </if>
        <include refid="commonQuery"/>
        union
        select p.ppriceid as ppid,
               p.product_name,
               p.product_color,
               m.id as       mkcId,
               1    as       kcCount,
               m.staticPrice as costPrice,
               m.imei,
               m.mouldFlag ,
               1    as       ismobile
        FROM dbo.productinfo p with (nolock)
         left join product_mkc m with (nolock) on m.ppriceid = p.ppriceid
        where m.areaid = #{req.transferAreaId}
          and m.basket_id is NULL
          and m.kc_check = 3
          and not exists(select 1 from dbo.xc_mkc xc where xc.mkc_id=m.id )
        <if test="req.queryType !=null and req.queryType == 3">
            and m.imei = #{req.key}
        </if>
        <if test="req.queryType !=null and req.queryType == 4">
            and m.id = #{req.key}
        </if>
        <if test="req.imeiList != null and req.imeiList.size != 0  ">
            and m.imei in
            <foreach collection="req.imeiList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <include refid="commonQuery"/>) t order by t.mkcId
    </select>
    <select id="selectCheckImeiInfo" resultType="com.jiuji.oa.stock.authorizationtransfer.vo.CheckImeiInfo">
        select m.imei,
               m.areaid,
               m.basket_id,
               m.kc_check,
               isnull(m.mouldFlag,0) as mouldFlag
        FROM dbo.productinfo p with (nolock)
         left join product_mkc m with (nolock) on m.ppriceid = p.ppriceid
        where
            not exists(select 1 from dbo.xc_mkc xc where xc.mkc_id=m.id )
        and m.kc_check =3
        <if test="req.imeiList != null and req.imeiList.size != 0  ">
            and m.imei in
            <foreach collection="req.imeiList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <sql id="commonQuery">
        <if test="req.queryType !=null and req.key !=null and req.key !=''">
            <choose>
                <when test="req.queryType == 0 ">
                    and  p.product_name like CONCAT('%',#{req.key},'%')
                </when>
                <when test="req.queryType == 1 ">
                    and p.product_id = #{req.key}
                </when>
                <when test="req.queryType == 2 ">
                    and p.ppriceid = #{req.key}
                </when>
            </choose>
        </if>
        <if test="req.cidList != null and req.cidList.size != 0">
            and p.cid in
            <foreach collection="req.cidList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <if test="req.brandList != null and req.brandList.size != 0">
            and p.brandID in
            <foreach collection="req.brandList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

    </sql>
</mapper>
