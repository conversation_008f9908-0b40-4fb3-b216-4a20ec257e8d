<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.authorizationtransfer.mapper.CrossAuthTransferDetailMapper">


    <select id="getCategoryName" resultType="com.jiuji.oa.stock.authorizationtransfer.vo.CrossAuthTransferDetailVO">
        select d.ppid,c.name as categoryName
        from cross_auth_transfer_detail  d with(nolock)
        left join dbo.productinfo p with(nolock ) on d.ppid = p.ppriceid
        left join category c  with(nolock ) on c.id = p.cid
        where d.is_del = 0 and d.transfer_id = #{transferId}
    </select>

</mapper>
