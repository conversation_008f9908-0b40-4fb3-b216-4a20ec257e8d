<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.nc.user.mapper.Ch999UserMapper">

    <select id="listAllUserByAreaIdListAndZhiWuList" resultType="com.jiuji.oa.nc.user.po.Ch999User">
        select *
        from (
        select u.ch999_id,
        u.ch999_name,
        u.mobile,
        u.area1id,
        ROW_NUMBER() over (partition by u.area1id order by z.leve,u.ch999_id) AS rn
        from dbo.ch999_user u with (nolock)
        left join dbo.zhiwu z
        with (nolock)
        on u.zhiwuid = z.id
        where u.iszaizhi = 1
        and isnull(u.islogin, 0) != 1
        and isnull(u.isshixi, 0) != 4
        and z.name in
        <foreach collection="zhiWuNameList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and u.area1id in
        <foreach collection="areaIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        ) AS a
        WHERE a.rn = 1
    </select>

    <select id="listAllUserByAreaIdAndZhiWuList" resultType="com.jiuji.oa.nc.user.po.Ch999User">
        select u.ch999_id,
        u.ch999_name,
        u.mobile
        from dbo.ch999_user u with (nolock)
        left join dbo.zhiwu z
        with (nolock)
        on u.zhiwuid = z.id
        where u.iszaizhi = 1
        and isnull(u.islogin, 0) != 1
        and isnull(u.isshixi, 0) != 4
        and z.name in
        <foreach collection="zhiWuNameList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and u.area1id = #{areaId} order by z.leve
    </select>

    <select id="selectAreaFirstCharge" resultType="com.jiuji.oa.nc.user.bo.Ch999UserInfoBO">
        SELECT
        TOP 1
        u.ch999_id AS ch999Id,
        u.ch999_name AS ch999Name,
        u.mobile AS mobile,
        ar.id AS areaId,
        ar.id AS area1Id,
        ar.area AS area
        FROM dbo.ch999_user u WITH(nolock)
        LEFT JOIN dbo.zhiwu z WITH(nolock) ON u.zhiwuid = z.id
        LEFT JOIN dbo.areainfo ar WITH(nolock) ON u.area1id = ar.id
        WHERE u.iszaizhi = 1
        AND isnull(u.islogin, 0) != 1
        AND isnull(u.isshixi, 0) != 4
        <!-- 店长职务id-27,副店长职务id-379 -->
        AND z.id IN (27, 379)
        AND ar.id = #{areaId}
        ORDER BY z.id
    </select>

    <select id="selectAreaUserByAreaId" resultType="com.jiuji.oa.nc.user.bo.Ch999UserInfoBO">
        SELECT
            u.ch999_id AS ch999Id,
            u.ch999_name AS ch999Name,
            u.mobile AS mobile,
            ar.id AS areaId,
            ar.id AS area1Id,
            ar.area AS area,
            u.Roles AS roles,
            z.id AS positionId
        FROM dbo.ch999_user u WITH(nolock)
        LEFT JOIN dbo.zhiwu z WITH(nolock) ON u.zhiwuid = z.id
            LEFT JOIN dbo.areainfo ar WITH(nolock) ON u.area1id = ar.id
        WHERE u.iszaizhi = 1
          AND isnull(u.islogin, 0) != 1
          AND isnull(u.isshixi, 0) != 4
          AND ar.id = #{areaId}
    </select>
    <select id="getUserInfoByCh999Ids" resultType="com.jiuji.oa.nc.user.bo.Ch999UserInfoBO">
        select
        u.ch999_id AS ch999Id,
        u.ch999_name AS ch999Name,
        u.mobile AS mobile,
        u.area1id AS area1Id,
        u.mainRole AS mainRole
        from dbo.ch999_user u with (nolock)
        where u.iszaizhi = 1
        and u.ch999_id in
        <foreach collection="ch999IdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="getUserInfoByMainRoles" resultType="com.jiuji.oa.nc.user.bo.Ch999UserInfoBO">
        SELECT
            u.ch999_id AS ch999Id,
            u.ch999_name AS ch999Name,
            u.mainRole
        FROM
            dbo.ch999_user u WITH(nolock)
        LEFT JOIN dbo.kaoqin k WITH(nolock) ON
            u.ch999_id = k.ch999_id
        WHERE u.iszaizhi = 1
        AND isnull(u.islogin,0) != 1
        AND isnull(u.isshixi,0) != 4
        AND datediff(day,k.dtime,getdate())=0
        AND k.kind1 = 1
        AND not exists (select 1 from dbo.kaoqin k1 with(nolock) where datediff(day,k.dtime,k1.dtime)=0 and k.ch999_id=k1.ch999_id and k1.kind1=2)
        AND u.mainRole IN
        <foreach collection="mainRoleList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="getUserInfoByMainRolesWithoutAttendance" resultType="com.jiuji.oa.nc.user.bo.Ch999UserInfoBO">
        SELECT
        u.areaid as areaId,
        u.area1id as area1Id,
        u.ch999_id AS ch999Id,
        u.ch999_name AS ch999Name,
        u.mainRole
        FROM
        dbo.ch999_user u WITH(nolock)
        WHERE u.iszaizhi = 1
        AND isnull(u.islogin,0) != 1
        AND isnull(u.isshixi,0) != 4
        AND u.mainRole IN
        <foreach collection="mainRoleList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="getUserByMobile" resultType="com.jiuji.oa.nc.user.po.Ch999User">
        select top 1 u.ch999_id,
        u.ch999_name,
        u.mobile
        from dbo.ch999_user u with (nolock)
        where u.mobile = #{mobile}
        and u.iszaizhi = 1
    </select>
    <select id="listUserByAreaId" resultType="com.jiuji.oa.nc.user.bo.AreaUserBo">
        SELECT
            ch999_id AS ch999UserId,
            ch999_name AS ch999UserName
        FROM ch999_user cu with(nolock)
        LEFT JOIN zhiwu z with(nolock) on cu.zhiwuid=z.id
        WHERE
          iszaizhi = 1
          AND isnull(isshixi,0) != 4
          AND islogin = 0
          AND area1id = #{areaId}
          <if test="zhiWuList != null and zhiWuList.size()>0">
              AND zhiwu IN
              <foreach collection="zhiWuList" item="item" open="(" close=")" separator=",">
                  #{item}
              </foreach>
          </if>
          <if test="kaoqi != null and kaoqi == 1">
              and exists (select 1
              from dbo.kaoqin k with (nolock)
              where k.ch999_id = cu.ch999_id
              and datediff(day, k.dtime, getdate()) = 0
              and k.kind1 = 1)
              and not exists (select 1
              from dbo.kaoqin k with (nolock)
              where k.ch999_id = cu.ch999_id
              and datediff(day, k.dtime, getdate()) = 0
              and k.kind1 = 2)
          </if>
        ORDER BY
            z.leve
    </select>
    <select id="listNowKind1ByCh999Name" resultType="java.lang.Integer">
        <!--当天只有kind1=1, 没有kind1=2,即为还在上班中-->
        SELECT  distinct k.kind1 from ch999_user cu with(nolock)
        left join kaoqin k with(nolock) on k.ch999_id = cu.ch999_id and k.dtime &gt;= CONVERT(DATE, GETDATE()) and k.dtime &lt; DATEADD(DAY, 1, CONVERT(DATE, GETDATE()))
        where cu.ch999_name = #{ch999Name}
    </select>
</mapper>
