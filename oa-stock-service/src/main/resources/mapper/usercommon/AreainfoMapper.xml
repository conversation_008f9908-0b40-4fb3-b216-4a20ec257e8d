<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.nc.user.mapper.AreainfoMapper">

    <select id="getAllChildren" resultType="java.lang.Integer">
        select *
        from f_category_children(#{pohone});
    </select>
    <select id="getAreaInfoByShopId" resultType="com.jiuji.oa.nc.user.po.Areainfo">
        select *
        from areainfo with(nolock)
        where id = #{shopId}
    </select>

    <select id="queryDistinctAreaList" resultType="com.jiuji.oa.nc.user.po.Areainfo">
        select distinct a.pid,al.name Province_name from areainfo a with(nolock)
        left join AreaList al with(nolock) on a.pid = al.code
        where a.ispass =1 and a.authorizeid = #{authorizeId} order by pid desc
    </select>

    <select id="getAreaInfoByAreaId" resultType="com.jiuji.oa.nc.user.po.Areainfo">
        select top 1 id,area
        from areainfo with(nolock)
        where area = #{area} and ispass = 1
    </select>
    <select id="getSkuIdByCidList" resultType="com.jiuji.oa.nc.user.bo.AreaSkuIdBo">
        select
        p.pLabel,
        pk.areaid as areaId,
        pk.inprice ,
        p.cid as category,
        p.ppriceid as skuId
        from dbo.productinfo p with(nolock) left join product_kc pk  with(nolock) on p.ppriceid = pk.ppriceid
        <where>
            pk .lcount > 0 AND p.isdel = 0
            and p.ismobile1=0
            <if test="cidList != null and cidList.size()>0">
                and p.cid in
                <foreach collection="cidList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="areaList != null and areaList.size()>0">
                and pk.areaid in
                <foreach collection="areaList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        order by p.cid,p.ppriceid
    </select>
    <select id="querySmallCategoryArea" resultType="com.jiuji.oa.nc.stock.vo.res.SelectSmallCategoryAreaDTO">
        select info.id         as areaId,
               info.area,
               info.area_name  as areaName,
               depart.name     as departName,
               depart.parentId as departParentId,
               depart.id       as departId,
              depart.departRank as departRank
        from departInfo depart with (nolock)
         right join areainfo info with (nolock) on info.depart_id = depart.id
        where info.ispass ='true' and info.id in (${sql})

    </select>
    <select id="getAreaById" resultType="java.lang.String">
        SELECT area from areainfo with(nolock) where id = #{areaId}
    </select>

    <select id="getCustomAreaNameById" resultType="java.lang.String">
        SELECT top 1 '-'+ISNULL(ISNULL(d.shotName,d.name),'')+'-'+a.area name
        FROM areainfo a WITH(NOLOCK) LEFT JOIN dbo.departInfo d
        WITH (NOLOCK)
        ON d.code= LEFT (a.areaCode,6)
        WHERE a.id= #{id}
    </select>

    <select id="getAreaInfoDistinct" resultType="com.jiuji.oa.nc.user.po.Areainfo">
        select distinct dcAreaID, authorizeid
        from dbo.areainfo with(nolock)
        where ispass = 1
    </select>
    <select id="getAreaName" resultType="com.jiuji.oa.nc.user.vo.AreaNameVO">
        SELECT a.id                             shopId,
               a.area + '(' + a.area_name + ')' shopName
        FROM areainfo a WITH (nolock)
        where a.id = #{areaId}
    </select>

    <select id="getAreaShotName" resultType="java.lang.String">
        select top 1 p.shotName area_M from dbo.areainfo a with(nolock) left join dbo.departInfo p with(nolock) on a.areaCode=p.code where a.id=#{areaId}
    </select>

    <select id="getAreaInfoById" resultType="com.jiuji.oa.nc.user.vo.AreaInfoSimpleVo">
        select
        id,
        <choose>
            <!--九机的前后端门店判断是通过area_region_divide字段，1：总部（后端），2：前端；-->
            <when test="xtenant &lt; 1000">
                <!--统一一下值的表示，1：前端，2：后端-->
                case when area_region_divide = 1 then 2
                when area_region_divide = 2 then 1
                else 0 end as areaType
            </when>
            <!--输出的前后端门店判断是通过AreaType字段，1：前端，2：后端-->
            <otherwise>
                isnull(AreaType,0) as areaType
            </otherwise>
        </choose>
        from areainfo with(nolock) where id = #{areaId}
    </select>
    <select id="queryCourierShop" resultType="com.jiuji.oa.nc.user.po.Areainfo">
        select id,area,courier
        from areainfo a with(nolock)
        where a.ispass = 1
        and a.kind1 in (1,2)
        and a.AreaType = 1
        and a.xtenant = 0
        and a.authorizeid not in (54,55,56,57,73,46)
    </select>
    <select id="queryRegion" resultType="com.jiuji.oa.nc.user.vo.AreaRegionVO">
        SELECT
            d2.name as bigRegion,di.name as smallRegion,a.id as areaId,a.area
        FROM
            dbo.areainfo a WITH(NOLOCK)
        LEFT JOIN dbo.departInfo di WITH(NOLOCK) ON di.id = dbo.getDepartTypeId(a.depart_id,4)
        LEFT JOIN dbo.departInfo d2 WITH(NOLOCK) ON d2.id = dbo.getDepartTypeId(a.depart_id,3)
        <where>
        <if test="areaIdList != null and areaIdList.size()>0">
            and a.id in
            <foreach collection="areaIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        </where>
    </select>

    <select id="queryRegionByName" resultType="com.jiuji.oa.nc.user.vo.AreaRegionVO">
        SELECT
            d2.name as bigRegion,di.name as smallRegion,a.id as areaId,a.area
        FROM
            dbo.areainfo a WITH(NOLOCK)
        LEFT JOIN dbo.departInfo di WITH(NOLOCK) ON di.id = dbo.getDepartTypeId(a.depart_id,4)
        LEFT JOIN dbo.departInfo d2 WITH(NOLOCK) ON d2.id = dbo.getDepartTypeId(a.depart_id,3)
        <where> a.ispass =1
            <if test="departNameList != null and departNameList.size()>0">
                and d2.name in
                <foreach collection="departNameList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="queryHQRegionByName" resultType="com.jiuji.oa.nc.user.vo.AreaRegionVO">
        SELECT
            d2.name as bigRegion,di.name as smallRegion,a.id as areaId,a.area
        FROM
            dbo.areainfo a WITH(NOLOCK)
        left join departinfo di with(nolock) on di.id = dbo.getDepartTypeId(a.depart_id,2)
            left join departinfo d2 with(nolock) on d2.id=dbo.getDepartTypeId(a.depart_id,1)
        <where> a.ispass =1
            <if test="departNameList != null and departNameList.size()>0">
                and d2.name in
                <foreach collection="departNameList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="queryHQRegion" resultType="com.jiuji.oa.nc.user.vo.AreaRegionVO">

        SELECT
            d2.name as bigRegion,di.name as smallRegion,a.id as areaId,a.area
        FROM
            dbo.areainfo a WITH(NOLOCK)
        left join departinfo di with(nolock) on di.id = dbo.getDepartTypeId(a.depart_id,2)
            left join departinfo d2 with(nolock) on d2.id=dbo.getDepartTypeId(a.depart_id,1)
            left join departinfo d3 with(nolock) on d3.id=dbo.getDepartTypeId(a.depart_id,3)
        where d2.name is not null or d3.id is null and a.ispass=1
    </select>
    <select id="selectFilterSku" resultType="com.jiuji.oa.nc.user.bo.AreaSkuIdBo">
        select
        p.pLabel,
        pk.areaid as areaId,
        pk.inprice ,
        p.cid as category,
        pk.lcount ,
        p.ppriceid as skuId
        from dbo.productinfo p with(nolock) left join product_kc pk  with(nolock) on p.ppriceid = pk.ppriceid
        <where> 1=1
            and pk.lcount > 0
            AND p.isdel = 0
            and p.ismobile1=0
            <if test="req.cidList != null and req.cidList.size()>0">
                and p.cid in
                <foreach collection="req.cidList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="req.areaList != null and req.areaList.size()>0">
                and pk.areaid in
                <foreach collection="req.areaList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="req.dynamicTouchSkuList != null and req.dynamicTouchSkuList.size()>0">
                and pk.ppriceid in
                <foreach collection="req.dynamicTouchSkuList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        order by p.cid,p.ppriceid

    </select>
    <select id="getAllSmallAndBigArea" resultType="com.jiuji.oa.nc.user.vo.SmallAndBigAreaInfoVO">
        select a.id areaId,a.area,d2.name smallArea,d1.name bigArea
        from areainfo a with (nolock)
        left join departInfo d1 with (nolock) on d1.id = dbo.getDepartTypeId(a.depart_id,3)
        left join departInfo d2 with (nolock) on d2.id = dbo.getDepartTypeId(a.depart_id,4)
        <where>
           <if test="ispass != null">
               and a.ispass = #{ispass}
           </if>
        </where>
    </select>
    <select id="getGuestbookAccount" resultType="com.jiuji.oa.nc.user.vo.GuestbookAccountVO">
        SELECT rb.show_area_code+'('+a.area_name+')' as show_area_code,rb.guestbook_account,rb.guestbook_password,rb.search_area_code from area_guestbook_account rb with(nolock)
		left join areainfo a with(nolock) on a.area = rb.show_area_code where rb.search_area_code = #{areaCode} and del_flag = 0
    </select>
    <select id="selectFilterSkuV2" resultType="com.jiuji.oa.nc.user.bo.AreaSkuIdBo">
        select
        p.pLabel,
        p.cid as category,
        p.ppriceid as skuId
        from dbo.productinfo p with(nolock)
        <where>
            AND p.isdel = 0
            and p.ismobile1=0
            <if test="req.cidList != null and req.cidList.size()>0">
                and p.cid in
                <foreach collection="req.cidList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="req.dynamicTouchSkuList != null and req.dynamicTouchSkuList.size()>0">
                and p.ppriceid in
                <foreach collection="req.dynamicTouchSkuList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        order by p.cid,p.ppriceid
    </select>
    <select id="selectXcFilterSku" resultType="com.jiuji.oa.nc.user.bo.AreaSkuIdBo">
        select d.count_ lcount, d.curAreaId as areaId,d.ppriceid as skuId
        from displayProductInfo d with(nolock)
        left join dbo.productinfo p
        with (nolock)
        on d.ppriceid=p.ppriceid
        where p.ismobile1=0
        and d.stats_!=2
        and d.stats_= 1
        and isnull(d.isFlaw
        , 0)= 1
        <if test="req.areaList != null and req.areaList.size()>0">
            and d.curAreaId in
            <foreach collection="req.areaList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="req.cidList != null and req.cidList.size()>0">
            and p.cid in
            <foreach collection="req.cidList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="selectDcFilterSku" resultType="com.jiuji.oa.nc.user.bo.AreaSkuIdBo">
        select 1 lcount, dppr.area_id as areaId,dppr.ppid as skuId
        from dp_photo_product_relation dppr
        <where>
        dppr.type = 1 and dppr.status = 1
        <if test="req.areaList != null and req.areaList.size()>0">
            and dppr.area_id in
            <foreach collection="req.areaList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="req.cidList != null and req.cidList.size()>0">
            and dppr.category_id in
            <foreach collection="req.cidList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        </where>
    </select>

    <update id="updateGba">
        update area_guestbook_account
        set guestbook_account  = #{account},
            guestbook_password = #{password}
        where show_area_code = #{area}
    </update>
</mapper>
