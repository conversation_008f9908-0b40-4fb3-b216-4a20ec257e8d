<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.nc.user.mapper.RoleInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.nc.user.po.RoleInfo">
        <id column="id" property="id"/>
        <result column="RoleName" property="RoleName"/>
        <result column="RoleType" property="RoleType"/>
        <result column="DepartCode" property="DepartCode"/>
        <result column="RoleRank" property="RoleRank"/>
        <result column="roleCate" property="roleCate"/>
        <result column="autoAuth" property="autoAuth"/>
        <result column="authType" property="authType"/>
        <result column="RoleDesc" property="RoleDesc"/>
        <result column="areaOrganizeId" property="areaOrganizeId"/>
    </resultMap>

    <select id="listAllJobs" resultType="com.jiuji.oa.orginfo.sysconfig.vo.SysConfigNameValueVO">
        select id as value, CASE WHEN zwType = 0 THEN '后端-' + name ELSE '前端-' + name end as name
        from zhiwu
        where kind = 'zw'
        order by zwType
    </select>
    <select id="listAllN" resultType="com.jiuji.oa.nc.user.po.RoleInfo">
        SELECT id, RoleName, RoleType, DepartCode, RoleRank, roleCate, autoAuth, authType, RoleDesc, areaOrganizeId FROM RoleInfo
    </select>
    <select id="getRoleIdsByUserIds" resultType="com.jiuji.cloud.org.vo.request.RolesSimpleVo">
        select distinct
               u.mainRole as id,
               r.RoleName as name
        from ch999_user u with(nolock)
        left join RoleInfo r with(nolock ) on U.mainRole = r.id
        where u.iszaizhi=1
        <if test="userIds != null and userIds.size > 0">
            and u.ch999_id in
            <foreach collection="userIds" index="index" item="userId" separator="," open="(" close=")">
                #{userId}
            </foreach>
        </if>
        <if test="roleName != null and roleName != ''">
            and r.RoleName like concat('%', #{roleName}, '%')
        </if>
    </select>
    <select id="getRoleByDepartIdOrRank" resultType="com.jiuji.tc.utils.enums.EnumVO">
        select id       as value,
        RoleName as label
        from RoleInfo with (nolock)
        <where>
            <if test="departId != null and departId != ''">
                ((charindex(#{departId}, ',' + DepartCode + ',') > 0 and RoleType = 2)
                or RoleType != 2
                <if test="rolesList != null and rolesList.size > 0">
                    or id in
                    <foreach collection="rolesList" index="index" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>)
            </if>
            <if test="haveRank != null and haveRank == false">
                and (id not in (314, 20, 231, 316, 228, 482)
                <if test="rolesList != null and rolesList.size > 0">
                    or id in
                    <foreach collection="rolesList" index="index" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>)
            </if>
        </where>
    </select>
    <select id="getRoleNameForResumeSheetVO" resultType="java.lang.String">
        select RoleName
        from RoleInfo
        where id = (select newRole
                    from ${officeName}.dbo.T_ApplyInfoEx
                    where ApplyId = #{applyId});
    </select>
</mapper>
