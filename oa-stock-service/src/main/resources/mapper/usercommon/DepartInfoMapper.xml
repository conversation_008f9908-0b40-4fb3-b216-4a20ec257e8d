<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.nc.user.mapper.DepartInfoMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.nc.user.po.DepartInfo">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="departType" property="departType"/>
        <result column="departRank" property="departRank"/>
        <result column="curAdmin" property="curAdmin"/>
        <result column="preAdmin" property="preAdmin"/>
        <result column="preAdminF" property="preAdminF"/>
        <result column="type" property="type"/>
        <result column="isdel" property="isdel"/>
        <result column="shotName" property="shotName"/>
        <result column="bianzhi" property="bianzhi"/>
        <result column="curSecondAdmin" property="curSecondAdmin"/>
        <result column="methodology" property="methodology"/>
        <result column="areaVision" property="areaVision"/>
        <result column="areaTask" property="areaTask"/>
        <result column="parentId" property="parentId"/>
        <result column="dataType" property="dataType"/>
        <result column="datalevel" property="dataLevel"/>
        <result column="departPath" property="departPath"/>
        <result column="dataLevel" property="dataLevel"/>
        <result column="organizationLevel" property="organizationLevel"/>
    </resultMap>


    <select id="listAll" resultMap="BaseResultMap">
        SELECT departInfo.id                                            as id,
               departInfo.name                                          as name,
               departInfo.departType                                    as departType,
               departInfo.departRank                                    as departRank,
               departInfo.curAdmin                                      as curAdmin,
               departInfo.preAdmin                                      as preAdmin,
               departInfo.preAdminF                                     as preAdminF,
               departInfo.type                                          as type,
               departInfo.isdel                                         as isdel,
               departInfo.shotName                                      as shotName,
               departInfo.bianzhi                                       as bianzhi,
               departInfo.curSecondAdmin                                as curSecondAdmin,
               departInfo.methodology                                   as methodology,
               departInfo.areaVision                                    as areaVision,
               departInfo.areaTask                                      as areaTask,
               IIF(departInfo.parentId is null, 0, departInfo.parentId) as parentId,
               departInfo.dataType                                      as dataType,
               departInfo.datalevel                                     as dataLevel,
               departInfo.departPath                                    as departPath
        FROM departInfo with (NOLOCK)
        WHERE ISNULL(isdel, 0) &lt;&gt; 1
        ORDER BY departRank
    </select>


</mapper>
