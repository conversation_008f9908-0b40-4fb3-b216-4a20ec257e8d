<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.nc.user.mapper.Ch999RanksMapper">

    <select id="getCh999IdsByRanks" resultType="java.lang.Integer">
        select distinct c.ch999_id
        from ch999Ranks cr with (nolock)
        cross apply f_split(cr.ranks, ',') f
        left join ch999_user c with (nolock) on cr.ch999_id = c.ch999_id and c.iszaizhi = 1
        where cr.areaid = c.area1id and c.ch999_id not in (2, 500)
        and split_value in
        <foreach collection="ranks" index="index" item="rank" separator="," open="(" close=")">
            #{rank}
        </foreach>
    </select>


    <select id="getCh999IdsByRanksAndBigRegion" resultType="java.lang.Integer">
        select distinct c.ch999_id
        from ch999Ranks cr with (nolock) cross apply f_split(cr.ranks, ',') f
        left join ch999_user c with (nolock) on cr.ch999_id = c.ch999_id and c.iszaizhi = 1
        where cr.areaid = c.area1id
        and c.ch999_id not in (2, 500)
        and split_value in
        <foreach collection="ranks" index="index" item="rank" separator="," open="(" close=")">
            #{rank}
        </foreach>
        and c.ch999_id  in (
        select u.ch999_id from dbo.ch999_user u with(nolock ) where  u.iszaizhi = 1
        and c.area1id in
        <foreach collection="areaIdList" index="index" item="rank" separator="," open="(" close=")">
            #{rank}
        </foreach>
        )

    </select>

</mapper>
