<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.wuliu.mapper.ShansongStoreConfigsMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.wuliu.entity.ShansongStoreConfigsEntity">
            <id property="id" column="Id" jdbcType="INTEGER"/>
            <result property="areaId" column="AreaId" jdbcType="INTEGER"/>
            <result property="area" column="Area" jdbcType="VARCHAR"/>
            <result property="storeName" column="StoreName" jdbcType="VARCHAR"/>
            <result property="storeId" column="StoreId" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id,AreaId,Area,
        StoreName,StoreId
    </sql>
    <select id="selectConfigList" resultType="com.jiuji.oa.wuliu.entity.ShansongStoreConfigsEntity">
        select Area,AreaId,StoreId,StoreName  from  ShansongStoreConfigs with(nolock) order by Id desc
    </select>
</mapper>
