<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.wuliu.mapper.RecoverMarketinfoMapper">

    <select id="queryWuliuBySubId" resultType="com.jiuji.oa.orderdynamics.vo.response.QueryWuliuBySubResVO">
        select top 1 w.id wuliuId,
               w.com,
               w.nu,
               s.sub_id subId
        from wuliu w with(nolock)
            join recover_marketInfo s with(nolock) on w.danhaobind = s.sub_id and w.wutype in (9)
        where w.stats in (1,2,3,4,6)
        and s.sub_id = #{subId}
        order by w.id desc
    </select>
    <select id="queryWuliuEntityBySubId" resultType="com.jiuji.oa.wuliu.entity.WuLiuEntity">
        select t2.*
        from (
                 select top 1 w.*
                 from wuliu w with(nolock)
                 join recover_marketInfo s with(nolock) on w.danhaobind = s.sub_id and w.wutype in (9)
                 where s.sub_id = #{subId}
                 and w.stats in (1,2,3,4,6)
                 order by w.id desc
             ) t2
        union
        SELECT w.*
        FROM recover_marketSubInfo b with(nolock)
            inner join recover_marketInfo s with(nolock) on s.sub_id = b.sub_id
            inner join recover_mkc k with(nolock) on k.to_basket_id = b.basket_id
            inner join recover_toarea t with(nolock) on t.mkc_id = k.id
            inner join wuliu w with(nolock) on t.wuliuid = w.id
        where s.sub_id = #{subId} and t.status != 3
    </select>

    <select id="getSubExpectTimeBySubId" resultType="com.jiuji.oa.wuliu.dto.SubExpectTimeDTO">
        select top 1 s.sub_id,
               s.sub_check as subCheck,
               isnull(delivery, 1) delivery,
               s.expectTime as expectTime,
               ss.userDate,
               ss.userTime
        from recover_marketInfo s WITH(NOLOCK)
        left join RecoverSubAddress ss WITH (NOLOCK) on ss.sub_id=s.sub_id
        where s.sub_id = #{subId}
    </select>


    <select id="getSubIdListByPpid" resultType="com.jiuji.oa.stock.develivery.vo.res.OrderOutStockPageRes">
        SELECT DISTINCT s.sub_id, '良品单' as subtype, '2' as subtypeId,
        case when s.islock = 1 then 1 else 0 end cancelFlag
        from recover_marketInfo s with(nolock)
        inner join recover_marketSubInfo b
        with (nolock)
        on b.sub_id = s.sub_id
        where s.sub_check = 1
          and delivery = 4
          and yingfuM = yifuM
          and ISNUll(b.isdel
            , 0) = 0
          and ppriceid = #{req.searchValue}
          and s.areaid = #{req.areaId}
    </select>
    <select id="getSubIdListByMkcId" resultType="com.jiuji.oa.stock.develivery.vo.res.OrderOutStockPageRes">
        SELECT DISTINCT s.sub_id, '良品单' as subtype, '2' as subtypeId,
        case when s.islock = 1 then 1 else 0 end cancelFlag
        from recover_marketInfo s with(nolock)
        inner join recover_marketSubInfo b
        with (nolock)
        on b.sub_id = s.sub_id
        inner join recover_mkc pm
        with (nolock)
        on pm.to_basket_id = b.basket_id
        where s.sub_check = 1 and delivery = 4 and yingfuM = yifuM and ISNUll(b.isdel,0) = 0 and pm.id = #{req.searchValue} and s.areaid =  #{req.areaId}
    </select>
    <select id="getSubIdListByImei" resultType="com.jiuji.oa.stock.develivery.vo.res.OrderOutStockPageRes">
        SELECT DISTINCT s.sub_id, '良品单' as subtype, '2' as subtypeId,
        case when s.islock = 1 then 1 else 0 end cancelFlag
        from recover_marketInfo s with(nolock)
        inner join recover_marketSubInfo b
        with (nolock)
        on b.sub_id = s.sub_id
        inner join recover_mkc pm
        with (nolock)
        on pm.to_basket_id = b.basket_id
        where s.sub_check = 1 and delivery = 4 and yingfuM = yifuM and ISNUll(b.isdel,0) = 0 and pm.imei = #{req.searchValue} and s.areaid =  #{req.areaId}
    </select>

    <select id="getSubIdListByBarcode" resultType="com.jiuji.oa.stock.develivery.vo.res.OrderOutStockPageRes">
        SELECT DISTINCT s.sub_id, '良品单' as subtype, '2' as subtypeId,
        case when s.islock = 1 then 1 else 0 end cancelFlag
        from recover_marketInfo s with(nolock)
        inner join recover_marketSubInfo b
        with (nolock)
        on b.sub_id = s.sub_id
        inner join productBarcode pb with (nolock)
        on pb.ppriceid = b.ppriceid
        where s.sub_check = 1
          and delivery = 4
          and yingfuM = yifuM
          and ISNUll(b.isdel
            , 0) = 0
          and pb.barCode = #{req.searchValue}
          and s.areaid = #{req.areaId}
    </select>


</mapper>
