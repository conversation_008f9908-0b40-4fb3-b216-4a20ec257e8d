<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~
  ~      Copyright © 2006 - 2020 九机网 All Rights Reserved
  ~
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.wuliu.mapper.WuLiuSubMapper">

    <select id="getSub" resultType="com.jiuji.oa.wuliu.entity.WuLiuSubEntity">
        SELECT top 1 * FROM dbo.sub s WITH(NOLOCK) WHERE s.sub_id = #{subId} AND s.sub_check IN (0,1,2,5,6,7)
    </select>
    <select id="getWuliuSubByWuliuIds" resultType="com.jiuji.oa.orderdynamics.dto.WuliuSubDTO">
        select b.sub_id subId,w.id wuliuId,w.com,w.nu,1 lineType,p.bpic productImage,1 subType
               ,b.ppriceid ppid
               ,b.basket_id basketId
        from mkc_toarea m with(nolock)
            join product_mkc k with(nolock) on k.id=m.mkc_id
            join basket b with(nolock) on b.basket_id=k.basket_id
            join productinfo p with(nolock) on b.ppriceid=p.ppriceid
            join wuliu w with(nolock) on w.id=m.wuliuid
        where w.id = #{wuliuId}
        and w.stats in (1,2,3,4,6)
        and abs(datediff(hour,m.dtime,b.basket_date)) <![CDATA[ < ]]> 6
        union
        select b.sub_id subId,w.id wuliuId,w.com,w.nu,1 lineType,p.bpic productImage,1 subType
              ,b.ppriceid ppid
              ,b.basket_id basketId
        from diaobo_sub s with(nolock)
            join diaobo_basket k with(nolock) on s.id=k.sub_id
            join basket b with(nolock) on b.basket_id=k.basket_id and k.basket_type = 0
            join productinfo p with(nolock) on b.ppriceid=p.ppriceid
            join wuliu w with(nolock) on w.id=s.wuliuid
        where w.id = #{wuliuId}
        and w.stats in (1,2,3,4,6)
        union
        select t1.subId,t1.wuliuId,t1.com,t1.nu,t1.lineType,t1.productImage,t1.subType
             ,0 ppid
             ,0 basketId
        from (
                select top 1 s.sub_id subId,w.id wuliuId,w.com,w.nu,2 lineType,'' productImage,1 subType
                from wuliu w with(nolock)
                join sub s with(nolock) on w.danhaobind = s.sub_id and w.wutype in (4,6)
                left join basket b with(nolock) on s.sub_id = b.sub_id
                where w.id = #{wuliuId}
                and isnull(b.[type],0) != 98
                and w.stats in (1,2,3,4,6)
                order by w.id desc
             ) t1
        union
        SELECT b.sub_id subId,w.id wuliuId,w.com,w.nu,1 lineType,'' productImage,3 subType
            ,b.ppriceid ppid
            ,b.basket_id basketId
        FROM recover_marketSubInfo b WITH(NOLOCK)
	    inner join recover_marketInfo s WITH(NOLOCK) on s.sub_id = b.sub_id
        inner join recover_mkc k WITH(NOLOCK) on k.to_basket_id = b.basket_id
        inner join recover_toarea t WITH(NOLOCK) on t.mkc_id = k.id
        inner join wuliu w with(nolock) on t.wuliuid = w.id
        where w.id = #{wuliuId}
        and w.stats in (1,2,3,4,6) and abs(datediff(hour,t.dtime,b.basket_date)) <![CDATA[ < ]]> 6
        union
        select t2.subId,t2.wuliuId,t2.com,t2.nu,t2.lineType,t2.productImage,t2.subType
             ,0 ppid
             ,0 basketId
        from (
                 select top 1 s.sub_id subId,
                        w.id wuliuId,
                        w.com,
                        w.nu,
                        2 lineType,
                        '' productImage,
                        3 subType
                 from wuliu w with(nolock)
                 join recover_marketInfo s WITH(NOLOCK) on w.danhaobind = s.sub_id and w.wutype in (9)
                 where w.id = #{wuliuId}
                   and w.stats in (1,2,3,4,6)
                 order by w.id desc
             ) t2
    </select>
    <select id="getDiySubByNu" resultType="com.jiuji.oa.orderdynamics.dto.DiySubDTO">
        select s.sub_id subId,sa.wuliuNo nu,sa.cityid cityId,sa.Address receiverAdress,2 lineType  from sub s with(nolock)
            join SubAddress sa with(nolock) on s.sub_id = sa.sub_id
            join basket b with(nolock) on s.sub_id = b.sub_id
        where b.[type] = 98
        and isnull(b.isdel,0) = 0
        and sa.wuliuNo = #{nu}
    </select>
    <select id="getWuliuSubByNu" resultType="com.jiuji.oa.orderdynamics.dto.WuliuSubDTO">
        select t1.subId,t1.wuliuId,t1.com,t1.nu,t1.subType
        from (
                 select top 1 s.sub_id subId,
                        w.id wuliuId,
                        w.com,
                        w.nu,
                        1 subType,
                        sub_check subCheck
                 from wuliu w with(nolock)
                 join sub s with(nolock) on w.danhaobind = s.sub_id and w.wutype in (4,6)
                 where w.nu = #{nu}
                 order by w.id desc
             ) t1
        union
        select t2.subId,t2.wuliuId,t2.com,t2.nu,t2.subType
        from (
                 select top 1 s.sub_id subId,
                        w.id wuliuId,
                        w.com,
                        w.nu,
                        3 subType,
                        sub_check subCheck
                 from wuliu w with(nolock)
                 join recover_marketInfo s WITH(NOLOCK) on w.danhaobind = s.sub_id and w.wutype in (9)
                 where w.nu = #{nu}
                 order by w.id desc
             ) t2
    </select>
    <select id="getTransferWuliuSubByNu" resultType="com.jiuji.oa.orderdynamics.dto.DiaoboWuliuSubDTO">
        select b.sub_id subId,
               w.id wuliuId,
               w.com,w.nu,
               1 subType,
               p.ppriceid ppid,
               p.product_name productName,
               p.product_color productColor,
               m.id diaoboSubId
        from mkc_toarea m with(nolock)
            join product_mkc k with(nolock) on k.id=m.mkc_id
            join basket b with(nolock) on b.basket_id=k.basket_id
            join productinfo p with(nolock) on b.ppriceid=p.ppriceid
            join wuliu w with(nolock) on w.id=m.wuliuid
        where w.nu = #{nu} and m.stats != 3
        union
        select b.sub_id subId,
               w.id wuliuId,
               w.com,w.nu,
               1 subType,
               p.ppriceid ppid,
               p.product_name productName,
               p.product_color productColor,
               s.id diaoboSubId
        from diaobo_sub s with(nolock)
            join diaobo_basket k with(nolock) on s.id=k.sub_id
            join basket b with(nolock) on b.basket_id=k.basket_id and k.basket_type = 0
            join productinfo p with(nolock) on b.ppriceid=p.ppriceid
            join wuliu w with(nolock) on w.id=s.wuliuid
        where w.nu = #{nu}
        union
        SELECT b.sub_id subId,
               w.id wuliuId,
               w.com,w.nu,
               3 subType,
               k.ppriceid ppid,
               '' productName,
               '' productColor,
               t.id diaoboSubId
        FROM recover_marketSubInfo b WITH(NOLOCK)
	    inner join recover_marketInfo s WITH(NOLOCK) on s.sub_id = b.sub_id
            inner join recover_mkc k WITH(NOLOCK) on k.to_basket_id = b.basket_id
            inner join recover_toarea t WITH(NOLOCK) on t.mkc_id = k.id
            inner join wuliu w with(nolock) on t.wuliuid = w.id
        where w.nu = #{nu} and t.status != 3
    </select>
    <select id="getTransferWuliuSubByWuliuId" resultType="com.jiuji.oa.orderdynamics.dto.DiaoboWuliuSubDTO">
        select b.sub_id subId,
               w.id wuliuId,
               w.com,w.nu,
               1 subType,
               p.ppriceid ppid,
               p.product_name productName,
               p.product_color productColor,
               m.id diaoboSubId
        from mkc_toarea m with(nolock)
            join product_mkc k with(nolock) on k.id=m.mkc_id
            join basket b with(nolock) on b.basket_id=k.basket_id
            join productinfo p with(nolock) on b.ppriceid=p.ppriceid
            join wuliu w with(nolock) on w.id=m.wuliuid
        where w.id = #{wuliuId} and m.stats != 3
        union
        select b.sub_id subId,
               w.id wuliuId,
               w.com,w.nu,
               1 subType,
               p.ppriceid ppid,
               p.product_name productName,
               p.product_color productColor,
               s.id diaoboSubId
        from diaobo_sub s with(nolock)
            join diaobo_basket k with(nolock) on s.id=k.sub_id
            join basket b with(nolock) on b.basket_id=k.basket_id and k.basket_type = 0
            join productinfo p with(nolock) on b.ppriceid=p.ppriceid
            join wuliu w with(nolock) on w.id=s.wuliuid
        where w.id = #{wuliuId}
        union
        SELECT b.sub_id subId,
               w.id wuliuId,
               w.com,w.nu,
               3 subType,
               k.ppriceid ppid,
               '' productName,
               '' productColor,
               t.id diaoboSubId
        FROM recover_marketSubInfo b WITH(NOLOCK)
	    inner join recover_marketInfo s WITH(NOLOCK) on s.sub_id = b.sub_id
            inner join recover_mkc k WITH(NOLOCK) on k.to_basket_id = b.basket_id
            inner join recover_toarea t WITH(NOLOCK) on t.mkc_id = k.id
            inner join wuliu w with(nolock) on t.wuliuid = w.id
        where w.id = #{wuliuId} and t.status != 3
    </select>
    <select id="queryWuliuBySubId" resultType="com.jiuji.oa.orderdynamics.vo.response.QueryWuliuBySubResVO">
        select top 1 w.id wuliuId,
               w.com,
               w.nu,
               s.sub_id subId
               from wuliu w with(nolock)
            join sub s with(nolock) on w.danhaobind = s.sub_id and w.wutype in (4,6)
        where w.stats in (1,2,3,4,6)
        and s.sub_id = #{subId}
        order by w.id desc
    </select>
    <select id="getWuliuEntityBySubId" resultType="com.jiuji.oa.wuliu.entity.WuLiuEntity">
        select w.* from diaobo_sub s with(nolock)
            join diaobo_basket k with(nolock) on s.id=k.sub_id
            join basket b with(nolock) on b.basket_id=k.basket_id and k.basket_type = 0
            join productinfo p with(nolock) on b.ppriceid=p.ppriceid
            join wuliu w with(nolock) on w.id=s.wuliuid
        where b.sub_id = #{subId}
        and w.stats in (1,2,3,4,6)
        union
        select w.*
        from mkc_toarea m with(nolock)
            join product_mkc k with(nolock) on k.id=m.mkc_id
            join basket b with(nolock) on b.basket_id=k.basket_id
            join productinfo p with(nolock) on b.ppriceid=p.ppriceid
            join wuliu w with(nolock) on w.id=m.wuliuid
        where b.sub_id = #{subId} and m.stats != 3
        union
        select t1.*
        from (
                 select top 1 w.* from wuliu w with(nolock)
                join sub s with(nolock) on w.danhaobind = s.sub_id and w.wutype in (4,6)
                 where s.sub_id = #{subId}
                   and w.stats in (1,2,3,4,6)
                 order by w.id desc
             ) t1
    </select>
    <select id="getSubExpectTimeBySubId" resultType="com.jiuji.oa.wuliu.dto.SubExpectTimeDTO">
        seLect top 1 s.sub_id as subId,
               s.sub_check as subCheck,
               s.delivery,
               s.expectTime,
               ss.userDate,
               ss.userTime
        from sub s with(nolock)
        left join SubAddress ss with (nolock) on ss.sub_id=s.sub_id
        where s.sub_id=#{subId}
    </select>
    <select id="getSubPositionBySub" resultType="com.jiuji.oa.wuliu.dto.SubPositionDTO">
        select
            top 1
            s.sub_id subId,
            s.userid userId,
            isnull(tpo.buyer_position,a.[position]) addressPosition,
            a.cityid cityId
        from sub s with(nolock)
        left join Addinfo a with(nolock) on s.userid = a.userid and a.Address = #{req.address}
        left join third_platform_order tpo with(nolock) on s.sub_id = tpo.sub_id and s.subtype = 19
        where s.sub_id = #{req.subId}
        order by a.id desc
    </select>
    <select id="getOrderByPayId" resultType="com.jiuji.oa.wuliu.vo.res.WuliuOrderInfoRes">
        select top 1 s.sub_id,
               ai.id aliPayId,
               ai.payType,
               s.sub_check,
               s.userid,
               STUFF((select ';' + convert(nvarchar(50), p.product_name)
                      FROM basket b with (nolock)
                   left join productinfo p with(nolock) on b.ppriceid = p.ppriceid
                   WHERE b.sub_id = s.sub_id and isnull(b.isdel,0) = 0
                   FOR XML Path('')), 1, 1, '') as productName,
               s.delivery,
               sa.wuliucompany com,
               sa.wuliuNo nu,
               s.sub_mobile,
               wet.express_receive_time
        from alipayInfo ai with (nolock)
        left join sub s with (nolock) on ai.orderNum = s.sub_id and ai.payType = 1
        left join SubAddress sa with (nolock) on sa.sub_id = s.sub_id
        left join wuliu_express_time wet with (nolock) on sa.wuliuNo = wet.wuliuNo
        where ai.id = #{payId}
    </select>
    <select id="getOrderByPayDes" resultType="com.jiuji.oa.wuliu.vo.res.WuliuOrderInfoRes">
        select top 1 s.sub_id,
        ai.id aliPayId,
        ai.payType,
        s.sub_check,
        s.userid,
        STUFF((select ';' + convert(nvarchar(50), p.product_name)
        FROM basket b with (nolock)
        left join productinfo p with(nolock) on b.ppriceid = p.ppriceid
        WHERE b.sub_id = s.sub_id and isnull(b.isdel,0) = 0
        FOR XML Path('')), 1, 1, '') as productName,
        s.delivery,
        sa.wuliucompany com,
        sa.wuliuNo nu,
        s.sub_mobile,
        wet.express_receive_time
        from alipayInfo ai with (nolock)
        left join sub s with (nolock) on ai.orderNum = s.sub_id and ai.payType = 1
        left join SubAddress sa with (nolock) on sa.sub_id = s.sub_id
        left join wuliu_express_time wet with (nolock) on sa.wuliuNo = wet.wuliuNo
        where ai.dsc = #{payDes} or ai.otherDsc = #{payDes}
    </select>
    <select id="getBySubId" resultType="com.jiuji.oa.wuliu.entity.WuLiuSubEntity">
        SELECT top 1 s.sub_id subId,
               s.sub_check subCheck,
               s.subtype subType
        FROM dbo.sub s WITH(NOLOCK)
        WHERE s.sub_id = #{subId}
    </select>
    <select id="getSubTelByNu" resultType="java.lang.String">
        select top 1 a.company_tel1
        from wuliu w with(nolock)
        left join areainfo a with(nolock) on a.id = w.sareaid
        where w.stats in (1,2,3,4,6,7)
        and w.nu = #{nu}
    </select>
    <select id="getSubAddressPositionBySubId" resultType="com.jiuji.oa.wuliu.dto.SubPositionDTO">
        SELECT top 1 s.sub_id subId,
        s.userid userId,
        sa.position addressPosition,
        sa.Address address,
        sa.cityid cityId from SubAddress sa with(nolock)
        inner join sub s with(nolock) on sa.sub_id = s.sub_id
        where s.sub_id = #{req.subId}
        ORDER by sa.sub_id DESC
    </select>
</mapper>
