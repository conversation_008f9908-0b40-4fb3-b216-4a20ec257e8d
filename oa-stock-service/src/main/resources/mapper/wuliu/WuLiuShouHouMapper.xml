<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~
  ~      Copyright © 2006 - 2020 九机网 All Rights Reserved
  ~
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.wuliu.mapper.WuLiuShouHouMapper">

    <select id="getShouHou" resultType="com.jiuji.oa.wuliu.entity.WuLiuShouHouEntity">
        SELECT top 1 *
        FROM dbo.shouhou s WITH(NOLOCK)
        WHERE s.id = #{subId}
          AND s.xianshi = #{xianShi}
    </select>

    <select id="getShouHou2" resultType="com.jiuji.oa.wuliu.entity.WuLiuShouHouEntity">
        SELECT top 1 *
        FROM dbo.shouhou s WITH(NOLOCK)
        WHERE s.id = #{subId}
          AND s.xianshi = #{xianShi}
          and (ISNULL(s.toareaid
            , s.areaid) = #{areaId}
           OR EXISTS (SELECT 1 FROM dbo.shouhou_toarea a WITH (NOLOCK) WHERE a.shouhou_id = s.id
          AND a.toareaid = #{areaId} ))
    </select>

    <select id="getShouHou3" resultType="com.jiuji.oa.wuliu.entity.WuLiuShouHouEntity">
        SELECT top 1 * FROM dbo.sub s WITH(NOLOCK) WHERE s.sub_id = #{subId} AND s.sub_check IN (0,1,2,5,6,7) and s.areaid = #{areaId}
    </select>

</mapper>
