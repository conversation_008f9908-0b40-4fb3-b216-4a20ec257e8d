<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.wuliu.mapper.WuliuExpressTimeMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.wuliu.entity.WuliuExpressTime">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="wuliuid" column="wuliuid" jdbcType="INTEGER"/>
            <result property="wuliuno" column="wuliuNo" jdbcType="VARCHAR"/>
            <result property="expressGenerateTime" column="express_generate_time" jdbcType="TIMESTAMP"/>
            <result property="expressReceiveTime" column="express_receive_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,wuliuid,wuliuNo,
        express_generate_time,express_receive_time
    </sql>
    <update id="saveOrUpdateExpressCreateTime">
        if not exists (select 1 from wuliu_express_time where wuliuid = #{wuliuExpressTime.wuliuid})
   	    INSERT INTO wuliu_express_time (
            wuliuid,
            wuliuNo,
            express_generate_time,
            express_receive_time
   	    ) VALUES (
            #{wuliuExpressTime.wuliuid},
            #{wuliuExpressTime.wuliuNo},
            #{wuliuExpressTime.expressGenerateTime},
            #{wuliuExpressTime.expressReceiveTime}
        )
        else
        UPDATE wuliu_express_time
        SET wuliuNo = #{wuliuExpressTime.wuliuNo}
        <if test="wuliuExpressTime.expressGenerateTime">
            , express_generate_time = #{wuliuExpressTime.expressGenerateTime}
        </if>
        <if test="wuliuExpressTime.expressReceiveTime">
            , express_receive_time = #{wuliuExpressTime.expressReceiveTime}
        </if>
        WHERE wuliuid = #{wuliuExpressTime.wuliuid}
    </update>
</mapper>
