<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.wuliu.mapper.WuliuInstockPendingMapper">

    <select id="getPendingSum" resultType="com.jiuji.oa.stock.stockmanage.vo.req.WuliuInstockPendingSumVO">
        select COUNT(1) count ,k.areaid
        from apple_purchase_store_inventory i
            inner join dbo.product_mkc k WITH(nolock) on i.mkc_id=k.id
            left join dbo.wuliu w WITH(nolock) on w.id=i.wuliu_id
        where i.wuliu_id is not NULL and w.stats in (4,6) and k.imei is NULL
        <if test="req.mkcId != null and req.mkcId != ''">
            and k.areaid  in (SELECT DISTINCT pm.areaid from product_mkc pm WITH(nolock) where pm.id = #{req.mkcId})
        </if>
        <if test="req.wuLiuId != null and req.wuLiuId != ''">
            and w.id = #{req.wuliuId}
        </if>
        GROUP by k.areaid
    </select>
    <select id="getPendingCaigouList"
            resultType="com.jiuji.oa.stock.stockmanage.vo.req.WuliuInstockPendingCaigouVO">
        select DISTINCT s.id as caigouId,a.area,w.nu
        from apple_purchase_store_inventory i
                 inner join dbo.product_mkc k WITH(nolock) on i.mkc_id=k.id
            left join dbo.areainfo a WITH(nolock) on a.id=k.areaid
            left join dbo.wuliu w WITH(nolock) on w.id=i.wuliu_id
            inner join dbo.mkcCaiGouBasket b WITH(nolock) on b.mkc_id=k.id
            inner join dbo.mkcCaiGouSub s WITH(nolock) on s.id=b.sub_id
        where i.wuliu_id = #{wuLiuId} and w.stats in (4,6) and k.imei is NULL
    </select>
    <select id="selectThirdProductConfigNotifyUserIds" resultType="java.lang.String">
        select Ch999ids from WXSmsReceiver with(nolock) where Classify='103' and isnull(IsDel,0)=0
    </select>
</mapper>
