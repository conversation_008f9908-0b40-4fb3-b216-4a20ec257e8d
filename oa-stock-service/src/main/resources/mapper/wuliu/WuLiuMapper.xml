<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~
  ~      Copyright © 2006 - 2020 九机网 All Rights Reserved
  ~
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.wuliu.mapper.WuLiuMapper">

    <select id="getWuLiuInfoById" resultType="com.jiuji.oa.wuliu.entity.WuLiu2Entity">
        SELECT top 1 w.*, wd.orgcode, wd.destcode, wd.payType, wd.exepresstype, wd.yuejiekahao
        FROM wuliu w with(nolock) left join wuliuwangdian wd
        with (nolock)
        on w.id = wd.wuliuid
        where w.id = #{id}
    </select>

    <select id="getWuLiuInfoBySubId" resultType="com.jiuji.oa.wuliu.entity.WuLiu2Entity">
        SELECT top 1 w.*, wd.orgcode, wd.destcode, wd.payType, wd.exepresstype, wd.yuejiekahao
        FROM wuliu w with(nolock) left join wuliuwangdian wd
        with (nolock)
        on w.id = wd.wuliuid
        where w.danhaobind = #{subId}
        ORDER BY w.dtime desc
    </select>

    <resultMap id="WuLiuDTOMap" type="com.jiuji.oa.wuliu.vo.WuLiuDTO">
        <id property="id" column="id"/>
        <result property="sName" column="sname"/>
        <result property="sMobile" column="smobile"/>
        <result property="sAddress" column="saddress"/>
        <result property="sArea" column="sarea"/>
        <result property="sCityId" column="scityid"/>
        <result property="rName" column="rname"/>
        <result property="rMobile" column="rmobile"/>
        <result property="rAddress" column="raddress"/>
        <result property="rArea" column="rarea"/>
        <result property="rCityId" column="rcityid"/>
        <result property="area" column="area"/>
        <result property="dTime" column="dtime"/>
        <result property="cTime" column="ctime"/>
        <result property="price" column="price"/>
        <result property="inPrice" column="inprice"/>
        <result property="shouJianRen" column="shoujianren"/>
        <result property="paiJianRen" column="paijianren"/>
        <result property="stats" column="stats"/>
        <result property="wuType" column="wutype"/>
        <result property="comment" column="comment"/>
        <result property="com" column="com"/>
        <result property="nu" column="nu"/>
        <result property="weight" column="weight"/>
        <result property="inUser" column="inuser"/>
        <result property="linkType" column="linktype"/>
        <result property="areaId" column="areaid"/>
        <result property="sAreaId" column="sareaid"/>
        <result property="rAreaId" column="rareaid"/>
        <result property="receiveUser" column="receiveUser"/>
        <result property="receiveTime" column="receiveTime"/>
        <result property="notifyType" column="notifyType"/>
        <result property="subKinds" column="subKinds"/>
        <result property="payMethod" column="pay_method"/>
        <result property="wPid" column="wpid"/>
        <result property="wCateId" column="wCateId"/>
        <result property="sendTime" column="sendtime"/>
        <result property="lastRouteTime" column="LastRouteTime"/>
        <result property="isCreateManually" column="isCreateManually"/>
        <result property="danHaoBind" column="danHaoBind"/>
        <result property="estimatedArrivalTime" column="EstimatedArrivalTime"/>
        <result property="productPriceTotal" column="ProductPriceTotal"/>
        <result property="abnormalFlag" column="isAbnomal"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <select id="getWuLiuList" resultType="com.jiuji.oa.wuliu.vo.WuLiuDTO">
        select wuliu.*,isnull(danhaobind,0) as danhaobind, (case when m.WuliuId is null then 0 else 1 end) as
        is_abnomal,m.remark ,wee.express_type,wee.express_company from wuliu with(nolock) left join
        dbo.MarkAbnomalWuliuRecords m with(nolock) on m.WuliuId =wuliu.id left join dbo.wuliu_express_extend wee
        with(nolock) on wuliu.id = wee.wuliu_id
        <where>
            isnull(m.IsDel,0)=0
            <if test="wuLiuId != 0">
                and wuliu.id=#{wuLiuId}
            </if>
            <if test="subId != 0">
                and isnull(linkType,0)=0 and ISNULL(wutype,1) != 1 and danhaobind= #{subId}
                <if test="actionName == 'sub'">
                    and wutype in (4,6) and isnull(stats,0) != 5
                </if>
                <if test="actionName == 'ReSub'">
                    and wutype in (4,6) and isnull(stats,0) != 5
                </if>
            </if>
        </where>
    </select>

    <select id="getWuLiu" resultType="com.jiuji.oa.wuliu.vo.WuLiuDTO">
        select top 1 wuliu.*,isnull(danhaobind,0) as danhaobind, (case when m.WuliuId is null then 0 else 1 end) as
        is_abnomal,m.remark ,wee.express_type,wee.express_company from wuliu with(nolock) left join
        dbo.MarkAbnomalWuliuRecords m with(nolock) on m.WuliuId =wuliu.id left join dbo.wuliu_express_extend wee
        with(nolock) on wuliu.id = wee.wuliu_id
        <where>
            isnull(m.IsDel,0)=0
            <if test="wuLiuId != 0">
                and wuliu.id=#{wuLiuId}
            </if>
            <if test="subId != 0">
                and isnull(linkType,0)=0 and ISNULL(wutype,1) != 1 and danhaobind= #{subId}
                <if test="actionName == 'sub'">
                    and wutype in (4,6) and isnull(stats,0) != 5
                </if>
                <if test="actionName == 'ReSub'">
                    and wutype = 9 and isnull(stats,0) != 5
                </if>
            </if>
        </where>
    </select>

    <select id="getWuLiuSub" resultType="com.jiuji.oa.wuliu.vo.WuLiuSubDTO">
        seLect top 1 s.sub_id,s.subtype,sub_to,
               ss.address                   as sub_adds,
               s.sub_check as subCheck,
               case when s.islock = 1 then 1 else 0 end cancelApplication,
               s.expectTime,
               sub_mobile,
               isnull(ss.cityid, 0)         as cityid,
               delivery,
               isnull(s.kcAreaid, s.areaid) as areaid,
               zitidianID as zitidian_id,
               yingfum,
               s.yifuM,
               ss.userDate userDate,
               ss.userTime userTime,
               ss.position
        from sub s with(nolock)
             left join SubAddress ss
        with (nolock)
        on ss.sub_id=s.sub_id
        where s.sub_id=#{subId}
    </select>
    <select id="getWuLiuReSub" resultType="com.jiuji.oa.wuliu.vo.WuLiuSubDTO">
        select top 1 s.sub_id,s.subtype,sub_to,
               ss.address           as sub_adds,
               s.sub_check as subCheck,
               case when s.islock = 1 then 1 else 0 end cancelApplication,
               sub_mobile,
               isnull(ss.cityid, 0) as cityid,
               isnull(delivery, 1) delivery,
               s.areaid,
               s.zitidianID as zitidian_id,
               s.saleType as sale_type,
               s.expectTime as expectTime,
               s.yingfuM as yingfum,
               s.yifuM,
             ss.userDate userDate,
             ss.userTime userTime,
             ss.position
        from recover_marketInfo s WITH(NOLOCK)
                        left join RecoverSubAddress ss
        WITH (NOLOCK)
        on ss.sub_id=s.sub_id
        where s.sub_id = #{subId}
    </select>
    <select id="getMarkAbnormalWuLiu" resultMap="WuLiuDTOMap">
        SELECT top 1 wuliu.*,
        ISNULL(danhaobind, 0) AS danhaobind,
        (
        CASE
        WHEN m.WuliuId IS NULL
        THEN 0
        ELSE 1
        END) AS is_abnomal,
        m.remark
        FROM wuliu with(nolock)
        LEFT JOIN
        dbo.MarkAbnomalWuliuRecords m with(nolock)
        ON
        m.WuliuId =wuliu.id
        <where>
            AND ISNULL(m.IsDel,0)=0
            <if test="vo.linkType!=null">
                and linkType=#{vo.linkType}
            </if>
            <if test="vo.wuliuid!=null and vo.wuliuid!=0">
                and id=#{vo.wuliuid}
            </if>
            <choose>
                <when test="vo.yuyueId!=null and vo.yuyueId!=0">
                    and linktype in(2,3) and danhaobind=#{vo.yuyueId}
                </when>
                <otherwise>
                    <if test="vo.shouHouId!=null and vo.shouHouId!=0">
                        and linktype in(4,5) and danhaobind=#{vo.shouHouId}
                    </if>
                </otherwise>
            </choose>
        </where>
    </select>
    <update id="updateRecoverAuction">
        UPDATE RecoverAuction
        SET wuliuid=#{wuLiuId},
            wuliugongsi=#{wuLiuGongSi},
            wuliutime=GETDATE() OUTPUT Inserted.InsourceId
        WHERE subid=#{subId} AND wuliutime IS NULL
    </update>
    <update id="updateRecoverAuctionLinkWuId">
        UPDATE RecoverAuction
        SET wuliuid=#{wuLiuId},
            wuliugongsi=#{wuLiuGongSi},
            wuliutime=GETDATE()
        WHERE InsourceId = #{inSourceId}
          AND wuid = #{wuId}
          AND wuliuid IS NULL
    </update>
    <update id="delWuliuByLinkId">
        update wuliu
        set stats=5 output Deleted.id,Deleted.com,Deleted.nu
        where isnull(linkType, 0)=0 and danhaobind= #{linkId};
    </update>
    <select id="selectUserByUserId" resultType="com.jiuji.oa.wuliu.entity.WuLiuWeixinUserEntity">
        SELECT TOP 1 openid,wxid
        FROM dbo.WeixinUser with(nolock)
        where follow =1 and userid = #{userId}
    </select>
    <select id="getsubConfirmStartTime" resultType="java.time.LocalDateTime">
        select top 1 case when ss.sub_date between ss.stime and ss.etime then ss.sub_date when ss.sub_date &lt; ss.stime then ss.stime else dateadd(day,1,ss.stime) end as startTime
        from (
            select s.sub_id, s.areaid, s.sub_date, (convert (nvarchar(10), s.sub_date, 121)+' '+ trim (isnull(a.deliveryBeginTime, '09:00'))) as stime, (convert (nvarchar(10), s.sub_date, 121)+' '+ trim (isnull(a.deliveryEndTime, '21:30'))) as etime
            from dbo.sub s with (nolock) left join dbo.areainfo a with (nolock) on s.areaid=a.id where s.sub_id = #{sub_id}
            ) ss
    </select>
    <select id="getSecordHandsubConfirmStartTime" resultType="java.time.LocalDateTime">
        select top 1 case when ss.sub_date between ss.stime and ss.etime then ss.sub_date when ss.sub_date &lt; ss.stime then ss.stime else dateadd(day,1,ss.stime) end as startTime
        from (
            select s.sub_id, s.areaid, s.sub_date, (convert (nvarchar(10), s.sub_date, 121) + ' ' + trim (isnull(a.deliveryBeginTime, '09:00'))) as stime, (convert (nvarchar(10), s.sub_date, 121) + ' ' + trim (isnull(a.deliveryEndTime, '21:30'))) as etime
            from dbo.recover_marketInfo s with (nolock) left join dbo.areainfo a with (nolock) on s.areaid = a.id where s.sub_id = #{sub_id}
            ) ss
    </select>
    <select id="getZtoSiteConfig" resultType="com.jiuji.oa.wuliu.vo.ZtoSiteConfigDTO">
        SELECT areaid id,*
        FROM wuliu_ztoconfig WITH(NOLOCK)
        WHERE areaid=#{areaId}
    </select>
    <select id="getWuLiuClaim" resultType="com.jiuji.oa.wuliu.entity.WuLiuClaimEntity">
        SELECT TOP 1 w.com, ISNULL(l.dtime, w.dtime) dtime,
               w.sareaid,
               w.areaid,
               f.Id,
               w.stats,
               w.rareaid,
               w.isCreateManually,
               isnull(w.wutype,0) wutype,
               isnull(w.danhaobind,0) danhaobind,
               case when w.wutype in (4,6) then s.delivery when w.wutype in (9) then rm.delivery else 0 end delivery
        FROM dbo.wuliu w WITH(NOLOCK)
                LEFT JOIN  dbo.wuliu_logs l
        WITH (NOLOCK)
        ON l.wuliuid = w.id
            LEFT JOIN dbo.wuliuClaimForm f
        WITH (NOLOCK)
        ON w.id = f.wuliuId
        LEFT JOIN  sub s WITH(NOLOCK) ON w.danhaobind = s.sub_id and w.wutype in (4,6)
        LEFT JOIN  recover_marketInfo rm WITH(NOLOCK) ON w.danhaobind = rm.sub_id and w.wutype in (9)
        WHERE w.id = #{wuliuid}
        ORDER BY l.dtime DESC
    </select>
    <select id="getJieXinByCmd" resultType="integer">
        select count(1)
        from dbo.shouying s with(nolock)
                 left join dbo.sub sub with(nolock) on s.sub_id = sub.sub_id
        where exists(select 1 from dbo.areainfo a with(nolock) where s.areaid = a.id and a.xtenant = 0)
          and exists(select 1 from dbo.shouyin_other o with(nolock) where s.id = o.shouyinid and o.type_ in (4, 7))
          and s.sub_id = #{subId};
    </select>
    <select id="getAreaList" resultType="com.jiuji.oa.orginfo.areainfo.vo.res.AreaListRes">
        select name, rank, code, parent_code, isnull(isReal, 0) isReal, level_, name1
        from AreaList with(nolock)
        where name !='全国' and level_ &lt;=4
    </select>
    <select id="getJieXinBySql" resultType="integer">
        select count(1)
        from dbo.shouying s with(nolock) left join dbo.sub sub
        with (nolock)
        on s.sub_id=sub.sub_id
        where exists (select 1 from dbo.areainfo a with (nolock) where s.areaid=a.id
          and a.xtenant=0)
          and exists (select 1 from dbo.shouyin_other o with (nolock) where s.id=o.shouyinid
          and o.type_ in (4
            , 7) )
          and s.sub_id= #{subId}
    </select>
    <select id="getThirdPartyLog" resultType="com.jiuji.oa.wuliu.vo.SubWuliuTransferLogDTO">
        select b.sub_id        as sub_id,
               p.product_name  as product_name,
               p.product_color as product_color,
               w.sareaid       as areaid,
               w.rareaid       as toareaid,
               b.basket_count  as basket_count,
               1               as lcount,
               w.com           as com,
               w.nu            as nu
        from dbo.mkc_toarea m with(nolock)
        join dbo.product_mkc k
        with (nolock)
        on k.id=m.mkc_id
            join dbo.basket b
        with (nolock)
        on b.basket_id=k.basket_id
            join dbo.productinfo p
        with (nolock)
        on b.ppriceid=p.ppriceid
            join wuliu w
        with (nolock)
        on w.id=m.wuliuid
        where w.id =#{wuliuid} and w.nu is not null and isnull(w.com, '') &lt;&gt;''
        union
        select b.sub_id,
               p.product_name,
               p.product_color,
               w.sareaid areaid,
               w.rareaid toareaid,
               b.basket_count,
               k.lcount,
               w.com,
               w.nu
        from dbo.diaobo_sub s with(nolock)
        join dbo.diaobo_basket k
        with (nolock)
        on s.id=k.sub_id
            join dbo.basket b
        with (nolock)
        on b.basket_id=k.basket_id and k.basket_type = 0
            join dbo.productinfo p
        with (nolock)
        on b.ppriceid=p.ppriceid
            join wuliu w
        with (nolock)
        on w.id=s.wuliuid
        where w.id =#{wuliuid} and w.nu is not null and isnull(w.com, '') &lt;&gt;'';
    </select>

    <insert id="inserWuLiuNoEx">
        insert into wuliunoex(nu, wuliuid, com, packageCount)
        values (#{nuid}, #{wuliuid}, #{com}, #{packageCount})
    </insert>

    <select id="getPrintCount" resultType="integer">
        SELECT printCount
        FROM dbo.PrintCount with(nolock)
        WHERE subId = #{subId}
          AND printType = #{type}
    </select>
    <select id="getReceiveNameList" resultType="com.jiuji.oa.wuliu.vo.res.WuLiuSendMsgRes">
        SELECT STRING_AGG(w.id, ',') AS wuIds, w.rname as name, u.ch999_id AS userId
        FROM dbo.wuliu w with(nolock) LEFT
        JOIN dbo.ch999_user u
        with (nolock)
        ON w.rname=u.ch999_name
        WHERE w.id IN
        <foreach collection="ids" index="index" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        AND w.rareaid
        != 0
        AND u.ch999_id IS NOT NULL
        GROUP BY w.rname, u.ch999_id
    </select>
    <select id="getInuserNameList" resultType="com.jiuji.oa.wuliu.vo.res.WuLiuSendMsgRes">
        SELECT u.ch999_id AS userId, w.inuser AS name, STRING_AGG(w.id, ',') AS wuIds
        FROM dbo.wuliu w with(nolock) LEFT JOIN dbo.ch999_user u
        with (nolock)
        ON w.inuser=u.ch999_name
        WHERE w.id IN
        <foreach collection="ids" index="index" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        AND u.ch999_id IS NOT NULL
        GROUP BY w.inuser, u.ch999_id
    </select>
    <select id="getSubAreaidBySubAndPaytype" resultType="java.lang.Integer">
        <if test="payType == 1">
            select top 1 areaid from dbo.sub with(nolock) where sub_id = #{subId}
        </if>
        <if test="payType == 2">
            select top 1 isnull(toareaid,areaid) as areaid from dbo.shouhou with(nolock) where id = #{subId}
        </if>
        <if test="payType == 3">
            select top 1 areaid from dbo.recover_marketInfo with(nolock) where sub_id = #{subId}
        </if>
        <if test="payType == 5">
            select top 1 s.areaid from office.dbo.EvaluateScore e with(nolock) left join dbo.sub s  with(nolock) on e.sub_id=s.sub_id where e.id = #{subId}
        </if>
        <if test="payType == 10">
            select top 1 isnull(toareaid,areaid) as areaid from dbo.smallpro with(nolock) where id = #{subId}
        </if>
        <if test="payType == 4">
            select top 1 s.areaid from dbo.punishSubDetail b with(nolock) left join dbo.punishSub s  with(nolock) on b.sub_id = s.sub_id where b.id = #{subId}
        </if>
        <if test="payType == 6">
            select top 1 s.areaid FROM dbo.shouhou s WITH(NOLOCK) INNER JOIN dbo.daiyongji_yajin yj WITH(NOLOCK) ON s.id=yj.wxid WHERE yj.id = #{subId}
        </if>
    </select>

    <select id="getExpressTypeByNu" resultType="java.lang.String">
        SELECT d.exepresstype FROM dbo.wuliu w WITH(NOLOCK) INNER JOIN dbo.wuliuwangdian d WITH(NOLOCK) ON w.id=d.wuliuid WHERE w.com='shunfeng' AND w.nu = #{nu}
    </select>
    <select id="countMeituanFastSub" resultType="java.lang.Integer">
        SELECT count(1) FROM sub s with(nolock) WHERE s.subtype = 19 and sub_id = #{subId}
    </select>
    <select id="getAllAreaWeb" resultType="com.jiuji.oa.wuliu.vo.AreaWebModel">
        select cityid,isnull(kc_dids,cityid) as kcDidString,id,dname from areaweb  with(nolock)
    </select>
    <select id="getWuLiuByWuType" resultType="com.jiuji.oa.wuliu.entity.WuLiuEntity">
        select top 1 * from wuliu w with(nolock) where w.sareaid = #{sAreaId} and w.wutype = #{wuType} order by id desc
    </select>
    <select id="queryPayMethodByAreaId" resultType="java.lang.Integer">
        SELECT TOP 1 pay_method FROM dbo.wuliu with(nolock)
        WHERE areaid = #{areaId}
            AND pay_method IS NOT NULL
        ORDER BY ID DESC
    </select>
    <select id="getWuliuByNu" resultType="com.jiuji.oa.wuliu.entity.WuLiuEntity">
        select
            w.*
        from
            wuliu w with(nolock)
        where
            w.nu = #{nu}
        UNION
        select
            w.*
        from
            wuliu w with(nolock)
            left join wuliunoex wn WITH(NOLOCK) on w.id = wn.wuliuid
        where
            wn.nu = #{nu}
    </select>
    <select id="getWuLiuByWuTypeAndDanhaobind" resultType="com.jiuji.oa.wuliu.entity.WuLiuEntity">
        select top 1 * from wuliu with(nolock)
        where stats in (1,2,3,4,6)
        and wutype = #{wuType}
        and danhaobind = #{danhaobind}
        order by id desc
    </select>
    <update id="updateStateById">
        update wuliu set stats= #{stats} where id=#{wuliuid}
    </update>
    <select id="getTimeoutWuliuList" resultType="com.jiuji.oa.wuliu.entity.WuLiuEntity">
        select w.* from wuliu w with(nolock) where w.stats in (1,2,3) and DATEDIFF(dd,w.dtime,getdate()) <![CDATA[ > ]]> #{days} order by id
    </select>
    <select id="getInnerWuliuInfo" resultType="com.jiuji.oa.wuliu.vo.res.InnerWuliuInfoRes">
        <choose>
            <when test="req.type == 1">
                select w.id wuliuId,p.product_name,b.ppriceid skuId,k.basket_id basketId,
                a1.area sendArea,a2.area receiveArea
                from mkc_toarea m with(nolock)
                join product_mkc k with(nolock) on k.id=m.mkc_id
                join basket b with(nolock) on b.basket_id=k.basket_id
                join productinfo p with(nolock) on b.ppriceid=p.ppriceid
                join wuliu w with(nolock) on w.id=m.wuliuid
                left join areainfo a1 with(nolock) on a1.id=w.sareaid
                left join areainfo a2 with(nolock) on a2.id=w.rareaid
                where w.stats in (1,2,3,4,6)
                <include refid="innerWuliuWhere"/>
                union
                select w.id wuliuId,p.product_name,b.ppriceid skuId,k.basket_id basketId,
                a1.area sendArea,a2.area receiveArea
                from diaobo_sub s with(nolock)
                join diaobo_basket k with(nolock) on s.id=k.sub_id
                join basket b with(nolock) on b.basket_id=k.basket_id and k.basket_type = 0
                join productinfo p with(nolock) on b.ppriceid=p.ppriceid
                join wuliu w with(nolock) on w.id=s.wuliuid
                left join areainfo a1 with(nolock) on a1.id=w.sareaid
                left join areainfo a2 with(nolock) on a2.id=w.rareaid
                where w.stats in (1,2,3,4,6)
                <include refid="innerWuliuWhere"/>
            </when>
            <when test="req.type == 2">
                SELECT w.id wuliuId,p.product_name,p.ppriceid skuId,k.to_basket_id basketId,
                a1.area sendArea,a2.area receiveArea
                FROM recover_marketSubInfo b WITH(NOLOCK)
                join recover_marketInfo s WITH(NOLOCK) on s.sub_id = b.sub_id
                join recover_mkc k WITH(NOLOCK) on k.to_basket_id = b.basket_id
                left join productinfo p WITH(NOLOCK) on k.ppriceid = p.ppriceid
                join recover_toarea t WITH(NOLOCK) on t.mkc_id = k.id
                join wuliu w with(nolock) on t.wuliuid = w.id
                left join areainfo a1 with(nolock) on a1.id=w.sareaid
                left join areainfo a2 with(nolock) on a2.id=w.rareaid
                where w.stats in (1,2,3,4,6)
                <include refid="innerWuliuWhere"/>
            </when>
            <when test="req.type == 3">
                select w.id wuliuId,p.product_name,p.ppriceid skuId,sa.caigouid basketId,
                a1.area sendArea,a2.area receiveArea
                from shouhou_apply sa with(nolock)
                left join diaobo_sub ds with(nolock) on sa.caigouid = ds.id and ds.stats not in(4, 0)
                left join wuliu w with(nolock) on ds.wuliuid = w.id
                left join productinfo p with(nolock) on sa.ppid = p.ppriceid
                left join areainfo a1 with(nolock) on a1.id=w.sareaid
                left join areainfo a2 with(nolock) on a2.id=w.rareaid
                where ds.id is not null
                and w.stats in (1,2,3,4,6)
                <if test="req.wuliuId != null">
                    and w.id = #{req.wuliuId}
                </if>
                <if test="req.danhaobind != null">
                    and sa.wxid = #{req.danhaobind}
                </if>
            </when>
        </choose>
    </select>
    <select id="getInnerWuliuCount" resultType="java.lang.Integer">
        select count(*) from (
        select w.id from wuliu w with(nolock)
        left join mkc_toarea m with(nolock) on w.id=m.wuliuid
        left join product_mkc k with(nolock) on k.id=m.mkc_id
        where w.id = #{wuliuId} and k.basket_id is not null
        union
        select w.id from wuliu w with(nolock)
        left join diaobo_sub s with(nolock) on w.id=s.wuliuid
        left join diaobo_basket k with(nolock) on s.id=k.sub_id
        where w.id = #{wuliuId} and k.basket_id is not null
        union
        select w.id from wuliu w with(nolock)
        left join recover_toarea t with(nolock) on t.wuliuid = w.id
        left join recover_mkc k WITH(nolock) on k.id = t.mkc_id
        where w.id = #{wuliuId} and k.to_basket_id is not null
        union
        select w.id from wuliu w with(nolock)
        left join diaobo_sub ds with(nolock) on w.id=ds.wuliuid
        left join shouhou_apply sa with(nolock) on sa.caigouid = ds.id and ds.stats not in(4, 0)
        where w.id = #{wuliuId} and sa.id is not null
        ) t
    </select>
    <select id="getDiaobWuliuCount" resultType="java.lang.Integer">
        select count(*) from (
        select w.id from wuliu w with(nolock)
        left join mkc_toarea m with(nolock) on w.id=m.wuliuid
        where w.id = #{wuliuId} and m.id is not null
        union
        select w.id from wuliu w with(nolock)
        left join diaobo_sub s with(nolock) on w.id=s.wuliuid
        left join diaobo_basket k with(nolock) on s.id=k.sub_id
        where w.id = #{wuliuId} and s.id is not null
        union
        select w.id from wuliu w with(nolock)
        left join recover_toarea t with(nolock) on t.wuliuid = w.id
        where w.id = #{wuliuId} and t.id is not null
        ) t
    </select>
    <select id="querySignatureWuliuList" resultType="com.jiuji.oa.wuliu.bo.SignatureWuliuBO">
        select w.id wuliuId,u.ch999_id ch999UserId from wuliu w with(nolock)
        join wuliu_logs wl with(nolock) on w.id = wl.wuliuid and wl.kind = 2
        join ch999_user u with (nolock) on w.rname = u.ch999_name and u.iszaizhi = 1
        where w.stats in (0,1,2,3,7)
        and datediff(dd,wl.dtime,getdate()) > 0
    </select>
    <select id="queryDiaoboPaotuiWuliu" resultType="com.jiuji.oa.wuliu.bo.DiaoboPaotuiWuliuResBO">
        select w.id wuliuId,s.id subId,'' orderid,u.ch999_id sendUserId,1 kind from wuliu w with(nolock)
        left join diaobo_sub s with(nolock) on w.id=s.wuliuid
        left join ch999_user u with(nolock) on w.sname=u.ch999_name and u.iszaizhi=1
        where w.wutype = 1
        and s.id is not null
        and w.id = #{req.wuliuId}
        and s.kinds in('pj','wx')
        and s.stats = 3
        union all
        select w.id wuliuId,m.id subId,k.orderid,u.ch999_id sendUserId,2 kind from wuliu w with(nolock)
        left join mkc_toarea m with(nolock) on w.id=m.wuliuid
        left join product_mkc k with(nolock) on m.mkc_id = k.id
        left join ch999_user u with(nolock) on m.senduser=u.ch999_name and u.iszaizhi=1
        where w.wutype = 1
        and m.id is not null
        and m.stats = 2
        and w.id = #{req.wuliuId}
        union all
        select w.id wuliuId,m.id subId,k.orderid,u.ch999_id sendUserId,2 kind from wuliu w with(nolock)
        left join recover_toarea m with(nolock) on w.id=m.wuliuid
        left join recover_mkc k with(nolock) on m.mkc_id = k.id
        left join ch999_user u with(nolock) on m.senduser=u.ch999_name and u.iszaizhi=1
        where w.wutype = 1
        and m.id is not null
        and m.status = 2
        and k.issalf = 1
        and w.id = #{req.wuliuId}
    </select>
    <sql id="innerWuliuWhere">
        <if test="req.wuliuId != null">
            and w.id = #{req.wuliuId}
        </if>
        <if test="req.danhaobind != null">
            and b.sub_id = #{req.danhaobind}
        </if>
    </sql>
</mapper>
