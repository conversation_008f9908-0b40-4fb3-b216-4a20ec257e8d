<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.wuliu.mapper.WuLiuSubFlagRecordMapper">


    <select id="pageNationalSupplementState" resultType="com.jiuji.oa.stock.nationalSupplement.res.NationalSupplementAttachmentRes">
        select *
        from (
        <include refid="selectRow"/>
        <include refid="selectComment"/>
        ) t order by t.tradeDate1 desc
    </select>
    <select id="selectNationalAttachmentInfo" resultType="com.jiuji.oa.stock.nationalSupplement.res.NationalAttachmentInfo">
        SELECT ac.attachment_name ,a.filepath,ac.description,a.linkedID as subId  FROM dbo.attachments a left join dbo.attachment_config ac on a.kind = ac.id
        <where>
            <if test="subIdList != null and subIdList.size() > 0">
                and a.linkedID in
                <foreach collection="subIdList" item="subId" open="(" separator="," close=")">
                    #{subId}
                </foreach>
            </if>
            and a.[type] = 10 and ac.id is not NULL and isnull(ac.is_del,0) = 0
        </where>

    </select>
    <select id="selectDepartNameInfo" resultType="com.jiuji.oa.stock.nationalSupplement.res.DepartNameInfo">
        select a.id as areaId, d.name as departName
        from dbo.areainfo a with (nolock)
        left join dbo.departInfo d with (nolock) on d.id = dbo.getDepartTypeId(a.depart_id, 4)
        <where>
            <if test="areaIdList != null and areaIdList.size() > 0">
                a.id in
                <foreach collection="areaIdList" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>
    <sql id="selectRow">
        select f.id,s.sub_id,a.area,p.product_name,p.product_color,b.price,s.tradeDate1,f.check_state,a.id as areaId,
               s.sub_to,sa.invoice_id_card,sa.invoice_received_email,b.seller,f.check_time,f.check_user,f.status,f.finance_check_state,
               f.operation_check_state,f.operation_check_time,f.check_comment,s.sub_date,b.basket_id,f.operation_check_user,f.status_comment
    </sql>
    <sql id="selectComment">
         from dbo.subFlagRecord f with (nolock)
        left join dbo.sub s with (nolock) on f.sub_id = s.sub_id
        left join dbo.SubAddress sa on f.sub_id = sa.sub_id
        left join dbo.basket b with(nolock ) on b.sub_id = s.sub_id
        left join dbo.productinfo p with(nolock ) on p.ppriceid = b.ppriceid
        left join dbo.areainfo a with(nolock ) on a.id = s.areaid
        <where>
            s.sub_check = 3 and f.flagType = 7 and isnull(b.isdel,0) = 0
            <if test="req.checkState != null ">
                and isnull(f.check_state,0) = #{req.checkState}
            </if>

            <if test="req.financeCheckState != null ">
                and isnull(f.finance_check_state,0) = #{req.financeCheckState}
            </if>
            <if test="req.status != null ">
                and isnull(f.status,0) = #{req.status}
            </if>

            <if test="req.operationCheckState != null ">
                and isnull(f.operation_check_state,0) = #{req.operationCheckState}
            </if>
            <if test="req.areaIdList != null and req.areaIdList.size() > 0">
                and s.areaid in
                <foreach collection="req.areaIdList" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="req.cidList != null and req.cidList.size() > 0">
                and p.cid in
                <foreach collection="req.cidList" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="req.brandIdList != null and req.brandIdList.size() > 0">
                and p.brandID in
                <foreach collection="req.brandIdList" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test=" req.startTime != null and req.endTime != null  ">
                <choose>
                    <when test="req.selectTimeType!=null and  req.selectTimeType == 2">
                        and f.check_time between #{req.startTime} and #{req.endTime}
                    </when>
                    <when test="req.selectTimeType!=null and  req.selectTimeType == 3">
                        and f.operation_check_time between #{req.startTime} and #{req.endTime}
                    </when>
                    <when test="req.selectTimeType!=null and  req.selectTimeType == 4">
                        and s.sub_date between #{req.startTime} and #{req.endTime}
                    </when>
                    <otherwise>
                        and s.tradeDate1 between #{req.startTime} and #{req.endTime}
                    </otherwise>
                </choose>
            </if>
            <if test="req.selectType != null and req.selectValue != '' and req.selectValue !=null and req.selectType == 1">
                and s.sub_id = #{req.selectValue}
            </if>
            <if test="req.selectType != null and req.selectValue != '' and req.selectValue !=null and req.selectType == 2">
                and p.product_name = #{req.selectValue}
            </if>
            <if test="req.selectType != null and req.selectValue != '' and req.selectValue !=null and req.selectType == 3">
                and b.ppriceid = #{req.selectValue}
            </if>
            <if test="req.selectType != null and req.selectValue != '' and req.selectValue !=null and req.selectType == 4">
                and s.trader = #{req.selectValue}
            </if>
            <if test="req.selectType != null and req.selectValue != '' and req.selectValue !=null and req.selectType == 5">
                and b.seller = #{req.selectValue}
            </if>
            <if test="req.selectType != null and req.selectValue != '' and req.selectValue !=null and req.selectType == 6">
                and f.check_user = #{req.selectValue}
            </if>
            <if test="req.selectType != null and req.selectValue != '' and req.selectValue !=null and req.selectType == 7">
                and f.operation_check_user = #{req.selectValue}
            </if>
            <choose>
                <when test="req.nationalAttachmentCashier != null and req.nationalAttachmentCashier.size() > 0 and req.onlineNationalAttachmentCashier != null and req.onlineNationalAttachmentCashier.size() > 0">
                    and (exists(select 1 from dbo.netpay_record n with(nolock)
                    where n.type=1 and isnull(n.butie_kind,0)=1
                    and isnull(n.payWay,'') != '小UPOS快速开单' and cast(s.sub_id as nvarchar(20))=n.sub_number)
                    or exists( select 1 from dbo.shouying y with(nolock) inner join dbo.shouyin_other so with(nolock) on y.id=so.shouyinid
                    where y.shouying_type in ('订金','交易')  and y.sub_id =s.sub_id
                    and so.type_ in
                    <foreach collection="req.nationalAttachmentCashier" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach> ))
                </when>
                <when test="req.nationalAttachmentCashier != null and req.nationalAttachmentCashier.size() > 0">
                    and EXISTS(
                    SELECT 1 FROM shouying y with(nolock)
                    left join shouyin_other so  with(nolock) on y.id = so.shouyinid
                    where y.shouying_type in ('订金','交易') and y.sub_id = s.sub_id and so.type_ in
                    <foreach collection="req.nationalAttachmentCashier" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                    )
                </when>
                <when test="req.onlineNationalAttachmentCashier != null and req.onlineNationalAttachmentCashier.size() > 0">
                    and EXISTS( select 1 from dbo.netpay_record n with(nolock)
                    where n.type=1 and isnull(n.butie_kind,0)=1
                    and isnull(n.payWay,'') != '小UPOS快速开单' and cast(s.sub_id as nvarchar(20))=n.sub_number)
                </when>
            </choose>
        </where>
    </sql>
    <select id="selectNationalSupplementState" resultType="com.jiuji.oa.stock.nationalSupplement.res.NationalSupplementAttachmentRes">
        <include refid="selectRow"/>
        <include refid="selectComment"/>
    </select>
    <select id="selectNationalSupplementStateCount" resultType="java.lang.Integer">
        select count(1)
        <include refid="selectComment"/>
    </select>
    <select id="selectDeclarationNotApproved" resultType="com.jiuji.oa.stock.nationalSupplement.res.DeclarationNotApprovedRes">
        select cu.ch999_id as userId, s.sub_id as subId, sr.status_comment,s.areaid
        from dbo.sub s with (nolock)
        left join dbo.ch999_user cu with (nolock) on cu.ch999_name = s.trader
        left join dbo.subFlagRecord sr with (nolock) on sr.sub_id = s.sub_id
        <where>
            sr.flagType = 7
            and sr.status = 2
            <if test="req.notPushList != null and req.notPushList.size() > 0">
                and sr.status_comment not in
                <foreach collection="req.notPushList" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="req.userId != null and req.userId != 0">
                and cu.ch999_id = #{req.userId}
            </if>
        </where>
    </select>
    <select id="selectNationalSupplementCount" resultType="com.jiuji.oa.stock.nationalSupplement.res.NationalAttachmentCount">
        select
        SUM(CASE WHEN isnull(check_state,0) = 0 THEN 1 ELSE 0 END) AS approved_count,
        SUM(CASE WHEN isnull(check_state,0) = 1 THEN 1 ELSE 0 END) AS pending_count,
        SUM(CASE WHEN isnull(check_state,0) = 2 THEN 1 ELSE 0 END) AS rejected_count,
        SUM(CASE WHEN isnull(status,0 ) = 0 THEN 1 ELSE 0 END)      AS not_declare_count,
        SUM(CASE WHEN isnull(status,0 ) = 1 THEN 1 ELSE 0 END)      AS declare_count,
        SUM(CASE WHEN isnull(status,0 ) = 2 THEN 1 ELSE 0 END)      AS declare_fail_count,
        COUNT(*) AS all_count
        <include refid="selectComment"/>
    </select>
    <select id="selectDeclarationDataSaas" resultType="java.lang.Integer">
     select sfr.sub_id
        from dbo.subFlagRecord sfr with (nolock)
         left join dbo.sub s with (nolock) on s.sub_id = sfr.sub_id
        <where>
            sfr.flagType =7 and status =0
            AND EXISTS (SELECT 1 FROM dbo.tax_piao t WITH (NOLOCK) WHERE t.sub_id = s.sub_id AND t.type_ = 0  AND t.flag in(3,4)
            AND NOT EXISTS (
            SELECT 1 FROM dbo.tax_piao_response tpr WITH(NOLOCK)
            WHERE tpr.piaoid = t.id and tpr.requestKind != 0 ))
         <if test="req.checkState != null ">
             and isnull(sfr.check_state,0) = #{req.checkState}
         </if>
        <if test="req.subCheck != null ">
            and isnull(s.sub_check,0) = #{req.subCheck}
        </if>
        </where>
    </select>
    <select id="selectAgencyCount" resultType="java.lang.Integer">
        select COUNT(1)
        from dbo.subFlagRecord sfr with (nolock)
        where ISNULL(sfr.flagType, 0) = 7
          and ISNULL(sfr.check_state, 0) = 2
          and exists(select 1 from dbo.sub s with(nolock ) where s.trader = #{req.userName} and s.sub_id = sfr.sub_id and s.sub_check=3)
    </select>
    <select id="selectDepartVUser" resultType="java.lang.Integer">
        SELECT cu.ch999_id
        from ch999_user cu with(nolock)
        left join departInfo di with(nolock) on di.id = dbo.getDepartTypeId(cu.depart_id, 3)
        where isnull(cu.iszaizhi, 0) = 1
          and isnull(di.isdel, 0) = 0
          and di.id = 896
        <if test="userIdList != null and userIdList.size() > 0">
            and cu.ch999_id in
            <foreach collection="userIdList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>
    <select id="selectDepartVAreaId" resultType="java.lang.Integer">
        SELECT a.id
        from dbo.areainfo a with(nolock)
        left join dbo.departInfo di with(nolock) on di.id = dbo.getDepartTypeId(a.depart_id, 3)
        where isnull(di.isdel, 0) = 0
          and di.id = 896
          and isnull(a.ispass, 0) = 1
    </select>
    <select id="getNationalSupplementKindList"
            resultType="com.jiuji.oa.stock.nationalSupplement.res.NationalSupplementKindRes">
        select n.sub_number as subId,
               1 as kind
        from dbo.netpay_record n with(nolock)
        where n.type=1
        and isnull(n.butie_kind,0)=1
        and isnull(n.refundPrice,0)=0
        and isnull(n.payWay,'') != '小UPOS快速开单'
        and n.sub_number in
        <foreach collection="subIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    
    <select id="querySubsByReturnDate" resultType="com.jiuji.oa.stock.nationalSupplement.res.SubReturnDateRes">
        select s.sub_id as subId, s.returnDate
        from dbo.sub s with (nolock)
        where s.sub_check = 9
          and exists(select 1 from dbo.subFlagRecord sf with (nolock) where sf.sub_id = s.sub_id and sf.flagType = 7)
          and s.returnDate between #{req.startTime} and #{req.endTime}
    </select>
    <select id="getTenantId" resultType="java.lang.Integer">
        select MIN(xtenant)
        from dbo.subFlagRecord sfr with (nolock)
        left join dbo.sub s with (nolock) on s.sub_id = sfr.sub_id
        left join dbo.areainfo a with (nolock) on a.id = s.areaid
        <where>
            sfr.flagType =7 and status =0
            AND EXISTS (SELECT 1 FROM dbo.tax_piao t WITH (NOLOCK) WHERE t.sub_id = s.sub_id AND t.type_ = 0  AND t.flag in(3,4)
            AND NOT EXISTS (
            SELECT 1 FROM dbo.tax_piao_response tpr WITH(NOLOCK)
            WHERE tpr.piaoid = t.id and tpr.requestKind != 0 ))
            <if test="req.checkState != null ">
                and isnull(sfr.check_state,0) = #{req.checkState}
            </if>
            <if test="req.subCheck != null ">
                and isnull(s.sub_check,0) = #{req.subCheck}
            </if>
        </where>
    </select>
</mapper>
