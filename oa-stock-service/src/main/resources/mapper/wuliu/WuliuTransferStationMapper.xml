<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.wuliu.mapper.WuliuTransferStationMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.wuliu.entity.WuliuTransferStation">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="wuliuId" column="wuliu_id" jdbcType="INTEGER"/>
            <result property="stationAreaId" column="station_area_id" jdbcType="INTEGER"/>
            <result property="state" column="state" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="isDel" column="is_del" jdbcType="INTEGER"/>
            <result property="wuliuTransferStationRv" column="wuliu_transfer_station_rv" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,wuliu_id,station_area_id,
        state,create_time,update_time,
        is_del,wuliu_transfer_station_rv
    </sql>
</mapper>
