<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.wuliu.mapper.AreaassetsubMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.wuliu.entity.Areaassetsub">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="title" column="title" jdbcType="VARCHAR"/>
            <result property="areaid" column="areaid" jdbcType="INTEGER"/>
            <result property="subcheck" column="subCheck" jdbcType="INTEGER"/>
            <result property="inuser" column="inuser" jdbcType="VARCHAR"/>
            <result property="comment" column="comment" jdbcType="VARCHAR"/>
            <result property="dtime" column="dtime" jdbcType="TIMESTAMP"/>
            <result property="checkuser" column="checkUser" jdbcType="VARCHAR"/>
            <result property="checktime" column="checkTime" jdbcType="TIMESTAMP"/>
            <result property="outuser" column="outUser" jdbcType="VARCHAR"/>
            <result property="outtime" column="outTime" jdbcType="TIMESTAMP"/>
            <result property="result" column="result" jdbcType="VARCHAR"/>
            <result property="wuliudan" column="wuliudan" jdbcType="BIGINT"/>
            <result property="wuliutime" column="wuliutime" jdbcType="TIMESTAMP"/>
            <result property="confirmuser" column="confirmUser" jdbcType="VARCHAR"/>
            <result property="confirmtime" column="confirmTime" jdbcType="TIMESTAMP"/>
            <result property="departcode" column="departCode" jdbcType="VARCHAR"/>
            <result property="piqianid" column="piqianid" jdbcType="BIGINT"/>
            <result property="pzid" column="pzid" jdbcType="BIGINT"/>
            <result property="outareaid" column="outAreaId" jdbcType="INTEGER"/>
            <result property="emergencylevel" column="emergencylevel" jdbcType="INTEGER"/>
            <result property="departId" column="depart_id" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,title,areaid,
        subCheck,inuser,comment,
        dtime,checkUser,checkTime,
        outUser,outTime,result,
        wuliudan,wuliutime,confirmUser,
        confirmTime,departCode,piqianid,
        pzid,outAreaId,emergencylevel,
        depart_id
    </sql>

    <update id="updateAreaAssetSub">
        UPDATE AreaAssetSub
        SET confirmTime=GETDATE(),
            confirmUser = #{userName},
            subCheck = #{subChenkType}
        WHERE id = #{danHaoBind}
    </update>
    <select id="selectAreaAssetSubById" resultType="java.lang.Integer">
        select pzid from AreaAssetSub with(nolock)
        where id = #{danHaoBind}
    </select>
</mapper>
