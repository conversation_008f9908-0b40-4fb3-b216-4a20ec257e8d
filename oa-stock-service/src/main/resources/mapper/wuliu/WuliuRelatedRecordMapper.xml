<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.wuliu.mapper.WuliuRelatedRecordMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.wuliu.entity.WuliuRelatedRecord">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="isDel" column="is_del" jdbcType="BIT"/>
            <result property="wuliuId" column="wuliu_id" jdbcType="INTEGER"/>
            <result property="businessType" column="business_type" jdbcType="INTEGER"/>
            <result property="businessId" column="business_id" jdbcType="INTEGER"/>
            <result property="estimatedStockingTime" column="estimated_stocking_time" jdbcType="TIMESTAMP"/>
            <result property="actualStockingTime" column="actual_stocking_time" jdbcType="TIMESTAMP"/>
            <result property="estimatedShippingTime" column="estimated_shipping_time" jdbcType="TIMESTAMP"/>
            <result property="actualShippingTime" column="actual_shipping_time" jdbcType="TIMESTAMP"/>
            <result property="estimatedDeliveryTime" column="estimated_delivery_time" jdbcType="TIMESTAMP"/>
            <result property="actualDeliveryTime" column="actual_delivery_time" jdbcType="TIMESTAMP"/>
            <result property="estimatedReceiptTime" column="estimated_receipt_time" jdbcType="TIMESTAMP"/>
            <result property="actualReceiptTime" column="actual_receipt_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,create_time,update_time,
        is_del,wuliu_id,business_type,
        business_id,estimated_stocking_time,actual_stocking_time,
        estimated_shipping_time,actual_shipping_time,estimated_delivery_time,
        actual_delivery_time,estimated_receipt_time,actual_receipt_time,
        wuliu_related_record_rv
    </sql>
    <select id="getWuLiuRelatedRecordByWuliuId" resultType="com.jiuji.oa.wuliu.entity.WuliuRelatedRecord">
        select top 1
        <include refid="Base_Column_List"/>
        from wuliu_related_record with(nolock)
        where wuliu_id = #{wuliuId}
        order by id desc
    </select>
</mapper>
