<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.wuliu.mapper.WuLiuSubAddressMapper">
    <select id="getSubPaisongOutPush" resultType="com.jiuji.oa.wuliu.vo.req.WuLiuSubAddressVO">
        SELECT
        s.sub_id,
        s.delivery,
        b.psuser,
        b.wuliucompany,
        b.wuliuNo,
        b.paisongState,
        s.trader
        FROM
        dbo.SubAddress b WITH(nolock)
        LEFT JOIN dbo.sub s WITH(nolock) ON
        s.sub_id = b.sub_id
        <where>
            AND s.sub_check IN (2, 6)
            AND s.sub_id = #{subId}
        </where>
    </select>
</mapper>