<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.wuliu.mapper.WuliuAddressMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.wuliu.entity.WuliuAddress">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="wlid" column="wlid" jdbcType="INTEGER"/>
            <result property="sareaid" column="sareaid" jdbcType="INTEGER"/>
            <result property="sname" column="sname" jdbcType="VARCHAR"/>
            <result property="smobile" column="smobile" jdbcType="VARCHAR"/>
            <result property="saddress" column="saddress" jdbcType="VARCHAR"/>
            <result property="rareaid" column="rareaid" jdbcType="INTEGER"/>
            <result property="rname" column="rname" jdbcType="VARCHAR"/>
            <result property="rmobile" column="rmobile" jdbcType="VARCHAR"/>
            <result property="raddress" column="raddress" jdbcType="VARCHAR"/>
            <result property="kdorder" column="kdorder" jdbcType="VARCHAR"/>
            <result property="inuser" column="inuser" jdbcType="VARCHAR"/>
            <result property="addtime" column="addtime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,wlid,sareaid,
        sname,smobile,saddress,
        rareaid,rname,rmobile,
        raddress,kdorder,inuser,
        addtime
    </sql>
    <select id="selectByNu" resultType="com.jiuji.oa.wuliu.entity.WuliuAddress">
        select top 1 * from wuliu_address wa with(nolock)
        where wa.kdorder = #{nu}
    </select>
</mapper>
