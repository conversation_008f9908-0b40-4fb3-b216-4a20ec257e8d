<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.wuliu.mapper.ShansongAppConfigsMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.wuliu.entity.ShansongAppConfigs">
            <id property="id" column="Id" jdbcType="INTEGER"/>
            <result property="clientid" column="ClientId" jdbcType="VARCHAR"/>
            <result property="shopid" column="ShopId" jdbcType="VARCHAR"/>
            <result property="appsecrty" column="AppSecrty" jdbcType="VARCHAR"/>
            <result property="createtime" column="CreateTime" jdbcType="TIMESTAMP"/>
            <result property="serveraddress" column="ServerAddress" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id,ClientId,ShopId,
        AppSecrty,CreateTime,ServerAddress
    </sql>
    <select id="selectShansongAppConfigs" resultMap="BaseResultMap">
        select top 1 ClientId,ShopId,AppSecrty,createtime,ServerAddress from ShansongAppConfigs with(nolock) order by Id desc
    </select>
</mapper>
