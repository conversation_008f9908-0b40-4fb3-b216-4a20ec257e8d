<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.wuliu.mapper.ZtoBillInfoMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.wuliu.entity.ZtoBillInfo">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="wuliuid" column="wuliuid" jdbcType="BIGINT"/>
            <result property="billCode" column="bill_code" jdbcType="VARCHAR"/>
            <result property="bigMark" column="big_mark" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="is_del" jdbcType="BIT"/>
            <result property="wuliuZtoBillInfoRv" column="wuliu_zto_bill_info_rv" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,wuliuid,bill_code,
        big_mark,create_time,update_time,
        is_del,wuliu_zto_bill_info_rv
    </sql>
</mapper>
