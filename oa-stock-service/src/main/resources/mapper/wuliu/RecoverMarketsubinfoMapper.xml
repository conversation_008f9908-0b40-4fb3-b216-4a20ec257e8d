<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.wuliu.mapper.RecoverMarketsubinfoMapper">

    <select id="getSellerInfoBySubId" resultType="com.jiuji.oa.wuliu.bo.SellerInfoBO">
        select top 1 b.sub_id,u.ch999_id ch999UserId,b.seller,u.mobile from recover_marketSubInfo b with(nolock)
        join ch999_user u with(nolock) on b.seller = u.ch999_name
        where isnull(b.isdel,0) = 0
        and b.sub_id = #{subId}
        order by b.ismobile desc
    </select>
</mapper>
