<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.wuliu.mapper.WuliuClaimformMapper">

    <select id="getCurrent" resultType="com.jiuji.oa.wuliu.vo.WuliuClaimformCurrentRes">
        select ISNULL(sum(Amount),0) as CurrentAmount, count(1) as CurrentCount
        from wuliuClaimForm with(nolock)
        where OrderTime>= CAST(DATEADD(MM, DATEDIFF(MM, 0, GETDATE()), 0) AS DATE)
          and Status in (2, 4, 5)
          and CreatorId=#{userId}
    </select>

</mapper>
