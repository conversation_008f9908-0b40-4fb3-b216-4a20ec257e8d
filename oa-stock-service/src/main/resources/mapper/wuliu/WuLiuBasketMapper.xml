<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.wuliu.mapper.WuLiuBasketMapper">

    <select id="getSubBasket" resultType="com.jiuji.oa.wuliu.entity.WuLiuBasket2Entity">
        select bd.deal_time,b.basket_id ,b.basket_count ,b.basket_date ,b.product_peizhi,b.seller,b.ismobile ,
        b.price ,b.sub_id ,b.price1 ,b.ppriceid ,b.inprice ,b.giftid ,b.[type] ,b.isdel ,b.ischu ,b.price2 ,
        b.iskc ,b.isOnShop ,b.return_price ,b.youhuiPrice ,b.jifenPrice ,b.giftPrice,p.productid,product_name,
        p.product_color,p.ppriceid1,p.cid,p.brandID,p.noPromotion,p.cidfamily,p.barCode,p.que,p.isSn,
        p.supportService,p.saleStartTime,p.vendor
        <if test="xcAreaId == null || xcAreaId lte 0">
            ,0 as xcKcShowCount
        </if>
        <if test="xcAreaId > 0">
            ,xck.xcKcShowCount
        </if>
        from basket b with(nolock) left join productinfo p with(nolock) on b.ppriceid=p.ppriceid LEFT JOIN
        dbo.basket_deal bd WITH(NOLOCK) ON bd.basket_id=b.basket_id
        <if test="xcAreaId > 0">
            LEFT JOIN(SELECT d.ppriceid,SUM(d.count_) xcKcShowCount FROM displayProductInfo d with(nolock) WHERE
            d.curAreaId=#{xcAreaId} and d.stats_=1 and isnull(d.isFlaw,0)=1 GROUP BY d.ppriceid) xck ON
            xck.ppriceid=b.ppriceid
        </if>
        where sub_id=#{subId}
        <if test="showDel == 0">
            and isnull(b.isdel,0) = 0
        </if>
        and b.ppriceid not in
        <foreach collection="redPacketPpriceIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        order by b.basket_id
    </select>
    <select id="getSellerInfoBySubId" resultType="com.jiuji.oa.wuliu.bo.SellerInfoBO">
        select top 1 b.sub_id,u.ch999_id ch999UserId,b.seller,u.mobile from basket b with(nolock)
        join ch999_user u with(nolock) on b.seller = u.ch999_name
        where isnull(b.isdel,0) = 0
        and b.sub_id = #{subId}
        order by b.ismobile desc
    </select>
    <select id="getOnlineNationalSupplementStockBySubId"
            resultType="com.jiuji.oa.wuliu.dto.OnlineNationalSupplementStockDTO">
        select top 1 k.imei,k.imei3 as sn,b.ppriceid as ppid,a.xtenant from dbo.basket b with(nolock)
        left join dbo.product_mkc k with(nolock) on k.basket_id = b.basket_id
        left join sub s with(nolock) on s.sub_id = b.sub_id
        left join areainfo a with(nolock) on s.areaid = a.id
        where isnull(b.isdel,0) = 0
        and b.sub_id = #{subId}
        order by b.ismobile desc,b.price desc
    </select>

</mapper>