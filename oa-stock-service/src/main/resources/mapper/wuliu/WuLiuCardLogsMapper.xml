<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.wuliu.mapper.WuLiuCardLogsMapper">


    <select id="getYuyueYouHuiMa" resultType="java.lang.String">
        SELECT TOP 1 n.CardID FROM dbo.cardLogs cl WITH(nolock) LEFT JOIN dbo.NumberCard n WITH(nolock) ON n.id = cl.cardid  WHERE ((n.limitids='23' AND n.State=1 AND n.limitType=1 AND n.ch999_id=-25) OR ( n.ch999_id=-23 )) AND sub_id = #{id}
    </select>

</mapper>