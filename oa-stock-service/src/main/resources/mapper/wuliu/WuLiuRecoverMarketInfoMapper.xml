<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~
  ~      Copyright © 2006 - 2020 九机网 All Rights Reserved
  ~
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.wuliu.mapper.WuLiuRecoverMarketInfoMapper">
    <update id="updateSellerBySubId">
        update recover_marketSubInfo
        set seller=#{userName}
        where seller = '网络'
          and sub_id = #{subId}
          and EXISTS(select *
                     from recover_marketInfo s with(nolock)
                     where sub_check = 0
                       and s.sub_id = recover_marketSubInfo.sub_id
                       and isnull(recover_marketSubInfo.isdel, 0) = 0)

    </update>

    <select id="getRecoverMarketInfo" resultType="com.jiuji.oa.wuliu.entity.WuLiuRecoverMarketInfoEntity">
        SELECT top 1 * FROM dbo.recover_marketInfo s WITH(NOLOCK) WHERE s.sub_id = #{subId} AND s.sub_check IN (0,1,2,5,6,7)
        <if test="areaId != null and areaId > 0">
            and s.areaid = #{areaId}
        </if>
    </select>

    <select id="getSubBasket" resultType="com.jiuji.oa.wuliu.entity.WuLiuRecoverMarketInfo2Entity">
        select p.bpic,k.orderid,b.basket_id,b.basket_count,b.basket_date,b.seller,b.ismobile,b.price,b.sub_id,b.price1,ISNULL(p2.ppriceid,p.ppriceid) ppriceid,b.inprice,b.giftid
             ,b.type,b.isdel,b.ischu,b.price2,b.ishm,b.ksRemark,b.ksfid,b.imei2,b.isOnShop
             ,ISNULL(p2.productid,p.productid) productid,ISNULL(p2.product_name,p.product_name) product_name,ISNULL(p2.product_color,p.product_color) product_color
             ,p.ppriceid1,p.cid,k.comment,k.imei,k.areaid,isnull(k.id,b.mkc_id2) mkc_id,k.mkc_check,k.addprice,k.product_peizhi,k.level_,k.from_basket_id,k.mkcid,k.remark
        from recover_marketSubInfo b with(nolock)
            LEFT join productinfo p with(nolock) on b.ppriceid =p.ppriceid
            LEFT join dbo.recover_mkc k with(nolock) on b.basket_id=k.to_basket_id
            LEFT join productinfo p2 WITH(NOLOCK) on k.ppriceid = p2.ppriceid
        WHERE sub_id=#{subId}
        <if test="showDel == 0">
            and isnull(b.isdel,0) = 0
        </if>
        ORDER by ismobile DESC,b.basket_id ASC
    </select>

    <select id="getBasketId" resultType="java.lang.Integer">
        select top 1 basket_id from recover_marketsubinfo with(nolock) where ismobile = 1 and sub_id = #{danHaoBind}
    </select>
    <select id="selectSubdate" resultType="java.time.LocalDateTime">
     select sub_date
        from recover_marketInfo WITH (NOLOCK)
        where sub_id = #{subId}
          and sub_check = 0
          AND EXISTS(SELECT *
            FROM dbo.recover_marketSubInfo b WITH (NOLOCK)
            WHERE b.seller = '网络'
          AND b.sub_id = sub_id
          and isnull(b.isdel, 0) = 0)
    </select>
    <select id="getSubPositionBySub" resultType="com.jiuji.oa.wuliu.dto.SubPositionDTO">
        select
            top 1
            s.sub_id subId,
            s.userid userId,
            a.[position] addressPosition,
            a.cityid cityId
        from recover_marketInfo s with(nolock)
        left join Addinfo a with(nolock) on s.userid = a.userid
        where s.sub_id = #{req.subId}
        and a.Address = #{req.address}
        order by a.id desc
    </select>
    <select id="getOrderByPayId" resultType="com.jiuji.oa.wuliu.vo.res.WuliuOrderInfoRes">
        select top 1 s.sub_id,
               ai.id aliPayId,
               ai.payType,
               s.sub_check,
               s.userid,
               STUFF((select ';' + convert(nvarchar(50), p.product_name)
                      FROM recover_marketSubInfo b with (nolock)
                   left join productinfo p with(nolock) on b.ppriceid = p.ppriceid
                   WHERE b.sub_id = s.sub_id and isnull(b.isdel,0) = 0
                   FOR XML Path('')), 1, 1, '') as productName,
               s.delivery,
               sa.wuliucompany com,
               sa.wuliuNo nu,
               s.sub_mobile,
               wet.express_receive_time
        from alipayInfo ai with (nolock)
        left join recover_marketInfo s with (nolock) on ai.orderNum = s.sub_id and ai.payType = 3
        left join RecoverSubAddress sa with (nolock) on sa.sub_id = s.sub_id
        left join wuliu_express_time wet with (nolock) on sa.wuliuNo = wet.wuliuNo
        where ai.id = #{payId}
    </select>
    <select id="getOrderByPayDes" resultType="com.jiuji.oa.wuliu.vo.res.WuliuOrderInfoRes">
        select top 1 s.sub_id,
        ai.id aliPayId,
        ai.payType,
        s.sub_check,
        s.userid,
        STUFF((select ';' + convert(nvarchar(50), p.product_name)
        FROM recover_marketSubInfo b with (nolock)
        left join productinfo p with(nolock) on b.ppriceid = p.ppriceid
        WHERE b.sub_id = s.sub_id and isnull(b.isdel,0) = 0
        FOR XML Path('')), 1, 1, '') as productName,
        s.delivery,
        sa.wuliucompany com,
        sa.wuliuNo nu,
        s.sub_mobile,
        wet.express_receive_time
        from alipayInfo ai with (nolock)
        left join recover_marketInfo s with (nolock) on ai.orderNum = s.sub_id and ai.payType = 3
        left join RecoverSubAddress sa with (nolock) on sa.sub_id = s.sub_id
        left join wuliu_express_time wet with (nolock) on sa.wuliuNo = wet.wuliuNo
        where ai.dsc = #{payDes}
    </select>
    <select id="getSubBySubId" resultType="com.jiuji.oa.wuliu.entity.WuLiuRecoverMarketInfoEntity">
        SELECT top 1
            sub_id subId,
            sub_date subDate,
            sub_check subCheck,
            subtype,
            delivery,
            yingfuM AS yingFuMoney,
            yifuM AS yiFuMoney,
            feeM AS feeMoney,
            youhui1M AS youHui1Money,
            shouxuM AS shouXuMoney,
            jidianM AS jiDianMoney,
            tradeDate,
            tradeDate1
        FROM
            recover_marketInfo with(nolock)
        WHERE
            sub_id = #{subId}
    </select>
    <select id="getSubAddressPositionBySubId" resultType="com.jiuji.oa.wuliu.dto.SubPositionDTO">
        SELECT top 1 s.sub_id subId,
        s.userid userId,
        sa.position addressPosition,
        sa.Address address,
        sa.cityid cityId from RecoverSubAddress sa with(nolock)
        inner join recover_marketInfo s with(nolock) on sa.sub_id = s.sub_id
        where s.sub_id = #{req.subId}
        ORDER by sa.sub_id DESC
    </select>


</mapper>
