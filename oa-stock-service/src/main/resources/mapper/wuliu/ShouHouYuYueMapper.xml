<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.wuliu.mapper.ShouHouYuYueMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.wuliu.entity.ShouHouYuYueEntity">
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">

    </sql>
  <select id="getOne" resultType="com.jiuji.oa.wuliu.entity.ShouHouYuYueEntity">
    SELECT y.*, u.userclass
    FROM shouhou_yuyue as y WITH(NOLOCK)
    LEFT JOIN BBSXP_Users as u  WITH(NOLOCK) on y.userid=u.id
    WHERE y.id=#{id}
  </select>
    <select id="getOne2" resultType="com.jiuji.oa.wuliu.entity.ShouHouYuYueEntity2">
        SELECT y.*, u.userclass
        FROM shouhou_yuyue as y WITH(NOLOCK)
        LEFT JOIN BBSXP_Users as u WITH(NOLOCK) on y.userid=u.id
        WHERE y.id = #{id}
    </select>
    <select id="getYuYueAddInfo" resultType="com.jiuji.oa.wuliu.dto.YuYueAddInfoDTO">
        select top 1 y.stype,
               y.stats,
               y.id as yuyueId,
               y.areaid as rareaid,
               y.mobile as smobile,
               a.Reciver as sname,
               a.cityid as scityid,
               a.Address as saddress,
               a2.company_tel1 as rmobile,
               a2.printName as rname,
               a2.cityid as rcityid,
               a2.company_address as raddress
        from shouhou_yuyue y with(nolock)
        left join Addinfops a with(nolock) on a.[type] in (3) and y.id = a.BindId
        left join areainfo a2 with(nolock) on y.areaid = a2.id
        where y.id = #{yuyueId}
    </select>
    <select id="getShouhouAddInfo" resultType="com.jiuji.oa.wuliu.dto.ShouHouAddInfoDTO">
        select top 1 s.stats,
               isnull(s.toareaid,s.areaid) as sareaid,
               s.mobile as rmobile,
               s.truename as rname,
               a.cityid as rcityid,
               a.Address as raddress,
               isnull(a3.company_tel1,a2.company_tel1) as smobile,
               isnull(a3.printName,a2.printName) as sname,
               isnull(a3.cityid,a2.cityid) as scityid,
               isnull(a3.company_address,a2.company_address) as saddress
        from shouhou s with(nolock)
        left join Addinfops a with(nolock) on a.[type] in (2) and s.id = a.BindId
        left join areainfo a2 with(nolock) on s.areaid = a2.id
        left join areainfo a3 with(nolock) on s.toareaid = a3.id
        where s.id = #{shouhouId}
    </select>

</mapper>
