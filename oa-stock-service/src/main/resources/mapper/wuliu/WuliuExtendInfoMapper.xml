<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.wuliu.mapper.WuliuExtendInfoMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.wuliu.entity.WuliuExtendInfo">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="wuliuId" column="wuliu_id" jdbcType="INTEGER"/>
            <result property="sendPosition" column="send_position" jdbcType="VARCHAR"/>
            <result property="receivePosition" column="receive_position" jdbcType="VARCHAR"/>
            <result property="distance" column="distance" jdbcType="DECIMAL"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="deleteFlag" column="is_delete" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,wuliu_id,send_position,
        receive_position,distance,create_time,
        update_time,is_delete
    </sql>
    <select id="queryWuliuExtendInfoByWuliuId" resultType="com.jiuji.oa.wuliu.entity.WuliuExtendInfo">
        select top 1 * from wuliu_extend_info with(nolock)
        where wuliu_id = #{wuliuId}
    </select>
    <select id="queryWuliuByNotDistributionCost" resultType="com.jiuji.oa.wuliu.entity.WuLiuEntity">
        select top 1000 w.id,w.com from wuliu w with(nolock)
        left join wuliu_extend_info wei with(nolock) on w.id = wei.wuliu_id
        where wei.distance is null
        and isnull(w.com,'') in ('uupt','dada','meituan','','paotui','jiujikuaisong','sftc')
        and w.id > #{wuliuId}
        order by w.id desc
    </select>
    <select id="queryWuliuIdByDtime" resultType="java.lang.Integer">
        select max(id) as id from wuliu w with(nolock)
        where w.dtime <![CDATA[ < ]]>  DATEADD(day,#{days},GETDATE())
    </select>
</mapper>
