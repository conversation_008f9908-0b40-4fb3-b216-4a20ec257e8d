<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.wuliu.mapper.PrintCountMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.jiuji.oa.wuliu.entity.PrintCountEntity">
  </resultMap>

  <!-- 通用查询结果列 -->
  <sql id="Base_Column_List">
    id, subid, printcount, printtype
  </sql>
  <select id="getShelvesNumBySubId" resultType="com.jiuji.oa.wuliu.vo.ShelveDTO">
    SELECT a.shelvesNum shelves_num, COUNT(1) cnt
    FROM dbo.RecoverAuction a WITH(NOLOCK)
    INNER JOIN dbo.recover_marketSubInfo b
    WITH (NOLOCK)
    ON a.SubId = b.sub_id
    WHERE a.SubId = #{subId} AND ISNULL(b.isdel, 0) = 0
    GROUP BY a.shelvesNum
  </select>

</mapper>
