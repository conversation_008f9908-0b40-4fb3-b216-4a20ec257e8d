<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.wuliu.mapper.LcExpressOrderMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.wuliu.entity.LcExpressOrder">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="receiverDetailAddress" column="receiver_detail_address" jdbcType="VARCHAR"/>
            <result property="receiverName" column="receiver_name" jdbcType="VARCHAR"/>
            <result property="receiverPhone" column="receiver_phone" jdbcType="VARCHAR"/>
            <result property="senderDetailAddress" column="sender_detail_address" jdbcType="VARCHAR"/>
            <result property="senderName" column="sender_name" jdbcType="VARCHAR"/>
            <result property="senderPhone" column="sender_phone" jdbcType="VARCHAR"/>
            <result property="logisticsType" column="logistics_type" jdbcType="TINYINT"/>
            <result property="logisticsId" column="logistics_id" jdbcType="BIGINT"/>
            <result property="waybillNo" column="waybill_no" jdbcType="VARCHAR"/>
            <result property="deliveryId" column="delivery_id" jdbcType="VARCHAR"/>
            <result property="receiveShopId" column="receive_shop_id" jdbcType="VARCHAR"/>
            <result property="receiveShopName" column="receive_shop_name" jdbcType="VARCHAR"/>
            <result property="sendShopId" column="send_shop_id" jdbcType="VARCHAR"/>
            <result property="sendShopName" column="send_shop_name" jdbcType="VARCHAR"/>
            <result property="expressType" column="express_type" jdbcType="TINYINT"/>
            <result property="xtenant" column="xtenant" jdbcType="BIGINT"/>
            <result property="tenantScale" column="tenant_scale" jdbcType="TINYINT"/>
            <result property="combo" column="combo" jdbcType="VARCHAR"/>
            <result property="comboId" column="combo_id" jdbcType="INTEGER"/>
            <result property="bbsxpUserId" column="bbsxp_user_id" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,receiver_detail_address,receiver_name,
        receiver_phone,sender_detail_address,sender_name,
        sender_phone,logistics_type,logistics_id,
        waybill_no,delivery_id,receive_shop_id,
        receive_shop_name,send_shop_id,send_shop_name,
        express_type,xtenant,tenant_scale,
        combo,combo_id,bbsxp_user_id,
        create_time,update_time
    </sql>
    <select id="selectExpressOrderByWaybillNo" resultType="com.jiuji.oa.wuliu.entity.LcExpressOrder">
        select * from lc_express_order where waybill_no = #{waybillNo}
    </select>
</mapper>
