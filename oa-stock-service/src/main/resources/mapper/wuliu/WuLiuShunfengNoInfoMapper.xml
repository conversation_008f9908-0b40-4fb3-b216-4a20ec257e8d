<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.wuliu.mapper.WuLiuShunfengNoInfoMapper">

  <insert id="addAll" useGeneratedKeys="true">
    insert into shunfengNoInfo (mailno, wuliuid, destRouteLabel, twoDimensionCode, sareaid, custid, adddate, j_mobile, d_mobile, sign_time, ckind)
    values
    <trim suffixOverrides=",">
      <foreach collection="list" item="item">
        (
        #{item.mailNo}, #{item.wuLiuId}, #{item.destRouteLabel}, #{item.twoDimensionCode}, #{item.sareaid}, #{item.custId}
        ,#{item.addDate}, #{item.jMobile}, #{item.dMobile}, #{item.signTime}, #{item.ckind}
        ),
      </foreach>
    </trim>
  </insert>
    <select id="queryByMailNoAndWuliuId" resultType="com.jiuji.oa.wuliu.entity.WuLiuShunfengNoInfoEntity">
      select top 1 *  from shunfengNoInfo sni with(nolock)
      <where>
        <if test="waybillNo != null and waybillNo != ''">
          and sni.mailno = #{waybillNo}
        </if>
        <if test="wuliuId != null">
          and sni.wuliuId = #{wuliuId}
        </if>
      </where>
    </select>

</mapper>
