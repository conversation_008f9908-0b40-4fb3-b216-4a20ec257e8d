<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.wuliu.mapper.WuLiuLogMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.wuliu.entity.WuLiuLogEntity">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="wuliuid" column="wuliuid" jdbcType="INTEGER"/>
            <result property="dtime" column="dtime" jdbcType="TIMESTAMP"/>
            <result property="inuser" column="inuser" jdbcType="VARCHAR"/>
            <result property="msg" column="msg" jdbcType="VARCHAR"/>
            <result property="statistcsCategray" column="statistcs_categray" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,wuliuid,dtime,
        inuser,msg,statistcs_categray
    </sql>

    <insert id="insertLogBatch">
        INSERT INTO wuliu_logs (
        wuliuid,dtime,
        inuser,msg,kind
        )
        VALUES
        <foreach collection="logs" item="item" index="index" separator=",">
            (
            #{item.wuliuid},
            #{item.dtime},
            #{item.inuser},
            #{item.msg},
            #{item.kind}
            )
        </foreach>
    </insert>
</mapper>
