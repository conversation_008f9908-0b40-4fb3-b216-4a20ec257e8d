<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.wuliu.mapper.WuliunoexMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.wuliu.entity.Wuliunoex">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="nu" column="nu" jdbcType="VARCHAR"/>
            <result property="wuliuid" column="wuliuid" jdbcType="INTEGER"/>
            <result property="com" column="com" jdbcType="VARCHAR"/>
            <result property="packagecount" column="packageCount" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,nu,wuliuid,
        com,packageCount
    </sql>
</mapper>
