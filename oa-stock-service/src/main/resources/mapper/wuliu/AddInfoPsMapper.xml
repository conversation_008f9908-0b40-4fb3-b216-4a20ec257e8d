<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.wuliu.mapper.AddInfoPsMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.jiuji.oa.wuliu.entity.AddInfoPsEntity">
    <id column="id" property="id"/>
    <result column="Reciver" property="receiver"/>
    <result column="Address" property="address"/>
    <result column="mobile" property="mobile"/>
    <result column="Tel" property="tel"/>
    <result column="Email" property="email"/>
    <result column="cityid" property="cityId"/>
    <result column="type" property="type"/>
    <result column="BindId" property="bindId"/>
    <result column="psuser" property="psUser"/>
    <result column="Consignee" property="consignee"/>
  </resultMap>

  <!-- 通用查询结果列 -->
  <sql id="Base_Column_List">
    id, Reciver, Address, mobile, Tel, Email, cityid, type, BindId, psuser, Consignee
  </sql>
  <select id="getByShouHouId" resultType="com.jiuji.oa.wuliu.entity.AddInfoPsEntity">
    select top 1 id, Reciver receiver, Address address, mobile, Tel tel, Email email, cityid cityId, [type], BindId bindId, psuser psUser, Consignee consignee from Addinfops with(nolock)
    <where>
      <choose>
        <when test="shouHouId==null || shouHouId==0">
          type in(3,4)
          and BindId=#{yuyueId}
        </when>
        <otherwise>
          type in(1,2)
          and BindId=#{shouHouId}
        </otherwise>
      </choose>
    </where>
    order by type desc
  </select>

</mapper>
