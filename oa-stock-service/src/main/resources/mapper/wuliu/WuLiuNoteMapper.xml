<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.wuliu.mapper.WuLiuNoteMapper">
    <select id="pageList" resultType="com.jiuji.oa.wuliu.dto.res.WuLiuNoteRes">
        SELECT
        temp.id,
        temp.sendName,
        temp.sendMobile,
        temp.sendAddress,
        temp.sendCityId,
        temp.receiveName,
        temp.receiveMobile,
        temp.receiveAddress,
        temp.receiveCityId,
        temp.area,
        temp.registerTime,
        temp.accomplishTime,
        temp.costPrice,
        temp.receiver,
        temp.courier,
        temp.status,
        temp.trackNumBind,
        temp.wuLiuType,
        temp.comment,
        temp.expressCompany,
        temp.trackingNum,
        temp.weight,
        temp.operator,
        temp.linktype,
        temp.sendtime,
        temp.areaid,
        temp.sendAreaId,
        temp.receiveAreaId,
        temp.recipient,
        temp.signTime,
        temp.notifyType,
        temp.orderType,
        temp.pay_method,
        temp.wpid,
        temp.wCateId,
        temp.LastRouteTime,
        temp.EstimatedArrivalTime,
        temp.ProductPriceTotal,
        temp.manuallyCreate,
        temp.sendArea,
        temp.receiveArea,
        temp.overTime,
        temp.distance,
        temp.distributionCost,
        temp.fee as fee1
        from (
        SELECT
         TOP 100 PERCENT ROW_NUMBER ( ) OVER ( ORDER BY w.id DESC ) AS __row_number__,
         w.id,
         w.sname AS sendName,
         w.smobile AS sendMobile,
         w.saddress AS sendAddress,
         w.scityid AS sendCityId,
         w.rname AS receiveName,
         w.rmobile AS receiveMobile,
         w.raddress AS receiveAddress,
         w.rcityid AS receiveCityId,
         w.area,
         w.dtime AS registerTime,
         w.ctime AS accomplishTime,
         w.inprice AS costPrice,
         w.shoujianren AS receiver,
         w.paijianren AS courier,
         w.stats AS status,
         isnull(w.danhaobind,0) AS trackNumBind,
         w.wutype AS wuLiuType,
         ''AS comment,
         w.com AS expressCompany,
         w.nu AS trackingNum,
         w.weight,
         w.inuser AS operator,
         isnull(w.linktype,0) as linktype,
         w.sendtime,
         w.areaid,
         w.sareaid AS sendAreaId,
         w.rareaid AS receiveAreaId,
         w.receiveUser AS recipient,
         w.receiveTime AS signTime,
         w.notifyType,
         w.subKinds AS orderType,
         w.pay_method,
         w.wpid,
         w.wCateId,
         w.LastRouteTime,
         w.EstimatedArrivalTime,
         w.ProductPriceTotal,
         w.isCreateManually AS manuallyCreate,
         sa.area as sendArea,
         case when w.wutype in(4,6,9) then '' else ra.area end receiveArea,
         DATEDIFF(MINUTE,DATEADD(HOUR,1,w.EstimatedArrivalTime),GETDATE())/60 as overTime,
         wei.distance as distance,
         wei.distribution_cost as distributionCost,
         cf.Amount as fee
        FROM wuliu w with(nolock)
        left join wuliuClaimForm cf with(nolock) on cf.wuliuid=w.id
        LEFT JOIN areainfo sa with(nolock) on w.sareaid = sa.id
        LEFT JOIN areainfo ra with(nolock) on w.rareaid = ra.id
        LEFT JOIN wuliu_extend_info wei with(nolock) ON w.id = wei.wuliu_id
        <if test='param.wuLiuType == "4" and param.delivery != null'>
            left join dbo.sub s with(nolock) on (s.sub_id = w.danhaobind and s.delivery = #{param.delivery})
        </if>
        <where>
            1 = 1
            <choose>
                <when test="param.wuLiuType == null">

                </when>
                <when test="param.wuLiuType == 1">
                    and w.wutype in(1,2,3)
                </when>
                <when test='param.wuLiuType == "12"'>
                    and isnull(w.linktype,0)=6 and w.wutype=8 and exists(select 1 from dbo.tax_piao t with(nolock) where
                    t.wuliuid=w.id and t.takeWay=3 )
                </when>
                <otherwise>
                    and w.wutype in(#{param.wuLiuType})
                </otherwise>
            </choose>

            <if test='param.wuLiuType == "4" and param.delivery != null'>
                and s.delivery = 4
            </if>
            <choose>
                <when test="param.optionType == null">

                </when>
                <when test="param.optionType == 14">
                    and exists (select 1 from wuliu_box wb with(nolock) where wb.wuliu_id=w.id and wb.is_del = 0 and wb.box_id = #{param.key})
                </when>
                <when test="param.optionType == 13">
                    and exists (SELECT 1 FROM wuliu_logs wl with(nolock)
                    where w.id = wl.wuliuid
                    and wl.msg like #{param.key}
                    )
                </when>
                <when test="param.optionType == 4">
                    and w.id in (
                    select
                    id
                    from
                    wuliu w with(nolock)
                    where
                    w.nu = #{param.key}
                    UNION ALL
                    select
                    wuliuid
                    from
                    wuliunoex wn WITH(NOLOCK)
                    where
                    wn.nu = #{param.key})
                </when>
                <when test="param.optionType == 1 or param.optionType == 2 or param.optionType == 11">
                    and ${param.keyTypeName} like #{param.key}
                </when>
                <otherwise>
                    and ${param.keyTypeName} = #{param.key}
                </otherwise>
            </choose>
            <choose>
                <when test="param.status != null and param.status != 0">
                    <choose>
                        <when test='param.status == "4"'>
                            and w.stats in (4,6)
                        </when>
                        <otherwise>
                            and ISNULL(w.stats,1) = #{param.status}
                        </otherwise>
                    </choose>
                </when>
                <when test="param.optionType != 3 and param.key != null and param.key != ''">
                    and ISNULL(w.stats,1) &lt;&gt; 5
                </when>
            </choose>
            <if test="param.extend == true">
                <if test="param.dateType == 1">
                    <if test="param.startTime != null and param.endTime != null">
                        and w.dtime between #{param.startTime} and #{param.endTime}
                    </if>
                </if>
                <if test="param.dateType == 2">
                    <if test="param.startTime != null and param.endTime != null">
                        and w.ctime between #{param.startTime} and #{param.endTime}
                    </if>
                </if>
                <if test="param.overTimeHours != null">
                    AND w.EstimatedArrivalTime IS NOT NULL AND w.stats &lt;&gt; 4 AND w.stats &lt;&gt; 5
                    AND w.wutype IN (1,4,6,9) AND DATEDIFF(MINUTE,DATEADD(HOUR,1,w.EstimatedArrivalTime),GETDATE()) &gt;
                    #{param.overTimeHours} * 60
                </if>
                <if test="param.systemCreate == true">
                    and w.inuser = '系统'
                </if>
                <if test="param.systemCreate == false">
                    and w.inuser &lt;&gt; '系统'
                </if>
                <if test="param.attachment != null and param.attachment == true">
                    and exists (SELECT 1 FROM attachments f WITH(nolock)
                    where f.linkedID = w.id
                    and f.type =13
                    )
                </if>
                <if test="param.attachment != null and param.attachment == false">
                    and not exists (SELECT 1 FROM attachments f WITH(nolock)
                    where f.linkedID = w.id
                    and f.type =13
                    )
                </if>
                <if test="param.abnormal != null and param.abnormal == true">
                    and exists (SELECT 1 FROM MarkAbnomalWuliuRecords m with(nolock)
                    where m.WuliuId=w.id
                    and isnull(m.IsDel,0)=0
                    )
                </if>
                <if test="param.abnormal != null and param.abnormal == false">
                    and not exists (SELECT 1 FROM MarkAbnomalWuliuRecords m with(nolock)
                    where m.WuliuId=w.id
                    and isnull(m.IsDel,0)=0
                    )
                </if>
                <if test="param.reimbursement != null">
                    AND cf.STATUS = #{param.reimbursement}
                </if>
            </if>
            <if test="param.sendAreaId != null and param.sendAreaId.size() > 0">
                and w.sareaid in
                <foreach collection="param.sendAreaId" item="sAreaId" index="index" separator="," open="(" close=")">
                    #{sAreaId}
                </foreach>
            </if>
            <if test="param.receiveAreaId != null and param.receiveAreaId.size() > 0">
                and w.rareaid in
                <foreach collection="param.receiveAreaId" item="rAreaId" index="index" separator="," open="(" close=")">
                    #{rAreaId}
                </foreach>
            </if>
            <if test="param.wCateId != null and param.wCateId.size() > 0">
                and w.wCateId in
                <foreach collection="param.wCateId" item="wCateId" index="index" separator="," open="(" close=")">
                    #{wCateId}
                </foreach>
            </if>
            <if test="param.expressComList != null and param.expressComList.size() > 0">
                and w.com in
                <foreach collection="param.expressComList" item="com" index="index" separator="," open="(" close=")">
                    #{com}
                </foreach>
            </if>
        </where>
          ) temp
        WHERE
        __row_number__ BETWEEN #{begin} AND #{end}
    </select>

    <select id="countAll" resultType="java.lang.Integer">
        SELECT COUNT (1)
        FROM
        (select distinct
        w.id
        FROM wuliu w with(nolock)
        left join wuliuClaimForm cf with(nolock) on cf.wuliuid=w.id
        <if test='param.wuLiuType == "4" and param.delivery != null'>
            left join dbo.sub s with(nolock) on (s.sub_id = w.danhaobind and s.delivery = #{param.delivery})
        </if>
        <if test="param.optionType == 13">
            left join wuliu_logs wl with(nolock) on wl.wuliuid =w.id
        </if>
        <if test="param.optionType == 4">
            left join wuliunoex wn with(nolock) on (wn.wuliuid = w.id and wn.nu=#{param.key})
        </if>
        <if test="param.abnormal != null ">
            left join dbo.MarkAbnomalWuliuRecords m with(nolock) on (m.WuliuId=w.id and isnull(m.IsDel,0)=0)
        </if>
        <if test="param.attachment != null">
            left join attachments f WITH(NOLOCK) on (f.linkedID =w.id and f.type =13)
        </if>
        <where>
            1 = 1
            <choose>
                <when test="param.wuLiuType == null">

                </when>
                <when test="param.wuLiuType == 1">
                    and w.wutype in(1,2,3)
                </when>
                <when test='param.wuLiuType == "12"'>
                    and isnull(w.linktype,0)=6 and w.wutype=8 and exists(select 1 from dbo.tax_piao t with(nolock) where
                    t.wuliuid=w.id and t.takeWay=3 )
                </when>
                <otherwise>
                    and w.wutype in(#{param.wuLiuType})
                </otherwise>
            </choose>

            <if test='param.wuLiuType == "4" and param.delivery != null'>
                and s.delivery = 4
            </if>
            <choose>
                <when test="param.optionType == null">

                </when>
                <when test="param.optionType == 14">
                    and exists (select 1 from wuliu_box wb with(nolock) where wb.wuliu_id=w.id and wb.is_del = 0 and wb.box_id = #{param.key})
                </when>
                <when test="param.optionType == 13">
                    and wl.msg like #{param.key}
                </when>
                <when test="param.optionType == 4">
                    and (w.nu=#{param.key})
                </when>
                <when test="param.optionType == 1 or param.optionType == 2 or param.optionType == 11">
                    and ${param.keyTypeName} like #{param.key}
                </when>
                <otherwise>
                    and ${param.keyTypeName} = #{param.key}
                </otherwise>
            </choose>
            <choose>
                <when test="param.status != null and param.status != 0">
                    and w.stats = #{param.status}
                </when>
                <when test="param.optionType != 3 and param.key != null and param.key != ''">
                    and w.stats &lt;&gt; 5
                </when>
            </choose>
            <if test="param.extend == true">
                <if test="param.dateType == 1">
                    <if test="param.startTime != null and param.endTime != null">
                        and w.dtime between #{param.startTime} and #{param.endTime}
                    </if>
                </if>
                <if test="param.dateType == 2">
                    <if test="param.startTime != null and param.endTime != null">
                        and w.ctime between #{param.startTime} and #{param.endTime}
                    </if>
                </if>
                <if test="param.overTimeHours != null">
                    AND w.EstimatedArrivalTime IS NOT NULL AND w.stats &lt;&gt; 4 AND w.stats &lt;&gt; 5
                    AND w.wutype IN (1,4,6,9) AND DATEDIFF(MINUTE,DATEADD(HOUR,1,w.EstimatedArrivalTime),GETDATE()) &gt;
                    #{param.overTimeHours} * 60
                </if>
                <if test="param.systemCreate == true">
                    and w.inuser = '系统'
                </if>
                <if test="param.systemCreate == false">
                    and w.inuser &lt;&gt; '系统'
                </if>
                <if test="param.attachment != null and param.attachment == true">
                    and f.id is not null
                </if>
                <if test="param.attachment != null and param.attachment == false">
                    and f.id is null
                </if>
                <if test="param.abnormal != null and param.abnormal == true">
                    and m.Id is not null
                </if>
                <if test="param.abnormal != null and param.abnormal == false">
                    and m.Id is null
                </if>
                <if test="param.reimbursement != null">
                    AND cf.STATUS = #{param.reimbursement}
                </if>
            </if>
            <if test="param.sendAreaId != null and param.sendAreaId.size() > 0">
                and w.sareaid in
                <foreach collection="param.sendAreaId" item="sAreaId" index="index" separator="," open="(" close=")">
                    #{sAreaId}
                </foreach>
            </if>
            <if test="param.receiveAreaId != null and param.receiveAreaId.size() > 0">
                and w.rareaid in
                <foreach collection="param.receiveAreaId" item="rAreaId" index="index" separator="," open="(" close=")">
                    #{rAreaId}
                </foreach>
            </if>
            <if test="param.wCateId != null and param.wCateId.size() > 0">
                and w.wCateId in
                <foreach collection="param.wCateId" item="wCateId" index="index" separator="," open="(" close=")">
                    #{wCateId}
                </foreach>
            </if>
        </where>
        ) temp
    </select>

    <select id="getWuLiuData" resultType="com.jiuji.oa.wuliu.dto.res.WuLiuNoteRes">
        SELECT w.id, w.sname AS sendName, w.smobile AS sendMobile, w.saddress AS sendAddress,
        w.scityid AS sendCityId, w.rname AS receiveName, w.rmobile AS receiveMobile, w.raddress AS receiveAddress,
        w.rcityid AS receiveCityId, w.area, w.dtime AS registerTime, w.ctime AS accomplishTime,
        w.price as fee, w.inprice AS costPrice, w.shoujianren AS receiver, w.paijianren AS courier, w.stats AS status,
        w.wutype AS wuLiuType, w.comment, w.com AS expressCompany, w.nu AS trackingNum,
        w.weight, w.inuser AS operator, w.linktype, w.sendtime, w.areaid, w.sareaid AS sendAreaId, w.rareaid AS
        receiveAreaId,
        w.receiveUser AS recipient, w.receiveTime AS signTime, w.notifyType, w.subKinds AS orderType, w.pay_method,
        w.wpid, w.wCateId,
        w.LastRouteTime, w.EstimatedArrivalTime, w.ProductPriceTotal, w.isCreateManually AS manuallyCreate,
        a.area as sendArea,
        case when w.wutype in(4,6,9) then '' else d.area end receiveArea,
        DATEDIFF(MINUTE,DATEADD(HOUR,1,w.EstimatedArrivalTime),GETDATE())/60 as overTime,
        b.name AS sendSmallArea,
        c.name AS sendBigArea,
        a.area_name AS sendAreaName ,
        case when w.wutype in(4,6,9) then '' else d.area_name end receiveAreaName,
        case when w.wutype in(4,6,9) then '' else e.name end receiveSmallArea,
        case when w.wutype in(4,6,9) then '' else f.name end receiveBigArea,
        wei.distance as distance,
        wei.distribution_cost as distributionCost,
        cf.Amount as fee1,
        ISNULL(w.danhaobind,0) as trackNumBind,
        (
        SELECT
        dbo.getCityName(w.scityid)) AS sendCityName,
        (
        SELECT
        dbo.getCityName(w.rcityid)) AS receiveCityName
        from
        wuliu w with(nolock)
        left join wuliuClaimForm cf with(nolock) on cf.wuliuid=w.id
        LEFT JOIN dbo.areainfo a with(nolock) ON
        w.sareaid = a.id
        LEFT JOIN dbo.departInfo b with(nolock) ON
        b.code = a.areaCode
        LEFT JOIN dbo.departInfo c with(nolock) ON
        c.code = LEFT(a.areaCode,
        6)
        LEFT JOIN dbo.areainfo d with(nolock) ON
        w.rareaid = d.id
        LEFT JOIN dbo.departInfo e with(nolock) ON
        e.code = d.areaCode
        LEFT JOIN dbo.departInfo f with(nolock) ON
        f.code = LEFT(d.areaCode,
        6)
        LEFT JOIN wuliu_extend_info wei with(nolock) ON w.id = wei.wuliu_id
        <where>
            <choose>
                <when test="param.wuLiuType == null">

                </when>
                <when test="param.wuLiuType == 1">
                    and w.wutype in(1,2,3)
                </when>
                <when test='param.wuLiuType == "12"'>
                    and isnull(w.linktype,0)=6 and w.wutype=8 and exists(select 1 from dbo.tax_piao t with(nolock) where
                    t.wuliuid=w.id and t.takeWay=3 )
                </when>
                <otherwise>
                    and w.wutype in(#{param.wuLiuType})
                </otherwise>
            </choose>

            <if test='param.wuLiuType == "4" and param.delivery != null'>
                and exists(select * from sub s with(nolock) where s.sub_id=w.danhaobind and s.delivery=#{param.delivery} )
            </if>
            <choose>
                <when test="param.optionType == null">

                </when>
                <when test="param.optionType == 14">
                    and exists (select 1 from wuliu_box wb with(nolock) where wb.wuliu_id=w.id and wb.is_del = 0 and wb.box_id = #{param.key})
                </when>
                <when test="param.optionType == 13">
                    and exists(select 1 from wuliu_logs l with(nolock) where l.wuliuid=w.id and l.msg like #{param.key})
                </when>
                <when test="param.optionType == 4">
                    and (nu=#{param.key} or EXISTS (select id from wuliunoex wn WITH(NOLOCK) where wn.wuliuid = w.id AND
                    wn.nu = #{param.key}))
                </when>
                <when test="param.optionType == 1 or param.optionType == 2 or param.optionType == 11">
                    and ${param.keyTypeName} like #{param.key}
                </when>
                <otherwise>
                    and ${param.keyTypeName} = #{param.key}
                </otherwise>
            </choose>
            <choose>
                <when test="param.status != null and param.status != 0">
                    and w.stats = #{param.status}
                </when>
                <when test="param.optionType != 3 and param.key != null and param.key != ''">
                    and w.stats &lt;&gt; 5
                </when>
            </choose>
            <if test="param.extend == true">
                <if test="param.dateType == 1">
                    <if test="param.startTime != null and param.endTime != null">
                        and w.dtime between #{param.startTime} and #{param.endTime}
                    </if>
                </if>
                <if test="param.dateType == 2">
                    <if test="param.startTime != null and param.endTime != null">
                        and w.ctime between #{param.startTime} and #{param.endTime}
                    </if>
                </if>
                <if test="param.overTimeHours != null">
                    AND w.EstimatedArrivalTime IS NOT NULL AND w.stats &lt;&gt; 4 AND w.stats &lt;&gt; 5
                    AND w.wutype IN (1,4,6,9) AND DATEDIFF(MINUTE,DATEADD(HOUR,1,w.EstimatedArrivalTime),GETDATE()) &gt;
                    #{param.overTimeHours} * 60
                </if>
                <if test="param.systemCreate == true">
                    and w.inuser = '系统'
                </if>
                <if test="param.systemCreate == false">
                    and w.inuser &lt;&gt; '系统'
                </if>
                <if test="param.attachment == true">
                    AND EXISTS(SELECT 1 FROM attachments f WITH(NOLOCK) WHERE f.type=13 AND f.linkedID=w.id)
                </if>
                <if test="param.abnormal == true">
                    AND EXISTS(select 1 from dbo.MarkAbnomalWuliuRecords m with(nolock) where WuliuId =w.id and
                    isnull(m.IsDel,0)=0 )
                </if>
                <if test="param.reimbursement != null">
                    AND cf.STATUS = #{param.reimbursement}
                </if>
            </if>
            <if test="param.sendAreaId != null and param.sendAreaId.size() > 0">
                and w.sareaid in
                <foreach collection="param.sendAreaId" item="sAreaId" index="index" separator="," open="(" close=")">
                    #{sAreaId}
                </foreach>
            </if>
            <if test="param.receiveAreaId != null and param.receiveAreaId.size() > 0">
                and w.rareaid in
                <foreach collection="param.receiveAreaId" item="rAreaId" index="index" separator="," open="(" close=")">
                    #{rAreaId}
                </foreach>
            </if>
            <if test="param.wCateId != null and param.wCateId.size() > 0">
                and w.wCateId in
                <foreach collection="param.wCateId" item="wCateId" index="index" separator="," open="(" close=")">
                    #{wCateId}
                </foreach>
            </if>
            <if test="param.expressComList != null and param.expressComList.size() > 0">
                and w.com in
                <foreach collection="param.expressComList" item="com" index="index" separator="," open="(" close=")">
                    #{com}
                </foreach>
            </if>
        </where>
        order by w.id desc
    </select>

    <select id="getWuLiuDataV2" resultType="com.jiuji.oa.wuliu.dto.res.WuLiuNoteRes">
        SELECT
        w.id, w.sname AS sendName, w.smobile AS sendMobile, w.saddress AS sendAddress,
        w.scityid AS sendCityId, w.rname AS receiveName, w.rmobile AS receiveMobile, w.raddress AS receiveAddress,
        w.rcityid AS receiveCityId, w.dtime AS registerTime, w.ctime AS accomplishTime,
        w.price as fee, w.inprice AS costPrice, w.shoujianren AS receiver, w.paijianren AS courier, w.stats AS status,
        w.wutype AS wuLiuType, w.com AS expressCompany, w.nu AS trackingNum,
        w.weight, w.inuser AS operator, w.linktype, w.sendtime, w.areaid, w.sareaid AS sendAreaId, w.rareaid AS
        receiveAreaId,
        w.receiveUser AS recipient, w.receiveTime AS signTime, w.subKinds AS orderType, w.pay_method, w.wCateId,
        a.area as sendArea,
        case when w.wutype in(4,6,9) then '' else d.area end receiveArea,
        DATEDIFF(MINUTE,DATEADD(HOUR,1,w.EstimatedArrivalTime),GETDATE())/60 as overTime,
        a.area_name AS sendAreaName ,
        case when w.wutype in(4,6,9) then '' else d.area_name end receiveAreaName,
        cf.Amount as fee1,
        wei.distance as distance,
        wei.distribution_cost as distributionCost,
        ISNULL(w.danhaobind,0) as trackNumBind
        from
        wuliu w with(nolock)
        left join wuliuClaimForm cf with(nolock) on cf.wuliuid=w.id
        LEFT JOIN dbo.areainfo a with(nolock) ON
        w.sareaid = a.id
        LEFT JOIN dbo.areainfo d with(nolock) ON
        w.rareaid = d.id
        LEFT JOIN wuliu_extend_info wei with(nolock) ON w.id = wei.wuliu_id
        <where>
            <choose>
                <when test="param.wuLiuType == null">

                </when>
                <when test="param.wuLiuType == 1">
                    and w.wutype in(1,2,3)
                </when>
                <when test='param.wuLiuType == "12"'>
                    and isnull(w.linktype,0)=6 and w.wutype=8 and exists(select 1 from dbo.tax_piao t with(nolock) where
                    t.wuliuid=w.id and t.takeWay=3 )
                </when>
                <otherwise>
                    and w.wutype in(#{param.wuLiuType})
                </otherwise>
            </choose>

            <if test='param.wuLiuType == "4" and param.delivery != null'>
                and exists(select * from sub s with(nolock) where s.sub_id=w.danhaobind and s.delivery=#{param.delivery} )
            </if>
            <choose>
                <when test="param.optionType == null">

                </when>
                <when test="param.optionType == 1 and param.key != null and param.key != ''">
                    and w.smobile like #{param.key}
                </when>
                <when test="param.optionType == 2 and param.key != null and param.key != ''">
                    and w.rmobile like #{param.key}
                </when>
                <when test="param.optionType == 3 and param.key != null and param.key != ''">
                    and w.id = #{param.key}
                </when>
                <when test="param.optionType == 4 and param.key != null and param.key != ''">
                    and w.id in (
                    select
                    id
                    from
                    wuliu w with(nolock)
                    where
                    w.nu = #{param.key}
                    UNION ALL
                    select
                    wuliuid
                    from
                    wuliunoex wn WITH(NOLOCK)
                    where
                    wn.nu = #{param.key})
                </when>
                <when test="param.optionType == 5 and param.key != null and param.key != ''" >
                    and w.danhaobind = #{param.key}
                </when>
                <when test="param.optionType == 6 and param.key != null and param.key != ''">
                    and w.sareaid = #{param.key}
                </when>
                <when test="param.optionType == 7 and param.key != null and param.key != ''">
                    and w.rareaid = #{param.key}
                </when>
                <when test="param.optionType == 8 and param.key != null and param.key != ''">
                    and w.receiveUser like #{param.key}
                </when>
                <when test="param.optionType == 9 and param.key != null and param.key != ''">
                    and w.paijianren like #{param.key}
                </when>
                <when test="param.optionType == 10 and param.key != null and param.key != ''">
                    and w.sname like #{param.key}
                </when>
                <when test="param.optionType == 11 and param.key != null and param.key != ''">
                    and w.rname like #{param.key}
                </when>
                <when test="param.optionType == 12 and param.key != null and param.key != ''">
                    and w.comment like #{param.key}
                </when>
                <when test="param.optionType == 13 and param.key != null and param.key != ''">
                    and exists(select 1 from wuliu_logs l with(nolock) where l.wuliuid=w.id and l.msg like #{param.key})
                </when>
                <when test="param.optionType == 14">
                    and exists (select 1 from wuliu_box wb with(nolock) where wb.wuliu_id=w.id and wb.is_del = 0 and wb.box_id = #{param.key})
                </when>
            </choose>
            <choose>
                <when test="param.status != null and param.status != 0">
                    <choose>
                        <when test='param.status == "4"'>
                            and w.stats in (4,6)
                        </when>
                        <otherwise>
                            and w.stats = #{param.status}
                        </otherwise>
                    </choose>
                </when>
                <when test="param.optionType != 3 and param.key != null and param.key != ''">
                    and w.stats &lt;&gt; 5
                </when>
            </choose>
            <if test="param.extend == true">
                <if test="param.dateType == 1">
                    <if test="param.startTime != null and param.endTime != null">
                        and w.dtime between #{param.startTime} and #{param.endTime}
                    </if>
                </if>
                <if test="param.dateType == 2">
                    <if test="param.startTime != null and param.endTime != null">
                        and w.ctime between #{param.startTime} and #{param.endTime}
                    </if>
                </if>
                <if test="param.overTimeHours != null">
                    AND w.EstimatedArrivalTime IS NOT NULL AND w.stats &lt;&gt; 4 AND w.stats &lt;&gt; 5
                    AND w.wutype IN (1,4,6,9) AND DATEDIFF(MINUTE,DATEADD(HOUR,1,w.EstimatedArrivalTime),GETDATE()) &gt;
                    #{param.overTimeHours} * 60
                </if>
                <if test="param.systemCreate == true">
                    and w.inuser = '系统'
                </if>
                <if test="param.systemCreate == false">
                    and w.inuser &lt;&gt; '系统'
                </if>
                <if test="param.attachment == true">
                    AND EXISTS(SELECT 1 FROM attachments f WITH(NOLOCK) WHERE f.type=13 AND f.linkedID=w.id)
                </if>
                <if test="param.abnormal == true">
                    AND EXISTS(select 1 from dbo.MarkAbnomalWuliuRecords m with(nolock) where WuliuId =w.id and
                    isnull(m.IsDel,0)=0 )
                </if>
                <if test="param.reimbursement != null">
                    AND cf.STATUS = #{param.reimbursement}
                </if>
            </if>
            <if test="param.sendAreaId != null and param.sendAreaId.size() > 0">
                and w.sareaid in
                <foreach collection="param.sendAreaId" item="sAreaId" index="index" separator="," open="(" close=")">
                    #{sAreaId}
                </foreach>
            </if>
            <if test="param.receiveAreaId != null and param.receiveAreaId.size() > 0">
                and w.rareaid in
                <foreach collection="param.receiveAreaId" item="rAreaId" index="index" separator="," open="(" close=")">
                    #{rAreaId}
                </foreach>
            </if>
            <if test="param.wCateId != null and param.wCateId.size() > 0">
                and w.wCateId in
                <foreach collection="param.wCateId" item="wCateId" index="index" separator="," open="(" close=")">
                    #{wCateId}
                </foreach>
            </if>
            <if test="param.expressComList != null and param.expressComList.size() > 0">
                and w.com in
                <foreach collection="param.expressComList" item="com" index="index" separator="," open="(" close=")">
                    #{com}
                </foreach>
            </if>
        </where>
        order by w.id desc
    </select>

</mapper>
