<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.wuliu.mapper.WuLiuWuliuIsvstoreMapper">

    <select id="getWuLiuIsvstoreByWuliuId" resultType="com.jiuji.oa.wuliu.entity.WuLiuWuliuIsvstoreEntity">
        SELECT top 1 *
        FROM dbo.wuliu_isvstore w WITH(NOLOCK) INNER JOIN dbo.diaobo_sub d
        WITH (NOLOCK)
        ON d.id=w.linkid
        WHERE d.wuliuid= #{wuliuId}
    </select>

</mapper>