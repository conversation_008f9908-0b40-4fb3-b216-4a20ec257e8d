<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.wuliu.mapper.ExpressTypeMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.jiuji.oa.wuliu.entity.ExpressTypeEntity">
    <id column="id" property="id"/>
    <result column="express_name" property="expressName"/>
    <result column="express_code" property="expressCode"/>
    <result column="express_type" property="expressType"/>
    <result column="status" property="status"/>
    <result column="is_del" property="delFlag"/>
    <result column="is_combo" property="combo"/>
    <result column="rank_order" property="rankOrder"/>
    <result column="create_time" property="createTime"/>
    <result column="update_time" property="updateTime"/>
  </resultMap>

  <!-- 通用查询结果列 -->
  <sql id="Base_Column_List">
    id, express_name, express_code, express_type, status, is_delete, is_combo, create_time, update_time, `rank_order`
  </sql>

</mapper>
