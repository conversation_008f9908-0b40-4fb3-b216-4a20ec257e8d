<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.inventory.mapper.StockInventoryRuleAreaMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.stock.inventory.entity.StockInventoryRuleArea">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="inventoryRuleId" column="inventory_rule_id" jdbcType="BIGINT"/>
            <result property="areaId" column="area_id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="isDelete" column="is_delete" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,inventory_rule_id,area_id,
        create_time,update_time,is_delete
    </sql>
</mapper>
