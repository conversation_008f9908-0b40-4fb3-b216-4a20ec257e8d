<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~
  ~      Copyright © 2006 - 2020 九机网 All Rights Reserved
  ~
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.stock.inventory.mapper.StockInventoryRuleMapper">

  <resultMap id="stockCheckInventoryRuleMap" type="com.jiuji.oa.stock.inventory.entity.StockInventoryRule">
                  <id property="id" column="id"/>
                        <result property="createTime" column="create_time"/>
                        <result property="updateTime" column="update_time"/>
                        <result property="isDel" column="is_del"/>
                        <result property="ruleCode" column="rule_code"/>
                        <result property="inventoryType" column="inventory_type"/>
                        <result property="inventoryWay" column="inventory_way"/>
                        <result property="areaLevel" column="area_level"/>
                        <result property="bigCycle" column="big_cycle"/>
                        <result property="smallCycle" column="small_cycle"/>
                        <result property="touchType" column="touch_type"/>
                        <result property="isMobile" column="is_mobile"/>
                        <result property="isUse" column="is_use"/>

            </resultMap>
    <select id="selectInventoryRuleByPage" resultType="com.jiuji.oa.stock.inventory.vo.StockInventoryRuleVO">
        select r.id,
               r.accessory_labels accessorylabels,
               r.rule_code ruleCode,
               r.accessory_skuIds accessorySkuIds,
               r.inventory_type inventoryType,
               r.inventory_way inventoryWay,
               r.area_level areaLevel,
               r.big_cycle bigCycle,
               r.small_cycle smallCycle,
               r.touch_type touchType,
               r.is_mobile isMobile,
               r.generation_method generationMethod,
               r.generation_method_time generationMethodTime,
               r.accessory_categorys accessoryCategorys,
               r.sku_sort,
               r.stock_type,
               r.inventory_mode,
               r.sku_count_upper_limit,
               r.sku_kc_count_upper_limit,
               r.inventory_change_days,
               r.exclude_trigger_days,
               r.inventory_not_change_days,
               r.product_type productType,
               r.statistics_label statisticsLabelStr,
               r.is_use isUse
        from stock_inventory_rule r
        <where>
            and r.is_del = 0
            <if test="param.id != null">
                and r.id = #{param.id}
            </if>
            <choose>
                <when test="param.sourceType == 1">
                    and r.source_type = 1
                </when>
                <when test="param.sourceType == 99">
                    and r.source_type is null
                </when>
            </choose>
            <if test="param.generationMethod != null">
                and r.generation_method = {#param.generationMethod}
            </if>
            <if test="param.productType !=null ">
                and r.product_type = #{param.productType}
            </if>
            <if test="param.generationMethodTime != null  and param.generationMethodTime != ''">
                and r.generation_method_time = {#generationMethodTime}
            </if>
            <if test="param.ruleCode != null and param.ruleCode != '' ">
                and r.rule_code = #{param.ruleCode}
            </if>
            <if test="param.inventoryType != null">
                and r.inventory_type = #{param.inventoryType}
            </if>
            <if test="param.status != null">
                and r.is_use = #{param.status}
            </if>
            <if test="param.areaLevel != null">
                and r.area_level = #{param.areaLevel}
            </if>
            <if test="param.ruleIdList != null and param.ruleIdList.size > 0">
                and r.id in
                <foreach collection="param.ruleIdList" item="it" separator="," open="(" close=")" >
                    #{it}
                </foreach>
            </if>
            order by  create_time desc
        </where>
    </select>
    <select id="selectRuleByPlanId" resultType="com.jiuji.oa.stock.inventory.entity.StockInventoryRule">
        select rule.*
        from stock_inventory_plan plan
                 left join stock_inventory_rule rule on rule.id = plan.inventory_rule_id
        where plan.id= #{planId}
    </select>

</mapper>
