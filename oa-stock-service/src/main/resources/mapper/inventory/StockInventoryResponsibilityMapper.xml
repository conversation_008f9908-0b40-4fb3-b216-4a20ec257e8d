<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~
  ~      Copyright © 2006 - 2020 九机网 All Rights Reserved
  ~
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.stock.inventory.mapper.StockInventoryResponsibilityMapper">

  <resultMap id="stockCheckInventoryResponsibilityMap" type="com.jiuji.oa.stock.inventory.entity.StockInventoryResponsibility">
                  <id property="id" column="id"/>
                        <result property="createTime" column="create_time"/>
                        <result property="updateTime" column="update_time"/>
                        <result property="isDel" column="is_del"/>
                        <result property="areaId" column="area_id"/>
                        <result property="inventoryPlanId" column="inventory_plane_id"/>
                        <result property="inventoryPlanName" column="inventory_plane_name"/>
                        <result property="skuId" column="sku_id"/>
                        <result property="differnetCount" column="differnet_count"/>
                        <result property="differnetReason" column="differnet_reason"/>
                        <result property="responsibleDepartment" column="responsible_department"/>
                        <result property="inventoryState" column="inventory_state"/>
                        <result property="responsibleTime" column="responsible_time"/>
                        <result property="inventoryDetailedId" column="inventory_detailed_id"/>
                        <result property="purchaseId" column="purchase_id"/>
                        <result property="loseReportId" column="lose_report_id"/>
            </resultMap>
</mapper>
