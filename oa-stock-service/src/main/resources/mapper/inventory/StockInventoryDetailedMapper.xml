<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~
  ~      Copyright © 2006 - 2020 九机网 All Rights Reserved
  ~
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.stock.inventory.mapper.StockInventoryDetailedMapper">

  <resultMap id="stockCheckInventoryDetailedMap" type="com.jiuji.oa.stock.inventory.entity.StockInventoryDetailed">
                  <id property="id" column="id"/>
                        <result property="createTime" column="create_time"/>
                        <result property="updateTime" column="update_time"/>
                        <result property="isDel" column="is_del"/>
                        <result property="warehouseName" column="warehouse_name"/>
                        <result property="areaId" column="area_id"/>
                        <result property="inventoryPlanId" column="inventory_plan_id"/>
                        <result property="inventoryPlanName" column="inventory_plan_name"/>
                        <result property="position" column="position"/>
                        <result property="count" column="count"/>
                        <result property="inventorCount" column="inventor_count"/>
                        <result property="haveDifference" column="have_difference"/>
                        <result property="differenceCount" column="difference_count"/>
                        <result property="inventoryUser" column="inventory_user"/>
                        <result property="inventoryTime" column="inventory_time"/>
                        <result property="isMobile" column="is_mobile"/>
                        <result property="imei" column="imei"/>
                        <result property="skuId" column="sku_id"/>
                        <result property="dutyState" column="duty_state"/>
            </resultMap>
    <select id="getDifferenceDetailByPlan" resultType="com.jiuji.oa.stock.inventory.vo.DifferenceDetailVo">
        SELECT
            detail.id,
            detail.area_id AS areaId,
            detail.inventory_plan_name AS inventoryPlanName,
            detail.inventory_user AS inventoryUser,
            detail.sku_id AS skuId,
            detail.inventor_count AS inventorCount,
            detail.difference_count AS differenceCount,
            detail.review_different_count AS reviewDifferentCount,
            res.responsible_department AS responsibleDepartment,
            res.purchase_id,
            res.lose_report_id,
            detail.count,
            res.differnet_reason AS differenceReason,
            res.responsible_time AS responsibleTime
        FROM
        stock_inventory_detailed detail
        LEFT JOIN stock_inventory_responsibility res ON detail.id = res.inventory_detailed_id
        WHERE
        detail.have_difference = 0
        AND detail.inventory_plan_id = #{planId}
    </select>
    <select id="getDifferenceDetailByPlanV2" resultType="com.jiuji.oa.stock.inventory.vo.DifferenceDetailVo">
        SELECT
            detail.id,
            detail.area_id AS areaId,
            detail.inventory_plan_name AS inventoryPlanName,
            detail.inventory_user AS inventoryUser,
            detail.sku_id AS skuId,
            detail.inventory_time AS inventoryTime,
            detail.inventor_count AS inventorCount,
            detail.inventory_plan_id AS planId,
            detail.display_id AS displayId,
            detail.difference_count AS differenceCount,
            detail.review_different_count AS reviewDifferentCount,
            res.responsible_department AS responsibleDepartment,
            res.purchase_id,
            res.lose_report_id,
            detail.count,
            ifnull(res.differnet_reason,detail.differnet_reason) AS differenceReason,
            res.responsible_time AS responsibleTime,
            detail.check_people AS checkPeople,
            res.id as responsibilityId
        FROM
            stock_inventory_detailed detail
            LEFT JOIN stock_inventory_responsibility res ON detail.id = res.inventory_detailed_id
        ${ew.customSqlSegment}
    </select>
    <select id="getInventoryDetailV2" resultType="com.jiuji.oa.stock.inventory.vo.InventoryDetailPage">
        select sku_id as ppid, inventor_count as inventoryCount, count as productKcCount, inventory_time, display_id
        from stock_inventory_detailed ${ew.customSqlSegment}
    </select>
    <select id="getCidByPlanId" resultType="java.lang.Integer">
        select ca.category_id
        from stock_inventory_plan plan
                 left join stock_inventory_rule_category ca on ca.inventory_rule_id = plan.inventory_rule_id
        where plan.id=#{planId}
    </select>
    <select id="getSmallXcCountByAreaAndSku" resultType="com.jiuji.oa.stock.productkc.entity.ProductKc">
        select d.count_ lcount, d.id as id, d.ppriceid as ppriceid
        from displayProductInfo d with(nolock)
        left join dbo.productinfo p
        with (nolock)
        on d.ppriceid=p.ppriceid
        <where>p.ismobile1=0
            and d.stats_!=2
            and d.stats_= 1
            and isnull(d.isFlaw
            , 0)= 1
            and d.curAreaId =#{areaId}
            and d.ppriceid in
            <foreach collection="skuIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </select>
    <select id="getSmallDcCountByAreaAndSku" resultType="com.jiuji.oa.stock.productkc.entity.ProductKc">
        select dppr.relation_id as id,dppr.ppid as ppriceid,1 as lcount
        from dp_photo_product_relation dppr
        <where>
            dppr.type = 1 and dppr.status = 1
            and dppr.area_id =#{areaId}
            and dppr.ppid in
            <foreach collection="skuIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </select>
</mapper>
