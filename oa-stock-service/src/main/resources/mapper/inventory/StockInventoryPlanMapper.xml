<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~
  ~      Copyright © 2006 - 2020 九机网 All Rights Reserved
  ~
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.stock.inventory.mapper.StockInventoryPlanMapper">

  <resultMap id="stockCheckInventoryPlanMap" type="com.jiuji.oa.stock.inventory.entity.StockInventoryPlan">
                  <id property="id" column="id"/>
                        <result property="createTime" column="create_time"/>
                        <result property="updateTime" column="update_time"/>
                        <result property="isDel" column="is_del"/>
                        <result property="areaId" column="area_id"/>
                        <result property="inventoryPlanName" column="inventory_plan_name"/>
                        <result property="state" column="state"/>
                        <result property="inventoryUser" column="inventory_user"/>
                        <result property="inventoryTime" column="inventory_time"/>
                        <result property="planRank" column="plan_rank"/>
                        <result property="showFlag" column="show_flag"/>
                        <result property="inventoryRuleId" column="inventory_rule_id"/>
                        <result property="inventoryType" column="inventory_type"/>
                        <result property="inventoryWay" column="inventory_way"/>
                        <result property="isMobile" column="is_mobile"/>
                        <result property="areaLevel" column="area_level"/>
                        <result property="ruleCode" column="rule_code"/>
            </resultMap>
    <update id="updateStateById">
        update stock_inventory_plan set state= #{state} where is_del=0 and  id=#{id}
    </update>

    <select id="inventoryPlanPage" resultType="com.jiuji.oa.stock.inventory.vo.InventoryPlanVo">
        SELECT
            plan.id,
            plan.area_id as areaId,
            plan.inventory_plan_name as inventoryPlanName,
            plan.state as state,
            plan.inventory_user as inventoryUser,
            plan.create_time as createTime,
            plan.inventory_time as inventoryTime,
            plan.inventory_rule_id as inventoryRuleId,
            plan.inventory_type as inventoryType,
            plan.area_level as areaLevel,
            plan.rule_code as ruleCode
        FROM
            stock_inventory_plan plan
        <where>
            plan.show_flag = 1
            <if test="req.getPlanName() != null and req.getPlanName() != ''">
                AND plan.inventory_plan_name LIKE CONCAT('%',#{req.planName},'%')
            </if>
            <if test="req.getInventoryTimeBegin() != null and req.getInventoryTimeEnd() != null">
                AND plan.inventory_time BETWEEN #{req.inventoryTimeBegin} AND #{req.inventoryTimeEnd}
            </if>
            <if test="req.getCreateTimeBegin() != null and req.getCreateTimeEnd() != null">
                AND plan.create_time BETWEEN #{req.createTimeBegin} AND #{req.createTimeEnd}
            </if>
            <if test="req.getCreateTimeBegin() != null and req.getCreateTimeEnd() != null">
                AND plan.create_time BETWEEN #{req.createTimeBegin} AND #{req.createTimeEnd}
            </if>
            <if test="req.getState() != null">
                AND plan.state = #{req.state}
            </if>
            <if test="req.getAreaId() != null">
                AND plan.area_id = #{req.areaId}
            </if>
            <if test="req.getAreaIdList() != null and req.getAreaIdList().size()>0">
                AND plan.area_id in
                <foreach item="item" index="index" collection="req.areaIdList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="req.getHaveDifference() != null and req.getHaveDifference() == 1">
                AND plan.state = 3
                AND (select
                count(1)
                from
                stock_inventory_detailed detail
                where
                detail.inventory_plan_id = plan.id
                and detail.have_difference = 0) = 0
            </if>
            <if test="req.getHaveDifference() != null and req.getHaveDifference() == 0">
                AND exists(
                select
                detail.id
                from
                stock_inventory_detailed detail
                where
                detail.inventory_plan_id = plan.id
                and detail.have_difference = #{req.haveDifference}
                )
            </if>
            AND plan.is_del = 0
        </where>
    </select>
    <select id="pageDifferent" resultType="com.jiuji.oa.stock.inventory.vo.ShowPageDifferentVo">
        select distinct
               plan.id as id,
               plan.area_id as area_id ,
               plan.inventory_plan_name as inventory_plan_name ,
               plan.create_time as create_time,
               plan.responsible_time as responsible_time ,
               plan.state as state,
               rule.inventory_type,
               plan.is_del as is_del
        from stock_inventory_plan plan
            left join stock_inventory_detailed detail  on plan.id = detail.inventory_plan_id
            left join stock_inventory_rule rule on rule.id=plan.inventory_rule_id
        ${ew.customSqlSegment}
    </select>

    <select id="inventoryPlanPageV2" resultType="com.jiuji.oa.stock.inventory.vo.InventoryPlanVo">
        SELECT
        plan.id,
        plan.area_id as areaId,
        plan.inventory_plan_name as inventoryPlanName,
        plan.state as state,
        plan.inventory_user as inventoryUser,
        plan.create_time as createTime,
        plan.inventory_time as inventoryTime,
        plan.inventory_rule_id as inventoryRuleId,
        plan.inventory_type as inventoryType,
        plan.area_level as areaLevel,
        plan.rule_code as ruleCode
        FROM
        stock_inventory_plan plan
        left join stock_inventory_rule rule on rule.id=plan.inventory_rule_id
        <where>
            plan.show_flag = 1
            <if test="req.getPlanName() != null and req.getPlanName() != ''">
                AND plan.inventory_plan_name LIKE CONCAT('%',#{req.planName},'%')
            </if>
            <if test="req.getProductType() != null">
                AND rule.product_type = #{req.productType}
            </if>
            <if test="req.getInventoryType() !=null">
                AND rule.inventory_type=#{req.inventoryType}
            </if>
            <if test="req.getInventoryTimeBegin() != null and req.getInventoryTimeEnd() != null">
                AND plan.inventory_time BETWEEN #{req.inventoryTimeBegin} AND #{req.inventoryTimeEnd}
            </if>
            <if test="req.getCreateTimeBegin() != null and req.getCreateTimeEnd() != null">
                AND plan.create_time BETWEEN #{req.createTimeBegin} AND #{req.createTimeEnd}
            </if>
            <if test="req.getCreateTimeBegin() != null and req.getCreateTimeEnd() != null">
                AND plan.create_time BETWEEN #{req.createTimeBegin} AND #{req.createTimeEnd}
            </if>
            <if test="req.getState() != null and req.getState().size() > 0">
                AND plan.state in
                <foreach collection="req.getState()" separator="," open="(" close=")" index="index" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="req.getAreaId() != null">
                AND plan.area_id = #{req.areaId}
            </if>
            <if test="req.getAreaIdList() != null and req.getAreaIdList().size()>0">
                AND plan.area_id in
                <foreach item="item" index="index" collection="req.areaIdList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="req.getHaveDifference() != null and req.getHaveDifference() == 1">
                AND plan.state = 3
                AND (select
                count(1)
                from
                stock_inventory_detailed detail
                where
                detail.inventory_plan_id = plan.id
                and detail.have_difference = 0) = 0
            </if>
            <if test="req.getHaveDifference() != null and req.getHaveDifference() == 0">
                AND exists(
                select
                detail.id
                from
                stock_inventory_detailed detail
                where
                detail.inventory_plan_id = plan.id
                and detail.have_difference = #{req.haveDifference}
                )
            </if>
            AND plan.is_del = 0
        </where>
    </select>
    <select id="getUndeterminedResponsibilityPlan" resultType="java.lang.String">
        select plan.inventory_plan_name from stock_inventory_plan plan
        where plan.state = 3
          and plan.create_time &gt; '2022-05-18 23:59:59'
          and EXISTS(select 1
                     from stock_inventory_detailed detailed
                     where detailed.duty_state = 2
                       and detailed.have_difference = 0
                       and plan.id = detailed.inventory_plan_id)
        <if test="responsibilityPlanVo.getPlanIds() != null and responsibilityPlanVo.getPlanIds().size() > 0">
            and plan.id in ()
            <foreach collection="responsibilityPlanVo.getPlanIds()" separator="," open="(" close=")" index="index" item="item">
                #{item}
            </foreach>
        </if>
        <if test="responsibilityPlanVo.areaId!= null ">
            AND plan.area_id=#{responsibilityPlanVo.areaId}
        </if>
        <if test="responsibilityPlanVo.productType!= null ">
            and EXISTS(select 1 from stock_inventory_rule rule where rule.id = plan.inventory_rule_id and rule.product_type = #{responsibilityPlanVo.productType} )

        </if>

    </select>
    <select id="queryPlan" resultType="com.jiuji.oa.stock.inventory.vo.InventoryPlanAppNewVo">
        select plan.id,
               plan.inventory_plan_name,
               rule.inventory_way,
               rule.product_type,
               rule.inventory_mode,
               plan.inventory_user,
               rule.inventory_type
        from stock_inventory_plan plan
                 left join stock_inventory_rule rule on rule.id = plan.inventory_rule_id
        where plan.state in (1,2)
           and plan.is_del=0
        <if test="req.areaId != null">
           and plan.area_id = #{req.areaId}
        </if>
        <if test="req.oldFlag == true">
           and rule.inventory_type in (1,2,3,4,5)
        </if>
        <if test="req.inventoryPlanName !=null and req.inventoryPlanName !='' ">
            AND plan.inventory_plan_name LIKE CONCAT('%',#{req.inventoryPlanName},'%')
        </if>
        order by state desc
    </select>

</mapper>
