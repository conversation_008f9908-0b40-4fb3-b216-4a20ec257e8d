<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.accessory.mapper.WuliuCalculateStatisticsSpecMapper">
    <delete id="deleteSpec">
        DELETE FROM wuliu_calculate_statistics_spec WHERE id in
        <foreach collection="idList" item="it" separator="," open="(" close=")">
            #{it}
        </foreach>
    </delete>

    <update id="createTemporaryWl">
        select *
        into #temporaryWl
        from dbo.wuliu_logs l with(nolock)
        where l.dtime between #{param.startTime}
          and #{param.endTime}
    </update>

    <update id="createTemporaryDbs">
        select s.*
        into #temporaryDbs
        from dbo.diaobo_sub s with(nolock)
        where s.stats IN (3, 4, 5, 6)
    </update>

    <select id="wuliuCalculateStatisticsV2"
            resultType="com.jiuji.oa.stock.accessory.vo.res.WuliuCalculateStatisticsOutputRes">
        select *
        into #temporaryWl
        from dbo.wuliu_logs l with(nolock)
        where l.dtime between #{param.startTime}
        and #{param.endTime};
        CREATE NONCLUSTERED INDEX NonClusteredIndex_temporaryWl ON #temporaryWl (  wuliuid ASC  )
        WITH (  PAD_INDEX = OFF ,FILLFACTOR = 100  ,SORT_IN_TEMPDB = OFF , IGNORE_DUP_KEY = OFF , STATISTICS_NORECOMPUTE = OFF , ONLINE = OFF , ALLOW_ROW_LOCKS = ON , ALLOW_PAGE_LOCKS = ON  )
        ON [PRIMARY ] ;
        select * from(
        select
        row_number ( ) OVER ( ORDER BY ISNULL(t4.cnt,0) DESC,u.ch999_id ASC ) AS rownumber,
        u.ch999_id,u.ch999_name,a.area,ISNULL(t1.cnt,0) dajianrkl,ISNULL(t2.cnt,0) peijianrkl,ISNULL(t3.cnt,0) liangpinrkl,ISNULL(t4.cnt,0) dajianshl,ISNULL(t5.cnt,0) peijianshl
        ,ISNULL(t6.cnt,0) peijianxhjhl,ISNULL(t7.cnt,0) peijiandhjhl,ISNULL(t8.cnt,0) dajianjhl,ISNULL(t9.cnt,0) liangpinjhl,ISNULL(t10.cnt,0) kuaidiysl
        ,ISNULL(t11.cnt,0) daifasml,ISNULL(t12.cnt,0) fhsml,ISNULL(t13.cnt,0) wxpeijianrkl,ISNULL(t14.cnt,0) wxpeijianshl,ISNULL(t15.cnt,0) wxpeijianxhjhl,ISNULL(t16.cnt,0) wxpeijiandhjhl
        FROM dbo.ch999_user u WITH(NOLOCK)
        INNER JOIN dbo.areainfo a WITH(NOLOCK) ON a.id = u.area1id
        LEFT JOIN
        (
        SELECT k.imeiuser,COUNT(1) cnt FROM dbo.product_mkc k WITH(NOLOCK)
        WHERE k.imeidate BETWEEN #{param.startTime} and #{param.endTime}
        GROUP BY k.imeiuser
        ) t1 ON t1.imeiuser = u.ch999_name
        LEFT JOIN
        (
        SELECT temp.inuser,SUM(temp.cnt) as cnt FROM( SELECT s.inuser,SUM(CASE WHEN pf.cid in (485) THEN b.lcount WHEN isnull(pf.otherLimit,0) &amp; 2 = 2 THEN b.lcount
        * 1 ELSE b.lcount END)/isnull(spe.spec,1) cnt FROM dbo.caigouInputSub s WITH(NOLOCK)
        INNER JOIN dbo.caigouInputBasket b WITH(NOLOCK) ON s.id = b.inputId
        INNER JOIN dbo.productinfo pf WITH(NOLOCK) ON pf.ppriceid = b.ppriceid
        inner join dbo.caigou_sub ss with(nolock) on ss.id= s.caigouId
        left join dbo.wuliu_calculate_statistics_spec spe with(nolock) on spe.ppriceid= pf.ppriceid
        WHERE s.dtime BETWEEN #{param.startTime} and #{param.endTime} and ss.kinds='pj'
        GROUP BY s.inuser,spe.spec )temp GROUP BY temp.inuser
        ) t2 ON t2.inuser = u.ch999_name
        LEFT JOIN
        (
        SELECT t.reciveuser,COUNT(1) cnt FROM dbo.recover_toarea t WITH(NOLOCK)
        WHERE t.reciveuser IS NOT NULL AND t.recivedtime BETWEEN #{param.startTime} and #{param.endTime}
        GROUP BY t.reciveuser
        ) t3 ON t3.reciveuser = u.ch999_name
        LEFT JOIN
        (
        SELECT t.reciveuser,COUNT(1) cnt FROM dbo.mkc_toarea t WITH(NOLOCK)
        WHERE t.reciveuser IS NOT NULL AND t.recivedtime BETWEEN #{param.startTime} and #{param.endTime}
        GROUP BY t.reciveuser
        ) t4 ON t4.reciveuser = u.ch999_name
        LEFT JOIN
        (
        SELECT temp.inuser,SUM(temp.cnt) as cnt FROM( SELECT c.inuser,SUM(CASE WHEN pf.cid in (485) THEN b.lcount WHEN
        isnull(pf.otherLimit,0) &amp; 2 = 2 THEN b.lcount
        * 1 ELSE b.lcount END)/isnull(spe.spec,1) cnt
        from dbo.diaobo_sub s WITH(NOLOCK)
        INNER JOIN dbo.diaobo_basket b WITH(NOLOCK) ON s.id = b.sub_id
        INNER JOIN (
        SELECT * FROM
        (
        SELECT c.db_id,c.inuser,c.dtime,ROW_NUMBER() OVER(PARTITION BY c.db_id ORDER BY c.id DESC) rn
        FROM dbo.diaoboSub_comment c WITH(NOLOCK)
        WHERE c.operationKinds = 4
        ) t WHERE t.rn = 1
        ) c ON s.id = c.db_id
        left join dbo.productinfo pf with(nolock) on pf.ppriceid = b.ppriceid
        left join dbo.wuliu_calculate_statistics_spec spe with(nolock) on spe.ppriceid= pf.ppriceid
        WHERE s.stats = 4 AND c.dtime BETWEEN #{param.startTime} and #{param.endTime} and s.kinds = 'pj'
        GROUP BY c.inuser,spe.spec,pf.ppriceid )temp GROUP BY temp.inuser
        ) t5 ON t5.inuser = u.ch999_name
        LEFT JOIN
        (
        SELECT temp.inuser,SUM(temp.cnt) as cnt FROM( SELECT c.inuser,SUM(CASE WHEN pf.cid in (485) THEN b.lcount
        WHEN isnull(pf.otherLimit,0) &amp; 2 = 2 THEN b.lcount * 1 ELSE b.lcount END)/isnull(spe.spec,1) cnt
        from dbo.diaobo_sub s WITH(NOLOCK)
        INNER JOIN dbo.diaobo_basket b WITH(NOLOCK) ON s.id = b.sub_id
        INNER JOIN (
        SELECT * FROM
        (
        SELECT c.db_id,c.inuser,c.dtime,ROW_NUMBER() OVER(PARTITION BY c.db_id ORDER BY c.id DESC) rn
        FROM dbo.diaoboSub_comment c WITH(NOLOCK)
        WHERE c.operationKinds = 6
        ) t WHERE t.rn = 1
        ) c ON s.id = c.db_id
        left join dbo.productinfo pf with(nolock) on pf.ppriceid = b.ppriceid
        left join dbo.wuliu_calculate_statistics_spec spe with(nolock) on spe.ppriceid= pf.ppriceid
        WHERE s.stats IN (3,4,5,6) AND s.kinds = 'pj' AND c.dtime BETWEEN #{param.startTime} and #{param.endTime} AND
        b.basket_id IS NULL
        GROUP BY c.inuser,spe.spec,pf.ppriceid )temp GROUP BY temp.inuser
        ) t6 ON t6.inuser = u.ch999_name
        LEFT JOIN
        (
        SELECT temp.inuser,SUM(temp.cnt) as cnt FROM(SELECT c.inuser,SUM(CASE WHEN pf.cid in (485) THEN b.lcount ELSE b.lcount
        END)/isnull(spe.spec,1) cnt
        from dbo.diaobo_sub s WITH(NOLOCK)
        INNER JOIN dbo.diaobo_basket b WITH(NOLOCK) ON s.id = b.sub_id
        INNER JOIN (
        SELECT * FROM
        (
        SELECT c.db_id,c.inuser,c.dtime,ROW_NUMBER() OVER(PARTITION BY c.db_id ORDER BY c.id DESC) rn
        FROM dbo.diaoboSub_comment c WITH(NOLOCK)
        WHERE c.operationKinds = 6
        ) t WHERE t.rn = 1
        ) c ON s.id = c.db_id
        left join dbo.productinfo pf with(nolock) on pf.ppriceid = b.ppriceid
        left join dbo.wuliu_calculate_statistics_spec spe with(nolock) on spe.ppriceid= pf.ppriceid
        WHERE s.stats IN (3,4,5,6) AND s.kinds = 'pj' AND c.dtime BETWEEN #{param.startTime} and #{param.endTime} AND
        b.basket_id IS NOT NULL
        GROUP BY c.inuser,spe.spec,pf.ppriceid )temp GROUP BY temp.inuser
        ) t7 ON t7.inuser = u.ch999_name
        LEFT JOIN
        (
        SELECT t.senduser,COUNT(1) cnt FROM dbo.mkc_toarea t WITH(NOLOCK)
        WHERE t.senduser IS NOT NULL AND t.sendtime BETWEEN #{param.startTime} and #{param.endTime}
        GROUP BY t.senduser
        ) t8 ON t8.senduser = u.ch999_name
        LEFT JOIN
        (
        SELECT t.senduser,COUNT(1) cnt FROM dbo.recover_toarea t WITH(NOLOCK)
        WHERE t.senduser IS NOT NULL AND t.sendtime BETWEEN #{param.startTime} and #{param.endTime}
        GROUP BY t.senduser
        ) t9 ON t9.senduser = u.ch999_name
        LEFT JOIN
        (
        SELECT t.inuser,sum(basket_count) cnt FROM
        (
        SELECT t.inuser,s.sub_id,t.subType,sb.basket_count/isnull(spe.spec,1) as basket_count FROM dbo.subConfirmRecord t WITH(NOLOCK)
        INNER JOIN dbo.sub s WITH(NOLOCK) ON s.sub_id = t.sub_id
        left join dbo.basket sb with(nolock) on sb.sub_id =s.sub_id
        left join dbo.wuliu_calculate_statistics_spec spe with(nolock) on spe.ppriceid= sb.ppriceid
        WHERE t.subType = 1 AND t.operationKinds = 2 AND t.dtime BETWEEN #{param.startTime} and #{param.endTime} AND
        s.delivery = 4 and isnull(sb.isdel,0)=0
        UNION ALL
        SELECT t.inuser,s.sub_id,t.subType,rb.count/isnull(spe.spec,1) as basket_count FROM dbo.subConfirmRecord t WITH(NOLOCK)
        INNER JOIN dbo.recover_marketInfo s WITH(NOLOCK) ON s.sub_id = t.sub_id
        left join dbo.recover_basket rb with(nolock) on rb.sub_id =s.sub_id
        left join dbo.wuliu_calculate_statistics_spec spe with(nolock) on spe.ppriceid= rb.ppriceid
        WHERE t.subType = 2 AND t.operationKinds = 2 AND t.dtime BETWEEN #{param.startTime} and #{param.endTime} AND
        s.delivery = 4 and isnull(rb.isdel,0)=0
        ) t
        GROUP BY t.inuser
        ) t10 ON t10.inuser = u.ch999_name
        LEFT JOIN
        (
        SELECT t.inuser,SUM(t.fhl) cnt FROM
        (
        SELECT l.inuser,1 kinds,COUNT(1) fhl
        FROM dbo.wuliu w WITH(NOLOCK)
        INNER JOIN dbo.#temporaryWl l WITH(NOLOCK) ON w.id = l.wuliuid
        INNER JOIN dbo.mkc_toarea m with(nolock) ON m.wuliuid = w.id
        WHERE isnull(w.danhaobind,0) = 0 AND l.statistcs_categray =1
        GROUP BY l.inuser
        UNION ALL
        SELECT l.inuser,2 kinds,SUM(CASE WHEN pf.cid in (485) THEN b.lcount WHEN isnull(pf.otherLimit,0) &amp; 2 = 2 THEN
        b.lcount * 1 ELSE b.lcount END)/isnull(spe.spec,1)
        FROM dbo.wuliu w WITH(NOLOCK)
        INNER JOIN dbo.#temporaryWl l WITH(NOLOCK) ON w.id = l.wuliuid
        INNER JOIN dbo.diaobo_sub s with(nolock) ON s.wuliuid = w.id
        INNER join dbo.diaobo_basket b with(nolock) on s.id=b.sub_id
        left join dbo.productinfo pf with(nolock) on pf.ppriceid = b.ppriceid
        left join dbo.wuliu_calculate_statistics_spec spe with(nolock) on spe.ppriceid= b.ppriceid
        WHERE isnull(w.danhaobind,0) = 0 AND l.statistcs_categray =1
        GROUP BY l.inuser,spe.spec
        UNION ALL
        SELECT l.inuser,3 kinds,SUM(CASE WHEN pf.cid in (485) THEN b.basket_count WHEN isnull(pf.otherLimit,0) &amp; 2 = 2 THEN
        b.basket_count * 1 ELSE b.basket_count END)/isnull(spe.spec,1)
        FROM dbo.wuliu w WITH(NOLOCK)
        INNER JOIN dbo.#temporaryWl l WITH(NOLOCK) ON w.id = l.wuliuid
        INNER JOIN dbo.sub s with(nolock) ON s.sub_id = w.danhaobind
        INNER join dbo.basket b with(nolock) on b.sub_id = s.sub_id
        left join dbo.productinfo pf with(nolock) on pf.ppriceid = b.ppriceid
        left join dbo.wuliu_calculate_statistics_spec spe with(nolock) on spe.ppriceid= pf.ppriceid
        WHERE (w.subKinds=1 or w.wutype in (4,6)) and isnull(b.isdel,0) = 0 AND l.statistcs_categray =1
        GROUP BY l.inuser,spe.spec
        UNION ALL
        SELECT l.inuser,4 kinds,COUNT(1) t
        FROM dbo.wuliu w WITH(NOLOCK)
        INNER JOIN dbo.#temporaryWl l WITH(NOLOCK) ON w.id = l.wuliuid
        WHERE (w.subKinds=3 or w.wutype in (5)) AND l.statistcs_categray =1
        GROUP BY l.inuser
        UNION ALL
        SELECT l.inuser,5 kinds,sum(b.basket_count)/isnull(spe.spec,1) t
        FROM dbo.wuliu w WITH(NOLOCK)
        INNER JOIN dbo.#temporaryWl l WITH(NOLOCK) ON w.id = l.wuliuid
        INNER JOIN dbo.recover_marketInfo s with(nolock) ON s.sub_id = w.danhaobind
        INNER join dbo.recover_marketSubInfo b with(nolock) on b.sub_id = s.sub_id
        left join dbo.wuliu_calculate_statistics_spec spe with(nolock) on spe.ppriceid= b.ppriceid
        WHERE (w.subKinds=2 or w.wutype in (9)) and isnull(b.isdel,0) = 0 AND l.statistcs_categray =1
        GROUP BY l.inuser,spe.spec
        union ALL
        SELECT l.inuser,5 kinds,count(1) t
        FROM dbo.wuliu w WITH(NOLOCK)
        INNER JOIN dbo.#temporaryWl l WITH(NOLOCK) ON w.id = l.wuliuid
        INNER JOIN dbo.recover_toarea s with(nolock) ON s.wuliuid = w.id
        WHERE l.statistcs_categray =1
        GROUP BY l.inuser
        UNION ALL
        SELECT l.inuser,6 kinds,COUNT(1) t
        FROM dbo.wuliu w WITH(NOLOCK)
        INNER JOIN dbo.#temporaryWl l WITH(NOLOCK) ON w.id = l.wuliuid
        WHERE isnull(w.danhaobind,0) != 0 AND w.subKinds NOT IN (1,2,3) AND w.wutype NOT IN (4,5,6,9) AND
        l.statistcs_categray =1
        GROUP BY l.inuser
        ) t
        GROUP BY t.inuser
        ) t11 ON t11.inuser = u.ch999_name
        LEFT JOIN
        (
        SELECT t.inuser,SUM(t.fhl) cnt FROM
        (
        SELECT l.inuser,1 kinds,COUNT(1) fhl
        FROM dbo.wuliu w WITH(NOLOCK)
        INNER JOIN dbo.#temporaryWl l WITH(NOLOCK) ON w.id = l.wuliuid
        INNER JOIN dbo.mkc_toarea m with(nolock) ON m.wuliuid = w.id
        WHERE isnull(w.danhaobind,0) = 0 and l.statistcs_categray =2
        GROUP BY l.inuser
        UNION ALL
        SELECT l.inuser,2 kinds,SUM(CASE WHEN pf.cid in (485) THEN b.lcount WHEN isnull(pf.otherLimit,0) &amp; 2 = 2 THEN
        b.lcount * 1 ELSE b.lcount END)/isnull(spe.spec,1)
        FROM dbo.wuliu w WITH(NOLOCK)
        INNER JOIN dbo.#temporaryWl l WITH(NOLOCK) ON w.id = l.wuliuid
        INNER JOIN dbo.diaobo_sub s with(nolock) ON s.wuliuid = w.id
        INNER join dbo.diaobo_basket b with(nolock) on s.id=b.sub_id
        left join dbo.productinfo pf with(nolock) on pf.ppriceid = b.ppriceid
        left join dbo.wuliu_calculate_statistics_spec spe with(nolock) on spe.ppriceid= pf.ppriceid
        WHERE isnull(w.danhaobind,0) = 0 and l.statistcs_categray =2
        GROUP BY l.inuser,spe.spec
        UNION ALL
        SELECT l.inuser,3 kinds,SUM(CASE WHEN pf.cid in (485) THEN b.basket_count WHEN isnull(pf.otherLimit,0) &amp; 2 = 2 THEN
        b.basket_count * 1 ELSE b.basket_count END)/isnull(spe.spec,1)
        FROM dbo.wuliu w WITH(NOLOCK)
        INNER JOIN dbo.#temporaryWl l WITH(NOLOCK) ON w.id = l.wuliuid
        INNER JOIN dbo.sub s with(nolock) ON s.sub_id = w.danhaobind
        INNER join dbo.basket b with(nolock) on b.sub_id = s.sub_id
        left join dbo.productinfo pf with(nolock) on pf.ppriceid = b.ppriceid
        left join dbo.wuliu_calculate_statistics_spec spe with(nolock) on spe.ppriceid= pf.ppriceid
        WHERE (w.subKinds=1 or w.wutype in (4,6)) and isnull(b.isdel,0) = 0 and l.statistcs_categray =2
        GROUP BY l.inuser,spe.spec
        UNION ALL
        SELECT l.inuser,4 kinds,COUNT(1) t
        FROM dbo.wuliu w WITH(NOLOCK)
        INNER JOIN dbo.#temporaryWl l WITH(NOLOCK) ON w.id = l.wuliuid
        WHERE (w.subKinds=3 or w.wutype in (5)) and l.statistcs_categray =2
        GROUP BY l.inuser
        UNION ALL
        SELECT l.inuser,5 kinds,COUNT(1) t
        FROM dbo.wuliu w WITH(NOLOCK)
        INNER JOIN dbo.#temporaryWl l WITH(NOLOCK) ON w.id = l.wuliuid
        INNER JOIN dbo.recover_toarea s with(nolock) ON s.wuliuid = w.id
        WHERE l.statistcs_categray =2
        GROUP BY l.inuser
        UNION ALL
        SELECT l.inuser,6 kinds,COUNT(1) t
        FROM dbo.wuliu w WITH(NOLOCK)
        INNER JOIN dbo.#temporaryWl l WITH(NOLOCK) ON w.id = l.wuliuid
        WHERE isnull(w.danhaobind,0) != 0 AND w.subKinds NOT IN (1,2,3) AND w.wutype NOT IN (4,5,6,9) and
        l.statistcs_categray =2
        GROUP BY l.inuser
        ) t
        GROUP BY t.inuser
        ) t12 ON t12.inuser = u.ch999_name
        LEFT JOIN
        (
        SELECT temp.inuser,SUM(temp.cnt) as cnt FROM(SELECT s.inuser,SUM(CASE WHEN pf.cid in (485) THEN b.lcount WHEN
        isnull(pf.otherLimit,0) &amp; 2 = 2 THEN b.lcount
        * 1 ELSE b.lcount END)/isnull(spe.spec,1) cnt FROM dbo.caigouInputSub s WITH(NOLOCK)
        INNER JOIN dbo.caigouInputBasket b WITH(NOLOCK) ON s.id = b.inputId
        INNER JOIN dbo.productinfo pf WITH(NOLOCK) ON pf.ppriceid = b.ppriceid
        inner join dbo.caigou_sub ss with(nolock) on ss.id= s.caigouId
        left join dbo.wuliu_calculate_statistics_spec spe with(nolock) on spe.ppriceid= pf.ppriceid
        WHERE s.dtime BETWEEN #{param.startTime} and #{param.endTime} and ss.kinds='wx'
        GROUP BY s.inuser,spe.spec,pf.ppriceid)temp GROUP BY temp.inuser
        ) t13 ON t13.inuser = u.ch999_name
        LEFT JOIN
        (
        SELECT temp.inuser,SUM(temp.cnt) as cnt FROM(SELECT c.inuser,SUM(CASE WHEN pf.cid in (485) THEN b.lcount WHEN
        isnull(pf.otherLimit,0) &amp; 2 = 2 THEN b.lcount
        * 1 ELSE b.lcount END)/isnull(spe.spec,1) cnt
        FROM dbo.diaobo_sub s WITH(NOLOCK)
        INNER JOIN dbo.diaobo_basket b WITH(NOLOCK) ON s.id = b.sub_id
        INNER JOIN (
        SELECT * FROM
        (
        SELECT c.db_id,c.inuser,c.dtime,ROW_NUMBER() OVER(PARTITION BY c.db_id ORDER BY c.id DESC) rn
        FROM dbo.diaoboSub_comment c WITH(NOLOCK)
        WHERE c.operationKinds = 4
        ) t WHERE t.rn = 1
        ) c ON s.id = c.db_id
        left join dbo.productinfo pf with(nolock) on pf.ppriceid = b.ppriceid
        left join dbo.wuliu_calculate_statistics_spec spe with(nolock) on spe.ppriceid= pf.ppriceid
        WHERE s.stats = 4 AND c.dtime BETWEEN #{param.startTime} and #{param.endTime} and s.kinds = 'wx'
        GROUP BY c.inuser,spe.spec,pf.ppriceid )temp GROUP BY temp.inuser
        ) t14 ON t14.inuser = u.ch999_name
        LEFT JOIN
        (
        SELECT temp.inuser,SUM(temp.cnt) as cnt FROM(SELECT c.inuser,SUM(CASE WHEN pf.cid in (485) THEN b.lcount WHEN
        isnull(pf.otherLimit,0) &amp; 2 = 2 THEN b.lcount
        * 1 ELSE b.lcount END)/isnull(spe.spec,1) cnt
        from dbo.diaobo_sub s WITH(NOLOCK)
        INNER JOIN dbo.diaobo_basket b WITH(NOLOCK) ON s.id = b.sub_id
        INNER JOIN (
        SELECT * FROM
        (
        SELECT c.db_id,c.inuser,c.dtime,ROW_NUMBER() OVER(PARTITION BY c.db_id ORDER BY c.id DESC) rn
        FROM dbo.diaoboSub_comment c WITH(NOLOCK)
        WHERE c.operationKinds = 6
        ) t WHERE t.rn = 1
        ) c ON s.id = c.db_id
        left join dbo.productinfo pf with(nolock) on pf.ppriceid = b.ppriceid
        left join dbo.wuliu_calculate_statistics_spec spe with(nolock) on spe.ppriceid= pf.ppriceid
        WHERE s.stats IN (3,4,5,6) AND s.kinds = 'wx' AND c.dtime BETWEEN #{param.startTime} and #{param.endTime} AND b.basket_id IS NULL
        GROUP BY c.inuser,spe.spec,pf.ppriceid )temp GROUP BY temp.inuser
        ) t15 ON t15.inuser = u.ch999_name
        LEFT JOIN
        (
        SELECT temp.inuser,SUM(temp.cnt) as cnt FROM(
        SELECT c.inuser,SUM(CASE WHEN pf.cid in (485) THEN b.lcount WHEN isnull(pf.otherLimit,0) &amp; 2 = 2 THEN b.lcount
        * 1 ELSE b.lcount END)/isnull(spe.spec,1) cnt
        FROM dbo.diaobo_sub s WITH(NOLOCK)
        INNER JOIN dbo.diaobo_basket b WITH(NOLOCK) ON s.id = b.sub_id
        INNER JOIN (
        SELECT * FROM
        (
        SELECT c.db_id,c.inuser,c.dtime,ROW_NUMBER() OVER(PARTITION BY c.db_id ORDER BY c.id DESC) rn
        FROM dbo.diaoboSub_comment c WITH(NOLOCK)
        WHERE c.operationKinds = 6
        ) t WHERE t.rn = 1
        ) c ON s.id = c.db_id
        left join dbo.productinfo pf with(nolock) on pf.ppriceid = b.ppriceid
        left join dbo.wuliu_calculate_statistics_spec spe with(nolock) on spe.ppriceid= pf.ppriceid
        WHERE s.stats IN (3,4,5,6) AND s.kinds = 'wx' AND c.dtime BETWEEN #{param.startTime} and #{param.endTime} AND b.basket_id IS NOT NULL
        GROUP BY c.inuser,spe.spec,pf.ppriceid )temp GROUP BY temp.inuser
        ) t16 ON t16.inuser = u.ch999_name
        <where>
            <include refid="whereCondition"></include>
        </where>
        ) ttt
        <if test="current != null and size != null">
            where rownumber BETWEEN (#{current}-1) * #{size} +1 and #{current}*#{size};
        </if>
    </select>
    
    <select id="wuliuCalculateStatisticsV2PartOne"
            resultType="com.jiuji.oa.stock.accessory.vo.res.WuliuCalculateStatisticsOutputRes">
        select u.ch999_id,u.ch999_name,a.area,ISNULL(t1.cnt,0) dajianrkl,ISNULL(t2.cnt,0) peijianrkl,ISNULL(t3.cnt,0)
        liangpinrkl,ISNULL(t4.cnt,0) dajianshl,ISNULL(t5.cnt,0) peijianshl
        ,ISNULL(t6.cnt,0) peijianxhjhl,ISNULL(t7.cnt,0) peijiandhjhl,ISNULL(t8.cnt,0) dajianjhl,ISNULL(t9.cnt,0)
        liangpinjhl,ISNULL(t10.cnt,0) kuaidiysl
        FROM dbo.ch999_user u WITH(NOLOCK)
        INNER JOIN dbo.areainfo a WITH(NOLOCK) ON a.id = u.area1id
        LEFT JOIN
        (
        SELECT k.imeiuser,COUNT(1) cnt FROM dbo.product_mkc k WITH(NOLOCK)
        WHERE k.imeidate BETWEEN #{param.startTime} and #{param.endTime}
        GROUP BY k.imeiuser
        ) t1 ON t1.imeiuser = u.ch999_name
        LEFT JOIN
        (
        SELECT temp.inuser,SUM(temp.cnt) as cnt FROM( SELECT s.inuser,SUM(CASE WHEN pf.cid in (485) THEN b.lcount WHEN isnull(pf.otherLimit,0) &amp; 2 = 2 THEN b.lcount
        * 1 ELSE b.lcount END)/isnull(spe.spec,1) cnt FROM dbo.caigouInputSub s WITH(NOLOCK)
        INNER JOIN dbo.caigouInputBasket b WITH(NOLOCK) ON s.id = b.inputId
        INNER JOIN dbo.productinfo pf WITH(NOLOCK) ON pf.ppriceid = b.ppriceid
        inner join dbo.caigou_sub ss with(nolock) on ss.id= s.caigouId
        left join dbo.wuliu_calculate_statistics_spec spe with(nolock) on spe.ppriceid= pf.ppriceid
        WHERE s.dtime BETWEEN #{param.startTime} and #{param.endTime} and ss.kinds='pj'
        GROUP BY s.inuser,spe.spec )temp GROUP BY temp.inuser
        ) t2 ON t2.inuser = u.ch999_name
        LEFT JOIN
        (
        SELECT t.reciveuser,COUNT(1) cnt FROM dbo.recover_toarea t WITH(NOLOCK)
        WHERE t.reciveuser IS NOT NULL AND t.recivedtime BETWEEN #{param.startTime} and #{param.endTime}
        GROUP BY t.reciveuser
        ) t3 ON t3.reciveuser = u.ch999_name
        LEFT JOIN
        (
        SELECT t.reciveuser,COUNT(1) cnt FROM dbo.mkc_toarea t WITH(NOLOCK)
        WHERE t.reciveuser IS NOT NULL AND t.recivedtime BETWEEN #{param.startTime} and #{param.endTime}
        GROUP BY t.reciveuser
        ) t4 ON t4.reciveuser = u.ch999_name
        LEFT JOIN
        (
        SELECT temp.inuser,SUM(temp.cnt) as cnt FROM( SELECT c.inuser,SUM(CASE WHEN pf.cid in (485) THEN b.lcount WHEN
        isnull(pf.otherLimit,0) &amp; 2 = 2 THEN b.lcount
        * 1 ELSE b.lcount END)/isnull(spe.spec,1) cnt
        from dbo.diaobo_sub s WITH(NOLOCK)
        INNER JOIN dbo.diaobo_basket b WITH(NOLOCK) ON s.id = b.sub_id
        INNER JOIN (
        SELECT * FROM
        (
        SELECT temp.inuser,SUM(temp.cnt) as cnt FROM(SELECT c.db_id,c.inuser,c.dtime,ROW_NUMBER() OVER(PARTITION BY
        c.db_id ORDER BY c.id DESC) rn
        FROM dbo.diaoboSub_comment c WITH(NOLOCK)
        WHERE c.operationKinds = 4
        ) t WHERE t.rn = 1
        ) c ON s.id = c.db_id
        left join dbo.productinfo pf with(nolock) on pf.ppriceid = b.ppriceid
        left join dbo.wuliu_calculate_statistics_spec spe with(nolock) on spe.ppriceid= pf.ppriceid
        WHERE s.stats = 4 AND c.dtime BETWEEN #{param.startTime} and #{param.endTime} and s.kinds = 'pj'
        AND pf.cid != 485
        GROUP BY c.inuser,spe.spec,pf.ppriceid )temp GROUP BY temp.inuser
        ) t5 ON t5.inuser = u.ch999_name
        LEFT JOIN
        (
        SELECT temp.inuser,SUM(temp.cnt) as cnt FROM( SELECT c.inuser,SUM(CASE WHEN pf.cid in (485) THEN b.lcount
        WHEN isnull(pf.otherLimit,0) &amp; 2 = 2 THEN b.lcount * 1 ELSE b.lcount END)/isnull(spe.spec,1) cnt
        from dbo.diaobo_sub s WITH(NOLOCK)
        INNER JOIN dbo.diaobo_basket b WITH(NOLOCK) ON s.id = b.sub_id
        INNER JOIN (
        SELECT * FROM
        (
        SELECT c.db_id,c.inuser,c.dtime,ROW_NUMBER() OVER(PARTITION BY c.db_id ORDER BY c.id DESC) rn
        FROM dbo.diaoboSub_comment c WITH(NOLOCK)
        WHERE c.operationKinds = 6
        ) t WHERE t.rn = 1
        ) c ON s.id = c.db_id
        left join dbo.productinfo pf with(nolock) on pf.ppriceid = b.ppriceid
        left join dbo.wuliu_calculate_statistics_spec spe with(nolock) on spe.ppriceid= pf.ppriceid
        WHERE s.kinds = 'pj' AND c.dtime BETWEEN #{param.startTime} and #{param.endTime} AND
        b.basket_id IS NULL
        GROUP BY c.inuser,spe.spec,pf.ppriceid )temp GROUP BY temp.inuser
        ) t6 ON t6.inuser = u.ch999_name
        LEFT JOIN
        (
        SELECT temp.inuser,SUM(temp.cnt) as cnt FROM(SELECT c.inuser,SUM(CASE WHEN pf.cid in (485) THEN b.lcount ELSE b.lcount
        END)/isnull(spe.spec,1) cnt
        from dbo.diaobo_sub s WITH(NOLOCK)
        INNER JOIN dbo.diaobo_basket b WITH(NOLOCK) ON s.id = b.sub_id
        INNER JOIN (
        SELECT * FROM
        (
        SELECT c.db_id,c.inuser,c.dtime,ROW_NUMBER() OVER(PARTITION BY c.db_id ORDER BY c.id DESC) rn
        FROM dbo.diaoboSub_comment c WITH(NOLOCK)
        WHERE c.operationKinds = 6
        ) t WHERE t.rn = 1
        ) c ON s.id = c.db_id
        left join dbo.productinfo pf with(nolock) on pf.ppriceid = b.ppriceid
        left join dbo.wuliu_calculate_statistics_spec spe with(nolock) on spe.ppriceid= pf.ppriceid
        WHERE s.kinds = 'pj' AND c.dtime BETWEEN #{param.startTime} and #{param.endTime} AND
        b.basket_id IS NOT NULL
        AND pf.cid != 485
        GROUP BY c.inuser,spe.spec,pf.ppriceid )temp GROUP BY temp.inuser
        ) t7 ON t7.inuser = u.ch999_name
        LEFT JOIN
        (
        SELECT t.senduser,COUNT(1) cnt FROM dbo.mkc_toarea t WITH(NOLOCK)
        WHERE t.senduser IS NOT NULL AND t.sendtime BETWEEN #{param.startTime} and #{param.endTime}
        GROUP BY t.senduser
        ) t8 ON t8.senduser = u.ch999_name
        LEFT JOIN
        (
        SELECT t.senduser,COUNT(1) cnt FROM dbo.recover_toarea t WITH(NOLOCK)
        WHERE t.senduser IS NOT NULL AND t.sendtime BETWEEN  #{param.startTime} and  #{param.endTime}
        GROUP BY t.senduser
        ) t9 ON t9.senduser = u.ch999_name
        LEFT JOIN
        (
        SELECT t.inuser,sum(basket_count) cnt FROM
        (
        SELECT t.inuser,s.sub_id,t.subType,sb.basket_count/isnull(spe.spec,1) as basket_count FROM dbo.subConfirmRecord t WITH(NOLOCK)
        INNER JOIN dbo.sub s WITH(NOLOCK) ON s.sub_id = t.sub_id
        left join  dbo.basket sb with(nolock) on sb.sub_id =s.sub_id
        left join dbo.wuliu_calculate_statistics_spec spe with(nolock) on spe.ppriceid= sb.ppriceid
        WHERE t.subType = 1 AND t.operationKinds = 2 AND t.dtime BETWEEN  #{param.startTime} and  #{param.endTime} AND s.delivery = 4 and  isnull(sb.isdel,0)=0
        UNION ALL
        SELECT t.inuser,s.sub_id,t.subType,rb.count/isnull(spe.spec,1) as basket_count FROM dbo.subConfirmRecord t WITH(NOLOCK)
        INNER JOIN dbo.recover_marketInfo s WITH(NOLOCK) ON s.sub_id = t.sub_id
        left join  dbo.recover_basket rb with(nolock) on rb.sub_id =s.sub_id
        left join dbo.wuliu_calculate_statistics_spec spe with(nolock) on spe.ppriceid= rb.ppriceid
        WHERE t.subType = 2 AND t.operationKinds = 2 AND t.dtime BETWEEN  #{param.startTime} and  #{param.endTime} AND s.delivery = 4 and  isnull(rb.isdel,0)=0
        ) t
        GROUP BY t.inuser
        ) t10 ON t10.inuser = u.ch999_name
        <where>
            <include refid="whereCondition"></include>
        </where>
        <choose>
            <when test="param.orderBy != null and param.orderBy != ''">
                order by #{param.orderBy}
            </when>
            <otherwise>
                order by dajianshl DESC,u.ch999_id ASC
            </otherwise>
        </choose>
    </select>

    <select id="wuliuCalculateStatisticsV2PartTwo"
            resultType="com.jiuji.oa.stock.accessory.vo.res.WuliuCalculateStatisticsOutputRes">
        select *
        into #temporaryWl
        from dbo.wuliu_logs l with(nolock)
        where l.dtime between #{param.startTime}
        and #{param.endTime};

        select u.ch999_id,ISNULL(t11.cnt,0) daifasml,ISNULL(t12.cnt,0) fhsml
        FROM dbo.ch999_user u WITH(NOLOCK)
        LEFT JOIN
        (
        SELECT t.inuser,SUM(t.fhl) cnt FROM
        (
        SELECT l.inuser,1 kinds,COUNT(1) fhl
        FROM dbo.wuliu w WITH(NOLOCK)
        INNER JOIN dbo.#temporaryWl l WITH(NOLOCK) ON w.id = l.wuliuid
        INNER JOIN dbo.mkc_toarea m with(nolock) ON m.wuliuid = w.id
        WHERE isnull(w.danhaobind,0) = 0 AND l.statistcs_categray =1
        GROUP BY l.inuser
        UNION ALL
        SELECT l.inuser,2 kinds,SUM(CASE WHEN pf.cid in (485) THEN b.lcount WHEN isnull(pf.otherLimit,0) &amp; 2 = 2 THEN
        b.lcount * 1 ELSE b.lcount END)/isnull(spe.spec,1)
        FROM dbo.wuliu w WITH(NOLOCK)
        INNER JOIN dbo.#temporaryWl l WITH(NOLOCK) ON w.id = l.wuliuid
        INNER JOIN dbo.diaobo_sub s with(nolock) ON s.wuliuid = w.id
        INNER join dbo.diaobo_basket b with(nolock) on s.id=b.sub_id
        left join dbo.productinfo pf with(nolock) on pf.ppriceid = b.ppriceid
        left join dbo.wuliu_calculate_statistics_spec spe with(nolock) on spe.ppriceid= b.ppriceid
        WHERE isnull(w.danhaobind,0) = 0 AND l.statistcs_categray =1
        GROUP BY l.inuser,spe.spec
        UNION ALL
        SELECT l.inuser,3 kinds,SUM(CASE WHEN pf.cid in (485) THEN b.basket_count WHEN isnull(pf.otherLimit,0) &amp; 2 = 2 THEN
        b.basket_count * 1 ELSE b.basket_count END)/isnull(spe.spec,1)
        FROM dbo.wuliu w WITH(NOLOCK)
        INNER JOIN dbo.#temporaryWl l WITH(NOLOCK) ON w.id = l.wuliuid
        INNER JOIN dbo.sub s with(nolock) ON s.sub_id = w.danhaobind
        INNER join dbo.basket b with(nolock) on b.sub_id = s.sub_id
        left join dbo.productinfo pf with(nolock) on pf.ppriceid = b.ppriceid
        left join dbo.wuliu_calculate_statistics_spec spe with(nolock) on spe.ppriceid= pf.ppriceid
        WHERE (w.subKinds=1 or w.wutype in (4,6)) and isnull(b.isdel,0) = 0 AND l.statistcs_categray =1
        GROUP BY l.inuser,spe.spec
        UNION ALL
        SELECT l.inuser,4 kinds,COUNT(1) t
        FROM dbo.wuliu w WITH(NOLOCK)
        INNER JOIN dbo.#temporaryWl l WITH(NOLOCK) ON w.id = l.wuliuid
        WHERE (w.subKinds=3 or w.wutype in (5)) AND l.statistcs_categray =1
        GROUP BY l.inuser
        UNION ALL
        SELECT l.inuser,5 kinds,sum(b.basket_count)/isnull(spe.spec,1) t
        FROM dbo.wuliu w WITH(NOLOCK)
        INNER JOIN dbo.#temporaryWl l WITH(NOLOCK) ON w.id = l.wuliuid
        INNER JOIN dbo.recover_marketInfo s with(nolock) ON s.sub_id = w.danhaobind
        INNER join dbo.recover_marketSubInfo b with(nolock) on b.sub_id = s.sub_id
        left join dbo.wuliu_calculate_statistics_spec spe with(nolock) on spe.ppriceid= b.ppriceid
        WHERE (w.subKinds=2 or w.wutype in (9)) and isnull(b.isdel,0) = 0 AND l.statistcs_categray =1
        GROUP BY l.inuser,spe.spec
        union ALL
        SELECT l.inuser,5 kinds,count(1) t
        FROM dbo.wuliu w WITH(NOLOCK)
        INNER JOIN dbo.#temporaryWl l WITH(NOLOCK) ON w.id = l.wuliuid
        INNER JOIN dbo.recover_toarea s with(nolock) ON s.wuliuid = w.id
        WHERE l.statistcs_categray =1
        GROUP BY l.inuser
        UNION ALL
        SELECT l.inuser,6 kinds,COUNT(1) t
        FROM dbo.wuliu w WITH(NOLOCK)
        INNER JOIN dbo.#temporaryWl l WITH(NOLOCK) ON w.id = l.wuliuid
        WHERE isnull(w.danhaobind,0) != 0 AND w.subKinds NOT IN (1,2,3) AND w.wutype NOT IN (4,5,6,9) AND
        l.statistcs_categray =1
        GROUP BY l.inuser
        ) t
        GROUP BY t.inuser
        ) t11 ON t11.inuser = u.ch999_name
        LEFT JOIN
        (
        SELECT t.inuser,SUM(t.fhl) cnt FROM
        (
        SELECT l.inuser,1 kinds,COUNT(1) fhl
        FROM dbo.wuliu w WITH(NOLOCK)
        INNER JOIN dbo.#temporaryWl l WITH(NOLOCK) ON w.id = l.wuliuid
        INNER JOIN dbo.mkc_toarea m with(nolock) ON m.wuliuid = w.id
        WHERE isnull(w.danhaobind,0) = 0 and l.statistcs_categray =2
        GROUP BY l.inuser
        UNION ALL
        SELECT l.inuser,2 kinds,SUM(CASE WHEN pf.cid in (485) THEN b.lcount WHEN isnull(pf.otherLimit,0) &amp; 2 = 2 THEN
        b.lcount * 1 ELSE b.lcount END)/isnull(spe.spec,1)
        FROM dbo.wuliu w WITH(NOLOCK)
        INNER JOIN dbo.#temporaryWl l WITH(NOLOCK) ON w.id = l.wuliuid
        INNER JOIN dbo.diaobo_sub s with(nolock) ON s.wuliuid = w.id
        INNER join dbo.diaobo_basket b with(nolock) on s.id=b.sub_id
        left join dbo.productinfo pf with(nolock) on pf.ppriceid = b.ppriceid
        left join dbo.wuliu_calculate_statistics_spec spe with(nolock) on spe.ppriceid= pf.ppriceid
        WHERE isnull(w.danhaobind,0) = 0 and l.statistcs_categray =2
        GROUP BY l.inuser,spe.spec
        UNION ALL
        SELECT l.inuser,3 kinds,SUM(CASE WHEN pf.cid in (485) THEN b.basket_count WHEN isnull(pf.otherLimit,0) &amp; 2 = 2 THEN
        b.basket_count * 1 ELSE b.basket_count END)/isnull(spe.spec,1)
        FROM dbo.wuliu w WITH(NOLOCK)
        INNER JOIN dbo.#temporaryWl l WITH(NOLOCK) ON w.id = l.wuliuid
        INNER JOIN dbo.sub s with(nolock) ON s.sub_id = w.danhaobind
        INNER join dbo.basket b with(nolock) on b.sub_id = s.sub_id
        left join dbo.productinfo pf with(nolock) on pf.ppriceid = b.ppriceid
        left join dbo.wuliu_calculate_statistics_spec spe with(nolock) on spe.ppriceid= pf.ppriceid
        WHERE (w.subKinds=1 or w.wutype in (4,6)) and isnull(b.isdel,0) = 0 and l.statistcs_categray =2
        GROUP BY l.inuser,spe.spec
        UNION ALL
        SELECT l.inuser,4 kinds,COUNT(1) t
        FROM dbo.wuliu w WITH(NOLOCK)
        INNER JOIN dbo.#temporaryWl l WITH(NOLOCK) ON w.id = l.wuliuid
        WHERE (w.subKinds=3 or w.wutype in (5)) and l.statistcs_categray =2
        GROUP BY l.inuser
        UNION ALL
        SELECT l.inuser,5 kinds,COUNT(1) t
        FROM dbo.wuliu w WITH(NOLOCK)
        INNER JOIN dbo.#temporaryWl l WITH(NOLOCK) ON w.id = l.wuliuid
        INNER JOIN dbo.recover_toarea s with(nolock) ON s.wuliuid = w.id
        WHERE l.statistcs_categray =2
        GROUP BY l.inuser
        UNION ALL
        SELECT l.inuser,6 kinds,COUNT(1) t
        FROM dbo.wuliu w WITH(NOLOCK)
        INNER JOIN dbo.#temporaryWl l WITH(NOLOCK) ON w.id = l.wuliuid
        WHERE isnull(w.danhaobind,0) != 0 AND w.subKinds NOT IN (1,2,3) AND w.wutype NOT IN (4,5,6,9) and
        l.statistcs_categray =2
        GROUP BY l.inuser
        ) t
        GROUP BY t.inuser
        ) t12 ON t12.inuser = u.ch999_name
        <where>
            u.ch999_id in
            <foreach collection="param.userIdList" item="it" separator="," open="(" close=")">
                #{it}
            </foreach>
        </where>
    </select>

    <select id="wuliuCalculateStatisticsV2PartThree"
            resultType="com.jiuji.oa.stock.accessory.vo.res.WuliuCalculateStatisticsOutputRes">
       select u.ch999_id,ISNULL(t13.cnt,0) wxpeijianrkl,ISNULL(t14.cnt,0)
            wxpeijianshl,ISNULL(t15.cnt,0) wxpeijianxhjhl,ISNULL(t16.cnt,0) wxpeijiandhjhl
            FROM dbo.ch999_user u WITH(NOLOCK)
            LEFT JOIN
            (
        SELECT temp.inuser,SUM(temp.cnt) as cnt FROM(SELECT s.inuser,SUM(CASE WHEN pf.cid in (485) THEN b.lcount WHEN isnull(pf.otherLimit,0) &amp; 2 = 2 THEN b.lcount
            * 1 ELSE b.lcount END)/isnull(spe.spec,1) cnt FROM dbo.caigouInputSub s WITH(NOLOCK)
            INNER JOIN dbo.caigouInputBasket b WITH(NOLOCK) ON s.id = b.inputId
            INNER JOIN dbo.productinfo pf WITH(NOLOCK) ON pf.ppriceid = b.ppriceid
        left join dbo.wuliu_calculate_statistics_spec spe with(nolock) on spe.ppriceid= pf.ppriceid
        inner join dbo.caigou_sub ss with(nolock) on ss.id= s.caigouId
        WHERE s.dtime BETWEEN #{param.startTime} and #{param.endTime} and ss.kinds='wx'
        GROUP BY c.inuser,spe.spec,pf.ppriceid )temp GROUP BY temp.inuser
        ) t13 ON t13.inuser = u.ch999_name
        LEFT JOIN
        (
        SELECT temp.inuser,SUM(temp.cnt) as cnt FROM(SELECT c.inuser,SUM(CASE WHEN pf.cid in (485) THEN b.lcount WHEN
        isnull(pf.otherLimit,0) &amp; 2 = 2 THEN b.lcount
        * 1 ELSE b.lcount END)/isnull(spe.spec,1) cnt
        FROM dbo.diaobo_sub s WITH(NOLOCK)
        INNER JOIN dbo.diaobo_basket b WITH(NOLOCK) ON s.id = b.sub_id
        INNER JOIN (
        SELECT * FROM
        (
        SELECT c.db_id,c.inuser,c.dtime,ROW_NUMBER() OVER(PARTITION BY c.db_id ORDER BY c.id DESC) rn
        FROM dbo.diaoboSub_comment c WITH(NOLOCK)
        WHERE c.operationKinds = 4
        ) t WHERE t.rn = 1
        ) c ON s.id = c.db_id
        left join dbo.productinfo pf with(nolock) on pf.ppriceid = b.ppriceid
        left join dbo.wuliu_calculate_statistics_spec spe with(nolock) on spe.ppriceid= pf.ppriceid
        WHERE s.stats = 4 AND c.dtime BETWEEN #{param.startTime} and #{param.endTime} and s.kinds = 'wx'
            AND pf.cid != 485
            GROUP BY c.inuser,spe.spec,pf.ppriceid )temp GROUP BY temp.inuser
            ) t14 ON t14.inuser = u.ch999_name
            LEFT JOIN
            (
            SELECT temp.inuser,SUM(temp.cnt) as cnt FROM(SELECT c.inuser,SUM(CASE WHEN pf.cid in (485) THEN b.lcount WHEN
            isnull(pf.otherLimit,0) &amp; 2 = 2 THEN b.lcount
            * 1 ELSE b.lcount END)/isnull(spe.spec,1) cnt
            from dbo.diaobo_sub s WITH(NOLOCK)
            INNER JOIN dbo.diaobo_basket b WITH(NOLOCK) ON s.id = b.sub_id
            INNER JOIN (
            SELECT * FROM
            (
            SELECT c.db_id,c.inuser,c.dtime,ROW_NUMBER() OVER(PARTITION BY c.db_id ORDER BY c.id DESC) rn
            FROM dbo.diaoboSub_comment c WITH(NOLOCK)
            WHERE c.operationKinds = 6
            ) t WHERE t.rn = 1
            ) c ON s.id = c.db_id
            left join dbo.productinfo pf with(nolock) on pf.ppriceid = b.ppriceid
            left join dbo.wuliu_calculate_statistics_spec spe with(nolock) on spe.ppriceid= pf.ppriceid
            WHERE s.stats IN (3,4,5,6) AND s.kinds = 'wx' AND c.dtime BETWEEN #{param.startTime} and #{param.endTime} AND
            b.basket_id IS NULL
            AND pf.cid != 485
            GROUP BY c.inuser,spe.spec,pf.ppriceid )temp GROUP BY temp.inuser
            ) t15 ON t15.inuser = u.ch999_name
            LEFT JOIN
            (
            SELECT temp.inuser,SUM(temp.cnt) as cnt FROM(
            SELECT c.inuser,SUM(CASE WHEN pf.cid in (485) THEN b.lcount WHEN isnull(pf.otherLimit,0) &amp; 2 = 2 THEN b.lcount
            * 1 ELSE b.lcount END)/isnull(spe.spec,1) cnt
            FROM dbo.diaobo_sub s WITH(NOLOCK)
            INNER JOIN dbo.diaobo_basket b WITH(NOLOCK) ON s.id = b.sub_id
            INNER JOIN (
            SELECT * FROM
            (
            SELECT c.db_id,c.inuser,c.dtime,ROW_NUMBER() OVER(PARTITION BY c.db_id ORDER BY c.id DESC) rn
            FROM dbo.diaoboSub_comment c WITH(NOLOCK)
            WHERE c.operationKinds = 6
            ) t WHERE t.rn = 1
            ) c ON s.id = c.db_id
            left join dbo.productinfo pf with(nolock) on pf.ppriceid = b.ppriceid
            left join dbo.wuliu_calculate_statistics_spec spe with(nolock) on spe.ppriceid= pf.ppriceid
            WHERE s.stats IN (3,4,5,6) AND s.kinds = 'wx' AND c.dtime BETWEEN #{param.startTime} and #{param.endTime} AND
            b.basket_id IS NOT NULL
            AND pf.cid != 485
            GROUP BY c.inuser,spe.spec,pf.ppriceid )temp GROUP BY temp.inuser
            ) t16 ON t16.inuser = u.ch999_name
            <where>
                u.ch999_id in
                <foreach collection="param.userIdList" item="it" separator="," open="(" close=")">
                    #{it}
                </foreach>
            </where>
    </select>

    <select id="wuliuCalculateStatisticsSumDataV2"
            resultType="com.jiuji.oa.stock.accessory.vo.res.WuliuCalculateStatisticsOutputRes">
        select *
        into #temporaryWl
        from dbo.wuliu_logs l with(nolock)
        where l.dtime between #{param.startTime}
        and #{param.endTime};
        CREATE NONCLUSTERED INDEX NonClusteredIndex_temporaryWl ON #temporaryWl (  wuliuid ASC  )
        WITH (  PAD_INDEX = OFF ,FILLFACTOR = 100  ,SORT_IN_TEMPDB = OFF , IGNORE_DUP_KEY = OFF , STATISTICS_NORECOMPUTE = OFF , ONLINE = OFF , ALLOW_ROW_LOCKS = ON , ALLOW_PAGE_LOCKS = ON  )
        ON [PRIMARY ] ;
        SELECT COUNT(1) cnt,SUM(dajianrkl) dajianrkl,SUM(peijianrkl) peijianrkl,SUM(liangpinrkl) liangpinrkl,SUM(dajianshl) dajianshl,SUM(peijianshl) peijianshl
        ,SUM(peijianxhjhl) peijianxhjhl,SUM(peijiandhjhl) peijiandhjhl,SUM(dajianjhl) dajianjhl,SUM(liangpinjhl) liangpinjhl,SUM(kuaidiysl) kuaidiysl
        ,SUM(daifasml) daifasml,SUM(fhsml) fhsml,SUM(wxpeijianrkl) wxpeijianrkl,SUM(wxpeijianshl) wxpeijianshl,SUM(wxpeijianxhjhl) wxpeijianxhjhl,SUM(wxpeijiandhjhl) wxpeijiandhjhl
        FROM
        (
        select u.ch999_id,u.ch999_name,a.area,ISNULL(t1.cnt,0) dajianrkl,ISNULL(t2.cnt,0) peijianrkl,ISNULL(t3.cnt,0) liangpinrkl,ISNULL(t4.cnt,0) dajianshl,ISNULL(t5.cnt,0) peijianshl
        ,ISNULL(t6.cnt,0) peijianxhjhl,ISNULL(t7.cnt,0) peijiandhjhl,ISNULL(t8.cnt,0) dajianjhl,ISNULL(t9.cnt,0) liangpinjhl,ISNULL(t10.cnt,0) kuaidiysl
        ,ISNULL(t11.cnt,0) daifasml,ISNULL(t12.cnt,0) fhsml,ISNULL(t13.cnt,0) wxpeijianrkl,ISNULL(t14.cnt,0) wxpeijianshl,ISNULL(t15.cnt,0) wxpeijianxhjhl,ISNULL(t16.cnt,0) wxpeijiandhjhl
        FROM dbo.ch999_user u WITH(NOLOCK)
        INNER JOIN dbo.areainfo a WITH(NOLOCK) ON a.id = u.area1id
        LEFT JOIN
        (
        SELECT k.imeiuser,COUNT(1) cnt FROM dbo.product_mkc k WITH(NOLOCK)
        WHERE k.imeidate BETWEEN #{param.startTime} and #{param.endTime}
        GROUP BY k.imeiuser
        ) t1 ON t1.imeiuser = u.ch999_name
        LEFT JOIN
        (
        SELECT s.inuser,SUM(CASE WHEN pf.cid in (485) THEN b.lcount WHEN isnull(pf.otherLimit,0) &amp; 2 = 2 THEN b.lcount * 1 ELSE b.lcount END) cnt FROM dbo.caigouInputSub s WITH(NOLOCK)
        INNER JOIN dbo.caigouInputBasket b WITH(NOLOCK) ON s.id = b.inputId
        INNER JOIN dbo.productinfo pf WITH(NOLOCK) ON pf.ppriceid = b.ppriceid
        inner join  dbo.caigou_sub  ss with(nolock) on ss.id= s.caigouId
        WHERE s.dtime BETWEEN #{param.startTime} and #{param.endTime}  and ss.kinds='pj'
        GROUP BY s.inuser
        ) t2 ON t2.inuser = u.ch999_name
        LEFT JOIN
        (
        SELECT t.reciveuser,COUNT(1) cnt FROM dbo.recover_toarea t WITH(NOLOCK)
        WHERE t.reciveuser IS NOT NULL AND t.recivedtime BETWEEN #{param.startTime} and #{param.endTime}
        GROUP BY t.reciveuser
        ) t3 ON t3.reciveuser = u.ch999_name
        LEFT JOIN
        (
        SELECT t.reciveuser,COUNT(1) cnt FROM dbo.mkc_toarea t WITH(NOLOCK)
        WHERE t.reciveuser IS NOT NULL AND t.recivedtime BETWEEN #{param.startTime} and #{param.endTime}
        GROUP BY t.reciveuser
        ) t4 ON t4.reciveuser = u.ch999_name
        LEFT JOIN
        (
        SELECT c.inuser,SUM(CASE WHEN pf.cid in (485) THEN b.lcount WHEN isnull(pf.otherLimit,0) &amp; 2 = 2 THEN b.lcount * 1 ELSE b.lcount END) cnt
        from dbo.diaobo_sub s WITH(NOLOCK)
        INNER JOIN dbo.diaobo_basket b WITH(NOLOCK) ON s.id = b.sub_id
        INNER JOIN (
        SELECT * FROM
        (
        SELECT c.db_id,c.inuser,c.dtime,ROW_NUMBER() OVER(PARTITION BY c.db_id ORDER BY c.id DESC) rn
        FROM dbo.diaoboSub_comment c WITH(NOLOCK)
        WHERE c.operationKinds = 4
        ) t WHERE t.rn = 1
        ) c ON s.id = c.db_id
        left join dbo.productinfo pf with(nolock) on pf.ppriceid = b.ppriceid
        WHERE s.stats = 4 AND c.dtime BETWEEN #{param.startTime} and #{param.endTime} and s.kinds = 'pj'
        GROUP BY c.inuser
        ) t5 ON t5.inuser = u.ch999_name
        LEFT JOIN
        (
        SELECT c.inuser,SUM(CASE WHEN pf.cid in (485) THEN b.lcount WHEN isnull(pf.otherLimit,0) &amp; 2 = 2 THEN b.lcount * 1 ELSE b.lcount END) cnt
        from dbo.diaobo_sub s WITH(NOLOCK)
        INNER JOIN dbo.diaobo_basket b WITH(NOLOCK) ON s.id = b.sub_id
        INNER JOIN (
        SELECT * FROM
        (
        SELECT c.db_id,c.inuser,c.dtime,ROW_NUMBER() OVER(PARTITION BY c.db_id ORDER BY c.id DESC) rn
        FROM dbo.diaoboSub_comment c WITH(NOLOCK)
        WHERE c.operationKinds = 6
        ) t WHERE t.rn = 1
        ) c ON s.id = c.db_id
        left join dbo.productinfo pf with(nolock) on pf.ppriceid = b.ppriceid
        WHERE s.stats IN (3,4,5,6) AND s.kinds = 'pj' AND c.dtime BETWEEN #{param.startTime} and #{param.endTime} AND b.basket_id IS NULL
        GROUP BY c.inuser
        ) t6 ON t6.inuser = u.ch999_name
        LEFT JOIN
        (
        SELECT c.inuser,SUM(CASE WHEN pf.cid in (485) THEN b.lcount ELSE b.lcount  END) cnt
        from dbo.diaobo_sub s WITH(NOLOCK)
        INNER JOIN dbo.diaobo_basket b WITH(NOLOCK) ON s.id = b.sub_id
        INNER JOIN (
        SELECT * FROM
        (
        SELECT c.db_id,c.inuser,c.dtime,ROW_NUMBER() OVER(PARTITION BY c.db_id ORDER BY c.id DESC) rn
        FROM dbo.diaoboSub_comment c WITH(NOLOCK)
        WHERE c.operationKinds = 6
        ) t WHERE t.rn = 1
        ) c ON s.id = c.db_id
        left join dbo.productinfo pf with(nolock) on pf.ppriceid = b.ppriceid
        WHERE s.stats IN (3,4,5,6) AND s.kinds = 'pj' AND c.dtime BETWEEN #{param.startTime} and #{param.endTime} AND b.basket_id IS NOT NULL
        GROUP BY c.inuser
        ) t7 ON t7.inuser = u.ch999_name
        LEFT JOIN
        (
        SELECT t.senduser,COUNT(1) cnt FROM dbo.mkc_toarea t WITH(NOLOCK)
        WHERE t.senduser IS NOT NULL AND t.sendtime BETWEEN #{param.startTime} and #{param.endTime}
        GROUP BY t.senduser
        ) t8 ON t8.senduser = u.ch999_name
        LEFT JOIN
        (
        SELECT t.senduser,COUNT(1) cnt FROM dbo.recover_toarea t WITH(NOLOCK)
        WHERE t.senduser IS NOT NULL AND t.sendtime BETWEEN #{param.startTime} and #{param.endTime}
        GROUP BY t.senduser
        ) t9 ON t9.senduser = u.ch999_name
        LEFT JOIN
        (
        SELECT t.inuser,sum(basket_count) cnt FROM
        (
        SELECT t.inuser,s.sub_id,t.subType,sb.basket_count FROM dbo.subConfirmRecord t WITH(NOLOCK)
        INNER JOIN dbo.sub s WITH(NOLOCK) ON s.sub_id = t.sub_id
        left join  dbo.basket sb with(nolock) on sb.sub_id =s.sub_id
        WHERE t.subType = 1 AND t.operationKinds = 2 AND t.dtime BETWEEN #{param.startTime} and #{param.endTime} AND s.delivery = 4 and  isnull(sb.isdel,0)=0
        UNION ALL
        SELECT t.inuser,s.sub_id,t.subType,rb.count as basket_count FROM dbo.subConfirmRecord t WITH(NOLOCK)
        INNER JOIN dbo.recover_marketInfo s WITH(NOLOCK) ON s.sub_id = t.sub_id
        left join  dbo.recover_basket rb with(nolock) on rb.sub_id =s.sub_id
        WHERE t.subType = 2 AND t.operationKinds = 2 AND t.dtime BETWEEN #{param.startTime} and #{param.endTime} AND s.delivery = 4 and  isnull(rb.isdel,0)=0
        ) t
        GROUP BY t.inuser
        ) t10 ON t10.inuser = u.ch999_name
        LEFT JOIN
        (
        SELECT t.inuser,SUM(t.fhl) cnt FROM
        (
        SELECT l.inuser,1 kinds,COUNT(1) fhl
        FROM dbo.wuliu w WITH(NOLOCK)
        INNER JOIN dbo.#temporaryWl l WITH(NOLOCK) ON w.id = l.wuliuid
        INNER JOIN dbo.mkc_toarea m with(nolock) ON m.wuliuid = w.id
        WHERE isnull(w.danhaobind,0) = 0 AND l.statistcs_categray =1
        GROUP BY l.inuser
        UNION ALL
        SELECT l.inuser,2 kinds,SUM(CASE WHEN pf.cid in (485) THEN b.lcount WHEN isnull(pf.otherLimit,0) &amp; 2 = 2 THEN b.lcount * 1 ELSE b.lcount END)
        FROM dbo.wuliu w WITH(NOLOCK)
        INNER JOIN dbo.#temporaryWl l WITH(NOLOCK) ON w.id = l.wuliuid
        INNER JOIN dbo.diaobo_sub s with(nolock) ON s.wuliuid = w.id
        INNER join dbo.diaobo_basket b with(nolock) on s.id=b.sub_id
        left join dbo.productinfo pf with(nolock) on pf.ppriceid = b.ppriceid
        WHERE isnull(w.danhaobind,0) = 0 AND l.statistcs_categray =1
        GROUP BY l.inuser
        UNION ALL
        SELECT l.inuser,3 kinds,SUM(CASE WHEN pf.cid in (485) THEN b.basket_count WHEN isnull(pf.otherLimit,0) &amp; 2 = 2 THEN b.basket_count * 1 ELSE b.basket_count END)
        FROM dbo.wuliu w WITH(NOLOCK)
        INNER JOIN dbo.#temporaryWl l WITH(NOLOCK) ON w.id = l.wuliuid
        INNER JOIN dbo.sub s with(nolock) ON s.sub_id = w.danhaobind
        INNER join dbo.basket b with(nolock) on b.sub_id = s.sub_id
        left join dbo.productinfo pf with(nolock) on pf.ppriceid = b.ppriceid
        WHERE (w.subKinds=1 or w.wutype in (4,6)) and isnull(b.isdel,0) = 0 AND l.statistcs_categray =1
        GROUP BY l.inuser
        UNION ALL
        SELECT l.inuser,4 kinds,COUNT(1) t
        FROM dbo.wuliu w WITH(NOLOCK)
        INNER JOIN dbo.#temporaryWl l WITH(NOLOCK) ON w.id = l.wuliuid
        WHERE (w.subKinds=3 or w.wutype in (5)) AND l.statistcs_categray =1
        GROUP BY l.inuser
        UNION ALL
        SELECT l.inuser,5 kinds,sum(b.basket_count) t
        FROM dbo.wuliu w WITH(NOLOCK)
        INNER JOIN dbo.#temporaryWl l WITH(NOLOCK) ON w.id = l.wuliuid
        INNER JOIN dbo.recover_marketInfo s with(nolock) ON s.sub_id = w.danhaobind
        INNER join dbo.recover_marketSubInfo b with(nolock) on b.sub_id = s.sub_id
        WHERE (w.subKinds=2 or w.wutype in (9)) and isnull(b.isdel,0) = 0 AND l.statistcs_categray =1
        GROUP BY l.inuser
        union ALL
        SELECT l.inuser,5 kinds,count(1) t
        FROM dbo.wuliu w WITH(NOLOCK)
        INNER JOIN dbo.#temporaryWl l WITH(NOLOCK) ON w.id = l.wuliuid
        INNER JOIN dbo.recover_toarea s with(nolock) ON s.wuliuid = w.id
        WHERE l.msg = '已经取货'
        GROUP BY l.inuser
        UNION ALL
        SELECT l.inuser,6 kinds,COUNT(1) t
        FROM dbo.wuliu w WITH(NOLOCK)
        INNER JOIN dbo.#temporaryWl l WITH(NOLOCK) ON w.id = l.wuliuid
        WHERE isnull(w.danhaobind,0) != 0 AND w.subKinds NOT IN (1,2,3) AND w.wutype NOT IN (4,5,6,9) AND l.statistcs_categray =1
        GROUP BY l.inuser
        ) t
        GROUP BY t.inuser
        ) t11 ON t11.inuser = u.ch999_name
        LEFT JOIN
        (
        SELECT t.inuser,SUM(t.fhl) cnt FROM
        (
        SELECT l.inuser,1 kinds,COUNT(1) fhl
        FROM dbo.wuliu w WITH(NOLOCK)
        INNER JOIN dbo.#temporaryWl l WITH(NOLOCK) ON w.id = l.wuliuid
        INNER JOIN dbo.mkc_toarea m with(nolock) ON m.wuliuid = w.id
        WHERE isnull(w.danhaobind,0) = 0 and l.statistcs_categray =2
        GROUP BY l.inuser
        UNION ALL
        SELECT l.inuser,2 kinds,SUM(CASE WHEN pf.cid in (485) THEN b.lcount WHEN isnull(pf.otherLimit,0) &amp; 2 = 2 THEN b.lcount * 1 ELSE b.lcount END)
        FROM dbo.wuliu w WITH(NOLOCK)
        INNER JOIN dbo.#temporaryWl l WITH(NOLOCK) ON w.id = l.wuliuid
        INNER JOIN dbo.diaobo_sub s with(nolock) ON s.wuliuid = w.id
        INNER join dbo.diaobo_basket b with(nolock) on s.id=b.sub_id
        left join dbo.productinfo pf with(nolock) on pf.ppriceid = b.ppriceid
        WHERE isnull(w.danhaobind,0) = 0 and l.statistcs_categray =2
        GROUP BY l.inuser
        UNION ALL
        SELECT l.inuser,3 kinds,SUM(CASE WHEN pf.cid in (485) THEN b.basket_count WHEN isnull(pf.otherLimit,0) &amp; 2 = 2 THEN b.basket_count * 1 ELSE b.basket_count END)
        FROM dbo.wuliu w WITH(NOLOCK)
        INNER JOIN dbo.#temporaryWl l WITH(NOLOCK) ON w.id = l.wuliuid
        INNER JOIN dbo.sub s with(nolock) ON s.sub_id = w.danhaobind
        INNER join dbo.basket b with(nolock) on b.sub_id = s.sub_id
        left join dbo.productinfo pf with(nolock) on pf.ppriceid = b.ppriceid
        WHERE (w.subKinds=1 or w.wutype in (4,6)) and isnull(b.isdel,0) = 0 and l.statistcs_categray =2
        GROUP BY l.inuser
        UNION ALL
        SELECT l.inuser,4 kinds,COUNT(1) t
        FROM dbo.wuliu w WITH(NOLOCK)
        INNER JOIN dbo.#temporaryWl l WITH(NOLOCK) ON w.id = l.wuliuid
        WHERE (w.subKinds=3 or w.wutype in (5)) and l.statistcs_categray =2
        GROUP BY l.inuser
        UNION ALL
        SELECT l.inuser,5 kinds,COUNT(1) t
        FROM dbo.wuliu w WITH(NOLOCK)
        INNER JOIN dbo.#temporaryWl l WITH(NOLOCK) ON w.id = l.wuliuid
        INNER JOIN dbo.recover_toarea s with(nolock) ON s.wuliuid = w.id
        WHERE  l.statistcs_categray =2
        GROUP BY l.inuser
        UNION ALL
        SELECT l.inuser,6 kinds,COUNT(1) t
        FROM dbo.wuliu w WITH(NOLOCK)
        INNER JOIN dbo.#temporaryWl l WITH(NOLOCK) ON w.id = l.wuliuid
        WHERE isnull(w.danhaobind,0) != 0 AND w.subKinds NOT IN (1,2,3) AND w.wutype NOT IN (4,5,6,9) and l.statistcs_categray =2
        GROUP BY l.inuser
        ) t
        GROUP BY t.inuser
        ) t12 ON t12.inuser = u.ch999_name
        LEFT JOIN
        (
        SELECT s.inuser,SUM(CASE WHEN pf.cid in (485) THEN b.lcount WHEN isnull(pf.otherLimit,0) &amp; 2 = 2 THEN b.lcount * 1 ELSE b.lcount END) cnt FROM dbo.caigouInputSub s WITH(NOLOCK)
        INNER JOIN dbo.caigouInputBasket b WITH(NOLOCK) ON s.id = b.inputId
        INNER JOIN dbo.productinfo pf WITH(NOLOCK) ON pf.ppriceid = b.ppriceid
        inner join  dbo.caigou_sub  ss with(nolock) on ss.id= s.caigouId
        WHERE s.dtime BETWEEN #{param.startTime} and #{param.endTime}  and ss.kinds='wx'
        GROUP BY s.inuser
        ) t13 ON t13.inuser = u.ch999_name
        LEFT JOIN
        (
        SELECT c.inuser,SUM(CASE WHEN pf.cid in (485) THEN b.lcount WHEN isnull(pf.otherLimit,0) &amp; 2 = 2 THEN b.lcount * 1 ELSE b.lcount END) cnt
        FROM dbo.diaobo_sub s WITH(NOLOCK)
        INNER JOIN dbo.diaobo_basket b WITH(NOLOCK) ON s.id = b.sub_id
        INNER JOIN (
        SELECT * FROM
        (
        SELECT c.db_id,c.inuser,c.dtime,ROW_NUMBER() OVER(PARTITION BY c.db_id ORDER BY c.id DESC) rn
        FROM dbo.diaoboSub_comment c WITH(NOLOCK)
        WHERE c.operationKinds = 4
        ) t WHERE t.rn = 1
        ) c ON s.id = c.db_id
        left join dbo.productinfo pf with(nolock) on pf.ppriceid = b.ppriceid
        WHERE s.stats = 4 AND c.dtime BETWEEN #{param.startTime} and #{param.endTime} and s.kinds = 'wx'
        GROUP BY c.inuser
        ) t14 ON t14.inuser = u.ch999_name
        LEFT JOIN
        (
        SELECT c.inuser,SUM(CASE WHEN pf.cid in (485) THEN b.lcount WHEN isnull(pf.otherLimit,0) &amp; 2 = 2 THEN b.lcount * 1 ELSE b.lcount END) cnt
        from dbo.diaobo_sub s WITH(NOLOCK)
        INNER JOIN dbo.diaobo_basket b WITH(NOLOCK) ON s.id = b.sub_id
        INNER JOIN (
        SELECT * FROM
        (
        SELECT c.db_id,c.inuser,c.dtime,ROW_NUMBER() OVER(PARTITION BY c.db_id ORDER BY c.id DESC) rn
        FROM dbo.diaoboSub_comment c WITH(NOLOCK)
        WHERE c.operationKinds = 6
        ) t WHERE t.rn = 1
        ) c ON s.id = c.db_id
        left join dbo.productinfo pf with(nolock) on pf.ppriceid = b.ppriceid
        WHERE s.stats IN (3,4,5,6) AND s.kinds = 'wx' AND c.dtime BETWEEN #{param.startTime} and #{param.endTime} AND b.basket_id IS NULL
        GROUP BY c.inuser
        ) t15 ON t15.inuser = u.ch999_name
        LEFT JOIN
        (
        SELECT c.inuser,SUM(CASE WHEN pf.cid in (485) THEN b.lcount WHEN isnull(pf.otherLimit,0) &amp; 2 = 2 THEN b.lcount * 1 ELSE b.lcount END) cnt
        FROM dbo.diaobo_sub s WITH(NOLOCK)
        INNER JOIN dbo.diaobo_basket b WITH(NOLOCK) ON s.id = b.sub_id
        INNER JOIN (
        SELECT * FROM
        (
        SELECT c.db_id,c.inuser,c.dtime,ROW_NUMBER() OVER(PARTITION BY c.db_id ORDER BY c.id DESC) rn
        FROM dbo.diaoboSub_comment c WITH(NOLOCK)
        WHERE c.operationKinds = 6
        ) t WHERE t.rn = 1
        ) c ON s.id = c.db_id
        left join dbo.productinfo pf with(nolock) on pf.ppriceid = b.ppriceid
        WHERE s.stats IN (3,4,5,6) AND s.kinds = 'wx' AND c.dtime BETWEEN #{param.startTime} and #{param.endTime} AND b.basket_id IS NOT NULL
        GROUP BY c.inuser
        ) t16 ON t16.inuser = u.ch999_name
        <where>
            <include refid="whereCondition"></include>
        </where>
        ) t
    </select>
    <select id="searchSpec" resultType="com.jiuji.oa.stock.accessory.vo.res.SpecRes">
        SELECT id, ppriceid, spec
        from wuliu_calculate_statistics_spec wcss WITH(NOLOCK)
        order by create_time DESC
    </select>


    <sql id="whereCondition" >
        <if test="param.staffName != null and param.staffName != ''">
            and u.ch999_name = #{param.staffName}
        </if>
        <if test="param.staffId != null and param.staffId != ''">
            and u.ch999_id = #{param.staffId}
        </if>
        <if test="param.authorizeid != null and param.authorizeid != ''">
            and a.authorizeid = #{param.authorizeid}
        </if>
        <if test="param.departCode != null and param.departCode.size()>0 ">
            and EXISTS(SELECT 1 FROM dbo.departInfo d WITH(NOLOCK) WHERE  d.id = u.depart_id
            AND d.id IN
            <foreach collection="param.departCode" item="it" separator="," open="(" close=")">
                #{it}
            </foreach>
            )
        </if>
        <if test="param.kind1 != null and param.kind1.size()>0 ">
            and a.kind1 in
            <foreach collection="param.kind1" item="it" separator="," open="(" close=")">
                #{it}
            </foreach>
        </if>
        <if test="param.areaCode != null and param.areaCode.size()>0 ">
            and a.id in
            <foreach collection="param.areaCode" item="it" separator="," open="(" close=")">
                #{it}
            </foreach>
        </if>
        <if test="param.areaLevel != null and param.areaLevel.size()>0 ">
            and a.level1 in
            <foreach collection="param.areaLevel" item="it" separator="," open="(" close=")">
                #{it}
            </foreach>
        </if>
        <if test="param.iszaizhi != null and param.iszaizhi != ''">
            and u.iszaizhi = #{param.iszaizhi}
        </if>
    </sql>

</mapper>
