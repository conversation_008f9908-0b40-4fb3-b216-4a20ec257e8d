<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.verification.mapper.StockFundWaitingVerificationMapper">

    <select id="getMobileWaitingVerification" resultType="com.jiuji.oa.stock.verification.vo.res.MobilePageResVO">
        SELECT pm.id as mkc_id,pm.imei as imei, mcb.sub_id,pm.ppriceid ,p.product_name ,p.product_color,
        pm.insourceId2 as insourceId,owq.company_jc as insourceName,pm.inuser as submitUsername, pm.dtime as submitTime,
        pm.imeidate as instockTime ,pm.inbeihuoprice,pm.areaid
        FROM product_mkc pm WITH (NOLOCK)
        left join mkcCaiGouBasket mcb WITH (NOLOCK) on mcb.mkc_id = pm.id
        left join productinfo p WITH (NOLOCK) on pm.ppriceid =p.ppriceid
        left join Ok3w_qudao owq WITH (NOLOCK) on pm.insourceId2 =owq.id
        <where> pm.id in
            <foreach item="item" index="index" collection="mkcIdList" open="(" separator="," close=")">
                ${item}
            </foreach>
        </where>
        order by pm.id DESC
    </select>

    <select id="getMobileWaitingVerificationPage" resultType="com.jiuji.oa.stock.verification.vo.res.MobilePageResVO">
        SELECT pm.id as mkc_id, mcb.sub_id,pm.ppriceid ,p.product_name ,p.product_color,
        pm.insourceId2 as insourceId ,owq.company_jc as insourceName,pm.inuser as submitUsername, pm.dtime as submitTime,
        pm.imeidate as instockTime ,pm.inbeihuoprice,pm.areaid,ISNULL(smwv.forbidden_flag,0) as forbidden_flag
        FROM product_mkc pm WITH (NOLOCK)
        left join mkcCaiGouBasket mcb WITH (NOLOCK) on mcb.mkc_id = pm.id
        left join productinfo p WITH (NOLOCK) on pm.ppriceid =p.ppriceid
        left join Ok3w_qudao owq WITH (NOLOCK) on pm.insourceId2 =owq.id
        left join stock_mobile_waiting_verification smwv WITH (NOLOCK) on pm.id =smwv.mkc_id
        <where>not exists(
            select 1 from
            stock_verification_record_detail_mobile svrd with(nolock)
            where is_del = 0 AND
            svrd.mkc_id = pm.id) and pm.imei is not NULL and pm.dtime > '2022-07-19 00:00:00'
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==1">
                AND mcb.sub_id = #{req.searchValue}
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==2">
                AND pm.ppriceid = #{req.searchValue}
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==3">
                AND p.product_id = #{req.searchValue}
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==4">
                and p.product_name like '%' + #{req.searchValue}+'%'
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==5">
                AND pm.id = #{req.searchValue}
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==6">
                AND pm.imei = #{req.searchValue}
            </if>
            <if test="req.areaIdList!=null and req.areaIdList.size()>0">
                AND pm.areaid in
                <foreach item="item" index="index" collection="req.areaIdList" open="(" separator="," close=")">
                    ${item}
                </foreach>
            </if>
            <if test="req.stockChannelId != null and req.stockChannelId != ''">
                AND pm.insourceId2 = #{req.stockChannelId}
            </if>
            <if test="req.operationTimeType != null">
                <if test="req.startTime != null and req.endTime != null and req.operationTimeType == 1">
                    and pm.dtime between #{req.startTime} and #{req.endTime}
                </if>
                <if test="req.startTime != null and req.endTime != null and req.operationTimeType == 2 ">
                    and pm.imeidate between #{req.startTime} and #{req.endTime}
                </if>
            </if>
            <if test="req.forbiddenFlag != null and req.forbiddenFlag != ''">
                AND ISNULL(smwv.forbidden_flag,0)  = #{req.forbiddenFlag}
            </if>
        </where>
        order by pm.id DESC
    </select>

    <select id="getFundWaitingVerification" resultType="com.jiuji.oa.stock.verification.vo.res.FundPageResVO">
        SELECT sfwv.id, sfwv.order_type, sfwv.order_id, sfwv.fund_channel_id,owq.company_jc as fund_channel_name,
        sfwv.fund, sfwv.already_verify_fund, sfwv.not_verify_fund, sfwv.submit_ch999Id,
        sfwv.submit_ch999Name, sfwv.submit_time,ISNULL(sfwv.forbidden_flag,0) as forbidden_flag
        FROM stock_fund_waiting_verification sfwv WITH (NOLOCK)
        left join Ok3w_qudao owq WITH (NOLOCK) on sfwv.fund_channel_id =owq.id
        <where>AND sfwv.is_del = 0
            AND  sfwv.id in
            <foreach item="item" index="index" collection="fundIdList" open="(" separator="," close=")">
                ${item}
            </foreach>
        </where>
        order by sfwv.id
    </select>

    <select id="getFundWaitingVerificationPage" resultType="com.jiuji.oa.stock.verification.vo.res.FundPageResVO">
        SELECT sfwv.id, sfwv.order_type, sfwv.order_id, sfwv.fund_channel_id,owq.company_jc as fund_channel_name,
        sfwv.fund, sfwv.already_verify_fund, sfwv.not_verify_fund, sfwv.submit_ch999Id,
        sfwv.submit_ch999Name, sfwv.submit_time,ISNULL(sfwv.forbidden_flag,0) as forbidden_flag
        FROM stock_fund_waiting_verification sfwv WITH (NOLOCK)
        left join Ok3w_qudao owq WITH (NOLOCK) on sfwv.fund_channel_id =owq.id
        <where>AND sfwv.is_del = 0
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==1">
                AND sfwv.id = #{req.searchValue}
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==2">
                AND sfwv.order_id = #{req.searchValue} and order_type = 1
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==3">
                AND sfwv.order_id = #{req.searchValue} and order_type = 2
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==4">
                AND sfwv.order_id = #{req.searchValue} and order_type = 3
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==5">
                AND sfwv.order_id = #{req.searchValue} and order_type = 4
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==6">
                AND sfwv.submit_ch999Name LIKE CONCAT('%',#{req.searchValue},'%')
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==7">
                AND sfwv.order_id = #{req.searchValue} and order_type = 5
            </if>
            <if test="req.orderType != null and req.orderType != ''">
                AND sfwv.order_type = #{req.orderType}
            </if>
            <if test="req.fundChannelId != null and req.fundChannelId != ''">
                AND sfwv.fund_channel_id = #{req.fundChannelId}
            </if>
            <if test="req.forbiddenFlag != null and req.forbiddenFlag != ''">
                AND ISNULL(sfwv.forbidden_flag,0) = #{req.forbiddenFlag}
            </if>
        </where>
        order by sfwv.create_time desc
    </select>

    <select id="getRecordVerificationPage" resultType="com.jiuji.oa.stock.verification.vo.res.RecordPageResVO">
        SELECT * from (SELECT distinct svr.id, svr.total_verify_fund, svr.submit_ch999Id, svr.submit_ch999Name, svr.submit_time, svr.state
        FROM stock_verification_record svr WITH (NOLOCK)
        left join stock_verification_record_detail_mobile svrd WITH (NOLOCK) on svr.id = svrd.verification_record_id
        left join product_mkc pm WITH (NOLOCK) on pm.id = svrd.mkc_id
        left join mkcCaiGouBasket mcb WITH (NOLOCK) on mcb.mkc_id = pm.id
        left join productinfo p WITH (NOLOCK) on pm.ppriceid =p.ppriceid
        <where>AND svr.is_del = 0
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==1">
                AND mcb.sub_id = #{req.searchValue}
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==2">
                AND pm.ppriceid = #{req.searchValue}
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==3">
                AND p.product_id = #{req.searchValue}
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==4">
                AND p.product_name LIKE CONCAT('%',#{req.searchValue},'%')
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==5">
                AND pm.id = #{req.searchValue}
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==6">
                AND pm.imei = #{req.searchValue}
            </if>
            <if test="req.submitCh999Name != null and req.submitCh999Name != ''">
                AND svr.submit_ch999Name LIKE CONCAT('%',#{req.submitCh999Name},'%')
            </if>
            <if test="req.startTime != null and req.endTime != null">
                and svr.submit_time between #{req.startTime} and #{req.endTime}
            </if>
        </where>
        ) as temp order by temp.submit_time desc
    </select>

    <select id="getRecordVerificationCount" resultType="java.lang.Integer">
        SELECT count(distinct svr.id)
        FROM stock_verification_record svr WITH (NOLOCK)
        left join stock_verification_record_detail_mobile svrd WITH (NOLOCK) on svr.id = svrd.verification_record_id
        left join product_mkc pm WITH (NOLOCK) on pm.id = svrd.mkc_id
        left join mkcCaiGouBasket mcb WITH (NOLOCK) on mcb.mkc_id = pm.id
        left join productinfo p WITH (NOLOCK) on pm.ppriceid =p.ppriceid
        <where>AND svr.is_del = 0
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==1">
                AND mcb.sub_id = #{req.searchValue}
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==2">
                AND pm.ppriceid = #{req.searchValue}
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==3">
                AND p.product_id = #{req.searchValue}
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==4">
                AND p.product_name LIKE CONCAT('%',#{req.searchValue},'%')
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==5">
                AND pm.id = #{req.searchValue}
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==6">
                AND pm.imei = #{req.searchValue}
            </if>
            <if test="req.submitCh999Name != null and req.submitCh999Name != ''">
                AND svr.submit_ch999Name LIKE CONCAT('%',#{req.submitCh999Name},'%')
            </if>
            <if test="req.startTime != null and req.endTime != null">
                and svr.submit_time between #{req.startTime} and #{req.endTime}
            </if>
        </where>
    </select>

    <select id="getCanVerifyFund" resultType="com.jiuji.oa.stock.verification.vo.res.FundPageResVO">
        SELECT sfwv.id, sfwv.order_id, sfwv.already_verify_fund, sfwv.submit_ch999Name, sfwv.fund_channel_id,
               owq.company_jc as fund_channel_name, sfwv.not_verify_fund,
        sfwv.submit_time, sfwv.fund, sfwv.submit_ch999Id, sfwv.order_type
        FROM stock_fund_waiting_verification sfwv with(nolock)
        left join Ok3w_qudao owq WITH (NOLOCK) on sfwv.fund_channel_id =owq.id
        <where>AND sfwv.not_verify_fund != 0 AND sfwv.is_del = 0 and ISNULL(sfwv.forbidden_flag,0) = 0 AND sfwv.fund_channel_id in
            <foreach item="item" index="index" collection="insourceidList" open="(" separator="," close=")">
                ${item}
            </foreach>
        </where>
        order by sfwv.id
    </select>
    <select id="getVerificationSum" resultType="java.math.BigDecimal">
        SELECT SUM(svr.total_verify_fund)
        FROM stock_verification_record svr WITH (NOLOCK)
        left join stock_verification_record_detail_mobile svrd WITH (NOLOCK) on svr.id = svrd.verification_record_id
        left join product_mkc pm WITH (NOLOCK) on pm.id = svrd.mkc_id
        left join mkcCaiGouBasket mcb WITH (NOLOCK) on mcb.mkc_id = pm.id
        left join productinfo p WITH (NOLOCK) on pm.ppriceid =p.ppriceid
        <where>AND svr.is_del = 0
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==1">
                AND mcb.sub_id = #{req.searchValue}
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==2">
                AND pm.ppriceid = #{req.searchValue}
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==3">
                AND p.product_id = #{req.searchValue}
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==4">
                AND p.product_name LIKE CONCAT('%',#{req.searchValue},'%')
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==5">
                AND pm.id = #{req.searchValue}
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==6">
                AND pm.imei = #{req.searchValue}
            </if>
            <if test="req.submitCh999Name != null and req.submitCh999Name != ''">
                AND svr.submit_ch999Name LIKE CONCAT('%',#{req.submitCh999Name},'%')
            </if>
            <if test="req.startTime != null and req.endTime != null">
                and svr.submit_time between #{req.startTime} and #{req.endTime}
            </if>
        </where>
    </select>

</mapper>
