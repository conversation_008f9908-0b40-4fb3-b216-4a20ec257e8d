<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~
  ~      Copyright © 2006 - 2020 九机网 All Rights Reserved
  ~
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.nc.channel.mapper.ChannelKindLinkMapper">

    <resultMap id="channelKindLinkMap" type="com.jiuji.oa.nc.channel.entity.ChannelKindLink">
        <id property="id" column="id"/>
        <result property="channelId" column="channel_id"/>
        <result property="kind" column="kind"/>
        <result property="businessType" column="business_type"/>
        <result property="settlementType" column="settlement_type"/>
        <result property="partnerCompany" column="partner_company"/>
        <result property="subject" column="subject"/>
        <result property="margin" column="margin"/>
        <result property="depositHasReceipt" column="deposit_has_receipt"/>
        <result property="invoicingFlag" column="invoicing_flag"/>
        <result property="channelState" column="channel_state"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>
    <select id="getLinkNull" resultType="com.jiuji.oa.nc.channel.vo.res.ChannelPageVo">
        select qudao.id, company
        from Ok3w_qudao qudao with (nolock)
        left join channel_kind_link link with(nolock)
        on link.channel_id = qudao.id
        where NOT EXISTS (select * from channel_kind_link where link.channel_id = qudao.id)
    </select>
    <select id="getPickingKingChannel" resultType="com.jiuji.oa.nc.channel.vo.dto.PickingKingChannel">
        select qudao.id,
               qudao.source_type as sourceType,
               qudao.company,
               qudao.authorizeid as authorizeId,
               qudao.adddate     as addDate
        from Ok3w_qudao qudao with (nolock )
                 left join(
            select company, count(company) as companyCount, authorizeid
            from Ok3w_qudao with (nolock )
            where source_type = 1
            group by company, authorizeid
        ) t
        on t.company = qudao.company
        where t.companyCount
            > 1
          and qudao.authorizeid = t.authorizeid
          and qudao.source_type = 1
    </select>
    <select id="getPickingKingChannelState"
            resultType="com.jiuji.oa.nc.channel.vo.dto.PickingKingChannelState">

        select qudao.id as id, link.channel_state as channelState,link.kind
        from Ok3w_qudao qudao with (nolock)
         left join channel_kind_link link
        with (nolock)
        on link.channel_id = qudao.id
        where qudao.source_type = 1
    </select>
    <select id="selectChannelByKindAndState" resultType="com.jiuji.oa.nc.channel.entity.Ok3wQudao">
        select *
        from Ok3w_qudao qudao with(nolock)
                 left join channel_kind_link link with(nolock) on link.channel_id = qudao.id
        where link.kind = #{kind}
          and link.channel_state = #{channelState}
    </select>
    <select id="getDesensitizeChannel" resultType="java.lang.Integer">
        select
            distinct channel_id
        from
            dbo.channel_kind_link l with(nolock)
        where
            desensitize_flag = 1
          and l.del_flag = 0
    </select>
    <select id="selectCompanyType" resultType="com.jiuji.oa.nc.channel.vo.dto.BeAccountConfigDto">
        select  Id as id,case when isnull(ShortName,'')='' THEN  AccountName  else ShortName  end  as accountName from  dbo.BeAccountConfig  b with(nolock)
        left join  dbo.ch999_user u with(nolock) on b.InUser=u.ch999_id
        WHERE PayAccountNameType=0
    </select>
    <select id="selectCompanyTypeByIdList" resultType="java.lang.String">
        select AccountName as accountName from  dbo.BeAccountConfig  b with(nolock)
        left join  dbo.ch999_user u with(nolock) on b.InUser=u.ch999_id
        WHERE PayAccountNameType=0
        and Id in
        <foreach collection="idList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="selectChannelByKindAndDesensitizeFlag"
            resultType="com.jiuji.oa.nc.channel.entity.ChannelKindLink">
        select * from channel_kind_link l with(nolock)
        where l.kind = #{kind}
        and l.desensitize_flag = #{desensitizeFlag}
        and isnull(l.del_flag,0) = 0
    </select>

    <select id="selectChannelKindLinkByCondition" 
            resultType="com.jiuji.oa.nc.channel.entity.ChannelKindLink">
        select channel_id, kind 
        from channel_kind_link t2 with(nolock)
        where isnull(t2.del_flag,0) = 0
        <if test="params.kind != null">
            and t2.kind = #{params.kind}
        </if>
        <if test="params.invoicingFlag != null">
            and t2.invoicing_flag = #{params.invoicingFlag}
        </if>
        <if test="params.passFlag != null">
            and t2.channel_state = #{params.passFlag}
        </if>
        <if test="params.cooperationId != null">
            and #{params.cooperationId} IN (SELECT TRIM(value) FROM STRING_SPLIT(t2.partner_company, ','))
        </if>
    </select>

</mapper>
