<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~
  ~      Copyright © 2006 - 2020 九机网 All Rights Reserved
  ~
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.nc.channel.mapper.QudaoaccountsMapper">

  <resultMap id="qudaoaccountsMap" type="com.jiuji.oa.nc.channel.entity.Qudaoaccounts">
                  <id property="id" column="id"/>
                        <result property="accountnumber" column="accountNumber"/>
                        <result property="username" column="userName"/>
                        <result property="openingbank" column="openingBank"/>
                        <result property="dtime" column="dTime"/>
                        <result property="isgs" column="isGS"/>
                        <result property="isenable" column="isEnable"/>
                        <result property="isdel" column="isDel"/>
                        <result property="qudaoid" column="qudaoID"/>
                        <result property="city" column="city"/>
                        <result property="banknumber" column="bankNumber"/>
                        <result property="issamecity" column="isSameCity"/>
                        <result property="linkId" column="link_id"/>
            </resultMap>
    <select id="getZtidById" resultType="java.lang.Integer">
       select ztid from authorize(nolock ) where id=#{id}
    </select>
</mapper>
