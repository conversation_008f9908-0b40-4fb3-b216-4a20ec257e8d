<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.nc.oaapp.mapper.AppToolMenuMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.nc.oaapp.po.AppToolMenu">
        <id column="id" property="id" />
        <result column="parent_id" property="parentId" />
        <result column="name" property="name" />
        <result column="icon" property="icon" />
        <result column="level" property="level" />
        <result column="link" property="link" />
        <result column="area" property="area" />
        <result column="ranks" property="ranks" />
        <result column="roles" property="roles" />
        <result column="order_rank" property="orderRank" />
        <result column="remark" property="remark" />
        <result column="area_type" property="areaType" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="is_del" property="isDel" />
    </resultMap>
<!--    <select id="getAppToolMenu" resultType="com.jiuji.oa.nc.oaapp.po.AppToolMenu">-->
<!--        select * from app_tool_menu where is_del = 0-->
<!--        and (area is null or CONCAT(',', area, ',') like CONCAT('%', ',', #{oaUserBO.areaId}, ',', '%'))-->
<!--        and (area_type is null or area_type = #{areaType})-->
<!--    </select>-->

    <select id="getAppToolMenu" resultType="com.jiuji.oa.nc.oaapp.po.AppToolMenu">
        SELECT
            t.*
        FROM
            app_tool_menu t
                LEFT JOIN app_tool_platform_menu p ON t.id = p.menu_id
        WHERE
            t.is_del = 0
          AND p.is_delete = 0
          AND (
                area IS NULL
                OR CONCAT( ',', area, ',' ) LIKE CONCAT( '%', ',',  #{oaUserBO.areaId}, ',', '%' ))
          AND ( area_type IS NULL OR area_type = #{areaType} )
          AND p.platform_name = #{oaUserBO.platformName}
          AND #{oaUserBO.version} BETWEEN p.minimum_dition
          AND p.highest_dition
  </select>

</mapper>
