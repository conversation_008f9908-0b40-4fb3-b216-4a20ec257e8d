<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.nc.oaapp.mapper.SysConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.nc.oaapp.po.SysConfig">
        <id column="id" property="id" />
        <result column="dsc" property="dsc" />
        <result column="name" property="name" />
        <result column="value" property="value" />
        <result column="code" property="code"/>
        <result column="xtenant" property="xtenant"/>
        <result column="authId" property="authId"/>
        <result column="areaids" property="areaids"/>
        <result column="kemu" property="kemu"/>
        <result column="rank" property="rank"/>
        <result column="isdel" property="isdel"/>
        <result column="fzhsType" property="fzhsType"/>
    </resultMap>

    <select id="getAuthorizeInfoList" resultType="com.jiuji.oa.nc.oaapp.bo.AuthorizeInfo">
        select id,dcAreaId,HQAreaId,D1AreaId,H1AreaId from authorize with(nolock)
    </select>
  <select id="getCompany" resultType="java.lang.String">
      SELECT
      top 1 [value]
      FROM
          dbo.sysconfig with(nolock)
      where [code]=3
  </select>
    <select id="getXtenantId" resultType="java.lang.Long">
        SELECT
            top 1 [xtenant]
        FROM
            dbo.sysconfig with(nolock)
        where [code]=4
    </select>
    <select id="getByCodeAndXtenant" resultType="com.jiuji.oa.nc.oaapp.po.SysConfig">
        SELECT
            top 1 *
        FROM
            dbo.sysconfig with(nolock)
        where [code] = #{code}
          and xtenant = #{xtenant}
          and isnull(isdel,0) = 0
    </select>

    <select id="getByCodeAndXtenantV2" resultType="com.jiuji.oa.nc.oaapp.po.SysConfig">
        SELECT
             *
        FROM
            dbo.sysconfig with(nolock)
        where [code] = #{code}
          and isnull(isdel,0) = 0
          <if test="xtenant != null">
              and xtenant = #{xtenant}
          </if>
    </select>
    <update id="updateValueByCode">
        update sysConfig set value = #{value} WHERE code in(168,165,166,167,200)
    </update>
    <insert id="sqlServerBatchInsert" useGeneratedKeys="false">
        insert into sysConfig ( dsc, name, value, code , authId ) values
        <foreach collection="list" item="item" index="index" open="(" separator="),(" close=")">
         #{item.dsc}, #{item.name}, #{item.value}, #{item.code}, #{item.authId}
        </foreach>
    </insert>
</mapper>
