<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.displayproductinfo.mapper.MkcDelLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.stock.displayproductinfo.entity.MkcDelLogEntity">
        <id column="id" property="id" />
        <result column="kinds" property="kinds" />
        <result column="check2user" property="check2user" />
        <result column="dtime" property="dtime" />
        <result column="check1user" property="check1user" />
        <result column="ppriceid" property="ppriceid" />
        <result column="check1" property="check1" />
        <result column="mkc_id" property="mkcId" />
        <result column="frareaid" property="frareaid" />
        <result column="check2" property="check2" />
        <result column="comment" property="comment" />
        <result column="price1" property="price1" />
        <result column="areaid" property="areaid" />
        <result column="youhuiPrice" property="youhuiPrice" />
        <result column="check1dtime" property="check1dtime" />
        <result column="inuser" property="inuser" />
        <result column="check2dtime" property="check2dtime" />
        <result column="area" property="area" />
        <result column="price2" property="price2" />
        <result column="pzid" property="pzid" />
        <result column="lpToAreaId" property="lpToAreaId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        kinds, check2user, dtime, check1user, ppriceid, check1, mkc_id, frareaid, check2, comment, price1, areaid, youhuiPrice, check1dtime, inuser, check2dtime, area, price2, pzid, lpToAreaId, id
    </sql>

</mapper>
