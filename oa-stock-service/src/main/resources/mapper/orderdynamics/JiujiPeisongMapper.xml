<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.orderdynamics.mapper.JiujiPeisongMapper">
    <sql id="whereSql">
        and w.stats in (3)
        and datediff(day,w.dtime,getdate()) &lt; 30
        and isnull(w.nu, '')  = ''
    </sql>

    <select id="selectJiujiPeisongWuLiuidByPaijianren" resultType="java.lang.Long">
        select id
        from wuliu w with(nolock)
        where stats = '3'
        and dtime &gt; DATEADD(DAY, 0, DATEDIFF(DAY, 0, GETDATE()))
        <where>
            <if test="req.paijianren != null and req.paijianren!='' ">
                and paijianren = #{paijianren}
            </if>
        </where>
    </select>

    <select id="selectJiujiPeisongPaijianrenByPaijianren" resultType="java.lang.String">
        select distinct paijianren
        from wuliu w with(nolock)
        where stats = '3'
          and dtime &gt; DATEADD(DAY, 0, DATEDIFF(DAY, 0, GETDATE()))
          and paijianren is not null
    </select>
    <select id="selectWuLiuByPaijianren" resultType="com.jiuji.oa.wuliu.entity.WuLiuEntity">
        select w.*
        from wuliu w with(nolock)
        where w.paijianren = #{paijianren}
        and w.wutype in (1)
        and exists(select 1 from dbo.mkc_toarea m with(nolock) left join dbo.product_mkc k with(nolock) on m.mkc_id=k.id where m.wuliuid=w.id and k.basket_id is not null)
        <include refid="whereSql"/>
        union
        select w.*
        from wuliu w with(nolock)
        where w.paijianren = #{paijianren}
        and w.wutype in (1)
        and exists(select 1 from dbo.diaobo_sub s with(nolock) left join dbo.diaobo_basket b with(nolock) on s.id=b.sub_id where s.wuliuid=w.id and b.basket_id is not null and s.stats in (3))
        <include refid="whereSql"/>
        union
        select w.*
        from wuliu w with(nolock)
        join sub s with(nolock) on w.danhaobind = s.sub_id and w.wutype in (4,6)
        where w.paijianren = #{paijianren}
        and s.sub_check in (1,2,6)
        <include refid="whereSql"/>
        union
        select w.*
        from wuliu w with(nolock)
        join recover_marketInfo s with(nolock) on w.danhaobind = s.sub_id and w.wutype in (9)
        where w.paijianren = #{paijianren}
        and s.sub_check in (1,2,6)
        <include refid="whereSql"/>
    </select>
    <select id="selectPaijianrenIdByWuliuId" resultType="java.lang.String">
        select top 1 ch.ch999_id
        from wuliu w with(nolock)
        left join ch999_user ch with(nolock) on w.paijianren = ch.ch999_name
        where w.id = #{wuliuId}
    </select>
</mapper>
