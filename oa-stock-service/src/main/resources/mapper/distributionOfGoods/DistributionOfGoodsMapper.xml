<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.distributionOfGoods.mapper.DistributionOfGoodsServiceMapper">

    <sql id="commonWhere">
        <if test="searchCriteriaVO.salesStartTime !=null and searchCriteriaVO.salesEndTime !=null ">
                and s.tradeDate1 between #{searchCriteriaVO.salesStartTime} and #{searchCriteriaVO.salesEndTime}
        </if>
        <if test="searchCriteriaVO.searchTermType==1">
            and info.ppriceid in
            <foreach collection="searchCriteriaVO.searchTermValue" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="searchCriteriaVO.searchTermType==2">
            and info.product_id in
            <foreach collection="searchCriteriaVO.searchTermValue" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="searchCriteriaVO.kind1List!=null and searchCriteriaVO.kind1List.size()>0">
            and area.kind1 in
            <foreach collection="searchCriteriaVO.kind1List" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="searchCriteriaVO.areaIdList!=null and searchCriteriaVO.areaIdList.size()>0">
            and area.id in
            <foreach collection="searchCriteriaVO.areaIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="searchCriteriaVO.attributeList!=null and searchCriteriaVO.attributeList.size()>0">
            and area.attribute in
            <foreach collection="searchCriteriaVO.attributeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>


    </sql>
    <select id="selectReportFormProduct" resultType="com.jiuji.oa.stock.distributionOfGoods.vo.ProductDetailVO">
    select product.ppriceid, product.product_name, product.product_color, ISNULL(t.basketCount,0) as basketCount
        from productinfo product with (nolock)
        left join (
    select info.ppriceid, sum(case when b.ismobile=1 then 1 else b.basket_count end) as basketCount
    from basket b with (nolock)
             left join sub s with (nolock) on b.sub_id = s.sub_id
             left join areainfo area with (nolock) on area.id = s.areaid
             left join productinfo info with (nolock) on info.ppriceid = b.ppriceid
             left join product_mkc k with (nolock)  on k.basket_id = b.basket_id
        where s.sub_check = 3
    and isnull(b.isdel, 0) = 0
    and isnull(k.mouldFlag,0) = 0
    and not EXISTS (SELECT 1 FROM xc_mkc xm with(nolock) where xm.mkc_id = k.id)
     <include refid="commonWhere"/>
    group by info.ppriceid) t
    on t.ppriceid = product.ppriceid
    where
        <if test="searchCriteriaVO.searchTermType==1">
             product.ppriceid in
            <foreach collection="searchCriteriaVO.searchTermValue" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="searchCriteriaVO.searchTermType==2">
             product.product_id in
            <foreach collection="searchCriteriaVO.searchTermValue" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="selectReportFormStock" resultType="com.jiuji.oa.stock.distributionOfGoods.vo.StockDetailVO">
        select ppriceid,count(ppriceid) as stockCount from product_mkc k with(nolock)
        where kc_check in
          <foreach collection="searchStockDetailVO.kcCheckList" index="index" item="item" open="(" separator="," close=")">
              #{item}
          </foreach>
          and basket_id is null
          and areaid = #{searchStockDetailVO.transferOutAreaId}
          and ppriceid in
          <foreach collection="searchStockDetailVO.ppidList" index="index" item="item" open="(" separator="," close=")">
              #{item}
          </foreach>
          and isnull(mouldFlag,0) = 0 and not EXISTS (SELECT 1 FROM xc_mkc xm with(nolock) where xm.mkc_id = k.id)
        group by ppriceid
    </select>

    <select id="selectReportFormStockV2" resultType="com.jiuji.oa.stock.distributionOfGoods.vo.StockDetailVO">
        select info.ppriceid,t1.stockCount as stockCountOnTheWay,t2.stockCount  from productinfo info  with(nolock)
        left join (select ppriceid,count(ppriceid) as stockCount from product_mkc k with(nolock)
        where kc_check in (10)
          and basket_id is null
          and isnull(mouldFlag,0) = 0
          and areaid = #{searchStockDetailVO.transferOutAreaId}
          and not exists(seLect 1 FROM xc_mkc xm with(nolock) where xm.mkc_id = k.id)
          and not exists(seLect 1 from mkc_dellogs md with(nolock) where kinds in('zf','h1','h2') and check2 is null and md.mkc_id=k.id )
          and not exists(select 1 from mkc_toarea mt with(nolock) where recivedtime is null and mt.mkc_id=k.id  )
        and ppriceid in
        <foreach collection="searchStockDetailVO.ppidList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by ppriceid ) t1 on t1.ppriceid=info.ppriceid
            left join ( select ppriceid,count(ppriceid) as stockCount from product_mkc k with(nolock)
            where kc_check in (3,2)
            and basket_id is null
            and isnull(mouldFlag,0) = 0
            and areaid = #{searchStockDetailVO.transferOutAreaId}
            and not exists(seLect 1 FROM xc_mkc xm with(nolock) where xm.mkc_id = k.id)
            and not exists(seLect 1 from mkc_dellogs md with(nolock) where kinds in('zf','h1','h2') and check2 is null and md.mkc_id=k.id )
            and ppriceid in
        <foreach collection="searchStockDetailVO.ppidList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
       group by ppriceid) t2 on t2.ppriceid=info.ppriceid
       where
         info.ppriceid in
        <foreach collection="searchStockDetailVO.ppidList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>



    <select id="selectStoreSales" resultType="com.jiuji.oa.stock.distributionOfGoods.vo.StoreSales">
        select s.areaid as areaId, isnull(sum(case when b.ismobile=1 then 1 else b.basket_count end),0)  as storeSum
        from basket b with (nolock)
         left join sub s with (nolock) on b.sub_id = s.sub_id
        left join product_mkc k with (nolock) on k.basket_id = b.basket_id
        left join areainfo a with (nolock) on a.id = s.areaid
          where s.sub_check = 3
          and isnull(b.isdel, 0) = 0
          and isnull(k.mouldFlag,0) = 0
          and not EXISTS (SELECT 1 FROM xc_mkc xm with(nolock) where xm.mkc_id = k.id)
          and b.ppriceid in
        <foreach collection="SearchStoreSales.ppidList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="SearchStoreSales.areaIdList!=null and SearchStoreSales.areaIdList.size()>0">
            and s.areaid in
            <foreach collection="SearchStoreSales.areaIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="SearchStoreSales.kind1List!=null and SearchStoreSales.kind1List.size()>0">
            and a.kind1 in
            <foreach collection="SearchStoreSales.kind1List" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="SearchStoreSales.attributeList!=null and SearchStoreSales.attributeList.size()>0">
            and a.attribute in
            <foreach collection="SearchStoreSales.attributeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
          and s.tradeDate1 between #{SearchStoreSales.salesStartTime} and #{SearchStoreSales.salesEndTime}
        group by s.areaid

    </select>
    <select id="selectDropDownStock" resultType="com.jiuji.oa.stock.distributionOfGoods.vo.StoreSales">
        select  areaid as areaId,isnull(count(ppriceid),0)  as storeSum from product_mkc k with (nolock)
        where kc_check in
        <foreach collection="dropDownDetailVO.kcCheckList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and basket_id is null
        and isnull(mouldFlag,0) = 0
        and not exists (SELECT 1 FROM xc_mkc xm with(nolock) where xm.mkc_id = k.id)
        and not exists(seLect 1 from mkc_dellogs md with(nolock) where kinds in('zf','h1','h2') and check2 is null and md.mkc_id=k.id )
        <if test="dropDownDetailVO.type!=null and dropDownDetailVO.type==3">
            and not exists(select 1 from mkc_toarea mt with(nolock) where recivedtime is null and mt.mkc_id=k.id  )
        </if>
        <if test="dropDownDetailVO.transferOutAreaIdList!=null and dropDownDetailVO.transferOutAreaIdList.size()>0">
            and areaid in
            <foreach collection="dropDownDetailVO.transferOutAreaIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        and ppriceid in
        <foreach collection="dropDownDetailVO.ppidList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

        group by areaid

    </select>
    <select id="selectGoodsInStock" resultType="com.jiuji.oa.stock.distributionOfGoods.vo.GoodsInStockVO">
        select sum(b.basket_count) as allCount, s.areaid, sum(case when iskc = 1 then basket_count else 0 end) kcCount
        from dbo.basket b with (nolock)
        left join dbo.sub s with (nolock) on b.sub_id = s.sub_id
        left join dbo.productinfo p with (nolock) on b.ppriceid = p.ppriceid
        left join areainfo a with (nolock) on a.id=s.areaid
        where b.ismobile = 1
        and isnull(b.isdel, 0) = 0
        and s.sub_check = 3
        and basket_count <![CDATA[ < ]]>5
        and isnull(b.type, 0) <![CDATA[ <> ]]> 22
        <if test="searchGoodsInStockVO.kind1List!=null and searchGoodsInStockVO.kind1List.size()>0">
            and a.kind1 in
            <foreach collection="searchGoodsInStockVO.kind1List" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="searchGoodsInStockVO.attributeList!=null and searchGoodsInStockVO.attributeList.size()>0">
            and a.attribute in
            <foreach collection="searchGoodsInStockVO.attributeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        and not exists(select 1
        from (select count(b.basket_count) counts, s.userid, cast(s.tradeDate1 as date) dtime
        from dbo.sub s with (nolock)
        join dbo.basket b with (nolock) on s.sub_id = b.sub_id
        where isnull(b.isdel, 0) = 0
        and b.ismobile = 1
        and s.sub_check = 3
        and s.tradeDate1 between #{searchGoodsInStockVO.salesStartTime} and #{searchGoodsInStockVO.salesEndTime}
        group by s.userid, cast(s.tradeDate1 as date)
        having count(b.basket_count) >= 3) aa
        where aa.userid = s.userid
        and cast(s.tradeDate1 as date) = aa.dtime)
        and s.tradeDate1 between #{searchGoodsInStockVO.salesStartTime} and #{searchGoodsInStockVO.salesEndTime}
        <if test="searchGoodsInStockVO.ppidList!=null and searchGoodsInStockVO.ppidList.size()>0">
        and b.ppriceid in
            <foreach collection="searchGoodsInStockVO.ppidList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="searchGoodsInStockVO.dropDownAreaIdList!=null and searchGoodsInStockVO.dropDownAreaIdList.size()>0">
            and s.areaId in
            <foreach collection="searchGoodsInStockVO.dropDownAreaIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>

        </if>
        group by s.areaid

    </select>



    <select id="selectAreaInfo" resultType="com.jiuji.oa.stock.distributionOfGoods.vo.AreaInfoVO">
        select code,parent_code,name,rank from AreaList  with (nolock ) where code in
        <foreach collection="codeList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="selectStoreSalesNoAreaIds" resultType="com.jiuji.oa.stock.distributionOfGoods.vo.StoreSales">
        select id  as areaId ,0 as storeSum  from areainfo with (nolock ) where ispass='true'
        <if test="areaIds!=null and areaIds.size()>0">
            and id not in
            <foreach collection="areaIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>

        </if>
        <if test="kind1List!=null and kind1List.size()>0" >
            and kind1 in
            <foreach collection="kind1List" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="attributeList!=null and attributeList.size()>0">
            and attribute in
            <foreach collection="attributeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

    </select>
    <select id="selectStoreSalesAreaIds" resultType="com.jiuji.oa.stock.distributionOfGoods.vo.StoreSales">
        select id  as areaId ,0 as storeSum  from areainfo with (nolock ) where ispass='true'
        <if test="areaIds!=null and areaIds.size()>0">
            and id in
            <foreach collection="areaIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="kind1List!=null and kind1List.size()>0" >
            and kind1 in
            <foreach collection="kind1List" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="attributeList!=null and attributeList.size()>0">
            and attribute in
            <foreach collection="attributeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="selectStoreSalesV2" resultType="com.jiuji.oa.stock.distributionOfGoods.vo.StoreSalesV2">
        select s.areaid as areaId,b.ismobile, b.basket_count as basketCount
        from basket b with (nolock)
        left join sub s with (nolock) on b.sub_id = s.sub_id
        left join product_mkc k with (nolock) on k.basket_id = b.basket_id
        left join areainfo a with (nolock) on a.id = s.areaid
        where s.sub_check = 3
        and isnull(b.isdel, 0) = 0
        and isnull(k.mouldFlag,0) = 0
        and not EXISTS (SELECT 1 FROM xc_mkc xm with(nolock) where xm.mkc_id = k.id)
        and b.ppriceid in
        <foreach collection="SearchStoreSales.ppidList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="SearchStoreSales.areaIdList!=null and SearchStoreSales.areaIdList.size()>0">
            and s.areaid in
            <foreach collection="SearchStoreSales.areaIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="SearchStoreSales.kind1List!=null and SearchStoreSales.kind1List.size()>0">
            and a.kind1 in
            <foreach collection="SearchStoreSales.kind1List" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="SearchStoreSales.attributeList!=null and SearchStoreSales.attributeList.size()>0">
            and a.attribute in
            <foreach collection="SearchStoreSales.attributeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        and s.tradeDate1 between #{SearchStoreSales.salesStartTime} and #{SearchStoreSales.salesEndTime}

    </select>

</mapper>