<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.rareproduct.mapper.RareProductConfigMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.stock.rareproduct.entity.RareProductConfig">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="productid" column="productid" jdbcType="INTEGER"/>
            <result property="isnotice" column="isnotice" jdbcType="BIT"/>
            <result property="inuser" column="inuser" jdbcType="VARCHAR"/>
            <result property="intime" column="intime" jdbcType="TIMESTAMP"/>
            <result property="limitArea" column="limitArea" jdbcType="VARCHAR"/>
            <result property="buyLimit" column="buyLimit" jdbcType="BIT"/>
            <result property="buyLimitNum" column="buyLimitNum" jdbcType="INTEGER"/>
            <result property="autoTransfer" column="autoTransfer" jdbcType="BIT"/>
    </resultMap>

    <resultMap id="RareProductConfigAddOrUpdateResMap" type="com.jiuji.oa.stock.rareproduct.vo.res.RareProductConfigAddOrUpdateRes">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="productid" column="productid" jdbcType="INTEGER"/>
        <result property="isnotice" column="isnotice" jdbcType="BIT"/>
        <result property="isManualTransfer" column="is_manual_transfer" jdbcType="BIT"/>
        <result property="inuser" column="inuser" jdbcType="VARCHAR"/>
        <result property="intime" column="intime" jdbcType="TIMESTAMP"/>
        <result property="limitArea" column="limitArea" jdbcType="VARCHAR"/>
        <result property="buyLimit" column="buyLimit" jdbcType="BIT"/>
        <result property="buyLimitNum" column="buyLimitNum" jdbcType="INTEGER"/>
        <result property="autoTransfer" column="autoTransfer" jdbcType="BIT"/>
        <result property="xtenants" column="xtenants" jdbcType="VARCHAR"/>
        <collection property="productInfoList" javaType="java.util.List" ofType="com.jiuji.oa.stock.rareproduct.vo.res.RareProductInfoRes">
            <result property="skuId" column="ppriceid" jdbcType="INTEGER"/>
            <result property="productName" column="productName" jdbcType="VARCHAR"/>
            <result property="productColor" column="productColor" jdbcType="VARCHAR"/>
            <result property="counts" column="counts" jdbcType="INTEGER"/>
        </collection>
    </resultMap>

    <sql id="Base_Column_List">
        id,productid,isnotice,
        inuser,intime,limitArea,
        buyLimit,buyLimitNum,autoTransfer
    </sql>
    <select id="selectByPage" resultType="com.jiuji.oa.stock.rareproduct.vo.res.RareProductConfigPageRes">
        select r.*,
               r.productid as ppriceid,
               p.product_name as productName,
               p.product_color as productColor,
               case when p.ismobile1 = 1 then isnull(k.counts,0) else isnull(kc.counts1,0) end counts,
               p.ismobile1 as ismobile
            from dbo.rareProductConfig r with(nolock)
            left join productinfo p with(nolock) on r.productid=p.ppriceid
            left join (select sum([count]) counts,ppriceid from product_mkcjh with(nolock) group by ppriceid ) k on k.ppriceid=r.productid
            left join (SELECT SUM([lcount]) AS counts1,ppriceid FROM product_kc with(nolock) group by ppriceid) kc on kc.ppriceid=r.productid
            <where>
                <if test="req.cidList != null and req.cidList.size()>0">
                    and p.cid in
                    <foreach collection="req.cidList" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </if>
                <if test="req.brandIdList != null and req.brandIdList.size()>0">
                    and p.brandID in
                    <foreach collection="req.brandIdList" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </if>
                <if test="req.searchKind == 1 and req.keyWords != null and req.keyWords != ''" >
                    and p.product_name like CONCAT('%',#{req.keyWords},'%')
                </if>
                <if test="req.searchKind == 2 and req.keyWords != null and req.keyWords != ''">
                    and r.productid = #{req.keyWords}
                </if>
                <if test="req.searchKind == 3 and req.keyWords != null and req.keyWords != ''">
                    and p.productid = #{req.keyWords}
                </if>
            </where>
    </select>
    <select id="getRareProductConfigAddOrUpdateResById"
            resultMap="RareProductConfigAddOrUpdateResMap">
        select r.*,
               r.productid as ppriceid,
               p.product_name as productName,
               p.product_color as productColor,
               case when p.ismobile1 = 1 then isnull(k.counts,0) else isnull(kc.counts1,0) end counts,
               p.ismobile1 as ismobile
        from dbo.rareProductConfig r with(nolock)
            left join productinfo p with(nolock) on r.productid=p.ppriceid
            left join (select sum([count]) counts,ppriceid from product_mkcjh with(nolock) group by ppriceid ) k on k.ppriceid=r.productid
            left join (SELECT SUM([lcount]) AS counts1,ppriceid FROM product_kc with(nolock) group by ppriceid) kc on kc.ppriceid=r.productid
        where r.id = #{id}
    </select>
    <select id="searchProduct" resultType="com.jiuji.oa.stock.productkc.vo.req.ProductConfigVo">
        <choose>
            <when test="searchProductVo.configType == 1">
                select top 50 p.ppriceid,p.product_name ,p.product_color
                from productinfo p with(nolock)
                where p.ismobile1 = 1
                <choose>
                    <when test="searchProductVo.keys != null and !searchProductVo.keys.isEmpty()">
                        and p.ppriceid in
                        <foreach collection="searchProductVo.keys" item="key" open="(" close=")" separator=",">
                            #{key}
                        </foreach>
                    </when>
                    <when test="searchProductVo.numberKey != null">
                        and p.ppriceid = #{searchProductVo.numberKey}
                    </when>
                    <otherwise>
                        and p.product_name like concat(#{searchProductVo.key},'%')
                    </otherwise>
                </choose>
            </when>
            <when test="searchProductVo.configType == 2">
                select top 50 p.id ppriceid,p.name product_name,null product_color
                from dbo.product p WITH(NOLOCK)
                where p.ismobile1 = 1
                <choose>
                    <when test="searchProductVo.keys != null and !searchProductVo.keys.isEmpty()">
                        and p.id in
                        <foreach collection="searchProductVo.keys" item="key" open="(" close=")" separator=",">
                            #{key}
                        </foreach>
                    </when>
                    <when test="searchProductVo.numberKey != null">
                        and p.id = #{searchProductVo.numberKey}
                    </when>
                    <otherwise>
                        and p.name like concat(#{searchProductVo.key},'%')
                    </otherwise>
                </choose>
            </when>
        </choose>
    </select>
</mapper>
