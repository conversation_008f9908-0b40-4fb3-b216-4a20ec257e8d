<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.rareproduct.mapper.RareProductConfigLogMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.stock.rareproduct.entity.RareProductConfigLog">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="rareProductConfigId" column="rare_product_config_id" jdbcType="BIGINT"/>
            <result property="comment" column="comment" jdbcType="VARCHAR"/>
            <result property="inuser" column="inuser" jdbcType="VARCHAR"/>
            <result property="deleteFlag" column="is_deleted" jdbcType="TINYINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,rare_product_config_id,comment,
        inuser,is_deleted,create_time,
        update_time
    </sql>
</mapper>
