<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.thirdplatform.mapper.ThirdPlatformPurchaseLogMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.stock.thirdplatform.entity.ThirdPlatformPurchaseLog">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="dtime" column="dtime" jdbcType="TIMESTAMP"/>
            <result property="comment" column="comment" jdbcType="VARCHAR"/>
            <result property="inUser" column="in_user" jdbcType="VARCHAR"/>
            <result property="isDel" column="is_del" jdbcType="TINYINT"/>
            <result property="linkType" column="link_type" jdbcType="TINYINT"/>
            <result property="linkId" column="link_id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,dtime,comment,
        in_user,is_del,link_type,
        link_id,create_time,update_time
    </sql>
    <select id="getLogList" resultType="com.jiuji.oa.stock.thirdplatform.vo.res.ThirdPlatformPurchaseLogRes">
        select
            id,dtime,comment,
            in_user,is_del,link_type,
            link_id,create_time,update_time
        from third_platform_purchase_log
        where link_type = #{linkType} and link_id = #{linkId}
        order by id
    </select>
</mapper>
