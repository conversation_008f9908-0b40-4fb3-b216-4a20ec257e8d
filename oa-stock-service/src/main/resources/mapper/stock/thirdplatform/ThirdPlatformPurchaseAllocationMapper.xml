<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.thirdplatform.mapper.ThirdPlatformPurchaseAllocationMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.stock.thirdplatform.entity.ThirdPlatformPurchaseAllocation">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="allocationId" column="allocation_id" jdbcType="VARCHAR"/>
            <result property="customerCode" column="customer_code" jdbcType="VARCHAR"/>
            <result property="customerName" column="customer_name" jdbcType="VARCHAR"/>
            <result property="storeId" column="store_id" jdbcType="VARCHAR"/>
            <result property="allocationStore" column="allocation_store" jdbcType="VARCHAR"/>
            <result property="typeDesc" column="type_desc" jdbcType="VARCHAR"/>
            <result property="isActive" column="is_active" jdbcType="VARCHAR"/>
            <result property="branch" column="branch" jdbcType="VARCHAR"/>
            <result property="commonModelName" column="common_model_name" jdbcType="VARCHAR"/>
            <result property="materialCode" column="material_code" jdbcType="VARCHAR"/>
            <result property="materialDesc" column="material_desc" jdbcType="VARCHAR"/>
            <result property="allocationDate" column="allocation_date" jdbcType="DATE"/>
            <result property="batchSeq" column="batch_seq" jdbcType="VARCHAR"/>
            <result property="qtyAllocation" column="qty_allocation" jdbcType="INTEGER"/>
            <result property="platCode" column="plat_code" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,allocation_id,customer_code,
        customer_name,store_id,allocation_store,
        type_desc,is_active,branch,
        common_model_name,material_code,material_desc,
        allocation_date,batch_seq,qty_allocation,
        plat_code,create_time,update_time,
        is_del,third_platform_purchase_allocation_rv
    </sql>
    <select id="listByPage" resultType="com.jiuji.oa.stock.thirdplatform.vo.res.PurchaseAllocationResVO">
        select tpa.id, tpa.allocation_id,tpa.customer_code,tpa.customer_name,tpa.store_id,tpa.allocation_store,
        tpa.type_desc,tpa.is_active,tpa.branch,tpa.common_model_name,tpa.material_code,tpa.material_desc,
        tpa.allocation_date,tpa.batch_seq,tpa.qty_allocation,tpa.plat_code,
        tps.area_id as areaId,a.area
        from third_platform_purchase_allocation tpa with(nolock)
        left join third_platform_purchase_store tps with(nolock) on tps.store_code = tpa.store_id and tpa.customer_code = tps.customer_code
        left join areainfo a with(nolock) on a.id = tps.area_id
        where tpa.is_del = 0
        <if test="req.customerCode != null and req.customerCode != ''">
            and tpa.customer_code = #{req.customerCode}
        </if>
        <if test="req.areaIdList != null and req.areaIdList.size() > 0">
            and tps.area_id in
            <foreach collection="req.areaIdList" item="areaId" separator="," open="(" close=")">
                #{areaId}
            </foreach>
        </if>
        <if test="req.startTime != null and req.endTime !=null ">
            and tpa.allocation_date between #{req.startTime} and #{req.endTime}
        </if>
    </select>
</mapper>
