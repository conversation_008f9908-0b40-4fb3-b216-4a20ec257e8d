<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.thirdplatform.mapper.ThirdPlatformPurchaseOrderAlterApplyDetailMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.stock.thirdplatform.entity.ThirdPlatformPurchaseOrderAlterApplyDetail">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="alterApplyId" column="alter_apply_id" jdbcType="INTEGER"/>
            <result property="goodsCode" column="goods_code" jdbcType="VARCHAR"/>
            <result property="goodsName" column="goods_name" jdbcType="VARCHAR"/>
            <result property="materielCode" column="materiel_code" jdbcType="VARCHAR"/>
            <result property="materielName" column="materiel_name" jdbcType="VARCHAR"/>
            <result property="alterNum" column="alter_num" jdbcType="INTEGER"/>
            <result property="originalPrice" column="original_price" jdbcType="DECIMAL"/>
            <result property="createUid" column="create_uid" jdbcType="BIGINT"/>
            <result property="createUname" column="create_uname" jdbcType="VARCHAR"/>
            <result property="createAt" column="create_at" jdbcType="TIMESTAMP"/>
            <result property="modifyUid" column="modify_uid" jdbcType="BIGINT"/>
            <result property="modifyUname" column="modify_uname" jdbcType="VARCHAR"/>
            <result property="modifyAt" column="modify_at" jdbcType="TIMESTAMP"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="isDel" column="is_del" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,alter_apply_id,goods_code,
        goods_name,materiel_code,materiel_name,
        alter_num,original_price,create_uid,
        create_uname,create_at,modify_uid,
        modify_uname,modify_at,create_time,
        update_time,is_del,third_platform_purchase_order_alter_apply_detail_rv
    </sql>
</mapper>
