<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.thirdplatform.mapper.ThirdPlatformPurchaseDeliveryImeiMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.stock.thirdplatform.entity.ThirdPlatformPurchaseDeliveryImei">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="shipNo" column="ship_no" jdbcType="VARCHAR"/>
            <result property="shipLineNo" column="ship_line_no" jdbcType="VARCHAR"/>
            <result property="customerCode" column="customer_code" jdbcType="VARCHAR"/>
            <result property="materialCode" column="material_code" jdbcType="VARCHAR"/>
            <result property="imei" column="imei" jdbcType="VARCHAR"/>
            <result property="imei1" column="imei1" jdbcType="VARCHAR"/>
            <result property="imei2" column="imei2" jdbcType="VARCHAR"/>
            <result property="meid" column="meid" jdbcType="VARCHAR"/>
            <result property="productBarcode" column="product_barcode" jdbcType="VARCHAR"/>
            <result property="mac" column="mac" jdbcType="VARCHAR"/>
            <result property="mac2" column="mac2" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="is_del" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,ship_no,ship_line_no,
        customer_code,material_code,imei,
        imei1,imei2,meid,
        product_barcode,mac,mac2,
        create_time,update_time,is_del,
        third_platform_purchase_delivery_imei_rv
    </sql>
    <insert id="insertDeliveryImeiBatch">
        insert into third_platform_purchase_delivery_imei
        (ship_no,ship_line_no,customer_code,material_code,imei,
        imei1,imei2,meid,
        product_barcode,mac,mac2,plat_code,create_time)
        values
        <foreach collection="reqList" item="item" separator=",">
            (#{item.shipNo,jdbcType=VARCHAR}, #{item.shipLineNo,jdbcType=VARCHAR},#{item.customerCode,jdbcType=VARCHAR},#{item.materialCode,jdbcType=VARCHAR},#{item.imei,jdbcType=VARCHAR},
            #{item.imei1,jdbcType=VARCHAR},#{item.imei2,jdbcType=VARCHAR},#{item.meid,jdbcType=VARCHAR},
            #{item.productBarcode,jdbcType=VARCHAR},#{item.mac,jdbcType=VARCHAR},#{item.mac2,jdbcType=VARCHAR},#{item.platCode,jdbcType=VARCHAR},getdate()
            )
        </foreach>
    </insert>
</mapper>
