<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.thirdplatform.mapper.ThirdPlatformPurchaseProductMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.stock.thirdplatform.entity.ThirdPlatformPurchaseProduct">
            <result property="id" column="id" jdbcType="INTEGER"/>
            <result property="customerCode" column="customer_code" jdbcType="VARCHAR"/>
            <result property="platSkuId" column="plat_sku_id" jdbcType="VARCHAR"/>
            <result property="platProductName" column="plat_product_name" jdbcType="VARCHAR"/>
            <result property="platProductColor" column="plat_product_color" jdbcType="VARCHAR"/>
            <result property="skuId" column="sku_id" jdbcType="INTEGER"/>
            <result property="mouldFlag" column="mould_flag" jdbcType="BIT"/>
            <result property="platCode" column="plat_code" jdbcType="VARCHAR"/>
            <result property="inuser" column="inuser" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="is_del" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,customer_code,plat_sku_id,
        plat_product_name,plat_product_color,sku_id,
        mould_flag,plat_code,inuser,
        create_time,update_time,is_del,
        third_platform_purchase_product_rv
    </sql>
    <insert id="insertPurchaseProductBatch">
        insert into third_platform_purchase_product
        (plat_sku_id,plat_product_name,plat_product_color,plat_code,
         gb_code,extend_property,materiel_code,
         sku_type,sku_property,customer_code
        )
        values
        <foreach collection="purchaseProductList" item="item" separator=",">
            (#{item.platSkuId,jdbcType=VARCHAR}, #{item.platProductName,jdbcType=VARCHAR},
             #{item.platProductColor,jdbcType=VARCHAR},#{item.platCode,jdbcType=VARCHAR},
            #{item.gbCode,jdbcType=VARCHAR},#{item.extendProperty,jdbcType=VARCHAR},#{item.materielCode,jdbcType=VARCHAR},
            #{item.skuType,jdbcType=VARCHAR},#{item.skuProperty,jdbcType=VARCHAR},
            #{item.customerCode,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>
    <update id="updatePurchaseProductBatchById">
        <foreach collection="purchaseProductList" item="item" separator=";">
            update third_platform_purchase_product
            set sku_id = #{item.skuId},
                mould_flag = #{item.mouldFlag},
                inuser = #{item.inuser},
                update_time = getdate()
            where id = #{item.id}
        </foreach>
    </update>
    <update id="updatePlatPurchaseProductBatchById">
        <foreach collection="purchaseProductList" item="item" separator=";">
            update third_platform_purchase_product
            set plat_product_name = #{item.platProductName},
            plat_product_color = #{item.platProductColor},
            gb_code = #{item.gbCode},
            extend_property = #{item.extendProperty},
            materiel_code = #{item.materielCode},
            sku_type = #{item.skuType},
            sku_property = #{item.skuProperty},
            update_time = getdate()
            where id = #{item.id}
            and plat_sku_id = #{item.platSkuId}
        </foreach>
    </update>
    <select id="listByPage" resultType="com.jiuji.oa.stock.thirdplatform.vo.res.PurchaseProductResVO">
        select tp.id,tp.plat_sku_id,tp.plat_product_name,tp.plat_product_color,
               tp.mould_flag,tp.sku_id,tp.create_time,tp.inuser,
               p.product_name,p.product_color,tp.gb_code,tp.extend_property,
               tp.sku_type,tp.sku_property,tp.update_time
        from third_platform_purchase_product tp with(nolock)
        left join productinfo p with(nolock) on tp.sku_id = p.ppriceid
        <where>
            and isnull(tp.is_del,0) = 0
            <if test="req.searchKey != null and req.searchKey != '' and req.searchKeyWord != null and req.searchKeyWord != ''">
                <if test="req.searchKey == 'plat_sku_id'">
                    and tp.plat_sku_id = #{req.searchKeyWord}
                </if>
                <if test="req.searchKey == 'plat_product_name'">
                    and tp.plat_product_name like concat('%',#{req.searchKeyWord},'%')
                </if>
                <if test="req.searchKey == 'product_name'">
                    and p.product_name like concat('%',#{req.searchKeyWord},'%')
                </if>
                <if test="req.searchKey == 'sku_id'">
                    and tp.sku_id = #{req.searchKeyWord}
                </if>
            </if>
            <if test="req.platProductName != null and req.platProductName != '' ">
                and tp.plat_product_name = #{req.platProductName}
            </if>
            <if test="req.ppid != null">
                and tp.sku_id = #{req.ppid}
            </if>
            <if test="req.mouldFlag == 0">
                and tp.mould_flag = 0
            </if>
            <if test="req.mouldFlag == 1">
                and tp.mould_flag = 1
            </if>
            <if test="req.mappingFlag == 0">
                and tp.sku_id is null
            </if>
            <if test="req.mappingFlag == 1">
                and tp.sku_id is not null
            </if>
            <if test="req.platCode != null and req.platCode != '' ">
                and tp.plat_code = #{req.platCode}
            </if>
        </where>
    </select>
    <select id="getPurchaseProductById"
            resultType="com.jiuji.oa.stock.thirdplatform.vo.res.PurchaseProductDetailResVO">
        select tp.id,tp.plat_sku_id,tp.plat_product_name,tp.plat_product_color,
               tp.mould_flag,tp.sku_id,tp.create_time,tp.inuser,
               p.product_name,p.product_color,tp.gb_code,tp.extend_property
        from third_platform_purchase_product tp with(nolock)
        left join productinfo p with(nolock) on tp.sku_id = p.ppriceid
        where tp.id = #{id}
    </select>
    <select id="getPlatProductDetail"
            resultType="com.jiuji.oa.stock.thirdplatform.vo.res.PlatProductDetailResVO">
        select top 1 tp.plat_sku_id,tp.plat_product_name,tp.plat_product_color,tp.sku_id,tp.id
        from third_platform_purchase_product tp with(nolock)
        where isnull(tp.is_del,0) = 0
        <if test="req.platSkuId != null and req.platSkuId != ''">
            and tp.plat_sku_id = #{req.platSkuId}
        </if>
        <if test="req.platProductName != null and req.platProductName != ''">
            and tp.plat_product_name like concat('%',#{req.platProductName},'%')
        </if>
        <if test="req.platCode != null and req.platCode != ''">
            and tp.plat_code = #{req.platCode}
        </if>
    </select>
    <select id="listProductMappingByPage"
            resultType="com.jiuji.oa.stock.thirdplatform.vo.res.ProductMappingResVO">
        select tp.plat_sku_id,tp.mould_flag,tp.sku_id
        from third_platform_purchase_product tp with(nolock)
        <where>
            and isnull(tp.is_del,0) = 0
            and tp.sku_id is not null
            <if test="req.platCode != null and req.platCode != ''">
                and tp.plat_code = #{req.platCode}
            </if>
            <if test="req.platSkuIdList != null and req.platSkuIdList.size>0">
                and tp.plat_sku_id in
                <foreach collection="req.platSkuIdList" open="(" close=")" item="platSkuId" separator=",">
                    #{platSkuId}
                </foreach>
            </if>
        </where>
    </select>
    <select id="getPurchaseProductListByPlatSkuIds"
            resultType="com.jiuji.oa.stock.thirdplatform.vo.res.PurchaseProductDetailResVO">
        select tp.id,tp.plat_sku_id,tp.plat_product_name,tp.plat_product_color,
               tp.mould_flag,tp.sku_id,tp.create_time,tp.inuser,
               p.product_name,p.product_color,tp.gb_code,tp.extend_property
        from third_platform_purchase_product tp with(nolock)
        left join productinfo p with(nolock) on tp.sku_id = p.ppriceid
        where tp.is_del = 0
        and tp.plat_code = #{platCode}
        and tp.plat_sku_id in
        <foreach collection="platSkuIdList" open="(" close=")" item="platSkuId" separator=",">
            #{platSkuId}
        </foreach>
    </select>
</mapper>
