<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.thirdplatform.mapper.ThirdPlatformPurchaseOrderItemsMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.stock.thirdplatform.entity.ThirdPlatformPurchaseOrderItems">
            <result property="id" column="id" jdbcType="INTEGER"/>
            <result property="thirdOrderId" column="third_order_id" jdbcType="INTEGER"/>
            <result property="orderId" column="order_id" jdbcType="VARCHAR"/>
            <result property="orderLineId" column="order_line_id" jdbcType="VARCHAR"/>
            <result property="skuId" column="sku_id" jdbcType="VARCHAR"/>
            <result property="unitPrice" column="unit_price" jdbcType="DECIMAL"/>
            <result property="buyQty" column="buy_qty" jdbcType="INTEGER"/>
            <result property="orderLineAmount" column="order_line_amount" jdbcType="DECIMAL"/>
            <result property="materialCode" column="material_code" jdbcType="VARCHAR"/>
            <result property="materialDesc" column="material_desc" jdbcType="VARCHAR"/>
            <result property="allocationStore" column="allocation_store" jdbcType="VARCHAR"/>
            <result property="shippingMethod" column="shipping_method" jdbcType="VARCHAR"/>
            <result property="addressName" column="address_name" jdbcType="VARCHAR"/>
            <result property="shippedQty" column="shipped_qty" jdbcType="INTEGER"/>
            <result property="storeId" column="store_id" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="orderLineStatus" column="order_line_status" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="is_del" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,third_order_id,order_id,
        order_line_id,sku_id,unit_price,
        buy_qty,order_line_amount,material_code,
        material_desc,allocation_store,shipping_method,
        address_id,address_name,shipped_qty,store_id,
        remark,order_line_status,create_time,
        update_time,is_del,third_platform_purchase_order_items_rv
    </sql>
    <insert id="insertOrderItemsBatch">
        insert into third_platform_purchase_order_items
        (third_order_id,order_id,order_line_id,sku_id,unit_price,
        buy_qty,order_line_amount,material_code,
        material_desc,allocation_store,shipping_method,
        address_id,address_name,shipped_qty,store_id,
        remark,order_line_status,create_time)
        values
        <foreach collection="orderItemsList" item="item" separator=",">
            (#{item.thirdOrderId,jdbcType=INTEGER}, #{item.orderId,jdbcType=VARCHAR},#{item.orderLineId,jdbcType=VARCHAR},#{item.skuId,jdbcType=VARCHAR},#{item.unitPrice,jdbcType=DECIMAL},
            #{item.buyQty,jdbcType=INTEGER},#{item.orderLineAmount,jdbcType=DECIMAL},#{item.materialCode,jdbcType=VARCHAR},
            #{item.materialDesc,jdbcType=VARCHAR},#{item.allocationStore,jdbcType=VARCHAR},#{item.shippingMethod,jdbcType=VARCHAR},
            #{item.addressId,jdbcType=VARCHAR},#{item.addressName,jdbcType=VARCHAR},#{item.shippedQty,jdbcType=INTEGER},#{item.storeId,jdbcType=VARCHAR},
            #{item.remark,jdbcType=VARCHAR},#{item.orderLineStatus,jdbcType=VARCHAR},getdate()
            )
        </foreach>
    </insert>
    <select id="getStoreBuyQtyByThirdOrderIds"
            resultType="com.jiuji.oa.stock.thirdplatform.vo.res.PurchaseOrderItemsBuyQty">
        select tpi.third_order_id,isnull(a.area,a1.area) area,
               sum(isnull(tpi.actual_purchaser_num,tpi.buy_qty)) as buy_qty
        from third_platform_purchase_order_items tpi with(nolock)
        left join third_platform_purchase_store ts with(nolock) on tpi.store_id=ts.store_code and ts.code_type=1 and tpi.plat_code=ts.plat_code
        left join areainfo a with(nolock) on ts.area_id=a.id
        left join third_platform_purchase_store ts1 with(nolock) on tpi.address_id=ts1.store_code and ts1.code_type=2 and tpi.plat_code=ts1.plat_code
        left join areainfo a1 with(nolock) on ts1.area_id=a1.id
        where tpi.is_del=0
        and tpi.third_order_id in
        <foreach collection="thirdOrderIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by tpi.third_order_id,a.area,a1.area
    </select>
    <select id="getOrderAddress" resultType="java.lang.String">
        select top 1 address_name from third_platform_purchase_order_items ti with(nolock)
        where ti.is_del=0 and ti.order_id=#{orderNo} and ti.plat_code=#{platCode}
    </select>
</mapper>
