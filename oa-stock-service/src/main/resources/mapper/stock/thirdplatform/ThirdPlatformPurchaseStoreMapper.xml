<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.thirdplatform.mapper.ThirdPlatformPurchaseStoreMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.stock.thirdplatform.entity.ThirdPlatformPurchaseStore">
            <result property="id" column="id" jdbcType="INTEGER"/>
            <result property="customerCode" column="customer_code" jdbcType="VARCHAR"/>
            <result property="storeCode" column="store_code" jdbcType="VARCHAR"/>
            <result property="storeName" column="store_name" jdbcType="VARCHAR"/>
            <result property="areaId" column="area_id" jdbcType="INTEGER"/>
            <result property="qudaoId" column="qudao_id" jdbcType="INTEGER"/>
            <result property="platCode" column="plat_code" jdbcType="VARCHAR"/>
            <result property="inuser" column="inuser" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="is_del" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,customer_code,store_code,
        store_name,area_id,qudao_id,
        plat_code,inuser,
        create_time,update_time,is_del
    </sql>
    <select id="listByPage" resultType="com.jiuji.oa.stock.thirdplatform.vo.res.PurchaseStoreResVO">
        select ts.id,ts.customer_code customerCode,ts.store_code storeCode,ts.store_name storeName,ts.code_type codeType,
               ts.area_id areaId,ts.plat_code platCode,ts.inuser,ts.create_time createTime,
               a.area,a.area_name areaName,ts.qudao_id qudaoId,o.company_jc qudaoJc
        from third_platform_purchase_store ts with(nolock)
        left join areainfo a with(nolock) on ts.area_id = a.id
        left join Ok3w_qudao o with(nolock) on ts.qudao_id = o.id
        <where>
            and isnull(ts.is_del,0) = 0
            <if test="req.platCode != null and req.platCode != ''">
                and ts.plat_code = #{req.platCode}
            </if>
            <if test="req.searchKey != null and req.searchKey != '' and req.searchKeyWord != null and req.searchKeyWord != ''">
                <if test="req.searchKey == 'customer_code'">
                    and ts.customer_code = #{req.searchKeyWord}
                </if>
                <if test="req.searchKey == 'store_code'">
                    and ts.store_code = #{req.searchKeyWord}
                </if>
                <if test="req.searchKey == 'store_name'">
                    and ts.store_name like concat('%',#{req.searchKeyWord},'%')
                </if>
            </if>
            <if test="req.storeName != null and req.storeName != '' ">
                and ts.store_name = #{req.storeName}
            </if>
            <if test="req.areaIdList != null and req.areaIdList.size() > 0">
                and ts.area_id in
                <foreach collection="req.areaIdList" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="req.qudaoId != null">
                and ts.qudao_id = #{req.qudaoId}
            </if>
        </where>
    </select>
    <select id="getPurchaseStoreInfo" resultType="com.jiuji.oa.stock.thirdplatform.vo.res.PurchaseStoreInfoResVO">
        select ts.id,ts.store_code storeCode,ts.store_name storeName,ts.code_type codeType
        from third_platform_purchase_store ts with(nolock)
        <where>
            and isnull(ts.is_del,0) = 0
            <if test="req.storeCode != null and req.storeCode != '' ">
                and ts.store_code = #{req.storeCode}
            </if>
            <if test="req.storeName != null and req.storeName != '' ">
                and ts.store_name like concat('%',#{req.storeName},'%')
            </if>
            <if test="req.platCode != null and req.platCode != '' ">
                and ts.plat_code = #{req.platCode}
            </if>
            <if test="req.areaId != null">
                and ts.area_id = #{req.areaId}
            </if>
        </where>
    </select>
    <select id="getPurchaseStoreListByAreaId"
            resultType="com.jiuji.oa.stock.thirdplatform.entity.ThirdPlatformPurchaseStore">
        select id,customer_code,store_code,
        store_name,area_id,qudao_id,code_type from third_platform_purchase_store ts with(nolock)
        where isnull(ts.is_del,0) = 0
        and ts.area_id in
        <foreach collection="areaIds" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="platCode != null and platCode != '' ">
            and ts.plat_code = #{platCode}
        </if>
    </select>
    <select id="queryAreaByStoreIds" resultType="com.jiuji.oa.stock.thirdplatform.vo.res.PurchaseStoreData">
        select ts.id,ts.store_code,
        ts.store_name,a.id,a.area,ts.code_type
        from third_platform_purchase_store ts with(nolock)
        left join areainfo a with(nolock) on ts.area_id = a.id
        where isnull(ts.is_del,0) = 0
        and ts.code_type = 1
        and ts.store_code in
        <foreach collection="storeIds" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="platCode != null and platCode != '' ">
            and ts.plat_code = #{platCode}
        </if>
    </select>
</mapper>
