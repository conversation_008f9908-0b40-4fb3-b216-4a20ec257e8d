<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.thirdplatform.mapper.ThirdPlatformPurchaseDeliveryMaterialMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.stock.thirdplatform.entity.ThirdPlatformPurchaseDeliveryMaterial">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="shipNo" column="ship_no" jdbcType="VARCHAR"/>
            <result property="customerCode" column="customer_code" jdbcType="VARCHAR"/>
            <result property="materialCode" column="material_code" jdbcType="VARCHAR"/>
            <result property="materialDesc" column="material_desc" jdbcType="VARCHAR"/>
            <result property="qty" column="qty" jdbcType="INTEGER"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="isSerial" column="is_serial" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="is_del" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,ship_no,customer_code,
        material_code,material_desc,qty,
        remark,is_serial,create_time,
        update_time,is_del,third_platform_purchase_delivery_material_rv
    </sql>
    <insert id="insertDeliveryMaterialBatch">
        insert into third_platform_purchase_delivery_material
        (ship_no,customer_code,material_code,material_desc,qty,
        remark,is_serial,plat_code,create_time)
        values
        <foreach collection="reqList" item="item" separator=",">
            (#{item.shipNo,jdbcType=VARCHAR}, #{item.customerCode,jdbcType=VARCHAR},#{item.materialCode,jdbcType=VARCHAR},#{item.materialDesc,jdbcType=VARCHAR},#{item.qty,jdbcType=INTEGER},
            #{item.remark,jdbcType=VARCHAR},#{item.isSerial,jdbcType=VARCHAR},#{item.platCode,jdbcType=VARCHAR},getdate()
            )
        </foreach>
    </insert>
</mapper>
