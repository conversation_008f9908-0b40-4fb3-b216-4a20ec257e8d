<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.thirdplatform.mapper.ThirdPlatformPurchaseConfigMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.stock.thirdplatform.entity.ThirdPlatformPurchaseConfig">
            <result property="id" column="id" jdbcType="INTEGER"/>
            <result property="customerCode" column="customer_code" jdbcType="VARCHAR"/>
            <result property="customerName" column="customer_name" jdbcType="VARCHAR"/>
            <result property="appKey" column="app_key" jdbcType="VARCHAR"/>
            <result property="appSecret" column="app_secret" jdbcType="VARCHAR"/>
            <result property="rsaPublicKey" column="rsa_public_key" jdbcType="VARCHAR"/>
            <result property="rsaPrivateKey" column="rsa_private_key" jdbcType="VARCHAR"/>
            <result property="aesKey" column="aes_key" jdbcType="VARCHAR"/>
            <result property="platCode" column="plat_code" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="is_del" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,customer_code,customer_name,
        app_key,app_secret,rsa_public_key,
        rsa_private_key,aes_key,plat_code,
        status,create_time,update_time,
        is_del,third_platform_purchase_config_rv
    </sql>
    <select id="listByPage" resultType="com.jiuji.oa.stock.thirdplatform.vo.res.PurchaseConfigResVO">
        select id,customer_code,customer_name,
               tid,
               app_key,app_secret,rsa_public_key,
               rsa_private_key,aes_key,plat_code,inuser,
               status,create_time,update_time
        from third_platform_purchase_config tc with(nolock)
        <where>
            and isnull(tc.is_del,0) = 0
            <if test="req.platCode != null and req.platCode != ''">
                and tc.plat_code = #{req.platCode}
            </if>
            <if test="req.customerCode != null and req.customerCode != ''">
                and tc.customer_code like concat('%',#{req.customerCode},'%')
            </if>
            <if test="req.status != null">
                and tc.status = #{req.status}
            </if>
        </where>
    </select>
    <select id="checkConfig" resultType="java.lang.Integer">
        select count(1) from third_platform_purchase_config tc with(nolock)
        <where>
            and isnull(tc.is_del,0) = 0
            <if test="req.customerCode != null and req.customerCode != ''">
                and tc.customer_code = #{req.customerCode}
            </if>
            <if test="req.customerName != null and req.customerName != ''">
                and tc.customer_name = #{req.customerName}
            </if>
            <if test="req.tid != null and req.tid != ''">
                and tc.tid = #{req.tid}
            </if>
            <if test="req.platCode != null and req.platCode != ''">
                and tc.plat_code = #{req.platCode}
            </if>
            <if test="req.id != null">
                and tc.id != #{req.id}
            </if>
        </where>
    </select>
    <select id="getPurchaseConfigByCustomerAndPlatCode"
            resultType="com.jiuji.oa.stock.thirdplatform.entity.ThirdPlatformPurchaseConfig">
        select top 1
            id,customer_code,customer_name,
            tid,
            app_key,app_secret,rsa_public_key,
            rsa_private_key,aes_key,plat_code,inuser,
            status,create_time,update_time
        from third_platform_purchase_config tc with(nolock)
        where isnull(tc.is_del,0) = 0
        and tc.status = 1
        and tc.customer_code = #{customerCode}
        and tc.plat_code = #{platCode}
    </select>
</mapper>
