<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.thirdplatform.mapper.ThirdPlatformPurchaseOrderMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.stock.thirdplatform.entity.ThirdPlatformPurchaseOrder">
            <result property="id" column="id" jdbcType="INTEGER"/>
            <result property="orderId" column="order_id" jdbcType="VARCHAR"/>
            <result property="customerCode" column="customer_code" jdbcType="VARCHAR"/>
            <result property="customerName" column="customer_name" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="VARCHAR"/>
            <result property="orderAmount" column="order_amount" jdbcType="DECIMAL"/>
            <result property="orderDate" column="order_date" jdbcType="TIMESTAMP"/>
            <result property="platCode" column="plat_code" jdbcType="VARCHAR"/>
            <result property="inuser" column="inuser" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="is_del" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,order_id,customer_code,
        customer_name,status,order_amount,
        order_date,plat_code,inuser,
        create_time,update_time,is_del
    </sql>
    <select id="listByPage" resultType="com.jiuji.oa.stock.thirdplatform.vo.res.PurchaseOrderResVO">
        select tpo.id,tpo.order_id,tpo.customer_code,
               tpo.customer_name,tpo.status,
               case when tpo.plat_code = 'PTAC' then tpo.order_amount-isnull(tpo.freight,0) else tpo.order_amount end order_amount,
               tpo.order_date,tpo.plat_code,
               tpo.mkc_caigou_id,
               tpo.kc_caigou_id,
               isnull(tpo.freight,0) as freight
        from dbo.third_platform_purchase_order tpo with(nolock)
        where tpo.is_del=0
        <if test="req.platCode != null and req.platCode != '' ">
            and tpo.plat_code = #{req.platCode}
        </if>
        <if test="req.searchKey != null and req.searchKey != '' and req.searchKeyWord != null and req.searchKeyWord != ''">
            <if test="req.searchKey == 'order_id'">
                and tpo.order_id = #{req.searchKeyWord}
            </if>
            <if test="req.searchKey == 'caigou_id'">
                and (tpo.kc_caigou_id = #{req.searchKeyWord} or tpo.mkc_caigou_id = #{req.searchKeyWord})
            </if>
            <if test="req.searchKey == 'customer_code'">
                and tpo.customer_code = #{req.searchKeyWord}
            </if>
            <if test="req.searchKey == 'ship_no'">
                and exists(SELECT 1 from third_platform_purchase_delivery_head tph with(nolock)
                where isnull(tph.is_del,0)=0
                and tpo.order_id=tph.order_id
                and tpo.plat_code=tph.plat_code
                and tph.ship_no=#{req.searchKeyWord})
            </if>
        </if>
        <if test="req.statusList != null and req.statusList.size() > 0">
            and tpo.status in
            <foreach collection="req.statusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.startTime != null and req.endTime !=null ">
            and tpo.order_date between #{req.startTime} and #{req.endTime}
        </if>
        <if test="req.isGeneratePurchase != null and req.isGeneratePurchase == 0 ">
            and tpo.mkc_caigou_id is null
            and tpo.kc_caigou_id is null
        </if>
        <if test="req.isGeneratePurchase != null and req.isGeneratePurchase == 1 ">
            and (tpo.mkc_caigou_id is not null or tpo.kc_caigou_id is not null)
        </if>
        <choose>
            <when test="req.storeIdsStr != null and req.storeIdsStr != '' and req.addressIdsStr != null and req.addressIdsStr != ''">
                and exists(SELECT 1 from third_platform_purchase_order_items tpi with(nolock)
                left join third_platform_purchase_store ts with(nolock) on tpi.store_id=ts.store_code and ts.code_type=1
                left join third_platform_purchase_store ts1 with(nolock) on tpi.address_id=ts1.store_code and ts1.code_type=2
                where tpi.third_order_id = tpo.id
                and (tpi.store_id in (select * from dbo.F_SPLIT(#{req.storeIdsStr},',') f)
                or (tpi.address_id in (select * from dbo.F_SPLIT(#{req.addressIdsStr},',') f) and tpi.store_id is null)
                or (ts.id is null and  ts1.id is null)
                ))
            </when>
            <when test="req.storeIdsStr != null and req.storeIdsStr != ''">
                and exists(SELECT 1 from third_platform_purchase_order_items tpi with(nolock)
                left join third_platform_purchase_store ts with(nolock) on tpi.store_id=ts.store_code and ts.code_type=1
                left join third_platform_purchase_store ts1 with(nolock) on tpi.address_id=ts1.store_code and ts1.code_type=2
                where tpi.third_order_id = tpo.id
                and (tpi.store_id in (select * from dbo.F_SPLIT(#{req.storeIdsStr},',') f)
                    or (ts.id is null and  ts1.id is null) )
                )
            </when>
            <when test="req.addressIdsStr != null and req.addressIdsStr != ''">
                and exists(SELECT 1 from third_platform_purchase_order_items tpi with(nolock)
                left join third_platform_purchase_store ts with(nolock) on tpi.store_id=ts.store_code and ts.code_type=1
                left join third_platform_purchase_store ts1 with(nolock) on tpi.address_id=ts1.store_code and ts1.code_type=2
                where tpi.third_order_id = tpo.id
                and ((tpi.address_id in (select * from dbo.F_SPLIT(#{req.addressIdsStr},',') f) and tpi.store_id is null)
                or (ts.id is null and  ts1.id is null) )
                )
            </when>
        </choose>
    </select>
    <select id="listOrderItemsByPage"
            resultType="com.jiuji.oa.stock.thirdplatform.vo.res.PurchaseOrderItemsResVO">
        select tpi.id,tpi.third_order_id,tpi.order_id,
               tpi.material_code as platSkuId,isnull(tpi.actual_purchaser_num,tpi.buy_qty) as buy_qty,tpi.unit_price,tpi.order_line_amount,
               tpp.plat_product_name,tpp.plat_product_color
        from third_platform_purchase_order_items tpi with(nolock)
        left join third_platform_purchase_product tpp with(nolock) on tpi.material_code = tpp.plat_sku_id and tpi.plat_code = tpp.plat_code
        where tpi.is_del=0
        <if test="req.thirdOrderId != null">
            and tpi.third_order_id = #{req.thirdOrderId}
        </if>
    </select>
    <select id="listCaigouOrderItemsByPage"
            resultType="com.jiuji.oa.stock.thirdplatform.vo.res.PurchaseCaigouOrderItemsResVO">
        select tpi.id,tpi.third_order_id,tpi.order_id,
        tpi.material_code as platSkuId,isnull(tpi.actual_purchaser_num,tpi.buy_qty) as buy_qty,tpi.unit_price,tpi.order_line_amount,
        tpp.plat_product_name,tpp.plat_product_color,tpi.store_id,tpi.address_id,
        p.ppriceid skuId,p.product_name productName
        from third_platform_purchase_order_items tpi with(nolock)
        left join third_platform_purchase_product tpp with(nolock) on tpi.material_code = tpp.plat_sku_id and tpi.plat_code = tpp.plat_code
        left join productinfo p with(nolock) on tpp.sku_id = p.ppriceid
        where tpi.is_del=0
        <if test="req.thirdOrderId != null">
            and tpi.third_order_id = #{req.thirdOrderId}
        </if>
    </select>
    <select id="listCaigouOrderItems"
            resultType="com.jiuji.oa.stock.thirdplatform.vo.res.PurchaseCaigouOrderItemsResVO">
        select tpi.id,tpi.third_order_id,tpi.order_id,tpi.order_line_id,
        tpi.material_code as platSkuId,tpi.actual_purchaser_num,tpi.buy_qty,tpi.unit_price,tpi.order_line_amount,
        tpp.plat_product_name,tpp.plat_product_color,tpi.store_id,isnull(tpp.mould_flag,0) mouldFlag,tpi.address_id,
        p.ppriceid skuId,p.product_name productName,p.ismobile1 isMobile
        from third_platform_purchase_order_items tpi with(nolock)
        left join third_platform_purchase_product tpp with(nolock) on tpi.material_code = tpp.plat_sku_id and tpi.plat_code = tpp.plat_code
        left join productinfo p with(nolock) on tpp.sku_id = p.ppriceid
        where tpi.is_del=0
        and tpi.third_order_id = #{id}
    </select>
    <select id="getMkcCaigouSubInfoData"
            resultType="com.jiuji.oa.stock.thirdplatform.vo.res.CaigouSubInfoDataRes">
        select c.sub_id caigouId,count(c.mkc_id) caigouCount from mkcCaiGouBasket c with(nolock)
        where c.sub_id in
        <foreach collection="mkcCaigouIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        group by c.sub_id
    </select>
    <select id="getKcCaigouSubInfoData"
            resultType="com.jiuji.oa.stock.thirdplatform.vo.res.CaigouSubInfoDataRes">
        select cs.id caigouId,cs.stats,sum(cb.lcount) caigouCount
        from caigou_sub cs with(nolock)
        left join caigou_basket cb with(nolock) on cs.id = cb.sub_id
        where cs.id in
        <foreach collection="kcCaigouIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        group by cs.id,cs.stats
    </select>
    <select id="getOrderByOrderId"
            resultType="com.jiuji.oa.stock.thirdplatform.entity.ThirdPlatformPurchaseOrder">
        select top 1
        tpo.*
        from third_platform_purchase_order tpo with(nolock)
        where tpo.order_id = #{orderId}
        and tpo.plat_code = #{platCode}
        and tpo.is_del=0
    </select>
    <select id="selectDeliveryInfo"
            resultType="com.jiuji.oa.stock.thirdplatform.vo.res.PurchaseDeliveryProductInfoVO">
        select tpo.mkc_caigou_id,th.ship_no,th.order_id,tm.qty,tp.sku_id,tp.mould_flag from third_platform_purchase_order tpo with(nolock)
        join third_platform_purchase_delivery_head th with(nolock) on tpo.order_id = th.order_id and th.plat_code = tpo.plat_code and th.is_del = 0
        left join third_platform_purchase_delivery_material tm with(nolock) on th.ship_no = tm.ship_no and th.plat_code = tm.plat_code and tm.is_del = 0
        left join third_platform_purchase_product tp with(nolock) on tm.material_code = tp.plat_sku_id and th.plat_code = tp.plat_code and tp.is_del = 0
        where tpo.mkc_caigou_id is not null
        and tpo.is_del = 0
        and tpo.plat_code = #{platCode}
        and th.ship_no = #{shipNo}
    </select>
    <select id="selectNoCheckMkcBySubId"
            resultType="com.jiuji.oa.stock.thirdplatform.vo.res.NocheckStockDataVO">
        select k.id mkcId, k.kc_check,k.ppriceid ppid,k.areaid areaId,k.insourceid2 insourceid2,k.mouldflag mouldFlag,mb.sub_id subId
        from product_mkc k with(nolock)
        left join dbo.mkccaigoubasket mb with(nolock) on mb.mkc_id =k.id
        where mb.sub_id = #{subId}
          and kc_check in(1)
          and k.inbeihuoprice is not null
    </select>
    <select id="selectDeliveryInfoByShipNo"
            resultType="com.jiuji.oa.stock.thirdplatform.vo.res.PurchaseDeliveryProductInfoVO">
        select tpo.mkc_caigou_id,th.ship_no,th.order_id,tm.qty,tp.sku_id,tp.mould_flag from third_platform_purchase_order tpo with(nolock)
        join third_platform_purchase_delivery_head th with(nolock) on tpo.order_id = th.order_id and th.plat_code = tpo.plat_code and th.is_del = 0
        left join third_platform_purchase_delivery_material tm with(nolock) on th.ship_no = tm.ship_no and th.plat_code = tm.plat_code and tm.is_del = 0
        left join third_platform_purchase_product tp with(nolock) on tm.material_code = tp.plat_sku_id and th.plat_code = tp.plat_code and tp.is_del = 0
        where tpo.mkc_caigou_id is not null
        and tpo.is_del = 0
        and tpo.plat_code = #{platCode}
        and th.ship_no = #{shipNo}
    </select>
    <select id="selectDeliveryList"
            resultType="com.jiuji.oa.stock.thirdplatform.vo.res.PurchaseDeliveryDataVO">
        select th.id,tpo.mkc_caigou_id,th.ship_no,th.plat_code
        from third_platform_purchase_delivery_head th with(nolock)
        left join third_platform_purchase_order tpo with(nolock) on tpo.order_id = th.order_id and th.plat_code = tpo.plat_code and tpo.is_del = 0
        where tpo.mkc_caigou_id is not null
        and tpo.plat_code = #{platCode}
        and th.is_del = 0
        and isnull(th.is_check_stock,0) = 0
        and th.create_time > CAST('2025-02-26 00:00:00' AS DATETIME)
    </select>
    <select id="listNotSignedOrderId" resultType="java.lang.String">
        select tpo.order_id from third_platform_purchase_order tpo with(nolock)
        where tpo.is_del = 0
        and isnull(tpo.is_all_signed,0) = 0
        and tpo.order_date >= DATEADD(MONTH, -3, GETDATE())
        and tpo.plat_code = #{platCode}
        and tpo.customer_code = #{customerCode}
    </select>

    <update id="handleOrderIsAllSigned">
        UPDATE tpo
        SET is_all_signed = 1
            FROM third_platform_purchase_order tpo
        JOIN (
            SELECT
                tpo.order_id,
                SUM(tpoi.buy_qty) AS buy_qty,
                SUM(tpm.signed_num) AS signed_num
            FROM third_platform_purchase_order tpo WITH (NOLOCK)
            LEFT JOIN third_platform_purchase_order_items tpoi WITH (NOLOCK)
                ON tpo.id = tpoi.third_order_id
                AND tpoi.is_del = 0
            LEFT JOIN third_platform_purchase_delivery_head tph WITH (NOLOCK)
                ON tpo.order_id = tph.order_id
                AND tpo.plat_code = tph.plat_code
                AND tph.is_del = 0
            LEFT JOIN third_platform_purchase_delivery_material tpm WITH (NOLOCK)
                ON tph.ship_no = tpm.ship_no
                AND tph.plat_code = tpm.plat_code
                AND tpm.is_del = 0
            WHERE tpo.is_del = 0
             and tpo.plat_code = #{platCode}
             and tpo.order_id = #{orderId}
            GROUP BY tpo.order_id
            HAVING SUM(tpoi.buy_qty) = SUM(tpm.signed_num)
        ) AS t1 ON tpo.order_id = t1.order_id
        WHERE tpo.is_del = 0
        and isnull(tpo.is_all_signed,0) = 0
        and tpo.order_id = #{orderId}
        and tpo.plat_code = #{platCode}
    </update>
</mapper>
