<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.thirdplatform.mapper.ThirdPlatformPurchaseDeliveryHeadMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.stock.thirdplatform.entity.ThirdPlatformPurchaseDeliveryHead">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="shipNo" column="ship_no" jdbcType="VARCHAR"/>
            <result property="orderId" column="order_id" jdbcType="VARCHAR"/>
            <result property="destinationCity" column="destination_city" jdbcType="VARCHAR"/>
            <result property="address" column="address" jdbcType="VARCHAR"/>
            <result property="allocationStore" column="allocation_store" jdbcType="VARCHAR"/>
            <result property="qty" column="qty" jdbcType="INTEGER"/>
            <result property="customerCode" column="customer_code" jdbcType="VARCHAR"/>
            <result property="receiptCounty" column="receipt_county" jdbcType="VARCHAR"/>
            <result property="deliveryDate" column="delivery_date" jdbcType="TIMESTAMP"/>
            <result property="storeId" column="store_id" jdbcType="VARCHAR"/>
            <result property="receiptCity" column="receipt_city" jdbcType="VARCHAR"/>
            <result property="receiptProvince" column="receipt_province" jdbcType="VARCHAR"/>
            <result property="platCode" column="plat_code" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="is_del" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,ship_no,order_id,
        destination_city,address,allocation_store,
        qty,customer_code,receipt_county,
        delivery_date,store_id,receipt_city,
        receipt_province,plat_code,create_time,
        update_time,is_del,delivery_status,receipt_store_id,
        receipt_store
    </sql>
    <insert id="insertDeliveryHeadBatch">
        insert into third_platform_purchase_delivery_head
        (ship_no,order_id,destination_city,address,allocation_store,
        qty,customer_code,receipt_county,
        delivery_date,store_id,receipt_city,
        receipt_province,plat_code,create_time)
        values
        <foreach collection="reqList" item="item" separator=",">
            (#{item.shipNo,jdbcType=VARCHAR}, #{item.orderId,jdbcType=VARCHAR},#{item.destinationCity,jdbcType=VARCHAR},#{item.address,jdbcType=VARCHAR},#{item.allocationStore,jdbcType=VARCHAR},
            #{item.qty,jdbcType=INTEGER},#{item.customerCode,jdbcType=VARCHAR},#{item.receiptCounty,jdbcType=VARCHAR},
            #{item.deliveryDate,jdbcType=TIMESTAMP},#{item.storeId,jdbcType=VARCHAR},#{item.receiptCity,jdbcType=VARCHAR},
            #{item.receiptProvince,jdbcType=VARCHAR},#{item.platCode,jdbcType=VARCHAR},getdate()
            )
        </foreach>
    </insert>
    <select id="selectDeliveryByShipNo"
            resultType="com.jiuji.oa.stock.thirdplatform.entity.ThirdPlatformPurchaseDeliveryHead">
        select top 1
            <include refid="Base_Column_List"/>
        from third_platform_purchase_delivery_head dh with(nolock)
        where isnull(dh.is_del,0) = 0
        and dh.ship_no = #{shipNo}
        and dh.plat_code = #{platCode}
    </select>
    <select id="listDeliveryImeiByPage"
            resultType="com.jiuji.oa.stock.thirdplatform.vo.res.PurchaseDeliveryImeiRes">
        select ship_no,customer_code,material_code,
               imei,imei1,imei2,meid,
               product_barcode,mac,mac2
        from third_platform_purchase_delivery_imei di with(nolock)
        where di.ship_no = #{req.shipNo}
        and di.material_code = #{req.materialCode}
        and di.plat_code = #{req.platCode}
        and isnull(di.is_del,0) = 0
    </select>
    <select id="getDeliveryMaterialList"
            resultType="com.jiuji.oa.stock.thirdplatform.vo.res.PurchaseDeliveryMaterialDataRes">
        select dm.ship_no,dm.customer_code,dm.material_code,
               case when isnull(dm.material_desc,'') = '' then pp.plat_product_name else dm.material_desc end materialDesc,
               dm.price as unitPrice,
               sum(dm.qty) qty,dm.is_serial,
               sum(dm.signed_num) signedNum,
               dm.signed_time,
               dm.shipped_time
        from third_platform_purchase_delivery_material dm with(nolock)
        left join third_platform_purchase_product pp with(nolock) on dm.material_code = pp.plat_sku_id and dm.plat_code = pp.plat_code
        where isnull(dm.is_del,0) = 0
        and dm.ship_no = #{shipNo}
        and dm.plat_code = #{platCode}
        group by dm.ship_no,dm.customer_code,dm.material_code,dm.material_desc,dm.is_serial,
                 dm.signed_time,dm.shipped_time,pp.plat_product_name,dm.price
    </select>
    <select id="getDeliveryImeiMaterialQtyList"
            resultType="com.jiuji.oa.stock.thirdplatform.vo.res.DeliveryImeiMaterialData">
        select ship_no,material_code,count(*) qty
        from third_platform_purchase_delivery_imei di with(nolock)
        where di.ship_no = #{shipNo}
          and di.plat_code = #{platCode}
          and isnull(di.is_del,0) = 0
        group by ship_no,material_code
    </select>
</mapper>
