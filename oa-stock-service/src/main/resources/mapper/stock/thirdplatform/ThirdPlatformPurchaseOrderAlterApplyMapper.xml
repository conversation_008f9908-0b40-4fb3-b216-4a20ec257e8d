<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.thirdplatform.mapper.ThirdPlatformPurchaseOrderAlterApplyMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.stock.thirdplatform.entity.ThirdPlatformPurchaseOrderAlterApply">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="orderId" column="order_id" jdbcType="VARCHAR"/>
            <result property="alterAddressCode" column="alter_address_code" jdbcType="VARCHAR"/>
            <result property="alterCode" column="alter_code" jdbcType="VARCHAR"/>
            <result property="payStatus" column="pay_status" jdbcType="VARCHAR"/>
            <result property="shipmentStatus" column="shipment_status" jdbcType="VARCHAR"/>
            <result property="orderStatus" column="order_status" jdbcType="VARCHAR"/>
            <result property="alterStatus" column="alter_status" jdbcType="VARCHAR"/>
            <result property="createAt" column="create_at" jdbcType="TIMESTAMP"/>
            <result property="createUid" column="create_uid" jdbcType="BIGINT"/>
            <result property="createUname" column="create_uname" jdbcType="VARCHAR"/>
            <result property="modifyUid" column="modify_uid" jdbcType="BIGINT"/>
            <result property="modifyUname" column="modify_uname" jdbcType="VARCHAR"/>
            <result property="modifyAt" column="modify_at" jdbcType="TIMESTAMP"/>
            <result property="platCode" column="plat_code" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="isDel" column="is_del" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,order_id,alter_address_code,
        alter_code,pay_status,shipment_status,
        order_status,alter_status,create_at,
        create_uid,create_uname,modify_uid,
        modify_uname,modify_at,plat_code,
        create_time,update_time,is_del
    </sql>
    <select id="listUnAlterOrderApply"
            resultType="com.jiuji.oa.stock.thirdplatform.vo.res.PurchaseOrderAlterDataVO">
        select top 1000
            tpa.id,
            tpo.mkc_caigou_id,
            tpp.plat_sku_id,
            tpp.sku_id,
            tpad.alter_num
        from third_platform_purchase_order_alter_apply tpa with(nolock)
        left join third_platform_purchase_order_alter_apply_detail tpad with(nolock) on tpa.id = tpad.alter_apply_id
        left join third_platform_purchase_product tpp with(nolock) on tpad.goods_code = tpp.plat_sku_id and tpad.plat_code = tpp.plat_code
        left join third_platform_purchase_order tpo with(nolock) on tpa.order_id = tpo.order_id and tpa.plat_code = tpo.plat_code
        where tpa.is_del = 0
          and tpo.is_del = 0
          and tpo.mkc_caigou_id is not null
          and tpp.sku_id is not null
          and isnull(tpa.is_alter_order,0) = 0
          and tpa.alter_status = 'COMPLETED'
    </select>
    <select id="listProductMkcInfoByCaigouId"
            resultType="com.jiuji.oa.stock.thirdplatform.vo.res.PurchaseProductDataVO">
        select pm.id mkcId,
               pm.ppriceid skuId,
               pm.kc_check kcCheck,
               pm.imei,
               isnull(pm.mouldFlag,0) mouldFlag
        from mkcCaiGouBasket mb with(nolock)
        join product_mkc pm with(nolock) on mb.mkc_id = pm.id
        where mb.sub_id = #{caigouId}
        and pm.kc_check = 1
        and pm.basket_id is null
        and pm.imei is null
    </select>
    <select id="listUnprocessedOrderAlterApply"
            resultType="com.jiuji.oa.stock.thirdplatform.entity.ThirdPlatformPurchaseOrderAlterApply">
        select tpa.id,tpa.order_id,
               tpa.alter_status,
               tpa.plat_code
        from third_platform_purchase_order_alter_apply tpa with(nolock)
        where tpa.is_del = 0
          and tpa.alter_status = 'COMPLETED'
          and isnull(tpa.is_alter_order,0) = 0
    </select>
    <select id="listProductKcInfoByCaigouId"
            resultType="com.jiuji.oa.stock.thirdplatform.vo.res.PurchaseProductDataVO">
        select pm.ppriceid skuId,
               isnull(sum(pm.leftCount),0) leftCount
        from caigou_basket pm with(nolock)
        where pm.sub_id = #{caigouId}
        group by pm.ppriceid
    </select>
</mapper>
