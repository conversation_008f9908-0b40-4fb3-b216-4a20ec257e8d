<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.nc.stock.mapper.BrandMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.jiuji.oa.nc.stock.entity.Brand">
    <id column="id" property="id"/>
    <result column="name" property="name"/>
  </resultMap>
  <select id="listPage" resultType="com.jiuji.oa.nc.stock.entity.Brand">
    SELECT
    a.id,a.name
    FROM
    brand a with(nolock)
    <where>
      a.id in
      <foreach collection="brandIds" item="item" open="(" close=")" separator=",">
        #{item}
      </foreach>
      <if test="brandName!=null and brandName!=''">
        and a.name like #{brandName} +'%'
      </if>
    </where>
  </select>

  <select id="listBrand" resultType="com.jiuji.oa.nc.stock.entity.Brand">
    SELECT
    a.id,a.name
    FROM
    brand a with(nolock)
    <where>
      a.id in
      <foreach collection="brandIds" item="item" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </where>
    order by a.id
  </select>
</mapper>
