<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.stock.pdapurchase.mapper.PurchaseMapper">

    <resultMap id="PurchaseVOMap" type="com.jiuji.oa.stock.pdapurchase.vo.res.PurchaseVO">
        <result property="ppid" column="ppriceid"/>
        <result property="productId" column="product_id"/>
        <result property="productName" column="product_name"/>
        <result property="productColor" column="product_color"/>
        <result property="stock" column="stock"/>
        <result property="inPrice" column="inprice"/>
        <result property="fanli" column="fanli"/>
        <result property="areaId" column="areaid"/>
        <result property="inSourceId" column="insourceid"/>
        <result property="inSourceId2" column="insourceid2"/>
        <result property="basketId" column="basket_id"/>
        <result property="companyJc" column="company_jc"/>
        <result property="mkcId" column="mkcId"/>
        <result property="inbeihuodate" column="inbeihuodate"/>
        <result property="inbehuoprice" column="inbeihuoprice"/>
        <result property="provinceName" column="province_name"/>
        <result property="kcCheck" column="kc_check"/>
        <result property="isTax" column="isTax"/>
        <result property="area" column="area"/>
        <result property="areaName" column="area_name"/>
        <result property="countdown" column="countdown"/>
    </resultMap>
    <update id="updateRecordBasketByMainId">
        update recordbasket set completed=1,deldtime=getdate() where completed=0 and mainid=#{id}
    </update>
    <select id="queryProductNoTake" resultMap="PurchaseVOMap">
        select k.ppriceid,
        p.product_id,
        p.product_name,
        p.product_color,
        k.count as stock,
        k.inprice
        from (
        select ppriceid,
        MAX(inprice) as inprice,
        COUNT(1) as count
        from
        product_mkc k with(nolock)
        where
        kc_check in (1)
        and isnull(inbeihuo , 0)= 0
        <include refid="baseCondition1"></include>

        group by
        ppriceid) k
        left join productinfo p with(nolock) on
        k.ppriceid = p.ppriceid
        <where>
            <include refid="baseCondition2"></include>
        </where>

        order by count desc
    </select>

    <sql id="baseCondition1" >
        <if test="param.mkcId != null and param.mkcId != '' ">
            and k.id = #{param.mkcId}
        </if>

        <if test="inSourceList != null and inSourceList.size() > 0">
            and k.insourceid in
            <foreach collection="inSourceList" item="it" separator="," open="(" close=")" index="index">
                #{it}
            </foreach>
        </if>

        <if test="param.inSourceId != null and param.inSourceId != '' ">
            and k.insourceid = #{param.inSourceId}
        </if>
        <if test="param.inSourceId2 != null and param.inSourceId2.size() > 0 ">
            and k.insourceid2 in
            <foreach collection="param.inSourceId2" index="index" item="it" separator="," open="(" close=")">
                #{it}
            </foreach>
        </if>

        <if test="param.areaIds != null and param.areaIds.size() > 0 ">
            and k.areaid in
            <foreach collection="param.areaIds" index="index" item="it" separator="," open="(" close=")">
                #{it}
            </foreach>

        </if>

        <if test="param.showKind == 'jxc' ">
            and exists (select 1 from areainfo a with(nolock) where a.id=k.areaid and a.swkind = 1)
        </if>

        <if test="param.pids != null and param.pids.size() > 0 ">
            and exists(select 1 from areainfo a with(nolock) where a.id=k.areaid and a.pid in
            <foreach collection="param.pids" index="index" item="it" separator="," open="(" close=")">
                #{it}
            </foreach>
            )
        </if>
        <if test="param.kind != null and param.kind !=''">
            and exists (select 1 from areainfo a with(nolock) where a.id=k.areaid and a.kind1 = #{param.kind} )
        </if>

        <if test="param.batchNo != null and param.batchNo != '' ">
            and takeBatch= #{param.batchNo}
        </if>

        <if test="param.ch999Name != null and param.ch999Name != '' ">
            and k.beihuoUser like CONCAT('%',#{param.ch999Name},'%')
        </if>

        <if test="param.basketId == 1">
            and k.basket_id is not null
        </if>

        <if test="param.basketId == 0">
            and k.basket_id is null
        </if>
    </sql>

    <sql id="baseCondition2" >
        <if test="param.brandIds != null and param.brandIds.size() > 0">
            and p.brandid in
            <foreach collection="param.brandIds" index="index" item="it" separator="," open="(" close=")">
                #{it}
            </foreach>
        </if>

        <if test="param.ppids != null and param.ppids.size() > 0 ">
            and p.ppriceid in
            <foreach collection="param.ppids" item="it" index="index" separator="," open="(" close=")">
                #{it}
            </foreach>
        </if>

        <if test="param.productId != null and param.productId != '' ">
            and p.product_id = #{param.productId}
        </if>
        <if test="param.ppid != null and param.ppid != '' ">
            and p.ppriceid = #{param.ppid}
        </if>

        <if test="param.productName != null and param.productName != '' ">
            and p.product_name like CONCAT('%',#{param.productName},'%')
        </if>

        <if test="param.hang != null and param.hang != '' ">
            <choose>
                <when test="param.hang == 1">
                    and p.product_color like CONCAT('%',N'行货','%')
                </when>
                <when test="param.hang == 0">
                    and p.product_color not like CONCAT('%',N'行货','%')
                </when>
            </choose>
        </if>

        <if test="param.cids != null and param.cids.size() > 0 ">
            and p.cid in
            <foreach collection="param.cids" item="it" index="index" separator="," open="(" close=")">
                #{it}
            </foreach>
        </if>

        <if test="param.noCids != null and param.noCids.size() > 0 ">
            and p.brandid not in
            <foreach collection="param.noCids" item="it" index="index" separator="," open="(" close=")">
                #{it}
            </foreach>
        </if>
    </sql>

    <select id="queryAreaStock" resultType="com.jiuji.oa.stock.pdapurchase.vo.res.PurchaseAreaRes">
        select
        isnull(a.pid,0) pid,
        isnull(l.name,'') provinceName,
        k.ppriceid ppid,
        count(1) stock
        from product_mkc k with(nolock)
        left join productinfo p with(nolock) on k.ppriceid = p.ppriceid
        left join areainfo a with(nolock) on a.id = k.areaid
        left join dbo.Ok3w_qudao o with(nolock) on o.id = k.insourceid2
        left join arealist l with(nolock) on a.pid = l.code
        <where>
            and kc_check in (1)
            and isnull(inbeihuo, 0) = 0
            <include refid="baseCondition1"></include>
            <include refid="baseCondition2"></include>
        </where>
        group by
        a.pid,l.name,k.ppriceid
    </select>

    <select id="queryCompanyJc" resultType="com.jiuji.oa.stock.pdapurchase.vo.res.PurchaseAreaRes">
        select
        distinct
        k.ppriceid ppid,
        o.company_jc,
        k.insourceid2
        from product_mkc k with(nolock)
        left join productinfo p with(nolock) on k.ppriceid = p.ppriceid
        left join areainfo a with(nolock) on a.id = k.areaid
        left join dbo.Ok3w_qudao o with(nolock) on o.id = k.insourceid2
        <where>
            and kc_check in (1)
            and isnull(inbeihuo, 0) = 0
            <include refid="baseCondition1"></include>
            <include refid="baseCondition2"></include>
        </where>
    </select>

    <select id="listByAreaStock" resultType="com.jiuji.oa.stock.pdapurchase.vo.res.PurchaseAreaVO">
        select k.id as mkcId,
        isnull(k.inprice,0) inprice,
        k.areaid,
        a.pid,
        k.insourceid2,
        a.Province_name,
        o.company_jc,
        k.ppriceid as ppid
        from product_mkc k with(nolock)
        left join productinfo p with(nolock) on k.ppriceid = p.ppriceid
        left join areainfo a with(nolock) on a.id = k.areaid
        left join dbo.Ok3w_qudao o with(nolock) on o.id = k.insourceid2
        <where>
            and kc_check in (1)
            and isnull(inbeihuo, 0) = 0
            <include refid="baseCondition1"></include>
            <include refid="baseCondition2"></include>
        </where>
    </select>
    <select id="queryModifyMkcId" resultType="java.lang.Integer">
        select k.id
        from dbo.product_mkc k with(nolock)
        where ppriceid = #{ppid}
        and kc_check in (1)
        and isnull(inbeihuo,
        0) = 0
        <if test="insources != null and insources.size() > 0">
            and insourceid in
            <foreach collection="insources" separator="," open="(" close=")" index="index" item="it">
                #{it}
            </foreach>
        </if>
        <if test="mkcIdList != null and mkcIdList.size() > 0">
            and k.id in
            <foreach collection="mkcIdList" separator="," open="(" close=")" index="index" item="it">
                ${it}
            </foreach>
        </if>
        and exists(
        select *
        from dbo.areainfo b with(nolock)
        where b.id = k.areaid
        and b.pid = #{pid})
        order by k.basket_id desc,dtime asc
    </select>

    <select id="queryProductTaking" resultMap="PurchaseVOMap">
        SELECT
        k.id AS mkcId,
        k.ppriceid,
        isnull(k.inprice,0) inprice,
        isnull(k.inbeihuoprice,0) inbeihuoprice,
        k.fanli,
        k.isTax,
        p.product_id,
        p.product_name,
        p.product_color,
        k.areaid,
        k.basket_id,
        k.insourceid2 insourceid2,
        o.company_jc,
        a.pid,
        a.province_name,
        a.area,
        a.area_name,
        DATEDIFF(DAY,GETDATE(),mcgb.expect_time) countdown
        FROM
        product_mkc k with(nolock)
        LEFT JOIN productinfo p with(nolock) ON k.ppriceid = p.ppriceid
        LEFT JOIN Ok3w_qudao o with(nolock) ON o.id = k.insourceid2
        LEFT JOIN areainfo a with(nolock) ON a.id = k.areaid
        LEFT JOIN mkcCaiGouBasket mcgb with(nolock) ON k.id = mcgb.mkc_id
        WHERE
        k.kc_check = 1
        AND k.inbeihuo = 1
        <include refid="baseCondition1"></include>
        <include refid="baseCondition2"></include>
        <if test="param.isTax == 1">
            and k.istax = 1
        </if>
        <if test="param.isTax == 0">
            and k.istax = 0
        </if>
        <if test="param.countdownType != null and param.countdown != null">
            <if test="param.countdownType == 1 ">
                and DATEDIFF(DAY,GETDATE(),mcgb.expect_time) &gt; #{param.countdown}
            </if>
            <if test="param.countdownType == 2 ">
                and DATEDIFF(DAY,GETDATE(),mcgb.expect_time) = #{param.countdown}
            </if>
            <if test="param.countdownType == 3 ">
                and DATEDIFF(DAY,GETDATE(),mcgb.expect_time) &lt; #{param.countdown}
            </if>
        </if>
        <if test="param.optionType != null and param.optionType == 'caigouId' and param.key != null and param.key != ''">
            and mcgb.sub_id = #{param.key}
        </if>
        order by p.ppriceid,k.id
    </select>

    <select id="queryProductTook" resultMap="PurchaseVOMap">
        SELECT
        k.id AS mkcId,
        a.area,
        k.ppriceid,
        isnull(k.inprice,0) inprice,
        isnull(k.inbeihuoprice,0) inbeihuoprice,
        k.fanli,
        k.areaid,
        k.insourceid,
        k.insourceid2,
        k.inbeihuodate,
        k.kc_check,
        k.isTax,
        k.imei,
        k.imei2,
        p.product_id,
        p.product_name,
        p.product_color,
        o.company_jc,
        a.pid,
        a.province_name,
        a.area_name
        FROM
        product_mkc k with(nolock)
        LEFT JOIN
        productinfo p with(nolock)
        ON
        k.ppriceid=p.ppriceid
        LEFT JOIN
        Ok3w_qudao o with(nolock)
        ON
        o.id=k.insourceid2
        LEFT JOIN
        areainfo a with(nolock)
        ON
        a.id = k.areaid
        left join mkcCaiGouBasket mcgb with(nolock) on k.id = mcgb.mkc_id
        WHERE
        inbeihuo=2
        and kc_check in(1,2,3,6,13,5,8,9,10,11)
        <if test="startTime != null and endTime != null">
            and inbeihuodate between #{startTime} and #{endTime}
        </if>
        <include refid="baseCondition1"></include>
        <include refid="baseCondition2"></include>
        <if test="param.kcCheck == 1">
            and kc_check in(2,3,6,13,5,8,9,10,11)
        </if>
        <if test="param.kcCheck == 0">
            and kc_check in(1)
        </if>

        <if test="param.isEnterSerialNumber != null and param.isEnterSerialNumber==0">
            and  k.imei is null
        </if>

        <if test="param.isEnterSerialNumber != null and param.isEnterSerialNumber==1">
            and  k.imei is not null
        </if>

        <if test="param.isTax == 1">
            and k.istax = 1
        </if>
        <if test="param.isTax == 0">
            and k.istax = 0
        </if>
        <if test="param.optionType != null and param.optionType == 'caigouId' and param.key != null and param.key != ''">
            and mcgb.sub_id = #{param.key}
        </if>
        order by inbeihuodate desc;
    </select>
    <select id="selectMkcById" resultType="com.jiuji.oa.stock.pdapurchase.bo.ProductMkcBO">
        select k.* from product_mkc k with (nolock) where k.id = #{id}
    </select>

    <select id="queryCaigouSubId" resultType="java.lang.String">
        select top 1 b.sub_id from dbo.mkcCaiGouBasket b with(nolock)
        left join dbo.product_mkc k with(nolock) on k.id=b.mkc_id
        where isnull(b.isDel,0)=0
          and b.sub_id in
            (select  b1.sub_id from dbo.mkcCaiGouBasket b1 with(nolock)
            where b1.mkc_id = #{id})
          and b.mkc_id <![CDATA[ <> ]]> #{id}
          and k.kc_check not in (4,7)
    </select>
    <select id="queryIdByMkcId" resultType="java.lang.String">
        select top 1 b.sub_id from dbo.mkcCaiGouBasket b with(nolock)
        where b.mkc_id = #{id}
    </select>
    <select id="selectToareaById" resultType="com.jiuji.oa.stock.pdapurchase.bo.MkcToAreaBO">
        select top 1 * from dbo.mkc_toarea with(nolock) where mkc_id = #{id}
    </select>
    <select id="queryProductGroupTookPage" resultType="com.jiuji.oa.stock.pdapurchase.vo.res.PuchaseGroupVO">
        WITH selectTemp AS (
        SELECT TOP
        100 PERCENT ROW_NUMBER ( ) OVER ( ORDER BY t.imeidate desc ) AS __row_number__,
        t.area,
        t.ppid,
        t.product_id,
        t.product_name,
        t.product_color,
        t.inprice,
        t.insourceid2,
        t.company_jc companyJc,
        t.imeidate,
        COUNT(t.mkc_id) COUNT
        FROM
        (
        SELECT
        k.id mkc_id,
        a.area,
        k.ppriceid ppid,
        p.product_id,
        p.product_name,
        p.product_color,
        ISNULL(k.inbeihuoprice,0) inprice,
        k.insourceid2,
        o.company_jc,
        CONVERT(nvarchar(10),k.imeidate,120) imeidate
        FROM
        product_mkc k with(nolock)
        LEFT JOIN
        productinfo p with(nolock)
        ON
        k.ppriceid=p.ppriceid
        LEFT JOIN
        Ok3w_qudao o with(nolock)
        ON
        o.id=k.insourceid2
        LEFT JOIN
        areainfo a with(nolock)
        ON
        a.id = k.areaid
        WHERE
        k.inbeihuo=2
        and k.kc_check in(1,2,3,6,13,5,8,9,10,11)
        AND k.imeidate is not null
        <if test="param.productName != null ">
            and p.product_name like CONCAT('%',#{param.productName},'%')
        </if>
        <if test="param.ppid != null ">
            and k.ppriceid = #{param.ppid}
        </if>
        <if test="param.inSourceId2 != null ">
            and k.insourceid2 = #{param.inSourceId2}
        </if>
        <if test="param.areaIds != null and param.areaIds.size() > 0 ">
            and k.areaid in
            <foreach collection="param.areaIds" index="index" item="it" separator="," open="(" close=")">
                #{it}
            </foreach>
        </if>
        <if test="param.showKind == 'jxc' ">
            and exists (select 1 from areainfo a with(nolock) where a.id=k.areaid and a.swkind = 1)
        </if>
        <if test="param.startTime != null and param.endTime != null">
            and k.imeidate between #{param.startTime} and #{param.endTime}
        </if>
        ) t
        GROUP BY
        t.area,
        t.ppid,
        t.product_id,
        t.product_name,
        t.product_color,
        t.inprice,
        t.insourceid2,
        t.company_jc,
        t.imeidate
        )
        SELECT * FROM selectTemp WHERE __row_number__ BETWEEN #{startRow} AND #{endRow} ORDER BY __row_number__
    </select>
    <select id="queryProductGroupTookList" resultType="com.jiuji.oa.stock.pdapurchase.vo.res.PuchaseGroupVO">
        SELECT
        t.area,
        t.ppid,
        t.product_id,
        t.product_name,
        t.product_color,
        t.inprice,
        t.insourceid2,
        t.company_jc companyJc,
        t.imeidate,
        COUNT(t.mkc_id) COUNT
        FROM
        (
        SELECT
        k.id mkc_id,
        a.area,
        k.ppriceid ppid,
        p.product_id,
        p.product_name,
        p.product_color,
        ISNULL(k.inbeihuoprice,0) inprice,
        k.insourceid2,
        o.company_jc,
        CONVERT(nvarchar(10),k.imeidate,120) imeidate
        FROM
        product_mkc k with(nolock)
        LEFT JOIN
        productinfo p with(nolock)
        ON
        k.ppriceid=p.ppriceid
        LEFT JOIN
        Ok3w_qudao o with(nolock)
        ON
        o.id=k.insourceid2
        LEFT JOIN
        areainfo a with(nolock)
        ON
        a.id = k.areaid
        WHERE
        k.inbeihuo=2
        and k.kc_check in(1,2,3,6,13,5,8,9,10,11)
        AND k.imeidate is not null
        <if test="param.inSourceId2 != null ">
            and k.insourceid2 = #{param.inSourceId2}
        </if>
        <if test="param.productName != null ">
            and p.product_name like CONCAT('%',#{param.productName},'%')
        </if>
        <if test="param.ppid != null ">
            and k.ppriceid = #{param.ppid}
        </if>
        <if test="param.areaIds != null and param.areaIds.size() > 0 ">
            and k.areaid in
            <foreach collection="param.areaIds" index="index" item="it" separator="," open="(" close=")">
                #{it}
            </foreach>
        </if>
        <if test="param.showKind == 'jxc' ">
            and exists (select 1 from areainfo a with(nolock) where a.id=k.areaid and a.swkind = 1)
        </if>
        <if test="param.startTime != null and param.endTime != null">
            and k.imeidate between #{param.startTime} and #{param.endTime}
        </if>
        ) t
        GROUP BY
        t.area,
        t.ppid,
        t.product_id,
        t.product_name,
        t.product_color,
        t.inprice,
        t.insourceid2,
        t.company_jc,
        t.imeidate
        order by t.imeidate desc
    </select>
    <select id="queryProductGroupTookTotal"
            resultType="com.jiuji.oa.stock.pdapurchase.vo.res.PurchaseGroupTotalVO">
        SELECT
        count(k.id) totalCount,
        coalesce(sum(k.inbeihuoprice),0) totalPrice
        FROM
        product_mkc k with(nolock)
        LEFT JOIN
        productinfo p with(nolock)
        ON
        k.ppriceid=p.ppriceid
        LEFT JOIN
        Ok3w_qudao o with(nolock)
        ON
        o.id=k.insourceid2
        LEFT JOIN
        areainfo a with(nolock)
        ON
        a.id = k.areaid
        WHERE
        k.inbeihuo=2
        and k.kc_check in(1,2,3,6,13,5,8,9,10,11)
        AND k.imeidate is not null
        <if test="param.inSourceId2 != null ">
            and k.insourceid2 = #{param.inSourceId2}
        </if>
        <if test="param.productName != null ">
            and p.product_name like CONCAT('%',#{param.productName},'%')
        </if>
        <if test="param.ppid != null ">
            and k.ppriceid = #{param.ppid}
        </if>
        <if test="param.areaIds != null and param.areaIds.size() > 0 ">
            and k.areaid in
            <foreach collection="param.areaIds" index="index" item="it" separator="," open="(" close=")">
                #{it}
            </foreach>
        </if>
        <if test="param.showKind == 'jxc' ">
            and exists (select 1 from areainfo a with(nolock) where a.id=k.areaid and a.swkind = 1)
        </if>
        <if test="param.startTime != null and param.endTime != null">
            and k.imeidate between #{param.startTime} and #{param.endTime}
        </if>
    </select>
    <select id="queryProductGroupTookCount" resultType="java.lang.Long">
        SELECT COUNT(1) FROM (
        SELECT
        t.area,
        t.ppid,
        t.product_id,
        t.product_name,
        t.product_color,
        t.inprice,
        t.insourceid2,
        t.company_jc companyJc,
        t.imeidate,
        COUNT ( t.mkc_id ) count
        FROM
        (
        SELECT
        k.id mkc_id,
        a.area,
        k.ppriceid ppid,
        p.product_id,
        p.product_name,
        p.product_color,
        ISNULL( k.inbeihuoprice, 0 ) inprice,
        k.insourceid2,
        o.company_jc,
        CONVERT ( nvarchar ( 10 ), k.imeidate, 120 ) imeidate
        FROM
        product_mkc k WITH ( NOLOCK )
        LEFT JOIN productinfo p WITH ( NOLOCK ) ON k.ppriceid = p.ppriceid
        LEFT JOIN Ok3w_qudao o WITH ( NOLOCK ) ON o.id = k.insourceid2
        LEFT JOIN areainfo a WITH ( NOLOCK ) ON a.id = k.areaid
        WHERE
        k.inbeihuo = 2
        AND k.kc_check IN ( 1, 2, 3, 6, 13, 5, 8, 9, 10, 11 )
        AND k.imeidate is not null
        <if test="param.inSourceId2 != null ">
            and k.insourceid2 = #{param.inSourceId2}
        </if>
        <if test="param.productName != null ">
            and p.product_name like CONCAT('%',#{param.productName},'%')
        </if>
        <if test="param.ppid != null ">
            and k.ppriceid = #{param.ppid}
        </if>
        <if test="param.areaIds != null and param.areaIds.size() > 0 ">
            and k.areaid in
            <foreach collection="param.areaIds" index="index" item="it" separator="," open="(" close=")">
                #{it}
            </foreach>
        </if>
        <if test="param.showKind == 'jxc' ">
            and exists (select 1 from areainfo a with(nolock) where a.id=k.areaid and a.swkind = 1)
        </if>
        <if test="param.startTime != null and param.endTime != null">
            and k.imeidate between #{param.startTime} and #{param.endTime}
        </if>
        ) t
        GROUP BY
        t.area,
        t.ppid,
        t.product_id,
        t.product_name,
        t.product_color,
        t.inprice,
        t.insourceid2,
        t.company_jc,
        t.imeidate
        ) TOTAL
    </select>
    <select id="queryProductTakingByPage" resultType="com.jiuji.oa.stock.pdapurchase.vo.res.PurchaseVO">
        SELECT
        k.id AS mkcId,
        k.ppriceid ppid,
        isnull(k.inprice,0) inPrice,
        isnull(k.inbeihuoprice,0) inbehuoprice,
        k.fanli fanli,
        k.isTax isTax,
        p.product_id productId,
        p.product_name productName,
        p.product_color productColor,
        k.areaid areaId,
        k.basket_id basketId,
        k.insourceid2 insourceid2,
        k.insourceid inSourceId,
        o.company_jc companyJc,
        a.pid pid,
        a.province_name provinceName,
        a.area,
        a.area_name areaName,
        xian.allCount,
        DATEDIFF(DAY,GETDATE(),mcgb.expect_time) countdown
        FROM
        product_mkc k with(nolock)
        LEFT JOIN productinfo p with(nolock) ON k.ppriceid = p.ppriceid
        LEFT JOIN Ok3w_qudao o with(nolock) ON o.id = k.insourceid2
        LEFT JOIN areainfo a with(nolock) ON a.id = k.areaid
        LEFT JOIN mkcCaiGouBasket mcgb with(nolock) ON k.id = mcgb.mkc_id
        left join (select sum(case when kk.kc_check=3 then 1 else 0 end) as kcCount,count(1) as allCount ,ppriceid from product_mkc kk with(nolock)
        left join dbo.areainfo aa with(nolock) on kk.areaid=aa.id where kk.insourceid <![CDATA[ <> ]]> 5 and kk.inbeihuoprice is not null
        and kk.inbeihuodate is not null and kk.inbeihuoprice>0 and kk.kc_check in(3,10,2) and kk.basket_id is null
        and isnull(kk.mouldFlag,0)=0 and not exists(select 1 from dbo.xc_mkc x with(nolock) where kk.id=x.mkc_id )   group by kk.ppriceid) xian on xian.ppriceid=k.ppriceid
        where k.kc_check = 1
        and k.inbeihuo = 1
        <include refid="productTakingWhereSql"/>
    </select>
    <select id="queryTotalProductTaking" resultType="com.jiuji.oa.stock.pdapurchase.vo.res.PurchaseTotalVO">
        SELECT
        count(k.id) totalItemToShipped,
        sum(k.inprice) totalInPrice
        FROM product_mkc k with(nolock)
        LEFT JOIN productinfo p with(nolock) ON k.ppriceid = p.ppriceid
        LEFT JOIN Ok3w_qudao o with(nolock) ON o.id = k.insourceid2
        LEFT JOIN areainfo a with(nolock) ON a.id = k.areaid
        LEFT JOIN mkcCaiGouBasket mcgb with(nolock) ON k.id = mcgb.mkc_id
        where k.kc_check = 1
        and k.inbeihuo = 1
        <include refid="productTakingWhereSql"/>
    </select>
    <select id="queryProductTookByPage" resultType="com.jiuji.oa.stock.pdapurchase.vo.res.PurchaseVO">
        SELECT
        k.id AS mkcId,
        a.area,
        k.ppriceid ppid,
        isnull(k.inprice,0) inPrice,
        isnull(k.inbeihuoprice,0) inbehuoprice,
        k.fanli fanli,
        k.areaid areaId,
        k.insourceid inSourceId,
        k.insourceid2 insourceid2,
        k.inbeihuodate,
        k.kc_check kcCheck,
        k.isTax isTax,
        k.imei,
        k.imei2,
        p.product_id productId,
        p.product_name productName,
        p.product_color productColor,
        o.company_jc companyJc,
        a.pid,
        a.province_name provinceName,
        a.area_name areaName
        FROM product_mkc k with(nolock)
        LEFT JOIN productinfo p with(nolock) ON k.ppriceid=p.ppriceid
        LEFT JOIN Ok3w_qudao o with(nolock) ON o.id=k.insourceid2
        LEFT JOIN areainfo a with(nolock) ON a.id = k.areaid
        left join mkcCaiGouBasket mcgb with(nolock) on k.id = mcgb.mkc_id
        WHERE inbeihuo=2
        and kc_check in(1,2,3,6,13,5,8,9,10,11)
        <include refid="productTakingWhereSql"/>
        <if test="param.isEnterSerialNumber != null and param.isEnterSerialNumber==0">
            and  k.imei is null
        </if>
        <if test="param.isEnterSerialNumber != null and param.isEnterSerialNumber==1">
            and  k.imei is not null
        </if>
    </select>
    <select id="queryTotalProductTook" resultType="com.jiuji.oa.stock.pdapurchase.vo.res.PurchaseTotalVO">
        SELECT
        count(k.id) totalItemToShipped,
        sum(k.inbeihuoprice) totalInPrice
        FROM product_mkc k with(nolock)
        LEFT JOIN productinfo p with(nolock) ON k.ppriceid = p.ppriceid
        LEFT JOIN Ok3w_qudao o with(nolock) ON o.id = k.insourceid2
        LEFT JOIN areainfo a with(nolock) ON a.id = k.areaid
        LEFT JOIN mkcCaiGouBasket mcgb with(nolock) ON k.id = mcgb.mkc_id
        where inbeihuo=2
        and kc_check in(1,2,3,6,13,5,8,9,10,11)
        <include refid="productTakingWhereSql"/>
        <if test="param.isEnterSerialNumber != null and param.isEnterSerialNumber==0">
            and  k.imei is null
        </if>
        <if test="param.isEnterSerialNumber != null and param.isEnterSerialNumber==1">
            and  k.imei is not null
        </if>
    </select>
    <select id="queryProductNotakeByPage" resultType="com.jiuji.oa.stock.pdapurchase.vo.res.PurchaseVO">
        select k.ppriceid ppid,
        p.product_id productId,
        p.product_name productName,
        p.product_color productColor,
        k.count as stock,
        k.inprice inPrice
        from (
        select ppriceid,
        MAX(inprice) as inprice,
        COUNT(1) as count
        from product_mkc k with(nolock)
        where kc_check in (1)
        and isnull(inbeihuo , 0)= 0
        <include refid="whereSqlBaseCondition1"/>
        group by ppriceid) k
        left join productinfo p with(nolock) on k.ppriceid = p.ppriceid
        <where>
            <include refid="baseCondition2"/>
        </where>
    </select>
    <select id="queryTotalProductNotake" resultType="com.jiuji.oa.stock.pdapurchase.vo.res.PurchaseTotalVO">
        select sum(t.count) totalItemToShipped,
        sum(t.inprice * t.count) totalInPrice from (
        select k.ppriceid,
        MAX(k.inprice) as inprice,
        COUNT(1) as count
        from product_mkc k with(nolock)
        LEFT JOIN productinfo p with(nolock) ON k.ppriceid = p.ppriceid
        where kc_check in (1)
        and isnull(inbeihuo , 0)= 0
        <include refid="whereSqlBaseCondition1"/>
        <include refid="baseCondition2"/>
        group by k.ppriceid
        ) t
    </select>
    <sql id="whereSqlBaseCondition1" >
        <if test="param.mkcId != null and param.mkcId != '' ">
            and k.id = #{param.mkcId}
        </if>

        <if test="param.inSourceList != null and param.inSourceList.size() > 0">
            and k.insourceid in
            <foreach collection="param.inSourceList" item="it" separator="," open="(" close=")" index="index">
                #{it}
            </foreach>
        </if>

        <if test="param.inSourceId != null and param.inSourceId != '' ">
            and k.insourceid = #{param.inSourceId}
        </if>
        <if test="param.inSourceId2 != null and param.inSourceId2.size() > 0 ">
            and k.insourceid2 in
            <foreach collection="param.inSourceId2" index="index" item="it" separator="," open="(" close=")">
                #{it}
            </foreach>
        </if>

        <if test="param.areaIds != null and param.areaIds.size() > 0 ">
            and k.areaid in
            <foreach collection="param.areaIds" index="index" item="it" separator="," open="(" close=")">
                #{it}
            </foreach>

        </if>

        <if test="param.showKind == 'jxc' ">
            and exists (select 1 from areainfo a with(nolock) where a.id=k.areaid and a.swkind = 1)
        </if>

        <if test="param.pids != null and param.pids.size() > 0 ">
            and exists(select 1 from areainfo a with(nolock) where a.id=k.areaid and a.pid in
            <foreach collection="param.pids" index="index" item="it" separator="," open="(" close=")">
                #{it}
            </foreach>
            )
        </if>
        <if test="param.kind != null and param.kind !=''">
            and exists (select 1 from areainfo a with(nolock) where a.id=k.areaid and a.kind1 = #{param.kind} )
        </if>

        <if test="param.batchNo != null and param.batchNo != '' ">
            and takeBatch= #{param.batchNo}
        </if>

        <if test="param.ch999Name != null and param.ch999Name != '' ">
            and k.beihuoUser like CONCAT('%',#{param.ch999Name},'%')
        </if>

        <if test="param.basketId == 1">
            and k.basket_id is not null
        </if>

        <if test="param.basketId == 0">
            and k.basket_id is null
        </if>
    </sql>

    <sql id="productTakingWhereSql">
        <if test="param.kcCheck == 1">
            and kc_check in(2,3,6,13,5,8,9,10,11)
        </if>
        <if test="param.kcCheck == 0">
            and kc_check in(1)
        </if>
        <if test="param.startTime != null and param.endTime != null">
            and k.inbeihuodate between #{param.startTime} and #{param.endTime}
        </if>
        <if test="param.mkcId != null and param.mkcId != '' ">
            and k.id = #{param.mkcId}
        </if>
        <if test="param.inSourceList != null and param.inSourceList.size() > 0">
            and k.insourceid in
            <foreach collection="param.inSourceList" item="it" separator="," open="(" close=")" index="index">
                #{it}
            </foreach>
        </if>
        <if test="param.inSourceId != null and param.inSourceId != '' ">
            and k.insourceid = #{param.inSourceId}
        </if>
        <if test="param.inSourceId2 != null and param.inSourceId2.size() > 0 ">
            and k.insourceid2 in
            <foreach collection="param.inSourceId2" index="index" item="it" separator="," open="(" close=")">
                #{it}
            </foreach>
        </if>
        <if test="param.areaIds != null and param.areaIds.size() > 0 ">
            and k.areaid in
            <foreach collection="param.areaIds" index="index" item="it" separator="," open="(" close=")">
                #{it}
            </foreach>
        </if>
        <if test="param.showKind == 'jxc' ">
            and exists (select 1 from areainfo a with(nolock) where a.id=k.areaid and a.swkind = 1)
        </if>
        <if test="param.pids != null and param.pids.size() > 0 ">
            and exists(select 1 from areainfo a with(nolock) where a.id=k.areaid and a.pid in
            <foreach collection="param.pids" index="index" item="it" separator="," open="(" close=")">
                #{it}
            </foreach>
            )
        </if>
        <if test="param.kind != null and param.kind !=''">
            and exists (select 1 from areainfo a with(nolock) where a.id=k.areaid and a.kind1 = #{param.kind} )
        </if>
        <if test="param.batchNo != null and param.batchNo != '' ">
            and takeBatch= #{param.batchNo}
        </if>
        <if test="param.ch999Name != null and param.ch999Name != '' ">
            and k.beihuoUser like CONCAT('%',#{param.ch999Name},'%')
        </if>
        <if test="param.basketId == 1">
            and k.basket_id is not null
        </if>
        <if test="param.basketId == 0">
            and k.basket_id is null
        </if>
        <if test="param.brandIds != null and param.brandIds.size() > 0">
            and p.brandid in
            <foreach collection="param.brandIds" index="index" item="it" separator="," open="(" close=")">
                #{it}
            </foreach>
        </if>

        <if test="param.ppids != null and param.ppids.size() > 0 ">
            and p.ppriceid in
            <foreach collection="param.ppids" item="it" index="index" separator="," open="(" close=")">
                #{it}
            </foreach>
        </if>

        <if test="param.productId != null and param.productId != '' ">
            and p.product_id = #{param.productId}
        </if>
        <if test="param.ppid != null and param.ppid != '' ">
            and p.ppriceid = #{param.ppid}
        </if>

        <if test="param.productName != null and param.productName != '' ">
            and p.product_name like CONCAT('%',#{param.productName},'%')
        </if>

        <if test="param.hang != null and param.hang != '' ">
            <choose>
                <when test="param.hang == 1">
                    and p.product_color like CONCAT('%',N'行货','%')
                </when>
                <when test="param.hang == 0">
                    and p.product_color not like CONCAT('%',N'行货','%')
                </when>
            </choose>
        </if>
        <if test="param.cids != null and param.cids.size() > 0 ">
            and p.cid in
            <foreach collection="param.cids" item="it" index="index" separator="," open="(" close=")">
                #{it}
            </foreach>
        </if>
        <if test="param.noCids != null and param.noCids.size() > 0 ">
            and p.brandid not in
            <foreach collection="param.noCids" item="it" index="index" separator="," open="(" close=")">
                #{it}
            </foreach>
        </if>
        <include refid="baseCondition2"></include>
        <if test="param.isTax == 1">
            and k.istax = 1
        </if>
        <if test="param.isTax == 0">
            and k.istax = 0
        </if>
        <if test="param.countdownType != null and param.countdown != null">
            <if test="param.countdownType == 1 ">
                and DATEDIFF(DAY,GETDATE(),mcgb.expect_time) &gt; #{param.countdown}
            </if>
            <if test="param.countdownType == 2 ">
                and DATEDIFF(DAY,GETDATE(),mcgb.expect_time) = #{param.countdown}
            </if>
            <if test="param.countdownType == 3 ">
                and DATEDIFF(DAY,GETDATE(),mcgb.expect_time) &lt; #{param.countdown}
            </if>
        </if>
        <if test="param.optionType != null and param.optionType == 'caigouId' and param.key != null and param.key != ''">
            and mcgb.sub_id = #{param.key}
        </if>
    </sql>
</mapper>
