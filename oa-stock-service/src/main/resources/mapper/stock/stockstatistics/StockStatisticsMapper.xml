<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.stockstatistics.mapper.StockStatisticsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="ProductStatisticsMap" type="com.jiuji.oa.stock.stockstatistics.vo.res.ProductStatisticsResVO">
        <result column="id" property="mkcId" />
        <result column="imei" property="imei" />
        <result column="orderid" property="orderId" />
        <result column="inuser" property="inUser" />
        <result column="mouldFlag" property="mould" />
        <result column="isOnWay" property="onWay" />
        <result column="isOrder" property="order" />
        <result column="area" property="area" />
        <result column="product_name" property="productName" />
        <result column="product_color" property="productColor" />
        <result column="transfer_in_datetime" property="transferInDatetime" />
        <result column="imeidate" property="imeidate" />
        <result column="ppriceid" property="ppid" />
        <result column="staticPrice" property="cost" />
        <result column="insourceid2" property="channelId" />
        <result column="brandID" property="brandId" />
    </resultMap>

    <!-- 统计射结果映射 -->
    <resultMap id="StockStatisticsResultMap" type="com.jiuji.oa.stock.stockstatistics.dto.StockStatisticsDTO">
        <result column="amount" property="totalAmount"/>
        <result column="totalCost" property="totalCost"/>
        <result column="avgCost" property="avgCost"/>
    </resultMap>

    <sql id="commWhere">
        <if test="req.orderType == 2">
            and k.basket_id is not null
        </if>
        <if test="req.orderType == 1">
            and k.basket_id is null
        </if>
        <if test="req.categoryCharSeq != null and req.categoryCharSeq != '' ">
            and p.cid in (select id from f_category_children (#{req.categoryCharSeq}))
        </if>
        <if test="req.brandIds != null and req.brandIds.size > 0 ">
            and p.brandID in
            <foreach collection="req.brandIds" item="it" separator="," open="(" close=")">
                #{it}
            </foreach>
        </if>
        <if test="req.kcChecks != null and req.kcChecks.size > 0 ">
            and k.kc_check in
            <foreach collection="req.kcChecks" item="it" open="(" separator="," close=")">
                #{it}
            </foreach>
        </if>
        <if test="req.channelId != null and req.channelId.size > 0 ">
            and k.insourceid2 in
            <foreach collection="req.channelId" item="it" separator="," open="(" close=")">
                #{it}
            </foreach>
        </if>
        <if test="req.arrivalTime != null and req.arrivalTime.operationType != null and req.arrivalTime.operationValue != null ">
            <if test="req.arrivalTime.operationType==1 ">
                and DateDiff(dd,k.imeidate,getdate()) <![CDATA[ > ]]> #{req.arrivalTime.operationValue}
            </if>
            <if test="req.arrivalTime.operationType==2">
                and DateDiff(dd,k.imeidate,getdate()) <![CDATA[ >= ]]> #{req.arrivalTime.operationValue}
            </if>
            <if test="req.arrivalTime.operationType==3">
                and DateDiff(dd,k.imeidate,getdate()) <![CDATA[ = ]]> #{req.arrivalTime.operationValue}
            </if>
            <if test="req.arrivalTime.operationType==4">
                and DateDiff(dd,k.imeidate,getdate()) <![CDATA[ < ]]> #{req.arrivalTime.operationValue}
            </if>
            <if test="req.arrivalTime.operationType==5">
                and DateDiff(dd,k.imeidate,getdate()) <![CDATA[ <= ]]> #{req.arrivalTime.operationValue}
            </if>
        </if>
        <if test="req.reduceRange != null and req.reduceRange.operationType != null and req.reduceRange.operationValue != null ">
            <if test="req.reduceRange.operationType==1 ">
                and (k.inbeihuoprice - p.costprice) <![CDATA[ > ]]> #{req.reduceRange.operationValue}
            </if>
            <if test="req.reduceRange.operationType==2">
                and (k.inbeihuoprice - p.costprice) <![CDATA[ >= ]]> #{req.reduceRange.operationValue}
            </if>
            <if test="req.reduceRange.operationType==3">
                and (k.inbeihuoprice - p.costprice) <![CDATA[ = ]]> #{req.reduceRange.operationValue}
            </if>
            <if test="req.reduceRange.operationType==4">
                and (k.inbeihuoprice - p.costprice) <![CDATA[ < ]]> #{req.reduceRange.operationValue}
            </if>
            <if test="req.reduceRange.operationType==5">
                and (k.inbeihuoprice - p.costprice) <![CDATA[ <= ]]> #{req.reduceRange.operationValue}
            </if>
        </if>
        <if test="req.defect == 0">
            and not exists( select 1 from xc_mkc x with(nolock) where x.mkc_id = k.id  )
        </if>
        <if test="req.defect == 1">
            and exists (select 1 from xc_mkc x with(nolock) where x.mkc_id = k.id )
        </if>
        <if test="req.mould == 0">
            and isnull(k.mouldFlag,0) = 0
        </if>
        <if test="req.mould == 1">
            and isnull(k.mouldFlag,0) = 1
        </if>
        <if test="req.unsalable == 0">
            and not exists (select 1 from zx_mkc x with(nolock) where x.mkc_id = k.id )
        </if>
        <if test="req.unsalable == 1">
            and exists (select 1 from zx_mkc x with(nolock) where x.mkc_id = k.id )
        </if>
        <if test="req.operationTimeType != null">
            <if test="req.startTime != null and req.endTime != null and req.operationTimeType == 1">
                and k.inbeihuodate between #{req.startTime} and #{req.endTime}
            </if>
            <if test="req.startTime != null and req.endTime != null and req.operationTimeType == 2 ">
                and k.imeidate between #{req.startTime} and #{req.endTime}
            </if>
        </if>
        <if test="req.areaIdsCharSeq != null and req.areaIdsCharSeq != ''">
            and exists(SELECT 1 FROM dbo.F_SPLIT(#{req.areaIdsCharSeq},',') f WHERE f.split_value = CAST(k.areaid AS VARCHAR(20)))
        </if>
        <if test="req.chinaMobileSales != null and req.chinaMobileSales.size > 0 ">
            <choose>
                <when test="req.chinaMobileSales.contains(3) ">
                    and not exists (select 1 from ${req.officeDbName}..mobilemkc mm with(nolock) where mm.mkc_id =k.id)
                </when>
                <otherwise>
                    and exists (SELECT 1 FROM  ${req.officeDbName}..mobileMkc mm with(nolock) WHERE  ISNULL(mm.kind,0) in
                    <foreach collection="req.chinaMobileSales" item="it" open="(" separator="," close=")">
                        #{it}
                    </foreach>
                    AND  mm.mkc_id =k.id )
                </otherwise>
            </choose>
        </if>
        <!--
        <if test="req.userAreaIdList!=null and req.userAreaIdList.size>0">
            and k.areaid in
            <foreach collection="req.userAreaIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if> -->
    </sql>

    <!-- 库存搜索条件 -->
    <sql id="searchByMkcWhere">
        <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==1">
            and k.imei like '%' + #{req.searchValue}
        </if>
        <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==2">
            and k.id = #{req.searchValue}
        </if>
        <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==3">
            and k.ppriceid = #{req.searchValue}
        </if>
        <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==6">
            and k.orderid like '%' + #{req.searchValue} + '%'
        </if>
    </sql>

    <!-- 商品搜索条件 -->
    <sql id="searchByProductWhere">
        <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==4">
            and p.product_name like '%' + #{req.searchValue} + '%'
        </if>
        <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==5">
            and p.productid = #{req.searchValue}
        </if>
    </sql>

    <select id="getProductStatistics" resultMap="ProductStatisticsMap">
        SELECT
        p.product_name,
        p.product_color,
        k.ppriceid,
        k.insourceid2,
        p.brandID,
        p.cid,
        <if test="req.showInPrice">
            ISNULL(k.staticPrice, 0.00) AS staticPrice,
        </if>
        k.id,
        k.orderid,
        k.imei,
        k.inuser,
        k.kc_check,
        k.mouldFlag,
        k.mouldFlag as mould,
        case when kc_check = 10 then 1 else 0 end isOnWay,
        case when basket_id is not null then 1 else 0 end isOrder,
        basket_id as basketId,
        a.area,
        a.pid as productMkcPid,
        k.transfer_in_datetime,
        k.imeidate
        FROM
        product_mkc k WITH (NOLOCK)
        left join areainfo a WITH (NOLOCK) on k.areaid = a.id
        left join productinfo p WITH (NOLOCK) on k.ppriceid = p.ppriceid
        <where>
            <if test="req.ppid != null and req.ppid != '' ">
                and k.ppriceid = #{req.ppid}
            </if>
            <include refid="commWhere"></include>
            <include refid="searchByMkcWhere"></include>
            <include refid="searchByProductWhere"></include>
        </where>
        order by k.id desc
    </select>

    <!-- listStatisticItem -->
    <select id="listStatisticItem" resultType="com.jiuji.oa.stock.stockstatistics.dto.StatisticItemDTO">
        <if test="req.type != null and req.type != '' and req.type == 'brand'">
            SELECT p.brandid as id
            FROM product_mkc AS k WITH (NOLOCK),
                 productinfo AS p WITH (NOLOCK)
            WHERE
                p.ppriceid = k.ppriceid
                <include refid="searchByMkcWhere"></include>
                <include refid="searchByProductWhere"></include>
                <include refid="commWhere"></include>
            GROUP BY p.brandid
            order by p.brandid
        </if>
        <if test="req.type != null and req.type != '' and req.type == 'area'">
            SELECT a.id, a.area AS name
            FROM areainfo a WITH (NOLOCK),
                productinfo AS p WITH (NOLOCK),
                product_mkc AS k WITH (NOLOCK)
            WHERE
                a.id = k.areaid AND p.ppriceid = k.ppriceid
                <include refid="searchByMkcWhere"></include>
                <include refid="searchByProductWhere"></include>
                <include refid="commWhere"></include>
            GROUP BY a.id, a.area
            ORDER BY a.id
        </if>
    </select>

    <!-- getStockStatistics -->
    <select id="getStockStatistics" resultMap="StockStatisticsResultMap">
        SELECT COUNT(1) AS amount
        <if test="req.showInPrice">
            , ISNULL(SUM(k.staticPrice), 0.00) AS totalCost,
            Convert(decimal(18,2), ISNULL(SUM(k.staticPrice) / NULLIF(COUNT(1), 0), 0.00)) AS avgCost
        </if>
        FROM
            productinfo AS p WITH (NOLOCK),
            product_mkc AS k WITH (NOLOCK)
        WHERE
            p.ppriceid = k.ppriceid
            <include refid="searchByMkcWhere"></include>
            <include refid="searchByProductWhere"></include>
            <include refid="commWhere"></include>
        <if test="req.type == 'brand' and req.value!=null and req.value!=0">
            and p.brandID=#{req.value}
        </if>
        <if test="req.type == 'area' and req.value!=null and req.value!=0">
            and k.areaid=#{req.value}
        </if>
    </select>

    <!-- pageStatisticItem -->
    <select id="pageStatisticItem" resultType="com.jiuji.oa.stock.stockstatistics.dto.StatisticItemDTO">
        <if test="req.type != null and req.type != '' and req.type == 'brand'">
            SELECT b.id, b.name, COUNT(k.ppriceid) AS amount
            <if test="req.showInPrice">
                , ISNULL(SUM(k.staticPrice), 0.00) AS totalCost,
                Convert(decimal(18,2), ISNULL(SUM(k.staticPrice) / nullif(COUNT(k.ppriceid), 0), 0.00)) AS avgCost
            </if>
            FROM brand AS b WITH (NOLOCK),
                productinfo AS p WITH (NOLOCK),
                product_mkc AS k WITH (NOLOCK)
            WHERE
                p.ppriceid = k.ppriceid AND p.brandID = b.id
                AND b.id IN
                <foreach collection="itemIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                <include refid="searchByProductWhere"></include>
                <include refid="searchByMkcWhere"></include>
                <include refid="commWhere"></include>
            GROUP BY b.id, b.name
            ORDER BY b.id
        </if>
        <if test="req.type != null and req.type != '' and req.type == 'area'">
            SELECT a.id, a.area AS name, COUNT(k.areaid) AS amount
            <if test="req.showInPrice">
                , ISNULL(SUM(k.staticPrice), 0.00) AS totalCost,
                Convert(decimal(18,2), ISNULL(SUM(k.staticPrice) / nullif(COUNT(k.areaid), 0), 0.00)) AS avgCost
            </if>
            FROM areainfo a WITH (NOLOCK),
                productinfo AS p WITH (NOLOCK),
                product_mkc AS k WITH (NOLOCK)
            WHERE
                a.id = k.areaid AND p.ppriceid = k.ppriceid
                AND a.id IN
                <foreach collection="itemIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                <include refid="searchByProductWhere"></include>
                <include refid="searchByMkcWhere"></include>
                <include refid="commWhere"></include>
            GROUP BY a.id, a.area
            ORDER BY a.id
        </if>
    </select>

    <select id="getPageStatisticItem" resultType="com.jiuji.oa.stock.stockstatistics.dto.StatisticItemDTO">
        <if test="req.type != null and req.type != '' and req.type == 'brand'">
            SELECT t.* FROM (
            SELECT b.id, b.name, COUNT(k.ppriceid) AS amount
            <if test="req.showInPrice">
                , ISNULL(SUM(k.staticPrice), 0.00) AS totalCost,
                Convert(decimal(18,2), ISNULL(SUM(k.staticPrice) / nullif(COUNT(k.ppriceid), 0), 0.00)) AS avgCost
            </if>
            FROM brand AS b WITH (NOLOCK),
            productinfo AS p WITH (NOLOCK),
            product_mkc AS k WITH (NOLOCK)
            WHERE
            p.ppriceid = k.ppriceid AND p.brandID = b.id
            <include refid="searchByProductWhere"></include>
            <include refid="searchByMkcWhere"></include>
            <include refid="commWhere"></include>
            GROUP BY b.id, b.name
            ) t order by t.amount desc
        </if>
        <if test="req.type != null and req.type != '' and req.type == 'area'">
            SELECT t.* FROM (
            SELECT a.id, a.area AS name, COUNT(k.areaid) AS amount
            <if test="req.showInPrice">
                , ISNULL(SUM(k.staticPrice), 0.00) AS totalCost,
                Convert(decimal(18,2), ISNULL(SUM(k.staticPrice) / nullif(COUNT(k.areaid), 0), 0.00)) AS avgCost
            </if>
            FROM areainfo a WITH (NOLOCK),
            productinfo AS p WITH (NOLOCK),
            product_mkc AS k WITH (NOLOCK)
            WHERE
            a.id = k.areaid AND p.ppriceid = k.ppriceid
            <include refid="searchByProductWhere"></include>
            <include refid="searchByMkcWhere"></include>
            <include refid="commWhere"></include>
            GROUP BY a.id, a.area
            ) t order by t.amount desc
        </if>
    </select>
    <select id="getPageStatisticItemCount" resultType="java.lang.Integer">
        <if test="req.type != null and req.type != '' and req.type == 'brand'">
            SELECT * INTO #brandstockstatistics FROM (
            SELECT b.id, b.name, COUNT(k.ppriceid) AS amount
            <if test="req.showInPrice">
                , ISNULL(SUM(k.staticPrice), 0.00) AS totalCost,
                Convert(decimal(18,2), ISNULL(SUM(k.staticPrice) / nullif(COUNT(k.ppriceid), 0), 0.00)) AS avgCost
            </if>
            FROM brand AS b WITH (NOLOCK),
            productinfo AS p WITH (NOLOCK),
            product_mkc AS k WITH (NOLOCK)
            WHERE
            p.ppriceid = k.ppriceid AND p.brandID = b.id
            <include refid="searchByProductWhere"></include>
            <include refid="searchByMkcWhere"></include>
            <include refid="commWhere"></include>
            GROUP BY b.id, b.name
            ) t ;
            SELECT count(*) FROM  #brandstockstatistics ;
            drop table #brandstockstatistics ;
        </if>
        <if test="req.type != null and req.type != '' and req.type == 'area'">
            SELECT * INTO #areastockstatistics FROM (
            SELECT a.id, a.area AS name, COUNT(k.areaid) AS amount
            <if test="req.showInPrice">
                , ISNULL(SUM(k.staticPrice), 0.00) AS totalCost,
                Convert(decimal(18,2), ISNULL(SUM(k.staticPrice) / nullif(COUNT(k.areaid), 0), 0.00)) AS avgCost
            </if>
            FROM areainfo a WITH (NOLOCK),
            productinfo AS p WITH (NOLOCK),
            product_mkc AS k WITH (NOLOCK)
            WHERE
            a.id = k.areaid AND p.ppriceid = k.ppriceid
            <include refid="searchByProductWhere"></include>
            <include refid="searchByMkcWhere"></include>
            <include refid="commWhere"></include>
            GROUP BY a.id, a.area
            ) t ;
            SELECT count(*) FROM  #areastockstatistics;
            drop table #areastockstatistics;
        </if>
    </select>

    <!-- listProductSkuByType -->
    <select id="listProductSkuByType" resultType="com.jiuji.oa.stock.stockstatistics.dto.ProductSkuDTO">
        SELECT p.ppriceid AS ppid, p.product_name AS name, p.product_color AS productColor, pm.amount
        <if test="req.showInPrice">
            , pm.totalCost
            , Convert(decimal(18,2), pm.totalCost / pm.amount) AS avgCost
        </if>
        , ca.name AS categoryName, b.name AS brandName
        FROM productinfo as p with (nolock)
        left join brand as b with (nolock) on p.brandID = b.id,
        category AS ca WITH (NOLOCK),
        (SELECT k.ppriceid, COUNT(k.ppriceid) AS amount, ISNULL(SUM(k.staticPrice), 0.00) AS totalCost
            FROM productinfo AS p WITH (NOLOCK),
            product_mkc AS k WITH (NOLOCK)
            WHERE
                p.ppriceid = k.ppriceid
                <include refid="searchByProductWhere"></include>
                <include refid="searchByMkcWhere"></include>
                <include refid="commWhere"></include>
                <if test="req.type != null and req.type != '' and req.type == 'brand'">
                    AND p.brandID = #{req.typeId}
                </if>
                <if test="req.type != null and req.type != '' and req.type == 'area'">
                    AND k.areaid = #{req.typeId}
                </if>
                <if test="req.productId!=null and req.productId!=0">
                    and p.productid=#{req.productId}
                </if>
            GROUP BY k.ppriceid) AS pm
        <where>p.cid = ca.ID
                AND p.ppriceid = pm.ppriceid
        </where>
        order by pm.amount desc
    </select>

    <select id="getProductStatisticsTotal"
            resultType="com.jiuji.oa.stock.stockstatistics.vo.res.ProductStatisticsTotalRes">
        SELECT COUNT(1) amount
        <if test="req.showInPrice">
            , isnull(SUM(k.staticPrice),0) totalCost,
            Convert(decimal(18,2), isnull(SUM(k.staticPrice)/COUNT(1),0)) AS avgCost
        </if>
        FROM
        product_mkc k WITH (NOLOCK)
        left join areainfo a WITH (NOLOCK) on k.areaid = a.id
        left join productinfo p WITH (NOLOCK) on k.ppriceid = p.ppriceid
        <where>
            <if test="req.ppid != null and req.ppid != '' ">
                and k.ppriceid = #{req.ppid}
            </if>
            <include refid="commWhere"></include>
            <include refid="searchByMkcWhere"></include>
            <include refid="searchByProductWhere"></include>
        </where>
    </select>
    <select id="listPageProductByType"
            resultType="com.jiuji.oa.stock.stockstatistics.dto.ProductDTO">
        SELECT p.id AS product_id, p.name, pm.amount
        <if test="req.showInPrice">
            , pm.totalCost
            , Convert(decimal(18,2), pm.totalCost / pm.amount) AS avgCost
        </if>
        , ca.name AS categoryName, b.name AS brandName
        FROM product AS p WITH(NOLOCK) left join brand as b with (nolock) on p.brandID = b.id,
        category AS ca WITH(NOLOCK),
        (SELECT p.productid, COUNT(p.productid) AS amount, ISNULL(SUM(k.staticPrice), 0.00) AS totalCost
        FROM productinfo AS p WITH(NOLOCK),
        product_mkc AS k WITH(NOLOCK)
        WHERE
        p.ppriceid = k.ppriceid
        <include refid="searchByProductWhere"></include>
        <include refid="searchByMkcWhere"></include>
        <include refid="commWhere"></include>
        <if test="req.type != null and req.type != '' and req.type == 'brand'">
            AND p.brandID = #{req.typeId}
        </if>
        <if test="req.type != null and req.type != '' and req.type == 'area'">
            AND k.areaid = #{req.typeId}
        </if>
        GROUP BY p.productid) AS pm
        WHERE p.cid = ca.ID AND p.id = pm.productid
        order by pm.amount desc
    </select>
    <select id="getStockAmountByPpid" resultType="com.jiuji.oa.stock.stockstatistics.vo.res.StockAmountResVO">
        SELECT COUNT(1) AS totalAmount,k.ppriceid  AS ppid
        FROM
        product_mkc k WITH (NOLOCK)
        left join productinfo p WITH (NOLOCK) on k.ppriceid = p.ppriceid
        <where> k.ppriceid = #{req.ppid}
        </where>
        group by k.ppriceid
    </select>
    <select id="getStockAmountByPpidAndArea"
            resultType="com.jiuji.oa.stock.stockstatistics.vo.res.StockAreaAmountResVO">
        SELECT a.id as areaId,k.ppriceid  AS ppid, COUNT(k.areaid) AS totalAmount
        FROM areainfo a WITH (NOLOCK),
        productinfo AS p WITH (NOLOCK),
        product_mkc AS k WITH (NOLOCK)
        WHERE k.ppriceid = #{req.ppid} AND
        a.id = k.areaid AND p.ppriceid = k.ppriceid AND a.id IN
        <foreach collection="req.areaIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY a.id,k.ppriceid
        ORDER BY a.id
    </select>

    <select id="getStockAmountByPpidBatch"
            resultType="com.jiuji.oa.stock.stockstatistics.vo.res.StockAmountResVO">
        select ppriceid AS ppid,count(1) AS totalAmount from product_mkc k with(nolock) where
        kc_check in(3,10) and isnull(mouldFlag,0)=0 and basket_id is null and not exists(select 1 from dbo.xc_mkc xc
        with(nolock) where xc.mkc_id=k.id and isnull(xc.isLock,0)=1 )
        and exists(select 1 from dbo.areainfo a with(nolock) where k.areaid=a.id and a.kind1 in (1,2) and
        a.attribute=1101)
        and k.ppriceid IN
        <foreach collection="mobilePpid" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by k.ppriceid
    </select>

    <select id="getUnLockedStockAmountByPpidBatch"
            resultType="com.jiuji.oa.stock.stockstatistics.vo.res.StockAmountResVO">
        select ISNULL(sum(b.basket_count),0) as totalAmount, p.ppriceid1 as ppid from dbo.basket b with(nolock) left join dbo.productinfo p on p.ppriceid = b.ppriceid
        left  join dbo.sub s with(nolock)
        on s.sub_id = b.sub_id
        where s.sub_check in (0,1) and isnull(b.isdel,0)=0 and isnull(b.ischu,0)=0 and p.ppriceid1 IN
        <foreach collection="mobilePpid" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and exists(select 1 from dbo.areainfo a with(nolock) where s.areaid=a.id and a.kind1 in (1,2) and a.attribute=1101)
        and not exists(select 1 from dbo.product_mkc k with(nolock) where b.basket_id=k.basket_id and k.kc_check in (2,3,10,6) )
        group by p.ppriceid1
    </select>
    <select id="getProductSkuStatisticsTotal"
            resultType="com.jiuji.oa.stock.stockstatistics.vo.res.ProductStatisticsTotalRes">
        SELECT COUNT(1) amount
        <if test="req.showInPrice">
            , isnull(SUM(k.staticPrice),0) totalCost,
            Convert(decimal(18,2), isnull(SUM(k.staticPrice)/COUNT(1),0)) AS avgCost
        </if>
        FROM
        product_mkc k WITH (NOLOCK)
        left join areainfo a WITH (NOLOCK) on k.areaid = a.id
        left join productinfo p WITH (NOLOCK) on k.ppriceid = p.ppriceid
        <where>
            <if test="req.type != null and req.type != '' and req.type == 'brand'">
                AND p.brandID = #{req.typeId}
            </if>
            <if test="req.type != null and req.type != '' and req.type == 'area'">
                AND k.areaid = #{req.typeId}
            </if>
            <if test="req.productId!=null and req.productId!=0">
                and p.productid=#{req.productId}
            </if>
            <include refid="commWhere"></include>
            <include refid="searchByMkcWhere"></include>
            <include refid="searchByProductWhere"></include>
        </where>
    </select>
</mapper>
