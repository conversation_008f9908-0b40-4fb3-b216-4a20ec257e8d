<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.stockstatistics.mapper.QueryMkcStockMapper">

    <select id="queryMkcStock" resultType="com.jiuji.oa.stock.stockstatistics.vo.res.QueryMkcStockResVO">
        select
        (case when mi.mkc_id is not null then  cast(isnull(mi.kind,0) as nvarchar(10)) else ''  end) as mobileidentity,
        k.id as mkc_id,
        p.product_name,p.product_color,p.productid productId,isnull(k.imei3,'') as sn,isnull(k.imei2,'') as imei2,
        isnull((k.staticprice-isnull(p.costprice,k.staticprice)),0) as jiang,
        k.ppriceid ppid,k.orderid,k.inbeihuo,k.inbeihuodate,isnull(k.transferprice,k.inbeihuoprice) inbeihuoprice,
        k.imeidate,k.imei,k.inuser,k.imeiuser,k.kc_check,
        k.areaid,k.frareaid,k.basket_id,s.sub_id,k.inprice,k.origareaid,k.caigoulock,
        k.pandian,k.pandiandate,k.pandianuser,k.cangkuid,
        k.rank,k.insourceid,k.fanli,k.mouldflag,k.insourceid2,q.company_jc,
        p.memberprice, k.protectprice,k.modifyprice,k.staticprice,
        case when xc.mkc_id is null then 0 else 1 end as isxc,
        case when gi.mkc_id is null then 0 else 1 end as isGbAnomaly,
        k.transfer_in_datetime transferInDatetime,p.brandid brandId,p.bpic,p.cid
        <if test="req.xtenant > 1000">
            ,k.examPrice
        </if>
        <!-- ,i.name as insource_name,mb.fid -->
        <if test="req.searchKind == 'zc'">
            ,d.check2dtime
        </if>
        <if test="req.searchKind == 'xc'">
            ,x.isLock
        </if>
        from product_mkc k with(nolock)
        left join dbo.ok3w_qudao q with ( nolock ) on k.insourceid2= q.id
        left join productinfo p with(nolock) on k.ppriceid=p.ppriceid
        left join basket b with(nolock) on k.basket_id=b.basket_id
        left join sub s with(nolock) on b.sub_id=s.sub_id
        left join ${req.officeDbName}..mobilemkc mi  with(nolock) on mi.mkc_id =k.id
        left join dbo.xc_mkc xc  with(nolock) on k.id = xc.mkc_id
        left join product_mkc_guobu_info gi with(nolock) on gi.mkc_id=k.id and DATEADD(hour, 48, gi.create_time)>GETDATE() and gi.is_del=0
        <!--
        left join insource i with(nolock) on k.insourceid=i.id
        left join dbo.mobilemockupbinding mb with(nolock) on (mb.imei =k.imei and isnull(mb.isdel,0)=0)
        -->
        <if test="req.isyj != null and req.isyj != ''">
            left join model_machine_depreciation yj with(nolock) on yj.mkc_id=k.id and yj.is_del=0
        </if>
        <if test="req.searchKind == 'zc'">
            left join (select * from dbo.mkc_dellogs with(nolock) where kinds='h2') d on k.id=d.mkc_id
        </if>
        where 1=1
        <include refid="whereSql" />
    </select>
    <select id="queryMkcIdByImei" resultType="java.lang.Integer">
        select top 1 k.id from product_mkc k with(nolock)
        where k.imei=#{imei}
    </select>
    <select id="queryMkcIdByMkcId" resultType="java.lang.Integer">
        select top 1 k.id from product_mkc k with(nolock)
        where k.id=#{mkcId}
    </select>
    <select id="querySumMkcStock" resultType="com.jiuji.oa.stock.stockstatistics.vo.res.QueryMkcStockSumResVO">
        select count(k.id) totalCount,
               sum(k.staticprice) totalStaticprice,
               avg(k.staticprice) avgStaticprice
        from product_mkc k with(nolock)
        left join dbo.ok3w_qudao q with ( nolock ) on k.insourceid2= q.id
        left join productinfo p with(nolock) on k.ppriceid=p.ppriceid
        left join basket b with(nolock) on k.basket_id=b.basket_id
        left join sub s with(nolock) on b.sub_id=s.sub_id
        left join ${req.officeDbName}..mobilemkc mi  with(nolock) on mi.mkc_id =k.id
        left join dbo.xc_mkc xc  with(nolock) on k.id = xc.mkc_id
        left join product_mkc_guobu_info gi with(nolock) on gi.mkc_id=k.id and DATEADD(hour, 48, gi.create_time)>GETDATE() and gi.is_del=0
        <!--
        left join insource i with(nolock) on k.insourceid=i.id
        left join dbo.mobilemockupbinding mb with(nolock) on (mb.imei =k.imei and isnull(mb.isdel,0)=0)
        -->
        <if test="req.isyj != null and req.isyj != ''">
            left join model_machine_depreciation yj with(nolock) on yj.mkc_id=k.id and yj.is_del=0
        </if>
        <if test="req.searchKind == 'zc'">
            left join (select * from dbo.mkc_dellogs with(nolock) where kinds='h2') d on k.id=d.mkc_id
        </if>
        where 1=1
        <include refid="whereSql" />
    </select>

    <sql id="whereSql">
        <if test="req.kcCheckList != null and req.kcCheckList.size > 0 ">
            and k.kc_check in
            <foreach collection="req.kcCheckList" item="it" separator="," open="(" close=")">
                #{it}
            </foreach>
        </if>
        <choose>
            <when test="req.otherinsourceid2 != null and req.otherinsourceid2 != ''">
                and k.insourceid2 = #{req.otherinsourceid2} and k.kc_check in (3,10,5)
            </when>
            <otherwise>
                <if test="req.areaKind1 != 1 ">
                    and exists( select 1 from dbo.areainfo a with(nolock) where authorizeid=#{req.authorizeid} and a.id=k.areaid  )
                </if>
                <if test="!req.o5Flag">
                    <if test="req.xtenant == 2 or req.xtenant == 3 ">
                        and ( k.areaid=#{req.curAreaid} or exists(select 1 from Ok3w_qudao o with(nolock) where o.id=k.insourceid2 and o.bindAreaId=#{req.curAreaid}  ))
                    </if>
                </if>
                <if test="req.isMpage">
                    <choose>
                        <when test="req.areakind1 != 1 and req.all == 'all'">
                            and exists(select 1 from dbo.areainfo a with(nolock) where authorizeid= #{req.authorizeid} and a.id=k.areaid )
                        </when>
                        <otherwise>
                            <if test="req.searchKind != 'zc' and req.searchKind != 'xc'">
                                and exists( select 1 from dbo.areainfo a with(nolock) where authorizeid=#{req.authorizeid} and a.id=k.areaid  )
                            </if>
                        </otherwise>
                    </choose>
                </if>
            </otherwise>
        </choose>
        <if test='req.basketId == "0"'>
            and k.basket_id is null
        </if>
        <if test='req.basketId == "1"'>
            and k.basket_id is not null
        </if>
        <if test="req.isxc != null and req.isxc != ''">
            <choose>
                <when test="req.searchKind == 'xc'">
                    and xc.mkc_id is not null
                </when>
                <otherwise>
                    <if test='req.isxc == "1"'>
                        and xc.mkc_id is not null
                    </if>
                    <if test='req.isxc == "0"'>
                        and xc.mkc_id is null
                    </if>
                </otherwise>
            </choose>
        </if>
        <if test="req.isgh != null">
            <choose>
                <when test="req.isgh">
                    and exists( select 1 from gh_mkc AS gh with(nolock) where gh.mkc_id=k.id  )
                </when>
                <otherwise>
                    and not exists( select 1 from gh_mkc AS gh with(nolock) where gh.mkc_id=k.id  )
                </otherwise>
            </choose>
        </if>
        <if test='req.iszx == "1"'>
            and exists( select 1 from zx_mkc x with(nolock) where x.mkc_id=k.id  )
        </if>
        <if test='req.iszx == "0"'>
            and not exists( select 1 from zx_mkc x with(nolock) where x.mkc_id=k.id  )
        </if>
        <if test='req.isSn!=null and req.isSn=="1"'>
            AND isnull(k.imei3,'')!='' AND isnull(k.imei3,'')!=''
        </if>
        <if test='req.isSn!=null and req.isSn=="0"'>
            AND (isnull(k.imei3,'')='' OR isnull(k.imei3,'')='')
        </if>
        <if test='req.isGbAnomaly!=null and req.isGbAnomaly=="1"'>
            and gi.mkc_id is not null
        </if>
        <if test='req.isGbAnomaly!=null and req.isGbAnomaly=="0"'>
            and gi.mkc_id is null
        </if>
        <if test='req.isyj == "1"'>
            and isnull(k.mouldFlag,0)=1
            <if test='req.ysscType == "1" and req.yssc != null '>
                and mr.duration &gt; #{req.yssc}
            </if>
            <if test='req.ysscType == "2" and req.yssc != null '>
                and mr.duration &lt; #{req.yssc}
            </if>
        </if>
        <if test='req.isyj == "0"'>
            and isnull(k.mouldFlag,0)=0
        </if>
        <if test='req.ishs == "1"'>
            and exists (SELECT 1 FROM dbo.mkc_dellogs md WITH(NOLOCK) WHERE md.kinds='xch' AND md.mkc_id=k.id)
        </if>
        <if test='req.ishs == "0"'>
            and not exists (SELECT 1 FROM dbo.mkc_dellogs md WITH(NOLOCK) WHERE md.kinds='xch' AND md.mkc_id=k.id)
        </if>
        <if test='req.hasInputImei == "0"'>
            and k.imei is null
        </if>
        <if test='req.hasInputImei == "1"'>
            and k.imei is not null
        </if>
        <if test="req.proMark != null and req.proMark.size > 0 ">
            and p.markId in
            <foreach collection="req.proMark" item="it" separator="," open="(" close=")">
                #{it}
            </foreach>
        </if>
        <if test="req.chinaMobileSaleList != null and req.chinaMobileSaleList.size > 0 ">
            <choose>
                <when test="req.chinaMobileSaleList.contains(3) ">
                    and not exists (select 1 from ${req.officeDbName}..mobilemkc mm with(nolock) where mm.mkc_id =k.id)
                </when>
                <otherwise>
                    and exists (SELECT 1 FROM  ${req.officeDbName}..mobileMkc mm with(nolock) WHERE  ISNULL(mm.kind,0) in
                    <foreach collection="req.chinaMobileSaleList" item="it" open="(" separator="," close=")">
                        #{it}
                    </foreach>
                    AND  mm.mkc_id =k.id )
                </otherwise>
            </choose>
        </if>
        <if test="req.xcLock != null and req.xcLock != '' and req.searchKind == 'xc'">
            and isnull(xc.isLock,0)=#{req.xcLock}
        </if>
        <if test="req.mainkey != null and req.mainkey != ''">
            <if test="req.kind1 == 'product_name'">
                and p.product_name like '%'+#{req.mainkey}+'%'
            </if>
            <if test="req.kind1 == 'orderid'">
                and k.orderid like '%'+#{req.mainkey}+'%'
            </if>
            <if test="req.kind1 == 'imei'">
                and k.imei like '%'+#{req.mainkey}+'%'
            </if>
            <if test="req.kind1 == 'mkc_id' and req.mainkeyList != null and req.mainkeyList.size > 0 ">
                and k.id in
                <foreach collection="req.mainkeyList" item="it" separator="," open="(" close=")">
                    #{it}
                </foreach>
            </if>
            <if test="req.kind1 == 'ppriceid' and req.mainkeyList != null and req.mainkeyList.size > 0 ">
                and k.ppriceid in
                <foreach collection="req.mainkeyList" item="it" separator="," open="(" close=")">
                    #{it}
                </foreach>
            </if>
            <if test="req.kind1 == 'days'">
                and datediff(day,k.imeidate,getdate()) &gt;= #{req.mainkey}
            </if>
            <if test="req.kind1 == 'costprice'">
                and (k.inbeihuoprice-p.costprice) &gt;= #{req.mainkey}
            </if>
            <if test="req.kind1 == 'productid' and req.mainkeyList != null and req.mainkeyList.size > 0 ">
                and p.productid in
                <foreach collection="req.mainkeyList" item="it" separator="," open="(" close=")">
                    #{it}
                </foreach>
            </if>
            <if test="req.kind1 == 'imeiuser'">
                and k.imeiuser= #{req.mainkey}
            </if>
            <if test="req.kind1 == 'imei2'">
                and k.imei2 like '%'+#{req.mainkey}+'%'
            </if>
            <if test="req.kind1 == 'sn'">
                and k.imei3 like '%'+#{req.mainkey}+'%'
            </if>
        </if>
        <if test="req.hwLimitAreaids != null and req.hwLimitAreaids.size > 0">
            and p.brandID = 7
        </if>
        <if test='req.gaoji == "1"'>
            <if test="req.dateKind == 'tradedate1' and req.date1 != null and req.date2 != null">
                and s.tradeDate1 between #{req.date1} and #{req.date2}
            </if>
            <if test="req.dateKind == 'imeidate' and req.date1 != null and req.date2 != null">
                and k.imeidate between #{req.date1} and #{req.date2}
            </if>
            <if test="req.dateKind == 'inbeihuodate' and req.date1 != null and req.date2 != null">
                and k.inbeihuodate between #{req.date1} and #{req.date2}
            </if>
            <!--<if test="req.cidList != null and req.cidList.size > 0">
                and exists( select 1 from f_category_children(#{req.cid) f where f.id=p.cid )
            </if>
            <if test="req.cidsVal != null and req.cidsVal != ''">
                and exists( select 1 from f_category_children(#{req.cids_val}) f where f.id=p.cid )
            </if>-->
            <if test="req.categoryCharSeq != null and req.categoryCharSeq != '' ">
                and p.cid in (select id from f_category_children (#{req.categoryCharSeq}))
            </if>

            <if test="req.brandIdList != null and req.brandIdList.size > 0">
                and p.brandID in
                <foreach collection="req.brandIdList" item="it" separator="," open="(" close=")">
                    #{it}
                </foreach>
            </if>
            <!--<if test="req.brandid == '0' and req.yJBrandIdList.size > 0">
                and p.brandID in (conn.YJBrandIdList)
            </if>-->
            <if test="req.insourceid != null and req.insourceid != ''">
                and k.insourceid=#{req.insourceid}
            </if>
            <if test="req.insourceid2List != null and req.insourceid2List.size > 0">
                and k.insourceid2 in
                <foreach collection="req.insourceid2List" item="it" separator="," open="(" close=")">
                    #{it}
                </foreach>
            </if>
            <if test='req.isSale == "1"'>
                and exists(select 1 from ${req.officeDbName}..mobileMkc m with(nolock) where k.id=m.mkc_id)
            </if>
            <if test="req.imeiuser != null and req.imeiuser != ''">
                and k.imeiuser=#{req.imeiuser}
            </if>
            <!--<if test="req.imeis != null and req.imeis != ''">
                and k.imei in
            </if>-->
        </if>
        <if test="req.searchKind == 'xc'">
            <if test="req.zcdays != null and req.zcdays > 0">
                and x.dtime &lt;= getdate() - #{req.zcdays}
            </if>
            <if test="req.dateKind == 'xiacidate' and req.date1 != null and req.date2 != null">
                and x.dtime between #{req.date1} and #{req.date2}
            </if>
        </if>
        <if test="req.areaIdsCharSeq != null and req.areaIdsCharSeq != ''">
            and exists(SELECT 1 FROM dbo.F_SPLIT(#{req.areaIdsCharSeq},',') f WHERE f.split_value = CAST(k.areaid AS VARCHAR(20)))
        </if>
        <!--<if test="req.areaIdList != '' and req.areaIdList.size > 0 and ((#{req.xtenant} != 2 and conn.xtenant != 3 and !o5Flag) || o5Flag) ">
            and k.areaid in
            <foreach collection="req.areaIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>-->
        <!--<if test="req.webArea_ != '' and ((conn.xtenant != (int)EXtenant.华为授权店 and conn.xtenant != (int)EXtenant.九讯苹果体验店 and !o5Flag) || o5Flag) ">
            and k.areaid in
        </if>-->
        <if test="req.areaLevel != null and req.areaLevel.size > 0 ">
            and exists (select 1 from dbo.areainfo ar with(nolock) where k.areaid = ar.id and ar.level1= #{req.areaLevel})
        </if>
        <if test="req.searchKind == 'zc'">
            and d.check2dtime is not null and not exists(select 1 from xc_mkc x with(nolock) where x.mkc_id=k.id and isnull(x.isLock,0)=1 )
            <if test="req.zcdays > 0">
                and d.check2dtime &lt;= getdate() - #{req.zcdays}
            </if>
            <if test='req.gaoji == "1" and req.dateKind == "check2dtime" and req.date1 != null and req.date2 != null'>
                and d.check2dtime between #{req.date1} and #{req.date2}
            </if>
        </if>
        <if test="req.isTaxModel">
            and exists(select 1  from dbo.channel_kind_link ck with(nolock) where isnull(ck.channel_state,0)=1 and k.insourceid2=ck.channel_id )
            <if test="req.taxCompanyId == null or req.taxCompanyId.size == 0">
                and isnull(k.isTax,0)=1 and exists ( select 1 from dbo.areainfo a1 with(nolock) where a1.id =  k.areaid and a1.ispass=1 and a1.taxpayer =1
                and exists(SELECT 1 FROM dbo.F_SPLIT(#{req.areaIdsCharSeq},',') f WHERE f.split_value = CAST(a1.area AS VARCHAR(20))) )
            </if>
            <if test="req.taxCompanyId != null and req.taxCompanyId.size > 0">
                and isnull(k.isTax,0)=1 and exists ( select id from dbo.areainfo a1 with(nolock) where a1.id = k.areaid and a1.ispass=1 and a1.taxpayer =1
                and exists(SELECT 1 FROM dbo.F_SPLIT(#{req.areaIdsCharSeq},',') f WHERE f.split_value = CAST(a1.area AS VARCHAR(20)))
                and isnull(a1.main_company,0) in
                <foreach collection="req.taxCompanyId" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="req.o5Flag != null and req.o5Flag">
            and exists(select 1 from areainfo a1 with(nolock) where a1.id = k.areaid and a1.ispass=1 and a1.xtenant ={req.xtenant})
            <if test="req.channelIds != null and req.channelIds.size > 0">
                and k.insourceid2 in
                <foreach collection="req.channelIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="req.isyj != null and req.isyj != ''">
            <if test="req.yjHasBind != null and req.yjHasBind != '' ">
                and (case when mb.Id> 0 then 1 else 0 end )=#{req.yjHasBind}
            </if>
            <if test='req.gaoji != "1" and req.depreciationFlag != null and req.depreciationFlag != "" '>
                <choose>
                    <when test='req.depreciationFlag == "0"'>
                        and yj.id>0 and isnull(yj.depreciation_flag,0)=0
                    </when>
                    <otherwise>
                        and (isnull(yj.id,0)=0 or yj.depreciation_flag=1)
                    </otherwise>
                </choose>
            </if>
        </if>
    </sql>
</mapper>
