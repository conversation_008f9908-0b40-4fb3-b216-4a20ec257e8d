<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.stockstatistics.mapper.StockMapper">

    <!-- 商品搜索条件 -->
    <sql id="searchByProductWhere">
        <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==1">
            and p.ppriceid = #{req.searchValue}
        </if>
        <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==2">
            and p.productid = #{req.searchValue}
        </if>
        <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==3">
            and trim(p.product_name + ' ' + isnull(p.product_color,'')) like '%' + #{req.searchValue} + '%'
        </if>
        <if test="req.categoryCharSeq != null and req.categoryCharSeq != '' ">
            and p.cid in (select id from f_category_children (#{req.categoryCharSeq}))
        </if>
        <if test="req.brandIds != null and req.brandIds.size > 0 ">
            and p.brandID in
            <foreach collection="req.brandIds" item="it" separator="," open="(" close=")">
                #{it}
            </foreach>
        </if>
        <if test="req.ppidType != null and req.ppidType == 1">
            and p.ismobile1 = 1
        </if>
        <if test="req.ppidType != null and req.ppidType == 2">
            and p.ismobile1 = 0 and p.cid not in (select id from f_category_children('23'))
        </if>
        <if test="req.ppidType != null and req.ppidType == 3">
            and p.cid in (select id from f_category_children('23'))
        </if>
        <if test="req.labels != null and req.labels.size > 0 ">
            and p.pLabel in
            <foreach collection="req.labels" item="it" separator="," open="(" close=")">
                #{it}
            </foreach>
        </if>
    </sql>


    <sql id="getMobileKc">
        SELECT k.ppriceid AS ppriceid, k.areaid as areaId, COUNT(k.id) AS amount
        FROM product_mkc AS k WITH (NOLOCK)
        <where>
            k.kc_check in (2,3) and k.basket_id is null
            <if test="req.channelIdList != null and req.channelIdList.size > 0">
                and k.insourceid2 in
                <foreach collection="req.channelIdList" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.flaw == 0">
                and not exists( select 1 from xc_mkc x with(nolock) where x.mkc_id = k.id )
            </if>
            <if test="req.flaw == 1">
                and exists (select 1 from xc_mkc x with(nolock) where x.mkc_id = k.id )
            </if>
            <if test="req.mould == 0">
                and isnull(k.mouldFlag,0) = 0
            </if>
            <if test="req.mould == 1">
                and isnull(k.mouldFlag,0) = 1
            </if>
        </where>
        GROUP BY k.areaid, k.ppriceid
    </sql>

    <sql id="getMobileBasketKc">
        SELECT k.ppriceid AS ppriceid, k.areaid as areaId, COUNT(id) AS amount
        FROM product_mkc AS k WITH (NOLOCK)
        <where>
            k.kc_check in (2,3) and k.basket_id is not null
            <if test="req.channelIdList != null and req.channelIdList.size > 0">
                and k.insourceid2 in
                <foreach collection="req.channelIdList" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.flaw == 0">
                and not exists( select 1 from xc_mkc x with(nolock) where x.mkc_id = k.id )
            </if>
            <if test="req.flaw == 1">
                and exists (select 1 from xc_mkc x with(nolock) where x.mkc_id = k.id )
            </if>
            <if test="req.mould == 0">
                and isnull(k.mouldFlag,0) = 0
            </if>
            <if test="req.mould == 1">
                and isnull(k.mouldFlag,0) = 1
            </if>
        </where>
        GROUP BY k.areaid, k.ppriceid
    </sql>

    <sql id="getAccessaoriesBasketKc">
        select o.ppriceid    as ppriceid,
               o.areaid      as areaId,
               sum(o.lcount) as amount
        from dbo.basket_other o with(nolock)
            left join dbo.basket b
        with (nolock)
        on
            b.basket_id = o.basket_id
        where
            isnull(b.isdel
            , 0)= 0
          and o.isDone = 0
        group by
            o.ppriceid,
            o.areaid
    </sql>

    <sql id="getRepairAccessaoriesBasketKc">
        select o.ppriceid        as ppriceid,
               o.areaid          as areaId,
               sum(o.orderCount) as amount
        from dbo.product_kc o with(nolock)
        group by
            o.ppriceid,
            o.areaid
    </sql>

    <sql id="getAccessoriesKc">
        select k.ppriceid,
               k.areaid,
               k.leftCount as amount
        from dbo.product_kc k with(nolock)
        where k.leftCount >= 0
    </sql>

    <sql id="getKc">
        select temp.ppriceid,SUM( temp.amount) as kcAmount from(
        <include refid="getMobileKc"></include>
        <if test="req.ppidType != null and req.ppidType != 1">
            UNION ALL
            <include refid="getAccessoriesKc"></include>
        </if>
        ) temp
        Where exists(SELECT 1 FROM dbo.F_SPLIT(#{req.areaIdsCharSeq},',') f WHERE f.split_value = CAST(temp.areaid AS
        VARCHAR(20)))
        GROUP BY ppriceid
    </sql>
    <sql id="getAccessoriesSale">
        select b.ppriceid AS ppriceid, s.areaid as areaId, isnull(sum(b.basket_count), 0) as amount
        from basket b with (nolock)
        left join sub s with (nolock)
        on b.sub_id = s.sub_id
        left join productinfo p WITH (NOLOCK)
        on p.ppriceid = b.ppriceid
        <where>
            and p.ismobile1=0
            AND s.sub_check = 3
            AND isnull(b.isdel, 0) = 0
            <if test="req.startTime != null and req.endTime != null  ">
                AND s.tradeDate1 between #{req.startTime} and #{req.endTime}
            </if>
        </where>
        group by s.areaid, b.ppriceid
    </sql>
    <sql id="getMobileSale">
        select b.ppriceid AS ppriceid, s.areaid as areaId, isnull(COUNT(DISTINCT k.id), 0) as amount
        from basket b with (nolock)
        left join sub s with (nolock)
        on b.sub_id = s.sub_id
        left join product_mkc k with (nolock)  on k.basket_id = b.basket_id
        left join productinfo p WITH (NOLOCK) on p.ppriceid = b.ppriceid
        <where>
            and p.ismobile1=1
            AND s.sub_check = 3
            AND isnull(b.isdel, 0) = 0
            <if test="req.channelIdList != null and req.channelIdList.size > 0">
                and k.insourceid2 in
                <foreach collection="req.channelIdList" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.startTime != null and req.endTime != null  ">
                AND s.tradeDate1 between #{req.startTime} and #{req.endTime}
            </if>
            <if test="req.flaw == 0">
                and not exists( select 1 from xc_mkc x with(nolock) where x.mkc_id = k.id )
            </if>
            <if test="req.flaw == 1">
                and exists (select 1 from xc_mkc x with(nolock) where x.mkc_id = k.id )
            </if>
            <if test="req.mould == 0">
                and isnull(k.mouldFlag,0) = 0
            </if>
            <if test="req.mould == 1">
                and isnull(k.mouldFlag,0) = 1
            </if>
        </where>
        group by s.areaid, b.ppriceid
    </sql>
    <sql id="getSale">
        select temp.ppriceid,SUM( temp.amount) as saleAmount from(
        <include refid="getMobileSale"></include>
        UNION ALL
        <include refid="getAccessoriesSale"></include>
        UNION ALL
        <include refid="getRepairAccessoriesSale"></include>
        ) temp
        Where exists(SELECT 1 FROM dbo.F_SPLIT(#{req.areaIdsCharSeq},',') f WHERE f.split_value = CAST(temp.areaid AS
        VARCHAR(20)))
        GROUP BY ppriceid
    </sql>
    <sql id="getRepairAccessoriesSale">
        SELECT
        k.ppriceid as ppriceid,
        k.areaid as areaId,
        COUNT ( 1 ) as amount
        FROM
        wxkcoutput k WITH ( NOLOCK )
        <where>
            STATS != 3
            AND price > inprice
            AND NOT EXISTS (
            SELECT
            1
            FROM
            shouhou_huishou hs WITH ( NOLOCK )
            WHERE
            hs.shouhou_id = k.wxid
            AND hs.ishuanhuo = 1)
            <if test="req.startTime != null and req.endTime != null  ">
                AND k.dtime between #{req.startTime} and #{req.endTime}
            </if>
        </where>
        group by k.areaid, k.ppriceid
    </sql>
    <sql id="getMobileDiaoboOnWay">
        select
        k.ppriceid as ppriceid,
        a.toareaid as areaId,
        COUNT(a.areaid) as amount
        FROM mkc_toarea a with(nolock)
        LEFT JOIN product_mkc k with (nolock)
        ON k.id = a.mkc_id
        <where>a.stats in (0,1,2)
            and k.basket_id is NULL
            and k.ppriceid is not NULL
            <if test="req.channelIdList != null and req.channelIdList.size > 0">
                and k.insourceid2 in
                <foreach collection="req.channelIdList" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.flaw == 0">
                and not exists( select 1 from xc_mkc x with(nolock) where x.mkc_id = k.id )
            </if>
            <if test="req.flaw == 1">
                and exists (select 1 from xc_mkc x with(nolock) where x.mkc_id = k.id )
            </if>
            <if test="req.mould == 0">
                and isnull(k.mouldFlag,0) = 0
            </if>
            <if test="req.mould == 1">
                and isnull(k.mouldFlag,0) = 1
            </if>
        </where>
        GROUP BY a.toareaid, k.ppriceid
    </sql>
    <sql id="getMobileBasketOnWayOnWay">
        select
        k.ppriceid as ppriceid,
        a.toareaid as areaId,
        COUNT(a.areaid) as amount
        FROM mkc_toarea a with(nolock)
        LEFT JOIN product_mkc k with (nolock)
        ON k.id = a.mkc_id
        <where>k.kc_check in (10)
            and k.basket_id is not NULL
            and k.ppriceid is not NULL
            <if test="req.channelIdList != null and req.channelIdList.size > 0">
                and k.insourceid2 in
                <foreach collection="req.channelIdList" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.flaw == 0">
                and not exists( select 1 from xc_mkc x with(nolock) where x.mkc_id = k.id )
            </if>
            <if test="req.flaw == 1">
                and exists (select 1 from xc_mkc x with(nolock) where x.mkc_id = k.id )
            </if>
            <if test="req.mould == 0">
                and isnull(k.mouldFlag,0) = 0
            </if>
            <if test="req.mould == 1">
                and isnull(k.mouldFlag,0) = 1
            </if>
        </where>
        GROUP BY a.toareaid, k.ppriceid
    </sql>
    <sql id="getMobilePurchaseOnWay">
        select
        k.ppriceid as ppriceid,
        k.areaid as areaId,
        COUNT(k.id) as amount
        FROM product_mkc k with(nolock)
        LEFT JOIN mkc_toarea a with (nolock)
        ON k.id = a.mkc_id
        <where>a.mkc_id is null
            and k.kc_check = 10
            and k.ppriceid is not NULL
            <if test="req.channelIdList != null and req.channelIdList.size > 0">
                and k.insourceid2 in
                <foreach collection="req.channelIdList" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.flaw == 0">
                and not exists( select 1 from xc_mkc x with(nolock) where x.mkc_id = k.id )
            </if>
            <if test="req.flaw == 1">
                and exists (select 1 from xc_mkc x with(nolock) where x.mkc_id = k.id )
            </if>
            <if test="req.mould == 0">
                and isnull(k.mouldFlag,0) = 0
            </if>
            <if test="req.mould == 1">
                and isnull(k.mouldFlag,0) = 1
            </if>
        </where>
        GROUP BY k.areaid, k.ppriceid
    </sql>
    <sql id="getAccessaoriesPurchaseOnWay">
        select b.ppriceid       as ppriceid,
               s.areaid         as areaId,
               SUM(b.leftCount) as amount
        FROM dbo.caigou_sub s with(nolock)
        LEFT JOIN dbo.caigou_basket b
        with (nolock)
        ON b.sub_id = s.id
        WHERE
            s.kinds = 'pj'
          and s.stats = 2
          and s.id is not null
          and s.areaid is not null
          and b.ppriceid is not null
          and b.lcount is not null
        group by b.ppriceid, s.areaid
    </sql>
    <sql id="getRepairAccessaoriesPurchaseOnWay">
        select b.ppriceid       as ppriceid,
               s.areaid         as areaId,
               SUM(b.leftCount) as amount
        FROM dbo.caigou_sub s with(nolock)
        LEFT JOIN dbo.caigou_basket b
        with (nolock)
        ON b.sub_id = s.id
        WHERE
            s.kinds = 'wx'
          and s.stats = 2
          and s.id is not null
          and s.areaid is not null
          and b.ppriceid is not null
          and b.lcount is not null
        group by b.ppriceid, s.areaid
    </sql>
    <sql id="getAccessoriesUnsatisfiedAmount">
        select b.ppriceid                                as ppriceid,
               s.toareaid                                as areaId,
               SUM(b.lcount - isnull(b.InStockCount, 0)) as amount
        FROM dbo.diaobo_basket b WITH (NOLOCK)
            LEFT JOIN dbo.diaobo_sub s
        WITH (NOLOCK)
        ON s.id = b.sub_id
        where
            s.stats in (1)
        group by
            b.ppriceid,
            s.toareaid
    </sql>
    <sql id="getAccessoriesDiaoboOnWay">
        select b.ppriceid                                as ppriceid,
               s.toareaid                                as areaId,
               SUM(b.lcount - isnull(b.InStockCount, 0)) as amount
        FROM dbo.diaobo_basket b WITH (NOLOCK)
        LEFT JOIN dbo.diaobo_sub s
        WITH (NOLOCK)
        ON s.id = b.sub_id
        where
            s.kinds = 'pj'
          and s.stats in (2
            , 3
            , 5)
          and b.basket_id is NULL
        group by
            b.ppriceid,
            s.toareaid
    </sql>
    <sql id="getRepairAccessoriesDiaoboOnWay">
        select b.ppriceid                                as ppriceid,
               s.toareaid                                as areaId,
               SUM(b.lcount - isnull(b.InStockCount, 0)) as amount
        FROM dbo.diaobo_basket b WITH (NOLOCK)
        LEFT JOIN dbo.diaobo_sub s
        WITH (NOLOCK)
        ON s.id = b.sub_id
        where
            s.kinds = 'wx'
          and s.stats in (2
            , 3
            , 5)
          and b.basket_id is NULL
        group by
            b.ppriceid,
            s.toareaid
    </sql>
    <sql id="getAccessaoriesBasketOnWayOnWay">
        select b.ppriceid                                as ppriceid,
               s.toareaid                                as areaId,
               SUM(b.lcount - isnull(b.InStockCount, 0)) as amount
        FROM dbo.diaobo_basket b WITH (NOLOCK)
        LEFT JOIN dbo.diaobo_sub s
        WITH (NOLOCK)
        ON s.id = b.sub_id
        where
            s.stats in (2
            , 3
            , 5)
          and b.basket_id is not NULL
        group by
            b.ppriceid,
            s.toareaid
    </sql>
    <sql id="getDisplayKc">
        <choose>
            <when test="req.showInJiuJi!= null and req.showInJiuJi == true">
                select
                d.ppriceid AS ppriceid,
                d.curAreaId as areaid,
                sum(d.count_) as amount
                from
                dbo.displayProductInfo d with(nolock)
                where
                isnull(ischu,0)=1
                and d.curAreaId is not null
                group by
                d.ppriceid,
                d.curAreaId
            </when>
            <otherwise>
                select
                d.ppriceid AS ppriceid,
                d.areaid as areaid,
                sum(d.count_) as amount
                from
                dbo.displayProductInfo d with(nolock)
                where
                isnull(ischu,0)=1
                and d.areaid is not null
                group by
                d.ppriceid,
                d.areaid
            </otherwise>
        </choose>
    </sql>
    <update id="updateScanFlag">
      update dbo.SmallproBill
        set scan_flag = #{smallproBill.scanFlag},
            scan_time = #{smallproBill.scanTime},
            scanner = #{smallproBill.scanner}
        where
            id = #{smallproBill.id}

    </update>
    <!-- pageStatisticItem -->
    <select id="pageStatisticItem" resultType="com.jiuji.oa.stock.stockstatistics.dto.AreaStockItemDTO">
        SELECT top ${req.size} p.ppriceid AS ppid ,isnull(kc.kcAmount, 0) AS kc,isnull(sale.saleAmount, 0) AS sale
        FROM productinfo AS p WITH (NOLOCK)
        left join (<include refid="getKc"></include>) kc on kc.ppriceid = p.ppriceid
        left join (<include refid="getSale"></include>) sale on sale.ppriceid = p.ppriceid
        <where>
            (isnull(kc.kcAmount, 0)>0 or isnull(sale.saleAmount, 0)>0)
            <include refid="searchByProductWhere"></include>
        </where>
        <choose>
            <when test="req.orderField!= null and req.orderField == 'sale'">
                order by sale DESC
            </when>
            <otherwise>
                order by kc DESC
            </otherwise>
        </choose>
    </select>


    <!-- pageStatisticItem -->
    <select id="getMobileKc" resultType="com.jiuji.oa.stock.stockstatistics.dto.AreaStockItemDTO">
        SELECT a.id as areaId,p.ppriceid AS ppid ,isnull(SUM(kc.amount), 0) AS kc
        FROM areainfo a WITH (NOLOCK),
        productinfo AS p WITH (NOLOCK)
        left join (<include refid="getMobileKc"></include>) AS kc on kc.ppriceid = p.ppriceid
        <where>
            a.id = kc.areaid
            AND
            exists(SELECT 1 FROM dbo.F_SPLIT(#{req.areaIdsCharSeq},',') f WHERE f.split_value = CAST(a.id AS
            VARCHAR(20)))
            AND p.ppriceid IN
            <foreach collection="req.ppids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
        GROUP BY a.id, p.ppriceid
    </select>

    <select id="getMobileBasketKc" resultType="com.jiuji.oa.stock.stockstatistics.dto.AreaStockItemDTO">
        SELECT a.id as areaId,p.ppriceid AS ppid ,isnull(SUM(kc.amount), 0) AS basketKc
        FROM areainfo a WITH (NOLOCK),
        productinfo AS p WITH (NOLOCK)
        left join (<include refid="getMobileBasketKc"></include>) AS kc on kc.ppriceid = p.ppriceid
        <where>
            a.id = kc.areaid
            AND
            exists(SELECT 1 FROM dbo.F_SPLIT(#{req.areaIdsCharSeq},',') f WHERE f.split_value = CAST(a.id AS
            VARCHAR(20)))
            AND p.ppriceid IN
            <foreach collection="req.ppids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
        GROUP BY a.id, p.ppriceid
    </select>

    <select id="getAccessaoriesBasketKc" resultType="com.jiuji.oa.stock.stockstatistics.dto.AreaStockItemDTO">
        SELECT a.id as areaId,p.ppriceid AS ppid ,isnull(SUM(kc.amount), 0) AS basketKc
        FROM areainfo a WITH (NOLOCK),
        productinfo AS p WITH (NOLOCK)
        left join (<include refid="getAccessaoriesBasketKc"></include>) AS kc on kc.ppriceid = p.ppriceid
        <where>
            a.id = kc.areaid
            AND
            exists(SELECT 1 FROM dbo.F_SPLIT(#{req.areaIdsCharSeq},',') f WHERE f.split_value = CAST(a.id AS
            VARCHAR(20)))
            AND p.ppriceid IN
            <foreach collection="req.ppids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
        GROUP BY a.id, p.ppriceid
    </select>


    <select id="getRepairAccessaoriesBasketKc" resultType="com.jiuji.oa.stock.stockstatistics.dto.AreaStockItemDTO">
        SELECT a.id as areaId,p.ppriceid AS ppid ,isnull(SUM(kc.amount), 0) AS basketKc
        FROM areainfo a WITH (NOLOCK),
        productinfo AS p WITH (NOLOCK)
        left join (<include refid="getRepairAccessaoriesBasketKc"></include>) AS kc on kc.ppriceid = p.ppriceid
        <where>
            a.id = kc.areaid
            AND
            exists(SELECT 1 FROM dbo.F_SPLIT(#{req.areaIdsCharSeq},',') f WHERE f.split_value = CAST(a.id AS
            VARCHAR(20)))
            AND p.ppriceid IN
            <foreach collection="req.ppids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
        GROUP BY a.id, p.ppriceid
    </select>

    <select id="getMobileDiaoboOnWay" resultType="com.jiuji.oa.stock.stockstatistics.dto.AreaStockItemDTO">
        SELECT a.id as areaId,p.ppriceid AS ppid ,isnull(SUM(sale.amount), 0) AS diaoboOnWay
        FROM areainfo a WITH (NOLOCK),
        productinfo AS p WITH (NOLOCK)
        left join (<include refid="getMobileDiaoboOnWay"></include>) AS sale on sale.ppriceid = p.ppriceid
        <where>
            a.id = sale.areaid
            AND
            exists(SELECT 1 FROM dbo.F_SPLIT(#{req.areaIdsCharSeq},',') f WHERE f.split_value = CAST(a.id AS
            VARCHAR(20)))
            AND p.ppriceid IN
            <foreach collection="req.ppids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
        GROUP BY a.id, p.ppriceid
    </select>


    <select id="getMobileBasketOnWayOnWay" resultType="com.jiuji.oa.stock.stockstatistics.dto.AreaStockItemDTO">
        SELECT a.id as areaId,p.ppriceid AS ppid ,isnull(SUM(sale.amount), 0) AS basketOnWay
        FROM areainfo a WITH (NOLOCK),
        productinfo AS p WITH (NOLOCK)
        left join (<include refid="getMobileBasketOnWayOnWay"></include>) AS sale on sale.ppriceid = p.ppriceid
        <where>
            a.id = sale.areaid
            AND
            exists(SELECT 1 FROM dbo.F_SPLIT(#{req.areaIdsCharSeq},',') f WHERE f.split_value = CAST(a.id AS
            VARCHAR(20)))
            AND p.ppriceid IN
            <foreach collection="req.ppids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
        GROUP BY a.id, p.ppriceid
    </select>

    <select id="getMobilePurchaseOnWay" resultType="com.jiuji.oa.stock.stockstatistics.dto.AreaStockItemDTO">
        SELECT a.id as areaId,p.ppriceid AS ppid ,isnull(SUM(sale.amount), 0) AS purchaseOnWay
        FROM areainfo a WITH (NOLOCK),
        productinfo AS p WITH (NOLOCK)
        left join (<include refid="getMobilePurchaseOnWay"></include>) AS sale on sale.ppriceid = p.ppriceid
        <where>
            a.id = sale.areaid
            AND
            exists(SELECT 1 FROM dbo.F_SPLIT(#{req.areaIdsCharSeq},',') f WHERE f.split_value = CAST(a.id AS
            VARCHAR(20)))
            AND p.ppriceid IN
            <foreach collection="req.ppids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
        GROUP BY a.id, p.ppriceid
    </select>

    <select id="getAccessaoriesPurchaseOnWay" resultType="com.jiuji.oa.stock.stockstatistics.dto.AreaStockItemDTO">
        SELECT a.id as areaId,p.ppriceid AS ppid ,isnull(SUM(sale.amount), 0) AS purchaseOnWay
        FROM areainfo a WITH (NOLOCK),
        productinfo AS p WITH (NOLOCK)
        left join (<include refid="getAccessaoriesPurchaseOnWay"></include>) AS sale on sale.ppriceid = p.ppriceid
        <where>
            a.id = sale.areaid
            AND
            exists(SELECT 1 FROM dbo.F_SPLIT(#{req.areaIdsCharSeq},',') f WHERE f.split_value = CAST(a.id AS
            VARCHAR(20)))
            AND p.ppriceid IN
            <foreach collection="req.ppids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
        GROUP BY a.id, p.ppriceid
    </select>

    <select id="getRepairAccessaoriesPurchaseOnWay"
            resultType="com.jiuji.oa.stock.stockstatistics.dto.AreaStockItemDTO">
        SELECT a.id as areaId,p.ppriceid AS ppid ,isnull(SUM(sale.amount), 0) AS purchaseOnWay
        FROM areainfo a WITH (NOLOCK),
        productinfo AS p WITH (NOLOCK)
        left join (<include refid="getRepairAccessaoriesPurchaseOnWay"></include>) AS sale on sale.ppriceid = p.ppriceid
        <where>
            a.id = sale.areaid
            AND
            exists(SELECT 1 FROM dbo.F_SPLIT(#{req.areaIdsCharSeq},',') f WHERE f.split_value = CAST(a.id AS
            VARCHAR(20)))
            AND p.ppriceid IN
            <foreach collection="req.ppids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
        GROUP BY a.id, p.ppriceid
    </select>

    <select id="getAccessaoriesBasketOnWayOnWay" resultType="com.jiuji.oa.stock.stockstatistics.dto.AreaStockItemDTO">
        SELECT a.id as areaId,p.ppriceid AS ppid ,isnull(SUM(sale.amount), 0) AS basketOnWay
        FROM areainfo a WITH (NOLOCK),
        productinfo AS p WITH (NOLOCK)
        left join (<include refid="getAccessaoriesBasketOnWayOnWay"></include>) AS sale on sale.ppriceid = p.ppriceid
        <where>
            a.id = sale.areaid
            AND
            exists(SELECT 1 FROM dbo.F_SPLIT(#{req.areaIdsCharSeq},',') f WHERE f.split_value = CAST(a.id AS
            VARCHAR(20)))
            AND p.ppriceid IN
            <foreach collection="req.ppids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
        GROUP BY a.id, p.ppriceid
    </select>

    <select id="getMobileSale" resultType="com.jiuji.oa.stock.stockstatistics.dto.AreaStockItemDTO">
        SELECT a.id as areaId,p.ppriceid AS ppid ,isnull(SUM(sale.amount), 0) AS sale
        FROM areainfo a WITH (NOLOCK),
        productinfo AS p WITH (NOLOCK)
        left join (<include refid="getMobileSale"></include>) AS sale on sale.ppriceid = p.ppriceid
        <where>
            a.id = sale.areaid
            <if test="req.areaIdsCharSeq != null and req.areaIdsCharSeq != ''">
            AND exists(SELECT 1 FROM dbo.F_SPLIT(#{req.areaIdsCharSeq},',') f WHERE f.split_value = CAST(a.id AS VARCHAR(20)))
            </if>
            AND p.ppriceid IN
            <foreach collection="req.ppids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
        GROUP BY a.id, p.ppriceid
    </select>

    <select id="getAccessoriesSale" resultType="com.jiuji.oa.stock.stockstatistics.dto.AreaStockItemDTO">
        SELECT a.id as areaId,p.ppriceid AS ppid ,isnull(SUM(sale.amount), 0) AS sale
        FROM areainfo a WITH (NOLOCK),
        productinfo AS p WITH (NOLOCK)
        left join (<include refid="getAccessoriesSale"></include>) AS sale on sale.ppriceid = p.ppriceid
        <where>
            a.id = sale.areaid
            <if test="req.areaIdsCharSeq != null and req.areaIdsCharSeq != ''">
            AND exists(SELECT 1 FROM dbo.F_SPLIT(#{req.areaIdsCharSeq},',') f WHERE f.split_value = CAST(a.id AS VARCHAR(20)))
            </if>
            AND p.ppriceid IN
            <foreach collection="req.ppids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
        GROUP BY a.id, p.ppriceid
    </select>

    <select id="getRepairAccessoriesSale" resultType="com.jiuji.oa.stock.stockstatistics.dto.AreaStockItemDTO">
        SELECT a.id as areaId,p.ppriceid AS ppid ,isnull(SUM(sale.amount), 0) AS sale
        FROM areainfo a WITH (NOLOCK),
        productinfo AS p WITH (NOLOCK)
        left join (<include refid="getRepairAccessoriesSale"></include>) AS sale on sale.ppriceid = p.ppriceid
        <where>
            a.id = sale.areaid
            AND
            exists(SELECT 1 FROM dbo.F_SPLIT(#{req.areaIdsCharSeq},',') f WHERE f.split_value = CAST(a.id AS
            VARCHAR(20)))
            AND p.ppriceid IN
            <foreach collection="req.ppids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
        GROUP BY a.id, p.ppriceid
    </select>

    <select id="getAccessoriesKc" resultType="com.jiuji.oa.stock.stockstatistics.dto.AreaStockItemDTO">
        SELECT a.id as areaId,p.ppriceid AS ppid ,isnull(SUM(kc.amount), 0) AS kc
        FROM areainfo a WITH (NOLOCK),
        productinfo AS p WITH (NOLOCK)
        left join (<include refid="getAccessoriesKc"></include>) AS kc on kc.ppriceid = p.ppriceid
        <where>
            a.id = kc.areaid
            AND
            exists(SELECT 1 FROM dbo.F_SPLIT(#{req.areaIdsCharSeq},',') f WHERE f.split_value = CAST(a.id AS
            VARCHAR(20)))
            AND p.ppriceid IN
            <foreach collection="req.ppids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
        GROUP BY a.id, p.ppriceid
    </select>


    <select id="getAccessoriesDisplayKc" resultType="com.jiuji.oa.stock.stockstatistics.dto.AreaStockItemDTO">
        SELECT a.id as areaId,p.ppriceid AS ppid ,isnull(SUM(kc.amount), 0) AS displayKc
        FROM areainfo a WITH (NOLOCK),
        productinfo AS p WITH (NOLOCK)
        left join (<include refid="getDisplayKc"></include>) AS kc on kc.ppriceid = p.ppriceid
        <where>
            a.id = kc.areaid
            AND
            exists(SELECT 1 FROM dbo.F_SPLIT(#{req.areaIdsCharSeq},',') f WHERE f.split_value = CAST(a.id AS
            VARCHAR(20)))
            AND p.ppriceid IN
            <foreach collection="req.ppids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
        GROUP BY a.id, p.ppriceid
    </select>
    <select id="getAccessoriesDiaoboOnWay" resultType="com.jiuji.oa.stock.stockstatistics.dto.AreaStockItemDTO">
        SELECT a.id as areaId,p.ppriceid AS ppid ,isnull(SUM(kc.amount), 0) AS diaoboOnWay
        FROM areainfo a WITH (NOLOCK),
        productinfo AS p WITH (NOLOCK)
        left join (<include refid="getAccessoriesDiaoboOnWay"></include>) AS kc on kc.ppriceid = p.ppriceid
        <where>
            a.id = kc.areaid
            AND
            exists(SELECT 1 FROM dbo.F_SPLIT(#{req.areaIdsCharSeq},',') f WHERE f.split_value = CAST(a.id AS
            VARCHAR(20)))
            AND p.ppriceid IN
            <foreach collection="req.ppids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
        GROUP BY a.id, p.ppriceid
    </select>
    <select id="getRepairAccessoriesDiaoboOnWay" resultType="com.jiuji.oa.stock.stockstatistics.dto.AreaStockItemDTO">
        SELECT a.id as areaId,p.ppriceid AS ppid ,isnull(SUM(kc.amount), 0) AS diaoboOnWay
        FROM areainfo a WITH (NOLOCK),
        productinfo AS p WITH (NOLOCK)
        left join (<include refid="getRepairAccessoriesDiaoboOnWay"></include>) AS kc on kc.ppriceid = p.ppriceid
        <where>
            a.id = kc.areaid
            AND
            exists(SELECT 1 FROM dbo.F_SPLIT(#{req.areaIdsCharSeq},',') f WHERE f.split_value = CAST(a.id AS
            VARCHAR(20)))
            AND p.ppriceid IN
            <foreach collection="req.ppids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
        GROUP BY a.id, p.ppriceid
    </select>


    <select id="getAccessoriesUnsatisfiedAmount" resultType="com.jiuji.oa.stock.stockstatistics.dto.AreaStockItemDTO">
        SELECT a.id as areaId,p.ppriceid AS ppid ,isnull(SUM(kc.amount), 0) AS unsatisfiedAmount
        FROM areainfo a WITH (NOLOCK),
        productinfo AS p WITH (NOLOCK)
        left join (<include refid="getAccessoriesUnsatisfiedAmount"></include>) AS kc on kc.ppriceid = p.ppriceid
        <where>
            a.id = kc.areaid
            AND
            exists(SELECT 1 FROM dbo.F_SPLIT(#{req.areaIdsCharSeq},',') f WHERE f.split_value = CAST(a.id AS
            VARCHAR(20)))
            AND p.ppriceid IN
            <foreach collection="req.ppids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
        GROUP BY a.id, p.ppriceid
    </select>

    <select id="selectCidByPpid" resultType="com.jiuji.oa.stock.stockstatistics.dto.PpidInfoDTO">
        SELECT
        a.ppriceid as ppid,
        trim(a.product_name ) as productName,
        trim(isnull(a.product_color, '')) as productColor,
        a.cid as cid,
        a.ismobile1 as ismobile
        from
        productinfo a with (nolock)
        <where>a.ppriceid in
            <foreach collection="ppidList" item="ppid" separator="," open="(" close=")">
                #{ppid}
            </foreach>
        </where>
    </select>

    <select id="selectRepairCategoryChildren" resultType="java.lang.String">
        SELECT ID
        from f_category_children('23')
    </select>

    <select id="getInventoryList" resultType="com.jiuji.oa.nc.stock.entity.ProductMkc">
        select p.*
        from product_mkc p with(nolock)
        left join areainfo a
        with (nolock)
        on a.id = p.areaid
        <where>
            p.kc_check in (3, 10)
            and isnull(p.mouldFlag,0) = 0
            and a.kind1 = 1
            and not exists ( select 1 from xc_mkc x with (nolock) where x.mkc_id = p.id )
            <if test="ppidList != null and ppidList.size > 0 ">
                and p.ppriceid in
                <foreach collection="ppidList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="areaIdList != null and areaIdList.size > 0 ">
                and p.areaid in
                <foreach collection="areaIdList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectSmallProductList" resultType="com.jiuji.oa.stock.stockstatistics.vo.req.SmallProductInfo">
        select b.ppriceid,b.count,b.basket_id,p.product_name,product_color, b.scan_flag, b.scan_time, b.scanner,isnull(s.toareaid,s.areaid) as areaId,b.id as smallproBillId
        from dbo.Smallpro s with (nolock)
         left join dbo.SmallproBill  b with (nolock)  on b.smallproID = s.id
            left join dbo.productinfo p with(nolock ) on   iif(isnull(s.changePpriceid,0)=0, b.ppriceid,s.changePpriceid ) = p.ppriceid
            where s.Kind =2 and s.id = #{smallProId}
    </select>


</mapper>
