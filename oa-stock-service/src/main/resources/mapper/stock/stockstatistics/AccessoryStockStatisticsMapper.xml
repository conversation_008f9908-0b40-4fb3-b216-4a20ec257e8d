<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.stockstatistics.mapper.AccessoryStockStatisticsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="ProductStatisticsMap"
               type="com.jiuji.oa.stock.stockstatistics.vo.res.AccessoryProductStatisticsResVO">
        <result column="ppriceid" property="ppriceid"/>
        <result column="amount" property="amount"/>
        <result column="staticPrice" property="staticPrice"/>
        <result column="inprice" property="inprice"/>
        <result column="product_name" property="productName"/>
        <result column="product_color" property="productColor"/>
        <result column="areaId" property="areaId"/>
        <result column="productid" property="productid"/>
        <result column="cid" property="cid"/>
        <result column="bpic" property="bpic"/>
        <result column="brandId" property="brandId"/>
        <result column="plabel" property="plabel"/>
    </resultMap>

    <!-- 统计射结果映射 -->
    <resultMap id="StockStatisticsResultMap" type="com.jiuji.oa.stock.stockstatistics.dto.StockStatisticsDTO">
        <result column="amount" property="totalAmount"/>
        <result column="totalCost" property="totalCost"/>
        <result column="avgCost" property="avgCost"/>
    </resultMap>


    <!-- 商品搜索条件 -->
    <sql id="searchByProductWhere">
        <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==1">
            and p.product_name like '%' + #{req.searchValue} + '%'
        </if>
        <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==2">
            and p.productid = #{req.searchValue}
        </if>
        <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==3">
            and p.barcode = #{req.searchValue}
        </if>
        <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==4">
            and p.ppriceid = #{req.searchValue}
        </if>
        <if test="req.ppid != null and req.ppid != '' ">
            and k.ppriceid = #{req.ppid}
        </if>
        <if test="req.categoryCharSeq != null and req.categoryCharSeq != '' ">
            and p.cid in (select id from f_category_children (#{req.categoryCharSeq}))
        </if>
        <if test="req.kinds != null and req.kinds == 1">
            and p.cid in (select id from f_category_children('23'))
        </if>
        <if test="req.kinds != null and req.kinds == 2">
            and p.cid not in (select id from f_category_children('23'))
        </if>
        <if test="req.brandIds != null and req.brandIds.size > 0 ">
            and p.brandID in
            <foreach collection="req.brandIds" item="it" separator="," open="(" close=")">
                #{it}
            </foreach>
        </if>
        <if test="req.labels != null and req.labels.size > 0 ">
            and isnull(pxi.product_label,0) in
            <foreach collection="req.labels" item="it" separator="," open="(" close=")">
                #{it}
            </foreach>
        </if>
        <if test="req.markIds != null and req.markIds.size > 0 ">
            and p.markId in
            <foreach collection="req.markIds" item="it" separator="," open="(" close=")">
                #{it}
            </foreach>
        </if>
    </sql>

    <select id="getProductStatistics" resultMap="ProductStatisticsMap">
        select p.product_name,
        p.product_color,
        p.barCode,
        p.bpic,
        p.productid,
        p.cid,
        p.brandId,
        isnull(pxi.product_label,0) as plabel,
        k.ppriceid,
        k.areaid,
        k.inprice,
        k.staticPrice,
        k.amount,
        p.markId as markId,
        isnull(p.markName,'-') as markName
        from productinfo p WITH(NOLOCK)
        left join product_xtenant_info pxi with(nolock) on
        pxi.ppriceid = p.ppriceid and xTenant = #{req.xTenant},
        (<include refid="getAccessoryKc"></include>) k
        <where>k.ppriceid = p.ppriceid
            <include refid="searchByProductWhere"></include>
        </where>
        order by k.amount DESC
    </select>

    <sql id="getAccessoryKc">
        select
        t.ppriceid as ppriceid,
        t.areaid as areaid,
        avg(isnull(inprice, 0)) as inprice,
        sum(isnull(lcount, 0)) as amount,
        avg(isnull(inprice, 0)) * sum(isnull(lcount, 0)) as staticPrice
        from (
        select
        k.ppriceid,
        k.areaid,
        k.leftCount as lcount,
        k.inprice
        from
        dbo.product_kc k with(nolock)
        where k.ppriceid = -1
        <if test="req.kcFlag != null and req.kcFlag == true">
            UNION
            select
            k.ppriceid,
            k.areaid,
            k.leftCount as lcount,
            k.inprice
            from
            dbo.product_kc k with(nolock)
            where
            k.leftCount > 0
            <if test="req.areaIdsCharSeq != null and req.areaIdsCharSeq != '' ">
                AND exists(SELECT 1 FROM dbo.F_SPLIT(#{req.areaIdsCharSeq},',') f WHERE f.split_value = CAST(k.areaid AS VARCHAR(20)))
            </if>
        </if>
        <if test="req.displayFlag != null and req.displayFlag == true">
            UNION
            <choose>
                <when test="req.showInJiuJi!= null and req.showInJiuJi == true">
                    select
                    d.ppriceid,
                    d.curAreaId as areaid,
                    sum(d.count_) as lcount,
                    AVG(k.inprice) as inprice
                    from
                    dbo.displayProductInfo d with(nolock)
                    left join dbo.product_kc k with(nolock) on
                    k.ppriceid = d.ppriceid and d.curAreaId = k.areaid
                    where isnull(ischu,0)=1
                    <if test="req.areaIdsCharSeq != null and req.areaIdsCharSeq != '' ">
                        AND exists(SELECT 1 FROM dbo.F_SPLIT(#{req.areaIdsCharSeq},',') f WHERE f.split_value = CAST(d.curAreaId AS VARCHAR(20)))
                    </if>
                    group by
                    d.ppriceid,
                    d.curAreaId
                </when>
                <otherwise>
                    select
                    d.ppriceid,
                    d.areaid as areaid,
                    sum(d.count_) as lcount,
                    AVG(k.inprice) as inprice
                    from
                    dbo.displayProductInfo d with(nolock)
                    left join dbo.product_kc k with(nolock) on
                    k.ppriceid = d.ppriceid and d.areaid = k.areaid
                    where isnull(ischu,0)=1
                    <if test="req.areaIdsCharSeq != null and req.areaIdsCharSeq != '' ">
                        AND exists(SELECT 1 FROM dbo.F_SPLIT(#{req.areaIdsCharSeq},',') f WHERE f.split_value = CAST(d.areaid AS VARCHAR(20)))
                    </if>
                    group by
                    d.ppriceid,
                    d.areaid
                </otherwise>
            </choose>
        </if>
        <if test="req.basketFlag != null and req.basketFlag == true">
            UNION
            select
            o.ppriceid,
            o.areaid,
            sum(o.lcount) as lcount,
            AVG(b.inprice) as inprice
            from
            dbo.basket_other o with(nolock)
            left join dbo.basket b with(nolock) on
            b.basket_id = o.basket_id
            where
            isnull(b.isdel,
            0)= 0
            and o.isDone = 0
            <if test="req.areaIdsCharSeq != null and req.areaIdsCharSeq != '' ">
                AND exists(SELECT 1 FROM dbo.F_SPLIT(#{req.areaIdsCharSeq},',') f WHERE f.split_value = CAST(o.areaid AS VARCHAR(20)))
            </if>
            group by
            o.ppriceid,
            o.areaid
        </if>
        <if test="req.diaoboFlag != null and req.diaoboFlag == true">
            UNION
            select
            b.ppriceid,
            s.toareaid,
            SUM(b.lcount-isnull(b.InStockCount,0)) as lcount,
            AVG(b.inprice) as inprice
            FROM dbo.diaobo_basket b WITH (NOLOCK)
            LEFT JOIN dbo.diaobo_sub s WITH (NOLOCK) ON s.id = b.sub_id
            where s.stats in (2,3,5)
            <if test="req.areaIdsCharSeq != null and req.areaIdsCharSeq != '' ">
                AND exists(SELECT 1 FROM dbo.F_SPLIT(#{req.areaIdsCharSeq},',') f WHERE f.split_value = CAST(s.toareaid AS VARCHAR(20)))
            </if>
            <if test="req.orderType != null and req.orderType == 1 ">
                and b.basket_id is NULL
            </if>
            <if test="req.orderType != null and req.orderType == 2 ">
                and b.basket_id is not NULL
            </if>
            group by
            b.ppriceid,
            s.toareaid
        </if>
        <if test="req.giftFlag != null and req.giftFlag == true">
            UNION
            select k.ppriceid,
            k.areaid,
            k.leftCount as lcount,
            k.inprice FROM product_kc k WITH (NOLOCK)
            where
            k.leftCount > 0
            <if test="req.areaIdsCharSeq != null and req.areaIdsCharSeq != '' ">
                AND exists(SELECT 1 FROM dbo.F_SPLIT(#{req.areaIdsCharSeq},',') f WHERE f.split_value = CAST(k.areaid AS VARCHAR(20)))
            </if>
            and exists(select 1 from dbo.productGift_kc o with(nolock) where k.ppriceid=o.ppriceid and k.areaid=o.areaid
            and o.lcount> 0 )
        </if>
        ) t where t.ppriceid is not NULL and ppriceid !=0
        group by t.ppriceid,t.areaid HAVING sum(isnull(lcount, 0))>0
    </sql>


    <!-- listStatisticItem -->
    <select id="listStatisticItem" resultType="com.jiuji.oa.stock.stockstatistics.dto.StatisticItemDTO">
        <if test="req.type != null and req.type != '' and req.type == 'category'">
            SELECT p.cid as id
            FROM productinfo AS p WITH (NOLOCK)
            left join product_xtenant_info pxi with(nolock) on
            pxi.ppriceid = p.ppriceid and xTenant = #{req.xTenant},
            (<include refid="getAccessoryKc"></include>) k
            <where>p.ppriceid = k.ppriceid
                <include refid="searchByProductWhere"></include>
            </where>
            GROUP BY p.cid
            ORDER BY SUM(k.amount) DESC
        </if>
        <if test="req.type != null and req.type != '' and req.type == 'brand'">
            SELECT p.brandid as id
            FROM productinfo AS p WITH (NOLOCK)
            left join product_xtenant_info pxi with(nolock) on
            pxi.ppriceid = p.ppriceid and xTenant = #{req.xTenant},
            (<include refid="getAccessoryKc"></include>) k
            <where>p.ppriceid = k.ppriceid
                <include refid="searchByProductWhere"></include>
            </where>
            GROUP BY p.brandid
            ORDER BY SUM(k.amount) DESC
        </if>
        <if test="req.type != null and req.type != '' and req.type == 'area'">
            SELECT a.id, a.area AS name
            FROM areainfo a WITH (NOLOCK),
            productinfo p WITH (NOLOCK)
            left join product_xtenant_info pxi with(nolock) on
            pxi.ppriceid = p.ppriceid and xTenant = #{req.xTenant},
            (<include refid="getAccessoryKc"></include>) k
            <where>k.ppriceid = p.ppriceid and a.id = k.areaid
                <include refid="searchByProductWhere"></include>
            </where>
            GROUP BY a.id, a.area
            ORDER BY SUM(k.amount) DESC
        </if>
    </select>

    <!-- getStockStatistics -->
    <select id="getStockStatistics" resultMap="StockStatisticsResultMap">
        SELECT SUM(k.amount) AS amount
        <if test="req.showInPrice">
            , ISNULL(SUM(k.staticPrice), 0.00) AS totalCost,
            Convert(decimal(18,2), ISNULL(SUM(k.staticPrice) / nullif(SUM(k.amount), 0), 0.00)) AS avgCost
        </if>
        FROM
        productinfo AS p WITH (NOLOCK)
        left join product_xtenant_info pxi with(nolock) on
        pxi.ppriceid = p.ppriceid and xTenant = #{req.xTenant},
        (<include refid="getAccessoryKc"></include>) k
        <where>
            p.ppriceid = k.ppriceid
            <include refid="searchByProductWhere"></include>
            <if test="req.type == 'category' and req.typeId!=null and req.typeId!=0">
                and p.cid=#{req.typeId}
            </if>
            <if test="req.type == 'brand' and req.typeId!=null and req.typeId!=0">
                and p.brandID=#{req.typeId}
            </if>
            <if test="req.type == 'area' and req.typeId!=null and req.typeId!=0">
                and k.areaid=#{req.typeId}
            </if>
        </where>
    </select>

    <!-- pageStatisticItem -->
    <select id="pageStatisticItem" resultType="com.jiuji.oa.stock.stockstatistics.dto.StatisticItemDTO">
        <if test="req.type != null and req.type != '' and req.type == 'category'">
            SELECT ca.id, ca.name, SUM(k.amount) AS amount
            <if test="req.showInPrice">
                , ISNULL(SUM(k.staticPrice), 0.00) AS totalCost,
                Convert(decimal(18,2), ISNULL(SUM(k.staticPrice) / nullif(SUM(k.amount), 0), 0.00)) AS avgCost
            </if>
            FROM category AS ca WITH (NOLOCK),
            productinfo AS p WITH (NOLOCK)
            left join product_xtenant_info pxi with(nolock) on
            pxi.ppriceid = p.ppriceid and xTenant = #{req.xTenant},
            (<include refid="getAccessoryKc"></include>) k
            <where>p.ppriceid = k.ppriceid AND p.cid = ca.id
                AND ca.id IN
                <foreach collection="itemIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                <include refid="searchByProductWhere"></include>
            </where>
            GROUP BY ca.id, ca.name
        </if>
        <if test="req.type != null and req.type != '' and req.type == 'brand'">
            SELECT b.id, b.name, SUM(k.amount) AS amount
            <if test="req.showInPrice">
                , ISNULL(SUM(k.staticPrice), 0.00) AS totalCost,
                Convert(decimal(18,2), ISNULL(SUM(k.staticPrice) / nullif(SUM(k.amount), 0), 0.00)) AS avgCost
            </if>
            FROM brand AS b WITH (NOLOCK),
            productinfo AS p WITH (NOLOCK)
            left join product_xtenant_info pxi with(nolock) on
            pxi.ppriceid = p.ppriceid and xTenant = #{req.xTenant},
            (<include refid="getAccessoryKc"></include>) k
            <where>p.ppriceid = k.ppriceid AND p.brandID = b.id
                AND b.id IN
                <foreach collection="itemIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                <include refid="searchByProductWhere"></include>
            </where>
            GROUP BY b.id, b.name
        </if>
        <if test="req.type != null and req.type != '' and req.type == 'area'">
            SELECT a.id, a.area AS name, SUM (k.amount) AS amount
            <if test="req.showInPrice">
                , ISNULL(SUM(k.staticPrice), 0.00) AS totalCost,
                Convert(decimal(18,2), ISNULL(SUM(k.staticPrice) / nullif(SUM(k.amount), 0), 0.00)) AS avgCost
            </if>
            FROM areainfo a WITH (NOLOCK),
            productinfo AS p WITH (NOLOCK)
            left join product_xtenant_info pxi with(nolock) on
            pxi.ppriceid = p.ppriceid and xTenant = #{req.xTenant},
            (<include refid="getAccessoryKc"></include>) k
            <where>k.ppriceid = p.ppriceid and a.id = k.areaid AND a.id IN
                <foreach collection="itemIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                <include refid="searchByProductWhere"></include>
            </where>
            GROUP BY a.id, a.area
        </if>
    </select>

    <!-- pageStatisticItem -->
    <select id="pageStatisticItemV2" resultType="com.jiuji.oa.stock.stockstatistics.dto.StatisticItemDTO">
        <if test="req.type != null and req.type != '' and req.type == 'category'">
            SELECT ca.id, ca.name, SUM(k.amount) AS amount
            <if test="req.showInPrice">
                , ISNULL(SUM(k.staticPrice), 0.00) AS totalCost,
                Convert(decimal(18,2), ISNULL(SUM(k.staticPrice) / nullif(SUM(k.amount), 0), 0.00)) AS avgCost
            </if>
            FROM category AS ca WITH (NOLOCK),
            productinfo AS p WITH (NOLOCK)
            left join product_xtenant_info pxi with(nolock) on
            pxi.ppriceid = p.ppriceid and xTenant = #{req.xTenant},
            (<include refid="getAccessoryKc"></include>) k
            <where>p.ppriceid = k.ppriceid AND p.cid = ca.id
                <include refid="searchByProductWhere"></include>
            </where>
            GROUP BY ca.id, ca.name
            Order BY amount DESC
        </if>
        <if test="req.type != null and req.type != '' and req.type == 'brand'">
            SELECT b.id, b.name, SUM(k.amount) AS amount
            <if test="req.showInPrice">
                , ISNULL(SUM(k.staticPrice), 0.00) AS totalCost,
                Convert(decimal(18,2), ISNULL(SUM(k.staticPrice) / nullif(SUM(k.amount), 0), 0.00)) AS avgCost
            </if>
            FROM brand AS b WITH (NOLOCK),
            productinfo AS p WITH (NOLOCK)
            left join product_xtenant_info pxi with(nolock) on
            pxi.ppriceid = p.ppriceid and xTenant = #{req.xTenant},
            (<include refid="getAccessoryKc"></include>) k
            <where>p.ppriceid = k.ppriceid AND p.brandID = b.id
                <include refid="searchByProductWhere"></include>
            </where>
            GROUP BY b.id, b.name
            Order BY amount DESC
        </if>
        <if test="req.type != null and req.type != '' and req.type == 'area'">
            SELECT a.id, a.area AS name, SUM (k.amount) AS amount
            <if test="req.showInPrice">
                , ISNULL(SUM(k.staticPrice), 0.00) AS totalCost,
                Convert(decimal(18,2), ISNULL(SUM(k.staticPrice) / nullif(SUM(k.amount), 0), 0.00)) AS avgCost
            </if>
            FROM areainfo a WITH (NOLOCK),
            productinfo AS p WITH (NOLOCK)
            left join product_xtenant_info pxi with(nolock) on
            pxi.ppriceid = p.ppriceid and xTenant = #{req.xTenant},
            (<include refid="getAccessoryKc"></include>) k
            <where>k.ppriceid = p.ppriceid and a.id = k.areaid
                <include refid="searchByProductWhere"></include>
            </where>
            GROUP BY a.id, a.area
            Order BY amount DESC
        </if>
    </select>

    <!-- listProductSkuByType -->
    <select id="listProductSkuByType" resultType="com.jiuji.oa.stock.stockstatistics.dto.ProductSkuDTO">
        SELECT p.ppriceid AS ppid, p.product_name AS name, p.product_color AS productColor, pm.amount
        <if test="req.showInPrice">
            , pm.totalCost
            , Convert(decimal(18,2), pm.totalCost / pm.amount) AS avgCost
        </if>
        , ca.name AS categoryName
        FROM productinfo AS p WITH (NOLOCK),
        category AS ca WITH (NOLOCK),
        (SELECT k.ppriceid, SUM(k.amount) AS amount
        <if test="req.showInPrice">
        ,ISNULL(SUM(k.staticPrice), 0.00) AS totalCost,
        Convert(decimal(18,2), ISNULL(SUM(k.staticPrice) / nullif(SUM(k.amount), 0), 0.00)) AS avgCost
        </if>
        FROM productinfo AS p WITH (NOLOCK)
        left join product_xtenant_info pxi with(nolock) on
        pxi.ppriceid = p.ppriceid and xTenant = #{req.xTenant},
        (<include refid="getAccessoryKc"></include>) k
        WHERE
        p.ppriceid = k.ppriceid
        <include refid="searchByProductWhere"></include>
        <if test="req.type != null and req.type != '' and req.type == 'category'">
            and p.cid=#{req.typeId}
        </if>
        <if test="req.type != null and req.type != '' and req.type == 'brand'">
            AND p.brandID = #{req.typeId}
        </if>
        <if test="req.type != null and req.type != '' and req.type == 'area'">
            AND k.areaid = #{req.typeId}
        </if>
        <if test="req.productId!=null and req.productId!=0">
            and p.productid=#{req.productId}
        </if>
        GROUP BY k.ppriceid) AS pm
        <where>p.cid = ca.ID
            AND p.ppriceid = pm.ppriceid
        </where>

    </select>

    <select id="getProductStatisticsTotal"
            resultType="com.jiuji.oa.stock.stockstatistics.vo.res.AccessoryProductStatisticsTotalRes">
        SELECT SUM(k.amount) AS totalAmount
        <if test="req.showInPrice">
            , ISNULL(SUM(k.staticPrice), 0.00) AS totalCost,
            Convert(decimal(18,2), ISNULL(SUM(k.staticPrice) / nullif(SUM(k.amount), 0), 0.00)) AS avgCost
        </if>
        FROM
        (<include refid="getAccessoryKc"></include>) k
        left join areainfo a WITH (NOLOCK) on k.areaid = a.id
        left join productinfo p WITH (NOLOCK) on k.ppriceid = p.ppriceid
        left join product_xtenant_info pxi with(nolock) on
        pxi.ppriceid = p.ppriceid and pxi.xTenant = #{req.xTenant}
        <where>
            <if test="req.ppid != null and req.ppid != '' ">
                and k.ppriceid = #{req.ppid}
            </if>
            <include refid="searchByProductWhere"></include>
        </where>
    </select>
    <select id="listPageProductByType"
            resultType="com.jiuji.oa.stock.stockstatistics.dto.ProductDTO">
        SELECT p.id AS product_id, p.name, pm.amount
        <if test="req.showInPrice">
            , pm.totalCost
            , Convert(decimal(18,2), pm.totalCost / pm.amount) AS avgCost
        </if>
        , ca.name AS categoryName
        FROM product AS p WITH(NOLOCK),
        category AS ca WITH(NOLOCK),
        (
        <if test="req.type != null and req.type != '' and req.type == 'category'">
            SELECT p.productid, SUM (k.amount) AS amount
            <if test="req.showInPrice">
                , ISNULL(SUM(k.staticPrice), 0.00) AS totalCost,
                Convert(decimal(18,2), ISNULL(SUM(k.staticPrice) / nullif(SUM(k.amount), 0), 0.00)) AS avgCost
            </if>
            FROM category AS ca WITH (NOLOCK),
            productinfo AS p WITH (NOLOCK)
            left join product_xtenant_info pxi with(nolock) on
            pxi.ppriceid = p.ppriceid and xTenant = #{req.xTenant},
            (<include refid="getAccessoryKc"></include>) k
            <where>p.ppriceid = k.ppriceid AND p.cid = ca.id
                <include refid="searchByProductWhere"></include>
                <if test="req.type != null and req.type != '' and req.type == 'category'">
                    and p.cid=#{req.typeId}
                </if>
                <if test="req.type != null and req.type != '' and req.type == 'brand'">
                    AND p.brandID = #{req.typeId}
                </if>
                <if test="req.type != null and req.type != '' and req.type == 'area'">
                    AND k.areaid = #{req.typeId}
                </if>
            </where>
            GROUP BY p.productid
        </if>
        <if test="req.type != null and req.type != '' and req.type == 'brand'">
            SELECT p.productid, SUM (k.amount) AS amount
            <if test="req.showInPrice">
                , ISNULL(SUM(k.staticPrice), 0.00) AS totalCost,
                Convert(decimal(18,2), ISNULL(SUM(k.staticPrice) / nullif(SUM(k.amount), 0), 0.00)) AS avgCost
            </if>
            FROM brand AS b WITH (NOLOCK),
            productinfo AS p WITH (NOLOCK)
            left join product_xtenant_info pxi with(nolock) on
            pxi.ppriceid = p.ppriceid and xTenant = #{req.xTenant},
            (<include refid="getAccessoryKc"></include>) k
            <where>p.ppriceid = k.ppriceid AND p.brandID = b.id
                <include refid="searchByProductWhere"></include>
                <if test="req.type != null and req.type != '' and req.type == 'category'">
                    and p.cid=#{req.typeId}
                </if>
                <if test="req.type != null and req.type != '' and req.type == 'brand'">
                    AND p.brandID = #{req.typeId}
                </if>
                <if test="req.type != null and req.type != '' and req.type == 'area'">
                    AND k.areaid = #{req.typeId}
                </if>
            </where>
            GROUP BY p.productid
        </if>
        <if test="req.type != null and req.type != '' and req.type == 'area'">
            SELECT p.productid, SUM (k.amount) AS amount
            <if test="req.showInPrice">
                , ISNULL(SUM(k.staticPrice), 0.00) AS totalCost,
                Convert(decimal(18,2), ISNULL(SUM(k.staticPrice) / nullif(SUM(k.amount), 0), 0.00)) AS avgCost
            </if>
            FROM areainfo a WITH (NOLOCK),
            productinfo AS p WITH (NOLOCK)
            left join product_xtenant_info pxi with(nolock) on
            pxi.ppriceid = p.ppriceid and xTenant = #{req.xTenant},
            (<include refid="getAccessoryKc"></include>) k
            <where>k.ppriceid = p.ppriceid and a.id = k.areaid
                <include refid="searchByProductWhere"></include>
                <if test="req.type != null and req.type != '' and req.type == 'category'">
                    and p.cid=#{req.typeId}
                </if>
                <if test="req.type != null and req.type != '' and req.type == 'brand'">
                    AND p.brandID = #{req.typeId}
                </if>
                <if test="req.type != null and req.type != '' and req.type == 'area'">
                    AND k.areaid = #{req.typeId}
                </if>
            </where>
            GROUP BY p.productid
        </if>
        ) AS pm
        WHERE p.cid = ca.ID AND p.id = pm.productid
        ORDER BY pm.amount DESC
    </select>
    <sql id="getAccessoryKcAll">
        select t.ppriceid              as ppriceid,
               t.areaid                as areaid,
               avg(isnull(inprice, 0)) as inprice,
               sum(isnull(lcount, 0))  as amount
        from (
                 select k.ppriceid,
                        k.areaid,
                        k.leftCount as lcount,
                        k.inprice
                 from dbo.product_kc k with(nolock)
                 where k.leftCount > 0
             ) t
        where t.ppriceid is not NULL
          and ppriceid !=0
        group by t.ppriceid, t.areaid
        HAVING sum (isnull(lcount, 0))>0
    </sql>

    <select id="getStockAmountByPpid" resultType="com.jiuji.oa.stock.stockstatistics.vo.res.StockAmountResVO">
        SELECT SUM(k.amount) AS totalAmount,k.ppriceid AS ppid
        FROM
        (<include refid="getAccessoryKcAll"></include>) k
        left join areainfo a WITH (NOLOCK) on k.areaid = a.id
        left join productinfo p WITH (NOLOCK) on k.ppriceid = p.ppriceid
        where k.ppriceid = #{req.ppid}
        group by k.ppriceid
    </select>
    <select id="getStockAmountByPpidAndArea"
            resultType="com.jiuji.oa.stock.stockstatistics.vo.res.StockAreaAmountResVO">
        SELECT a.id as areaId,k.ppriceid AS ppid, SUM(k.amount) AS totalAmount
        FROM areainfo a WITH (NOLOCK),
        productinfo AS p WITH (NOLOCK),
        (<include refid="getAccessoryKcAll"></include>) k
        <where>k.ppriceid = #{req.ppid} AND k.ppriceid = p.ppriceid AND a.id = k.areaid AND a.id IN
            <foreach collection="req.areaIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
        GROUP BY a.id,k.ppriceid
        ORDER BY a.id
    </select>
    <select id="getStockAmountByPpidBatch"
            resultType="com.jiuji.oa.stock.stockstatistics.vo.res.StockAmountResVO">
        select k.ppriceid,ISNULL(sum(k.leftCount),0) as totalAmount,k.ppriceid AS ppid from dbo.product_kc k with(nolock)
        where k.ppriceid IN
        <foreach collection="accessorsPpid" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and exists(select 1 from dbo.areainfo a with(nolock) where k.areaid=a.id and a.kind1 in (1,2) and
        a.attribute=1101)
        group by k.ppriceid
    </select>

    <select id="getUnLockedStockAmountByPpidBatch"
            resultType="com.jiuji.oa.stock.stockstatistics.vo.res.StockAmountResVO">
        select ISNULL(sum(b.basket_count),0) as totalAmount,p.ppriceid1 AS ppid from dbo.basket b with(nolock) left join dbo.productinfo p on p.ppriceid = b.ppriceid
        left  join dbo.sub s with(nolock)
        on s.sub_id = b.sub_id
        where s.sub_check in (0,1) and isnull(b.isdel,0)=0 and isnull(b.ischu,0)=0 and p.ppriceid1 IN
        <foreach collection="accessorsPpid" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and exists(select 1 from dbo.areainfo a with(nolock) where s.areaid=a.id and a.kind1 in (1,2) and a.attribute=1101)
        and not exists(select 1 from dbo.basket_other o with(nolock) where b.basket_id=o.basket_id )
        and not exists(select 1 from dbo.diaobo_basket db with(nolock) left join dbo.diaobo_sub ds with(nolock) on db.sub_id=ds.id
        where b.basket_id=db.basket_id and db.basket_type = 0 and ds.stats in (2,5,3) )
        group by p.ppriceid1
    </select>
</mapper>
