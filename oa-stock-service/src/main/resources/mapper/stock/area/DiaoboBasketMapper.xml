<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.area.mapper.DiaoboBasketMapper">

    <select id="getDiaoboSubRukuDtime" resultType="com.jiuji.oa.stock.area.vo.DiaoboSubRukuDtimeVO">
        select bsk.basket_id as basketId ,sub.ruku_dtime as rukuDtime
        FROM diaobo_basket bsk with(nolock)
        INNER JOIN diaobo_sub sub with (nolock)
        ON sub.id=bsk.sub_id
        <where> bsk.basket_id is not null and sub.ruku_dtime is not null
            and bsk.basket_type = 0 and bsk.basket_id in
            <foreach collection="basketIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
        order by bsk.basket_id DESC,sub.dtime DESC
    </select>
</mapper>
