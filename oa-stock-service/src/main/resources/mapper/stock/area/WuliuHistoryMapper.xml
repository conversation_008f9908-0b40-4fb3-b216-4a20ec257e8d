<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.area.mapper.WuliuHistoryMapper">

    <select id="getPageWuliuHistoryList" resultType="com.jiuji.oa.stock.area.vo.res.WuliuHistoryRes">
        SELECT * from (
        SELECT s.area, s.smr, s.smTime, s.nu, s.scanningOrder, string_agg(w.id, ',') AS wuliuid FROM
        shipmentsHistory s WITH(nolock)
        LEFT JOIN wuliu w WITH(nolock) ON s.nu = w.nu
        where 1=1
        <include refid="sql1"></include>
        GROUP BY s.area,s.smr,s.smTime,s.nu,s.scanningOrder
        ) s
        <if test="wuliuHistoryReq.wuliuid != null and wuliuHistoryReq.wuliuid != ''">
            where wuliuid LIKE CONCAT('%',#{wuliuHistoryReq.wuliuid},'%')
        </if>
        Order BY s.smTime Desc
    </select>

    <select id="getPageWuliuHistoryCount" resultType="Integer">
        SELECT count(1) FROM (
        SELECT s.area, s.smr, s.smTime, s.nu, s.scanningOrder, string_agg(w.id, ',') AS wuliuid FROM
        shipmentsHistory s WITH(nolock)
        LEFT JOIN wuliu w WITH(nolock) ON s.nu = w.nu
        where 1=1
        <include refid="sql1"></include>
        <if test="wuliuHistoryReq.wuliuid != null and wuliuHistoryReq.wuliuid != ''">
            AND s.id LIKE CONCAT('%',#{wuliuHistoryReq.wuliuid},'%')
        </if>
        GROUP BY s.area,s.smr,s.smTime,s.nu,s.scanningOrder) as total
    </select>

    <sql id="sql1">
        <if test="wuliuHistoryReq.smr != null and wuliuHistoryReq.smr != ''">
            AND s.smr LIKE CONCAT('%',#{wuliuHistoryReq.smr},'%')
        </if>
        <if test="wuliuHistoryReq.nu != null and wuliuHistoryReq.nu != ''">
            AND s.nu LIKE CONCAT('%',#{wuliuHistoryReq.nu},'%')
        </if>
        <if test="wuliuHistoryReq.smTime != null and wuliuHistoryReq.smTime2 != null">
            AND s.smTime BETWEEN #{wuliuHistoryReq.smTime} AND #{wuliuHistoryReq.smTime2}
        </if>
        <if test="wuliuHistoryReq.areaCode_!=null and wuliuHistoryReq.areaCode_.size()>0">
            AND w.areaid in
            <foreach collection="wuliuHistoryReq.areaCode_" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </sql>
</mapper>