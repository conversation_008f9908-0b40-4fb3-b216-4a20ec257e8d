<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.area.mapper.ReceivePersonConfigMapper">

    <select id="getPageList" resultType="com.jiuji.oa.stock.area.vo.res.ReceivePersonConfigPageRes">
        select id,
               addTime,
               fromArea,
               toArea,
               wType,
               isdel,
               receiveUserid,
               receiveUserName
        from ReceivePersonConfig with(nolock)
        where
            isdel = 0
        order by
            addTime desc


    </select>
    <select id="selectThirdProductConfigNotifyUserIds" resultType="java.lang.String">
        select Ch999ids from WXSmsReceiver with(nolock) where Classify='101' and isnull(IsDel,0)=0
    </select>
</mapper>
