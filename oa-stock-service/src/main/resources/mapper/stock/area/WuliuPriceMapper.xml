<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.area.mapper.WuliuPriceMapper">

    <select id="getPageWuliuPriceResList" resultType="com.jiuji.oa.stock.area.vo.res.WuliuPriceRes">
        select wp.id,wp.istonchen,wp.unitPrice,wp.advancePrice,wp.type_ as typeStr,wp.cityid,wp.tocityid,
        CAST(isnull((SELECT name from AreaList al2 with(nolock) where code=al.parent_code),'') as varchar(20))+' '+CAST(al.name as
        varchar(20)) as cityname,
        CAST(isnull((SELECT name from AreaList al4 with(nolock) where code=al3.parent_code),'') as varchar(20))+' '+CAST(al3.name
        as varchar(20)) as tocityname
        from wuliuPriceConfigure wp with(nolock)
        left join AreaList al with(nolock) on al.code=wp.cityid
        left join AreaList al3 with(nolock) on al3.code=wp.tocityid
        where 1=1
        <if test="wuliuPriceReq.cityid != null and wuliuPriceReq.cityid != ''">
            AND wp.cityid = #{wuliuPriceReq.cityid}
        </if>
        <if test="wuliuPriceReq.tocityid != null and wuliuPriceReq.tocityid != ''">
            AND wp.tocityid = #{wuliuPriceReq.tocityid}
        </if>
        <if test="wuliuPriceReq.type_ != null and wuliuPriceReq.type_ != ''">
            AND wp.type_ = #{wuliuPriceReq.type_}
        </if>
        <if test="wuliuPriceReq.istonchen != null and wuliuPriceReq.istonchen != ''">
            AND wp.istonchen = #{wuliuPriceReq.istonchen}
        </if>
        order by
        wp.id desc
    </select>
    <select id="priceCollectionResult" resultType="com.jiuji.oa.stock.area.vo.res.PriceCollectionResultRes">
        select
        p.ppriceid,p.product_name,p.product_color,p.pLabel plable,A.avgbeforinprice aveBeforPrice,B.avginprice avePrice,B.count,A.beforprice,B.price
        from dbo.productinfo p with(nolock) left join (
        select b.ppriceid,cast(avg(inprice) as numeric(9, 2)) as avgbeforinprice,sum(b.inprice*b.lcount) beforprice from
        dbo.caigou_basket b with(nolock) left join dbo.caigou_sub s with(nolock) on b.sub_id=s.id left join
        dbo.productinfo p with(nolock) on b.ppriceid=p.ppriceid where s.stats in ( 2, 3 ) and
        datediff(month,s.dtime,#{req.date})=1 and not exists( select 1 from f_category_children('50,3,164') f where f.id=p.cid
        )
        <include refid="whereSql0"></include>
        group by b.ppriceid
        )A on p.ppriceid=A.ppriceid
        left join (
        select b.ppriceid,cast(avg(inprice) as numeric(9, 2)) as avginprice,sum(b.lcount) count,sum(b.inprice*b.lcount)
        price from dbo.caigou_basket b with(nolock) left join dbo.caigou_sub s with(nolock) on b.sub_id=s.id left join
        dbo.productinfo p with(nolock) on b.ppriceid=p.ppriceid where s.stats in ( 2, 3 ) and
        datediff(month,s.dtime,#{req.date})=0 and not exists( select 1 from f_category_children('50,3,164') f where f.id=p.cid
        )
        <include refid="whereSql0"></include>
        group by b.ppriceid
        )B on p.ppriceid=B.ppriceid where p.ismobile1=0 and B.count>0
        <include refid="whereSql1"></include>

    </select>

    <sql id="whereSql0">
        <if test="req.cidVal != null and req.cidVal !='' ">
            AND exists( select 1 from f_category_children(#{req.cidVal}) f where f.id=p.cid )
        </if>
        <if test="req.brandid != null and req.brandid !='' ">
            AND p.brandid= #{req.brandid}
        </if>
        <if test="req.productName != null and req.productName !='' ">
            AND p.product_name like concat('%', #{req.productName},'%')
        </if>
        <if test="req.ppriceid != null and req.ppriceid !='' ">
            AND p.ppriceid= #{req.ppriceid}
        </if>
        <if test="req.plable != null and req.plable !='' ">
            <choose>
                <when test="req.plable == 'null' ">
                    AND p.pLabel is null
                </when>
                <otherwise>
                    AND p.pLabel = #{req.plable}
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="req.areaKind != null and req.areaKind == 1 ">
                AND exists(select 1 from dbo.areainfo a1 with(nolock) where s.areaid=a1.id and a1.kind1 = 1 )
            </when>
            <otherwise>
                AND exists(select 1 from dbo.areainfo a1 with(nolock) where s.areaid=a1.id and a1.kind1 != 1
                <if test="req.authorizeid != null and req.authorizeid !='' ">
                    and a1.authorizeid= #{req.authorizeid}
                </if>
                )
            </otherwise>
        </choose>
    </sql>
    <sql id="whereSql1">
        <if test="req.gaoji != null and req.gaoji ==1 ">
            <choose>
                <when test="req.hasoldPrice != null and req.hasoldPrice == 1 ">
                    AND A.avgbeforinprice is not null
                    <if test="req.price1 != null and req.price1 !='' ">
                        AND (isnull(A.avgbeforinprice,0)-B.avginprice) &gt;= #{req.price1}
                    </if>
                    <if test="req.price2 != null and req.price2 !='' ">
                        AND (isnull(A.avgbeforinprice,0)-B.avginprice) &lt;= #{req.price2}
                    </if>
                </when>
                <otherwise>
                    <if test="req.hasoldPrice != null and req.hasoldPrice == 0 ">
                        AND A.avgbeforinprice is null
                    </if>
                </otherwise>
            </choose>
        </if>
    </sql>
</mapper>
