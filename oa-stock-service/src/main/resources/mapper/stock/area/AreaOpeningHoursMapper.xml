<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.area.mapper.AreaOpeningHoursMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.stock.area.entity.AreaOpeningHours">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="date1" column="date1" jdbcType="VARCHAR"/>
            <result property="date2" column="date2" jdbcType="VARCHAR"/>
            <result property="area_id" column="area_id" jdbcType="INTEGER"/>
            <result property="kind" column="kind" jdbcType="INTEGER"/>
            <result property="weeks" column="weeks" jdbcType="INTEGER"/>
            <result property="area_opening_hours_rv" column="area_opening_hours_rv" jdbcType="TIMESTAMP"/>
            <result property="create_time" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="is_del" column="is_del" jdbcType="BIT"/>
            <result property="create_user" column="create_user" jdbcType="VARCHAR"/>
            <result property="del_time" column="del_time" jdbcType="TIMESTAMP"/>
            <result property="del_user" column="del_user" jdbcType="VARCHAR"/>
            <result property="ageingDate1" column="ageingDate1" jdbcType="VARCHAR"/>
            <result property="ageingDate2" column="ageingDate2" jdbcType="VARCHAR"/>
            <result property="ageing" column="ageing" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,date1,date2,
        area_id,kind,weeks,
        area_opening_hours_rv,create_time,is_del,
        create_user,del_time,del_user,
        ageingDate1,ageingDate2,ageing
    </sql>
    <select id="getAreaOpeningHoursByAreaId" resultType="com.jiuji.oa.stock.area.entity.AreaOpeningHours">
        select
        <include refid="Base_Column_List"/>
        from area_opening_hours with(nolock)
        where area_id = #{areaId} and isnull(is_del,0) = 0 and kind = 1
    </select>
</mapper>
