<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.nc.abnormal.mapper.MkcPrintLogMapper">


    <select id="getPrintCount" resultType="java.lang.Long">
        select COUNT(a.mkc_id)
        from (select log.mkc_id ,COUNT(log.mkc_id) AS printingNumber
              from mkc_print_log log with (nolock )
              left join product_mkc mkc with (nolock)on mkc.id = log.mkc_id
              where type = #{printingType}
        <if test="areaIdList!=null and areaIdList.size>0">
            and mkc.areaid in
            <foreach collection="areaIdList" item="it" separator="," open="(" close=")">
                #{it}
            </foreach>
        </if>
        and create_time BETWEEN #{printingStartTime} and #{printingEndTime}
              group by mkc_id) as a
        where printingNumber >= #{printingNumber}
    </select>

    <select id="getPrintInfo" resultType="com.jiuji.oa.nc.abnormal.vo.ShowPrintingVO">
        select t.*
        from (
                 select row_number()       over (order by mkc_id desc) as row,
                        u.mkc_id        as mkcId,
                        mkc.ppriceid    as ppriceid,
                        info.area       as area,
                        p.product_name  as productName,
                        p.product_color as productColor,
                        mkc.orderid     as orderid,
                        printingNumber  AS printingNumber
                 from (
                          select mkc_id, COUNT(mkc_id) AS printingNumber
                          from mkc_print_log with (nolock)
                          where type = #{printingType}
                            and create_time BETWEEN #{printingStartTime}
                            and #{printingEndTime}
                          group by mkc_id
                      ) as u
                     left join product_mkc mkc with (nolock)on mkc.id = u.mkc_id
                     left join areainfo info with(nolock) on info.id=mkc.areaid
                     left join productinfo p with (nolock) on p.ppriceid = mkc.ppriceid
                     where printingNumber >= #{printingNumber}
                     <if test="areaIdList!=null and areaIdList.size>0">
                     and mkc.areaid in
                         <foreach collection="areaIdList" item="it" separator="," open="(" close=")">
                             #{it}
                         </foreach>
                     </if>
            ) t

        where t.row between #{startNumber} and #{endNumber}
    </select>
    <select id="getPrintingNumberInfoVO" resultType="com.jiuji.oa.nc.abnormal.vo.PrintingNumberInfoVO">
        select  row_number() over (order by create_time asc ) as number,  log.client_no as clientNo, a.area as area, log.inuser as inuser, log.create_time as createTime
        from mkc_print_log log with (nolock)
         left join areainfo a with (nolock) on a.id = log.area
          where log.mkc_id=#{mkcId}
    </select>
    <select id="getPrintInfoPageByPrintArea" resultType="com.jiuji.oa.nc.abnormal.vo.ShowPrintingVO">
        select * from (
            select
                pa.id printAreaId,
                pa.area area,
                count(pa.area) printingNumber
            from
                mkc_print_log mpl with(nolock)
            left join product_mkc mkc with (nolock) on mkc.id = mpl.mkc_id
            left join productinfo p with (nolock) on p.ppriceid = mkc.ppriceid
            left join areainfo pa with(nolock) on pa.id = mpl.area
            where isnumeric(mpl.area) = 1
            <include refid="whereSql"/>
            group by pa.id,pa.area
        ) t
        <where>
            <if test="req.printingNumber!=null and req.printingNumber != 0 ">
                and t.printingNumber &gt;= #{req.printingNumber}
            </if>
        </where>
    </select>
    <select id="getPrintInfoPageByArea" resultType="com.jiuji.oa.nc.abnormal.vo.ShowPrintingVO">
        select * from (
        select count(mkcId) printingNumber,mkcId,ppriceid,area,productName,productColor,orderid from (
        select
        mpl.mkc_id        as mkcId,
        mkc.ppriceid    as ppriceid,
        a.area       as area,
        p.product_name  as productName,
        p.product_color as productColor,
        mkc.orderid     as orderid
        from mkc_print_log mpl with(nolock)
        left join product_mkc mkc with (nolock) on mkc.id = mpl.mkc_id
        left join productinfo p with (nolock) on p.ppriceid = mkc.ppriceid
        left join areainfo a with(nolock) on a.id = mkc.areaid
        <where>
            <include refid="whereSql"/>
        </where>
        ) t1 group by mkcId,ppriceid,area,productName,productColor,orderid
        ) t
        <where>
            <if test="req.printingNumber!=null and req.printingNumber != 0 ">
                and t.printingNumber &gt;= #{req.printingNumber}
            </if>
        </where>
        order by t.printingNumber desc
    </select>

    <sql id="whereSql">
        <if test="req.printingType!=null and req.printingType!='' ">
            and mpl.type = #{req.printingType}
        </if>
        <if test="req.printingStartTime!=null and req.printingEndTime!=null ">
            and mpl.create_time BETWEEN #{req.printingStartTime} and #{req.printingEndTime}
        </if>
        <if test="req.printAreaIdList!=null and req.printAreaIdList.size>0">
            and mpl.area in
            <foreach collection="req.printAreaIdList" item="it" separator="," open="(" close=")">
                #{it}
            </foreach>
        </if>
        <if test="req.areaIdList!=null and req.areaIdList.size>0">
            and mkc.areaid in
            <foreach collection="req.areaIdList" item="it" separator="," open="(" close=")">
                #{it}
            </foreach>
        </if>
    </sql>
</mapper>
