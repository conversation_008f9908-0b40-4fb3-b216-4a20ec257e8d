<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.profitstatistics.mapper.ProfitStatisticsMapper">
  <sql id="profitstatisticsWhere">
    <where>
      a.sub_id=b.sub_id
      AND ISNULL(b.isdel,0) =0
      AND a.sub_check=3
      AND b.basket_id=c.basket_id
      AND c.kc_check =5
      <if test="params.subType==1">
        AND a.subtype=10
      </if>
      <if test="params.subType==0">
        AND a.subtype!=10
      </if>
      AND b.ppriceid=d.ppriceid
      and a.areaid=e.id
      <if test="params.strCIds!=null and params.strCIds!=''">
        and exists(
        select 1 from f_category_children(#{params.strCIds})
        where id = d.cid
        )
      </if>
      <if test="params.searchOption==1">
        and d.product_name like '%' + #{params.searchKey} + '%'
      </if>
      <if test="params.searchOption==2">
        and d.ppriceid = #{params.longSearchKey}
      </if>
      <if test="params.searchOption==3">
        and d.productid = #{params.longSearchKey}
      </if>
      <if test="params.brandIds!=null and params.brandIds.size>0">
        and d.brandID in
        <foreach collection="params.brandIds" open="(" close=")" separator="," item="brandId">
          #{brandId}
        </foreach>
      </if>
      <if test="params.areaIds!=null and params.areaIds.size>0">
        and a.areaid in
        <foreach collection="params.areaIds" open="(" close=")" separator="," item="areaId">
          #{areaId}
        </foreach>
      </if>
      <if test="params.startDate!=null and params.endDate!=null">
        AND a.tradedate1 BETWEEN #{params.startDate} AND #{params.endDate}
      </if>
      <if test="params.areaType!=null">
        and e.AreaType=#{params.areaType}
      </if>
    </where>
  </sql>
  <select id="listPageGroupBySku"
    resultType="com.jiuji.oa.stock.profitstatistics.vo.res.ProfitStatisticsResVO">
    SELECT
    d.ppriceid AS ppid,
    d.product_name,
    d.product_color,
    SUM(b.basket_count) AS [COUNT],
    SUM(b.price2-c.staticprice) AS profit1,
    SUM(b.price2-c.staticprice)/SUM(b.basket_count) AS singleGrossProfit1,
    SUM(b.price2-c.inprice) AS profit2,
    SUM(b.price2-c.inprice)/SUM(b.basket_count) AS singleGrossProfit2,
    SUM(b.price2-c.staticprice)-SUM(b.price2-c.inprice) AS difference
    FROM
    sub AS a WITH(NOLOCK),
    basket AS b WITH(NOLOCK),
    product_mkc AS c WITH(NOLOCK),
    productinfo AS d WITH(NOLOCK),
    areainfo AS e WITH(NOLOCK)
    <include refid="profitstatisticsWhere"/>
    GROUP BY
    d.ppriceid,
    d.product_name,
    d.product_color
  </select>
  <select id="listPageGroupByProduct"
    resultType="com.jiuji.oa.stock.profitstatistics.vo.res.ProfitStatisticsResVO">
    SELECT
    d.productid AS productId,
    d.product_name,
    SUM(b.basket_count) AS [COUNT],
    SUM(b.price2 - c.staticprice) AS profit1,
    SUM(b.price2 - c.staticprice) / SUM(b.basket_count) AS singleGrossProfit1,
    SUM(b.price2 - c.inprice) AS profit2,
    SUM(b.price2 - c.inprice) / SUM(b.basket_count) AS singleGrossProfit2,
    SUM(b.price2 - c.staticprice) - SUM(b.price2 - c.inprice) AS difference
    FROM
    sub AS a WITH(NOLOCK),
    basket AS b WITH(NOLOCK),
    product_mkc AS c WITH(NOLOCK),
    productinfo AS d WITH(NOLOCK),
    areainfo AS e WITH(NOLOCK)
    <include refid="profitstatisticsWhere"/>
    GROUP BY
    d.productid,
    d.product_name
  </select>
  <select id="listPageGroupByBrand"
    resultType="com.jiuji.oa.stock.profitstatistics.vo.res.ProfitStatisticsResVO">
    SELECT
    d.brandID AS brandId,
    f.name  as brandName,
    SUM(b.basket_count) AS [COUNT],
    SUM(b.price2 - c.staticprice) AS profit1,
    SUM(b.price2 - c.staticprice) / SUM(b.basket_count) AS singleGrossProfit1,
    SUM(b.price2 - c.inprice) AS profit2,
    SUM(b.price2 - c.inprice) / SUM(b.basket_count) AS singleGrossProfit2,
    SUM(b.price2 - c.staticprice) - SUM(b.price2 - c.inprice) AS difference
    FROM
    sub AS a WITH(NOLOCK),
    basket AS b WITH(NOLOCK),
    product_mkc AS c WITH(NOLOCK),
    productinfo AS d WITH(NOLOCK),
    areainfo AS e WITH(NOLOCK),
    brand AS f WITH(NOLOCK)
    <include refid="profitstatisticsWhere"/>
    and d.brandID=f.id
    GROUP BY d.brandID,f.name
  </select>
  <select id="listPageGroupByArea"
    resultType="com.jiuji.oa.stock.profitstatistics.vo.res.ProfitStatisticsResVO">
    SELECT
    a.areaid AS areaId,
    e.area as area,
    SUM(b.basket_count) AS [COUNT],
    SUM(b.price2 - c.staticprice) AS profit1,
    SUM(b.price2 - c.staticprice) / SUM(b.basket_count) AS singleGrossProfit1,
    SUM(b.price2 - c.inprice) AS profit2,
    SUM(b.price2 - c.inprice) / SUM(b.basket_count) AS singleGrossProfit2,
    SUM(b.price2 - c.staticprice) - SUM(b.price2 - c.inprice) AS difference
    FROM
    sub AS a WITH(NOLOCK),
    basket AS b WITH(NOLOCK),
    product_mkc AS c WITH(NOLOCK),
    productinfo AS d WITH(NOLOCK),
    areainfo AS e WITH(NOLOCK)
    <include refid="profitstatisticsWhere"/>
    GROUP BY a.areaid, e.area
  </select>
  <select id="getProfitStatistics"
    resultType="com.jiuji.oa.stock.profitstatistics.vo.res.ProfitStatisticsResVO">
    SELECT
    SUM(b.basket_count) AS [COUNT],
    SUM(b.price2 - c.staticprice) AS profit1,
    SUM(b.price2 - c.inprice) AS profit2,
    SUM(b.price2 - c.staticprice) - SUM(b.price2 - c.inprice) AS difference
    FROM
    sub AS a WITH(NOLOCK),
    basket AS b WITH(NOLOCK),
    product_mkc AS c WITH(NOLOCK),
    productinfo AS d WITH(NOLOCK),
    areainfo AS e WITH(NOLOCK)
    <include refid="profitstatisticsWhere"/>
  </select>
</mapper>
