<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.purchase.mapper.ApplePurchaseStoreInventorySumMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.stock.purchase.entity.ApplePurchaseStoreInventorySum">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="totalCount" column="total_count" jdbcType="INTEGER"/>
            <result property="totalPrice" column="total_price" jdbcType="DECIMAL"/>
            <result property="inuserId" column="inuser_id" jdbcType="INTEGER"/>
            <result property="inuser" column="inuser" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="is_del" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,total_count,total_price,
        inuser_id,inuser,create_time,
        update_time,is_del,apple_purchase_store_inventory_sum_rv
    </sql>
</mapper>
