<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.purchase.mapper.ApplePurchaseStoreInventoryMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.stock.purchase.entity.ApplePurchaseStoreInventory">
            <id property="mkc_id" column="mkc_id" jdbcType="VARCHAR"/>
            <result property="area_id" column="area_id" jdbcType="INTEGER"/>
            <result property="channel_id" column="channel_id" jdbcType="INTEGER"/>
            <result property="sku_id" column="sku_id" jdbcType="INTEGER"/>
            <result property="imei" column="imei" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        mkc_id,area_id,channel_id,
        sku_id,imei
    </sql>
    <insert id="batchInsertApplePurchase">
        insert into apple_purchase_store_inventory
        (area_id,sku_id,channel_id,imei,
        mkc_id,price,is_tax,company,mould_flag,
        send_name,send_mobile,send_address,
        express_company,waybill_no,wuliu_id,
        apple_purchase_id,
        receive_name,receive_mobile,receive_address,operator_activity)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.areaId},#{item.skuId},#{item.channelId},#{item.imei},#{item.mkcId},
            #{item.price},#{item.isTax},#{item.company},#{item.mouldFlag},
            #{item.sendName},#{item.sendMobile},#{item.sendAddress},
            #{item.expressCompany},#{item.waybillNo},#{item.wuliuId},
            #{item.applePurchaseId},
            #{item.receiveName},#{item.receiveMobile},#{item.receiveAddress},#{item.operatorActivity}
            )
        </foreach>
    </insert>
    <select id="selectApplePurchaseWuliu" resultType="com.jiuji.oa.wuliu.bo.ApplePurchaseWuliuBO">
        select distinct w.id wuliuId,w.stats,w.com,w.nu,
               w.rmobile receiveMobile,
               w.raddress receiveAddress
        from apple_purchase_store_inventory a with(nolock)
        left join wuliu w with(nolock) on a.wuliu_id = w.id
        where isnull(a.is_del,0) = 0
        and w.stats in (1,2,3,7)
    </select>
    <select id="getStatisticBySummary"
            resultType="com.jiuji.oa.stock.purchase.vo.res.ApplePurchaseStatisticSummaryResVO">
        select aps.id,aps.total_count,aps.total_price,
        aps.inuser,aps.create_time importTime
        from apple_purchase_store_inventory_sum aps with(nolock)
        where exists (select 1 from apple_purchase_store_inventory apsi with(nolock)
        left join product_mkc k with(nolock) on k.id = apsi.mkc_id
        left join productinfo p with(nolock) on k.ppriceid=p.ppriceid
        left join wuliu w with(nolock) on apsi.wuliu_id = w.id
        <include refid="statisticWhere"/>
        and aps.id = apsi.apple_purchase_id
        )
    </select>
    <select id="getStatisticByArea"
            resultType="com.jiuji.oa.stock.purchase.vo.res.ApplePurchaseStatisticAreaResVO">
        select t.* from (
        select apsi.area_id,
            count(apsi.id) purchaseCount,
            sum(case when k.imei is not null then 1 else 0 end) stockedCount,
            sum(case when k.imei is null then 1 else 0 end) unStockedCount
        from apple_purchase_store_inventory apsi with(nolock)
        left join product_mkc k with(nolock) on k.id = apsi.mkc_id
        left join productinfo p with(nolock) on k.ppriceid=p.ppriceid
        left join wuliu w with(nolock) on apsi.wuliu_id = w.id
        <include refid="statisticWhere"/>
        group by apsi.area_id
        ) t
    </select>
    <select id="getStatisticByCaigou"
            resultType="com.jiuji.oa.stock.purchase.vo.res.ApplePurchaseStatisticCaigouResVO">
        select apsi.area_id,
        apsi.caigou_sub_id caigouSubId,apsi.wuliu_id,
        count(apsi.id) purchaseCount,
        sum(case when k.imei is not null then 1 else 0 end) stockedCount,
        sum(case when k.imei is null then 1 else 0 end) unStockedCount
        from apple_purchase_store_inventory apsi with(nolock)
        left join product_mkc k with(nolock) on k.id = apsi.mkc_id
        left join productinfo p with(nolock) on k.ppriceid=p.ppriceid
        left join wuliu w with(nolock) on apsi.wuliu_id = w.id
        <include refid="statisticWhere"/>
        group by apsi.caigou_sub_id,apsi.wuliu_id,apsi.area_id
    </select>
    <select id="getStatisticByAreaAndSku"
            resultType="com.jiuji.oa.stock.purchase.vo.res.ApplePurchaseStatisticAreaAndSkuResVO">
        select t.* from (
        select apsi.area_id,
        p.ppriceid skuId,p.product_name productName,p.product_color productColor,
        count(apsi.id) purchaseCount,
        sum(case when k.imei is not null then 1 else 0 end) stockedCount,
        sum(case when k.imei is null then 1 else 0 end) unStockedCount
        from apple_purchase_store_inventory apsi with(nolock)
        left join product_mkc k with(nolock) on k.id = apsi.mkc_id
        left join productinfo p with(nolock) on k.ppriceid=p.ppriceid
        left join wuliu w with(nolock) on apsi.wuliu_id = w.id
        <include refid="statisticWhere"/>
        group by p.ppriceid,p.product_name,p.product_color,apsi.area_id
        ) t
    </select>
    <select id="getStatisticBySku"
            resultType="com.jiuji.oa.stock.purchase.vo.res.ApplePurchaseStatisticSkuResVO">
        select t.* from (
        select p.ppriceid skuId,p.product_name,p.product_color,
        count(apsi.id) purchaseCount,
        sum(case when k.imei is not null then 1 else 0 end) stockedCount,
        sum(case when k.imei is null then 1 else 0 end) unStockedCount
        from apple_purchase_store_inventory apsi with(nolock)
        left join product_mkc k with(nolock) on k.id = apsi.mkc_id
        left join productinfo p with(nolock) on k.ppriceid=p.ppriceid
        left join wuliu w with(nolock) on apsi.wuliu_id = w.id
        <include refid="statisticWhere"/>
        group by p.ppriceid,p.product_name,p.product_color
        ) t
    </select>
    <select id="getStatisticByMkc"
            resultType="com.jiuji.oa.stock.purchase.vo.res.ApplePurchaseStatisticMkcResVO">
        select
            apsi.mkc_id mkcId,
            apsi.sku_id skuId,
            c.name categoryName,
            apsi.area_id,
            p.product_name productName,
            p.product_color productColor,
            apsi.imei,
            concat(owq.company_jc,'(',owq.id,')') qudaoJc,
            apsi.waybill_no waybillNo,
            apsi.caigou_sub_id caigouSubId,
            k.inbeihuodate purchaseDate,
            w.ctime receiptDate,
            k.imeidate imeiDate
        from apple_purchase_store_inventory apsi with(nolock)
        left join product_mkc k with(nolock) on k.id = apsi.mkc_id
        left join productinfo p with(nolock) on k.ppriceid = p.ppriceid
        left join category c with(nolock) on c.id = p.cid
        left join Ok3w_qudao owq with(nolock) on apsi.channel_id = owq.id
        left join wuliu w with(nolock) on apsi.wuliu_id = w.id
        <include refid="statisticWhere"/>
    </select>
    <select id="getStatisticBySummarySum"
            resultType="com.jiuji.oa.stock.purchase.vo.res.ApplePurchaseStatisticSummaryResVO">
        select '合计' id,isnull(sum(aps.total_count),0) totalCount,isnull(sum(aps.total_price),0) totalPrice
        from apple_purchase_store_inventory_sum aps with(nolock)
        where exists (select 1 from apple_purchase_store_inventory apsi with(nolock)
        left join product_mkc k with(nolock) on k.id = apsi.mkc_id
        left join productinfo p with(nolock) on k.ppriceid=p.ppriceid
        left join wuliu w with(nolock) on apsi.wuliu_id = w.id
        <include refid="statisticWhere"/>
        and aps.id = apsi.apple_purchase_id
        )
    </select>
    <select id="getStatisticBySum"
            resultType="com.jiuji.oa.stock.purchase.vo.res.ApplePurchaseStatisticSkuResVO">
        select '合计' skuId,
        count(apsi.id) purchaseCount,
        sum(case when k.imei is not null then 1 else 0 end) stockedCount,
        sum(case when k.imei is null then 1 else 0 end) unStockedCount
        from apple_purchase_store_inventory apsi with(nolock)
        left join product_mkc k with(nolock) on k.id = apsi.mkc_id
        left join productinfo p with(nolock) on k.ppriceid=p.ppriceid
        left join wuliu w with(nolock) on apsi.wuliu_id = w.id
        <include refid="statisticWhere"/>
    </select>
    <select id="getCaigouIdByApplePurchaseIds"
            resultType="com.jiuji.oa.stock.purchase.vo.res.ApplePurchaseCaigouVO">
        select DISTINCT apsi.caigou_sub_id caigouId,apsi.apple_purchase_id applePurchaseId
        from apple_purchase_store_inventory apsi with(nolock)
        where apsi.caigou_sub_id is not null
        and apsi.apple_purchase_id in
        <foreach collection="applePurchaseIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <sql id="statisticWhere">
        <where>
            and isnull(apsi.is_del,0) = 0
        <if test="req.searchKey != null and req.searchKey != ''">
            <if test="req.searchKey == 'sku_id' and req.searchValue != null and req.searchValue != ''">
                and apsi.sku_id = #{req.searchValue}
            </if>
            <if test="req.searchKey == 'pruduct_id' and req.searchValue != null and req.searchValue != ''">
                and p.product_id = #{req.searchValue}
            </if>
            <if test="req.searchKey == 'pruduct_name' and req.searchValue != null and req.searchValue != ''">
                and p.product_name like concat('%',#{req.searchValue},'%')
            </if>
            <if test="req.searchKey == 'mkc_id' and req.searchValue != null and req.searchValue != ''">
                and apsi.mkc_id = #{req.searchValue}
            </if>
            <if test="req.searchKey == 'orderid' and req.searchValue != null and req.searchValue != ''">
                and k.orderid = #{req.searchValue}
            </if>
            <if test="req.searchKey == 'imei' and req.searchValue != null and req.searchValue != ''">
                and apsi.imei = #{req.searchValue}
            </if>
            <if test="req.searchKey == 'waybill_no' and req.searchValue != null and req.searchValue != ''">
                and apsi.waybill_no = #{req.searchValue}
            </if>
            <if test="req.searchKey == 'wuliu_id' and req.searchValue != null and req.searchValue != ''">
                and apsi.wuliu_id = #{req.searchValue}
            </if>
            <if test="req.searchKey == 'apple_purchase_id' and req.searchValue != null and req.searchValue != ''">
                and apsi.apple_purchase_id = #{req.searchValue}
            </if>
        </if>
        <if test="req.categoryCharSeq != null and req.categoryCharSeq != '' ">
            and p.cid in (select id from f_category_children (#{req.categoryCharSeq}))
        </if>
        <if test="req.brandIdList != null and req.brandIdList.size > 0">
            and p.brandID in
            <foreach collection="req.brandIdList" item="it" separator="," open="(" close=")">
                #{it}
            </foreach>
        </if>
        <if test="req.areaIdsCharSeq != null and req.areaIdsCharSeq != ''">
            and exists(SELECT 1 FROM dbo.F_SPLIT(#{req.areaIdsCharSeq},',') f WHERE f.split_value = CAST(apsi.area_id AS VARCHAR(20)))
        </if>
        <if test="req.channelIdList != null and req.channelIdList.size > 0">
            and apsi.channel_id in
            <foreach collection="req.channelIdList" item="it" separator="," open="(" close=")">
                #{it}
            </foreach>
        </if>
        <if test="req.hasReceipted == 0">
            and w.stats in (0,1,2,3,5,7)
        </if>
        <if test="req.hasReceipted == 1">
            and w.stats in (4,6)
        </if>
        <if test="req.hasInventory == 0">
            and k.imei is null
        </if>
        <if test="req.hasInventory == 1">
            and k.imei is not null
        </if>
        <if test="req.mouldFlag != null">
            and isnull(apsi.mould_flag,0) = #{req.mouldFlag}
        </if>
        <if test="req.timeType == 'caigou_date' and req.startTime != null and req.endTime != null">
            and k.inbeihuodate between #{req.startTime} and #{req.endTime}
        </if>
        <if test="req.timeType == 'imei_date' and req.startTime != null and req.endTime != null">
            and k.imeidate between #{req.startTime} and #{req.endTime}
        </if>
        <if test="req.timeType == 'receipted_date' and req.startTime != null and req.endTime != null">
            and w.ctime between #{req.startTime} and #{req.endTime}
        </if>
        </where>
    </sql>
    <select id="getApplePurchaseByMkcId"
            resultType="com.jiuji.oa.stock.purchase.entity.ApplePurchaseStoreInventory">
        select top 1 a.* from apple_purchase_store_inventory a with(nolock) where isnull(a.is_del,0) = 0 and a.mkc_id = #{mkcId}
    </select>
    <select id="getPushApplePurchase" resultType="com.jiuji.oa.stock.purchase.vo.ApplePurchasePushVO">
        select a.area_id,a.caigou_sub_id,a.waybill_no,COUNT(1) as count
        from apple_purchase_store_inventory a with(nolock)
            left join wuliu w with(nolock) on a.wuliu_id = w.id
            left join product_mkc m with(nolock) on m.id = a.mkc_id
        where isnull(a.is_del,0) = 0
          and m.imei is NULL
          and a.is_del = 0
          and m.kc_check = 10
          and w.stats in (4,6)
        GROUP by a.area_id,a.caigou_sub_id,a.waybill_no
    </select>
</mapper>
