<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.purchase.mapper.HasUserIdOutPutWebMapper">
    <select id="getHasUserIdOutPutWeb" resultType="com.jiuji.oa.stock.outputweb.entity.Outputweb">
        SELECT * FROM OutPutWeb with(nolock) where isnull(isDelete,0)=0 and userId IS NOT NULL ORDER BY rank desc;
    </select>
</mapper>
