<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.purchase.mapper.IsDisplyInStorageMapper">
    <select id="getSysInfo" resultType="com.jiuji.oa.nc.oaapp.po.SysConfig">
        select *  from sysConfig sc with(nolock) where code =#{code} and xtenant = #{xTenant}
    </select>
</mapper>
