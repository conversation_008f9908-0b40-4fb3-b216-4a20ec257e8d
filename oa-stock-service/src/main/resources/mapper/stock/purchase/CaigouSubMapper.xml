<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~
  ~      Copyright © 2006 - 2020 九机网 All Rights Reserved
  ~
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.stock.purchase.mapper.CaigouSubMapper">


    <!-- getCaigouSubStatsById -->
    <select id="getCaigouSubStatsById" resultType="com.jiuji.oa.stock.chw.dto.CaigouSubStatsDTO">
        select stats,pay1 , pay2 ,kinds,subKind
        from caigou_sub with(nolock)
        where id=#{id}
    </select>

    <!-- getCaigouAdjustPiqian -->
    <select id="getCaigouAdjustPiqian" resultType="com.jiuji.oa.stock.chw.dto.CaigouAdjustPiqianDTO">
        select stats,insourceid,adjustPiqianNum,(select sum(inprice) from dbo.caigou_basket with(nolock) where sub_id = #{subId}) as price,subKind
        from dbo.caigou_sub with(nolock)
        where id=#{subId}
    </select>

    <!-- countCaigouSubByAdjustPiqianNum -->
    <select id="countCaigouSubByAdjustPiqianNum" resultType="int">
        select count(1) from dbo.caigou_sub s with(nolock)
        where isnull(s.subKind,0)=1 and s.stats in (0,1,2,3) and s.adjustPiqianNum = #{adjustPiqianNum}
    </select>
    <select id="getInPrice" resultType="java.math.BigDecimal">
        SELECT
            TOP 1 b.inprice
        FROM caigou_sub s with(nolock)
        INNER JOIN dbo.caigou_basket b with(nolock) ON s.id=b.sub_id
        INNER JOIN dbo.areainfo a with(nolock) ON a.id=s.areaid
        WHERE
            s.stats = 3
            AND b.ppriceid= #{skuId}
            AND a.kind1 = 1 ORDER BY s.id DESC
    </select>
    <select id="getLastPurchasePrice" resultType="java.math.BigDecimal">
        select
            top 1 b.inprice
        from dbo.caigou_basket b with(nolock)
        left join dbo.caigou_sub s with(nolock) on b.sub_id=s.id
        where s.[stats]=3 and s.kinds='pj'
        and exists(select 1 from areainfo a with(nolock) where a.id=s.areaid and a.kind1=1)
        and b.ppriceid=#{skuId}
        and s.insourceid &lt;&gt; 540
        order by s.ruku_dtime desc
    </select>




    <select id="findSubById" resultType="com.jiuji.oa.stock.purchase.vo.CaigouDto">
        SELECT *,isnull(stats,0) as stats_,
               (case when pay1 is NULL then 0 else 1 end) as ispay1,
               (case when pay2 is NULL then 0 else 1 end) as ispay2
        FROM caigou_sub with(nolock)
        WHERE 1=1 and id= #{subId}
     </select>
    <select id="inspectCaigou" resultType="com.jiuji.oa.stock.purchase.dto.InspectCaigouDto">
        SELECT p.product_name,
               p.product_color,
               b.lcount,
               s.id as subId
        FROM caigou_basket b with(nolock)
                 LEFT JOIN productinfo p with(nolock) ON b.ppriceid = p.ppriceid
                 LEFT JOIN caigou_sub s with(nolock) ON s.id = b.sub_id
        <where>
            s.stats IN (0, 1, 2)
            AND s.areaid = #{area}
            AND b.ppriceid IN
            <foreach collection="ppriceids" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            ORDER BY s.id DESC
        </where>
  </select>

    <sql id="caigouConditionList">
        <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==1">
            AND s.id = #{req.searchValue}
        </if>
        <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==2">
            and p.ppriceid = #{req.searchValue}
        </if>
        <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==3">
            and p.product_name like '%' + #{req.searchValue} + '%'
        </if>
        <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==4">
            and s.title like '%' + #{req.searchValue} + '%'
        </if>
        <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==5">
            and p.productid = #{req.searchValue}
        </if>
        <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==6">
            AND s.inuser LIKE CONCAT('%',#{req.searchValue},'%')
        </if>
        <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==7">
            and p.barcode = #{req.searchValue}
        </if>
        <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==8">
            AND s.inuser LIKE CONCAT('%',#{req.searchValue},'%') and s.stats in (2,3)
        </if>
        <choose>
            <when test="req.stats != null">
                and s.stats=#{req.stats}
            </when>
            <otherwise>
                and s.stats &lt;&gt; -1
            </otherwise>
        </choose>
        <choose>
            <when test="req.payStats == 0 ">
                and s.piqianhao is null and s.pay1 is  null and s.pay2 is null
            </when>
            <when test="req.payStats==1">
                and isnull(s.payedPrice,0)=0
            </when>
            <when test="req.payStats==2">
                and isnull(s.payedPrice,0) &lt;&gt; 0
            </when>
            <when test="req.payStats==3">
                and s.pay2 &lt; 0
            </when>
            <when test="req.payStats==4">
                and s.piqianhao is not null and  s.pay1 is  null
            </when>
        </choose>
        <!--分类-->
        <if test="req.categoryCharSeq != null and req.categoryCharSeq !='' ">
            AND exists( select 1 from f_category_children(#{req.categoryCharSeq}) f where f.id=p.cid )
        </if>
        <!--品牌专区-->
        <if test="req.brandIds!=null and req.brandIds.size()!=0">
            and p.brandid in
            <foreach item="item" index="index" collection="req.brandIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.searchAreaIdList != null and req.searchAreaIdList.size()>0 ">
            and s.areaid in
            <foreach collection="req.searchAreaIdList" index="index" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <choose>
            <when test="req.kind1!=null and req.kind1!=''">
                <choose>
                    <when test="req.kind1==2">
                        and exists(select sub_id from caigou_basket b with(nolock) where b.fromAreaId is not null and sub_id=s.id)
                    </when>
                    <when test="req.kind1==1">
                        and exists(select sub_id from caigou_basket b with(nolock) where b.basket_id is not null and sub_id=s.id)
                    </when>
                    <otherwise>
                        and exists(select sub_id from caigou_basket b with(nolock) where b.basket_id is null and sub_id=s.id)
                    </otherwise>
                </choose>
            </when>
        </choose>
        <if test="req.subKind != null">
            and s.subKind= #{req.subKind}
        </if>
        <!--门店类别-->
        <if test="req.areaKind!=null and req.areaKind==1">
            and a.kind1 = 1
        </if>
        <if test="req.areaKind!=null and req.areaKind==2">
            and a.kind1 = 2
        </if>
        <if test="req.kinds!=null and req.kinds!=''">
            and s.kinds = #{req.kinds}
        </if>
        <if test="req.insourceIds!=null and req.insourceIds.size()!=0">
            AND s.insourceId in
            <foreach item="item" index="index" collection="req.insourceIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.startTime != null and req.endTime != null  ">
            <if test="req.searchTimeType!=null and req.searchTimeType==1">
                and s.dtime between #{req.startTime} and #{req.endTime}
            </if>
            <if test="req.searchTimeType!=null and req.searchTimeType==2">
                and s.pay1_dtime between #{req.startTime} and #{req.endTime}
            </if>
            <if test="req.searchTimeType!=null and req.searchTimeType==3">
                and s.pay2_dtime between #{req.startTime} and #{req.endTime}
            </if>
            <if test="req.searchTimeType!=null and req.searchTimeType==4">
                and s.ruku_dtime between #{req.startTime} and #{req.endTime}
            </if>
        </if>
    </sql>

    <select id="selectPageList" resultType="com.jiuji.oa.stock.purchase.vo.CaigouListPageVO">
        select s.*, isnull(s.stats, 0) as newStats, i.company_jc as insourceName, payedPrice as pay, bb.inprices, bb.lcount
        from caigou_sub s with (nolock)
        left join Ok3w_qudao i with (nolock) on s.insourceid = i.id
        LEFT JOIN (
        seLect isnull(SUM(lcount * inprice), 0) as inprices, sub_id, sum(lcount) lcount
        from caigou_basket with (nolock)
        GROUP BY sub_id
        ) bb ON s.id = bb.sub_id
        <where>
          <include refid="caigouConditionList"></include>
        </where>
    </select>

    <select id="selectCurrentQuery" resultType="com.jiuji.oa.stock.purchase.vo.CaigouListVO">
        seLect isnull(SUM(lcount * inprice), 0) as currentQueryInprices, sum(lcount) currentQueryLcount
        from caigou_basket b with (nolock)
         left join caigou_sub s with (nolock) on b.sub_id = s.id
        <where>
            <include refid="caigouConditionList"></include>
        </where>
    </select>



    <!-- 更新采购单状态-->
    <update id="updateCaigouSubStatsById">
        update caigou_sub
        set stats = #{stats}
        <if test="txtDay != null">
            , zaituDtime = dateadd(day, #{txtDay}, cast(getdate() as date))
        </if>
        where id = #{subId}
    </update>


    <select id="selectCaigouById" resultType="com.jiuji.oa.stock.purchase.vo.CaiGouShowVO">
        SELECT o.companyName,
               o.areaName,
               s.*,
               ISNULL(s.stats, 0)                         as stats_,
               (CASE WHEN pay1 is NULL then 0 else 1 end) as ispay1,
               (CASE WHEN pay2 is NULL then 0 else 1 end) as ispay2,
               i.company_jc                               as 'insource_name',
               s.subKind,
               s.adjustPiqianNum
        FROM caigou_sub s with(nolock)
                 LEFT JOIN Ok3w_qudao i with(nolock) on s.insourceid = i.id
                 LEFT JOIN dbo.purchaseExInfo o with(nolock) on s.id = o.caigouId
        WHERE s.id =#{subId}
    </select>
    <select id="selectCaigouBasketById" resultType="com.jiuji.oa.stock.purchase.vo.CaiGouShowDetailsVO">

        SELECT p.memberprice,
               p.product_name,
               p.product_color,
               p.pLabel,
               p.barCode,
               b.*,
               ref.ShouhouId,
               p.barCodeCount
        FROM caigou_basket b with(nolock)
                 LEFT JOIN productinfo p with(nolock) on b.ppriceid = p.ppriceid
                 LEFT JOIN (
            SELECT CaigouBasketId,
                   STUFF(
                           (
                               SELECT ',' + CAST(cra.ShouhouId AS VARCHAR(30))
                               FROM CaigouBasketRefShouhou cra with(nolock)
                               WHERE crb.CaigouBasketId = cra.CaigouBasketId
                               FOR XML PATH('')
                       ), 1, 1, ''
               ) AS ShouhouId
            FROM CaigouBasketRefShouhou crb with(nolock)
            GROUP BY CaigouBasketId
        ) AS ref ON ref.CaigouBasketId = b.id
        WHERE sub_id = #{subId}
        order by b.id;
   </select>
    <select id="getApplyInfoAmount" resultType="com.jiuji.oa.stock.purchase.vo.ApplyInfoAmountVO">
        SELECT f.CurrentStatus, f.amount
        FROM office.dbo.applyPayInfo a WITH (nolock)
         INNER JOIN office.dbo.T_ApplyInfo f WITH (nolock) ON f.id = a.applyid
            INNER JOIN caigou_sub s WITH (nolock) ON cast(s.id as nvarchar(50)) = a.caigouid
        WHERE f.IsDel = 0
          AND s.id = #{subId}

    </select>

    <select id="selectSum" resultType="java.math.BigDecimal">
        select sum(b.inprice*(case when s.subKind=1 then 1 else b.lcount end)) from caigou_basket b with(nolock) left join caigou_sub s with(nolock) on s.id = b.sub_id where b.sub_id = #{subId}
    </select>
    <select id="selectExportPageList" resultType="com.jiuji.oa.stock.purchase.vo.CaigouListExcelVO">
        select a.area                      as areaCode,
        s.id                        as subId,
        s.title,
        s.inuser,
        s.dtime,
        (case s.stats
        when -1 then '已删除'
        when 0 then '待审核'
        when 1 then '已审核'
        when 2 then '在途'
        when 3 then '已完成' end) as statsValue,
        s.zaituDtime,
        s.payedPrice,
        i.company_jc                as qudaoName,
        s.insourceid                as qudaoId,
        cb.ppriceid,
        p.product_name,
        p.product_color,
        cb.lcount                   as count,
        cb.inputCount,
        cb.leftCount,
        p.memberprice,
        (cb.inprice * cb.lcount)    as caigouPrice,
        p.costprice,
        cb.inprice,
        t.totalAmount
        from caigou_basket cb with (nolock)
        left join caigou_sub s with (nolock) on cb.sub_id = s.id
        left join productinfo p with (nolock) on p.ppriceid = cb.ppriceid
        left join areainfo a with (nolock) on a.id = s.areaid
        left join Ok3w_qudao i with (nolock) on i.id = s.insourceid
        left join (select sub_id, sum(cb.inprice * cb.lcount) as totalAmount from caigou_basket cb with (nolock) group by cb.sub_id) t on t.sub_id=cb.sub_id
        <where>
            <include refid="caigouConditionList"></include>
        </where>
    </select>
    <select id="selectCaiGouInfoByIds" resultType="com.jiuji.oa.stock.purchase.vo.BatchRemarksVo">
        select i.company_jc                as insource_name,
               t.certifNumber              as certifNumber,
               s.id                        as subId,
               s.kinds,
               (case s.stats
                    when -1 then '已删除'
                    when 0 then '待审核'
                    when 1 then '已审核'
                    when 2 then '在途'
                    when 3 then '已完成' end) as statsValue,
               bb.inprices,
               isnull('-1', s.pay1)        as pay1,
               isnull('-1', s.pay2)        as pay2,
               t.sumPrice
        from caigou_sub s with(nolock)
                 left join Ok3w_qudao i with(nolock) on s.insourceid = i.id
                 left join (seLect isnull(SUM(lcount * inprice), 0) as inprices, sub_id from caigou_basket with(nolock) GROUP BY sub_id) bb
                           ON s.id = bb.sub_id
                 left join(select qudao.id         as qudaoId,
                                  sub.CertifNumber as certifNumber,
                                  sum(cc.inprices) as sumPrice
                           from caigou_sub sub with(nolock)
                                    left join Ok3w_qudao qudao with(nolock) on sub.insourceid = qudao.id
                                    LEFT JOIN (seLect isnull(SUM(lcount * inprice), 0) as inprices, sub_id
                                               from caigou_basket with(nolock)
                                               GROUP BY sub_id) cc
                                              ON sub.id = cc.sub_id
                           <where>
                               sub.id in
                               <foreach collection="subIds" item="item" open="(" close=")" separator=",">
                                   #{item}
                               </foreach>

                           </where>

                           group by qudao.id, sub.CertifNumber) t on t.qudaoId = s.insourceid
        <where>
            s.id in
            <foreach collection="subIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>

        </where>

    </select>

    <select id="getCaigouSubListAppPage" resultType="com.jiuji.oa.stock.purchase.vo.CaiGouSubListAppPageRes">
        select * from (select DISTINCT s.id as subId, isnull(s.stats, 0) as status,s.title,s.insourceid as insourceId,i.company_jc as insourceName,
               payedPrice as pay, bb.inprices, bb.lcount,s.inuser,s.dtime,a.area as areaCode
        from caigou_sub s with (nolock)
        left join Ok3w_qudao i with (nolock) on s.insourceid = i.id
        left join areainfo a with(nolock) on a.id = s.areaid
        left join caigou_basket b with(nolock) on b.sub_id = s.id
        left join productinfo p with(nolock) on b.ppriceid = p.ppriceid
        LEFT JOIN (
        seLect isnull(SUM(lcount * inprice), 0) as inprices, sub_id, sum(lcount) lcount
        from caigou_basket with (nolock)
        GROUP BY sub_id
        ) bb ON s.id = bb.sub_id
        <where>
            <include refid="caigouConditionList"></include>
        </where>
        ) as temp
        order by temp.dtime desc
    </select>
    <select id="getApplyingPrice" resultType="java.math.BigDecimal">
        SELECT isnull(sum(amount),0) as applyingPrice
        FROM
            dbo.applyPayInfo a WITH (nolock)
        INNER JOIN dbo.T_ApplyInfo f WITH (nolock) ON
            f.id = a.applyid
        WHERE
            f.IsDel = 0
          AND f.CurrentStatus in (1,2,8)
          AND a.caigouid = #{caigouSubId}
    </select>

</mapper>
