<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.purchase.mapper.PurchaseExpressMapper">
    <select id="getNameByCode" resultType="java.lang.String">
        select expressName from expressEnum with(nolock) where expressCode=#{expressCode} AND isdel = 0;
    </select>

    <select id="getExpressInfo" resultType="java.lang.String">
        SELECT * from expressEnum with(nolock) WHERE expressName = #{expressName}
    </select>
</mapper>
