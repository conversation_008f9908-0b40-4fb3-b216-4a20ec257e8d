<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.purchase.mapper.CheckCaigouInpriceMapper">
    <select id="getCheckCaiGouInPrice" resultType="com.jiuji.oa.stock.purchase.vo.CaiGouAddDetailsVO">
        select c.inprice,
               c.ppriceid,
               p.product_name,
               p.product_color
        from (
                 select b.inprice,
                        ppriceid,
                        row_number() over (partition by ppriceid
                    order by
                        dtime desc) as rank_
                 from caigou_basket b with (nolock)
                  left join caigou_sub s with (nolock) on
                     b.sub_id = s.id
                <where>
                    s.stats in (2, 3)
                    and ppriceid in
                    <foreach collection="ppidList" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    and s.areaid = #{areaid}
                </where>
                  ) c
                 left join dbo.productinfo p with (nolock) on
            p.ppriceid = c.ppriceid
        where c.rank_ = 1

    </select>
</mapper>
