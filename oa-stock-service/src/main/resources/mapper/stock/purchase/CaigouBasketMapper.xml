<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~
  ~      Copyright © 2006 - 2020 九机网 All Rights Reserved
  ~
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.stock.purchase.mapper.CaigouBasketMapper">

  <resultMap id="CaigouBasketMap" type="com.jiuji.oa.stock.purchase.entity.CaigouBasket">
                  <id property="id" column="id"/>
                        <result property="id" column="id"/>
                        <result property="subId" column="sub_id"/>
                        <result property="lcount" column="lcount"/>
                        <result property="inprice" column="inprice"/>
                        <result property="comment" column="comment"/>
                        <result property="ppriceid" column="ppriceid"/>
                        <result property="basketId" column="basket_id"/>
                        <result property="yaid" column="yaid"/>
                        <result property="fromAreaId" column="fromAreaId"/>
                        <result property="inputCount" column="inputCount"/>
            </resultMap>
    <update id="updateSubState">
       update caigou_sub set stats= -1 where id= #{subId}
    </update>



      <select id="purchaseCancelQuery" resultType="com.jiuji.oa.stock.chw.vo.CaigouBasketCancelVo">
            select
                stats,
                (case when pay1 is NULL then 0 else 1 end) as ispay1,
                (case when pay2 is NULL then 0 else 1 end) as ispay2,
                kinds,
                subKind
            from
                caigou_sub with(nolock)
            where id=#{id}
      </select>

    <!-- listCaigouBasketBySubId -->
    <select id="listCaigouBasketBySubId" resultType="com.jiuji.oa.stock.chw.dto.CaigouBasketDTO">
        SELECT DISTINCT ya.ppriceid,ya.number
        FROM dbo.caigou_basket cb with(nolock)
            INNER JOIN dbo.shouhou_yaping ya with(nolock) ON cb.yaid=ya.id
        WHERE cb.sub_id=#{subId} AND cb.yaid IS NOT NULL
    </select>
    <select id="purchaseCancelQueryDetail" resultType="com.jiuji.oa.stock.chw.vo.CaigouDetailVo">
        SELECT
            DISTINCT ya.ppriceid,
            ya.number
        FROM
            dbo.caigou_basket cb with(nolock) INNER JOIN dbo.shouhou_yaping ya with(nolock) ON cb.yaid=ya.id
        WHERE cb.sub_id={id} AND cb.yaid IS NOT NULL

    </select>
    <select id="queryPurchaseDetails" resultType="com.jiuji.oa.stock.inboundquery.vo.ShowPurchaseDetailsVO">
        select b.id,
               b.ppriceid,
               b.inprice,
               b.leftCount                                              lcount,
               p.product_name,
               p.product_color,
               p.barCode,
               s.title,
               b.sub_id,
               kc.number,
               case when b.basket_id is null then 0 else 1 end as productType,
               case when b.basket_id is null then '现货' else '订货' end as  productTypeName
        from caigou_basket b with(nolock)
                 left join caigou_sub s with(nolock) on b.sub_id = s.id
                 left join productinfo p with(nolock) on b.ppriceid = p.ppriceid
                 left join dbo.product_kc kc with(nolock) on p.ppriceid = kc.ppriceid and kc.areaid = s.areaid
        where b.sub_id = #{selectPurchaseDetailsVO.caigouId}
          and s.areaid = #{selectPurchaseDetailsVO.areaId}
          and b.leftCount &gt;0
        order by b.id
    </select>
    <select id="getBasketStatistics" resultType="com.jiuji.oa.stock.purchase.dto.BasketStatisticsDto">
        select sub_id, sum(lcount * inprice)as amount from dbo.caigou_basket with(nolock)
        <where>
        1=1
            and sub_id in
            <foreach collection="subIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </where>
        group by sub_id


    </select>
    <select id="getCreateExcel" resultType="com.jiuji.oa.stock.purchase.vo.req.PaymentEndorsementExclVO">
        select qudao.company                    as channelName,
               sub.id                           as subId,
               info.product_name                as productName,
               basket.inprice                   as inprice,
               basket.lcount                    as lcount,
               (basket.inprice * basket.lcount) as smallSum,
               t.allSum                         as allSum
        from caigou_basket basket with(nolock)
                 left join caigou_sub sub with(nolock) on sub.id = basket.sub_id
                 left join Ok3w_qudao qudao with(nolock) on qudao.id = sub.insourceid
                 left join productinfo info with(nolock) on info.ppriceid = basket.ppriceid
                 left join (select ba.sub_id, sum(ba.inprice * ba.lcount) as allSum
                            from caigou_basket ba with(nolock) group by ba.sub_id) t on t.sub_id = sub.id
        <where>
            basket.sub_id in
            <foreach collection="subIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </where>
    </select>

    <!-- updateShouhouHuishouByYappid -->
    <update id="updateShouhouHuishouByYappid">
        UPDATE hs SET hs.yaping=2
        FROM dbo.shouhou_huishou hs
            INNER JOIN dbo.shouhou_yaping ya ON ya.hsid=hs.id
        WHERE ya.number=#{yanumber} AND ya.ppriceid=#{yappid} AND toareaid=360 AND ISNULL(yaping,0)=3
    </update>
    <update id="updateDetail">
        UPDATE hs
        SET hs.yaping=2
        FROM dbo.shouhou_huishou hs INNER JOIN dbo.shouhou_yaping ya ON ya.hsid=hs.id
        WHERE
            ya.number=#{yanumber}
            AND ya.ppriceid=#{yappid}
            AND toareaid=360
            AND ISNULL(yaping,0)=3
    </update>

    <!-- deleteShouhouHuishouByYappid -->
    <delete id="deleteShouhouHuishouByYappid">
        DELETE dbo.shouhou_yaping WHERE number=#{yanumber} AND ppriceid=#{yappid}
    </delete>


</mapper>
