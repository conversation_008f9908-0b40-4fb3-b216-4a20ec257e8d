<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~
  ~      Copyright © 2006 - 2020 九机网 All Rights Reserved
  ~
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.nc.stock.mapper.StockOutConfigMapper">

    <resultMap id="stockOutConfigMap" type="com.jiuji.oa.nc.stock.entity.StockOutConfig">
        <id property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="cid" column="cid"/>
        <result property="brandId" column="brand_id"/>
        <result property="areaId" column="area_id"/>
        <result property="areaType" column="area_type"/>
        <result property="channelId" column="channel_id"/>
        <result property="productExcludeId" column="product_exclude_id"/>
        <result property="costOutRate" column="cost_out_rate"/>
        <result property="stockAgeLimit" column="stock_age_limit"/>
        <result property="comparator" column="comparator"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    <select id="getStockOutConfigPage" resultType="com.jiuji.oa.nc.stock.vo.res.StockOutConfigRes">
        select * from stock_out_config
        <where>
            <if test="req.title != null and req.title != ''">
                and title like concat('%',#{req.title},'%')
            </if>
            <if test="req.cids != null and req.cids.size > 0">
                and cid in
                <foreach collection="req.cids" index="index" item="cid" separator="," open="(" close=")">
                    #{cid}
                </foreach>
            </if>
            <if test="req.brandIds != null and req.brandIds.size > 0">
                and brand_id in
                <foreach collection="req.brandIds" index="index" item="brandId" separator="," open="(" close=")">
                    #{brandId}
                </foreach>
            </if>
            <if test="req.areaIds != null and req.areaIds.size > 0">
                and area_id in
                <foreach collection="req.areaIds" index="index" item="areaId" separator="," open="(" close=")">
                    #{areaId}
                </foreach>
            </if>
            <if test="req.areaTypes != null and req.areaTypes.size > 0">
                and area_type in
                <foreach collection="req.areaTypes" index="index" item="areaType" separator="," open="(" close=")">
                    #{areaType}
                </foreach>
            </if>
            <if test="req.channelIds != null and req.channelIds.size > 0">
                and
                <foreach collection="req.channelIds" index="index" item="channelId" separator=" " open="(" close=")">
                    <choose>
                        <when test="index == 0">
                            CONCAT(',', channel_id, ',') like CONCAT('%', ',', #{channelId}, ',', '%')
                        </when>
                        <otherwise>
                            OR CONCAT(',', channel_id, ',') like CONCAT('%', ',', #{channelId}, ',', '%')
                        </otherwise>
                    </choose>
                </foreach>
            </if>
            <if test="req.authorizeId != null">
                and authorize_id = #{req.authorizeId}
            </if>
            <if test="req.xtenant != null">
                and xtenant = #{req.xtenant}
            </if>
            and is_delete = 0
        </where>
    </select>
    <select id="listByConfig" resultType="java.lang.Integer">
        <if test="configs != null and configs.size > 0">
            <foreach collection="configs" index="index" item="config" separator="UNION" open="" close="">
                select t.id from (
                    <choose>
                        <when test="config.costOutRate == null">
                            select top 100 percent
                        </when>
                        <otherwise>
                            select top CONVERT(decimal(19),#{config.costOutRate}) percent
                        </otherwise>
                    </choose>
                    k.id from product_mkc k with(nolock)
                    left join dbo.productinfo p with(nolock) on k.ppriceid = p.ppriceid
                    where basket_id is null and isnull(k.mouldFlag,0)=0 and kc_check in (2,3,10) and k.areaid != 13
                    and not exists(select 1 from dbo.xc_mkc xc with(nolock) where xc.mkc_id=k.id and isnull(xc.isLock,0)=1) and
                    exists(select 1 from dbo.areainfo a with(nolock) where k.areaid=a.id and isnull(a.xtenant,0) in (0,1) and a.ispass=1
                    <if test="config.areaTypes != null and config.areaTypes.size > 0">
                        and a.kind1 in
                        <foreach collection="config.areaTypes" index="index" item="areaType" separator="," open="(" close=")">
                            #{areaType}
                        </foreach>
                    </if>
                    )
                    <if test="config.cids != null and config.cids.size > 0">
                        and p.cid in
                        <foreach collection="config.cids" index="index" item="cid" separator="," open="(" close=")">
                            #{cid}
                        </foreach>
                    </if>
                    <if test="config.brandIds != null and config.brandIds.size > 0">
                        and p.brandID in
                        <foreach collection="config.brandIds" index="index" item="brandId" separator="," open="(" close=")">
                            #{brandId}
                        </foreach>
                    </if>
                    <if test="config.areaIds != null and config.areaIds.size > 0">
                        and k.areaid in
                        <foreach collection="config.areaIds" index="index" item="areaId" separator="," open="(" close=")">
                            #{areaId}
                        </foreach>
                    </if>
                    <if test="config.channelIds != null and config.channelIds.size > 0">
                        and k.insourceid2 in
                        <foreach collection="config.channelIds" index="index" item="channelId" separator="," open="(" close=")">
                            #{channelId}
                        </foreach>
                    </if>
                    <if test="config.productExcludeIds != null and config.productExcludeIds.size > 0">
                        and p.product_id not in
                        <foreach collection="config.productExcludeIds" index="index" item="productExcludeId" separator="," open="(" close=")">
                            #{productExcludeId}
                        </foreach>
                    </if>
                    <if test="config.stockAgeLimit != null">
                        <choose>
                            <when test="config.comparator == 1">
                                and datediff(DAY, k.ImeiDate, getdate()) > #{config.stockAgeLimit}
                            </when>
                            <when test="config.comparator == 2">
                                and datediff(DAY, k.ImeiDate, getdate()) = #{config.stockAgeLimit}
                            </when>
                            <when test="config.comparator == 3">
                                and datediff(DAY, k.ImeiDate, getdate()) <![CDATA[<]]> #{config.stockAgeLimit}
                            </when>
                            <otherwise>
                                and datediff(DAY, k.ImeiDate, getdate()) > #{config.stockAgeLimit}
                            </otherwise>
                        </choose>
                    </if>
                    order by isnull(transferPrice,inbeihuoprice) desc
                ) t
            </foreach>
        </if>
    </select>
    <select id="listByConfigNew" resultType="com.jiuji.oa.nc.stock.vo.StockDTO">
        select k.ppriceid,k.id mkcId,
               case
                   <if test="config.xtenant == null or config.xtenant &lt; 1000">
                        when k.staticPrice is not null then cast(k.staticPrice as money)
                   </if>
                    when k.transferPrice is not null then cast(k.transferPrice as money)
                    when k.inbeihuoprice is not null then k.inbeihuoprice
                    else 0
                end price
        from product_mkc k with(nolock)
        left join dbo.productinfo p with(nolock) on k.ppriceid = p.ppriceid
        where basket_id is null and isnull(k.mouldFlag,0)=0 and kc_check in (2,3,10) and k.areaid != 13
        and not exists(select 1 from dbo.xc_mkc xc with(nolock) where xc.mkc_id=k.id and isnull(xc.isLock,0)=1) and
        exists(select 1 from dbo.areainfo a with(nolock) where k.areaid=a.id and a.ispass=1
        <if test="config.areaTypes != null and config.areaTypes.size > 0">
            and a.kind1 in
            <foreach collection="config.areaTypes" index="index" item="areaType" separator="," open="(" close=")">
                #{areaType}
            </foreach>
        </if>
        <if test="config.authorizeId != null">
            and a.authorizeid = #{config.authorizeId}
        </if>
        <if test="config.xtenant != null">
            and a.xtenant = #{config.xtenant}
        </if>
        )
        <if test="config.cids != null and config.cids.size > 0">
            and p.cid in
            <foreach collection="config.cids" index="index" item="cid" separator="," open="(" close=")">
                #{cid}
            </foreach>
        </if>
        <if test="config.brandIds != null and config.brandIds.size > 0">
            and p.brandID in
            <foreach collection="config.brandIds" index="index" item="brandId" separator="," open="(" close=")">
                #{brandId}
            </foreach>
        </if>
        <if test="config.areaIds != null and config.areaIds.size > 0">
            and k.areaid in
            <foreach collection="config.areaIds" index="index" item="areaId" separator="," open="(" close=")">
                #{areaId}
            </foreach>
        </if>
        <if test="config.channelIds != null and config.channelIds.size > 0">
            and k.insourceid2 in
            <foreach collection="config.channelIds" index="index" item="channelId" separator="," open="(" close=")">
                #{channelId}
            </foreach>
        </if>
        <if test="config.productIds != null and config.productIds.size > 0">
            and p.product_id in
            <foreach collection="config.productIds" index="index" item="productId" separator="," open="(" close=")">
                #{productId}
            </foreach>
        </if>
        <if test="config.ppriceIds != null and config.ppriceIds.size > 0">
            and p.ppriceid in
            <foreach collection="config.ppriceIds" index="index" item="ppriceId" separator="," open="(" close=")">
                #{ppriceId}
            </foreach>
        </if>
        <if test="config.productExcludeIds != null and config.productExcludeIds.size > 0">
            and p.product_id not in
            <foreach collection="config.productExcludeIds" index="index" item="productExcludeId" separator="," open="(" close=")">
                #{productExcludeId}
            </foreach>
        </if>
        <if test="config.stockAgeLimit != null and config.comparator>0 and config.stockAgeLimit>0">
            <choose>
                <when test="config.comparator == 1">
                    and datediff(DAY, k.ImeiDate, getdate()) > #{config.stockAgeLimit}
                </when>
                <when test="config.comparator == 2">
                    and datediff(DAY, k.ImeiDate, getdate()) = #{config.stockAgeLimit}
                </when>
                <when test="config.comparator == 3">
                    and datediff(DAY, k.ImeiDate, getdate()) <![CDATA[<]]> #{config.stockAgeLimit}
                </when>
                <otherwise>
                    and datediff(DAY, k.ImeiDate, getdate()) > #{config.stockAgeLimit}
                </otherwise>
            </choose>
        </if>
    </select>
</mapper>
