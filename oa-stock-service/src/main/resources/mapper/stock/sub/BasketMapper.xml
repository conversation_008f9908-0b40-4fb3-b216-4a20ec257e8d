<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.sub.mapper.BasketMapper">

    <select id="selectSubSubCheckInfo" resultType="com.jiuji.oa.stock.sub.entity.SubSubCheckInfo">
        select b.basket_id,b.sub_id,s.sub_check
        from dbo.sub s with (nolock)
         left join dbo.basket b with (nolock) on b.sub_id = s.sub_id and isnull(b.isdel, 0) = 0
        where 1=1
        <if test="ids != null and ids.size() > 0">
            and b.basket_id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
     </select>
</mapper>
