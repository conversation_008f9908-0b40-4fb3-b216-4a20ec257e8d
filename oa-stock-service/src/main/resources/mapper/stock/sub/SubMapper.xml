<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.sub.mapper.SubMapper">

    <select id="getJdBuyerMobile" resultType="java.lang.String">
        SELECT
            buyer_mobile
        FROM
            jingdong_order jo with (nolock)
        where
            sub_id in (
            SELECT
            s.sub_id
            from
            sub s with (nolock)
            where
            s.sub_id in (
            SELECT
            w.danhaobind
            from
            wuliu w with (nolock)
            where
            w.wutype in (4, 6)
          and w.danhaobind = #{subId}
            )
          and s.subtype in (18)
            )
    </select>
    <select id="getMtBuyerMobile" resultType="java.lang.String">
        SELECT
            buyer_mobile
        FROM
            third_platform_order tpo with (nolock)
        where
            sub_id in (
            SELECT
            s.sub_id
            from
            sub s with (nolock)
            where
            s.sub_id in (
            SELECT
            w.danhaobind
            from
            wuliu w with (nolock)
            where
            w.wutype in (4, 6)
          and w.danhaobind = #{subId})
          and s.subtype in (19,32,38)
            )
    </select>
    <select id="getSubIdListByPpid" resultType="com.jiuji.oa.stock.develivery.vo.res.OrderOutStockPageRes">
        SELECT DISTINCT s.sub_id, '销售单' as subtype, '1' as subtypeId,
        case when s.islock = 1 then 1 else 0 end cancelFlag
        from sub s with(nolock)
        inner join basket b
        with (nolock)
        on b.sub_id = s.sub_id
        where s.sub_check = 1
          and delivery = 4
          and yingfuM = yifuM
          and ISNUll(b.isdel
            , 0) = 0
          and ppriceid = #{req.searchValue}
          and ISNUll(s.kcAreaid,s.areaid) = #{req.areaId}
    </select>
    <select id="getSubIdListByMkcId" resultType="com.jiuji.oa.stock.develivery.vo.res.OrderOutStockPageRes">
        SELECT DISTINCT s.sub_id, '销售单' as subtype, '1' as subtypeId,
        case when s.islock = 1 then 1 else 0 end cancelFlag
        from sub s with(nolock)
            inner join basket b
        with (nolock)
        on b.sub_id = s.sub_id
            inner join product_mkc pm
        with (nolock)
        on pm.basket_id = b.basket_id
        where s.sub_check = 1
          and delivery = 4
          and yingfuM = yifuM
          and ISNUll(b.isdel
            , 0) = 0
          and pm.id = #{req.searchValue}
          and ISNUll(s.kcAreaid,s.areaid) = #{req.areaId}
    </select>
    <select id="getSubIdListByImei" resultType="com.jiuji.oa.stock.develivery.vo.res.OrderOutStockPageRes">
        SELECT DISTINCT s.sub_id, '销售单' as subtype, '1' as subtypeId,
        case when s.islock = 1 then 1 else 0 end cancelFlag
        from sub s with(nolock)
            inner join basket b
        with (nolock)
        on b.sub_id = s.sub_id
            inner join product_mkc pm
        with (nolock)
        on pm.basket_id = b.basket_id
        where s.sub_check = 1
          and delivery = 4
          and yingfuM = yifuM
          and ISNUll(b.isdel
            , 0) = 0
          and pm.imei = #{req.searchValue}
          and ISNUll(s.kcAreaid,s.areaid) = #{req.areaId}
    </select>
    <select id="getSubIdListByDisplayCode"
            resultType="com.jiuji.oa.stock.develivery.vo.res.OrderOutStockPageRes">
        SELECT DISTINCT s.sub_id, '销售单' as subtype, '1' as subtypeId,
        case when s.islock = 1 then 1 else 0 end cancelFlag
        from sub s with(nolock)
        inner join basket b
        with (nolock)
        on b.sub_id = s.sub_id
        where s.sub_check = 1
          and delivery = 4
          and yingfuM = yifuM
          and ISNUll(b.isdel
            , 0) = 0
          and ISNUll(s.kcAreaid,s.areaid) = #{req.areaId}
          and EXISTS (SELECT 1 FROM displayProductInfo d with (nolock) where d.basket_id = b.basket_id
          and d.id = #{req.searchValue} )
    </select>
    
    <select id="getSubIdListByBarcode" resultType="com.jiuji.oa.stock.develivery.vo.res.OrderOutStockPageRes">
        SELECT DISTINCT s.sub_id, '销售单' as subtype, '1' as subtypeId,
        case when s.islock = 1 then 1 else 0 end cancelFlag
        from sub s with(nolock)
        inner join basket b
        with (nolock)
        on b.sub_id = s.sub_id
        inner join productBarcode pb with (nolock)
        on pb.ppriceid = b.ppriceid
        where s.sub_check = 1
          and delivery = 4
          and yingfuM = yifuM
          and ISNUll(b.isdel
            , 0) = 0
          and pb.barCode = #{req.searchValue}
          and ISNUll(s.kcAreaid,s.areaid) = #{req.areaId}
    </select>

</mapper>
