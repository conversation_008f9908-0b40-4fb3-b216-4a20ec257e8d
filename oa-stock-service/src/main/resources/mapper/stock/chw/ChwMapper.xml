<?xml version="1.0" encoding="UTF-8"?>

        <!--
          ~
          ~      Copyright © 2006 - 2020 九机网 All Rights Reserved
          ~
          ~
          -->

        <!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.stock.chw.mapper.ChwMapper">

    <!-- selectProductInfo -->
    <select id="selectProductInfo" resultType="com.jiuji.oa.stock.chw.dto.ProductSearchModelDTO">
        select ppriceid1,product_id,ppriceid,product_name,product_color,ismobile1,viewsWeek,viewsweekr,
               cid,brandID,isdel,display,que,barCode,vip2price,memberprice,rank1,isSn,sale_channel,costprice
        from productinfo with(nolock)
        where 1=1 and ppriceid NOT IN (89317,89305,88185)
    </select>

    <!-- selectCaigouSubBySubId -->
    <select id="selectCaigouSubBySubId" resultType="com.jiuji.oa.stock.chw.dto.CaigouSubDTO">
        SELECT *,isnull(stats,0) as stats_,
            (case when pay1 is NULL then 0 else 1 end) as ispay1,
            (case when pay2 is NULL then 0 else 1 end) as ispay2
        FROM caigou_sub with(nolock)
        WHERE 1=1 and id= #{subId}
    </select>

    <!-- getAreaListByCode -->
    <select id="getAreaListByCode" resultType="com.jiuji.oa.stock.chw.entity.AreaList">
        SELECT id, code, parent_code
        FROM AreaList with(nolock)
        WHERE code = #{code}
    </select>
    <select id="getSubInfo" resultType="com.jiuji.oa.stock.chw.dto.SubInfoDTO">
        select sub_id,sub_check FROM dbo.sub with (nolock ) where sub_id = #{subId}
    </select>

</mapper>
