<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.stock.chw.mapper.GoodsSalesMapper">

    <select id="getBasketCount" resultType="com.jiuji.oa.stock.chw.entity.Basket" >
        select temp.ppid as ppriceid,sum([count]) as basketCount  from (
             select s.ppriceid as ppid,sum(s.basket_count) as [count]
             from
                 productinfo p with(nolock)
                 left join sub_statistics s with(nolock) on
                 s.ppriceid = p.ppriceid
             where
                 ismobile=0 and s.tradeDate1 between DateAdd (month,  -1, CONVERT(nvarchar(11),dateadd(day,-1,CONVERT(nvarchar(11), getdate(), 23)))) and DateAdd (day,
        -1,
        CONVERT(nvarchar(11),
        getdate(),
        23))
        and sub_check in(2,3,6)
               and s.ppriceid in (
        <foreach item="item" index="index" collection="ppidList" separator=",">
            #{item}
        </foreach>
        )
               and (( s.jidianM > 0 and s.yingfuM > 0) or s.jidianM = 0)
               and areaid not in (13,22,14)
               and exists(select 1 from areainfo a with(nolock) where ispass=1 and a.id=s.areaid and a.kind1=1 )  group by s.ppriceid
             UNION ALL
             select p.ppriceid as ppid,0 as [count] from productinfo p  with(nolock) where p.ppriceid in (
        <foreach item="item" index="index" collection="ppidList" separator=",">
            #{item}
        </foreach>
        ) and p.ismobile1=0) temp GROUP BY temp.ppid;
    </select>
    <!-- getAreaListByCode -->
    <select id="getStock" resultType="com.jiuji.oa.stock.chw.entity.ProductKc">
        SELECT * FROM product_kc WITH (nolock)
        where areaid=16 and ppriceid in(
        <foreach item="item" index="index" collection="ppidList" separator=",">
            #{item}
        </foreach>
            )
    </select>
    <select id="getStockByPpidList" resultType="com.jiuji.oa.stock.supplier.vo.res.SalesStockVo">
        SELECT ppriceid,sum(leftCount) as stock FROM product_kc WITH (nolock)
        where areaid in (826,993,1146,1430) and ppriceid in(
        <foreach item="item" index="index" collection="ppidList" separator=",">
            #{item}
        </foreach>
        ) group by ppriceid
    </select>

</mapper>
