<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~
  ~      Copyright © 2006 - 2020 九机网 All Rights Reserved
  ~
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.stock.chw.mapper.ApplyPayInfoMapper">

    <!-- getApplyInfoByAdjustPiqianNum -->
    <select id="getApplyInfoByAdjustPiqianNum" resultType="com.jiuji.oa.stock.chw.dto.OfficeApplyInfoDTO">
        select Id,amount,QudaoChannelId
        from T_ApplyInfo with(nolock)
        where sCategoryId=101 and CurrentStatus in (2,4) and IsDel=0 and Id = #{adjustPiqianNum}
    </select>
    <select id="getApplyPayInfoBycaigouId" resultType="java.lang.Integer">
        select  1  from  applyPayInfo with (nolock )  where  ','+caigouid +',' like '%,${caigouId},%'
    </select>


</mapper>
