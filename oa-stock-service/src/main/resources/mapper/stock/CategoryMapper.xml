<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.nc.stock.mapper.CategoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.nc.stock.entity.Category">
        <id column="ID" property="id" />
        <result column="ParentID" property="parentID" />
        <result column="Name" property="name" />
        <result column="Level" property="level" />
        <result column="Child" property="child" />
        <result column="Rank" property="rank" />
        <result column="Path" property="path" />
        <result column="Display" property="display" />
        <result column="IsMobile" property="isMobile" />
        <result column="IsShow" property="isShow" />
        <result column="isVirtualGoods" property="isVirtualGoods" />
        <result column="isOutputCategory" property="isOutputCategory" />
    </resultMap>
    <select id="selectChildrenCategory" resultType="java.lang.Integer">
        select * from dbo.f_category_children (#{id})
    </select>


</mapper>
