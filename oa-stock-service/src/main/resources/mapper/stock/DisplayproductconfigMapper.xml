<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.nc.stock.mapper.DisplayProductConfigMapper">

    <select id="getPageList" resultType="com.jiuji.oa.nc.stock.vo.res.DisplayPageRes">
        select t.* from (
        select
        dpc.id,
        dpc.ppid ,
        p.product_name ,
        p.product_color ,
        ISNULL((SELECT SUM(d.count_) FROM displayProductInfo d with(nolock) WHERE d.ppriceid = dpc.ppid
        and d.curAreaId !=203
        and d.stats_=1 and isnull(d.isFlaw,0)=0 GROUP BY d.ppriceid),0) as displayCount,
        ISNULL((SELECT SUM(d.count_) FROM displayProductInfo d with(nolock) WHERE d.ppriceid = dpc.ppid
        and d.curAreaId !=203
        and d.stats_=1 and isnull(d.isFlaw,0)=1 GROUP BY d.ppriceid),0) as xcCount,
        dpc.callback_type,
        dpc.inTime,
        dpc.inuser
        from
        displayProductConfig dpc with (nolock)
        left join productinfo p with(nolock) on p.ppriceid = dpc.ppid
        <where>
            dpc.ppid is not NULL and dpc.callback_type is not NULL and dpc.isdel = 0
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==1">
                and p.product_name like '%' + #{req.searchValue} + '%'
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==2">
                and p.ppriceid in (SELECT * FROM dbo.F_SPLIT(#{req.searchValue},',') f )
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==3">
                and p.productid in (SELECT * FROM dbo.F_SPLIT(#{req.searchValue},',') f )
            </if>
            <if test="req.callbackType != null and req.callbackType !=''">
                AND dpc.callback_type = #{req.callbackType}
            </if>
        </where>
        ) t
        <where>
            <if test="req.displayCountSearch != null and req.displayCountSearch == 1">
                AND t.displayCount = 0
            </if>
            <if test="req.displayCountSearch != null and req.displayCountSearch == 2">
                AND t.displayCount != 0
            </if>
            <if test="req.xcCountSearch != null and req.xcCountSearch == 1">
                AND t.xcCount = 0
            </if>
            <if test="req.xcCountSearch != null and req.xcCountSearch == 2">
                AND t.xcCount != 0
            </if>
        </where>
    </select>
</mapper>
