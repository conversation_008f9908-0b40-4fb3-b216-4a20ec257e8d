<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.stockage.mapper.StockVerificationMapper">


    <!-- getSmallStock -->
    <select id="getSmallStock" resultType="com.jiuji.oa.stock.stockage.vo.res.StockCountVO">
        SELECT top 1
    kc.ppriceid,
               a.area    as store,
               leftCount as stock
        FROM dbo.product_kc kc with(nolock),
     areainfo a
        with (nolock)
        where kc.areaid = a.id
          and EXISTS (
            SELECT 1
            FROM dbo.productinfo info with (nolock)
            where info.ismobile1=0
          and info.cidfamily like #{cidfamily}
          and info.ppriceid = kc.ppriceid
            )
          and leftCount
            >0
    </select>

    <!-- getLargeStock -->
    <select id="getLargeStock" resultType="com.jiuji.oa.stock.stockage.vo.res.StockCountVO">
        SELECT top 1 mkc.ppriceid ,a.area as store, kc_check as stock
        FROM dbo.product_mkc mkc with(nolock),
     areainfo a
        with (nolock)
        where mkc.areaid = a.id
          and kc_check in (0
            , 1
            , 2
            , 3
            , 7
            , 8
            , 9
            , 10
            , 14)
          and EXISTS (
            SELECT 1
            FROM dbo.productinfo info with (nolock)
            where info.ismobile1 = 1
          and info.cidfamily like #{cidfamily}
          and info.ppriceid = mkc.ppriceid
            )
    </select>

    <!-- getLargeAreaStock -->
    <select id="getLargeAreaStock" resultType="com.jiuji.oa.stock.stockage.entity.StockArea">
        SELECT a.city_name as city, a.area as store, kc_check as status
        FROM dbo.product_mkc mkc with(nolock),
            areainfo a
        with (nolock)
        where
            mkc.areaid = a.id
          and mkc.id = #{kcId}
        group by a.city_name, a.area, kc_check
    </select>

    <select id="getId" resultType="Integer">
        SELECT id
        FROM dbo.product_mkc mkc with(nolock)
        where mkc.id = #{id}
    </select>

    <select id="getAreaStockList" resultType="com.jiuji.oa.stock.stockage.bo.StockAreaBO">
        SELECT mkc.id as id, a.city_name + '仓库-' + a.area as stockWare ,kc_check as status
        FROM
        dbo.product_mkc mkc with(nolock),
        areainfo a with(nolock)
        where
        mkc.areaid = a.id
        and mkc.id in
        <foreach collection="kcList" open="(" close=")" separator="," item="kcId">
            #{kcId}
        </foreach>
    </select>

    <select id="getAreaStockByDisplayId" resultType="com.jiuji.oa.stock.stockage.bo.StockAreaBO">
        SELECT
        d.id as id,
        a.city_name + '仓库-' + a.area as stockWare ,
        case
        when stats_ = 1 then 3
        when stats_ = 3 then 5
        else 0
        end status
        FROM
        displayProductInfo d with(nolock),
        areainfo a with(nolock)
        where
        d.areaid = a.id
        and d.stats_ in (1, 3)
        and d.id in
        <foreach collection="kcList" open="(" close=")" separator="," item="kcId">
            #{kcId}
        </foreach>
    </select>

    <select id="getLargeStockExist" resultType="com.jiuji.oa.stock.stockage.bo.PpidStockAmount">
        SELECT pm.ppriceid,COUNT(1) AS stockAmount FROM product_mkc pm
        <where>kc_check not in (4,5,11)
            and pm.ppriceid in
            <foreach collection="ppidList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </where>
        group by pm.ppriceid
    </select>

    <select id="countAccessorsKcByPpid" resultType="com.jiuji.oa.stock.stockage.bo.PpidStockAmount">
        SELECT pk.ppriceid ,SUM(pk.lcount) AS stockAmount FROM product_kc pk
        <where>pk.ppriceid in
            <foreach collection="ppidList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </where>
        GROUP by pk.ppriceid
    </select>


    <select id="countAccessorsLoseByPpid" resultType="com.jiuji.oa.stock.stockage.bo.PpidStockAmount">
        select rb.ppriceid,SUM(rb.lcount) AS stockAmount from return_basket rb with(nolock)
        left join return_sub rs on rs.id = rb.sub_id
        <where>rs.states not in (3,4)
            and rb.ppriceid in
            <foreach collection="ppidList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </where>
        GROUP by rb.ppriceid
    </select>

    <select id="countAccessorsDiaoboByPpid" resultType="com.jiuji.oa.stock.stockage.bo.PpidStockAmount">
        select db.ppriceid,SUM(db.lcount) AS stockAmount from diaobo_basket db with(nolock)
        left join diaobo_sub ds on ds.id = db.sub_id
        <where>ds.stats not in (0,4)
            and db.ppriceid in
            <foreach collection="ppidList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </where>
        GROUP by db.ppriceid
    </select>

    <select id="countAccessorsCaigouByPpid" resultType="com.jiuji.oa.stock.stockage.bo.PpidStockAmount">
        select cb.ppriceid ,SUM(cb.lcount) AS stockAmount from caigou_basket cb with(nolock)
        left join caigou_sub cs on cs.id = cb.sub_id
        <where>cs.stats not in (-1,3)
            and cb.ppriceid in
            <foreach collection="ppidList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </where>
        GROUP by cb.ppriceid
    </select>


    <select id="countSmallRefundByPpid" resultType="com.jiuji.oa.stock.stockage.bo.PpidStockAmount">
        select sp.changePpriceid as ppriceid,COUNT(1) AS stockAmount from smallpro sp with(nolock)
        <where>sp.changePpriceid in
            <foreach collection="ppidList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
            and ((sp.stats=0 and isnull(sp.wxState,0) = 0 and isnull(sp.isdel,0) = 0 and sp.stats!=2)
            or (sp.stats=3)
            or (isnull(wxState,0) = 1 and isnull(stats,0) != 1 and isnull(sp.isdel,0) = 0 and stats!=2)
            or (isnull(wxState,0) = 2 and isnull(stats,0) != 1 and isnull(sp.isdel,0) = 0 and stats!=2)
            )
            and isnull(sp.isdel,0) = 0
            and stats!=2
        </where>
        group by changePpriceid
    </select>

    <select id="countRepairSearchByPpid" resultType="com.jiuji.oa.stock.stockage.bo.PpidStockAmount">
        SELECT
        h.ppriceid as ppriceid, COUNT(1) AS stockAmount
        FROM
        shouhou h with(nolock)
        LEFT JOIN
        (
        SELECT
        tuihuan_kind,
        shouhou_id,
        check3
        FROM
        shouhou_tuihuan with(nolock)
        WHERE
        ISNULL( isdel, 0 ) = 0
        AND tuihuan_kind IN (
        1, 2, 3, 4, 5
        )
        ) t
        ON t.shouhou_id= h.id
        LEFT JOIN
        (
        SELECT
        DISTINCT shouhou_id
        FROM
        shouhou_huishou with(nolock)
        ) AS hs
        ON hs.shouhou_id= h.id
        LEFT JOIN
        BBSXP_Users u with(nolock)
        ON u.id= h.userid
        <where>
            h.ppriceid in
            <foreach collection="ppidList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
            AND isnull( h.isquji, 0 ) = 0
            and h.xianshi=1
        </where>
        group by h.ppriceid
    </select>

</mapper>
