<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.stockage.mapper.StockAgeMapper">

    <!-- 库龄查询映射结果 -->
    <resultMap id="StockAgeResultMap" type="com.jiuji.oa.stock.stockage.dto.StockAgeDTO">
        <id column="id" property="mkcId" />
        <result column="ppriceid" property="ppid" />
        <result column="imei" property="imei" />
        <result column="imeidate" property="imeiDate" />
        <result column="kc_check" property="kcCheck" />
        <result column="areaid" property="areaId" />
        <result column="stock_age" property="stockAge" />
    </resultMap>

    <!-- getMaxStockAge -->
    <select id="getMaxStockAge" resultMap="StockAgeResultMap">
        SELECT top 1
               id, ppriceid, imeidate, imei, kc_check, basket_id, areaid,
               DATEDIFF(day, imeidate, GETDATE()) AS stock_age
        FROM product_mkc with(nolock)
        WHERE kc_check = #{kcCheck} AND basket_id IS NULL AND imeidate IS NOT NULL AND areaid = #{areaId}
        ORDER BY imeidate
    </select>

    <!-- getStockAgeByMkcId -->
    <select id="getStockAgeByMkcId" resultType="int">
       SELECT  DATEDIFF(day, imeidate, GETDATE())
       FROM product_mkc with(nolock)
       WHERE id = #{mkcId}
    </select>

</mapper>
