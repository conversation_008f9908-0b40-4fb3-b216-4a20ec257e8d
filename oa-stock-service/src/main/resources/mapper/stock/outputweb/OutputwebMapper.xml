<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.stock.outputweb.mapper.OutputwebMapper">

    <resultMap id="outputwebMap" type="com.jiuji.oa.stock.outputweb.entity.Outputweb">
        <id property="id" column="id"/>
        <result property="web" column="web"/>
        <result property="webname" column="webName"/>
        <result property="isdelete" column="isDelete"/>
        <result property="rank" column="rank"/>
        <result property="moaurl" column="moaUrl"/>
        <result property="namepy" column="namePY"/>
        <result property="showkind" column="showKind"/>
        <result property="webkind" column="webKind"/>
        <result property="tokenurl" column="tokenUrl"/>
        <result property="namespaceid" column="nameSpaceId"/>
        <result property="xtencen" column="xtencen"/>
        <result property="pcweb" column="pcWeb"/>
        <result property="xtenant" column="xtenant"/>
        <result property="issystemdocking" column="isSystemDocking"/>
        <result property="userid" column="userId"/>
        <result property="areaconfig" column="areaConfig"/>
    </resultMap>

    <select id="getIdByWebName" resultType="java.util.HashMap">
        SELECT b.xtenant as value, b.webName as label
        FROM OutPutWeb b with (nolock)
    </select>


    <select id="getIdByWebNameForRes" resultType="com.jiuji.oa.stock.outputweb.vo.KeyValueRes"
            useCache="true" flushCache="false">
        SELECT b.xtenant as value, b.webName as label
        FROM OutPutWeb b with (nolock)
        <where>
            <if test="name != null and name != ''">
                b.webName like CONCAT('%',#{name},'%')
            </if>
        </where>
    </select>
    <select id="getUserIdByxTenant" resultType="java.lang.Long">
        SELECT top 1 userId
        from OutPutWeb with(nolock)
        <where>
            and xtenant = #{xtenant}
        </where>
    </select>
    <select id="getWebKindByxTenant" resultType="java.lang.Integer">
        SELECT top 1 webKind
        from OutPutWeb with (nolock)
        <where>
            and xtenant = #{xtenant}
        </where>
    </select>

    <select id="getOutPutWebByXtenant" resultType="com.jiuji.oa.stock.outputweb.entity.Outputweb">
        SELECT *
        FROM OutPutWeb with(nolock)
        where xtenant = #{xtenant}
    </select>
</mapper>
