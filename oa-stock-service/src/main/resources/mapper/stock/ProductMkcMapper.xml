<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.nc.stock.mapper.ProductMkcMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.nc.stock.entity.ProductMkc">
        <id column="id" property="id"/>
        <result column="ppriceid" property="ppriceid"/>
        <result column="orderid" property="orderid"/>
        <result column="inbeihuo" property="inbeihuo"/>
        <result column="dtime" property="dtime"/>
        <result column="inbeihuodate" property="inbeihuodate"/>
        <result column="inbeihuoprice" property="inbeihuoprice"/>
        <result column="imeidate" property="imeidate"/>
        <result column="imei" property="imei"/>
        <result column="inuser" property="inuser"/>
        <result column="imeiuser" property="imeiuser"/>
        <result column="kc_check" property="kcCheck"/>
        <result column="area" property="area"/>
        <result column="frarea" property="frarea"/>
        <result column="basket_id" property="basketId"/>
        <result column="inprice" property="inprice"/>
        <result column="origarea" property="origarea"/>
        <result column="caigoulock" property="caigoulock"/>
        <result column="pandian" property="pandian"/>
        <result column="pandiandate" property="pandiandate"/>
        <result column="cangkuid" property="cangkuid"/>
        <result column="rank" property="rank"/>
        <result column="insourceid" property="insourceid"/>
        <result column="insourceid2" property="insourceid2"/>
        <result column="ischu1" property="ischu1"/>
        <result column="useWay" property="useWay"/>
        <result column="takeBatch" property="takeBatch"/>
        <result column="beihuoUser" property="beihuoUser"/>
        <result column="areaid" property="areaid"/>
        <result column="origareaid" property="origareaid"/>
        <result column="frareaid" property="frareaid"/>
        <result column="pandianuser" property="pandianuser"/>
        <result column="lockDtime" property="lockDtime"/>
        <result column="pzid" property="pzid"/>
        <result column="isTax" property="isTax"/>
        <result column="fanli" property="fanli"/>
        <result column="protectPrice" property="protectPrice"/>
        <result column="modifyPrice" property="modifyPrice"/>
        <result column="staticPrice" property="staticPrice"/>
        <result column="fanlilock" property="fanlilock"/>
        <result column="fanlilockDtime" property="fanlilockDtime"/>
        <result column="fanlilockPzid" property="fanlilockPzid"/>
        <result column="rewardFlag" property="rewardFlag"/>
        <result column="mouldFlag" property="mouldFlag"/>
        <result column="transferPrice" property="transferPrice"/>
        <result column="pandianException" property="pandianException"/>
        <result column="inPzid" property="inPzid"/>
    </resultMap>
    <update id="lockProductTransferKc">
        update dbo.product_mkc set basket_id = -2  where basket_id is null and id = #{mkcId} and kc_check =3
     </update>
    <update id="unlockProductTransferKc">
        update dbo.product_mkc set basket_id = null  where basket_id in (-1,-2) and id = #{mkcId} and kc_check =3
    </update>
    <select id="getComment" resultType="com.jiuji.oa.nc.stock.vo.res.ProductMkcCommnetDTO">
        SELECT pm.id as mkcId,
        pm.ppriceid as ppriceid,
        i.name as name,
        owq.company_jc as companyJc,
        pm.insourceid2 as inSourceId2,
        pm.inbeihuodate as inBeiHuoDate,
        pm.fanli as fanli,
        pm.protectPrice as protectPrice,
        pm.modifyPrice as modifyPrice,
        pm.inbeihuoprice as inBeiHuoPrice,
        pm.staticPrice as staticPrice,
        pm.imeidate as imeidate,
        pm.pandiandate as pandiandate,
        pm.pandianuser as pandianUser,
        pm.imeiuser as imeiUser
        <if test="xTenantId != 0 and xTenantId != null">
            ,pm.examPrice as examPrice
        </if>
        FROM product_mkc pm with(nolock)
        LEFT JOIN Ok3w_qudao owq with(nolock) ON
        pm.insourceid2 = owq.id
        LEFT JOIN insource i with(nolock) ON
        i.id = pm.insourceid
        WHERE pm.id = #{mkcId}
    </select>
    <select id="findInboundQueryByPpid" resultType="com.jiuji.oa.stock.inboundquery.vo.ShowInboundQueryVO">
       select k.areaid,
               p.product_name,
               p.product_color,
               k.ppriceid,
               p.cidFamily,
               p.barCode,
               p.pLabel,
               p.saleStartTime,
               1               rn,
               COUNT(*)     AS lcount,
               pl.PlaceCode AS number,
               case when p.cid in (2,615) then 1 else 0 end as produtCategory
        from dbo.product_mkc k with (nolock)
         LEFT join dbo.productinfo p with (nolock) on p.ppriceid = k.ppriceid
            LEFT JOIN dbo.PlaceLabel pl with(nolock) ON pl.PPID = k.ppriceid
        WHERE 1 = 1
          and k.ppriceid= #{selectInboundQueryVO.ppriceIdOrBarcode}
          and k.areaid = #{selectInboundQueryVO.areaId}
          AND k.kc_check = 3
        GROUP BY k.areaid, p.product_name, p.product_color, k.ppriceid, p.cidFamily, p.barCode, p.pLabel, pl.PlaceCode, p.cid
        ,p.saleStartTime
   </select>

    <select id="findInboundQueryByCode" resultType="com.jiuji.oa.stock.inboundquery.vo.ShowInboundQueryVO">
        select *
        from (select k.areaid,
                     p.product_name,
                     p.product_color,
                     k.ppriceid,
                     p.cidFamily,
                     p.barCode,
                     p.pLabel,
                     1               rn,
                     COUNT(*)     AS lcount,
                     pl.PlaceCode AS number
              from dbo.product_mkc k with (nolock)
               LEFT join dbo.productinfo p with (nolock) on p.ppriceid = k.ppriceid
                  LEFT JOIN dbo.PlaceLabel pl with(nolock) ON pl.PPID = k.ppriceid
              WHERE 1 = 1
                and k.ppriceid = #{selectInboundQueryVO.ppriceIdOrBarcode}
                and k.areaid = #{selectInboundQueryVO.areaId}
                AND k.kc_check = 3
              GROUP BY k.areaid, p.product_name, p.product_color, k.ppriceid, p.cidFamily, p.barCode, p.pLabel, pl.PlaceCode
              UNION all
              select k.areaid,
                  p.product_name,
                  p.product_color,
                  k.ppriceid,
                  p.cidFamily,
                  p.barCode,
                  p.pLabel,
                  2               rn,
                  COUNT(*)     AS lcount,
                  pl.PlaceCode AS number
              from dbo.product_mkc k with (nolock)
                  LEFT join dbo.productinfo p with (nolock) on p.ppriceid = k.ppriceid
                  LEFT JOIN dbo.PlaceLabel pl with(nolock) ON pl.PPID = k.ppriceid
              where 1 = 1
                and p.barCode like '%' + #{selectInboundQueryVO.ppriceIdOrBarcode} + '%'
                and k.areaid = #{selectInboundQueryVO.areaId}
              GROUP BY k.areaid, p.product_name, p.product_color, k.ppriceid, p.cidFamily, p.barCode, p.pLabel, pl.PlaceCode
             ) aa
        order by rn
  </select>
    <select id="findInboundQueryByPpidRemoveArea" resultType="com.jiuji.oa.stock.inboundquery.vo.ShowInboundQueryVO">
        select product_name, product_color, ppriceid, cidFamily, barCode, pLabel, 1 rn, saleStartTime,
            case when cid in (2,615) then 1 else 0 end as produtCategory
        from dbo.productinfo with (nolock)
        where ismobile1 = 1
          and ppriceid = #{ppriceIdOrBarcode}
        order by rn
    </select>
    <select id="findInboundQueryByCodeRemoveArea" resultType="com.jiuji.oa.stock.inboundquery.vo.ShowInboundQueryVO">
        select product_name, product_color, ppriceid, cidFamily, barCode, pLabel
        from dbo.productinfo with (nolock)
        where ismobile1 = 1
          and barCode like '%' + #{ppriceIdOrBarcode} + '%'
    </select>
  <sql id="smallAndRepairSql">
    from kcSnaps kc with (nolock)
    left join productinfo p with (nolock) on kc.ppriceid = p.ppriceid
    <if test="req.cids !=  null and req.cids != ''">
      right join (SELECT id from f_category_children(#{req.cids})) f
      on p.cid= f.id
    </if>
    left join areainfo a with (nolock) on kc.areaid = a.id
    left join brand bd with (nolock) on bd.id = p.brandID
    left join Ok3w_qudao ok with (nolock) on ok.id = kc.insourceid2
    left join category ca with (nolock) on p.cid= ca.id
    where kc.dtime = #{req.date}
    <if test="req.stockQueryEnum != null">
        <if test="req.stockQueryEnum.code == 0">
            and p.ismobile1 = 1
        </if>
        <if test="req.stockQueryEnum.code == 1">
            and p.ismobile1 = 0
        </if>
        <if test="req.stockQueryEnum.code == 2">
            and exists( select 1 from f_category_children(23) f where f.id=p.cid )
        </if>
    </if>

      <if test="req.areaLevelEnum != null and req.areaLevelEnum.code >= 0">
          AND exists (select 1 from dbo.areainfo ar with(nolock) where kc.areaid = ar.id and ar.level1=
          (#{req.areaLevelEnum.code} + 1))
      </if>
      <if test="req.stockQueryByEnum != null and req.mainkey != null and req.mainkey != ''">
          <if test="req.stockQueryByEnum.code == 0">
              and kc.ppriceid =#{req.mainkey}
          </if>
          <if test="req.stockQueryByEnum.code == 1">
              and p.productid =#{req.mainkey}
          </if>
          <if test="req.stockQueryByEnum.code == 2">
              and p.product_name like CONCAT('%',#{req.mainkey},'%')
          </if>
      </if>
      <if test="req.areaIds != null and req.areaIds.size > 0 ">
          and kc.areaid in
          <foreach collection="req.areaIds" item="areaId" open="(" separator="," close=")">
              #{areaId}
          </foreach>
      </if>

  </sql>
  <sql id="sonGroupBy">
    GROUP BY kc.areaid ,
        a.area,
        ca.name,
        kc.ppriceid ,
        p.product_name ,
        p.product_color ,
        p.memberprice,
        ok.company,
        p.viewsWeek ,
        p.cidFamily,
        p.viewsweekr ,
        bd.name
  </sql>
  <select id="aggregateCountSmallAndRepair" resultType="java.lang.Integer">
    SELECT COUNT(1) from (
    SELECT kc.areaid
    <include refid="smallAndRepairSql"/>
    <include refid="sonGroupBy"/>
    ) x
  </select>
    <select id="findInboundQueryByPpidV2" resultType="com.jiuji.oa.stock.inboundquery.vo.Unbound">
        SELECT
        k.id as mkcId,k.insourceid2 as insourceId,k.insourceid as channelType,k.orderid as orderId,k.ppriceid as ppriceId,k.imei,k.imei2,k.areaid as areaId,
        p.product_name as productName,p.product_color as productColor,
        p.barCode as barCode,
        p.pLabel as pLabel,
        pl.PlaceCode AS number,
        tpo.order_id as tlOrderId,
        tpo.plat_code as platCode
        FROM product_mkc k WITH(NOLOCK)
        LEFT JOIN dbo.mkcCaiGouBasket m with (nolock) ON m.mkc_id = k.id
        LEFT JOIN mkc_toarea mt WITH(NOLOCK) ON mt.mkc_id = k.id
        LEFT join dbo.productinfo p with (nolock) on p.ppriceid = k.ppriceid
        LEFT JOIN dbo.PlaceLabel pl with(nolock) ON pl.PPID = k.ppriceid
        LEFT JOIN third_platform_purchase_order tpo WITH(NOLOCK) on m.sub_id= tpo.mkc_caigou_id and isnull(tpo.is_del,0) = 0
        <where> mt.mkc_id IS NULL AND k.imei IS NULL AND k.imei2 IS NULL AND k.kc_check in (2,3,10) AND k.areaid = #{req.areaId}
            <if test="req.channelId != null and req.channelId !=''">
            AND k.insourceid2 = #{req.channelId}
            </if>
            <if test="req.channelType != null and req.channelType !=''">
                AND k.insourceid = #{req.channelType}
            </if>
            <if test="req.ppriceId != null and req.ppriceId != ''">
                AND k.ppriceid = #{req.ppriceId}
            </if>
            <if test="req.subId != null and req.subId != ''">
                AND m.sub_id = #{req.subId}
            </if>
            <if test="req.type != null and req.type > 0 ">
                AND tpo.id is not null and tpo.plat_code = #{req.platCode}
            </if>
            <if test="req.type != null and req.type == 0 ">
                AND tpo.id is null
            </if>
            <if test="req.ppidList != null and req.ppidList.size() > 0 ">
                AND k.ppriceid in
                <foreach collection="req.ppidList" item="ppid" open="(" separator="," close=")">
                    #{ppid}
                </foreach>
            </if>
        </where>
        UNION
        SELECT
        k.id,k.insourceid2,k.insourceid,k.orderid,k.ppriceid,k.imei,k.imei2,mt.areaid,
        p.product_name,p.product_color,
        p.barCode,
        p.pLabel,
        pl.PlaceCode AS number,
        tpo.order_id as tlOrderId,
        tpo.plat_code as platCode
        FROM product_mkc k WITH(NOLOCK)
        LEFT JOIN dbo.mkcCaiGouBasket m with (nolock) ON m.mkc_id = k.id
        LEFT JOIN mkc_toarea mt WITH(NOLOCK) on mt.mkc_id = k.id
        LEFT join dbo.productinfo p with (nolock) on p.ppriceid = k.ppriceid
        LEFT JOIN dbo.PlaceLabel pl with(nolock) ON pl.PPID = k.ppriceid
        LEFT JOIN third_platform_purchase_order tpo WITH(NOLOCK) on m.sub_id= tpo.mkc_caigou_id and isnull(tpo.is_del,0) = 0
        <where> mt.mkc_id IS NOT NULL AND k.imei IS NULL AND k.imei2 IS NULL AND k.kc_check = 10 AND mt.areaid = #{req.areaId}
            <if test="req.channelId != null and req.channelId !=''">
                AND k.insourceid2 = #{req.channelId}
            </if>
            <if test="req.channelType != null and req.channelType !=''">
                AND k.insourceid = #{req.channelType}
            </if>
            <if test="req.ppriceId != null and req.ppriceId != ''">
                AND k.ppriceid = #{req.ppriceId}
            </if>
            <if test="req.subId != null and req.subId != ''">
                AND m.sub_id = #{req.subId}
            </if>
            <if test="req.type != null and req.type > 0 ">
                AND tpo.id is not null and tpo.plat_code = #{req.platCode}
            </if>
            <if test="req.type != null and req.type == 0 ">
                AND tpo.id is null
            </if>
            <if test="req.ppidList != null and req.ppidList.size() > 0 ">
                AND k.ppriceid in
                <foreach collection="req.ppidList" item="ppid" open="(" separator="," close=")">
                    #{ppid}
                </foreach>
            </if>
        </where>
        UNION
        SELECT
        k.id as mkcId,k.insourceid2 as insourceId,k.insourceid as channelType,k.orderid as orderId,k.ppriceid as ppriceId,k.imei,k.imei2,k.areaid as areaId,
        p.product_name as productName,p.product_color as productColor,
        p.barCode as barCode,
        p.pLabel as pLabel,
        pl.PlaceCode AS number,
        tpo.order_id as tlOrderId,
        tpo.plat_code as platCode
        FROM product_mkc k WITH(NOLOCK)
        LEFT join dbo.productinfo p with (nolock) on p.ppriceid = k.ppriceid
        LEFT JOIN dbo.PlaceLabel pl with(nolock) ON pl.PPID = k.ppriceid
        LEFT JOIN dbo.mkcCaiGouBasket m with (nolock) ON m.mkc_id = k.id
        LEFT JOIN third_platform_purchase_order tpo WITH(NOLOCK) on m.sub_id= tpo.mkc_caigou_id and isnull(tpo.is_del,0) = 0
        <where>  k.imei IS NULL AND k.imei2 IS NULL AND k.kc_check in(2,3)  AND k.areaid = #{req.areaId}
            <if test="req.channelId != null and req.channelId !=''">
                AND k.insourceid2 = #{req.channelId}
            </if>
            <if test="req.channelType != null and req.channelType !=''">
                AND k.insourceid = #{req.channelType}
            </if>
            <if test="req.ppriceId != null and req.ppriceId != ''">
                AND k.ppriceid = #{req.ppriceId}
            </if>
            <if test="req.type != null and req.type > 0 ">
                AND tpo.id is not null and tpo.plat_code = #{req.platCode}
            </if>
            <if test="req.type != null and req.type == 0 ">
                AND tpo.id is null
            </if>
            <if test="req.ppidList != null and req.ppidList.size() > 0 ">
                AND k.ppriceid in
                <foreach collection="req.ppidList" item="ppid" open="(" separator="," close=")">
                    #{ppid}
                </foreach>
            </if>
        </where>
    </select>
    <select id="jiujiFindInboundQueryByPpidV2" resultType="com.jiuji.oa.stock.inboundquery.vo.Unbound">
        SELECT
        k.id as mkcId,k.insourceid2 as insourceId,k.insourceid as channelType,k.orderid as orderId,k.ppriceid as ppriceId,k.imei,k.imei2,k.areaid as areaId,
        p.product_name as productName,p.product_color as productColor,
        p.barCode as barCode,
        p.pLabel as pLabel,
        pl.PlaceCode AS number,
        tpo.order_id as tlOrderId,
        tpo.plat_code as platCode
        FROM product_mkc k WITH(NOLOCK)
        LEFT JOIN dbo.mkcCaiGouBasket m with (nolock) ON m.mkc_id = k.id
        LEFT JOIN mkc_toarea mt WITH(NOLOCK) ON mt.mkc_id = k.id
        LEFT join dbo.productinfo p with (nolock) on p.ppriceid = k.ppriceid
        LEFT JOIN dbo.PlaceLabel pl with(nolock) ON pl.PPID = k.ppriceid
        LEFT JOIN dbo.Ok3w_qudao o WITH(NOLOCK)  on o.id=k.insourceid2
        LEFT JOIN dbo.mkcCaiGouSub mcgs WITH(NOLOCK)  on mcgs.id= m.sub_id
        LEFT JOIN dbo.areainfo a WITH(NOLOCK)  on a.id= k.areaid
        LEFT JOIN apple_purchase_store_inventory aps WITH(NOLOCK) on aps.mkc_id= k.id and isnull(aps.is_del,0) = 0
        LEFT JOIN third_platform_purchase_order tpo WITH(NOLOCK) on m.sub_id= tpo.mkc_caigou_id and isnull(tpo.is_del,0) = 0
        WHERE k.imei IS NULL AND k.kc_check in (2,3,10)
        and aps.id is null
        and  ((((mt.id is null and k.areaid =#{req.areaId} )  or  (mt.id is not null and  mt.areaid = #{req.areaId})) AND ISNULL(o.seltPurchase,0)=1 AND ISNULL(mcgs.purchase_area_id,0)=0)
        OR(ISNULL(mcgs.purchase_area_id,0)>0 AND ISNULL(mcgs.purchase_area_id,0)= #{req.areaId})
        OR(ISNULL(o.seltPurchase,0)=0 AND a.pid=53 AND #{req.areaId}=16 AND ISNULL(mcgs.purchase_area_id,0)=0)
        OR(ISNULL(o.seltPurchase,0)=0 AND a.pid=52 AND #{req.areaId}=113) AND ISNULL(mcgs.purchase_area_id,0)=0)
        <if test="req.channelId != null and req.channelId !=''">
            AND k.insourceid2 = #{req.channelId}
        </if>
        <if test="req.channelType != null and req.channelType !=''">
            AND k.insourceid = #{req.channelType}
        </if>
        <if test="req.ppriceId != null and req.ppriceId != ''">
            AND k.ppriceid = #{req.ppriceId}
        </if>
        <if test="req.subId != null and req.subId != ''">
            AND m.sub_id = #{req.subId}
        </if>
        <if test="req.type != null and req.type > 0 ">
            AND tpo.id is not null and tpo.plat_code = #{req.platCode}
        </if>
        <if test="req.type != null and req.type == 0 ">
            AND tpo.id is null
        </if>
        <if test="req.ppidList != null and req.ppidList.size() > 0 ">
            AND k.ppriceid in
            <foreach collection="req.ppidList" item="ppid" open="(" separator="," close=")">
                #{ppid}
            </foreach>
        </if>
    </select>
    <select id="getStockHQSmallAndRepair" resultType="com.jiuji.oa.stock.productkc.vo.res.AggregatedStockVO">
    select * from (
    SELECT kc.areaid areaId,
    a.area areaName,
    ca.Name as categoryName,
    kc.ppriceid ppid,
    p.product_name pName,
    p.product_color specs,
    p.viewsWeek ,
    p.viewsweekr ,
    p.cidFamily,
    p.memberprice memberPrice,
    bd.name brand,
    isnull(ok.company, #{req.vendor}) vendor,
    sum(kc.lossCount) lossCount ,
    sum(kc.lossPrices)  lossPrices,
    sum(kc.shouhouCount)  shouhouCount,
    sum(kc.shouhouPrices)  shouhouPrice,
    sum(kc.wxPrices)  wxPrice,
    sum(kc.wxCount)  wxCount,
    sum(kc.kcCount) stockCount,
    sum(kc.kcPrices) stockAmount,
    sum(kc.wayCount) transportCount,
    sum(kc.wayPrices) transportAmount,
    sum(kc.orderCount) debtCount,
    sum(kc.orderPrices) debtAmount,
    sum(kc.allCount) totalCount,
    sum(kc.allPrices) totalAmount
    <include refid="smallAndRepairSql"/>
    <include refid="sonGroupBy"/>
    ) x
    order by x.viewsWeek desc , x.viewsweekr desc , x.memberPrice asc
    <if test="req.pageQuery == true">
      OFFSET #{offset} ROWS FETCH NEXT #{pSize} ROWS ONLY
    </if>
  </select>

  <select id="sumAll" resultType="com.jiuji.oa.stock.productkc.vo.res.AggregatedStockVO">
    SELECT
      sum(kc.lossCount) lossCount ,
      sum(kc.lossPrices)  lossPrices,
      sum(kc.shouhouCount)  shouhouCount,
      sum(kc.shouhouPrices)  shouhouPrice,
      sum(kc.wxPrices)  wxPrice,
      sum(kc.wxCount)  wxCount,
      sum(kc.kcCount) stockCount,
      sum(kc.kcPrices) stockAmount,
      sum(kc.wayCount) transportCount,
      sum(kc.wayPrices) transportAmount,
      sum(kc.orderCount) debtCount,
      sum(kc.orderPrices) debtAmount,
      sum(kc.allCount) totalCount,
      sum(kc.allPrices) totalAmount
      <include refid="smallAndRepairSql"/>
  </select>

    <select id="searchYjBrandList" resultType="com.jiuji.oa.nc.stock.vo.res.YjBrandListRes">
        SELECT area.id as areaid,p.brandID as brandId,COUNT(DISTINCT k.id) cnt
        FROM areainfo area with(nolock)
        LEFT join product_mkc k with(nolock) on k.areaid=area.id
        LEFT join yjstock y with(nolock) on y.areaid=area.id
        LEFT join productinfo p with(nolock) on k.ppriceid=p.ppriceid
        LEFT join ch999_user u with(nolock) on u.area1id=k.areaid
        <where>ispass =1 and (isnull(k.mouldFlag,0)=1 and k.kc_check in (2,3,10) )
            and p.brandID is not null
            <!--商品名称-->
            <if test="req.searchValue != null and req.searchValue != '' and req.searchType == 1 ">
                and p.product_name like '%' + #{req.searchValue}+'%'
            </if>
            <!--ppid-->
            <if test="req.searchValue != null and req.searchValue != '' and req.searchType == 2 ">
                and k.ppriceid = #{req.searchValue}
            </if>
            <!--商品id-->
            <if test="req.searchValue != null and req.searchValue != '' and req.searchType == 3 ">
                and p.productid=#{req.searchValue}
            </if>
            <!--门店-->
            <if test="req.searchAreaIdList != null and req.searchAreaIdList.size()>0 ">
                and area.id in
                <foreach collection="req.searchAreaIdList" item="it" separator="," open="(" close=")">
                    #{it}
                </foreach>
            </if>
            <!--门店类别-->
            <if test="req.kindList!=null and req.kindList.size()>0">
                and area.kind1 in
                <foreach collection="req.kindList" index="index" item="item" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <!--门店级别-->
            <if test="req.areaLevel!=null and req.areaLevel.size()!=0">
                AND area.level1 IN
                <foreach collection="req.areaLevel" item="item" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <!--评定级别-->
            <if test="req.displayLevel!=null and req.displayLevel.size()!=0">
                AND area.displayLevel IN
                <foreach collection="req.displayLevel" item="item" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <!--品牌专区-->
            <!--<if test="req.brandidList!=null and req.brandidList.size()!=0">
                AND y.is_del = 0 AND y.brandid in
                <foreach item="item" index="index" collection="req.brandidList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>-->
            <!--品牌驻店-->
            <if test="req.isshixiList!=null and req.isshixiList.size()!=0">
                AND u.islogin = 0 AND u.isshixi in
                <foreach item="item" index="index" collection="req.isshixiList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <!--门店属性-->
            <if test="req.areaAttribute!=null and req.areaAttribute.size()!=0">
                AND area.attribute IN
                <foreach collection="req.areaAttribute" item="item" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <!--分类-->
            <if test="req.categoryCharSeq != null and req.categoryCharSeq !='' ">
                AND exists( select 1 from f_category_children(#{req.categoryCharSeq}) f where f.id=p.cid )
            </if>
            <!--门店类型-->
            <if test="req.shopCategory!=null and req.shopCategory.size()!=0">
                AND area.shopCategory IN
                <foreach collection="req.shopCategory" item="item" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY area.id ,p.brandID
        order by cnt DESC
    </select>

    <select id="searchYjDepreciationPage" resultType="com.jiuji.oa.nc.stock.vo.res.YjDepreciationRes">
        SELECT k.areaid as areaid,k.id as mkcId,p.product_name,k.staticPrice,ISNULL(SUM(ydd.delta_fee),0 ) as alreadyDepreciationFee,ISNULL(MIN(ydd.after_update_fee),0 ) as residualFee, ISNULL(ydc.depreciation_day_fee,0 ) depreciationDayFee,MAX(ydd.create_time) as lastDepreciationTime
        FROM product_mkc k with(nolock)
        LEFT join areainfo area with(nolock) on k.areaid=area.id
        LEFT join productinfo p with(nolock) on k.ppriceid=p.ppriceid
        LEFT join  yj_depreciation_detail ydd with(nolock) on ydd.mkcid=k.id
        LEFT join  yj_depreciation_config ydc with(nolock) on ydc.product_id=p.product_id
        <where> area.ispass =1 and (isnull(k.mouldFlag,0)=1 and k.kc_check in (2,3,10) )
            <!--商品id-->
            <if test="req.searchValue != null and req.searchValue != '' and req.searchType == 1 ">
                and k.id =#{req.searchValue}
            </if>
            <!--商品id-->
            <if test="req.searchValue != null and req.searchValue != '' and req.searchType == 2 ">
                and p.product_name like '%' + #{req.searchValue}+'%'
            </if>
            <if test="req.residualFlag != null and req.residualFlag != ''">
                and p.product_name like '%' + #{req.residualFlag}+'%'
            </if>
            <if test="req.searchAreaIdList != null and req.searchAreaIdList.size()>0 ">
                and area.id in
                <foreach collection="req.searchAreaIdList" item="it" separator="," open="(" close=")">
                    #{it}
                </foreach>
            </if>
        </where>
        GROUP BY k.areaid ,k.id ,p.product_name,k.staticPrice, ydc.depreciation_day_fee
    </select>

    <select id="getAreaWithYjStockList" resultType="com.jiuji.oa.nc.stock.vo.AreaWithYjStock">
        SELECT dfp.area_id areaId, ifnull(sum(dfp.machine_number),0) as num from dp_fix_photo dfp
        WHERE dfp.area_id in
        <foreach collection="areaIdList" item="areaId" separator="," open="(" close=")">
            #{areaId}
        </foreach>
        and dfp.cabinet_type in (3,4)
        and dfp.status = 0
        GROUP BY area_id
    </select>
    <select id="getAreaWithYjStockListByCidList" resultType="com.jiuji.oa.nc.stock.vo.AreaWithYjStock">
        SELECT
            p.area_id as areaId,
            SUM(d.count ) as num
        FROM
            dp_fix_photo p
                LEFT JOIN dp_fix_photo_machine_detail d ON p.id = d.fix_photo_id
        where
        d.deleted=0 and p.status=0
        <choose>
            <when test="brandIdList != null and brandIdList.size()>0">
                and d.parent_id is not null
            </when>
            <otherwise>
                and d.parent_id is null
            </otherwise>
        </choose>
        <if test="areaIdList != null and areaIdList.size()>0">
            AND  p.area_id IN
            <foreach collection="areaIdList" item="areaId" separator="," open="(" close=")">
                #{areaId}
            </foreach>
        </if>
        <if test="cidList != null and cidList.size()>0">
            AND  d.cid  IN
            <foreach collection="cidList" item="cid" separator="," open="(" close=")">
                #{cid}
            </foreach>
        </if>
        <if test="brandIdList != null and brandIdList.size()>0">
            AND  d.brand_id  IN
            <foreach collection="brandIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
             group by  p.area_id;
    </select>
    <select id="getProductMkcInfo" resultType="com.jiuji.oa.nc.stock.vo.ProductMkcInfo">
        select k.id mkcId,k.insourceid2 channelId,
               p.brandID brandId,a.pid provinceId
        from product_mkc k with(nolock)
        left join productinfo p with(nolock) on p.ppriceid = k.ppriceid
        left join areainfo a with(nolock) on a.id = k.areaid
        where k.id=#{mkcId}
    </select>
    <select id="findTlProdcutInfoByMkcId" resultType="com.jiuji.oa.stock.inboundquery.vo.TlProductInfoVO">
        select k.id mkcId,tpo.order_id orderId,t.qty
        from product_mkc k with(nolock)
        left join dbo.mkcCaiGouBasket m with (nolock) ON m.mkc_id = k.id
        left join third_platform_purchase_order tpo with(nolock) on m.sub_id = tpo.mkc_caigou_id and tpo.plat_code = 'TL_MALL'
        left join (select dh.order_id,dh.plat_code,count(di.id) qty
        from third_platform_purchase_delivery_head dh with(nolock)
        left join third_platform_purchase_delivery_imei di with(nolock) on dh.ship_no = di.ship_no and dh.plat_code = di.plat_code and isnull(di.is_del,0) = 0
        where isnull(dh.is_del,0) = 0
        group by dh.order_id,dh.plat_code
        ) t on tpo.order_id = t.order_id
        where k.id in
        <foreach collection="mkcIdList" item="it" separator="," open="(" close=")">
            #{it}
        </foreach>
    </select>
</mapper>
