<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.dj.mapper.DjMapper">
    <resultMap id="CustomerBillDtoMap" type="com.jiuji.oa.stock.dj.dto.CustomerBillDto">
        <result property="billType" column="billType" jdbcType="VARCHAR"/>
        <result property="billCode" column="billCode" jdbcType="VARCHAR"/>
        <result property="billDate" column="billDate" jdbcType="VARCHAR"/>
        <result property="storeCode" column="storeCode" jdbcType="VARCHAR"/>
        <result property="storeName" column="storeName" jdbcType="VARCHAR"/>
        <result property="createDate" column="createDate" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="oppNumberCp" column="oppNumberCp" jdbcType="VARCHAR"/>
        <collection property="psiAgentCustomerLineDTOList" javaType="java.util.List"
                    ofType="com.jiuji.oa.stock.dj.dto.PsiAgentCustomerLineDTO">
            <result property="ean" column="ean" jdbcType="VARCHAR"/>
            <result property="ppriceid" column="ppriceid" jdbcType="VARCHAR"/>
            <result property="prodQuantity" column="prodQuantity" jdbcType="VARCHAR"/>
            <result property="sn" column="sn" jdbcType="VARCHAR"/>
            <result property="stockCode" column="stockCode" jdbcType="VARCHAR"/>
            <result property="stockName" column="stockName" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>


    <select id="getPurchaseInbound" resultMap="CustomerBillDtoMap">
        select 'PURCHASE_IN' as billType,k.id as billCode,s.indate as billDate,'' as storeCode,'' as storeName,
        k.imeidate as createDate,'' as status,'' as oppNumberCp,
        bar.barCode as ean ,1 as prodQuantity,k.imei as sn,a.id as stockCode,a.area_name as stockName,p.ppriceid
        from dbo.product_mkc k WITH(nolock)
        left join dbo.Ok3w_qudao o WITH(nolock) on o.id=k.insourceid2
        left join dbo.productinfo p WITH(nolock) on p.ppriceid=k.ppriceid
        left join dbo.productBarcode bar on bar.ppriceid = p.ppriceid and bar.isDefault = 1 and bar.barCode is not null
        left join dbo.areainfo a WITH(nolock) on a.id=k.origareaid
        inner join dbo.mkcCaiGouBasket b WITH(nolock) on b.mkc_id=k.id
        inner join dbo.mkcCaiGouSub s WITH(nolock) on s.id=b.sub_id
        <where>
            <if test="xtenant == 0">
                and o.id in (4815,4816)
            </if>
            <if test="areaIds!=null and areaIds.size>0">
                and a.id in
                <foreach collection="areaIds" item="areaId" separator="," close=")" open="(">
                    #{areaId}
                </foreach>
            </if>
            and p.brandID=1146 and b.sub_id is not null and k.imei is not null and p.ismobile1 = 1 and k.imeidate
            between #{startTime} and #{endTime}
        </where>
        union all
        select 'PURCHASE_IN' as billType,k.id as billCode,k.imeidate as billDate,'' as storeCode,'' as storeName,
        k.imeidate as createDate,'' as status,'' as oppNumberCp,
        bar.barCode as ean ,1 as prodQuantity,k.imei as sn,a.id as stockCode,a.area_name as stockName,p.ppriceid
        from dbo.product_mkc k WITH(nolock)
        left join dbo.Ok3w_qudao o WITH(nolock) on o.id=k.insourceid2
        left join dbo.productinfo p WITH(nolock) on p.ppriceid=k.ppriceid
        left join dbo.productBarcode bar on bar.ppriceid = p.ppriceid and bar.isDefault = 1 and bar.barCode is not null
        left join dbo.areainfo a WITH(nolock) on a.id=k.origareaid
        left join dbo.mkcCaiGouBasket b WITH(nolock) on b.mkc_id=k.id
        <where>
            <if test="xtenant == 0">
                and o.id in (4815,4816)
            </if>
            <if test="areaIds!=null and areaIds.size>0">
                and a.id in
                <foreach collection="areaIds" item="areaId" separator="," close=")" open="(">
                    #{areaId}
                </foreach>
            </if>
            and p.brandID=1146 and b.sub_id is null and k.imei is not null and p.ismobile1 = 1 and k.imeidate
            between #{startTime} and #{endTime}
        </where>
        union all
        select 'PURCHASE_IN' as billType,b.sub_id as billCode,s.dtime as billDate,'' as storeCode,'' as storeName,
        s.ruku_dtime as createDate,'' as status,'' as oppNumberCp,
        bar.barCode as ean ,SUM(b.lcount) as prodQuantity,'' as sn,a.id as stockCode,a.area_name as stockName,p.ppriceid
        from dbo.caigou_basket b WITH(nolock)
        left join dbo.caigou_sub s WITH(nolock) on b.sub_id=s.id
        LEFT JOIN Ok3w_qudao o with(nolock) ON s.insourceid = o.id
        left join dbo.productinfo p WITH(nolock) on b.ppriceid=p.ppriceid
        left join dbo.productBarcode bar on bar.ppriceid = p.ppriceid and bar.isDefault = 1 and bar.barCode is not null
        left join dbo.areainfo a WITH(nolock) on a.id=s.areaid
        <where>
            <if test="xtenant == 0">
                and o.id in (4815,4816)
            </if>
            <if test="areaIds!=null and areaIds.size>0">
                and a.id in
                <foreach collection="areaIds" item="areaId" separator="," close=")" open="(">
                    #{areaId}
                </foreach>
            </if>
            and s.stats in (3) and p.brandID=1146 and s.ruku_dtime between #{startTime} and #{endTime}
        </where>
        group by b.sub_id,s.dtime ,s.ruku_dtime,bar.barCode,a.id ,a.area_name,p.ppriceid
    </select>

    <select id="getPurchaseReturn" resultMap="CustomerBillDtoMap">
        select 'PURCHASE_RETURN' as billType,b.sub_id as billCode,s.dtime as billDate,'' as storeCode,'' as storeName,
        s.finishTime as createDate,'' as status,'' as oppNumberCp,
        bar.barCode as ean ,1 as prodQuantity,k.imei as sn,a.id as stockCode,a.area_name as stockName,p.ppriceid
        from return_basket b WITH(nolock)
        inner join dbo.return_sub s WITH(nolock) on b.sub_id=s.id
        left join dbo.product_mkc k WITH(nolock) on k.id=b.ppriceid
        left join dbo.Ok3w_qudao o WITH(nolock) on o.id=k.insourceid2
        left join dbo.productinfo p WITH(nolock) on k.ppriceid=p.ppriceid
        left join dbo.productBarcode bar on bar.ppriceid = p.ppriceid and bar.isDefault = 1 and bar.barCode is not null
        left join dbo.areainfo a WITH(nolock) on a.id=k.origareaid
        <where>
            <if test="xtenant == 0">
                and o.id in (4815,4816)
            </if>
            <if test="areaIds!=null and areaIds.size>0">
                and a.id in
                <foreach collection="areaIds" item="areaId" separator="," close=")" open="(">
                    #{areaId}
                </foreach>
            </if>
            and s.states=3 and s.type_=3 and b.sub_id is not null and k.imei is not null and p.ismobile1 = 1 and
            p.brandID=1146 and s.finishTime between #{startTime} and #{endTime}
        </where>
        union all
        select 'PURCHASE_RETURN' as billType,b.sub_id as billCode,s.dtime as billDate,'' as storeCode,'' as storeName,
        s.finishTime as createDate,'' as status,'' as oppNumberCp,
        bar.barCode as ean ,SUM(b.lcount) as prodQuantity,'' as sn,a.id as stockCode,a.area_name as stockName,p.ppriceid
        from return_basket b WITH(nolock)
        inner join dbo.return_sub s WITH(nolock) on b.sub_id=s.id
        left join dbo.Ok3w_qudao o WITH(nolock) on o.id=s.insourceid
        left join dbo.productinfo p WITH(nolock) on b.ppriceid=p.ppriceid
        left join dbo.productBarcode bar on bar.ppriceid = p.ppriceid and bar.isDefault = 1 and bar.barCode is not null
        left join dbo.areainfo a WITH(nolock) on a.id=s.areaid
        <where>
            <if test="xtenant == 0">
                and o.id in (4815,4816)
            </if>
            <if test="areaIds!=null and areaIds.size>0">
                and a.id in
                <foreach collection="areaIds" item="areaId" separator="," close=")" open="(">
                    #{areaId}
                </foreach>
            </if>
            and s.states=3 and s.type_=1 and b.sub_id is not null and p.ismobile1 = 0 and p.brandID=1146 and
            s.finishTime
            between #{startTime} and #{endTime}
        </where>
        group by b.sub_id,s.dtime ,s.finishTime,bar.barCode,a.id ,a.area_name,p.ppriceid

    </select>

    <select id="getDistributionOutbound" resultMap="CustomerBillDtoMap">
        select 'DIS_SALES_OUT' as billType,s.sub_id as billCode,s.sub_date as billDate,s.userid as storeCode,
        <choose>
            <when test="xtenant == 51000">
                COALESCE(z.name,bu.UserName) as storeName,
            </when>
            <otherwise>
                bu.UserName as storeName,
            </otherwise>
        </choose>

        s.tradeDate1 as createDate,'' as status,'' as oppNumberCp,
        bar.barCode as ean ,1 as prodQuantity,k.imei as sn,a.id as stockCode,a.area_name as stockName,p.ppriceid
        from dbo.basket b WITH(nolock)
        left join dbo.sub s WITH(nolock) on b.sub_id=s.sub_id
        left join dbo.BBSXP_Users bu WITH(nolock) on bu.id=s.userid
        left join dbo.zitidian z with(nolock) on z.ispass = 1 and z.shopType=4 and z.userid = s.userid
        left join dbo.productinfo p WITH(nolock) on b.ppriceid=p.ppriceid
        left join dbo.productBarcode bar on bar.ppriceid = p.ppriceid and bar.isDefault = 1 and bar.barCode is not null
        left join dbo.product_mkc k WITH(nolock) on k.basket_id=b.basket_id
        left join dbo.Ok3w_qudao o WITH(nolock) on o.id=k.insourceid2
        left join dbo.areainfo a WITH(nolock) on a.id=s.areaid
        <where>
            <if test="xtenant == 0">
                and o.id in (4815,4816)
            </if>
            <if test="areaIds!=null and areaIds.size>0">
                and a.id in
                <foreach collection="areaIds" item="areaId" separator="," close=")" open="(">
                    #{areaId}
                </foreach>
            </if>
            and isnull(b.isdel,0)=0 and s.sub_check in (3,9) and s.tradeDate1 between #{startTime} and #{endTime}
            and p.brandID=1146 and p.ismobile1 = 1
            <if test="xtenant == 0">
                and o.id in (4815,4816)
            </if>
        </where>
        union all
        select 'DIS_SALES_OUT' as billType,s.sub_id as billCode,s.sub_date as billDate,s.userid as storeCode,
        <choose>
            <when test="xtenant == 51000">
                COALESCE(z.name,bu.UserName) as storeName,
            </when>
            <otherwise>
                bu.UserName as storeName,
            </otherwise>
        </choose>
        s.tradeDate1 as createDate,'' as status,'' as oppNumberCp,
        bar.barCode as ean ,SUM(b.basket_count) as prodQuantity,'' as sn,a.id as stockCode,a.area_name as
        stockName,p.ppriceid
        from dbo.basket b WITH(nolock)
        left join dbo.sub s WITH(nolock) on b.sub_id=s.sub_id
        left join dbo.BBSXP_Users bu WITH(nolock) on bu.id=s.userid
        left join dbo.zitidian z with(nolock) on z.ispass = 1 and z.shopType=4 and z.userid = s.userid
        left join dbo.productinfo p WITH(nolock) on b.ppriceid=p.ppriceid
        left join dbo.productBarcode bar on bar.ppriceid = p.ppriceid and bar.isDefault = 1 and bar.barCode is not null
        left join dbo.areainfo a WITH(nolock) on a.id=s.areaid
        <where>
            <if test="areaIds!=null and areaIds.size>0">
                and a.id in
                <foreach collection="areaIds" item="areaId" separator="," close=")" open="(">
                    #{areaId}
                </foreach>
            </if>
            and isnull(b.isdel,0)=0 and s.sub_check in (3,9) and s.tradeDate1 between #{startTime} and #{endTime}
            and p.brandID=1146 and p.ismobile1 = 0
        </where>
        group by s.sub_id,s.sub_date ,s.tradeDate1,bar.barCode,a.id ,a.area_name,s.userid, z.name, bu.UserName,p.ppriceid
        <if test="xtenant == 0">
            union all
            select 'DIS_SALES_OUT' as billType,s.id as billCode,k.imeidate as billDate,
            '0' as storeCode,'九机RKA' as storeName,
            k.imeidate as createDate,'' as status,'' as oppNumberCp,
            bar.barCode as ean ,1 as prodQuantity,k.imei as sn,a.id as stockCode,a.area_name as stockName,p.ppriceid
            from dbo.product_mkc k WITH(nolock)
            left join dbo.Ok3w_qudao o WITH(nolock) on o.id=k.insourceid2
            left join dbo.productinfo p WITH(nolock) on p.ppriceid=k.ppriceid
            left join dbo.productBarcode bar on bar.ppriceid = p.ppriceid and bar.isDefault = 1 and bar.barCode is not
            null
            left join dbo.areainfo a WITH(nolock) on a.id=k.origareaid
            inner join dbo.mkcCaiGouBasket b WITH(nolock) on b.mkc_id=k.id
            inner join dbo.mkcCaiGouSub s WITH(nolock) on s.id=b.sub_id
            <where>
                <if test="xtenant == 0">
                    and o.id in (4815,4816)
                </if>
                <if test="areaIds!=null and areaIds.size>0">
                    and a.id in
                    <foreach collection="areaIds" item="areaId" separator="," close=")" open="(">
                        #{areaId}
                    </foreach>
                </if>
                and a.id = 16 and p.brandID=1146 and b.sub_id is not null and k.imei is not null and p.ismobile1 = 1 and
                k.imeidate between #{startTime} and #{endTime}
            </where>
            union all
            select 'DIS_SALES_OUT' as billType,s.id as billCode,s.dtime as billDate,
            '0' as storeCode,'九机RKA' as storeName,
            s.ruku_dtime as createDate,'' as status,'' as oppNumberCp,
            bar.barCode as ean ,SUM(b.lCount) as prodQuantity,'' as sn,a.id as stockCode,a.area_name as stockName,p.ppriceid
            from dbo.caigou_basket b WITH(nolock)
            left join dbo.caigou_sub s WITH(nolock) on b.sub_id=s.id
            LEFT JOIN Ok3w_qudao o with(nolock) ON s.insourceid = o.id
            left join dbo.productinfo p WITH(nolock) on b.ppriceid=p.ppriceid
            left join dbo.productBarcode bar on bar.ppriceid = p.ppriceid and bar.isDefault = 1 and bar.barCode is not
            null
            left join dbo.areainfo a WITH(nolock) on a.id=s.areaid
            <where>
                <if test="xtenant == 0">
                    and o.id in (4815,4816)
                </if>
                <if test="areaIds!=null and areaIds.size>0">
                    and a.id in
                    <foreach collection="areaIds" item="areaId" separator="," close=")" open="(">
                        #{areaId}
                    </foreach>
                </if>
                and a.id = 16 and s.stats in (3) and p.brandID=1146 and s.ruku_dtime between #{startTime} and #{endTime}
            </where>
            group by s.id,s.dtime ,s.ruku_dtime,bar.barCode,a.id ,a.area_name,p.ppriceid
            union all
            select 'DIS_SALES_OUT' as billType,s.id as billCode,k.imeidate as billDate,
            '0' as storeCode,'九机RKA' as storeName,
            k.imeidate as createDate,'' as status,'' as oppNumberCp,
            bar.barCode as ean ,1 as prodQuantity,k.imei as sn,'16' as stockCode,'dc' as stockName,p.ppriceid
            from dbo.product_mkc k WITH(nolock)
            left join dbo.Ok3w_qudao o WITH(nolock) on o.id=k.insourceid2
            left join dbo.productinfo p WITH(nolock) on p.ppriceid=k.ppriceid
            left join dbo.productBarcode bar on bar.ppriceid = p.ppriceid and bar.isDefault = 1 and bar.barCode is not
            null
            left join dbo.areainfo a WITH(nolock) on a.id=k.origareaid
            inner join dbo.mkcCaiGouBasket b WITH(nolock) on b.mkc_id=k.id
            inner join dbo.mkcCaiGouSub s WITH(nolock) on s.id=b.sub_id
            where
            a.id not in (878,879,1200,1141,901,902,1204,1142,962,963,1202,1144,1201,1203,1145,980,981)
            and a.id not in
            (878,879,880,889,897,898,899,900,901,920,931,943,944,961,962,963,972,973,974,975,976,977,979,980,981,1045,891,892,1106,1107,1108,1109,1110,1000,1001,1004,1127,1128,1141,1149,1154,1172,1007,1019,929,930,914,915,1142,1143,1144,1145,1147,902,1174,1178,1200,1201,1202,1203,1204,1212,1209,1210,1211,1246)
            and k.kc_check in (2,3,10) and k.imeidate &lt;'2023-10-01 00:00:00' and o.id in (4815,4816)
            and p.brandID=1146 and k.imei is not null and p.ismobile1 = 1
            and GETDATE() &lt; '2023-11-25 00:00:00'
        </if>
        <if test="xtenant == 76000">
            union all
            select 'DIS_SALES_OUT' as billType,mt.id as billCode,mt.dtime as billDate,0 as storeCode,
            'KA' as storeName,
            mt.sendtime as createDate,'' as status,'' as oppNumberCp,
            bar.barCode as ean ,1 as prodQuantity,k.imei as sn,a.id as stockCode,a.area_name as stockName,p.ppriceid
            from dbo.mkc_toarea mt WITH(nolock)
            left join dbo.product_mkc k WITH(nolock) on k.id=mt.mkc_id
            left join dbo.productinfo p WITH(nolock) on k.ppriceid=p.ppriceid
            left join dbo.productBarcode bar on bar.ppriceid = p.ppriceid and bar.isDefault = 1 and bar.barCode is
            not null
            left join dbo.Ok3w_qudao o WITH(nolock) on o.id=k.insourceid2
            left join dbo.areainfo a WITH(nolock) on a.id=mt.areaid
            where
            a.id = 107
            and mt.stats in (2,3)
            and p.brandID=1146 and p.ismobile1 = 1
            and mt.sendtime between #{startTime} and #{endTime}
        </if>
        <if test="xtenant == 51000">
            union all
            select 'DIS_SALES_OUT' as billType,mt.id as billCode,mt.dtime as billDate,0 as storeCode,
            'ARS' as storeName,
            mt.sendtime as createDate,'' as status,'' as oppNumberCp,
            bar.barCode as ean ,1 as prodQuantity,k.imei as sn,a.id as stockCode,a.area_name as stockName,p.ppriceid
            from dbo.mkc_toarea mt WITH(nolock)
            left join dbo.product_mkc k WITH(nolock) on k.id=mt.mkc_id
            left join dbo.productinfo p WITH(nolock) on k.ppriceid=p.ppriceid
            left join dbo.productBarcode bar on bar.ppriceid = p.ppriceid and bar.isDefault = 1 and bar.barCode is
            not null
            left join dbo.Ok3w_qudao o WITH(nolock) on o.id=k.insourceid2
            left join dbo.areainfo a WITH(nolock) on a.id=mt.areaid
            where
            a.id in (94,92)
            and mt.stats in (2,3)
            and p.brandID=1146 and p.ismobile1 = 1
            and mt.sendtime between #{startTime} and #{endTime}
            union all
            select 'DIS_SALES_OUT' as billType,mt.id as billCode,mt.dtime as billDate,1 as storeCode,
            '直销' as storeName,
            mt.sendtime as createDate,'' as status,'' as oppNumberCp,
            bar.barCode as ean ,1 as prodQuantity,k.imei as sn,a.id as stockCode,a.area_name as stockName,p.ppriceid
            from dbo.mkc_toarea mt WITH(nolock)
            left join dbo.product_mkc k WITH(nolock) on k.id=mt.mkc_id
            left join dbo.productinfo p WITH(nolock) on k.ppriceid=p.ppriceid
            left join dbo.productBarcode bar on bar.ppriceid = p.ppriceid and bar.isDefault = 1 and bar.barCode is
            not null
            left join dbo.Ok3w_qudao o WITH(nolock) on o.id=k.insourceid2
            left join dbo.areainfo a WITH(nolock) on a.id=mt.areaid
            where
            a.id in (313,314)
            and mt.stats in (2,3)
            and p.brandID=1146 and p.ismobile1 = 1
            and mt.sendtime between #{startTime} and #{endTime}
        </if>
    </select>

    <select id="getDistributionReturnInbound" resultMap="CustomerBillDtoMap">
        select 'DIS_SALES_RETURN' as billType,s.sub_id as billCode,s.sub_date as billDate,s.userid as storeCode,
        <choose>
            <when test="xtenant == 51000">
                COALESCE(z.name,bu.UserName) as storeName,
            </when>
            <otherwise>
                bu.UserName as storeName,
            </otherwise>
        </choose>
        s.tradeDate1 as createDate,'' as status,'' as oppNumberCp,
        bar.barCode as ean ,1 as prodQuantity,k.imei as sn,a.id as stockCode,a.area_name as stockName,p.ppriceid
        from dbo.basket b WITH(nolock)
        left join dbo.sub s WITH(nolock) on b.sub_id=s.sub_id
        left join dbo.BBSXP_Users bu WITH(nolock) on bu.id=s.userid
        left join dbo.zitidian z with(nolock) on z.ispass = 1 and z.shopType=4 and z.userid = s.userid
        left join dbo.productinfo p WITH(nolock) on b.ppriceid=p.ppriceid
        left join dbo.productBarcode bar on bar.ppriceid = p.ppriceid and bar.isDefault = 1 and bar.barCode is not null
        left join dbo.product_mkc k WITH(nolock) on k.basket_id=b.basket_id
        left join dbo.Ok3w_qudao o WITH(nolock) on o.id=k.insourceid2
        left join dbo.areainfo a WITH(nolock) on a.id=s.areaid
        <where>
            <if test="xtenant == 0">
                and o.id in (4815,4816)
            </if>
            <if test="areaIds!=null and areaIds.size>0">
                and a.id in
                <foreach collection="areaIds" item="areaId" separator="," close=")" open="(">
                    #{areaId}
                </foreach>
            </if>
            and isnull(b.isdel,0)=0 and s.sub_check in (9) and s.tradeDate1 between #{startTime} and #{endTime}
            and p.brandID=1146 and p.ismobile1 = 1
        </where>
        union all
        select 'DIS_SALES_RETURN' as billType,s.sub_id as billCode,s.sub_date as billDate,s.userid as storeCode,
        <choose>
            <when test="xtenant == 51000">
                COALESCE(z.name,bu.UserName) as storeName,
            </when>
            <otherwise>
                bu.UserName as storeName,
            </otherwise>
        </choose>
        s.tradeDate1 as createDate,'' as status,'' as oppNumberCp,
        bar.barCode as ean ,SUM(b.basket_count) as prodQuantity,'' as sn,a.id as stockCode,a.area_name as
        stockName,p.ppriceid
        from dbo.basket b WITH(nolock)
        left join dbo.sub s WITH(nolock) on b.sub_id=s.sub_id
        left join dbo.BBSXP_Users bu WITH(nolock) on bu.id=s.userid
        left join dbo.zitidian z with(nolock) on z.ispass = 1 and z.shopType=4 and z.userid = s.userid
        left join dbo.productinfo p WITH(nolock) on b.ppriceid=p.ppriceid
        left join dbo.productBarcode bar on bar.ppriceid = p.ppriceid and bar.isDefault = 1 and bar.barCode is not null
        left join dbo.areainfo a WITH(nolock) on a.id=s.areaid
        <where>
            <if test="areaIds!=null and areaIds.size>0">
                and a.id in
                <foreach collection="areaIds" item="areaId" separator="," close=")" open="(">
                    #{areaId}
                </foreach>
            </if>
            and isnull(b.isdel,0)=0 and s.sub_check in (9) and s.tradeDate1 between #{startTime} and #{endTime}
            and p.brandID=1146 and p.ismobile1 = 0
        </where>
        group by s.sub_id,s.sub_date ,s.tradeDate1,bar.barCode,a.id ,a.area_name,s.userid, z.name, bu.UserName,p.ppriceid
        <if test="xtenant == 76000">
            union all
            select 'DIS_SALES_RETURN' as billType,mt.id as billCode,mt.dtime as billDate,0 as storeCode,
            'KA' as storeName,
            mt.recivedtime as createDate,'' as status,'' as oppNumberCp,
            bar.barCode as ean ,1 as prodQuantity,k.imei as sn,a.id as stockCode,a.area_name as stockName,p.ppriceid
            from dbo.mkc_toarea mt WITH(nolock)
            left join dbo.product_mkc k WITH(nolock) on k.id=mt.mkc_id
            left join dbo.productinfo p WITH(nolock) on k.ppriceid=p.ppriceid
            left join dbo.productBarcode bar on bar.ppriceid = p.ppriceid and bar.isDefault = 1 and bar.barCode is not
            null
            left join dbo.Ok3w_qudao o WITH(nolock) on o.id=k.insourceid2
            left join dbo.areainfo a WITH(nolock) on a.id=mt.toareaid
            where
            a.id = 107
            and mt.stats in (3)
            and p.brandID=1146 and p.ismobile1 = 1
            and mt.recivedtime between #{startTime} and #{endTime}
        </if>
    </select>



    <select id="getOtherOutbound" resultMap="CustomerBillDtoMap">
        select 'OTHERS_OUT' as billType,s.id as billCode,s.dtime as billDate,'' as storeCode,'' as storeName,
        s.finishTime as createDate,'' as status,'' as oppNumberCp,
        bar.barCode as ean ,1 as prodQuantity,'' as sn,a.id as stockCode,a.area_name as stockName,p.ppriceid
        from dbo.return_basket b WITH(nolock)
        left join dbo.return_sub s WITH(nolock) on b.sub_id=s.id
        left join dbo.Ok3w_qudao o WITH(nolock) on o.id=s.insourceid
        left join dbo.productinfo p WITH(nolock) on b.ppriceid=p.ppriceid
        left join dbo.productBarcode bar on bar.ppriceid = p.ppriceid and bar.isDefault = 1 and bar.barCode is not null
        left join dbo.areainfo a WITH(nolock) on a.id=s.areaid
        <where>
            <if test="xtenant == 0">
                and o.id in (4815,4816)
            </if>
            <if test="areaIds!=null and areaIds.size>0">
                and a.id in
                <foreach collection="areaIds" item="areaId" separator="," close=")" open="(">
                    #{areaId}
                </foreach>
            </if>
            and s.type_=2 and s.states in (3)
            and p.brandID=1146 and s.finishTime between #{startTime} and #{endTime}
        </where>
        group by s.id,s.dtime ,s.finishTime,bar.barCode,a.id ,a.area_name,p.ppriceid
    </select>

    <select id="getPurchaseInboundV2" resultMap="CustomerBillDtoMap">
        select 'PURCHASE_IN' as billType,k.id as billCode,k.imeidate as billDate,'' as storeCode,'' as storeName,
        k.imeidate as createDate,'' as status,'' as oppNumberCp,
        bar.barCode as ean ,1 as prodQuantity,k.imei as sn,a.id as stockCode,a.area_name as stockName,p.ppriceid
        from dbo.product_mkc k WITH(nolock)
        left join dbo.Ok3w_qudao o WITH(nolock) on o.id=k.insourceid2
        left join dbo.productinfo p WITH(nolock) on p.ppriceid=k.ppriceid
        left join dbo.productBarcode bar on bar.ppriceid = p.ppriceid and bar.isDefault = 1 and bar.barCode is not null
        inner join dbo.mkcCaiGouBasket b WITH(nolock) on b.mkc_id=k.id
        inner join dbo.mkcCaiGouSub s WITH(nolock) on s.id=b.sub_id
        left join dbo.areainfo a WITH(nolock) on a.id=s.areaid
        <where>
            <if test="xtenant == 0">
                and o.id in (4814)
            </if>
            <if test="areaIds!=null and areaIds.size>0">
                and a.id in
                <foreach collection="areaIds" item="areaId" separator="," close=")" open="(">
                    #{areaId}
                </foreach>
            </if>
            and p.brandID=1146 and b.sub_id is not null and k.imei is not null and p.ismobile1 = 1 and k.imeidate
            between #{startTime} and #{endTime}
        </where>
        <if test="xtenant == 0">
            union all
            select 'PURCHASE_IN' as billType,k.id as billCode,k.imeidate as billDate,a.id as storeCode,a.area_name as storeName,
            k.imeidate as createDate,'' as status,'' as oppNumberCp,
            bar.barCode as ean ,1 as prodQuantity,k.imei as sn,a.id as stockCode,a.area_name as stockName,p.ppriceid
            from dbo.product_mkc k WITH(nolock)
            left join dbo.Ok3w_qudao o WITH(nolock) on o.id=k.insourceid2
            left join dbo.productinfo p WITH(nolock) on p.ppriceid=k.ppriceid
            left join dbo.productBarcode bar on bar.ppriceid = p.ppriceid and bar.isDefault = 1 and bar.barCode is not
            null
            inner join dbo.mkcCaiGouBasket b WITH(nolock) on b.mkc_id=k.id
            inner join dbo.mkcCaiGouSub s WITH(nolock) on s.id=b.sub_id
            left join dbo.areainfo a WITH(nolock) on a.id=s.areaid
            <where>
                a.id in (892,1372)
                <if test="xtenant == 0">
                    and o.id in (4814)
                </if>
                and p.brandID=1146 and b.sub_id is not null and k.imei is not null and p.ismobile1 = 1 and k.imeidate
                between #{startTime} and #{endTime}
            </where>
        </if>

    </select>

    <select id="getPurchaseReturnV2" resultMap="CustomerBillDtoMap">
        select 'PURCHASE_RETURN' as billType,b.sub_id as billCode,s.finishTime as billDate,'' as storeCode,'' as storeName,
        s.finishTime as createDate,'' as status,'' as oppNumberCp,
        bar.barCode as ean ,1 as prodQuantity,k.imei as sn,a.id as stockCode,a.area_name as stockName,p.ppriceid
        from return_basket b WITH(nolock)
        inner join dbo.return_sub s WITH(nolock) on b.sub_id=s.id
        left join dbo.product_mkc k WITH(nolock) on k.id=b.ppriceid
        left join dbo.Ok3w_qudao o WITH(nolock) on o.id=k.insourceid2
        left join dbo.productinfo p WITH(nolock) on k.ppriceid=p.ppriceid
        left join dbo.productBarcode bar on bar.ppriceid = p.ppriceid and bar.isDefault = 1 and bar.barCode is not null
        left join dbo.areainfo a WITH(nolock) on a.id=s.areaid
        <where>
            <if test="xtenant == 0">
                and o.id in (4814)
            </if>
            <if test="areaIds!=null and areaIds.size>0">
                and a.id in
                <foreach collection="areaIds" item="areaId" separator="," close=")" open="(">
                    #{areaId}
                </foreach>
            </if>
            and s.states=3 and s.type_=3 and b.sub_id is not null and k.imei is not null and p.ismobile1 = 1 and
            p.brandID=1146 and s.finishTime between #{startTime} and #{endTime}
        </where>
    </select>

    <select id="getDistributionOutboundV2" resultMap="CustomerBillDtoMap">
        select 'DIS_SALES_OUT' as billType,
        mt.id           as billCode,
        mt.sendtime     as billDate,
        a2.id               as storeCode,
        a2.area_name             as storeName,
        mt.sendtime     as createDate,
        ''              as status,
        ''              as oppNumberCp,
        bar.barCode     as ean,
        1               as prodQuantity,
        k.imei          as sn,
        a.id            as stockCode,
        a.area_name          as stockName,
        p.ppriceid
        from dbo.mkc_toarea mt WITH(nolock)
        left join dbo.product_mkc k WITH (nolock) on k.id=mt.mkc_id
        left join dbo.productinfo p WITH (nolock) on k.ppriceid=p.ppriceid
        left join dbo.productBarcode bar on bar.ppriceid = p.ppriceid and bar.isDefault = 1 and bar.barCode is not null
        left join dbo.Ok3w_qudao o WITH (nolock) on o.id=k.insourceid2
        left join dbo.areainfo a WITH (nolock) on a.id=mt.areaid
        left join dbo.areainfo a2 WITH (nolock) on a2.id=mt.toareaid
        <where>

        <if test="xtenant == 0">
            and o.id in (4814)
        </if>
        <if test="areaIds!=null and areaIds.size>0">
            and mt.areaid in
            <foreach collection="areaIds" item="areaId" separator="," close=")" open="(">
                #{areaId}
            </foreach>
            and mt.toareaid not in
            <foreach collection="areaIds" item="areaId" separator="," close=")" open="(">
                #{areaId}
            </foreach>
        </if>
        and mt.stats in (2,3)
        and k.imei is not null
        and p.brandID=1146
        and p.ismobile1 = 1
        and mt.sendtime between #{startTime}
        and #{endTime}
        </where>
        <if test="xtenant == 0">
            union all
            select 'DIS_SALES_OUT' as billType,k.id as billCode,k.imeidate as billDate,a.id as storeCode,a.area_name as storeName,
            k.imeidate as createDate,'' as status,'' as oppNumberCp,
            bar.barCode as ean ,1 as prodQuantity,k.imei as sn,a.id as stockCode,a.area_name as stockName,p.ppriceid
            from dbo.product_mkc k WITH(nolock)
            left join dbo.Ok3w_qudao o WITH(nolock) on o.id=k.insourceid2
            left join dbo.productinfo p WITH(nolock) on p.ppriceid=k.ppriceid
            left join dbo.productBarcode bar on bar.ppriceid = p.ppriceid and bar.isDefault = 1 and bar.barCode is not
            null
            inner join dbo.mkcCaiGouBasket b WITH(nolock) on b.mkc_id=k.id
            inner join dbo.mkcCaiGouSub s WITH(nolock) on s.id=b.sub_id
            left join dbo.areainfo a WITH(nolock) on a.id=s.areaid
            <where>
                a.id in (892,1372)
                <if test="xtenant == 0">
                    and o.id in (4814)
                </if>
                and p.brandID=1146 and b.sub_id is not null and k.imei is not null and p.ismobile1 = 1 and k.imeidate
                between #{startTime} and #{endTime}
            </where>
        </if>
    </select>

    <select id="getDistributionReturnInboundV2" resultMap="CustomerBillDtoMap">
        select 'DIS_SALES_RETURN' as billType,
        mt.id           as billCode,
        mt.recivedtime     as billDate,
        a.id               as storeCode,
        a.area_name             as storeName,
        mt.recivedtime     as createDate,
        ''              as status,
        ''              as oppNumberCp,
        bar.barCode     as ean,
        1               as prodQuantity,
        k.imei          as sn,
        a2.id            as stockCode,
        a2.area_name          as stockName,
        p.ppriceid
        from dbo.mkc_toarea mt WITH(nolock)
        left join dbo.product_mkc k WITH (nolock) on k.id=mt.mkc_id
        left join dbo.productinfo p WITH (nolock) on k.ppriceid=p.ppriceid
        left join dbo.productBarcode bar on bar.ppriceid = p.ppriceid and bar.isDefault = 1 and bar.barCode is not null
        left join dbo.Ok3w_qudao o WITH (nolock) on o.id=k.insourceid2
        left join dbo.areainfo a WITH (nolock) on a.id=mt.areaid
        left join dbo.areainfo a2 WITH (nolock) on a2.id=mt.toareaid
        <where>
        <if test="xtenant == 0">
            and o.id in (4814)
        </if>
        <if test="areaIds!=null and areaIds.size>0">
            and mt.toareaid in
            <foreach collection="areaIds" item="areaId" separator="," close=")" open="(">
                #{areaId}
            </foreach>
            and mt.areaid not in
            <foreach collection="areaIds" item="areaId" separator="," close=")" open="(">
                #{areaId}
            </foreach>
        </if>
        and mt.stats in (3)
        and k.imei is not null
        and p.brandID=1146
        and p.ismobile1 = 1
        and mt.recivedtime between #{startTime}
        and #{endTime}
        </where>
    </select>
    <select id="getPurchaseInboundData" resultMap="CustomerBillDtoMap">
        select 'PURCHASE_RETURN' as billType,k.mkcId as billCode,k.instock_date as billDate,'880' as storeCode,'云南大疆DRP入库仓' as storeName,
               k.instock_date as createDate,'' as status,'' as oppNumberCp,
               k.ean as ean ,1 as prodQuantity,k.sn as sn,'880' as stockCode,'云南大疆DRP入库仓' as stockName,k.ppid as ppriceid
        from dbo.dj_data k WITH(nolock)
    </select>
    <select id="getDistributionOutboundData" resultMap="CustomerBillDtoMap">
        select 'DIS_SALES_RETURN' as billType,
               mt.mkcId           as billCode,
               mt.outstock_date     as billDate,
               '880'               as storeCode,
               '云南大疆DRP入库仓'            as storeName,
               mt.outstock_date     as createDate,
               ''              as status,
               ''              as oppNumberCp,
               mt.ean     as ean,
               1               as prodQuantity,
               mt.sn          as sn,
               '880'            as stockCode,
               '云南大疆DRP入库仓'          as stockName,
               mt.ppid as ppriceid
        from dbo.dj_data mt WITH(nolock)
    </select>

</mapper>
