<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.call.mapper.WuliuCallLogMapper">

    <select id="getWuLiuBasket" resultType="com.jiuji.oa.wuliu.entity.WuLiuBasketEntity">
        SELECT basket_id, basket_count, basket_date, product_peizhi, seller, ismobile, price, sub_id, price1, b.ppriceid,
        inprice, giftid, type,  b.isdel, ischu, price2, iskc, isOnShop, jifenPrice, giftPrice, return_price, youhuiPrice
        FROM basket b with(nolock)
        left join productInfo p with(nolock)
        on b.ppriceid = p.ppriceid
        WHERE p.ismobile1=1 AND seller != '网络' AND sub_id in (
        <foreach item="item" index="index" collection="subIdList" separator=",">
            #{item}
        </foreach>
        );
    </select>
</mapper>
