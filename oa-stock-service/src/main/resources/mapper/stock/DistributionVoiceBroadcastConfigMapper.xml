<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.nc.stock.mapper.DistributionVoiceBroadcastConfigMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.nc.stock.entity.DistributionVoiceBroadcastConfig">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="create_time" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="msg_type" column="msg_type" jdbcType="VARCHAR"/>
            <result property="xtenant" column="xtenant" jdbcType="INTEGER"/>
            <result property="is_remind" column="is_remind" jdbcType="BIT"/>
            <result property="remind_voice_url" column="remind_voice_url" jdbcType="VARCHAR"/>
            <result property="bei_voice_url" column="bei_voice_url" jdbcType="VARCHAR"/>
            <result property="is_bei" column="is_bei" jdbcType="BIT"/>
            <result property="version" column="version" jdbcType="VARCHAR"/>
            <result property="area_ids" column="area_ids" jdbcType="VARCHAR"/>
            <result property="is_del" column="is_del" jdbcType="BIT"/>
            <result property="msg_name" column="msg_name" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,create_time,msg_type,
        xtenant,is_remind,remind_voice_url,
        bei_voice_url,is_bei,version,
        area_ids,is_del,
        msg_name
    </sql>
    <select id="selectAllList" resultType="com.jiuji.oa.nc.stock.entity.DistributionVoiceBroadcastConfig">
        select
        <include refid="Base_Column_List"/>
        from distribution_voice_broadcast_config with(nolock)
        where isnull(is_del,0) = 0
    </select>
</mapper>
