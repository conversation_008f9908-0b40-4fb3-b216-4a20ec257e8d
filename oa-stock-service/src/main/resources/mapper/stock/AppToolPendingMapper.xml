<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.nc.stock.mapper.AppToolPendingMapper">

    <select id="mkcToareaListAllCount" resultType="com.jiuji.oa.nc.oaapp.vo.res.AppToolPendingCountVO">
        select COUNT(1) as pendingCount,m.areaid, '12' pendingType
        from mkc_toarea m with(nolock)
        left join product_mkc k
        with (nolock)
        on m.mkc_id=k.id
        left join productinfo p
        with (nolock)
        on p.ppriceid=k.ppriceid
        left join placeLabel place
        with (nolock)
        on place.areaid=k.areaid and place.ppid=k.ppriceid
        left join dbo.areainfo a
        with (nolock)
        on k.areaid = a.id
        left join product_stock_out o
        with (nolock)
        on ( o.related_id = m.id and o.related_type in (4,8) and o.order_status !=1 )
        outer apply (select top 1 wcsl.wms_container_list_id,wcsl.out_order_code from dbo.wms_container_sku_list wcsl
        with (nolock) where wcsl.out_order_code = o.out_order_code) wcsl1
        left join dbo.wms_container_list wcl
        with (nolock)
        on (wcsl1.wms_container_list_id = wcl.id and wcl.container_code like 'picklist%' and wcl.sku_amount !=0 and
        wcl.express_number is null )
        where k.id is not null
        and (o.out_order_code is null
        or (o.out_order_code is not null
        and wcl.id is not null ))
        and m.stats in (0
        , 1)
        <if test="areaId != null and areaId != ''">
            and m.areaid=#{areaId}
        </if>
        GROUP by m.areaid
    </select>

    <select id="pjdiaoboListCount" resultType="com.jiuji.oa.nc.oaapp.vo.res.AppToolPendingCountVO">
        select COUNT(1) as pendingCount,sub.areaid, '13' pendingType
        from diaobo_basket bsk with(nolock)
        inner join diaobo_sub sub
        with (nolock)
        on sub.id=bsk.sub_id
        left join product_kc pk
        with (nolock)
        on pk.ppriceid = bsk.ppriceid and pk.areaid = sub.areaid
        where sub.stats in (2, 5)
        <if test="areaId != null and areaId != ''">
            and sub.areaid=#{areaId}
        </if>
        AND NOT EXISTS (SELECT 1 FROM dbo.displayProductInfo d WITH (NOLOCK) WHERE d.basket_id = bsk.basket_id)
        and (''=''
        or '' = pk.number)
        and not exists (select 1 from product_stock_out o with (nolock) where o.related_id = sub.id
        and o.related_type in (2
        , 3
        , 9
        , 10)
        and o.order_status !=1 )
        GROUP by sub.areaid
    </select>

    <select id="getSmallProOldGoodsWaitingForSelectCount" resultType="com.jiuji.oa.nc.oaapp.vo.res.AppToolPendingCountVO">
        select COUNT(1) as pendingCount,s.areaid, '834' pendingType
        from Smallpro s with(nolock)
        left join areainfo a1
        with (nolock)
        on s.areaid = a1.id
        left join areainfo a2
        with (nolock)
        on s.toareaid = a2.id
        WHERE isnull(s.istoarea
        , 0) = 1
        <if test="areaId != null and areaId != ''">
            and s.areaid = #{areaId}
        </if>
        and s.stats != 2
        and s.toareaid is not null
        and s.wuliuid is null
        and EXISTS(select 1 from shouhou_fanchang f with(nolock) where f.smallproid = s.id and isnull(rstats,0) = 0)
        GROUP by s.areaid
    </select>

    <select id="getYpPjDiaoboListCount" resultType="com.jiuji.oa.nc.oaapp.vo.res.AppToolPendingCountVO">
        select COUNT(1) as pendingCount,s.areaid, '841' pendingType
        FROM dbo.diaobo_sub s with(nolock)
        left join dbo.diaobo_basket b
        with (nolock)
        on s.id=b.sub_id
        left join product_kc pk
        with (nolock)
        on pk.ppriceid = b.ppriceid and pk.areaid = s.areaid
        WHERE s.stats in (2, 5)
        <if test="areaId != null and areaId != ''">
            and s.areaid = #{areaId}
        </if>
        and s.title like '%陈列优品%'
        GROUP by s.areaid
    </select>

    <select id="mkcToAreaNoteListV2Count" resultType="com.jiuji.oa.nc.oaapp.vo.res.AppToolPendingCountVO">
        select COUNT(1) as pendingCount,k.areaid, '19' pendingType
        from mkc_toarea m with(nolock)
        left join product_mkc k
        with (nolock)
        on m.mkc_id=k.id
        left join productinfo p
        with (nolock)
        on p.ppriceid=k.ppriceid
        where k.id is not null and m.stats=2
        <if test="areaId != null and areaId != ''">
            and k.areaid=#{areaId}
        </if>
        GROUP by k.areaid
    </select>

    <select id="kcTransferReceiveListCount" resultType="com.jiuji.oa.nc.oaapp.vo.res.AppToolPendingCountVO">
        select COUNT(1) as pendingCount,s.areaid, '20' pendingType
        from dbo.diaobo_sub s with(nolock)
        left join
        (
        select sum(lcount) lcount,sub_id , sum(isnull(InStockCount,0)) as InStockCount
        from dbo.diaobo_basket b1 with(nolock)
        left join dbo.diaobo_sub s with(nolock) on s.id=b1.sub_id where isnull(isreceive,0)=0 and s.toareaid=#{areaId}
        and s.[stats]=3
        group by sub_id
        ) b
        on s.id=b.sub_id
        where [stats]=3
        <if test="areaId != null and areaId != ''">
            and s.toareaid=#{areaId}
        </if>
        GROUP by s.areaid
    </select>

</mapper>
