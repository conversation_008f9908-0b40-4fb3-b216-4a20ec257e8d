<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.stock.displayproductinfo.mapper.DisplayProductInfoMapper">

  <resultMap id="displayproductinfoMap"
    type="com.jiuji.oa.stock.displayproductinfo.entity.DisplayProductInfo">
    <id property="id" column="id"/>
    <result property="productId" column="dproductId"/>
    <result property="maintainId" column="mainTainID"/>
    <result property="mkcId" column="mkc_id"/>
    <result property="area" column="area"/>
    <result property="ppid" column="ppriceid"/>
    <result property="count" column="count_"/>
    <result property="flag" column="ischu"/>
    <result property="dtime" column="dtime"/>
    <result property="inuser" column="inuser"/>
    <result property="areaId" column="areaid"/>
    <result property="mobile" column="ismobile"/>
    <result property="basketId" column="basket_id"/>
    <result property="comment" column="comment"/>
    <result property="sellPrice" column="sellPrice"/>
    <result property="state" column="stats_"/>
    <result property="msguser" column="msgUser"/>
    <result property="msgdate" column="msgdate"/>
    <result property="msgcontent" column="msgContent"/>
    <result property="onSell" column="isOnSell"/>
    <result property="display" column="Display"/>
    <result property="flaw" column="isFlaw"/>
    <result property="kcCheck" column="kc_check"/>
    <result property="curAreaId" column="curAreaId"/>
  </resultMap>
  <select id="getDisplayProductInfo"
    resultType="com.jiuji.oa.stock.displayproductinfo.vo.res.DisplayProductReqDTO">
    SELECT b.id     as id,
           b.stats_ as state
    FROM dbo.basket a,
         dbo.displayProductInfo b
    where a.sub_id = #{subId}
      and a.basket_id = b.basket_id
  </select>
</mapper>
