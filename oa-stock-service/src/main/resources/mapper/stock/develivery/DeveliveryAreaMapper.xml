<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.develivery.mapper.DeveliveryAreaMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.stock.develivery.entity.DeveliveryArea">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="fromdcreachtime" column="fromdcReachTime" jdbcType="TIMESTAMP"/>
            <result property="fromdclastsubbmittime" column="fromdcLastSubbmitTime" jdbcType="TIMESTAMP"/>
            <result property="todcreachetime" column="todcReacheTime" jdbcType="TIMESTAMP"/>
            <result property="todclastsubbmittime" column="todcLastSubbmitTime" jdbcType="TIMESTAMP"/>
            <result property="addtime" column="addTime" jdbcType="TIMESTAMP"/>
            <result property="sort" column="sort" jdbcType="INTEGER"/>
            <result property="cityid" column="cityId" jdbcType="INTEGER"/>
            <result property="dispatchcenter" column="dispatchCenter" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,fromdcReachTime,fromdcLastSubbmitTime,
        todcReacheTime,todcLastSubbmitTime,addTime,
        sort,cityId,dispatchCenter
    </sql>
</mapper>
