<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.develivery.mapper.DeveliveryRouteMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.stock.develivery.entity.DeveliveryRoute">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="title" column="Title" jdbcType="VARCHAR"/>
            <result property="routeplan" column="Routeplan" jdbcType="VARCHAR"/>
            <result property="routecount" column="RouteCount" jdbcType="INTEGER"/>
            <result property="delFlag" column="IsDel" jdbcType="BIT"/>
            <result property="addtime" column="AddTime" jdbcType="TIMESTAMP"/>
            <result property="cityid" column="CityId" jdbcType="INTEGER"/>
            <result property="sort" column="sort" jdbcType="INTEGER"/>
            <result property="vihicle" column="vihicle" jdbcType="VARCHAR"/>
            <result property="routetype" column="RouteType" jdbcType="TINYINT"/>
            <result property="direct" column="direct" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,Title,Routeplan,
        RouteCount,IsDel,AddTime,
        CityId,sort,vihicle,
        RouteType,direct
    </sql>
    <insert id="syncRouteDate">
        set identity_insert Develivery_Route ON;
        insert into Develivery_Route([id],
            [Title],
            [Routeplan],
            [RouteCount],
            [IsDel],
            [AddTime],
            [CityId],
            [sort],
            [vihicle],
            [RouteType],
            [direct])
        select [id],
            [Title],
            [Routeplan],
            [RouteCount],
            [IsDel],
            [AddTime],
            [CityId],
            [sort],
            [vihicle],
            [RouteType],
            [direct]
        from Develivery_Route_Plan with(nolock)
        where plan_date = #{planDate}
        set identity_insert Develivery_Route OFF;
    </insert>
    <insert id="syncRouteBak">
        set identity_insert Develivery_Route ON;
        insert into Develivery_Route([id],
            [Title],
            [Routeplan],
            [RouteCount],
            [IsDel],
            [AddTime],
            [CityId],
            [sort],
            [vihicle],
            [RouteType],
            [direct])
        select [id],
            [Title],
            [Routeplan],
            [RouteCount],
            [IsDel],
            [AddTime],
            [CityId],
            [sort],
            [vihicle],
            [RouteType],
            [direct]
        from Develivery_Route_bak with(nolock)
        set identity_insert Develivery_Route OFF;
    </insert>

    <delete id="deleteHisData">
        delete from Develivery_Route where RouteType = 0
    </delete>
    <select id="queryCountByAreaId" resultType="java.lang.Integer">
        select count(*) from Develivery_Route dr with(nolock)
        left join Develivery_Node dn with(nolock) on dr.id = dn.Routeid
        where dr.IsDel = 0
        and dn.areaid = #{areaId}
    </select>
</mapper>
