<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.develivery.mapper.DeveliveryNodeMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.stock.develivery.entity.DeveliveryNode">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="area" column="area" jdbcType="VARCHAR"/>
            <result property="areaid" column="areaid" jdbcType="INTEGER"/>
            <result property="reachtime" column="reachTime" jdbcType="TIMESTAMP"/>
            <result property="lastsubbmittime" column="lastSubbmitTime" jdbcType="TIMESTAMP"/>
            <result property="nextnode" column="nextNode" jdbcType="INTEGER"/>
            <result property="index" column="Index" jdbcType="INTEGER"/>
            <result property="addtime" column="AddTime" jdbcType="TIMESTAMP"/>
            <result property="routeindex" column="RouteIndex" jdbcType="INTEGER"/>
            <result property="routeid" column="Routeid" jdbcType="INTEGER"/>
            <result property="days" column="days" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,area,areaid,
        reachTime,lastSubbmitTime,nextNode,
        Index,AddTime,RouteIndex,
        Routeid,days
    </sql>
    <insert id="syncNodeDate">
        SET IDENTITY_INSERT Develivery_Node ON;
        INSERT INTO Develivery_Node (
            [id],
            [areaid],
            [area],
            [reachTime],
            [lastSubbmitTime],
            [nextNode],
            [INDEX],
            [AddTime],
            [RouteIndex],
            [Routeid],
            [days] )
             SELECT
            [id],
            [areaid],
            [area],
            [reachTime],
            [lastSubbmitTime],
            [nextNode],
            [INDEX],
            [AddTime],
            [RouteIndex],
            [Routeid],
            [days]
        FROM
             Develivery_Node_Plan with(nolock)
        WHERE
             plan_date = #{ planDate }
        SET IDENTITY_INSERT Develivery_Node OFF;
    </insert>
    <insert id="syncNodeDateBak">
        SET IDENTITY_INSERT Develivery_Node ON;
        INSERT INTO Develivery_Node (
        [id],
            [areaid],
            [area],
            [reachTime],
            [lastSubbmitTime],
            [nextNode],
            [INDEX],
            [AddTime],
            [RouteIndex],
            [Routeid],
            [days] )
        SELECT
            [id],
            [areaid],
            [area],
            [reachTime],
            [lastSubbmitTime],
            [nextNode],
            [INDEX],
            [AddTime],
            [RouteIndex],
            [Routeid],
            [days]
        FROM
            Develivery_Node_bak with(nolock)
        SET IDENTITY_INSERT Develivery_Node OFF;
    </insert>
    <delete id="deleteHisData">
        delete from Develivery_Node where Routeid not in (select id from Develivery_Route where RouteType = 1)
    </delete>

    <select id="selectNodeByAreaId" resultType="com.jiuji.oa.stock.develivery.vo.res.DeveLiveryNodeDataVO">
        select
          area as area,
          areaid as areaId,
          reachTime as reachTime,
          lastSubbmitTime as lastSubbmitTime,
          [Index] as nodeIndex,
          [RouteIndex] as routeIndex,
          Routeid as routeId,
          days
        from Develivery_Route dr with(nolock)
        left join Develivery_Node dn with(nolock) on dr.id = dn.Routeid
        where dr.RouteType = 0
        and dr.IsDel = 0
        and dn.areaid in
        <foreach collection="areaIdList" open="(" close=")" separator="," item="areaId">
            #{areaId}
        </foreach>
    </select>
</mapper>
