<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.develivery.mapper.DeveliveryRoutePlanMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.stock.develivery.entity.DeveliveryRoutePlan">
            <result property="id" column="id" jdbcType="INTEGER"/>
            <result property="title" column="Title" jdbcType="VARCHAR"/>
            <result property="routeplan" column="Routeplan" jdbcType="VARCHAR"/>
            <result property="routecount" column="RouteCount" jdbcType="INTEGER"/>
            <result property="isdel" column="IsDel" jdbcType="BIT"/>
            <result property="addtime" column="AddTime" jdbcType="TIMESTAMP"/>
            <result property="cityid" column="CityId" jdbcType="INTEGER"/>
            <result property="sort" column="sort" jdbcType="INTEGER"/>
            <result property="vihicle" column="vihicle" jdbcType="VARCHAR"/>
            <result property="routetype" column="RouteType" jdbcType="TINYINT"/>
            <result property="direct" column="direct" jdbcType="BIT"/>
            <result property="planDate" column="plan_date" jdbcType="DATE"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,Title,Routeplan,
        RouteCount,IsDel,AddTime,
        CityId,sort,vihicle,
        RouteType,direct,plan_date
    </sql>
</mapper>
