<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.develivery.mapper.DiaoboSpendHourMapper">

    <select id="mobileDiaoboSpendHourPage"
            resultType="com.jiuji.oa.stock.develivery.vo.res.MobileDiaoboSpendHourPageResVO">
        select m.id as diaoboId,ds.need_send_time as need_send_time,ds.order_type as
        orderType,wet.express_generate_time,wet.express_receive_time,m.id,toarea.area as toareaName,fromarea.area as
        areaName,k.imeidate as instockTime,m.dtime as diaoboTime,
        m.[mkc_id],m.[area],m.[toarea],m.[dtime],m.[sendtime],m.[senduser],m.[recivedtime],m.[reciveuser],m.[shibian],m.[stats],m.[deliveryTime],m.areaid,m.toareaid,m.[wuliuid],m.[expectTime],m.[needPay],m.[isPay],m.[inUser],m.[delivery_type],(trim(p.product_name)
        + trim(isnull(p.product_color, ''))) as product_name,p.ppriceid,k.areaid
        as area1,isnull(j.ispay1,0) as ispay1_,k.orderid,k.kc_check,k.insourceid2,k.staticPrice
        inbeihuoprice,k.fanli,k.inbeihuodate,k.pandiandate,k.pandianuser,k.imeidate,k.basket_id,k.mouldFlag,
        k.imei,i.name as insource_name,k.imeiuser,o.company_jc,datediff(d, m.dtime, isnull(recivedtime, getdate())) as
        d1,DATEDIFF(day,k.imeidate,GETDATE()) as kuling,so.out_order_code from mkc_toarea m with(nolock) left join
        product_mkc k with(nolock) on m.mkc_id=k.id
        left join productinfo p with(nolock) on p.ppriceid=k.ppriceid left join insource i with(nolock) on
        k.insourceid=i.id left join joinMkcFee j with(nolock) on j.mkc_id=k.id LEFT JOIN Ok3w_qudao o WITH(nolock) ON
        o.id = k.insourceid2
        left join wuliu_express_time wet with(nolock) on wet.wuliuid = m.wuliuid
        left join areainfo fromarea with(nolock) on fromarea.id = m.areaid
        left join areainfo toarea with(nolock) on toarea.id = m.toareaid
        left join diaobo_spend_hour_need_time ds with(nolock) on ds.bussiness_id = m.id
        left join product_stock_out so with(nolock) on (so.related_id=m.id and so.related_type in (4,8) and
        isnull(so.is_delete,0)=0 and so.order_status != 1)
        <where>k.id is not null
            and k.kc_check != 4
            and m.areaid = 16
            and m.sendTime is not null
            and m.dtime > '2022-09-21 00:00:00'
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==1">
                and p.ppriceid = #{req.searchValue}
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==2">
                and k.orderid = #{req.searchValue}
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==3">
                and k.imei = #{req.searchValue}
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==4">
                and (trim(p.product_name) + trim(isnull(p.product_color, ''))) like '%' + #{req.searchValue} + '%'
            </if>
            <if test="req.areaKind!=null and req.areaKind!=''">
                <choose>
                    <when test="req.areaKind==1">
                        and toarea.kind1=1
                    </when>
                    <otherwise>
                        and toarea.kind1!=1
                    </otherwise>
                </choose>
            </if>
            <if test="req.orderType == 1">
                and ds.order_type = '现货'
            </if>
            <if test="req.orderType == 2">
                and ds.order_type = '订货'
            </if>
            <if test="req.timeAreaFlag == 1">
                and ds.time_area_flag = 1
            </if>
            <if test="req.timeAreaFlag == 0">
                and ds.time_area_flag = 0
            </if>
            <if test="req.imeiSubmitFlag == 1">
                and k.imei is not null
            </if>
            <if test="req.imeiSubmitFlag == 2">
                and k.imei is null
            </if>
            <if test="req.startTime != null and req.endTime != null">
                and m.dtime between #{req.startTime} and #{req.endTime}
            </if>
        </where>
    </select>


    <select id="accessoryDiaoboSpendHourPage"
            resultType="com.jiuji.oa.stock.develivery.vo.res.AccessoryDiaoboSpendHourPageResVO">
        select * from (select distinct s.id as diaoboId,ds.need_send_time as need_send_time,ds.order_type as
        orderType,wet.express_generate_time,wet.express_receive_time,fromarea.area as areaName, s.areaid as
        areaid,toarea.area as toareaName, s.toareaid as toareaid,s.id,s.title,s.inuser,s.check_dtime,s.stats,db.lcount,
        p.costprice,db.ppriceid,(trim(p.product_name) + trim(isnull(p.product_color, ''))) as product_name,s.check_dtime
        as diaoboTime,s.send_dtime as sendTime,s.ruku_dtime as recivedTime,db.basket_id
        from diaobo_basket db with(nolock)
        left join diaobo_sub s with(nolock) on db.sub_id = s.id
        left join productinfo p with(nolock) on p.ppriceid = db.ppriceid
        left join wuliu_express_time wet with(nolock) on wet.wuliuid = s.wuliuid
        left join areainfo fromarea with(nolock) on fromarea.id = s.areaid
        left join areainfo toarea with(nolock) on toarea.id = s.toareaid
        left join diaobo_spend_hour_need_time ds with(nolock) on ds.bussiness_id = s.id
        <where>s.areaid = 16
            and s.send_dtime is not null
            and s.check_dtime > '2022-09-21 00:00:00'
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==1">
                and p.ppriceid = #{req.searchValue}
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==2">
                and (trim(p.product_name) + trim(isnull(p.product_color, ''))) like '%' + #{req.searchValue} + '%'
            </if>
            <if test="req.areaKind!=null and req.areaKind!=''">
                <choose>
                    <when test="req.areaKind==1">
                        and toarea.kind1=1
                    </when>
                    <otherwise>
                        and toarea.kind1!=1
                    </otherwise>
                </choose>
            </if>
            <if test="req.orderType == 1">
                and ds.order_type = '现货'
            </if>
            <if test="req.orderType == 2">
                and ds.order_type = '订货'
            </if>
            <if test="req.timeAreaFlag == 1">
                and ds.time_area_flag = 1
            </if>
            <if test="req.timeAreaFlag == 0">
                and ds.time_area_flag = 0
            </if>
            <if test="req.startTime != null and req.endTime != null">
                and s.check_dtime between #{req.startTime} and #{req.endTime}
            </if>
        </where>
        ) temp
    </select>

    <select id="orderDiaoboSpendHourPage"
            resultType="com.jiuji.oa.stock.develivery.vo.res.OrderDiaoboSpendHourPageResVO">
        select
        P.product_name+' '+ISNULL(P.product_color,'') productName,
        s.sub_to,
        s.areaid,
        s.sub_date,
        s.sub_check,
        s.userid,
        s.sub_mobile,
        s.yifuM,
        s.kcAreaid,
        s.sub_pay,
        s.subtype,
        s.tradeDate,
        s.tradeDate1,
        s.expectTime,
        s.trader,
        s.delivery,
        b.*
        into #tempSub
        from dbo.sub s with(nolock)
        left join dbo.basket b with (nolock)
        on b.sub_id = s.sub_id
        left join areainfo a with(nolock) on a.id = s.areaid
        left join productinfo p with(nolock) ON b.ppriceid=p.ppriceid
        <where>s.sub_check = 3 and s.sub_id is not null and s.sub_date > '2022-09-21 00:00:00'
            <if test="req.areaKind!=null and req.areaKind!=''">
                <choose>
                    <when test="req.areaKind==1">
                        and a.kind1=1
                    </when>
                    <otherwise>
                        and a.kind1!=1
                    </otherwise>
                </choose>
            </if>
            <if test="req.areaId != null and req.areaId.size > 0">
                and s.areaid in
                <foreach collection="req.areaId" index="index" item="area" separator="," open="(" close=")">
                    #{area}
                </foreach>
            </if>
            <if test="req.delivery != null and req.delivery!='' ">
                AND s.delivery = #{req.delivery}
            </if>
            <if test="req.startTime != null and req.endTime != null">
                and s.sub_date between #{req.startTime} and #{req.endTime}
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==1">
                and p.ppriceid = #{req.searchValue}
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==2">
                and (trim(p.product_name) + trim(isnull(p.product_color, ''))) like '%' + #{req.searchValue} + '%'
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==3">
                and b.sub_id = #{req.searchValue}
            </if>
        </where>
        ;

        WITH selectTemp AS (SELECT
        TOP 100 PERCENT ROW_NUMBER() OVER (
        order by temp.sub_date DESC) as __row_number__,
        temp.*
        from
        (
        SELECT ISNULL(s.iskc,0) as iskc,s.basket_id as basketId,s.expectTime as expectTime,s.tradeDate as
        outstockTime,s.tradeDate1 as
        tradeCompleteTime,wet.express_generate_time,
        wet.express_receive_time,s.areaid,aaa.area as
        areaName,s.sub_id,s.productName,s.basket_count,s.price,s.basket_date,s.ppriceid,s.sub_check,s.seller,
        s.yifuM,s.userid,ISNULL(s.kcAreaid,s.areaid) as kcAreaid,aa.area as kcAreaName,u.UserName,s.sub_mobile,
        k.imei,k.id as mkcid,s.subtype,s.delivery,
        s.sub_pay,s.trader,sa.wuliucompany,sa.wuliuNo,s.sub_to,REPLACE(isnull(dbo.getCityName(sa.cityid),''),' ','') +
        sa.Address as Address,s.sub_date
        FROM
        #tempSub s
        LEFT JOIN dbo.product_mkc k with(nolock) on k.basket_id=s.basket_id
        LEFT JOIN dbo.BBSXP_Users u with(nolock) ON u.ID = s.userid
        left join dbo.SubAddress sa with(nolock) on s.sub_id = sa.sub_id
        left join wuliu_express_time wet with(nolock) on wet.wuliuNo = sa.wuliuNo
        left join dbo.areainfo aa with(nolock) on ISNULL(s.kcAreaid,s.areaid)=aa.id
        left join dbo.areainfo aaa with(nolock) on s.areaid=aaa.id
        )as temp )SELECT
        *
        FROM
        selectTemp
        WHERE
        __row_number__ BETWEEN #{begin} AND #{end}
        ORDER BY
        __row_number__
    </select>


    <select id="recoverDiaoboSpendHourPage"
            resultType="com.jiuji.oa.stock.develivery.vo.res.RecoverDiaoboSpendHourPageResVO">
        SELECT s.expectTime as expectTime,s.tradeDate as outstockTime,S.areaid as areaid,a.area as areaName,rsub.areaid
        as kcAreaId,aa.area as kcAreaName,
        s.subtype ,b.sub_id as subId,p.product_name+' '+ISNULL(p.product_color,'') as productName,
        B.basket_count as basketCount ,B.price as price,S.yifuM as yifuM,S.sub_check as subCheck, B.basket_date as
        basketDate,
        B.basket_date as subDate,b.seller,S.delivery,k.imei,s.tradeDate1 as tradeCompleteTime, s.trader,
        reareaid,B.ppriceid,
        rs.wuliucompany,rs.wuliuNo,wet.express_generate_time,wet.express_receive_time,
        isnull(k.id,b.mkc_id2) as mkcid,S.sub_pay
        FROM dbo.recover_marketSubInfo B with(nolock)
        LEFT JOIN recover_marketInfo S with(nolock) ON B.sub_id=S.sub_id
        LEFT JOIN dbo.productinfo P with(nolock) ON B.ppriceid=P.ppriceid
        left join dbo.recover_mkc k with(nolock) on k.to_basket_id=B.basket_id
        left join recover_basket rb with(nolock) on rb.id=k.from_basket_id
        LEFT JOIN dbo.recover_sub rsub WITH(NOLOCK) ON rsub.sub_id=rb.sub_id
        left join RecoverSubAddress rs with(nolock) on s.sub_id = rs.sub_id
        left join wuliu_express_time wet with(nolock) on wet.wuliuNo = rs.wuliuNo
        LEFT JOIN dbo.recover_marketInfo rm with(nolock) ON rm.sub_id=s.sub_id
        LEFT JOIN areainfo a with(nolock) on a.id=S.areaid
        LEFT JOIN areainfo aa with(nolock) on aa.id=rsub.areaid
        <where>S.sub_check = 3 and b.sub_id is not null and s.sub_date > '2022-10-20 00:00:00' and b.isdel is NULL
            <if test="req.areaKind!=null and req.areaKind!=''">
                <choose>
                    <when test="req.areaKind==1">
                        and a.kind1=1
                    </when>
                    <otherwise>
                        and a.kind1!=1
                    </otherwise>
                </choose>
            </if>
            <if test="req.areaId != null and req.areaId.size > 0">
                and s.areaid in
                <foreach collection="req.areaId" index="index" item="area" separator="," open="(" close=")">
                    #{area}
                </foreach>
            </if>
            <if test="req.delivery != null and req.delivery!='' ">
                AND s.delivery = #{req.delivery}
            </if>
            <if test="req.startTime != null and req.endTime != null">
                and B.basket_date between #{req.startTime} and #{req.endTime}
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==1">
                and isnull(k.id,b.mkc_id2) = #{req.searchValue}
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==2">
                and (trim(p.product_name) + trim(isnull(p.product_color, ''))) like '%' + #{req.searchValue} + '%'
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==3">
                and B.sub_id = #{req.searchValue}
            </if>
        </where>
        order by s.sub_date desc
    </select>

    <select id="computeMobileDiaoboSpendHourNeedTime"
            resultType="com.jiuji.oa.stock.develivery.vo.res.MobileDiaoboSpendHourPageResVO">
        select m.id as diaoboId, m.toareaid, k.basket_id, m.dtime as diaoboTime
        from mkc_toarea m with(nolock) left join
        product_mkc k
        with (nolock)
        on m.mkc_id=k.id
            left join productinfo p
        with (nolock)
        on p.ppriceid=k.ppriceid left join insource i
        with (nolock)
        on
            k.insourceid=i.id left join joinMkcFee j
        with (nolock)
        on j.mkc_id=k.id LEFT JOIN Ok3w_qudao o
        WITH (nolock)
        ON
            o.id = k.insourceid2
            left join wuliu_express_time wet
        with (nolock)
        on wet.wuliuid = m.wuliuid
            left join areainfo fromarea
        with (nolock)
        on fromarea.id = m.areaid
            left join areainfo toarea
        with (nolock)
        on toarea.id = m.toareaid
            left join product_stock_out so
        with (nolock)
        on (so.related_id=m.id and so.related_type in (4,8) and
            isnull(so.is_delete,0)=0 and so.order_status != 1)
        where k.id is not null
          and k.kc_check != 4
          and m.areaid = 16
          and m.stats in (2
            , 3)
          and sendTime is not null
          and m.dtime
            > '2022-09-21 00:00:00'
          AND NOT EXISTS (SELECT 1 FROM dbo.diaobo_spend_hour_need_time ds WITH (NOLOCK) WHERE ds.bussiness_id = m.id)
    </select>

    <select id="computeAccessoryDiaoboSpendHourNeedTime"
            resultType="com.jiuji.oa.stock.develivery.vo.res.AccessoryDiaoboSpendHourPageResVO">
        select s.id as diaoboId, s.toareaid as toareaid, s.check_dtime, db.basket_id
        from diaobo_basket db with(nolock)
        left join diaobo_sub s
        with (nolock)
        on db.sub_id = s.id
            left join productinfo p
        with (nolock)
        on p.ppriceid = db.ppriceid
            left join wuliu_express_time wet
        with (nolock)
        on wet.wuliuid = s.wuliuid
            left join areainfo fromarea
        with (nolock)
        on fromarea.id = s.areaid
            left join areainfo toarea
        with (nolock)
        on toarea.id = s.toareaid
        where s.areaid = 16
          and s.stats in (3
            , 4)
          and s.send_dtime is not null
          and s.check_dtime
            > '2022-09-21 00:00:00'
          AND NOT EXISTS (SELECT 1 FROM dbo.diaobo_spend_hour_need_time ds WITH (NOLOCK) WHERE ds.bussiness_id = s.id)
    </select>

    <select id="orderDiaoboSpendHourPageCount" resultType="java.lang.Integer">
        select
        P.product_name+' '+ISNULL(P.product_color,'') productName,
        s.sub_to,
        s.areaid,
        s.sub_date,
        s.sub_check,
        s.userid,
        s.sub_mobile,
        s.yifuM,
        s.kcAreaid,
        s.sub_pay,
        s.subtype,
        s.tradeDate,
        s.tradeDate1,
        s.trader,
        s.delivery,
        b.*
        into #tempSub
        from dbo.sub s with(nolock)
        left join dbo.basket b with (nolock)
        on b.sub_id = s.sub_id
        left join areainfo a with(nolock) on a.id = s.areaid
        left join productinfo p with(nolock) ON b.ppriceid=p.ppriceid
        <where>s.sub_check = 3 and s.sub_id is not null and s.sub_date > '2022-09-21 00:00:00'
            <if test="req.areaKind!=null and req.areaKind!=''">
                <choose>
                    <when test="req.areaKind==1">
                        and a.kind1=1
                    </when>
                    <otherwise>
                        and a.kind1!=1
                    </otherwise>
                </choose>
            </if>
            <if test="req.areaId != null and req.areaId.size > 0">
                and s.areaid in
                <foreach collection="req.areaId" index="index" item="area" separator="," open="(" close=")">
                    #{area}
                </foreach>
            </if>
            <if test="req.delivery != null and req.delivery!='' ">
                AND s.delivery = #{req.delivery}
            </if>
            <if test="req.startTime != null and req.endTime != null">
                and s.sub_date between #{req.startTime} and #{req.endTime}
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==1">
                and p.ppriceid = #{req.searchValue}
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==2">
                and (trim(p.product_name) + trim(isnull(p.product_color, ''))) like '%' + #{req.searchValue} + '%'
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==3">
                and b.sub_id = #{req.searchValue}
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==3">
                and b.sub_id = #{req.searchValue}
            </if>
        </where>
        ;
        SELECT count(1) FROM
        #tempSub s
        LEFT JOIN dbo.product_mkc k with(nolock) on k.basket_id=s.basket_id
        LEFT JOIN dbo.BBSXP_Users u with(nolock) ON u.ID = s.userid
        left join dbo.SubAddress sa with(nolock) on s.sub_id = sa.sub_id
        left join wuliu_express_time wet with(nolock) on wet.wuliuNo = sa.wuliuNo
        left join dbo.areainfo aa with(nolock) on ISNULL(s.kcAreaid,s.areaid)=aa.id
        left join dbo.areainfo aaa with(nolock) on s.areaid=aaa.id
    </select>

    <select id="wuliuSpendHourPageV2" resultType="com.jiuji.oa.stock.develivery.vo.res.WuLiuSpendHourPageResVO">
        SELECT temp2.* from (SELECT MAX(temp.delivery) as delivery ,MAX( temp.diaoboType) as diaoboType,
        temp.wuliuId,temp.com,temp.paijianren,temp.nu,temp.sareaid,temp.saddress,temp.rareaid,
        temp.raddress,temp.wuliuStatus,temp.wCateId,temp.wutype,temp.danhaobind,temp.diaoboTime,
        MAX(temp.subDate) as sub_date ,temp.estimated_stocking_time,temp.actual_stocking_time,
        temp.estimated_shipping_time,temp.actual_shipping_time,temp.estimated_delivery_time,
        temp.actual_delivery_time,temp.estimated_receipt_time,temp.actual_receipt_time
        from (
        SELECT
        distinct
        s.delivery as delivery,
        ISNUll(wrr.product_stock_category,0) as diaoboType,
        w.id as wuliuId,
        w.com ,
        w.paijianren ,
        w.nu ,
        w.sareaid ,
        w.saddress ,
        w.rareaid ,
        w.raddress ,
        w.stats as wuliuStatus,
        w.wCateId ,
        w.wutype ,
        w.danhaobind ,
        s.sub_date as subDate,
        w.dtime as diaoboTime,
        wrr.estimated_stocking_time ,
        wrr.actual_stocking_time ,
        wrr.estimated_shipping_time ,
        wrr.actual_shipping_time ,
        wrr.estimated_delivery_time ,
        wrr.actual_delivery_time ,
        wrr.estimated_receipt_time ,
        wrr.actual_receipt_time
        from
        wuliu w with(nolock)
        left join wuliu_related_record wrr with(nolock) on wrr.wuliu_id = w.id
        left join diaobo_sub ds with(nolock) on ds.wuliuid = w.id
        left join diaobo_basket db with(nolock) on db.sub_id = ds.id
        left join sub s with(nolock) on s.sub_id = w.danhaobind and w.wutype in (4,6)
        left join basket b with(nolock) on b.sub_id = s.sub_id
        left join dbo.basket_extend be with(nolock) on (be.basket_id = db.basket_id and db.basket_type = 0)
        <where> wrr.status = 0
            <include refid="whereSql"/>
            <choose>
                <when test="req.diaoboType==2">
                    and ISNUll(wrr.product_stock_category,0) = #{req.diaoboType}
                </when>
                <when test="req.diaoboType==3">
                    and ISNUll(wrr.product_stock_category,0) = #{req.diaoboType}
                </when>
                <when test="req.diaoboType==0">
                    and ISNUll(wrr.product_stock_category,0) = #{req.diaoboType}
                </when>
                <when test="req.diaoboType==1">
                    and ISNUll(wrr.product_stock_category,0) = #{req.diaoboType}
                </when>
                <otherwise>
                </otherwise>
            </choose>
            <if test="req.delivery != null and req.delivery!='' ">
                AND (s.delivery = #{req.delivery})
            </if>
        </where>
        union
        SELECT
        distinct
        s.delivery as delivery,
        case
        when w.wutype in (1) and be.stock_method != 10 and be.stock_method_kind = 1 then 2
        when w.wutype in (1) and be.stock_method = 10 then 3
        when ((w.wutype = 1 and pm.basket_id is NOT NULL) or (w.wutype in (4,5,6,9)) ) then 1
        else 0
        end as diaoboType,
        w.id as wuliuId,
        w.com ,
        w.paijianren ,
        w.nu ,
        w.sareaid ,
        w.saddress ,
        w.rareaid ,
        w.raddress ,
        w.stats as wuliuStatus,
        w.wCateId ,
        w.wutype ,
        w.danhaobind ,
        s.sub_date as subDate,
        w.dtime as diaoboTime,
        wrr.estimated_stocking_time ,
        wrr.actual_stocking_time ,
        wrr.estimated_shipping_time ,
        wrr.actual_shipping_time ,
        wrr.estimated_delivery_time ,
        wrr.actual_delivery_time ,
        wrr.estimated_receipt_time ,
        wrr.actual_receipt_time
        from
        wuliu w with(nolock)
        left join wuliu_related_record wrr with(nolock) on wrr.wuliu_id = w.id
        left join mkc_toarea mt with(nolock) on mt.wuliuid = w.id
        left join product_mkc pm with(nolock) on pm.id = mt.mkc_id
        left join sub s with(nolock) on s.sub_id = w.danhaobind and w.wutype in (4,6)
        left join basket b with(nolock) on b.sub_id = s.sub_id
        left join dbo.basket_extend be with(nolock) on (be.basket_id = pm.basket_id)
        <where>wrr.status = 0
            <include refid="whereSql"/>
            <choose>
                <when test="req.diaoboType==2">
                    and ISNUll(wrr.product_stock_category,0) = #{req.diaoboType}
                </when>
                <when test="req.diaoboType==3">
                    and ISNUll(wrr.product_stock_category,0) = #{req.diaoboType}
                </when>
                <when test="req.diaoboType==0">
                    and ISNUll(wrr.product_stock_category,0) = #{req.diaoboType}
                </when>
                <when test="req.diaoboType==1">
                    and ISNUll(wrr.product_stock_category,0) = #{req.diaoboType}
                </when>
                <otherwise>
                </otherwise>
            </choose>
            <if test="req.delivery != null and req.delivery!='' ">
                AND (s.delivery = #{req.delivery})
            </if>
        </where>
        union
        SELECT
        distinct
        rmi.delivery as delivery,
        case
        when ((w.wutype = 1 and rk.to_basket_id is NOT NULL) or (w.wutype in (4,5,6,9)) ) then 1
        else 0
        end as diaoboType,
        w.id as wuliuId,
        w.com ,
        w.paijianren ,
        w.nu ,
        w.sareaid ,
        w.saddress ,
        w.rareaid ,
        w.raddress ,
        w.stats as wuliuStatus,
        w.wCateId ,
        w.wutype ,
        w.danhaobind ,
        rmi.sub_date as subDate,
        w.dtime as diaoboTime,
        wrr.estimated_stocking_time ,
        wrr.actual_stocking_time ,
        wrr.estimated_shipping_time ,
        wrr.actual_shipping_time ,
        wrr.estimated_delivery_time ,
        wrr.actual_delivery_time ,
        wrr.estimated_receipt_time ,
        wrr.actual_receipt_time
        from
        wuliu w with(nolock)
        left join wuliu_related_record wrr with(nolock) on wrr.wuliu_id = w.id
        left join recover_toarea rt WITH(NOLOCK) on rt.wuliuid = w.id
        left join recover_mkc rk WITH(NOLOCK) on rk.id = rt.mkc_id
        left join recover_marketInfo rmi with(nolock) on rmi.sub_id = w.danhaobind and w.wutype in (9)
        <where>
            <include refid="whereSql"/>
            <choose>
                <when test="req.diaoboType==0">
                    and ISNUll(wrr.product_stock_category,0) = #{req.diaoboType}
                </when>
                <when test="req.diaoboType==1">
                    and ISNUll(wrr.product_stock_category,0) = #{req.diaoboType}
                </when>
                <otherwise>
                </otherwise>
            </choose>
            <if test="req.delivery != null and req.delivery!='' ">
                AND (rmi.delivery = #{req.delivery})
            </if>
        </where>
        ) as temp GROUP by
        temp.wuliuId,temp.com,temp.paijianren,temp.nu,temp.sareaid,temp.saddress,temp.rareaid,
        temp.raddress,temp.wuliuStatus,temp.wCateId,temp.wutype,temp.danhaobind,temp.diaoboTime,
        temp.estimated_stocking_time,temp.actual_stocking_time,
        temp.estimated_shipping_time,temp.actual_shipping_time,temp.estimated_delivery_time,
        temp.actual_delivery_time,temp.estimated_receipt_time,temp.actual_receipt_time
        ) as temp2 order by temp2.wuliuId desc
    </select>

    <sql id="whereSql">
            <if test="req.sareaidList != null and req.sareaidList.size > 0">
                and w.sareaid in
                <foreach collection="req.sareaidList" index="index" item="temp" separator="," open="(" close=")">
                    #{temp}
                </foreach>
            </if>
            <if test="req.rareaidList != null and req.rareaidList.size > 0">
                and w.rareaid in
                <foreach collection="req.rareaidList" index="index" item="temp" separator="," open="(" close=")">
                    #{temp}
                </foreach>
            </if>
            <if test="req.cateIdList != null and req.cateIdList.size > 0">
                and w.wCateId in
                <foreach collection="req.cateIdList" index="index" item="temp" separator="," open="(" close=")">
                    #{temp}
                </foreach>
            </if>
            <if test="req.wuType != null and req.wuType!='' ">
                AND w.wuType = #{req.wuType}
            </if>

            <if test="req.wuliuStatus != null and req.wuliuStatus.size > 0">
                and w.stats in
                <foreach collection="req.wuliuStatus" index="index" item="temp" separator="," open="(" close=")">
                    #{temp}
                </foreach>
            </if>
            <if test="req.com != null and req.com.size > 0">
                and w.com in
                <foreach collection="req.com" index="index" item="temp" separator="," open="(" close=")">
                    #{temp}
                </foreach>
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==1">
                and w.id = #{req.searchValue}
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==2">
                and w.nu = #{req.searchValue}
            </if>
            <if test="req.startTime != null and req.endTime != null">
                and w.dtime between #{req.startTime} and #{req.endTime}
            </if>
    </sql>


    <select id="wuliuSpendHourPage" resultType="com.jiuji.oa.stock.develivery.vo.res.WuLiuSpendHourPageResVO">
        SELECT temp.* from (SELECT
        distinct
        ISNULL(s.delivery,rmi.delivery) as delivery,
        ISNULL(wrr.product_stock_category,0) as diaoboType,
        wrr.wuliu_id as wuliuId,
        w.com ,
        w.paijianren ,
        w.nu ,
        w.sareaid ,
        w.saddress ,
        w.rareaid ,
        w.raddress ,
        w.stats as wuliuStatus,
        w.wCateId ,
        w.wutype ,
        wrr.business_id as danhaobind ,
        wrr.business_type as businessType,
        ISNULL(s.sub_date,rmi.sub_date) as subDate,
        w.dtime as diaoboTime,
        wrr.estimated_stocking_time ,
        wrr.actual_stocking_time ,
        wrr.estimated_shipping_time ,
        wrr.actual_shipping_time ,
        wrr.estimated_delivery_time ,
        wrr.actual_delivery_time ,
        wrr.estimated_receipt_time ,
        DATEDIFF(MINUTE, w.dtime, wrr.actual_stocking_time) as actualStockingTimeTakeMinute,
        DATEDIFF(MINUTE, wrr.actual_stocking_time, wrr.actual_shipping_time) as actualDeliveryTimeTake,
        DATEDIFF(MINUTE, wrr.actual_delivery_time, wrr.actual_receipt_time) as actualReceiptTimeTake,
        wrr.actual_receipt_time
        from wuliu_related_record wrr
        left join wuliu w with(nolock) on wrr.wuliu_id = w.id
        left join diaobo_sub ds with(nolock) on ds.wuliuid = wrr.wuliu_id
        left join diaobo_basket db with(nolock) on db.sub_id = ds.id
        left join mkc_toarea mt with(nolock) on mt.wuliuid = wrr.wuliu_id
        left join product_mkc pm with(nolock) on pm.id = mt.mkc_id
        left join sub s with(nolock) on s.sub_id = w.danhaobind and w.wutype in (4,6)
        left join basket b with(nolock) on b.sub_id = s.sub_id
        left join recover_toarea rt WITH(NOLOCK) on rt.wuliuid = wrr.wuliu_id
        left join recover_mkc rk WITH(NOLOCK) on rk.id = rt.mkc_id
        left join recover_marketInfo rmi with(nolock) on rmi.sub_id = w.danhaobind and w.wutype in (9)
        left join dbo.basket_extend be with(nolock) on (be.basket_id = db.basket_id and db.basket_type = 0 or be.basket_id = pm.basket_id)
        <where>and ISNULL(wrr.status,0) = 0
            <if test="req.sareaidList != null and req.sareaidList.size > 0">
                and w.sareaid in
                <foreach collection="req.sareaidList" index="index" item="temp" separator="," open="(" close=")">
                    #{temp}
                </foreach>
            </if>
            <if test="req.rareaidList != null and req.rareaidList.size > 0">
                and w.rareaid in
                <foreach collection="req.rareaidList" index="index" item="temp" separator="," open="(" close=")">
                    #{temp}
                </foreach>
            </if>
            <if test="req.cateIdList != null and req.cateIdList.size > 0">
                and w.wCateId in
                <foreach collection="req.cateIdList" index="index" item="temp" separator="," open="(" close=")">
                    #{temp}
                </foreach>
            </if>
            <if test="req.wuType != null and req.wuType!='' ">
                AND w.wuType = #{req.wuType}
            </if>

            <if test="req.wuliuStatus != null and req.wuliuStatus.size > 0">
                and w.stats in
                <foreach collection="req.wuliuStatus" index="index" item="temp" separator="," open="(" close=")">
                    #{temp}
                </foreach>
            </if>
            <if test="req.com != null and req.com.size > 0">
                and w.com in
                <foreach collection="req.com" index="index" item="temp" separator="," open="(" close=")">
                    #{temp}
                </foreach>
            </if>
            <choose>
                <when test="req.diaoboType==2">
                    and ISNUll(wrr.product_stock_category,0) = #{req.diaoboType}
                </when>
                <when test="req.diaoboType==3">
                    and ISNUll(wrr.product_stock_category,0) = #{req.diaoboType}
                </when>
                <when test="req.diaoboType==0">
                    and ISNUll(wrr.product_stock_category,0) = #{req.diaoboType}
                </when>
                <when test="req.diaoboType==1">
                    and ISNUll(wrr.product_stock_category,0) = #{req.diaoboType}
                </when>
                <when test="req.diaoboType==4">
                    and ISNUll(wrr.product_stock_category,0) = #{req.diaoboType}
                </when>
                <otherwise>
                </otherwise>
            </choose>
            <if test="req.diaoboTypeList != null and req.diaoboTypeList.size() > 0">
                and ISNUll(wrr.product_stock_category,0) IN
                <foreach item="item" index="index" collection="req.diaoboTypeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.delivery != null and req.delivery!='' ">
                AND (s.delivery = #{req.delivery} or rmi.delivery = #{req.delivery})
            </if>
            <if test="req.deliveryList != null and req.deliveryList.size > 0">
                and ISNULL(s.delivery,rmi.delivery) in
                <foreach collection="req.deliveryList" index="index" item="temp" separator="," open="(" close=")">
                    #{temp}
                </foreach>
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==1">
                and wrr.wuliu_id = #{req.searchValue}
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==2">
                and w.nu = #{req.searchValue}
            </if>
            <if test="req.startTime != null and req.endTime != null">
                <choose>
                    <when test="req.timeType == 1">
                        and wrr.actual_delivery_time between #{req.startTime} and #{req.endTime}
                    </when>
                    <when test="req.timeType == 2">
                        and wrr.actual_shipping_time between #{req.startTime} and #{req.endTime}
                    </when>
                    <when test="req.timeType == 3">
                        and wrr.actual_receipt_time between #{req.startTime} and #{req.endTime}
                    </when>
                    <when test="req.timeType == 4">
                        and w.dtime between #{req.startTime} and #{req.endTime}
                    </when>
                    <otherwise>
                    </otherwise>
                </choose>
            </if>
            <if test="req.actualTimeGreaterThanType!=null and req.actualTimeGreaterThanValue!=null">
                <if test="req.actualTimeGreaterThanType == 1">
                    and wrr.actual_shipping_time > DATEADD(MINUTE, #{req.actualTimeGreaterThanValue}, wrr.actual_stocking_time)
                </if>
                <if test="req.actualTimeGreaterThanType == 2">
                    and wrr.actual_receipt_time > DATEADD(MINUTE, #{req.actualTimeGreaterThanValue}, wrr.actual_delivery_time)
                </if>
                <if test="req.actualTimeGreaterThanType == 3">
                    and wrr.actual_stocking_time > DATEADD(MINUTE, #{req.actualTimeGreaterThanValue}, w.dtime)
                </if>
            </if>
        </where>
        ) as temp order by temp.wuliuId desc
    </select>
    <select id="selectOrderStatusByTypeAndIds"
            resultType="com.jiuji.oa.stock.develivery.vo.res.OrderStatusResVO">
        <choose>
            <when test="businessType == 27 and subIdList != null and subIdList.size() > 0">
                select s.sub_id subId,s.sub_check status,
                case when s.sub_check = 0 then '未确认'
                when s.sub_check = 1 then '已确认'
                when s.sub_check = 2 then '已出库'
                when s.sub_check = 3 then '已完成'
                when s.sub_check = 4 then '已删除'
                when s.sub_check = 5 then '等待确认'
                when s.sub_check = 6 then '欠款'
                when s.sub_check = 7 then '待处理'
                when s.sub_check = 8 then '退订'
                when s.sub_check = 9 then '退款'
                else '' end statusName
                from sub s with(nolock)
                where s.sub_id in
                <foreach collection="subIdList" index="index" item="temp" separator="," open="(" close=")">
                    #{temp}
                </foreach>
            </when>
            <when test="businessType == 24 and subIdList != null and subIdList.size() > 0">
                select s.id subId,isnull(s.stats,0) status,
                case when isnull(s.stats,0) = 0 then '处理中'
                when s.stats = 1 then '已修好'
                when s.stats = 2 then '修不好'
                else '' end statusName
                from shouhou s
                where s.id in
                <foreach collection="subIdList" index="index" item="temp" separator="," open="(" close=")">
                    #{temp}
                </foreach>
            </when>
            <when test="businessType == 6 and subIdList != null and subIdList.size() > 0">
                select s.sub_id subId,s.sub_check status,
                case when s.sub_check = 0 then '未确认'
                when s.sub_check = 1 then '已确认'
                when s.sub_check = 2 then '已出库'
                when s.sub_check = 3 then '已完成'
                when s.sub_check = 4 then '已删除'
                when s.sub_check = 5 then '等待确认'
                when s.sub_check = 6 then '欠款'
                when s.sub_check = 7 then '待处理'
                when s.sub_check = 8 then '退订'
                when s.sub_check = 9 then '退款'
                else '' end statusName
                from recover_marketInfo s with(nolock)
                where s.sub_id in
                <foreach collection="subIdList" index="index" item="temp" separator="," open="(" close=")">
                    #{temp}
                </foreach>
            </when>
            <when test="businessType == 2 and subIdList != null and subIdList.size() > 0">
                select s.id subId,s.stats status,
                case when s.stats = 1 then '已提交'
                when s.stats = 2 then '已审核'
                when s.stats = 3 then '已发货'
                when s.stats = 4 then '已完成'
                when s.stats = 5 then '准备发货'
                when s.stats = 6 then '出库中'
                when s.stats = 0 then '已删除'
                else '' end statusName
                from diaobo_sub s with(nolock)
                where s.id in
                <foreach collection="subIdList" index="index" item="temp" separator="," open="(" close=")">
                    #{temp}
                </foreach>
            </when>
            <when test="businessType == 3 and subIdList != null and subIdList.size() > 0">
                select s.id subId,s.stats status,
                case when s.stats = 0 then '未发出'
                when s.stats = 1 then '准备发货'
                when s.stats = 2 then '已发出'
                when s.stats = 3 then '已收到'
                else '' end statusName
                from mkc_toarea s with(nolock)
                where s.id in
                <foreach collection="subIdList" index="index" item="temp" separator="," open="(" close=")">
                    #{temp}
                </foreach>
            </when>
            <when test="businessType == 5 and subIdList != null and subIdList.size() > 0">
                select s.id subId,s.status,
                case when s.status = 0 then '未发出'
                when s.status = 1 then '准备发货'
                when s.status = 2 then '已发出'
                when s.status = 3 then '已收到'
                else '' end statusName
                from recover_toarea s with(nolock)
                where s.id in
                <foreach collection="subIdList" index="index" item="temp" separator="," open="(" close=")">
                    #{temp}
                </foreach>
            </when>
            <otherwise>
                select s.sub_id subId,s.sub_check status,'' statusName from sub s with(nolock) where s.sub_id = -1
            </otherwise>
        </choose>
    </select>
</mapper>
