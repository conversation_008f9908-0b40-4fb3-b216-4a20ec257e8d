<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.develivery.mapper.DeveliveryNodePlanMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.stock.develivery.entity.DeveliveryNodePlan">
            <result property="id" column="id" jdbcType="INTEGER"/>
            <result property="area" column="area" jdbcType="VARCHAR"/>
            <result property="areaid" column="areaid" jdbcType="INTEGER"/>
            <result property="reachtime" column="reachTime" jdbcType="TIMESTAMP"/>
            <result property="lastsubbmittime" column="lastSubbmitTime" jdbcType="TIMESTAMP"/>
            <result property="nextnode" column="nextNode" jdbcType="INTEGER"/>
            <result property="index" column="Index" jdbcType="INTEGER"/>
            <result property="addtime" column="AddTime" jdbcType="TIMESTAMP"/>
            <result property="routeindex" column="RouteIndex" jdbcType="INTEGER"/>
            <result property="routeid" column="Routeid" jdbcType="INTEGER"/>
            <result property="days" column="days" jdbcType="TINYINT"/>
            <result property="planDate" column="plan_date" jdbcType="DATE"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,area,areaid,
        reachTime,lastSubbmitTime,nextNode,
        Index,AddTime,RouteIndex,
        Routeid,days,plan_date
    </sql>
</mapper>
