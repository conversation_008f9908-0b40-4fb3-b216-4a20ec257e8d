<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.exempt.mapper.ActualCompensatePriceMapper">

    <select id="getActualCompensatePricePage"
            resultType="com.jiuji.oa.stock.exempt.vo.res.ActualCompensatePricePageResVO">
        SELECT id,
        ppriceid,
        product_name,
        product_color,
        compensate_price,
        submit_userid,
        submit_username,
        is_del,
        create_time,
        update_time
        FROM actual_compensate_price a WITH (NOLOCK)
        <where>  is_del =0
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==1">
                and a.product_name like concat('%', #{req.searchValue},'%')
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==2">
                and a.ppriceid = #{req.searchValue}
            </if>
        </where>
    </select>

    <select id="selectRepairCategoryChildren" resultType="java.lang.String">
        SELECT ID
        from f_category_children('23')
    </select>

    <select id="getInpriceFromKc" resultType="java.math.BigDecimal">
        SELECT TOP 1 k.inprice
        FROM product_kc k  WITH (NOLOCK)
        WHERE k.ppriceid = #{ppriceid}
          AND k.areaid = #{areaId}
        ORDER BY k.id DESC
    </select>

    <select id="getInpriceFromKcExtra" resultType="java.math.BigDecimal">
        select top 1 k.inprice
        from product_kc k WITH (NOLOCK)
        left join dbo.areainfo a with (nolock)
        on a.id = k.areaid
        where
            k.ppriceid = #{ppriceid}
          and a.authorizeid = #{authorizeid}
          and a.xtenant = #{xtenant}
        order by
            k.lcount DESC
    </select>

    <select id="getInprice" resultType="java.math.BigDecimal">
        SELECT TOP 1 b.inprice
        FROM caigou_sub s with(nolock)
                 INNER JOIN dbo.caigou_basket b  WITH (NOLOCK) ON s.id = b.sub_id
                 INNER JOIN dbo.areainfo a  WITH (NOLOCK) ON a.id = s.areaid
        WHERE s.stats = 3
          AND b.ppriceid = #{ppriceid}
          AND a.kind1 = 1
          and s.subKind = 0
        ORDER BY s.id DESC
    </select>

    <select id="getLastCaigouPrice" resultType="java.math.BigDecimal">
        select top 1 b.inprice
        from dbo.caigou_basket b with(nolock)
        left join dbo.caigou_sub s
        with (nolock)
        on b.sub_id=s.id
        where s.[stats]=3
          and s.kinds='pj'
          and exists (select 1 from areainfo a with (nolock) where a.id=s.areaid
          and a.kind1=1)
          and b.ppriceid=#{ppriceid}
          and s.insourceid != 540
          and s.subKind = 0
        order by s.ruku_dtime desc
    </select>

    <select id="getLastCaigouPriceWx" resultType="java.math.BigDecimal">
        select top 1 b.inprice
        from dbo.caigou_basket b with(nolock)
        left join dbo.caigou_sub s
        with (nolock)
        on b.sub_id=s.id
        where s.[stats]=3
          and s.kinds='wx'
          and exists (select 1 from areainfo a with (nolock) where a.id=s.areaid
          and a.kind1=1)
          and b.ppriceid=#{ppriceid}
          and s.insourceid != 3903
          and s.subKind = 0
        order by s.ruku_dtime desc
    </select>

    <update id="updateReturnBasket">
        UPDATE return_basket
        SET price1=pi.costprice FROM dbo.return_basket AS rb JOIN dbo.productinfo AS pi
        ON pi.ppriceid = rb.ppriceid
        WHERE rb.sub_id=#{id}
    </update>

    <update id="updateReturnSub">
        UPDATE return_sub
        SET checkState=cc.c_ FROM (SELECT count(1) c_, sub_id FROM return_check WHERE sub_id=#{id} group by sub_id ) cc
        WHERE cc.sub_id=dbo.return_sub.id and return_sub.id=#{id}
    </update>


    <delete id="deleteCaigouBasket">
        DELETE FROM caigou_basket WHERE sub_id=#{id}
    </delete>

    <delete id="deleteReturnBasket">
        DELETE FROM return_basket WHERE sub_id=#{id}
    </delete>


</mapper>
