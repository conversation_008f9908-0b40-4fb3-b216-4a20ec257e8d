<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.exempt.mapper.InventoryExemptQuotaMapper">

    <insert id="insertBatch">
        INSERT INTO Inventory_exempt_quota(
        `exempt_month`,
        `exempt_type`,
        `areaid`,
        `cid`,
        `last_profit`,
        `initial_quota`,
        `current_quota`
        )
        VALUES
        <foreach collection="inventoryExemptQuotaList" item="item" index="index" separator=",">
            (
            #{item.exemptMonth},
            #{item.exemptType},
            #{item.areaid},
            #{item.cid},
            #{item.lastMonthProfit},
            #{item.initialQuota},
            #{item.currentLeftQuota}
            )
        </foreach>
    </insert>

    <select id="getLastMonthProfit" resultType="com.jiuji.oa.stock.exempt.dto.LastMonthProfit">
        SELECT temp.areaid, SUM(temp.profit) as lastMonthProfit
        from (
        select s.areaid,pp.cid ,(case when b.ismobile = 1 and z.mkc_id is not null and b.price &lt; isnull(k.staticPrice,b.price2) then 0
        when b.ismobile = 1 then b.price2 - isnull(k.staticPrice,b.price2)
        when b.ismobile = 0 and isnull(b.type,0) in (1,13,29,22) then 0
        when b.ismobile = 0 and pp.pLabel in (3,11) and isnull(b.inprice,b.price2) > b.price2 then 0
        else b.price2 - isnull(b.inprice,b.price2) end  )as profit
        from sub s with(nolock)
        left join basket b with(nolock) on s.sub_id = b.sub_id
        left join dbo.productinfo pp with(nolock) on pp.ppriceid = b.ppriceid
        left join category c with(nolock) on c.id=pp.cid
        left join product_mkc k with(nolock) on b.basket_id = k.basket_id
        left join view_zx_mkc z with(nolock) on z.mkc_id = k.id
        left join BBSXP_Users u with(nolock) on u.id = s.userid
        where ((s.yingfuM>0 and s.jidianM>0) or s.jidianM=0  ) and s.sub_check in (3) and s.tradeDate1 between #{lastBeginDay} and #{lastEndDay} and isnull(b.ischu,0) = 1
        and s.subtype &lt;&gt; 10 and isnull(b.isdel,0) = 0 and exists( select 1 from f_category_children(#{cidString}) f where f.id=pp.cid )
        and exists(SELECT 1 FROM dbo.F_SPLIT(#{allAreaIdString},',') f WHERE f.split_value = CAST(s.areaid AS VARCHAR(20)))) as temp
        group by temp.areaid;
    </select>

    <select id="selectProductInfo" resultType="com.jiuji.oa.nc.product.entity.ProductInfoEntity">
        SELECT ppriceid AS ppid, product_id, product_name, product_color, costprice, vipPrice, memberprice, cid, ismobile1 AS mobile, brandID, isdel AS delFlag, cidFamily, rank1, pLabel, barcode, product_id AS productId1, ismobile1 AS isMobile, bpic, pricefd, que, display, isdel, viewsWeek, ppriceid1 AS ppid1, config, viewsweekr, noPromotion, OEMPrice, barCode, vip2price, isbarCode, Scarcity, isSn, supportService, otherLimit, saleStartTime, sale_channel, vendor, isuse, barCodeCount
        FROM productinfo with(nolock)
        WHERE ppriceid in
        <foreach collection="ppriceidList" item="item" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>


</mapper>
