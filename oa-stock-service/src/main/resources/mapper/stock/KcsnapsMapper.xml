<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~
  ~      Copyright © 2006 - 2020 九机网 All Rights Reserved
  ~
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.stock.snapshot.mapper.KcsnapsMapper">

    <resultMap id="kcsnapsMap" type="com.jiuji.oa.stock.snapshot.entity.Kcsnaps">
        <id property="id" column="id"/>
        <result property="areaid" column="areaid"/>
        <result property="ppriceid" column="ppriceid"/>
        <result property="kccount" column="kcCount"/>
        <result property="kcprices" column="kcPrices"/>
        <result property="waycount" column="wayCount"/>
        <result property="wayprices" column="wayPrices"/>
        <result property="ordercount" column="orderCount"/>
        <result property="orderprices" column="orderPrices"/>
        <result property="losscount" column="lossCount"/>
        <result property="kctype" column="kcType"/>
        <result property="lossprices" column="lossPrices"/>
        <result property="shouhoucount" column="shouhouCount"/>
        <result property="shouhouprices" column="shouhouPrices"/>
        <result property="wxcount" column="wxCount"/>
        <result property="wxprices" column="wxPrices"/>
        <result property="allcount" column="allCount"/>
        <result property="allprices" column="allPrices"/>
        <result property="dtime" column="dtime"/>
        <result property="mkcId" column="mkc_id"/>
        <result property="kcCheck" column="kc_check"/>
        <result property="insourceid2" column="insourceid2"/>
        <result property="fanli" column="fanli"/>
        <result property="protectprice" column="protectPrice"/>
        <result property="modifyprice" column="modifyPrice"/>
        <result property="staticprice" column="staticPrice"/>
        <result property="curprotectprice" column="curProtectPrice"/>
        <result property="curmodifyprice" column="curModifyPrice"/>
        <result property="curfanli" column="curFanli"/>
        <result property="adjustprice" column="adjustPrice"/>
        <result property="alladjustprice" column="allAdjustPrice"/>
    </resultMap>
    <select id="pageByQuery" resultType="com.jiuji.oa.stock.snapshot.vo.KcsnapsVo">
        SELECT
        kc.id,
        kc.areaid,
        kc.ppriceid,
        kc.kcCount,
        kc.kcPrices,
        kc.wayCount,
        kc.wayPrices,
        kc.orderCount,
        kc.orderPrices,
        kc.lossCount,
        kc.kcType,
        kc.lossPrices,
        kc.shouhouCount,
        kc.shouhouPrices,
        kc.wxCount,
        kc.wxPrices,
        kc.allCount,
        kc.allPrices,
        kc.dtime,
        kc.mkc_id,
        kc.kc_check,
        kc.insourceid2,
        kc.fanli,
        kc.protectPrice,
        kc.modifyPrice,
        kc.staticPrice,
        kc.curProtectPrice,
        kc.curModifyPrice,
        kc.curFanli,
        kc.adjustPrice,
        kc.allAdjustPrice,
        pm.imei,
        pm.mouldFlag,
        pi.cidFamily
        FROM
        kcSnaps as kc with(nolock)
        left join product_mkc as pm with(nolock) on kc.mkc_id = pm.id
        left join productinfo as pi with(nolock) on kc.ppriceid = pi.ppriceid
        <where> kc.ppriceid is not null
            <if test="param.areaIdList != null and param.areaIdList.size() > 0">
                AND kc.areaid IN
                <foreach collection="param.areaIdList" item="tempArea" open="(" close=")" separator=",">
                    #{tempArea}
                </foreach>
            </if>
            <if test="param.kctype != null">
                AND kc.kcType = #{param.kctype}
            </if>
            <if test="param.ppriceidList != null and param.ppriceidList.size() > 0">
                AND kc.ppriceid IN
                <foreach collection="param.ppriceidList" item="ppid" open="(" close=")" separator=",">
                    #{ppid}
                </foreach>
            </if>
            <if test="param.queryDate != null and param.queryDate != ''">
                AND kc.dtime = #{param.queryDate}
            </if>
            <if test="param.imei != null and param.imei != ''">
                AND pm.imei = #{param.imei}
            </if>
            <if test="param.cids != null and param.cids.size() > 0">
                AND (
                <foreach collection="param.cids" item="cid" index="index">
                    <if test="index!=0">or </if>
                    pi.cidFamily like '%,' + convert(varchar(50),#{cid}) + ',%'
                </foreach>
                )
            </if>
            <if test="param.brandIds != null and param.brandIds.size() > 0">
                AND pi.brandID IN
                <foreach collection="param.brandIds" item="brandId" open="(" close=")" separator=",">
                    #{brandId}
                </foreach>
            </if>
            and isnull(allcount,0)!=0
        </where>
        order by  kc.id
    </select>


    <select id="pageByQueryCount" resultType="Integer">
        SELECT
        count (1)
        FROM
        kcSnaps as kc with(nolock)
        left join product_mkc as pm with(nolock) on kc.mkc_id = pm.id
        left join productinfo as pi with(nolock) on kc.ppriceid = pi.ppriceid
        <where> kc.ppriceid is not null
            <if test="param.areaIdList != null and param.areaIdList.size() > 0">
                AND kc.areaid IN
                <foreach collection="param.areaIdList" item="tempArea" open="(" close=")" separator=",">
                    #{tempArea}
                </foreach>
            </if>
            <if test="param.kctype != null">
                AND kc.kcType = #{param.kctype}
            </if>
            <if test="param.ppriceidList != null and param.ppriceidList.size() > 0">
                AND kc.ppriceid IN
                <foreach collection="param.ppriceidList" item="ppid" open="(" close=")" separator=",">
                    #{ppid}
                </foreach>
            </if>
            <if test="param.queryDate != null and param.queryDate != ''">
                AND kc.dtime = #{param.queryDate}
            </if>
            <if test="param.imei != null and param.imei != ''">
                AND pm.imei = #{param.imei}
            </if>
            <if test="param.cids != null and param.cids.size() > 0">
                AND (
                <foreach collection="param.cids" item="cid" index="index">
                    <if test="index!=0">or </if>
                    pi.cidFamily like '%,' + convert(varchar(50),#{cid}) + ',%'
                </foreach>
                )
            </if>
            <if test="param.brandIds != null and param.brandIds.size() > 0">
                AND pi.brandID IN
                <foreach collection="param.brandIds" item="brandId" open="(" close=")" separator=",">
                    #{brandId}
                </foreach>
            </if>
            and isnull(allcount,0)!=0
        </where>
    </select>


</mapper>
