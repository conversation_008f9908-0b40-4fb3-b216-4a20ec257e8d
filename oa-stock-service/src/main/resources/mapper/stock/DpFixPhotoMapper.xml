<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.nc.stock.mapper.DpFixPhotoMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.nc.stock.entity.DpFixPhoto">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="areaId" column="area_id" jdbcType="INTEGER"/>
            <result property="orders" column="orders" jdbcType="INTEGER"/>
            <result property="type" column="type" jdbcType="INTEGER"/>
            <result property="description" column="description" jdbcType="VARCHAR"/>
            <result property="createBy" column="create_by" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="INTEGER"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="fid" column="fid" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="cabinetType" column="cabinet_type" jdbcType="INTEGER"/>
            <result property="coordinateX" column="coordinate_x" jdbcType="DECIMAL"/>
            <result property="coordinateY" column="coordinate_y" jdbcType="DECIMAL"/>
            <result property="high" column="high" jdbcType="DECIMAL"/>
            <result property="withs" column="withs" jdbcType="DECIMAL"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="backMapId" column="back_map_id" jdbcType="INTEGER"/>
            <result property="colorType" column="color_type" jdbcType="INTEGER"/>
            <result property="standardFids" column="standard_fids" jdbcType="VARCHAR"/>
            <result property="auditStatus" column="audit_status" jdbcType="SMALLINT"/>
            <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
            <result property="uploadTime" column="upload_time" jdbcType="TIMESTAMP"/>
            <result property="reason" column="reason" jdbcType="VARCHAR"/>
            <result property="standardContent" column="standard_content" jdbcType="VARCHAR"/>
            <result property="isShop" column="is_shop" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,area_id,orders,
        type,description,create_by,
        create_time,update_by,update_time,
        fid,status,cabinet_type,
        coordinate_x,coordinate_y,high,
        withs,remark,name,
        back_map_id,color_type,standard_fids,
        audit_status,end_time,upload_time,
        reason,standard_content,is_shop
    </sql>
    <select id="getDpFixPhotoListByAreaIds" resultType="com.jiuji.oa.nc.stock.entity.DpFixPhoto">
        SELECT dfp.area_id areaId,dfp.machine_detail machineDetail, ifnull(dfp.machine_number,0) as machineNumber from dp_fix_photo dfp
        WHERE dfp.area_id in
        <foreach collection="areaIdList" item="areaId" separator="," open="(" close=")">
            #{areaId}
        </foreach>
        and dfp.status = 0
        and dfp.machine_detail is not null
    </select>
</mapper>
