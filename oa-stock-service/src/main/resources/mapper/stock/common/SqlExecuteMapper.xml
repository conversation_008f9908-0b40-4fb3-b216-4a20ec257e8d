<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.common.mapper.SqlExecuteMapper">

    <!-- updateAttachmentsName -->
    <update id="insertyProductbarcode">
        CREATE TABLE product_label_his (
                                           id bigint NOT NULL,
                                           start_time date NULL,
                                           end_time date NULL,
                                           create_userid int NULL,
                                           create_username nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
                                           create_time datetime NULL,
                                           remark nvarchar(255) COLLATE Chinese_PRC_CI_AS NULL,
                                           product_id int NULL,
                                           label int NULL,
                                           pre_his_id bigint NULL,
                                           sold int NULL,
                                           display_count int NULL,
                                           profits decimal(18,0) NULL,
                                           kc_count int NULL,
                                           other_kc_count int NULL,
                                           after_repair_count int NULL,
                                           PRIMARY KEY (id)
        )
    </update>

</mapper>
