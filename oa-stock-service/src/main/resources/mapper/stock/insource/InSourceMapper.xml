<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.insource.mapper.InSourceMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.jiuji.oa.stock.insource.entity.InSourceEntity">
    <id column="id" property="id"/>
    <result column="name" property="name"/>
    <result column="rank" property="rank"/>
    <result column="authorizeid" property="authorizeId"/>
  </resultMap>
  <select id="findByAuthorizeId" resultType="com.jiuji.oa.stock.insource.dto.InSourceDTO">
    SELECT
      i.id,
      i.name,
      i.authorizeid authorizeId,
      a.name authorizeName
    FROM
      insource i with(nolock),
      authorize a with(nolock)
    WHERE
      i.authorizeid = a.id
    <if test="authorizeId != null">
      and i.authorizeid = #{authorizeId}
    </if>
  </select>

</mapper>
