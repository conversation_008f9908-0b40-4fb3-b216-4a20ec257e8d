<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.returnpurchase.mapper.ReturnCaigouSubMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.stock.returnpurchase.entity.ReturnCaigouSub">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="returnSubId" column="return_sub_id" jdbcType="BIGINT"/>
            <result property="caigouSubId" column="caigou_sub_id" jdbcType="BIGINT"/>
            <result property="deleteFlag" column="is_delete" jdbcType="TINYINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,return_sub_id,caigou_sub_id,
        is_delete,create_time,update_time
    </sql>
    <select id="selectByReturnId" resultType="com.jiuji.oa.stock.returnpurchase.entity.ReturnCaigouSub">
        select a.id,a.return_sub_id,a.caigou_sub_id
        from return_caigou_sub a with(nolock)
        left join caigou_sub b with(nolock) on a.caigou_sub_id = b.id
        where b.stats != -1
        and a.return_sub_id = #{id}
    </select>
</mapper>
