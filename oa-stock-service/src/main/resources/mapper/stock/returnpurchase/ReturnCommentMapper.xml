<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.returnpurchase.mapper.ReturnCommentMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.stock.returnpurchase.entity.ReturnComment">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="subId" column="sub_id" jdbcType="INTEGER"/>
            <result property="comment" column="comment" jdbcType="VARCHAR"/>
            <result property="dtime" column="dtime" jdbcType="TIMESTAMP"/>
            <result property="inuser" column="inuser" jdbcType="VARCHAR"/>
            <result property="showtype" column="showType" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,sub_id,comment,
        dtime,inuser,showType
    </sql>
</mapper>
