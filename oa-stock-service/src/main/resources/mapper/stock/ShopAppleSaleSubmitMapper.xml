<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~
  ~      Copyright © 2006 - 2020 九机网 All Rights Reserved
  ~
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.nc.stock.mapper.ShopAppleSaleSubmitMapper">

    <select id="listImeiList" resultType="java.lang.String">
        select b.imei
        from t_shop_appleSale_submit a
                 left join t_shop_appleSale_in_stock b
                           on a.sn = b.sn
        where a.submit_time >= NOW() - INTERVAL 60 DAY   and  a.is_oa = 1 and a.is_submit = 1 and a.submit_status in (1,2,3)
          and a.submit_time !='2024-09-04 14:26:30.0'
        order by a.submit_time desc
    </select>

    <select id="getShopAppleInfoByArea" resultType="com.jiuji.oa.nc.stock.vo.ShopAppleInfoVO">
        SELECT
            shopid,
            shop_type,
            apple_account,
            apple_pwd,
            lat as latitude,
            long as longitude
        FROM
            t_shop_info
        where shopid = #{area}
    </select>
    <select id="getExistFlagByImei" resultType="com.jiuji.oa.nc.stock.vo.ShopAppleInfoVO">
        select COUNT(1) as needUploadFlag
        from t_shop_appleSale_in_stock s
        where s.imei = #{imei}
    </select>
    <select id="getImeiBySn" resultType="java.lang.String">
        select s.imei as needUploadFlag
        from t_shop_appleSale_in_stock s
        where s.sn = #{sn}
    </select>
</mapper>
