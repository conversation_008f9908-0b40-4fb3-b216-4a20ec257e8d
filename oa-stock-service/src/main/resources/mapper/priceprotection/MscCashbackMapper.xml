<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.priceprotection.mapper.MscCashbackMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.stock.priceprotection.entity.MscCashback">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="productCount" column="product_count" jdbcType="INTEGER"/>
            <result property="cashbackAmount" column="cashback_amount" jdbcType="DECIMAL"/>
            <result property="inuser" column="inuser" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="isDel" column="is_del" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,product_count,
        cashback_amount,cashback_settlement_ids,inuser,
        create_time,update_time,is_del,
        msc_cashback_rv
    </sql>
    <select id="selectMscCashbackPage"
            resultType="com.jiuji.oa.stock.priceprotection.vo.res.MscCashbackListRes">
        select
        mc.id,
        mc.product_count,
        mc.cashback_amount,
        mc.inuser,
        mc.create_time
        from msc_cashback mc with(nolock)
        where mc.id in (
            select
            m.id
            from msc_cashback m with(nolock)
            left join msc_cashback_product mcp with(nolock) on m.id = mcp.msc_cashback_id and mcp.is_del = 0
            left join productinfo p with(nolock) on mcp.sku_id = p.ppriceid
            where isnull(m.is_del,0) = 0
            <if test="req.seachWord != null and req.seachWord != ''">
                <if test="req.searchKey == 1">
                    and p.product_name like concat('%',#{req.seachWord},'%')
                </if>
                <if test="req.searchKey == 2">
                    and p.product_id = #{req.seachWord}
                </if>
                <if test="req.searchKey == 3">
                    and mcp.sku_id = #{req.seachWord}
                </if>
                <if test="req.searchKey == 4">
                    and mcp.merchant_sku_id = #{req.seachWord}
                </if>
                <if test="req.searchKey == 5">
                    and mcp.sn like concat('%',#{req.seachWord},'%')
                </if>
                <if test="req.searchKey == 6">
                    and m.id = #{req.seachWord}
                </if>
                <if test="req.searchKey == 7">
                    and m.inuser_id = #{req.seachWord}
                </if>
            </if>
            <if test="req.qudaoId != null">
                and mcp.qudao_id = #{req.qudaoId}
            </if>
            <if test="req.startTime != null and req.endTime != null">
                and m.create_time between #{req.startTime} and #{req.endTime}
            </if>
        )
    </select>
</mapper>
