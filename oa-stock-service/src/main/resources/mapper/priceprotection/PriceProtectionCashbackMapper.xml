<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.priceprotection.mapper.PriceProtectionCashbackMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.stock.priceprotection.entity.PriceProtectionCashback">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="title" column="title" jdbcType="VARCHAR"/>
            <result property="qudaoId" column="qudao_id" jdbcType="INTEGER"/>
            <result property="policyType" column="policy_type" jdbcType="TINYINT"/>
            <result property="cashbackType" column="cashback_type" jdbcType="TINYINT"/>
            <result property="areaid" column="areaid" jdbcType="INTEGER"/>
            <result property="startDate" column="start_date" jdbcType="DATE"/>
            <result property="endDate" column="end_date" jdbcType="DATE"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="estimateDate" column="estimate_date" jdbcType="DATE"/>
            <result property="cashbackSettlementId" column="cashback_settlement_id" jdbcType="INTEGER"/>
            <result property="cashbackAmout" column="cashback_amout" jdbcType="DECIMAL"/>
            <result property="settlementAmout" column="settlement_amout" jdbcType="DECIMAL"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="quantity" column="quantity" jdbcType="INTEGER"/>
            <result property="inuser" column="inuser" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,title,qudao_id,
        policy_type,cashback_type,areaid,
        start_date,end_date,status,
        estimate_date,cashback_settlement_id,cashback_amout,
        settlement_amout,remark,
        create_time,update_time,is_del,
        price_protection_cashback_rv,quantity,inuser,is_auto_settle,brand_id
    </sql>

    <update id="handleDoingStatus">
        UPDATE price_protection_cashback SET status = 1 WHERE is_del = 0 AND status = 0 AND CAST(GETDATE() AS DATE) BETWEEN start_date AND end_date
    </update>
    <update id="handleEndedStatus">
        UPDATE price_protection_cashback SET status = 2 WHERE is_del = 0 AND status = 1 AND CAST(GETDATE() AS DATE) > end_date
    </update>

    <select id="getPage" resultType="com.jiuji.oa.stock.priceprotection.vo.res.PriceProtectionListRes">
        select ppc.id,
               ppc.title,
               ppc.policy_type,
               ppc.cashback_type,
               ppc.areaid,
               ppc.start_date,
               ppc.end_date,
               ppc.create_time,
               ppc.status,
               ppc.cashback_settlement_id,
               ppc.settlement_amout,
               a.area,
               ppc.qudao_id qudaoId,
               ppc.is_auto_settle,
               ppc.brand_id,
               CONCAT(owq.company_jc,'(',ppc.qudao_id,')') qudaoJc
        from price_protection_cashback ppc with(nolock)
        left join Ok3w_qudao owq with(nolock) on ppc.qudao_id = owq.id
        left join areainfo a with(nolock) on ppc.areaid = a.id
        left join areainfo au with(nolock) on ppc.current_area_id = au.id
        <where>
            and ppc.is_del = 0
            <if test="req.status != null and req.status.size > 0">
                and ppc.status in
                <foreach collection="req.status" item="item" separator="," open="(" close=")" >
                    #{item}
                </foreach>
            </if>
            <if test="req.areaIds != null and req.areaIds.size > 0">
                and ppc.areaid in
                <foreach collection="req.areaIds" item="item" separator="," open="(" close=")" >
                    #{item}
                </foreach>
            </if>
            <if test="req.policyType != null and req.policyType != ''">
                and ppc.policy_type = #{req.policyType}
            </if>
            <if test="req.cashbackType != null and req.cashbackType.size > 0">
                and ppc.cashback_type in
                <foreach collection="req.cashbackType" item="item" separator="," open="(" close=")" >
                    #{item}
                </foreach>
            </if>
            <if test="req.qudaoId != null and req.qudaoId != ''">
                and ppc.qudao_id = #{req.qudaoId}
            </if>
            <choose>
                <when test="req.searchKey == 1 and req.seachWord != null and req.seachWord != ''">
                    and ppc.title like CONCAT('%',#{req.seachWord},'%')
                </when>
                <when test="req.searchKey == 2 and req.seachWord != null and req.seachWord != ''">
                    and ppc.cashback_settlement_id = #{req.seachWord}
                </when>
                <when test="req.searchKey == 3 and req.seachWord != null and req.seachWord != ''">
                    and ppc.inuser = #{req.seachWord}
                </when>
            </choose>
            <if test="req.searchTime == 1 and req.startTime != null and req.startTime != '' and req.endTime != null and req.endTime != ''">
                and ppc.create_time between #{req.startTime} and #{req.endTime}
            </if>
            <if test="req.searchTime == 2 and req.startTime != null and req.startTime != '' and req.endTime != null and req.endTime != ''">
                and ppc.estimate_date between #{req.startTime} and #{req.endTime}
            </if>
            <if test="req.authorizeId != null and req.authorizeId != 0">
                and au.authorizeid = #{req.authorizeId}
            </if>
        </where>
    </select>
    <select id="getProductMkcByRule"
            resultType="com.jiuji.oa.stock.priceprotection.vo.res.PriceProtectionProductMkc">
        select m.id mkcId,
               m.ppriceid skuId,
               m.imei,
               m.inbeihuoprice inbeihuoPrice,
               m.kc_check,
               m.staticPrice,
               isnull(m.mouldFlag,0) mouldFlag,
               p.product_name,
               p.product_color,
               a.area,
               b.name brandName
        from product_mkc m with(nolock)
        left join productinfo p with(nolock) on m.ppriceid = p.ppriceid
        left join areainfo a with(nolock) on a.id = m.areaid
        left join brand b with(nolock) on p.brandID = b.id
        <include refid="whereSqlByRule"></include>
        order by m.id desc
    </select>
    <select id="getPageCount" resultType="java.lang.Integer">
        select * into #category_price_protection from
        (
        select
        id,
        pr.price_protection_cashback_id,
        value cid
        from
        price_protection_cashback_rules pr
        CROSS APPLY STRING_SPLIT(pr.category_ids ,',')
        where pr.is_del = 0 ) t;
        select * into #brand_price_protection from
        (
        select id,pr.price_protection_cashback_id, value bid
        from price_protection_cashback_rules pr
        CROSS APPLY STRING_SPLIT(pr.brand_ids , ',')
        where pr.is_del = 0 ) t;
        select * into #product_price_protection from
        (
        select id,pr.price_protection_cashback_id, value product_id
        from price_protection_cashback_rules pr
        CROSS APPLY STRING_SPLIT(pr.product_ids , ',')
        where pr.is_del = 0 and pr.product_range = 3
        ) t;
        select * into #ppid_price_protection from
        (
        select id,pr.price_protection_cashback_id, value ppid
        from price_protection_cashback_rules pr
        CROSS APPLY STRING_SPLIT(pr.product_ids , ',')
        where pr.is_del = 0 and pr.product_range = 4
        ) t;

        select count(*) from (
        select
        ROW_NUMBER () OVER ( ORDER BY ppc.id desc ) AS __row_number__,
        ppc.id,
        ppc.title,
        ppc.policy_type,
        ppc.cashback_type,
        ppc.areaid,
        ppc.start_date,
        ppc.end_date,
        ppc.create_time,
        ppc.status,
        ppc.cashback_settlement_id,
        ppc.settlement_amout,
        a.area,
        ppc.is_auto_settle,
        ppc.brand_id,
        ppc.qudao_id qudaoId,
        ppc.estimate_date estimateDate,
        CONCAT(owq.company_jc,'(',ppc.qudao_id,')') qudaoJc
        from price_protection_cashback ppc with(nolock)
        left join Ok3w_qudao owq with(nolock) on ppc.qudao_id = owq.id
        left join areainfo a with(nolock) on ppc.areaid = a.id
        left join areainfo au with(nolock) on ppc.current_area_id = au.id
        <where>
            <include refid="whereSql" />
        </where>
        ) t;

        drop table #category_price_protection,#brand_price_protection,#product_price_protection,#ppid_price_protection;
    </select>
    <select id="getList" resultType="com.jiuji.oa.stock.priceprotection.vo.res.PriceProtectionListRes">
        select * into #category_price_protection from
        (
        select
        id,
        pr.price_protection_cashback_id,
        value cid
        from
        price_protection_cashback_rules pr
        CROSS APPLY STRING_SPLIT(pr.category_ids ,',')
        where pr.is_del = 0 ) t
        select * into #brand_price_protection from
        (
        select id,pr.price_protection_cashback_id, value bid
        from price_protection_cashback_rules pr
        CROSS APPLY STRING_SPLIT(pr.brand_ids , ',')
        where pr.is_del = 0 ) t
        select * into #product_price_protection from
        (
        select id,pr.price_protection_cashback_id, value product_id
        from price_protection_cashback_rules pr
        CROSS APPLY STRING_SPLIT(pr.product_ids , ',')
        where pr.is_del = 0 and pr.product_range = 3
        ) t
        select * into #ppid_price_protection from
        (
        select id,pr.price_protection_cashback_id, value ppid
        from price_protection_cashback_rules pr
        CROSS APPLY STRING_SPLIT(pr.product_ids , ',')
        where pr.is_del = 0 and pr.product_range = 4
        ) t

        select * from (
        select
            ROW_NUMBER () OVER ( ORDER BY ppc.id desc ) AS __row_number__,
            ppc.id,
            ppc.title,
            ppc.policy_type,
            ppc.cashback_type,
            ppc.areaid,
            ppc.start_date,
            ppc.end_date,
            ppc.create_time,
            ppc.status,
            ppc.cashback_settlement_id,
            ppc.settlement_amout,
            a.area,
            ppc.is_auto_settle,
            ppc.brand_id,
            ppc.qudao_id qudaoId,
            ppc.estimate_date estimateDate,
            CONCAT(owq.company_jc,'(',ppc.qudao_id,')') qudaoJc
        from price_protection_cashback ppc with(nolock)
            left join Ok3w_qudao owq with(nolock) on ppc.qudao_id = owq.id
            left join areainfo a with(nolock) on ppc.areaid = a.id
            left join areainfo au with(nolock) on ppc.current_area_id = au.id
        <where>
            <include refid="whereSql" />
        </where>
        ) t where __row_number__ BETWEEN #{begin} AND #{end}

        drop table #category_price_protection,#brand_price_protection,#product_price_protection,#ppid_price_protection
    </select>
    <select id="getProductMkcCountByRule" resultType="java.lang.Long">
        select count(m.id)
        from product_mkc m with(nolock)
        left join productinfo p with(nolock) on m.ppriceid = p.ppriceid
        left join areainfo a with(nolock) on a.id = m.areaid
        left join brand b with(nolock) on p.brandID = b.id
        <include refid="whereSqlByRule"></include>
    </select>
    <select id="selectPolicyOrderInfoList" resultType="com.jiuji.oa.stock.priceprotection.vo.req.PolicyOrderInfo">
        SELECT CS.policy_order_id as id , COUNT(csi.id) as quantity ,SUM(csi.SettlementPrice)  as cashbackAmout
        FROM dbo.CashbackSettlement cs with(nolock )
        inner join dbo.CashbackSettlementItem csi with(nolock ) on cs.id=csi.SettlementId
        <where>
            and isnull(cs.isdel,0) = 0
            and cs.policy_order_id in
            <foreach collection="idList" item="item" separator="," open="(" close=")" >
                #{item}
            </foreach>
        </where>
     GROUP BY CS.policy_order_id
    </select>
    <select id="selectCashbackSettlementItemMkcIdList" resultType="com.jiuji.oa.stock.priceprotection.vo.res.PriceProtectionProductMkc">
        SELECT csi.MkcId  as mkcId,
               a.area,
               p.product_name,
               p.product_color,
               p.ppriceid as skuId,
               mkc.imei,
               b.name as brandName,
               mkc.inbeihuoprice,
               mkc.kc_check,
               csi.SettlementPrice as cashbackAmount,
               mkc.mouldFlag
        FROM dbo.CashbackSettlementItem csi with (nolock)
         left join dbo.product_mkc mkc with (nolock) on mkc.id = csi.MkcId
            left join dbo.productinfo p with (nolock) on p.ppriceid = mkc.ppriceid
            left join dbo.brand b with(nolock ) on b.id = p.brandID
            left join dbo.areainfo a with (nolock) on a.id = mkc.areaid
        where csi.SettlementId =#{settlementId}
    </select>
    <sql id="whereSql">
        and ppc.is_del = 0
        <if test="req.status != null and req.status.size > 0">
            and ppc.status in
            <foreach collection="req.status" item="item" separator="," open="(" close=")" >
                #{item}
            </foreach>
        </if>
        <if test="req.areaIds != null and req.areaIds.size > 0">
            and ppc.areaid in
            <foreach collection="req.areaIds" item="item" separator="," open="(" close=")" >
                #{item}
            </foreach>
        </if>
        <if test="req.brandIds != null and req.brandIds.size > 0">
            and ppc.id in (
            select price_protection_cashback_id from #brand_price_protection cp with(nolock) where bid in
            <foreach collection="req.brandIds" item="item" separator="," open="(" close=")" >
                #{item}
            </foreach>
            union all
            select price_protection_cashback_id from #product_price_protection pp with(nolock)
            left join productinfo p with(nolock) on p.product_id = pp.product_id
            where p.brandID in
            <foreach collection="req.brandIds" item="item" separator="," open="(" close=")" >
                #{item}
            </foreach>
            union all
            select price_protection_cashback_id from #ppid_price_protection ppp with(nolock)
            left join productinfo p with(nolock) on p.ppriceid = ppp.ppid
            where p.brandID in
            <foreach collection="req.brandIds" item="item" separator="," open="(" close=")" >
                #{item}
            </foreach>
            )
        </if>
        <if test="req.cids != null and req.cids.size > 0">
            and ppc.id in (
            select price_protection_cashback_id from #category_price_protection cp with(nolock) where cid in
            <foreach collection="req.cids" item="item" separator="," open="(" close=")" >
                #{item}
            </foreach>
            union all
            select price_protection_cashback_id from #product_price_protection pp with(nolock)
            left join productinfo p with(nolock) on p.product_id = pp.product_id
            where p.cid in
            <foreach collection="req.cids" item="item" separator="," open="(" close=")" >
                #{item}
            </foreach>
            union all
            select price_protection_cashback_id from #ppid_price_protection ppp with(nolock)
            left join productinfo p with(nolock) on p.ppriceid = ppp.ppid
            where p.cid in
            <foreach collection="req.cids" item="item" separator="," open="(" close=")" >
                #{item}
            </foreach>
            )
        </if>
        <if test="req.policyType != null and req.policyType != ''">
            and ppc.policy_type = #{req.policyType}
        </if>
        <if test="req.cashbackType != null and req.cashbackType.size > 0">
            and ppc.cashback_type in
            <foreach collection="req.cashbackType" item="item" separator="," open="(" close=")" >
                #{item}
            </foreach>
        </if>
        <if test="req.qudaoId != null and req.qudaoId != ''">
            and ppc.qudao_id = #{req.qudaoId}
        </if>
        <if test="req.writeOff != null ">
            and exists(select 1 from dbo.CashbackSettlement cs with(nolock ) where cs.Id =ppc.cashback_settlement_id and cs.arrivalState = #{req.writeOff})
        </if>
        <choose>
            <when test="req.searchKey == 1 and req.seachWord != null and req.seachWord != ''">
                and ppc.title like CONCAT('%',#{req.seachWord},'%')
            </when>
            <when test="req.searchKey == 2 and req.seachWord != null and req.seachWord != ''">
                and ppc.cashback_settlement_id = #{req.seachWord}
            </when>
            <when test="req.searchKey == 3 and req.seachWord != null and req.seachWord != ''">
                and ppc.inuser = #{req.seachWord}
            </when>
            <when test="req.searchKey == 4 and req.seachWord != null and req.seachWord != ''">
                and ppc.id = #{req.seachWord}
            </when>
        </choose>
        <if test="req.searchTime == 1 and req.startTime != null and req.startTime != '' and req.endTime != null and req.endTime != ''">
            and ppc.create_time between #{req.startTime} and #{req.endTime}
        </if>
        <if test="req.searchTime == 2 and req.startTime != null and req.startTime != '' and req.endTime != null and req.endTime != ''">
            and ppc.estimate_date between #{req.startTime} and #{req.endTime}
        </if>
        <if test="req.authorizeId != null and req.authorizeId != 0">
            and au.authorizeid = #{req.authorizeId}
        </if>
    </sql>
    <sql id="whereSqlByRule">
        <where>
            and m.imei is not null
            <if test="req.qudaoId != null and req.qudaoId != ''">
                and m.insourceid2 = #{req.qudaoId}
            </if>
            <if test="req.timeType == 1 and req.startTime != null and req.endTime != null">
                and m.inbeihuodate between #{req.startTime} and #{req.endTime}
            </if>
            <if test="req.timeType == 2 and req.startTime != null and req.endTime != null">
                and m.imeidate between #{req.startTime} and #{req.endTime}
            </if>
            <if test="req.timeType == 3 and req.startTime != null and req.endTime != null">
                and exists( select 1 from sub s with(nolock)
                left join basket b with(nolock) on s.sub_id = b.sub_id
                where b.basket_id = m.basket_id and s.tradeDate1 between #{req.startTime} and #{req.endTime}
                <if test="req.areaIdList != null and req.areaIdList.size > 0">
                    and s.areaid in
                    <foreach collection="req.areaIdList" item="it" separator="," open="(" close=")">
                        #{it}
                    </foreach>
                </if>
                )
            </if>
            <if test="req.brandIdList != null and req.brandIdList.size > 0">
                and p.brandID in
                <foreach collection="req.brandIdList" item="it" separator="," open="(" close=")">
                    #{it}
                </foreach>
            </if>
            <if test="req.categoryIds != null and req.categoryIds != ''">
                and exists( select 1 from f_category_children(#{req.categoryIds}) f where f.id=p.cid )
            </if>
            <if test="req.productRange == 3 and req.productRangeIdList != null and req.productRangeIdList.size > 0">
                and p.product_id in
                <foreach collection="req.productRangeIdList" item="it" separator="," open="(" close=")">
                    #{it}
                </foreach>
            </if>
            <if test="req.productRange == 4 and req.productRangeIdList != null and req.productRangeIdList.size > 0">
                and m.ppriceid in
                <foreach collection="req.productRangeIdList" item="it" separator="," open="(" close=")">
                    #{it}
                </foreach>
            </if>
            <if test="req.kcCheckList != null and req.kcCheckList.size > 0">
                and m.kc_check in
                <foreach collection="req.kcCheckList" item="it" separator="," open="(" close=")">
                    #{it}
                </foreach>
            </if>
            <if test="req.productType == 1">
                and isnull(m.mouldFlag,0) = 1
            </if>
            <if test="req.productType == 2">
                and isnull(m.mouldFlag,0) = 0
            </if>
            <if test="req.authorizeId != null and req.authorizeId != 0">
                and a.authorizeid = #{req.authorizeId}
            </if>
        </where>
    </sql>
    <select id="getProductMkcByPolicyIdAndRulesId"
                  resultType="com.jiuji.oa.stock.priceprotection.vo.res.PriceProtectionProductMkcDwd">
        SELECT
            d.mkc_id,
            d.cashback_id,
            d.cashback_rule_id,
            d.area_id,
            d.area,
            d.brand_name,
            d.product_name,
            d.product_color,
            d.pprice_id,
            d.imei,
            d.purchase_price,
            d.market_price,
            d.kc_check,
            d.mould_flag,
            d.single_value,
            d.single_count,
            a.authorizeid
        FROM ${dwsPrefix}.dwd_product_mkc_policy_match_detail d
        LEFT JOIN ${dwsPrefix}.dim_area_info a on a.id = d.area_id
        where 1 = 1
        AND d.tenant_id = #{tenantId}
        AND a.tenant_id = #{tenantId}
        <if test="rulesIdList != null and rulesIdList.size > 0">
            AND d.cashback_rule_id in
            <foreach collection="rulesIdList" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>

        <if test="authorizeId != null">
            AND a.authorizeid = #{authorizeId}
        </if>
        order by d.mkc_id desc
    </select>

    <select id="getProductMkcByPolicyIdAndRulesIdIsPage"
                resultType="com.jiuji.oa.stock.priceprotection.vo.res.PriceProtectionProductMkcDwd">
            SELECT
            d.mkc_id,
            d.cashback_id,
            d.cashback_rule_id,
            d.single_value,
            d.single_count
        FROM ${dwsPrefix}.dwd_product_mkc_policy_match_detail d
        LEFT JOIN ${dwsPrefix}.dim_area_info a on a.id = d.area_id
        where 1 = 1
        AND d.tenant_id = #{tenantId}
        AND a.tenant_id = #{tenantId}
        <if test="rulesIdList != null and rulesIdList.size > 0">
            AND d.cashback_rule_id in
            <foreach collection="rulesIdList" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>

        <if test="authorizeId != null">
            AND a.authorizeid = #{authorizeId}
        </if>
        order by d.mkc_id desc
    </select>

    <select id="selectCashbackSettlementItemMkcIdListV1"
                     resultType="com.jiuji.oa.stock.priceprotection.vo.res.PriceProtectionProductMkc">
        SELECT csi.MkcId  as mkcId,
               a.area,
               p.product_name,
               p.product_color,
               p.ppriceid as skuId,
               mkc.imei,
               b.name as brandName,
               mkc.inbeihuoprice,
               mkc.kc_check,
               csi.SettlementPrice as cashbackAmount,
               mkc.mouldFlag,
               csi.SettlementId as cashbackSettlementId
        FROM dbo.CashbackSettlementItem csi with (nolock)
         left join dbo.product_mkc mkc with (nolock) on mkc.id = csi.MkcId
            left join dbo.productinfo p with (nolock) on p.ppriceid = mkc.ppriceid
            left join dbo.brand b with(nolock ) on b.id = p.brandID
            left join dbo.areainfo a with (nolock) on a.id = mkc.areaid
        where csi.SettlementId in
        <foreach collection="settlementIdList" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>
    <select id="initPriceProtectionCashbackSettle" resultMap="BaseResultMap">
        SELECT
            ppc.id,
            ppc.title,
            ppc.qudao_id,
            ppc.policy_type,
            ppc.cashback_type,
            ppc.areaid,
            ppc.start_date,
            ppc.end_date,
            ppc.status,
            ppc.estimate_date,
            ppc.cashback_settlement_id,
            ppc.cashback_amout,
            ppc.settlement_amout,
            ppc.remark,
            ppc.create_time,
            ppc.update_time,
            ppc.is_del,
            ppc.quantity,
            ppc.inuser,
            ppc.is_auto_settle,
            ppc.brand_id
        FROM
            price_protection_cashback ppc WITH(nolock)
        WHERE ISNULL(ppc.cashback_settlement_id, 0) != 0
    </select>

    <update id="updateSettlementAmout">
        update price_protection_cashback set settlement_amout = #{settlementAmoutSum} where id = #{priceProtectionCashbackId}
    </update>

    <select id="autoCreatePriceProtectionCashbackSettle" resultMap="BaseResultMap">
        SELECT
            ppc.id,
            ppc.title,
            ppc.qudao_id,
            ppc.policy_type,
            ppc.cashback_type,
            ppc.areaid,
            ppc.start_date,
            ppc.end_date,
            ppc.status,
            ppc.estimate_date,
            ppc.cashback_settlement_id,
            ppc.cashback_amout,
            ppc.settlement_amout,
            ppc.remark,
            ppc.create_time,
            ppc.update_time,
            ppc.is_del,
            ppc.quantity,
            ppc.is_auto_settle,
            ppc.brand_id,
            ppc.inuser
        FROM
            price_protection_cashback ppc WITH(nolock)
        WHERE isnull(ppc.brand_id, 0) != 0
            and isnull(ppc.is_auto_settle, 0) = 1
            and estimate_date is not null
            and status in (1,2)
            and ppc.policy_type = 1
    </select>
    <select id="queryPriceByMkcIds"
                     resultType="com.jiuji.oa.stock.priceprotection.vo.res.PriceProtectionProductMkcDwd">
        SELECT
            d.mkc_id,
            d.cashback_id,
            d.cashback_rule_id,
            d.single_value
        FROM ${dwsPrefix}.dwd_product_mkc_policy_match_detail d
        LEFT JOIN ${dwsPrefix}.dim_area_info a on a.id = d.area_id
        where 1=1
        AND d.tenant_id = #{tenantId}
        AND a.tenant_id = #{tenantId}
        AND d.mkc_id in
        <foreach collection="mkcIdList" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>
    <select id="obtainTheMkcIdOfTheFinishedProduct"
                     resultType="java.lang.Integer">
        SELECT
            cs.policy_order_id
        FROM dbo.CashbackSettlement cs with(nolock)
        inner join dbo.CashbackSettlementItem csi with(nolock) on cs.id = csi.SettlementId
        where isnull(cs.isdel,0) = 0
        and csi.MkcId in
        <foreach collection="mkcIdList" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
        and cs.Status = 3
    </select>

    <select id="queryPriceByMkcIdNews"
                     resultType="com.jiuji.oa.stock.priceprotection.vo.res.PriceProtectionProductMkcDwd">
        SELECT
            d.mkc_id,
            d.cashback_id,
            d.cashback_rule_id,
            d.single_value
        FROM ${dwsPrefix}.dwd_product_mkc_policy_match_detail_mini d
        LEFT JOIN ${dwsPrefix}.dim_area_info a on a.id = d.area_id
        where 1=1
        AND d.tenant_id = #{tenantId}
        AND a.tenant_id = #{tenantId}
        AND d.mkc_id in
        <foreach collection="mkcIdList" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>

    <select id="queryMkcInfoByMkcIds" resultType="com.jiuji.oa.stock.priceprotection.vo.res.MkcInfoRes">
        SELECT
            m.id AS mkcId,
            m.ppriceid AS skuId,
            a.area,
            m.imei,
            m.inbeihuoprice AS inbeihuoPrice,
            m.kc_check AS kcCheck,
            m.staticPrice,
            ISNULL(m.mouldFlag, 0) AS mouldFlag,
            p.product_name,
            p.product_color,
            b.name AS brandName,
            m.insourceid2 AS channelId,
            m.inbeihuodate AS purchaseDate,
            m.imeidate AS storageDate,
            p.brandID AS brandId,
            p.cid AS categoryId,
            p.product_id AS productId
        FROM product_mkc m WITH(NOLOCK)
        LEFT JOIN productinfo p WITH(NOLOCK) ON m.ppriceid = p.ppriceid
        LEFT JOIN areainfo a with(nolock) on a.id = m.areaid
        LEFT JOIN brand b WITH(NOLOCK) ON p.brandID = b.id
        WHERE m.imei IS NOT NULL
        AND m.id IN
        <foreach collection="mkcIdList" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>
    <select id="autoCreatePriceProtectionCashbackSettleV2" resultMap="BaseResultMap">
        SELECT
            ppc.id,
            ppc.title,
            ppc.qudao_id,
            ppc.policy_type,
            ppc.cashback_type,
            ppc.areaid,
            ppc.start_date,
            ppc.end_date,
            ppc.status,
            ppc.estimate_date,
            ppc.cashback_settlement_id,
            ppc.cashback_amout,
            ppc.settlement_amout,
            ppc.remark,
            ppc.create_time,
            ppc.update_time,
            ppc.is_del,
            ppc.quantity,
            ppc.is_auto_settle,
            ppc.brand_id,
            ppc.inuser
        FROM
            price_protection_cashback ppc WITH(nolock)
        WHERE isnull(ppc.is_auto_settle, 0) = 1
            and status = 1
            and ppc.policy_type in (1,3)
            and isnull(ppc.is_del,0) = 0
            <if test="channelIds != null and channelIds.size() != 0">
                and ppc.qudao_id in
                <foreach collection="channelIds" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>
    </select>
</mapper>
