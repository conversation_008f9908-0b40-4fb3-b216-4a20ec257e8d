<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.priceprotection.mapper.PriceProtectionCashbackRulesMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.stock.priceprotection.entity.PriceProtectionCashbackRules">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="priceProtectionCashbackId" column="price_protection_cashback_id" jdbcType="BIGINT"/>
            <result property="timeType" column="time_type" jdbcType="TINYINT"/>
            <result property="productRange" column="product_range" jdbcType="TINYINT"/>
            <result property="categoryIds" column="category_ids" jdbcType="VARCHAR"/>
            <result property="brandIds" column="brand_ids" jdbcType="VARCHAR"/>
            <result property="productIds" column="product_ids" jdbcType="VARCHAR"/>
            <result property="kcChecks" column="kc_checks" jdbcType="VARCHAR"/>
            <result property="productType" column="product_type" jdbcType="TINYINT"/>
            <result property="indicatorsType" column="indicators_type" jdbcType="TINYINT"/>
            <result property="cashbackMode" column="cashback_mode" jdbcType="TINYINT"/>
            <result property="isExcessIncluded" column="is_excess_included" jdbcType="TINYINT"/>
            <result property="targetValue" column="target_value" jdbcType="INTEGER"/>
            <result property="cashbackPolicy" column="cashback_policy" jdbcType="TINYINT"/>
            <result property="meteredValue" column="metered_value" jdbcType="DECIMAL"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="isDel" column="is_del" jdbcType="BIT"/>
            <result property="priceProtectionCashbackRulesRv" column="price_protection_cashback_rules_rv" jdbcType="TIMESTAMP"/>
            <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
            <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,price_protection_cashback_id,time_type,
        product_range,category_ids,brand_ids,
        product_ids,kc_checks,product_type,
        indicators_type,cashback_mode,is_excess_included,
        target_value,cashback_policy,metered_value,
        create_time,update_time,is_del,
        price_protection_cashback_rules_rv,start_time,end_time
    </sql>
</mapper>
