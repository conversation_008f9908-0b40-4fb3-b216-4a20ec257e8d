<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.priceprotection.mapper.PriceProtectionCashbackRulesPolicyMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.stock.priceprotection.entity.PriceProtectionCashbackRulesPolicy">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="cashbackRulesId" column="cashback_rules_id" jdbcType="BIGINT"/>
            <result property="targetValue" column="target_value" jdbcType="INTEGER"/>
            <result property="cashbackPolicy" column="cashback_policy" jdbcType="TINYINT"/>
            <result property="meteredValue" column="metered_value" jdbcType="DECIMAL"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="is_del" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,cashback_rules_id,target_value,
        cashback_policy,metered_value,create_time,
        update_time,is_del,price_protection_cashback_rules_policy_rv
    </sql>
</mapper>
