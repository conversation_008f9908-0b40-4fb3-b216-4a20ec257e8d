<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.priceprotection.mapper.PriceProtectionCashbackLogMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.stock.priceprotection.entity.PriceProtectionCashbackLog">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="msg" column="msg" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="isDel" column="is_del" jdbcType="BIT"/>
            <result property="priceProtectionCashbackLogRv" column="price_protection_cashback_log_rv" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,msg,create_time,
        update_time,is_del,price_protection_cashback_log_rv
    </sql>
</mapper>
