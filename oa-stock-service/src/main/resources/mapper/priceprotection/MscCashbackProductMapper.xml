<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.priceprotection.mapper.MscCashbackProductMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.stock.priceprotection.entity.MscCashbackProduct">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="skuId" column="sku_id" jdbcType="INTEGER"/>
            <result property="merchantSkuId" column="merchant_sku_id" jdbcType="VARCHAR"/>
            <result property="mkcId" column="mkc_id" jdbcType="INTEGER"/>
            <result property="sn" column="sn" jdbcType="VARCHAR"/>
            <result property="qudaoId" column="qudao_id" jdbcType="INTEGER"/>
            <result property="price" column="price" jdbcType="DECIMAL"/>
            <result property="discountAmount" column="discount_amount" jdbcType="DECIMAL"/>
            <result property="rebate" column="rebate" jdbcType="DECIMAL"/>
            <result property="cashbackAmount" column="cashback_amount" jdbcType="DECIMAL"/>
            <result property="cashbackSettlementId" column="cashback_settlement_id" jdbcType="INTEGER"/>
            <result property="inuser" column="inuser" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="isDel" column="is_del" jdbcType="BIT"/>
            <result property="mscCashbackProductRv" column="msc_cashback_product_rv" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,sku_id,merchant_sku_id,
        mkc_id,sn,qudao_id,
        price,discount_amount,rebate,
        cashback_amount,cashback_settlement_id,inuser,
        create_time,update_time,is_del,
        msc_cashback_product_rv
    </sql>
    <insert id="batchInsert">
        insert into msc_cashback_product
        (msc_cashback_id,sku_id,merchant_sku_id,
        mkc_id,sn,qudao_id,
        price,discount_amount,rebate,
        cashback_amount,inuser)
        values
        <foreach collection="list" item="item" separator=",">
        (#{item.mscCashbackId},#{item.skuId},#{item.merchantSkuId},#{item.mkcId},#{item.sn},#{item.qudaoId},#{item.price},#{item.discountAmount},#{item.rebate},#{item.cashbackAmount},#{item.inuser})
        </foreach>
    </insert>
    <select id="selectMscCashbackProductPage"
            resultType="com.jiuji.oa.stock.priceprotection.vo.res.MscCashbackProductRes">
        select
        mcp.id,
        mcp.sku_id,
        mcp.merchant_sku_id,
        mcp.mkc_id,
        mcp.sn,
        mcp.qudao_id,
        owq.company_jc as qudaoJc,
        mcp.price,
        mcp.discount_amount,
        mcp.rebate,
        mcp.cashback_amount,
        mcp.cashback_settlement_id,
        mcp.inuser,
        mcp.create_time
        from msc_cashback_product mcp with(nolock)
        left join productinfo p with(nolock) on mcp.sku_id = p.ppriceid
        left join Ok3w_qudao owq with(nolock) on mcp.qudao_id = owq.id
        <where>
            and isnull(mcp.is_del,0) = 0
            <if test="req.seachWord != null and req.seachWord != ''">
                <if test="req.searchKey == 1">
                    and p.product_name like concat('%',#{req.seachWord},'%')
                </if>
                <if test="req.searchKey == 2">
                    and p.product_id = #{req.seachWord}
                </if>
                <if test="req.searchKey == 3">
                    and mcp.sku_id = #{req.seachWord}
                </if>
                <if test="req.searchKey == 4">
                    and mcp.merchant_sku_id = #{req.seachWord}
                </if>
                <if test="req.searchKey == 5">
                    and mcp.sn like concat('%',#{req.seachWord},'%')
                </if>
                <if test="req.searchKey == 8">
                    and mcp.mkc_id = #{req.seachWord}
                </if>
                <if test="req.searchKey == 9">
                    and mcp.cashback_settlement_id = #{req.seachWord}
                </if>
            </if>
            <if test="req.qudaoId != null">
                and mcp.qudao_id = #{req.qudaoId}
            </if>
            <if test="req.mscCashbackId != null">
                and mcp.msc_cashback_id = #{req.mscCashbackId}
            </if>
        </where>
    </select>
</mapper>
