<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.priceprotection.mapper.MscPurchaseProductMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.stock.priceprotection.entity.MscPurchaseProduct">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="mscPurchaseId" column="msc_purchase_id" jdbcType="BIGINT"/>
            <result property="caigouId" column="caigou_id" jdbcType="INTEGER"/>
            <result property="areaId" column="area_id" jdbcType="INTEGER"/>
            <result property="skuId" column="sku_id" jdbcType="INTEGER"/>
            <result property="merchantSkuId" column="merchant_sku_id" jdbcType="VARCHAR"/>
            <result property="productCount" column="product_count" jdbcType="INTEGER"/>
            <result property="qudaoId" column="qudao_id" jdbcType="INTEGER"/>
            <result property="price" column="price" jdbcType="DECIMAL"/>
            <result property="imei" column="imei" jdbcType="VARCHAR"/>
            <result property="istax" column="isTax" jdbcType="BIT"/>
            <result property="company" column="company" jdbcType="VARCHAR"/>
            <result property="mouldflag" column="mouldFlag" jdbcType="BIT"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="inuser" column="inuser" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="isDel" column="is_del" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,msc_purchase_id,caigou_id,
        area_id,sku_id,merchant_sku_id,
        product_count,qudao_id,price,
        imei,is_tax,company,
        mould_flag,operator_activities,remark,
        inuser,create_time,update_time,
        is_del,msc_purchase_product_rv
    </sql>
    <insert id="batchInsertMscPurchaseProduct">
        insert into msc_purchase_product
        (msc_purchase_id,area_id,sku_id,merchant_sku_id,bar_code,
        product_count,qudao_id,price,imei,is_tax,company,
        mould_flag,operator_activities,remark,inuser,is_mobile,account_user_name)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.mscPurchaseId},#{item.areaId},#{item.skuId},#{item.merchantSkuId},#{item.barCode},
             #{item.productCount},#{item.qudaoId},#{item.price},#{item.imei},#{item.istax},#{item.company},
             #{item.mouldFlag},#{item.operatorActivities},#{item.remark},#{item.inuser},#{item.isMobile},#{item.accountUserName}
             )
        </foreach>
    </insert>
    <select id="getPage" resultType="com.jiuji.oa.stock.priceprotection.vo.res.MscPurchaseProductRes">
        select mpp.id,mpp.msc_purchase_id,mpp.caigou_id,mpp.area_id,mpp.sku_id,mpp.merchant_sku_id,
        mpp.bar_code,mpp.product_count,mpp.qudao_id,mpp.price,mpp.imei,
        case when mpp.is_tax = 1 then '是' else '否' end as isTax,
        mpp.company,
        case when mpp.mould_flag = 1 then '是' else '否' end as mouldFlag,
        mpp.operator_activities,mpp.remark,mpp.inuser,mpp.create_time,mpp.is_mobile,a.area,
        mpp.fail_reason
        from msc_purchase_product mpp with(nolock)
        left join areainfo a with(nolock) on a.id = mpp.area_id
        <where>
            <if test="req.seachWord != null and req.seachWord != ''">
                <if test="req.searchKey == 1">
                    and mpp.caigou_id = #{req.seachWord}
                </if>
                <if test="req.searchKey == 2">
                    and mpp.qudao_id = #{req.seachWord}
                </if>
                <if test="req.searchKey == 3">
                    and mpp.merchant_sku_id = #{req.seachWord}
                </if>
                <if test="req.searchKey == 4">
                    and mpp.imei like concat('%',#{req.seachWord},'%')
                </if>
                <if test="req.searchKey == 5">
                    and mpp.company like concat('%',#{req.seachWord},'%')
                </if>
                <if test="req.searchKey == 6">
                    and mpp.operator_activities like concat('%',#{req.seachWord},'%')
                </if>
            </if>
            <if test="req.areaIdList != null and req.areaIdList.size() > 0">
                and mpp.area_id in
                <foreach collection="req.areaIdList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.isTax != null">
                and mpp.is_tax = #{req.isTax}
            </if>
            <if test="req.modleFlag != null">
                and mpp.mould_flag = #{req.modleFlag}
            </if>
            <if test="req.mscPurchaseId != null">
                and mpp.msc_purchase_id = #{req.mscPurchaseId}
            </if>
        </where>
    </select>
</mapper>
