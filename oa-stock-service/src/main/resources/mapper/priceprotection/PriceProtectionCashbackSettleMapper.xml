<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.priceprotection.mapper.PriceProtectionCashbackSettleMapper">

    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        INSERT INTO price_protection_cashback_settle(price_protection_cashback_id, cashback_settlement_id, settlement_amout, create_time, update_time, is_del)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.priceProtectionCashbackId}, #{item.cashbackSettlementId}, #{item.settlementAmout}, #{item.createTime},
                #{item.updateTime}, #{item.isDel})
        </foreach>
    </insert>
</mapper>
