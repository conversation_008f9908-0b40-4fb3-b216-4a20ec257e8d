<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.priceprotection.mapper.MscCashbackConfigMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.stock.priceprotection.entity.MscCashbackConfig">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="skuId" column="sku_id" jdbcType="INTEGER"/>
            <result property="merchantSkuId" column="merchant_sku_id" jdbcType="VARCHAR"/>
            <result property="qudaoId" column="qudao_id" jdbcType="INTEGER"/>
            <result property="rebate" column="rebate" jdbcType="DECIMAL"/>
            <result property="inuser" column="inuser" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="isDel" column="is_del" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,sku_id,merchant_sku_id,
        qudao_id,rebate,inuser,
        create_time,update_time,is_del,
        msc_cashback_config_rv
    </sql>

    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        insert into msc_cashback_config
        (merchant_sku_id,qudao_id,rebate,inuser)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.merchantSkuId,jdbcType=VARCHAR},#{item.qudaoId,jdbcType=INTEGER},#{item.rebate,jdbcType=DECIMAL},#{item.inuser,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>
    <update id="updateHisDataIsDel">
        update msc_cashback_config set is_del = 1 where 1 = 1
    </update>

    <select id="selectMscCashbackConfigPage"
            resultType="com.jiuji.oa.stock.priceprotection.vo.res.MscCashbackConfigListRes">
        select mcc.id,
               t.sku_id,
               mcc.merchant_sku_id,
               mcc.qudao_id,
               mcc.rebate,
               mcc.is_del,
               owq.company_jc qudaoJc,
               p.product_name,
               p.product_color
        from msc_cashback_config mcc with(nolock)
        left join (
            select m.id,
            isnull(mpm.sku_id,pb.ppriceid) sku_id
            from msc_cashback_config m with(nolock)
            left join msc_product_mapping mpm with(nolock) on m.merchant_sku_id = mpm.merchant_sku_id and mpm.is_del = 0
            left join productBarcode pb with(nolock) on m.merchant_sku_id = pb.barcode
            where isnull(m.is_del,0) = 0
            <if test="req.seachWord != null and req.seachWord != '' and req.searchKey == 3">
                and (mpm.sku_id = #{req.seachWord} or pb.ppriceid = #{req.seachWord})
            </if>
        ) t on mcc.id = t.id
        left join productinfo p with(nolock) on t.sku_id = p.ppriceid
        left join Ok3w_qudao owq with(nolock) on mcc.qudao_id = owq.id
        <where>
            and isnull(mcc.is_del,0) = 0
            and t.sku_id is not null
            <if test="req.seachWord != null and req.seachWord != ''">
                <if test="req.searchKey == 1">
                    and p.product_name like concat('%',#{req.seachWord},'%')
                </if>
                <if test="req.searchKey == 2">
                    and p.product_id = #{req.seachWord}
                </if>
                <if test="req.searchKey == 4">
                    and mcc.merchant_sku_id = #{req.seachWord}
                </if>
            </if>
            <if test="req.qudaoId != null">
                and mcc.qudao_id = #{req.qudaoId}
            </if>
        </where>
    </select>
    <select id="getMscCashbackConfigListBySkuId" resultType="com.jiuji.oa.stock.priceprotection.entity.MscCashbackConfig">
        select
            m.id,
            isnull(mpm.sku_id,pb.ppriceid) sku_id,
            m.merchant_sku_id,
            m.qudao_id,
            m.rebate
        from msc_cashback_config m with(nolock)
        left join msc_product_mapping mpm with(nolock) on m.merchant_sku_id = mpm.merchant_sku_id
        left join productBarcode pb with(nolock) on m.merchant_sku_id = pb.barcode
        where isnull(m.is_del,0) = 0
        <if test="skuIdList != null and skuIdList.size() > 0">
            and (mpm.sku_id in
            <foreach collection="skuIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            or pb.ppriceid in
            <foreach collection="skuIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            )
        </if>
    </select>
</mapper>
