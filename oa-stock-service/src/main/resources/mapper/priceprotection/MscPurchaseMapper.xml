<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.priceprotection.mapper.MscPurchaseMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.stock.priceprotection.entity.MscPurchase">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="totalCount" column="total_count" jdbcType="INTEGER"/>
            <result property="totalPrice" column="total_price" jdbcType="DECIMAL"/>
            <result property="inuserId" column="inuser_id" jdbcType="INTEGER"/>
            <result property="inuser" column="inuser" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="isDel" column="is_del" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,total_count,total_price,
        inuser_id,inuser,create_time,
        update_time,is_del,msc_purchase_rv
    </sql>
    <select id="getPage" resultType="com.jiuji.oa.stock.priceprotection.vo.res.MscPurchaseRes">
        select id,total_count,total_price,inuser_id,inuser,create_time from msc_purchase mp with(nolock)
        <where>
            <if test="req.inuserId != null and req.inuserId != ''">
                and mp.inuser_id = #{req.inuserId}
            </if>
            <if test="req.startTime != null and req.endTime != null">
                and mp.create_time between #{req.startTime} and #{req.endTime}
            </if>
        </where>
    </select>
    <select id="getCaigouIdByMscPurchaseId"
            resultType="com.jiuji.oa.stock.priceprotection.vo.res.MscPurchaseCaigouRes">
        select distinct mpp.msc_purchase_id,mpp.caigou_id as id,mpp.is_mobile from msc_purchase_product mpp with(nolock)
        where mpp.msc_purchase_id in
        <foreach collection="mscPurchaseIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
</mapper>
