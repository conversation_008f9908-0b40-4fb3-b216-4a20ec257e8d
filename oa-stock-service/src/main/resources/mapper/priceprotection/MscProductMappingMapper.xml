<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.priceprotection.mapper.MscProductMappingMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.stock.priceprotection.entity.MscProductMapping">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="skuId" column="sku_id" jdbcType="INTEGER"/>
            <result property="merchantSkuId" column="merchant_sku_id" jdbcType="VARCHAR"/>
            <result property="inuser" column="inuser" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="isDel" column="is_del" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,sku_id,merchant_sku_id,
        inuser,create_time,update_time,
        is_del,msc_product_mapping_rv
    </sql>
    <select id="selectMscProductPage" resultType="com.jiuji.oa.stock.priceprotection.vo.res.MscProductListRes">
        select mp.id,mp.sku_id,mp.merchant_sku_id,mp.is_del,p.product_name,p.product_color
        from msc_product_mapping mp with(nolock)
        left join productinfo p with(nolock) on mp.sku_id = p.ppriceid
        <where>
            <if test="req.seachWord != null and req.seachWord != ''">
                <if test="req.searchKey == 1">
                    and p.product_name like concat('%',#{req.seachWord},'%')
                </if>
                <if test="req.searchKey == 2">
                    and p.product_id = #{req.seachWord}
                </if>
                <if test="req.searchKey == 3">
                    and mp.sku_id = #{req.seachWord}
                </if>
                <if test="req.searchKey == 4">
                    and mp.merchant_sku_id = #{req.seachWord}
                </if>
            </if>
            <if test="req.isDel == 0">
                and mp.is_del = 0
            </if>
            <if test="req.isDel == 1">
                and mp.is_del = 1
            </if>
        </where>
    </select>
    <select id="getMscProductById" resultType="com.jiuji.oa.stock.priceprotection.vo.res.MscProductRes">
        select mp.id,mp.sku_id,mp.merchant_sku_id,mp.is_del,p.product_name,p.product_color
        from msc_product_mapping mp with(nolock)
        left join productinfo p with(nolock) on mp.sku_id = p.ppriceid
        where mp.id = #{id}
    </select>
    <select id="getProductInfo" resultType="com.jiuji.oa.stock.priceprotection.vo.res.MscProductInfoRes">
        select p.ppriceid skuId,p.product_name,p.product_color
        from productinfo p with(nolock)
        <where>
            and isnull(p.isdel,0) = 0
            <if test="req.seachWord != null and req.seachWord != ''">
                <if test="req.searchKey == 1">
                    and p.product_name like concat('%',#{req.seachWord},'%')
                </if>
                <if test="req.searchKey == 2">
                    and p.product_id = #{req.seachWord}
                </if>
                <if test="req.searchKey == 3">
                    and p.ppriceid = #{req.seachWord}
                </if>
            </if>
            <if test="req.cidList != null and req.cidList.size > 0">
                and p.cid in
                <foreach collection="req.cidList" item="cid" index="idex" open="(" close=")" separator=",">
                    #{cid}
                </foreach>
            </if>
            <if test="req.brandIdList != null and req.brandIdList.size > 0">
                and p.brandId in
                <foreach collection="req.brandIdList" item="brandId" index="idex" open="(" close=")" separator=",">
                    #{brandId}
                </foreach>
            </if>
        </where>
    </select>

    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        insert into msc_product_mapping
        (sku_id,merchant_sku_id,inuser)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.skuId,jdbcType=INTEGER},#{item.merchantSkuId,jdbcType=VARCHAR},#{item.inuser,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>
</mapper>
