<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.accountingRecords.mapper.AreaGuestbookAccountMapper">

    <select id="selectSameData" resultType="com.jiuji.oa.stock.accountingRecords.entity.AreaGuestbookAccount">
        SELECT  *
        FROM dbo.area_guestbook_account
        WHERE(show_area_id = #{saleAreaId}
            AND EXISTS (
                SELECT 1
                FROM STRING_SPLIT(affiliated_area_id, ',') AS split_values
                WHERE split_values.value = #{purchaseAreaId} and del_flag = 0
            )) or (show_area_id = #{purchaseAreaId}
            AND EXISTS (
                SELECT 1
                FROM STRING_SPLIT(affiliated_area_id, ',') AS split_values
                WHERE split_values.value = #{saleAreaId} and del_flag = 0
            ))   order by  id  desc ;
    </select>
    <select id="selectMappingAreaGuestbookAccount" resultType="com.jiuji.oa.stock.accountingRecords.entity.AreaGuestbookAccount">
        SELECT  *
        FROM dbo.area_guestbook_account
        WHERE EXISTS (
            SELECT 1
            FROM STRING_SPLIT(affiliated_area_id, ',') AS split_values
            WHERE split_values.value = #{saleAreaId} and del_flag = 0
        )

    </select>
    <select id="selectSameAreaData" resultType="com.jiuji.oa.stock.accountingRecords.entity.AreaGuestbookAccount">
        SELECT *
        FROM dbo.area_guestbook_account
        WHERE show_area_id = #{saleAreaId}
            or EXISTS (
                SELECT 1
                FROM STRING_SPLIT(affiliated_area_id, ',') AS split_values
                WHERE split_values.value = #{purchaseAreaId} and del_flag = 0
            ) order by  id  desc ;
    </select>
</mapper>