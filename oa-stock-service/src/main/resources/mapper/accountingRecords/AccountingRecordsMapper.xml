<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.accountingRecords.mapper.AccountingRecordsMapper">

    <select id="accountingPage" resultType="com.jiuji.oa.stock.accountingRecords.vo.res.AccountingPageRes">
        select ar.id,
               p.ppriceid as skuId,
               p.product_name,
               p.product_color,
               pm.id as mkcId,
               pm.imei,
               pm.imei2,
               pm.imei3,
               pm.orderid,
               ar.accounting_area_id,
               ar.receive_user_id as accountingUserId,
               ar.receive_user_id_str,
               ar.accounting_state,
               ar.accounting_time,
               ar.transfer_time,
               ar.records_type,
                ISNULL(s.kcAreaid,s.areaid) as saleAreaId,
               s.sub_id,
               DATEDIFF(minute , ar.transfer_time, ISNULL(ISNULL(ar.accounting_time,ar.accounting_cancel_time),GETDATE())) as handleTimeout,
               s.sub_check,
               s.sub_date as addSubTime,
               a.pid as productMkcPid,
               p.brandID as productMkcBrandId,
               link.desensitize_flag,
               link.desensitize_flag_brand_id,
               link.desensitize_flag_province_id_list
        from dbo.accounting_records ar with (nolock)
         left join dbo.ch999_user u with(nolock ) on u.ch999_id = ar.receive_user_id
         left join dbo.productinfo p with (nolock) on p.ppriceid = ar.ppid
         left join dbo.product_mkc pm with (nolock) on pm.id = ar.mkc_id
         left join dbo.areainfo a with (nolock) on  pm.areaid = a.id
         left join dbo.channel_kind_link link with(nolock ) on pm.insourceid2 = link.channel_id and link.kind=3 and link.channel_state = 1
         left join dbo.basket b with (nolock) on b.basket_id = ar.basket_id
         left join dbo.sub s with (nolock) on s.sub_id = b.sub_id
    <where>
        1=1
        <if test="req.recordsType != null">
            and ar.records_type = #{req.recordsType}
        </if>
        <if test="req.selectType != null and  req.selectType == 1 and req.selectValue!='' and req.selectValue!=null">
            and p.product_name like #{req.selectValue} + '%'
        </if>
        <if test="req.selectType != null and  req.selectType == 2 and req.selectValue!='' and req.selectValue!=null">
            and ar.ppid = #{req.selectValue}
        </if>
        <if test="req.selectType != null and  req.selectType == 3 and req.selectValue!='' and req.selectValue!=null">
            and p.product_id = #{req.selectValue}
        </if>
        <if test="req.selectType != null and  req.selectType == 4 and req.selectValue!='' and req.selectValue!=null">
            and pm.imei like CONCAT (#{req.selectValue},'%')
        </if>
        <if test="req.selectType != null and  req.selectType == 5 and req.selectValue!='' and req.selectValue!=null">
            and pm.imei2 like CONCAT (#{req.selectValue},'%')
        </if>
        <if test="req.selectType != null and  req.selectType == 6 and req.selectValue!='' and req.selectValue!=null">
             <choose>
                 <when test="req.recordsType == 1">
                     and(EXISTS (
                     SELECT 1
                     FROM STRING_SPLIT(ar.receive_user_id_str, ',') AS split_values
                     WHERE split_values.value =(#{req.selectValue})
                     ) or u.ch999_id = #{req.selectValue})
                 </when>
             <otherwise>
                 and (u.ch999_id = #{req.selectValue})
             </otherwise>
             </choose>
        </if>
        <if test="req.selectType != null and  req.selectType == 7 and req.selectValue!='' and req.selectValue!=null">
            and s.sub_id = #{req.selectValue}
        </if>
        <if test="req.accountingAreaIdList != null and req.accountingAreaIdList.size > 0">
            and ar.accounting_area_id in
            <foreach collection="req.accountingAreaIdList" item="item" separator="," open="(" close=")" >
                #{item}
            </foreach>
        </if>
        <if test="req.saleAreaIdList != null and req.saleAreaIdList.size > 0">
            and s.areaid in
            <foreach collection="req.saleAreaIdList" item="item" separator="," open="(" close=")" >
                #{item}
            </foreach>
        </if>
        <if test="req.accountingStateList != null and req.accountingStateList.size > 0">
            and ar.accounting_state in
            <foreach collection="req.accountingStateList" item="item" separator="," open="(" close=")" >
                #{item}
            </foreach>
        </if>
        <if test="req.handleTimeoutMin != null or  req.handleTimeoutMax != null">
            and DATEDIFF(minute , ar.transfer_time, ISNULL(ISNULL(ar.accounting_time,ar.accounting_cancel_time),GETDATE())) between #{req.handleTimeoutMin} and #{req.handleTimeoutMax}
        </if>
        <if test="req.subCheckList != null and req.subCheckList.size > 0">
            and s.sub_check in
            <foreach collection="req.subCheckList" item="item" separator="," open="(" close=")" >
                #{item}
            </foreach>
        </if>
        <if test="req.timeStart != null and req.timeEnd != null  and  req.timeType == 1">
            and s.sub_date between #{req.timeStart} and #{req.timeEnd}
        </if>
        <if test="req.timeStart != null and req.timeEnd != null  and  req.timeType == 2">
            and ar.transfer_time between #{req.timeStart} and #{req.timeEnd}
        </if>
    </where>
    order by ar.id desc
    </select>
    <select id="selectInStoreUser" resultType="java.lang.Integer">
        SELECT ch999_id
        FROM dbo.ch999_user AS cu
        WHERE ISNULL(cu.iszaizhi, 0) = 1
          AND ISNULL(cu.islogin, 0) = 0
          AND cu.mainRole IN (1333)
          AND cu.area1id = #{areaId}
    </select>
</mapper>