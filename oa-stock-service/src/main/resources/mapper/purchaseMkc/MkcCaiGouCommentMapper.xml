<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.purchaseMkc.mapper.MkcCaigouCommentMapper">
    <update id="updateState">
        UPDATE wuliu_transfer_station
        SET state = 1
        WHERE station_area_id = #{stationAreaId}
          and wuLiu_Id = #{wuLiuId}
          and state = 0
    </update>

    <select id="listMkcIdAndZhejiaM" resultType="cn.hutool.core.lang.Dict">
        select shouhouxian.mkc_id mkcId,tuihuan.zhejiaM zhejiaM from shouhou shouhoutui with(nolock)
                  inner join shouhou shouhouxian with(nolock) on shouhouxian.fromshouhouid = shouhoutui.id
                  inner join shouhou_tuihuan tuihuan with(nolock) on shouhoutui.id = tuihuan.shouhou_id
        where shouhouxian.mkc_id in
              <foreach collection="mkcIds" item="mkcId" separator="," open="(" close=")">
                  #{mkcId}
              </foreach>
    </select>
    <select id="listPpidAndYoupinTrade" resultType="cn.hutool.core.lang.Dict">
        select b.ppriceid,b.price
        from(
            select bsk.ppriceid,bsk.price,ROW_NUMBER() over(PARTITION by bsk.ppriceid order by bsk.sub_id desc,basket_id desc) n
            from basket bsk with(nolock)
            where exists(select 1 from sub with(nolock) where sub.sub_id = bsk.sub_id and sub.sub_check = 3)
            and bsk.type = 22 and isnull(bsk.isdel,0)=0 and bsk.ppriceid in
            <foreach collection="ppids" item="ppid" separator="," open="(" close=")">
                #{ppid}
            </foreach>
        ) b
        where b.n = 1
    </select>
    <select id="listPpidAndLiangpinTrade" resultType="cn.hutool.core.lang.Dict">
        select b.ppriceid,b.price
        from(
            select bsk.ppriceid,bsk.price,ROW_NUMBER() over(PARTITION by bsk.ppriceid order by bsk.sub_id desc,basket_id desc) n
            from recover_marketSubInfo bsk with(nolock)
            where exists(select 1 from recover_marketInfo sub  with(nolock) where sub.sub_id = bsk.sub_id and sub.sub_check = 3) and bsk.ppriceid in
            <foreach collection="ppids" item="ppid" separator="," open="(" close=")">
                #{ppid}
            </foreach>
        ) b
        where b.n = 1
    </select>
    <select id="listPpidCostPricetMemberPrice" resultType="com.jiuji.oa.nc.product.entity.ProductInfoEntity">
        SELECT ppriceid AS ppid, costprice, memberprice
        FROM dbo.productinfo with(nolock)
        WHERE ppriceid IN
        <foreach collection="ppids" item="ppid" separator="," open="(" close=")">
            #{ppid}
        </foreach>
    </select>
    <select id="getMkcCaigouSubListAppPage"
            resultType="com.jiuji.oa.stock.purchaseMkc.vo.res.MkcCaiGouSubListAppPageRes">
        SELECT
        temp.areaId ,
        temp.areaCode ,
        temp.title ,
        temp.sub_id,
        temp.indate ,
        temp.inuser,
        COUNT(temp.id) as amount,
        isnull(SUM(temp.inbeihuoprice),0) as totalPrice  FROM (
        SELECT
        DISTINCT
        s.areaid as areaId ,a.area as areaCode ,s.title , s.id as sub_id,s.indate ,s.inuser,pm.id ,isnull(pm.inbeihuoprice,0) as inbeihuoprice
        FROM mkcCaiGouSub s WITH (NOLOCK)
        left join mkcCaiGouBasket b WITH (NOLOCK) on b.sub_id = s.id
        left join dbo.wuliu w with(nolock ) on s.wuliu_id=w.id
        left join product_mkc pm WITH (NOLOCK) on pm.id = b.mkc_id
        left join dbo.apple_purchase_store_inventory apple with(nolock ) on pm.id=apple.mkc_id
        left join productinfo p WITH (NOLOCK) on p.ppriceid = pm.ppriceid
        LEFT JOIN productbarcode c WITH ( nolock ) ON p.ppriceid= c.ppriceid
        left join areainfo a with (nolock) on a.id = s.areaid
        <where>b.isDel = 0
            <if test="req.scanCode!=null and req.scanCode!='' ">
                AND (w.nu= #{req.scanCode} or pm.imei = #{req.scanCode} or apple.imei=#{req.scanCode} )
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==1">
                and p.product_name like '%' + #{req.searchValue} + '%'
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==2">
                AND s.id = #{req.searchValue}
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==3">
                and p.ppriceid = #{req.searchValue}
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==4">
                and p.productid = #{req.searchValue}
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==5">
                and (pm.imei = #{req.searchValue} or apple.imei=#{req.searchValue})
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==6">
                and pm.id = #{req.searchValue}
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==7">
                AND s.inuser LIKE CONCAT('%',#{req.searchValue},'%')
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==8">
                AND c.isDefault = 1 AND c.barcode = #{req.searchValue}
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==9">
                AND w.nu= #{req.searchValue}
            </if>
            <!--分类-->
            <if test="req.categoryCharSeq != null and req.categoryCharSeq !='' ">
                AND exists( select 1 from f_category_children(#{req.categoryCharSeq}) f where f.id=p.cid )
            </if>
            <!--品牌专区-->
            <if test="req.brandIds!=null and req.brandIds.size()!=0">
                and p.brandid in
                <foreach item="item" index="index" collection="req.brandIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.insourceIds!=null and req.insourceIds.size()!=0">
                AND s.insourceId in
                <foreach item="item" index="index" collection="req.insourceIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.searchAreaIdList != null and req.searchAreaIdList.size()>0 ">
                and s.areaid in
                <foreach collection="req.searchAreaIdList" item="it" separator="," open="(" close=")">
                    #{it}
                </foreach>
            </if>
            <if test="req.startTime != null and req.endTime != null  ">
                <if test="req.searchTimeType!=null and req.searchTimeType==1">
                    and s.indate between #{req.startTime} and #{req.endTime}
                </if>
                <if test="req.searchTimeType!=null and req.searchTimeType==2">
                    and pm.imeidate between #{req.startTime} and #{req.endTime}
                </if>
            </if>
        </where>) as temp
        GROUP by
        temp.areaId ,
        temp.areaCode ,
        temp.title ,
        temp.sub_id,
        temp.indate ,
        temp.inuser
        having COUNT(temp.id) > 0
    </select>

    <select id="getOperator" resultType="java.lang.String">
        SELECT kind as kind from dbo.mobileMkc with(nolock) where kind is not NULL and mkc_id = #{mkcid}
    </select>
    <select id="selectWarehouseEntryRes" resultType="com.jiuji.oa.stock.purchaseMkc.vo.res.WarehouseEntryRes">
        select m.sub_id as subId , p.imei
        from dbo.mkcCaiGouBasket m with (nolock)
         left join dbo.product_mkc p with (nolock) on m.mkc_id = p.id
        <where>
            m.isDel = 'false'
            <if test="subIds!=null and subIds.size()>0">
                and m.sub_id in
                <foreach collection="subIds" item="it" separator="," open="(" close=")" >
                    #{it}
                </foreach>
            </if>
        </where>
    </select>
</mapper>
