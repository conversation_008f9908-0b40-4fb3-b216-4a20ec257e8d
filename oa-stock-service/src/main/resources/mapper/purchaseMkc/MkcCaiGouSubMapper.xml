<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.purchaseMkc.mapper.MkcCaiGouSubMapper">

    <select id="selectWarehousingDetail" resultType="com.jiuji.oa.stock.purchaseMkc.vo.res.WarehousingDetailInfoRes">
        select b.mkc_id,p.ppriceid as skuId , i.product_name as productName,i.product_color as productColor ,isnull(p.imei,apple.imei) as imei,iif(p.imei is not null ,'true','false') as isWarehousing
        from dbo.mkcCaiGouBasket b with (nolock)
         left join dbo.product_mkc p with (nolock) on p.id = b.mkc_id
            left join dbo.apple_purchase_store_inventory apple with(nolock ) on apple.mkc_id=p.id
            left join dbo.productinfo i with(nolock ) on p.ppriceid = i.ppriceid
        where isnull(b.isDel,0)=0 and b.sub_id = #{req.subId}

    </select>
    <select id="selectNoStock" resultType="com.jiuji.oa.stock.purchaseMkc.vo.res.NoStockDetailRes">
        select c.mkc_id AS mkcId, m.inbeihuoprice as purchasePrice,m.kc_check,ISNULL(m.imei,apple.imei) as imei
        from dbo.mkcCaiGouBasket c with (nolock)
         left join dbo.product_mkc m with (nolock) on m.id = c.mkc_id
            left join dbo.apple_purchase_store_inventory apple with (nolock) on c.mkc_id = apple.mkc_id
        where isnull(c.isDel,0)=0 and c.sub_id=#{req.purchaseId} and  m.ppriceid =#{req.ppid}
        <if test="req.type==1">
            and m.imei is not null
        </if>
        <if test="req.type==2">
            and m.imei is null
        </if>

    </select>
</mapper>