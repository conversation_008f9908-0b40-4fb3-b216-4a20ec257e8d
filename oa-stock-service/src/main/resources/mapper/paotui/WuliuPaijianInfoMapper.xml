<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.logistics.paotui.mapper.WuliuPaijianInfoMapper">

    <select id="getPaiJianInfoByBusinessTypeAndId" resultType="com.jiuji.oa.stock.logistics.paotui.vo.PaiJianInfo">
        <if test="businessType == 21 or businessType == 27">
            select top 1 u.ch999_id sellerUserId from basket b with(nolock)
            left join ch999_user u with(nolock) on b.seller = u.ch999_name
            where b.sub_id = #{businessId}
            and isnull(b.isdel,0) = 0
            order by b.ismobile desc
        </if>
        <if test="businessType == 6 or businessType == 22">
            select top 1 u.ch999_id sellerUserId from recover_marketSubInfo b with(nolock)
            left join ch999_user u with(nolock) on b.seller = u.ch999_name
            where b.sub_id = #{businessId}
            and isnull(b.isdel,0) = 0
            order by b.ismobile desc
        </if>
        <if test="businessType == 3">
            select top 1 u.ch999_id sellerUserId,m.orderid orderId from mkc_toarea mt with(nolock)
            left join product_mkc m with(nolock) on mt.mkc_id = m.id
            left join basket b with(nolock) on m.basket_id = b.basket_id
            left join ch999_user u with(nolock) on b.seller = u.ch999_name
            where mt.id = #{businessId}
        </if>
        <if test="businessType == 2">
            select top 1 u.ch999_id sellerUserId
            from diaobo_sub s with(nolock)
            left join diaobo_basket k with(nolock) on s.id=k.sub_id
            left join basket b with(nolock) on b.basket_id=k.basket_id
            left join ch999_user u with(nolock) on b.seller = u.ch999_name
            where s.id = #{businessId}
        </if>
        <if test="businessType == 11 or businessType == 13">
            select top 1 u.ch999_id sellerUserId
            from diaobo_sub s with(nolock)
            left join shouhou_apply sa with(nolock) on sa.caigouid = s.id and s.stats not in(4, 0)
            left join shouhou sh with(nolock) on sh.id = sa.wxid
            left join ch999_user u with(nolock) on sh.inuser = u.ch999_name
            where s.id = #{businessId}
        </if>
        <if test="businessType == 5">
            select top 1 u.ch999_id sellerUserId,m.orderid orderId from recover_toarea rt with(nolock)
            left join recover_mkc m with(nolock) on m.id = rt.mkc_id
            left join recover_marketSubInfo b WITH(NOLOCK) on m.to_basket_id = b.basket_id
            left join ch999_user u with(nolock) on b.seller = u.ch999_name
            where rt.id = #{businessId}
        </if>
    </select>
    <select id="getFirstRejectOrCancelTime" resultType="java.time.LocalDateTime">
        SELECT top 1 wpi.reject_time from wuliu_paijian_info wpi with(nolock)
        where wpi.status in(2,3) and wpi.fk_wuliu_id = #{wuliuId}
        <if test="startTime != null">
            and wpi.reject_time >= #{startTime}
        </if>
        order by wpi.id asc
    </select>
</mapper>
