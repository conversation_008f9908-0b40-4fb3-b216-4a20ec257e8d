<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.diy.mapper.DiyCostConfigMapper">

    <select id="pageList" resultType="com.jiuji.oa.stock.diy.vo.res.DiyCostFlowSearchRes">
        SELECT
        s.userid ,
        s.kind ,
        s.sub_id ,
        s.[money] ,
        s.smoney ,
        s.dtime,
        s.comment
        from
        save_money s with (nolock)
        <where>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==1">
                and s.sub_id = #{req.searchValue}
            </if>
            <if test="req.userId!=null and req.userId!=''">
                and s.userid = #{req.userId}
            </if>
            <if test="req.userIdList != null and req.userIdList.size() > 0 ">
                and s.userid in
                <foreach collection="req.userIdList" index="index" item="it" separator="," open="(" close=")">
                    #{it}
                </foreach>
            </if>
            <if test="req.kind != null and req.kind.size() > 0 ">
                and s.kind in
                <foreach collection="req.kind" index="index" item="it" separator="," open="(" close=")">
                    #{it}
                </foreach>
            </if>
            <if test="req.startTime != null and req.endTime != null">
                and s.dtime between #{req.startTime} and #{req.endTime}
            </if>
        </where>
        order by s.dtime desc
    </select>

    <select id="getLeftDiyCostList" resultType="com.jiuji.oa.stock.diy.vo.res.DiyCostFlowLeftRes">
        SELECT
        bu.erdu,
        bu.save_money ,
        bu.id as userid
        from
        BBSXP_Users bu with(nolock)
        <where>
            <if test="req.userIdList != null and req.userIdList.size() > 0 ">
                and bu.id in
                <foreach collection="req.userIdList" index="index" item="it" separator="," open="(" close=")">
                    #{it}
                </foreach>
            </if>
        </where>
    </select>
    <select id="getCount" resultType="java.lang.Integer">
        SELECT
        count (1)
        from
        save_money s with (nolock)
        <where>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==1">
                and s.sub_id = #{req.searchValue}
            </if>
            <if test="req.userId!=null and req.userId!=''">
                and s.userid = #{req.userId}
            </if>
            <if test="req.userIdList != null and req.userIdList.size() > 0 ">
                and s.userid in
                <foreach collection="req.userIdList" index="index" item="it" separator="," open="(" close=")">
                    #{it}
                </foreach>
            </if>
            <if test="req.kind != null and req.kind.size() > 0 ">
                and s.kind in
                <foreach collection="req.kind" index="index" item="it" separator="," open="(" close=")">
                    #{it}
                </foreach>
            </if>
            <if test="req.startTime != null and req.endTime != null">
                and s.dtime between #{req.startTime} and #{req.endTime}
            </if>
        </where>
    </select>
</mapper>
