<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.baozun.mapper.BaoZunFlowMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.jiuji.oa.baozun.entity.BaoZunFlowEntity">
    <id column="id" property="id"/>
    <result column="batch_id" property="batchId"/>
    <result column="area_id" property="areaId"/>
    <result column="warehouse_code" property="warehouseCode"/>
    <result column="ppid" property="ppid"/>
    <result column="upc" property="upc"/>
    <result column="count" property="count"/>
    <result column="create_time" property="createTime"/>
    <result column="update_time" property="updateTime"/>
    <result column="delete_flag" property="deleteFlag"/>
  </resultMap>

  <!-- 通用查询结果列 -->
  <sql id="Base_Column_List">
    id, batch_id, area_id, warehouse_code, ppid, upc, count, create_time, update_time, delete_flag
  </sql>
  <update id="updateBaoZunFlowWithRefund">
    update t_baozun_flow
    set `count` = `count` + #{entity.count}
    where batch_id = #{entity.batchId}
      and fk_tenant_id =#{entity.fkTenantId}
      and area_id =#{entity.areaId}
      and ppid =#{entity.ppid}
  </update>
  <select id="queryAccessoryStockFlow"
    resultType="com.jiuji.oa.baozun.dto.req.flow.StockFlowVo">
    select
    k.id as business_id,s2.sub_id as subId2,sp.sub_id as subId3
    ,ba.fk_tenant_id,ba.area_id,ba.tenant_store_code as warehouse_code,bv.ppriceid as
    ppid,bv.brand_sku_code
    as upc,k.count,k.dtime as business_time,k.comment,ISNULL(s.subtype,s2.subtype) as subtype,orf.fromName,sf.smallproid,k.basket_id,k.id as uniqueId
    from dbo.product_kclogs k with(nolock)
    left join shouhou_fanchang sf with(nolock) on sf.id = k.basket_id and k.comment like '%转现%'
    left join Smallpro sp with(nolock) on sp.id = k.basket_id and k.comment like '售后小件换货取件出库%'
    left join basket b with(nolock) on b.basket_id =sf.basket_id
    left join sub s with(nolock) on s.sub_id = b.sub_id
    left join basket b2 with(nolock) on b2.basket_id =k.basket_id
    left join sub s2 with(nolock) on s2.sub_id = b2.sub_id
    left join baozun_tenant_sales_order btso with(nolock) on btso.sub_id = b.sub_id and btso.is_del=0
    left join orderFrom as orf  with(nolock) on orf.sub_id = b.sub_id
    inner join dbo.baozun_tenant_area ba with(nolock) on (ba.area_id = k.areaid and
    isnull(ba.is_del,0)=0 and isnull(ba.is_enable,0)=1)
    inner join baozun_tenant_variants bv with(nolock) on (bv.is_enabled = 1 and bv.ppriceid = k.ppriceid and
    isnull(bv.is_del,0)=0)
    <where>
      k.dtime <![CDATA[ >= ]]> format(getdate(),'yyyy-MM-dd')
      and ba.fk_tenant_id = bv.fk_tenant_id
      and ba.fk_tenant_id = #{fkTenantId}
      <!--宝尊云仓不需要同步-->
      and not (k.count &gt; 0 and k.comment like '宝尊云仓下单生成采购入库%')
      AND btso.transaction_number is null
    </where>
  </select>
  <select id="queryAccessoryStock"
    resultType="com.jiuji.oa.baozun.dto.req.flow.StockFlowVo">
    with t_area_ppid as (
      SELECT row_number() OVER (ORDER BY ba.area_id, bv.ppriceid) as rownumber,
             #{fkTenantId}                                        as fk_tenant_id,
             ba.area_id,
             ba.tenant_store_code                                 AS warehouse_code,
             bv.ppriceid                                          AS ppid,
             bv.brand_sku_code                                    AS upc
      FROM dbo.baozun_tenant_variants bv
    WITH (NOLOCK),
      dbo.baozun_tenant_area ba
    WITH (NOLOCK),
      dbo.productinfo p
    with (nolock)
    where isnull(bv.is_del
        , 0)=0
      and isnull(ba.is_del
        , 0)=0
      and isnull(ba.is_enable
        , 0)=1
      and bv.ppriceid = p.ppriceid
      and bv.is_enabled = 1
      and ba.fk_tenant_id = bv.fk_tenant_id
      and ba.fk_tenant_id = #{fkTenantId}
      and p.ismobile1=0
      )
    select ap.fk_tenant_id,
           ap.area_id,
           ap.warehouse_code,
           ap.ppid,
           ap.upc,
           coalesce(kc.leftCount, 0) AS [COUNT]
    from t_area_ppid ap
    WITH (NOLOCK)
      left outer join dbo.product_kc kc
    WITH (NOLOCK)
    on ap.area_id = kc.areaid
      and ap.ppid = kc.ppriceid
    where ap.rownumber between #{start}
      and #{end}
  </select>
  <select id="countAccessoryAreaPpid" resultType="java.lang.Integer">
    SELECT count(1)
    FROM dbo.baozun_tenant_variants bv WITH(NOLOCK),
      dbo.baozun_tenant_area ba
    WITH (NOLOCK),
      dbo.productinfo p
    with (nolock)
    where isnull(bv.is_del
        , 0)=0
      and isnull(ba.is_del
        , 0)=0
      and isnull(ba.is_enable
        , 0)=1
      and bv.ppriceid = p.ppriceid
      and bv.is_enabled = 1
      and ba.fk_tenant_id = bv.fk_tenant_id
      and ba.fk_tenant_id = #{fkTenantId}
      and p.ismobile1=0
  </select>
  <select id="countMobileAreaPpid" resultType="java.lang.Integer">
    SELECT count(1)
    FROM dbo.baozun_tenant_variants bv WITH(NOLOCK),
      dbo.baozun_tenant_area ba
    WITH (NOLOCK),
      dbo.productinfo p
    with (nolock)
    where isnull(bv.is_del
        , 0)=0
      and isnull(ba.is_del
        , 0)=0
      and isnull(ba.is_enable
        , 0)=1
      and bv.ppriceid = p.ppriceid
      and bv.is_enabled = 1
      and ba.fk_tenant_id = bv.fk_tenant_id
      and ba.fk_tenant_id = #{fkTenantId}
      and p.ismobile1=1
  </select>
  <select id="queryMobileStock"
    resultType="com.jiuji.oa.baozun.dto.req.flow.StockFlowVo">
    with t_area_ppid as (
      SELECT row_number() OVER (ORDER BY ba.area_id, bv.ppriceid) as rownumber,
             #{fkTenantId}                                        as fk_tenant_id,
             ba.area_id,
             ba.tenant_store_code                                 AS warehouse_code,
             bv.ppriceid                                          AS ppid,
             bv.brand_sku_code                                    AS upc
      FROM dbo.baozun_tenant_variants bv
    WITH (NOLOCK),
dbo.baozun_tenant_area ba
    WITH (NOLOCK),
      dbo.productinfo p
    with (nolock)
    where isnull(bv.is_del
        , 0)=0
      and isnull(ba.is_del
        , 0)=0
      and isnull(ba.is_enable
        , 0)=1
      and bv.ppriceid = p.ppriceid
      and bv.is_enabled = 1
      and ba.fk_tenant_id = bv.fk_tenant_id
      and ba.fk_tenant_id = #{fkTenantId}
      and p.ismobile1=1
      )
        , t_mkc as (
    SELECT
      ap.area_id,
      ap.warehouse_code,
      ap.ppid,
      ap.upc,
      COUNT (mkc.id) AS [COUNT]
    FROM
      t_area_ppid ap
    WITH (NOLOCK),
      dbo.product_mkc mkc
    WITH (NOLOCK)
    <where> ap.area_id=mkc.areaid
      and ap.ppid=mkc.ppriceid
      and mkc.kc_check=3
      <if test="inSourceIdList != null and inSourceIdList.size>0">
      AND mkc.insourceid2 IN
      <foreach collection="inSourceIdList" index="index" item="supplierId" separator="," open="(" close=")">
        #{supplierId}
      </foreach>
      </if>
      AND COALESCE (mkc.mouldFlag
        , 0)=0
      AND NOT EXISTS
        (
      SELECT
      1
      FROM
      dbo.xc_mkc xc WITH (NOLOCK)
      WHERE
      mkc.id=xc.mkc_id )
      and ap.rownumber between #{start}
      and #{end}
    </where>
    group by ap.area_id, ap.warehouse_code, ap.ppid, ap.upc
      )
    SELECT apd.fk_tenant_id,
           apd.area_id,
           apd.warehouse_code,
           apd.ppid,
           apd.upc,
           COALESCE(mkc.[count], 0) as [count]
    FROM
      t_area_ppid apd left outer join t_mkc mkc
    on apd.area_id=mkc.area_id
      and apd.ppid=mkc.ppid
    where apd.rownumber between #{start}
      and #{end}
  </select>

  <select id="queryMobileStockSn"
          resultType="com.jiuji.oa.baozun.dto.req.flow.StockFlowVo">
    SELECT
      mkc.ppriceid,
      mkc.areaid,
      mkc.imei as sn,
      ISNULL(mkc.mouldFlag,0) as mouldFlag,
      ISNULL( (SELECT
                 1
               FROM
                 dbo.xc_mkc xc WITH (NOLOCK)
        WHERE
        mkc.id = xc.mkc_id ),0) as xcFlag
    FROM
      dbo.product_mkc mkc WITH (NOLOCK)
    <where>
      mkc.areaid = #{areaid}
      and mkc.ppriceid = #{ppriceid}
      and mkc.kc_check = 3
      <if test="inSourceIdList != null and inSourceIdList.size>0">
      AND mkc.insourceid2 IN
      <foreach collection="inSourceIdList" index="index" item="supplierId" separator="," open="(" close=")">
        #{supplierId}
      </foreach>
      </if>
    </where>
  </select>

  <select id="queryRefund" resultType="com.jiuji.oa.baozun.entity.BaoZunFlowEntity">
    SELECT #{fkTenantId} as fkTenantId,
           #{batchId}    as batchId,
           a.area_id,
           b.ppid,
           sum(b.quantity)  count
    FROM dbo.baozun_tenant_sales_order a WITH(NOLOCK),
    dbo.baozun_tenant_sales_detail b
    WITH (NOLOCK)
    where a.transaction_number=b.fk_transaction_number
      and b.transaction_type='S'
      and b.refund_time is not null
    group by a.area_id, b.ppid
  </select>
</mapper>
