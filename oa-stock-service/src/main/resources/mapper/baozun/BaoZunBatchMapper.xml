<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.baozun.mapper.BaoZunBatchMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.baozun.entity.BaoZunBatchEntity">
        <id column="id" property="id"/>
        <result column="batch_type" property="batchType"/>
        <result column="state" property="state"/>
        <result column="last_execute_time" property="lastExecuteTime"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="delete_flag" property="deleteFlag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , batch_type, state, last_execute_time, create_time, update_time, delete_flag
    </sql>
    <update id="updateState">
        update t_baozun_batch
        set state = 0
        where id in
        <foreach collection="idList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>


    <select id="judgeRetail" resultType="com.jiuji.oa.baozun.dto.SubFromSourceDTO">
        SELECT s.sub_id,
               orf.fromName,
               s.subtype
        from sub s with(nolock)
        left join orderFrom as orf
        with (nolock)
        on
            orf.sub_id = s.sub_id
        where
            s.sub_id = #{businessId}
    </select>

    <select id="getRetryList" resultType="Long">
        SELECT
            tbb.id
        from
            t_baozun_batch tbb
                join t_baozun_flow tbf on
                tbf.batch_id = tbb.id
        where
            tbf.create_time >= date_format(now(), '%Y-%m-%d')
          and tbb.state in (0)
          and tbf.business_id is not null
        union
        SELECT
            tbb.id
        from
            t_baozun_batch tbb
                join t_baozun_flow tbf on
                tbf.batch_id = tbb.id
        where
            tbb.state in (2)
          and tbb.batch_type = 1
          and tbf.create_time >= date_format(now(), '%Y-%m-%d')
    </select>

</mapper>
