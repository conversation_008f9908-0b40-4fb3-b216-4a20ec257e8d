<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.stock.logistics.order.mapper.AttachmentsMapper">

    <insert id="addBatch" useGeneratedKeys="true">
        insert into attachments (linkedID, type, filename, filepath, kind, dtime, userid, fid, Extension, receiveUser, receiveDate, fileSize) values

        <trim suffixOverrides=",">
        <foreach collection="list" item="item">
            (
            #{item.linkedID}, #{item.type}, #{item.filename}, #{item.filepath}, #{item.kind}, #{item.dtime}
            ,#{item.userid}, #{item.fid}, #{item.extension}, #{item.receiveUser}, #{item.receiveDate}, #{item.fileSize}
            ),
        </foreach>
        </trim>
    </insert>

    <select id="getAttachmentsSeparateConfig"
            resultType="com.jiuji.oa.stock.logistics.order.newAttachments.po.AttachmentsSeparateConfig">
            SELECT
                id,
                attachments_type,
                separate_time,
                separate_time_name,
                create_time,
                is_del
            FROM
                dbo.attachments_separate_config with(nolock)
            WHERE
                isnull(is_del,0) = 0 AND attachments_type = #{attachmentsType}
    </select>

    <select id="getSendTimeByLogisticsId"
            resultType="com.jiuji.oa.stock.logistics.order.newAttachments.res.BusinessTimeRes">
        select w.id AS businessId,w.dtime AS businessCreationTime from wuliu w with(nolock) where w.id in
        <foreach collection="wuLiuIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
</mapper>
