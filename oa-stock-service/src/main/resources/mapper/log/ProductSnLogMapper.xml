<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.logapi.mapper.ProductSnLogMapper">
    <select id="getLastRecord" resultType="com.jiuji.oa.logapi.pojo.entity.ProductSnLog">
        SELECT a.id,a.d_time,a.in_user,a.`comment`,a.sub_id,a.product_sn,a.show_type,a.rollback_id
        FROM product_sn_log a,
        (
        SELECT
        distinct product_sn,FIRST_VALUE(id) OVER (PARTITION BY product_sn ORDER BY d_time desc) id
        FROM
        product_sn_log
        where product_sn in
        <foreach collection="snList" index="index" item="sn" separator="," open="("
                 close=")">
            #{sn}
        </foreach>
        ) temp
        where a.id=temp.id
    </select>
</mapper>