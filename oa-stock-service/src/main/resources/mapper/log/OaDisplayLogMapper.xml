<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.logapi.mapper.OaDisplayLogMapper">
    <select id="getLastRecord" resultType="com.jiuji.oa.logapi.pojo.entity.OaDisplayLog">
        SELECT a.id,a.d_time,a.in_user,a.show_type,a.`comment`,a.sub_id,a.type,a.rollback_id
        FROM oa_display_log a,
        (
        SELECT
        distinct sub_Id,FIRST_VALUE(id) OVER (PARTITION BY sub_Id ORDER BY d_time desc) id
        FROM
        oa_display_log
        where sub_Id in
        <foreach collection="subIdList" index="index" item="subId" separator="," open="("
                 close=")">
            #{subId}
        </foreach>
        ) temp
        where a.id=temp.id
    </select>
</mapper>