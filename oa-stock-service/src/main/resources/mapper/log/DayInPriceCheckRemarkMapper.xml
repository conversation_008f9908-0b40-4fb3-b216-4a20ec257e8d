<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.logapi.mapper.DayInPriceCheckRemarkMapper">
    <select id="getLastRecord" resultType="com.jiuji.oa.logapi.pojo.entity.DayInPriceCheckRemark">
        SELECT
            a.*
        FROM
            day_in_price_check_remark a,
            (
                SELECT
                    distinct day_in_price_check_id,FIRST_VALUE(id) OVER (PARTITION BY day_in_price_check_id ORDER BY create_time desc) id
                FROM
                    day_in_price_check_remark
                where day_in_price_check_id in
        <foreach collection="dayInPriceRemarkIdList" index="index" item="dayInPriceRemarkId" separator="," open="(" close=")">
            #{dayInPriceRemarkId}
        </foreach>
            ) temp
        where a.id=temp.id
    </select>
</mapper>