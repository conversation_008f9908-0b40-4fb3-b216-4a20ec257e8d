<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.logapi.mapper.Ok3wQudaoLogMapper">
    <select id="getLastRecord" resultType="com.jiuji.oa.logapi.pojo.entity.Ok3wQudaoLog">
        SELECT a.id,a.d_time,a.in_user,a.`comment`,a.display_id,a.class,a.rollback_id
        FROM ok3w_qudao_log a,
        (
        SELECT
        distinct display_id,FIRST_VALUE(id) OVER (PARTITION BY display_id ORDER BY d_time desc) id
        FROM
        ok3w_qudao_log
        where display_id in
        <foreach collection="displayIdList" index="index" item="displayId" separator="," open="("
                 close=")">
            #{displayId}
        </foreach>
        ) temp
        where a.id=temp.id
    </select>
</mapper>