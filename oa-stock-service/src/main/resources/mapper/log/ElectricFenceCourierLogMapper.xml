<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.logapi.mapper.ElectricFenceCourierLogMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.logapi.pojo.entity.ElectricFenceCourierLog">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="area_id" column="area_id" jdbcType="BIGINT"/>
            <result property="comment" column="comment" jdbcType="VARCHAR"/>
            <result property="create_time" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="update_time" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="is_delete" column="is_delete" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,area_id,comment,
        create_time,update_time,is_delete
    </sql>
</mapper>
