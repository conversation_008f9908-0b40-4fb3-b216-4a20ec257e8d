<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.logapi.mapper.MkcLogNewMapper">
    <select id="getLastRecord" resultType="com.jiuji.oa.logapi.pojo.entity.MkcLogNew">
        SELECT a.id,a.d_time,a.in_user,a.show_type,a.`comment`,a.mkc_id,a.rollback_id
        FROM mkc_log_new a,
        (
        SELECT
        distinct mkc_id,FIRST_VALUE(id) OVER (PARTITION BY mkc_id ORDER BY d_time desc) id
        FROM
        mkc_log_new
        where mkc_id in
        <foreach collection="mkcIdList" index="index" item="mkcId" separator="," open="("
                 close=")">
            #{mkcId}
        </foreach>
        ) temp
        where a.id=temp.id
    </select>
</mapper>