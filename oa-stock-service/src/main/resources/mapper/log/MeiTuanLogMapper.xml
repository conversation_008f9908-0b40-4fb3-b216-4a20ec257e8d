<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.logapi.mapper.MeiTuanLogMapper">
    <select id="getLastRecord" resultType="com.jiuji.oa.logapi.pojo.entity.MeiTuanLog">
        SELECT
        a.*
        FROM
        meituan_log a,
        (
        SELECT
        distinct delivery_id,FIRST_VALUE(id) OVER (PARTITION BY delivery_id ORDER BY d_time desc) id
        FROM
        meituan_log
        where delivery_id in
        <foreach collection="deliveryIdList" index="index" item="deliveryId" separator="," open="("
                 close=")">
            #{deliveryId}
        </foreach>
        ) temp
        where a.id=temp.id
    </select>
</mapper>