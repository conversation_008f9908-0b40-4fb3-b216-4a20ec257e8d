<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.logapi.mapper.QuickPurchaseAccessoryLogMapper">
    <select id="getLastRecord" resultType="com.jiuji.oa.logapi.pojo.entity.QuickPurchaseAccessoryLog">
        SELECT a.id,a.d_time,a.in_user,a.`comment`,a.sub_id,a.show_type,a.rollback_id,a.log_type,a.show_type
        FROM quick_purchase_accessory_log a,
        (
        SELECT
        distinct sub_id,FIRST_VALUE(id) OVER (PARTITION BY sub_id ORDER BY d_time desc) id
        FROM
        quick_purchase_accessory_log
        where sub_id in
        <foreach collection="subIdList" index="index" item="subId" separator="," open="("
                 close=")">
            #{subId}
        </foreach>
        ) temp
        where a.id=temp.id
    </select>
</mapper>