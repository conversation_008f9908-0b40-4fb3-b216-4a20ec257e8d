
-- 创建人：刘昊楠
-- 作用：创建三方平台交互日志
-- 审核人：刘昊楠
-- 备注：
create table  if not exists three_party_platform_log
(
    id          BIGINT(19) auto_increment comment '表id'
    primary key,
    kind        VARCHAR(100)                           null comment '日志类型',
    result      TEXT(65535)                            null comment '返回结果',
    request     TEXT(65535)                            null comment '请求参数',
    create_time DATETIME default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time DATETIME                           null comment '修改时间'
    );

create index three_party_platform_log_kind_index
    on three_party_platform_log (kind);