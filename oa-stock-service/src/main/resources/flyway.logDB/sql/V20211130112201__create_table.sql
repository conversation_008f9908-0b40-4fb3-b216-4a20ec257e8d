CREATE TABLE `interactive_log`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT '表id',
    `kind`        varchar(100) DEFAULT NULL COMMENT '日志类型',
    `tenant`      int(11) DEFAULT NULL COMMENT '租户id',
    `result`      text         DEFAULT NULL COMMENT '返回结果',
    `request`     text         DEFAULT NULL COMMENT '请求参数',
    `create_time` datetime     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime     DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci