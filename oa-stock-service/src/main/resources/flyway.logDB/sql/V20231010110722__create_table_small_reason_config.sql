CREATE TABLE IF NOT EXISTS `small_reason_config` (
                                       `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
                                       `category_id` int(11) DEFAULT NULL COMMENT '分类ID',
                                       `category_name` varchar(50) DEFAULT NULL COMMENT '分类名称',
                                       `source` int(11) DEFAULT NULL COMMENT '来源（1 售前，2 售后）',
                                       `reason` varchar(200) DEFAULT NULL COMMENT '接件原因',
                                       `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                       PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT = '小件故障原因配置表'