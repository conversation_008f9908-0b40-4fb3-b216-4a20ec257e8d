-- 创建人：张友奎
-- 作用：美团闪购配送信息
-- 审核人：谢熊坤
-- 备注：
create table if not exists t_meituan_shangou_peisong
(
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `sub_id` bigint(20) NOT NULL COMMENT 'subId',
    `third_carrier_order_id` varchar(50) NOT NULL COMMENT '第三方配送商物流单号',
    `wuliu_id` varchar(50) DEFAULT NULL COMMENT '物流ID',
    `courier_name` varchar(30) DEFAULT NULL COMMENT '配送员姓名',
    `courier_phone` varchar(20) DEFAULT NULL COMMENT '配送员联系方式',
    `logistics_status` tinyint(4) DEFAULT NULL COMMENT '配送状态code',
    `logistics_provider_code` varchar(10) DEFAULT NULL COMMENT '配送此订单商品的物流平台',
    `latitude` varchar(20) DEFAULT '0' COMMENT '骑手当前的纬度',
    `longitude` varchar(20) DEFAULT '0' COMMENT '骑手当前的经度',
    `coordsys` varchar(10) DEFAULT NULL COMMENT '坐标系',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_delete` tinyint(4) DEFAULT '0' COMMENT '删除标识',
    `platform` varchar(30) DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `idx_sub_id` (`sub_id`),
    KEY `idx_wuliu_id` (`wuliu_id`),
    KEY `idx_third_carrier_order_id` (`third_carrier_order_id`)
    ) COMMENT='美团闪购配送信息';