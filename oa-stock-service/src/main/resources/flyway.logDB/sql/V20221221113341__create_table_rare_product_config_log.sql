-- 创建人：张友奎
-- 作用：稀缺商品配置日志
-- 审核人：张友奎
-- 备注：
CREATE TABLE if not exists `rare_product_config_log` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id' PRIMARY KEY,
    `rare_product_config_id` bigint(20) NOT NULL COMMENT '稀缺商品配置id',
    `comment` text COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '日志信息',
    `inuser` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作用户',
    `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否逻辑删除，0为未删除，1为已删除',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
    );

create index idx_rare_product_config_id
    on rare_product_config_log (rare_product_config_id);