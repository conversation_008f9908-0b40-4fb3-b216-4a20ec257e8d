CREATE TABLE IF NOT EXISTS `pjt_log`  (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `ref_id` bigint(20) NOT NULL COMMENT '关联操作id',
    `oper_business` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作业务',
    `oper_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作类型',
    `oper_type_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作类型名称',
    `oper_content` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作内容',
    `oper_user_id` int(11) NOT NULL COMMENT '操作用户id',
    `oper_user_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作用户名字',
    `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
    `remarks` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '附注，存储特殊数据',
    PRIMARY KEY (`id`) USING BTREE
    ) ENGINE = InnoDB AUTO_INCREMENT = 1647893346913060146 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'pop商品日志表' ROW_FORMAT = Compact;
