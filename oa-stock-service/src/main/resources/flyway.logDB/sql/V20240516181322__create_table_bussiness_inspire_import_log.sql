create table if not exists bussiness_inspire_import_log
(
    id             int auto_increment comment '主键'
    primary key,
    file_name      varchar(1024)                      not null comment '文件名称',
    file_content   json                               not null comment '文件内容',
    create_time    datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    create_user_id int                                not null comment '创建人',
    is_del         bit      default b'0'              not null comment '是否删除'
    )
    comment '销售激励导入日志' collate = utf8mb4_general_ci;