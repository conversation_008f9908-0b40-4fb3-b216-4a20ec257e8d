-- 创建人：张友奎
-- 作用：订单动态轨迹线路
-- 审核人：王建洪
-- 备注：

create table if not exists `wuliu_route_track` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `sub_id` bigint(20) NOT NULL COMMENT 'subId',
    `sub_type` tinyint(4) NOT NULL COMMENT '订单类型（1-新机单，2-良品单）',
    `line_type` tinyint(4) NOT NULL COMMENT '线路类型（1-调拨，2-配送）',
    `dispatch_status` tinyint(4) NOT NULL COMMENT '配送状态（1-运输中，2-配送中）',
    `wuliu_id` bigint(20) DEFAULT NULL COMMENT '物流id',
    `status` tinyint(4) DEFAULT NULL COMMENT '状态',
    `current_latitude` varchar(20) NOT NULL DEFAULT '0' COMMENT '当前的纬度',
    `current_longitude` varchar(20) NOT NULL DEFAULT '0' COMMENT '当前的经度',
    `send_latitude` varchar(20) NOT NULL DEFAULT '0' COMMENT '寄件的纬度',
    `send_longitude` varchar(20) NOT NULL DEFAULT '0' COMMENT '寄件的经度',
    `receiver_latitude` varchar(20) NOT NULL DEFAULT '0' COMMENT '收件的纬度',
    `receiver_longitude` varchar(20) NOT NULL DEFAULT '0' COMMENT '收件的经度',
    `waypoints` varchar(500) DEFAULT NULL COMMENT '路由途径点',
    `image` varchar(300) DEFAULT NULL COMMENT '图片地址',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_delete` tinyint(4) DEFAULT '0' COMMENT '删除标识',
    PRIMARY KEY (`id`),
    KEY `wuliu_route_track_wuliu_id_idx` (`wuliu_id`),
    KEY `wuliu_route_track_sub_id_type_idx` (`sub_id`,`sub_type`)
    ) COMMENT='快递物流轨迹节点';

create table if not exists `wuliu_kuaidi100_subscribe` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `xtenant` bigint(20) NOT NULL COMMENT 'xtenant',
    `wuliu_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '物流id',
    `com` varchar(50) DEFAULT NULL COMMENT '快递公司',
    `nu` varchar(50) DEFAULT NULL COMMENT '快递单号',
    `send_address` varchar(200) DEFAULT NULL COMMENT '寄件地址',
    `receiver_address` varchar(200) DEFAULT NULL COMMENT '收件地址',
    `phone` varchar(20) DEFAULT NULL COMMENT '寄件或收件号码',
    `result` tinyint(4) DEFAULT NULL COMMENT '快递100订阅结果',
    `return_code` varchar(20) DEFAULT NULL COMMENT '快递100返回码',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_delete` tinyint(4) DEFAULT '0' COMMENT '删除标识',
    PRIMARY KEY (`id`),
    KEY `wuliu_kuaidi100_subscribe_nu_idx` (`nu`),
    KEY `wuliu_kuaidi100_subscribe_wuliu_id_idx` (`wuliu_id`)
    ) COMMENT='快递100订阅';

create table if not exists `wuliu_express_route` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `nu` varchar(50) DEFAULT NULL COMMENT '快递单号',
    `opcode` varchar(50) DEFAULT NULL COMMENT '快递路由操作码',
    `context` varchar(500) DEFAULT NULL COMMENT '快递路由详情',
    `city` varchar(50) DEFAULT NULL COMMENT '快递路由详情',
    `location` varchar(20) DEFAULT NULL COMMENT '快递路由详情',
    `optime` datetime DEFAULT NULL COMMENT '快递路由时间',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_delete` tinyint(4) DEFAULT '0' COMMENT '删除标识',
    `xtenant` bigint(20) NOT NULL DEFAULT '0',
    `data_md5` varchar(100) NOT NULL,
    PRIMARY KEY (`id`),
    KEY `wuliu_express_route_nu_idx` (`nu`)
    ) COMMENT='快递路由轨迹记录';