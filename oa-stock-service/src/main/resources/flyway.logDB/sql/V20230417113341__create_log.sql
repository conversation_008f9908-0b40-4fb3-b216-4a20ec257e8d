CREATE TABLE IF NOT EXISTS  `barCode_manage_log`(
                                                    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '表id',
                                                    `ppriceid` int NOT NULL COMMENT 'ppriceid',
                                                    `comment` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '注释',
    `in_user_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作用户',
    `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否逻辑删除，0为未删除，1为已删除',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
    ) ENGINE=InnoDB AUTO_INCREMENT=42 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品SN设置日志表';

CREATE TABLE if not exists `stock_electric_fence_log` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '表id',
    `city_id` int NOT NULL COMMENT '配置id',
    `comment` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '注释',
    `in_user_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作用户',
    `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否逻辑删除，0为未删除，1为已删除',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
    ) ENGINE=InnoDB AUTO_INCREMENT=53 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='电子围栏日志表';

CREATE TABLE if not exists `wuliu_develiver_log` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '表id',
    `wuliu_develiver_Id` int NOT NULL COMMENT '配置id',
    `comment` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '注释',
    `in_user_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作用户',
    `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否逻辑删除，0为未删除，1为已删除',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
    ) ENGINE=InnoDB AUTO_INCREMENT=42 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='物流路线修改日志';
