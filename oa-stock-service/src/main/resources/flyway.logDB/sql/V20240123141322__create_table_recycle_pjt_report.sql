CREATE TABLE IF NOT EXISTS `recycle_pjt_report` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `pjt_pid` int(11) DEFAULT NULL COMMENT '拍机堂pid',
    `area_id` int(11) DEFAULT NULL COMMENT 'areaid',
    `ppid` int(11) NOT NULL COMMENT 'ppid',
    `pid` int(11) NOT NULL COMMENT 'pid',
    `good_id` int(11) NOT NULL COMMENT 'good_id',
    `client` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '评估平台',
    `report_no` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '质检报告编号',
    `property_name_id` int(11) NOT NULL COMMENT '属性名称id',
    `property_name` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '属性名称',
    `property_value_id` int(11) NOT NULL COMMENT '属性值id',
    `property_value_name` varchar(128) COLLATE utf8mb4_general_ci NOT NULL COMMENT '属性值名称',
    `sku_property_flag` tinyint(4) DEFAULT NULL COMMENT '是否sku属性',
    `better_flag` tinyint(4) DEFAULT NULL COMMENT '是否正常项',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_delete` tinyint(4) DEFAULT '0' COMMENT '删除标识',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT = '拍机堂验机报告记录表'