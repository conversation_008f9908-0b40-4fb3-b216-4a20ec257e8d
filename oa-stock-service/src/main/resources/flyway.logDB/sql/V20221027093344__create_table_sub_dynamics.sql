-- 创建人：王建洪
-- 作用：订单动态进程表
-- 审核人：谢熊坤
-- 备注：

create table if not exists sub_dynamics
(
    id            BIGINT(19) auto_increment comment '自增主键'
        primary key,
    sub_id        BIGINT UNSIGNED not null comment '订单号单号',
    sub_type      BIGINT UNSIGNED not null comment '订单类型，1 新机，3 良品',
    business_id   BIGINT UNSIGNED null comment '业务id',
    business_node INT(10)         null comment '业务节点',
    message       VARCHAR(1024)   null comment '业务日志',
    dynamics_node INT(10)         null comment '订单动态节点',
    wuliu_id      VARCHAR(50)     null comment '物流单号',
    create_time   DATETIME    not null comment '日志创建时间',
    update_time   DATETIME    not null comment '日志更新时间',
    extend        TEXT(65535)     null comment '扩展信息',
    data_md5      VARCHAR(200)    null comment '数据md5'
);

create index if not exists sub_dynamics_sub_id_sub_type_ppid_index
    on sub_dynamics (sub_id, sub_type, business_id);