-- ----------------------------
-- Table structure for day_in_price_check_remark
-- ----------------------------
DROP TABLE IF EXISTS `day_in_price_check_remark`;
CREATE TABLE `day_in_price_check_remark`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '表id',
  `ch999_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工号',
  `ch999_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作人姓名',
  `content` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `day_in_price_check_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '采购核价id',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NUll ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NULL DEFAULT 0 COMMENT '逻辑删除标识，0为未删除，1为已删除',
  `rollback_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'C#指定id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_day_in_price_check_id`(`day_in_price_check_id`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE,
  INDEX `idx_rollback_id`(`rollback_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for meituan_log
-- ----------------------------
DROP TABLE IF EXISTS `meituan_log`;
CREATE TABLE `meituan_log`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '表id',
  `delivery_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '快递id',
  `meituan_peisong_id` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '美团配送id',
  `status` int(2) NULL DEFAULT NULL COMMENT '状态号',
  `status_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '状态名',
  `courier_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配送员姓名',
  `courier_phone` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配送员电话',
  `cancel_reason_id` int(2) NULL DEFAULT NULL COMMENT '取消id',
  `cancel_reason` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '取消原因',
  `timestamp` bigint(20) NULL DEFAULT NULL COMMENT '时间戳',
  `time` datetime NULL DEFAULT NULL COMMENT '待确定',
  `d_time` datetime NULL DEFAULT NULL COMMENT '操作时间',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NUll ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NULL DEFAULT 0 COMMENT '逻辑删除标识，0为未删除，1为已删除',
  `rollback_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'C#指定id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_delivery_id`(`delivery_id`) USING BTREE,
  INDEX `idx_rollback_id`(`rollback_id`) USING BTREE,
  INDEX `idx_d_time`(`d_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for quick_purchase_accessory_log
-- ----------------------------
DROP TABLE IF EXISTS `quick_purchase_accessory_log`;
CREATE TABLE `quick_purchase_accessory_log`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主表id',
  `sub_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `d_time` datetime NULL DEFAULT NULL COMMENT '操作时间',
  `comment` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `in_user` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作人',
  `log_type` tinyint(1) NULL DEFAULT NULL COMMENT '日志类型',
  `show_type` tinyint(1) NULL DEFAULT NULL COMMENT '显示在前台标识',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NUll ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NULL DEFAULT 0 COMMENT '逻辑删除标识，0为未删除，1为已删除',
  `rollback_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'C#指定id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_sub_id`(`sub_id`) USING BTREE,
  INDEX `idx_rollback_id`(`rollback_id`) USING BTREE,
  INDEX `idx_d_time`(`d_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Compact;