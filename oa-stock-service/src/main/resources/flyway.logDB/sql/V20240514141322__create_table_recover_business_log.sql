CREATE TABLE IF NOT EXISTS `recover_business_log` (
    id            bigint auto_increment primary key,
    business_id   int                                null comment '业务主键',
    business_type int                                null comment '业务类型',
    opt_type      int                                not null comment '操作类型',
    title         varchar(150)                       not null comment '标题',
    content       text                               not null comment '内容',
    create_user   varchar(32)                        not null comment '创建人',
    create_time   datetime default CURRENT_TIMESTAMP null comment '创建时间'
    ) ENGINE=InnoDB COMMENT = '回收业务日志表'


