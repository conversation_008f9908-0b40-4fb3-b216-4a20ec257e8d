<?xml version="1.0" encoding="UTF-8"?>
<configuration status="WARN" monitorinterval="30">

    <Properties>
        <Property name="log.pattern">%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</Property>
    </Properties>

    <appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="${log.pattern}"/>
        </Console>

        <Socket name="elkAppender" host="logserver" port="10516" protocol="udp">
            <ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
            <JSONLog4j2Layout singleLine="false" locationInfo="true">
                <UserField>
                    <key>ServiceName</key>
                    <value>oajava-stock</value>
                </UserField>
                <UserField>
                    <key>TenantId</key>
                    <value>${sys:TenantId}</value>
                </UserField>
            </JSONLog4j2Layout>
        </Socket>

    </appenders>

    <loggers>
        <root level="INFO">
            <appender-ref ref="Console"/>
            <appender-ref ref="elkAppender"/>
        </root>
    </loggers>

</configuration>
