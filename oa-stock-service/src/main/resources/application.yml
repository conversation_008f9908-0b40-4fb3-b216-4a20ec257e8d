server:
  servlet:
    context-path: /oa-stock
  port: 10039
springdoc:
  swagger-ui:
    enabled: false
#apollo:
#  meta: http://**************:8080
#  bootstrap:
#    enabled: true
#    #暂时写死都用九机的，代码内判断只有九机才有切换数据源的功能
#    namespaces: application-jiuji-stock.yml
#    eagerLoad:
#      enabled: true

spring:
  cloud:
    inetutils:
      ignored-interfaces:
        - docker0
        - veth.*
      timeout-seconds: 5
    consul:
      host: ${spring.cloud.client.hostname}
      port: 8500
      discovery:
        register: true #注册到consul
        prefer-ip-address: true
        instance-group: sim
        service-name: ${spring.application.name}
        instance-id: ${spring.application.name}-${spring.cloud.consul.discovery.instance-zone}-${spring.cloud.consul.discovery.instance-group}-${spring.cloud.client.hostname}-${server.port}
        #健康检查失败多久强制取消服务注册
        health-check-critical-timeout: 120s
        #heartbeat:
        #  enabled: true
        tags: traefik.frontend.rule=Host:oa-stock
        health-check-url: http://${spring.cloud.client.ipaddress}:${server.port}/${server.servlet.context-path}/actuator/info
        default-query-tag: zone=${spring.cloud.consul.discovery.instance-zone}
        instance-zone: ${instance-zone}
      config:
        # enabled为true表示启用配置管理功能
        enabled: true
  messages:
    basePackages: com.jiuji.oa.nc.common.source
    basename: i18n/url,i18n/msg
  http:
    encoding:
      force-response: true
  profiles:
    active: local
  application:
    name: oa-stock-service

  main:
    allow-bean-definition-overriding: true
  # 数据库配置
  datasource:
    dynamic:
      primary: oa_nc #设置默认的数据源或者数据源组,默认值即为master
      strict: true #设置严格模式,默认false不启动. 启动后在未匹配到指定数据源时候会抛出异常,不启动则使用默认数据源.
      datasource:
        oa_nc:
          url: jdbc:mysql://${mysql.oa_nc.url}/${mysql.oa_nc.dbname}?useUnicode=true&characterEncoding=utf8&serverTimezone=Asia/Shanghai&autoReconnect=true&autoReconnectForPools=true&rewriteBatchedStatements=true
          username: ${mysql.oa_nc.username}
          password: ${mysql.oa_nc.password}
          type: ${mysql.datasource.type}
          driver-class-name: ${mysql.datasource.driver-class-name}
        ch999oanew:
          url: jdbc:sqlserver://${sqlserver.ch999oanew.host}:${sqlserver.ch999oanew.port};databaseName=${sqlserver.ch999oanew.dbname};applicationName=java_${spring.application.name}
          username: ${sqlserver.ch999oanew.username}
          password: ${sqlserver.ch999oanew.password}
          type: ${sqlserver.datasource.type}
          driver-class-name: ${sqlserver.datasource.driver-class-name}
          max-pool-size: ${sqlserver.datasource.max-pool-size}
        ch999oanew2:
          url: jdbc:sqlserver://${sqlserver.ch999oanew2.host:}:${sqlserver.ch999oanew2.port:};databaseName=${sqlserver.ch999oanew2.dbname:};applicationName=java_${spring.application.name}
          username: ${sqlserver.ch999oanew2.username:}
          password: ${sqlserver.ch999oanew2.password:}
          type: ${sqlserver.datasource.type}
          driver-class-name: ${sqlserver.datasource.driver-class-name}
        ch999oanewReport:
          url: jdbc:sqlserver://${sqlserver.ch999oanewReport.host}:${sqlserver.ch999oanewReport.port};databaseName=${sqlserver.ch999oanewReport.dbname};applicationName=java_${spring.application.name}
          username: ${sqlserver.ch999oanewReport.username}
          password: ${sqlserver.ch999oanewReport.password}
          type: ${sqlserver.datasource.type}
          driver-class-name: ${sqlserver.datasource.driver-class-name}
          max-pool-size: ${sqlserver.datasource.max-pool-size}
        ch999oahis:
          url: jdbc:sqlserver://${sqlserver.ch999oahis.host}:${sqlserver.ch999oahis.port};databaseName=${sqlserver.ch999oahis.dbname};applicationName=java_${spring.application.name}
          username: ${sqlserver.ch999oahis.username}
          password: ${sqlserver.ch999oahis.password}
          type: ${sqlserver.datasource.type}
          driver-class-name: ${sqlserver.datasource.driver-class-name}
          max-pool-size: ${sqlserver.datasource.max-pool-size}
        oanewWrite:
          url: jdbc:sqlserver://${sqlserver.oanewWrite.host}:${sqlserver.oanewWrite.port};databaseName=${sqlserver.oanewWrite.dbname};applicationName=java_${spring.application.name}
          username: ${sqlserver.oanewWrite.username}
          password: ${sqlserver.oanewWrite.password}
          type: ${sqlserver.datasource.type}
          driver-class-name: ${sqlserver.datasource.driver-class-name}
          max-pool-size: ${sqlserver.datasource.max-pool-size}
        officeWrite:
          url: jdbc:sqlserver://${sqlserver.officeWrite.host}:${sqlserver.officeWrite.port};databaseName=${sqlserver.officeWrite.dbname};applicationName=java_${spring.application.name}
          username: ${sqlserver.officeWrite.username}
          password: ${sqlserver.officeWrite.password}
          type: ${sqlserver.datasource.type}
          driver-class-name: ${sqlserver.datasource.driver-class-name}
          max-pool-size: ${sqlserver.datasource.max-pool-size}
        office2:
          url: jdbc:sqlserver://${sqlserver.office2.host:}:${sqlserver.office2.port:};databaseName=${sqlserver.office2.dbname:};applicationName=java_${spring.application.name}
          username: ${sqlserver.office2.username:}
          password: ${sqlserver.office2.password:}
          type: ${sqlserver.datasource.type}
          driver-class-name: ${sqlserver.datasource.driver-class-name}
          max-pool-size: ${sqlserver.datasource.max-pool-size}
        logDB:
          url: jdbc:mysql://${tidb.logdb.url}/${tidb.logdb.dbname}?useUnicode=true&characterEncoding=utf8&serverTimezone=Asia/Shanghai&autoReconnect=true&autoReconnectForPools=true&allowMultiQueries=true&rewriteBatchedStatements=true
          username: ${tidb.logdb.username}
          password: ${tidb.logdb.password}
          type: ${mysql.datasource.type}
          driver-class-name: ${mysql.datasource.driver-class-name}
        oa_log:
          url: jdbc:mysql://${mysql.oa_log.url}/${mysql.oa_log.dbname}?useUnicode=true&characterEncoding=utf8&serverTimezone=Asia/Shanghai&autoReconnect=true&autoReconnectForPools=true&allowMultiQueries=true&rewriteBatchedStatements=true
          username: ${mysql.oa_log.username}
          password: ${mysql.oa_log.password}
          type: ${mysql.datasource.type}
          driver-class-name: ${mysql.datasource.driver-class-name}
        appleSn:
          url: jdbc:mysql://${tidb.appleSn.url}/${tidb.appleSn.dbname}?useUnicode=true&characterEncoding=utf8&serverTimezone=Asia/Shanghai&autoReconnect=true&autoReconnectForPools=true&allowMultiQueries=true&rewriteBatchedStatements=true
          username: ${tidb.appleSn.username}
          password: ${tidb.appleSn.password}
          type: ${mysql.datasource.type}
          driver-class-name: ${mysql.datasource.driver-class-name}
        web999_other:
          url: jdbc:sqlserver://${sqlserver.web999_other.host}:${sqlserver.web999_other.port};databaseName=${sqlserver.web999_other.dbname};applicationName=java_${spring.application.name}
          username: ${sqlserver.web999_other.username}
          password: ${sqlserver.web999_other.password}
          type: ${sqlserver.datasource.type}
          driver-class-name: ${sqlserver.datasource.driver-class-name}
          max-pool-size: ${sqlserver.datasource.max-pool-size}
        ershou:
          url: jdbc:sqlserver://${sqlserver.ershou.host}:${sqlserver.ershou.port};databaseName=${sqlserver.ershou.dbname};applicationName=java_${spring.application.name}
          username: ${sqlserver.ershou.username}
          password: ${sqlserver.ershou.password}
          type: ${sqlserver.datasource.type}
          driver-class-name: ${sqlserver.datasource.driver-class-name}
          max-pool-size: ${sqlserver.datasource.max-pool-size}
        starRocks:
          username: ${mysql.starrocks.username}
          password: ${mysql.starrocks.password}
          url: jdbc:mysql://${mysql.starrocks.url}/${mysql.starrocks.dbname}?characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai&autoReconnect=true&autoReconnectForPools=true&allowMultiQueries=true&rewriteBatchedStatements=true
          type: ${mysql.datasource.type}
          driver-class-name: ${mysql.datasource.driver-class-name}

  redis:
    url: redis://${redis.oa.url}/
    host: ${redis.oa.host}
    port: ${redis.oa.port}
    password: ${redis.oa.password}
    jedis:
      pool:
        max-active: 128
        max-idle: 16
        min-idle: 0
    timeout: 30s
  # 上传文件配置
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 1024MB

sqlserver:
  datasource:
    driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
    type: com.zaxxer.hikari.HikariDataSource
mysql:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource

# mybatis-plus配置
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml

  global-config:
    sql-parser-cache: true
    db-config:
      id-type: AUTO
      field-strategy: not_empty
      table-underline: true #下划线驼峰模式
      logic-delete-value: 1
      logic-not-delete-value: 0
  configuration:
    map-underscore-to-camel-case: true #下划线驼峰模式
    cache-enabled: false
# jetcache缓存配置
jetcache:
  statIntervalMinutes: 60
  areaInCacheName: false
  hiddenPackages: com.jiuji
  local:
    default:
      type: caffeine
      limit: 100
      keyConvertor: fastjson
      expireAfterWriteInMillis: 3600000
  remote:
    default:
      type: redis.lettuce
      keyConvertor: fastjson
      valueEncoder: kryo
      valueDecoder: kryo
      expireAfterWriteInMillis: 21600000
      poolConfig:
        minIdle: 5
        maxIdle: 20
        maxTotal: 50
      uri: redis://${redis.oa.url}/

# MQ配置
rabbitmq:
  defaultMq: oa
  multiple:
    oa:
      host: ${rabbitmq.oa.url}
      port: ${rabbitmq.oa.port}
      username: ${rabbitmq.oa.username}
      password: ${rabbitmq.oa.password}
      virtual-host: ${rabbitmq.oa.vhost}
      listener:
        type: SIMPLE
        simple:
          auto-startup: true
          concurrency: 5
          max-concurrency: 15
          acknowledge-mode: AUTO
          retry:
            enabled: true
            max-attempts: 5
            max-interval: 12000
    oaAsync:
      host: ${rabbitmq.oaAsync.url}
      port: ${rabbitmq.oaAsync.port}
      username: ${rabbitmq.oaAsync.username}
      password: ${rabbitmq.oaAsync.password}
      virtual-host: ${rabbitmq.oaAsync.vhost}

# 日志配置，默认使用dev
logging:
  config: classpath:log/log4j2.xml

# 应用配置
#9机短信接口地址
sms:
  url: https://www.9xun.com
  sendEmailUrl: http://sms.ch999.com.cn/email/email.aspx
#图片服务器地址
image:
  uploadImgUrl: ${image.upload.url}
  delImgUrl: ${image.del.url}
  selectImgUrl: ${image.select.url}
  uploads:
    url: https://upload.9xun.com

#prometheus 配置
management:
  endpoints:
    web:
      exposure:
        include: "info,health,prometheus"
  metrics:
    tags:
      application: ${spring.application.name}

# 发送消息组件用到的配置
msg:
  smsHost: ${sms.url}
  appId: "vhwcwFYY"
  secretKey: "x*6EM1nsWf"
  newUrl: https://www.9xun.com/cloudapi_nc/api/sendmsg?xservicename=sms

mqtt:
  host: ${mqtt.host}
  topic: ${mqtt.topic}
  clientinid: nc-segments-${random.value}
  qoslevel: 1
  username: ${mqtt.username}
  password: ${mqtt.password}
  timeout: 10000
  keepalive: 20
  adminUrl: http://************:8081/api/v4
  adminUser: admin
  adminPassword: public

qrCode:
  topic: 9xunyun/pda/
  frequency: 60000 #二维码刷新1分钟 60000毫秒

geek:
  #  serverUrl : http://**************:8082/geekplus/api/artemis
  serverUrl: http://**********:8082/geekplus/api/artemis
  warehouseCode: JJJF
  ownerCode: JJJF001
  userId: wuyaxo
  userKey: a1122334

# mongodb
mongodb:
  url:
    ch999oa: ${mongodb.url1}
chw:
  tenant: https://manager.saas.ch999.cn/saasManager/api/thirdParty/getAllDomains/v1
  warehousing: ${chw.warehousing}
  cancelCaigou: ${chw.cancelCaigou}
  platformUrl: ${chw.platformUrl}

Inventory:
  downloadExcl:${inventory.downloadExcl}
purchase:
  downloadExcl:${purchase.downloadExcl}

stockoutarea:
  downloadExcel: https://img.9xun.com/newstatic/2373/03257f4f2bc76cc1.xlsx
stockoutposition:
  downloadExcel: https://img.9xun.com/newstatic/2373/03257f4f2bc76cc1.xlsx
stockoutcontainer:
  downloadExcel: https://img.9xun.com/newstatic/2373/03257f4f2bc76cc1.xlsx
syshost:
  wcf:

# 请求宝尊接口 url
baozun:
  url:


# TiDB
tidb:
  logdb:
    url: ${mysql.oa_log.url}
    dbname: ${mysql.oa_log.dbname}
    username: ${mysql.oa_log.username}
    password: ${mysql.oa_log.password}
  appleSn:
    url: ${mysql.appleSn.url}
    dbname: ${mysql.appleSn.dbname}
    username: ${mysql.appleSn.username}
    password: ${mysql.appleSn.password}



# 请求通讯录上级接口
userChiefs:
  url: https://moa.9ji.com/app/GetUserChiefs?ch999Id=


oa:
  subOutInUrl: ${subOutIn.url}
  calcEmployeePirceUrl: ${calcEmployeePirce.url}

app:
  id: oa-stockService
apollo:
  meta: ${apollo.url:}
  bootstrap:
    enabled: true
    namespaces: ${apollo.file:}


#延迟队列配置 生产环境
lmstfy:
  host: "lmstfy.service.ch999.cn"
  namespace:
  token:
  # 重试次数
  retryTimes: 2
  # 重试时间间隔（单位：毫秒）
  retryIntervalMilliseconds: 1000
  # 读 超时时间（单位：秒）
  readTimeoutSecond: 5
  # 写 超时时间（单位：秒）
  writeTimeoutSecond: 5
  # 连接 超时时间（单位：秒）
  connectTimeoutSecond: 5
  mult:
    first-lmstfy-client:
      namespace: oa_stock
      token: "01G4EF3K0XWN47VZM2E4F0C7SW"
purchase.handleStock: ${jiuji.sys.inWcf}/oaApi.svc/rest/BatchProductKc

fileService:
  appId: 190002
  publicKey: 'MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBALZk8XdJlRaATmb01CrWRbAu2Civz2hNZIW25RgyGCerg5YIMoDLP18iasDIGsc6iJy2YQeOTp60rkmRGJiYAGECAwEAAQ=='

active-instance-zone: ${active-zone:${instance-zone}}

feign:
  hystrix:
    enabled: true
    exclude-client-names: com.jiuji.cloud.logistics.service.WmsShelveCloud, com.jiuji.oa.orginfo.departinfo.client.DepartInfoClient
      ,com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient,com.jiuji.oa.orginfo.areainfo.client.AreaListClient,com.jiuji.cloud.office.service.ContractCloud
      ,com.jiuji.oa.office.api.AuthorizeSwitchClient
  client:
    config:
      default:
        connectTimeout: 120000
        readTimeout: 120000

hystrix:
  command:
    default:
      execution:
        timeoutInMilliseconds: 120000
        isolation:
          strategy: SEMAPHORE
          maxConcurrentRequests: 2000
          thread:
            timeoutInMilliseconds: 120000

#太力
tlmall:
  url: ${tlmall.url}
  saasCode: ${tlmall.saasCode}
  custEncType: ${tlmall.custEncType}
  custEncKey: ${tlmall.custEncKey}
  signType: ${tlmall.signType}
  custSignPubkey: ${tlmall.custSignPubkey}
  custSignPrikey: ${tlmall.custSignPrikey}
  ipList: ${tlmall.ipList}
ptac:
  tokenurl: ${ptac.tokenurl}
  apiurl: ${ptac.apiurl}
