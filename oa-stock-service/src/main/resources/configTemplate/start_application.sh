#!/bin/sh
JAR_NAME=$1
TENANT_ID=$2
SERVER_PORT=$3
GROUP=$4
JAVA_OPTS=$5
JAVA_MEM_OPTS=$6
JAR_PATH=$7
PROFILE=$8
HOSTNAME=$9

cd /opt


echo ${TENANT_ID} | grep -q '[^0-9]'
if [[ $? != 0 ]]; then
    cd /opt
    /opt/saas-config-generator -tpl saas-template.yml -output application-${TENANT_ID}.yml -tenantId ${TENANT_ID}
fi
java \
    -javaagent:/pinpoint-agent/pinpoint-bootstrap.jar \
    -Dpinpoint.agentId=${HOSTNAME}_${SERVER_PORT} \
    -Dpinpoint.applicationName=${JAR_NAME} \
    -Djava.awt.headless=true -Djava.net.preferIPv4Stack=true -Duser.timezone=Asia/Shanghai -server -XX:+DisableExplicitGC -Xtune:virtualized -Xshareclasses -Xquickstart -Djava.security.egd=file:/dev/./urandom \
    -DTenantId=${TENANT_ID} \
    ${JAVA_OPTS} \
    ${JAVA_MEM_OPTS} \
    -jar ${JAR_PATH} \
    --spring.profiles.active=${PROFILE} \
    --spring.cloud.consul.discovery.instance-group=${GROUP} \
    --spring.cloud.consul.discovery.instance-zone=${TENANT_ID} \
    --server.port=${SERVER_PORT} \
    --spring.cloud.consul.discovery.ip-address=${HOSTNAME}