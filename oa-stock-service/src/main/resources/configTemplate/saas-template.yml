consul:
  host: 127.0.0.1
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://{{.DomainImg.Domain}}/
  upload:
    url: http://**************:9333
instance-zone: {{.TenantId}}
jiuji:
  sys:
    moa: https://{{.DomainMoa.Domain}}
    pc: https://{{.DomainOa.Domain}}
    m: https://{{.DomainM.Domain}}
    inWcf: http://{{.DomainInWcf.Domain}}
    oaWcf: http://{{.DomainOaWcf.Domain}}
    xtenant: {{.TenantId}}
  xtenant: {{.XTenant}}
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__{{.TenantId}}:{{.MongoCh999Oa.Pwd}}@{{.MongoCh999Oa.Host}}/ch999oa__{{.TenantId}}
  url1: mongodb://ch999oa__{{.TenantId}}:{{.MongoCh999Oa.Pwd}}@{{.MongoCh999Oa.Host}}/ch999oa__{{.TenantId}}
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname: {{.DBManageTraining.Name}}
    password: {{.DBManageTraining.Pwd}}
    url: {{.DBManageTraining.Host}}:3306
    username: {{.DBManageTraining.Username}}
  oa_core:
    dbname: {{.DBOaCore.Name}}
    password: {{.DBOaCore.Pwd}}
    url: {{.DBOaCore.Host}}:3306
    username: {{.DBOaCore.Username}}
  oa_nc:
    dbname: {{.DBOaNc.Name}}
    password: '{{.DBOaNc.Pwd}}'
    url: {{.DBOaNc.Host}}:3306
    username: {{.DBOaNc.Username}}
  train:
    dbname: {{.DBTrain.Name}}
    password: "{{.DBTrain.Pwd}}"
    url: {{.DBTrain.Host}}:3306
    username: {{.DBTrain.Username}}
  oa_log:
    dbname: {{.DBOaLog.Name}}
    password: "{{.DBOaLog.Pwd}}"
    url: {{.DBOaLog.Host}}:{{.DBOaLog.Port}}
    username: {{.DBOaLog.Username}}
  appleSn:
    dbname: appleSn_9ji
    password: google00
    url: ************:3306
    username: appleSn_9ji
  starrocks:
    url: *************:19030
    dbname: ods
    username: dev_operation
    password: osPIRH2dT47

office:
  sys:
    xtenant: {{.TenantId}}
rabbitmq:
  master:
    password: {{.RabbitMQOaAsync.Pwd}}
    port: {{.RabbitMQOaAsync.Port}}
    url: {{.RabbitMQOaAsync.Host}}
    username: {{.RabbitMQOaAsync.Username}}
    vhost: {{.RabbitMQOaAsync.VHost}}
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: {{.RabbitMQOa.Pwd}}
    port: {{.RabbitMQOa.Port}}
    url: {{.RabbitMQOa.Host}}
    username: {{.RabbitMQOa.Username}}
    vhost: {{.RabbitMQOa.VHost}}
  oaAsync:
    password: {{.RabbitMQOaAsync.Pwd}}
    port: {{.RabbitMQOaAsync.Port}}
    url: {{.RabbitMQOaAsync.Host}}
    username: {{.RabbitMQOaAsync.Username}}
    vhost: {{.RabbitMQOaAsync.VHost}}
  printer:
    password: {{.RabbitMQPrinter.Pwd}}
    port: {{.RabbitMQPrinter.Port}}
    url: {{.RabbitMQPrinter.Host}}
    username: {{.RabbitMQPrinter.Username}}
    vhost: {{.RabbitMQPrinter.VHost}}
redis:
  oa:
    host: {{.RedisOA.MasterHost}}
    password: {{.RedisOA.Pwd}}
    port: {{.RedisOA.MasterPort}}
    url: {{.RedisOA.Pwd}}@{{.RedisOA.Master}}
sms:
  send:
    email:
      url: http://{{.DomainSms.Domain}}/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://{{.DomainSms.Domain}}/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: {{.TenantId}}
sqlserver:
  after_write:
    dbname: {{.DBCh999OaNew.Name}}
    host: {{.DBCh999OaNew.Host}}
    password: "{{.DBCh999OaNew.Pwd}}"
    port: 1433
    username: {{.DBCh999OaNew.Username}}
  ch999oanew:
    dbname: {{.DBCh999OaNew.Name}}
    host: {{.DBCh999OaNew.Host}}
    password: "{{.DBCh999OaNew.Pwd}}"
    port: 1433
    username: {{.DBCh999OaNew.Username}}
  ch999oanewReport:
    dbname: {{.DBCh999OaNew.Name}}
    host: {{.DBCh999OaNew.Host}}
    password: "{{.DBCh999OaNew.Pwd}}"
    port: 1433
    username: {{.DBCh999OaNew.Username}}
  ch999oanewHis:
    dbname: {{.DBCh999OaNew.Name}}
    host: {{.DBCh999OaNew.Host}}
    password: "{{.DBCh999OaNew.Pwd}}"
    port: 1433
    username: {{.DBCh999OaNew.Username}}
  ch999oahis:
    dbname: {{.DBCh999OaNew.Name}}
    host: {{.DBCh999OaNew.Host}}
    password: "{{.DBCh999OaNew.Pwd}}"
    port: 1433
    username: {{.DBCh999OaNew.Username}}
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: {{.DBOffice.Name}}
    host: {{.DBOffice.Host}}
    password: "{{.DBOffice.Pwd}}"
    port: 1433
    username: {{.DBOffice.Username}}
  oanewWrite:
    dbname: {{.DBCh999OaNew.Name}}
    host: {{.DBCh999OaNew.Host}}
    password: "{{.DBCh999OaNew.Pwd}}"
    port: 1433
    username: {{.DBCh999OaNew.Username}}
  office:
    dbname: {{.DBOffice.Name}}
    host: {{.DBOffice.Host}}
    password: "{{.DBOffice.Pwd}}"
    port: 1433
    username: {{.DBOffice.Username}}
  officeWrite:
    dbname: {{.DBOffice.Name}}
    host: {{.DBOffice.Host}}
    password: "{{.DBOffice.Pwd}}"
    port: 1433
    username: {{.DBOffice.Username}}
  smallpro_write:
    dbname: {{.DBCh999OaNew.Name}}
    host: {{.DBCh999OaNew.Host}}
    password: "{{.DBCh999OaNew.Pwd}}"
    port: 1433
    username: {{.DBCh999OaNew.Username}}
  web999:
    dbname: {{.DBWeb999.Name}}
    host: {{.DBWeb999.Host}}
    password: {{.DBWeb999.Pwd}}
    port: 1433
    username: {{.DBWeb999.Username}}
  web999_other:
    dbname: {{.DBWeb999Other.Name}}
    host: {{.DBWeb999Other.Host}}
    password: {{.DBWeb999Other.Pwd}}
    port: 1433
    username: {{.DBWeb999Other.Username}}
  ershou:
    dbname: {{.DBErShou.Name}}
    host: {{.DBErShou.Host}}
    password: "{{.DBErShou.Pwd}}"
    port: 1433
    username: {{.DBErShou.Username}}

url:
  delImgUrl: http://data3:5083
  oa-push-info: http://{{.DomainInWcf.Domain}}:2988/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://{{.DomainImg.Domain}}/
  source:
    path: i18n/url
  uploadImgUrl: http://data3:9333

mqtt:
  host: tcp://iot.9xun.com:1883
  topic: oa2/nc-segments
  clientinid: nc-segments-${random.value}
  qoslevel: 1
  username: client
  password: ch999
  timeout: 10000
  keepalive: 20
  adminUrl: http://************:8081/api/v4
  adminUser: admin
  adminPassword: public
  logistics:
    host: tcp://iot.9xun.com:1883
    topic: oa2/nc-segments
    clientinid: nc-segments-${random.value}
    qoslevel: 1
    username: client
    password: ch999
    timeout: 10000
    keepalive: 20
    adminUrl: http://************:8081/api/v4
    adminUser: admin
    adminPassword: public

chw:
  tenant: https://manager.saas.ch999.cn/saasManager/api/thirdParty/getAllDomains/v1
  warehousing: https://chw.9xun.com/cloudapi_nc/pick/api/order/oa/warehousing/v1?xservicename=pick-web
  cancelCaigou: https://chw.9xun.com/cloudapi_nc/pick/api/order/oa/cancel/v1?xservicename=pick-web
  platformUrl: https://chw.9xun.com/cloudapi_nc/pick/api/user/partner/skip/v1?xservicename=pick-web&token=%s&xtenant=%d
  messageSend: https://moa.9ji.com/cloudapi_nc/logistics/api/ChwNoticeController/sendMessage/v1
  messageChannelSend: https://moa.9ji.com/cloudapi_nc/logistics/api/ChwNoticeController/channelErrorMonitor/v1
  purchaseRemark: '%s:<a href=https://chw.9xun.com/mall/admin/trads/order-detail/%s>%s</a>'
#盘点重构excl 导入文件模板下载

inventory:
  downloadExcl: https://img.9xun.com/newstatic/2370/03805dac15b8cd57.xls
purchase:
  downloadExcl: https://img.9xun.com/newstatic/2373/032742dc68215552.xls

#渠道接口调用
channel.approve: /kcApi/SystemGenerateApplySupplierShortlisted

web:
  synchronousBarcodeUrl: https://www.9ji.com/web/api/products/updateBarCode

subOutIn:
  url: ''
calcEmployeePirce:
  url: ''

#请求宝尊接口 url
baozun.url: 'https://api-oms2wms.baozun.com/order'

apollo:
  url: {{.ApolloConfig.Meta}}
  file: application-stock.yml

#太力
tlmall:
  url: https://www.tlmall.com/page/ka/kaPrimary.html
  saasCode: jiuxunyun
  custEncType: AES
  custEncKey: xBmyu0A5kgezTju7
  signType: RSA
  custSignPubkey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQChQ1Nva1rmkA/SoxomqzXUk4s2vxzjKOjEGgaKqP8c1B2ux/hgVVFkN/xlUQpKkDNGKbtbOIUMI/aKjlspCVbkS7rZSm7r27xczm/dVQ0e9c648FEE9P11Jy1r4Kd0D4Pjq/JfBy6LoHe4JpQVg83YH87Mme2p/E8MY6fnhQKRLQIDAQAB
  custSignPrikey: MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAKFDU29rWuaQD9KjGiarNdSTiza/HOMo6MQaBoqo/xzUHa7H+GBVUWQ3/GVRCkqQM0Ypu1s4hQwj9oqOWykJVuRLutlKbuvbvFzOb91VDR71zrjwUQT0/XUnLWvgp3QPg+Or8l8HLougd7gmlBWDzdgfzsyZ7an8Twxjp+eFApEtAgMBAAECgYAD3t7QzM3YvNp0Xs/Q38kh+gycWsfxt9imZE2F5HqDEaBIwqn2ffW/JwzazbAmjAF/DJ9fmCKxYOeY+cO8X2oDdQDgj7wtxDTLCysdXFqe3J2bCUN473KZaVEaQMwNX4G+g69qviv37yviJbqh2tbJK517bMdY6JNo67LicIVl7QJBAMnBvMFCuYlgStnq5CNlO7OKeepaY21YdapfKlQTno7bJk9jdIBQZrqjhiBDBuxECX0Gdpss8cJOE5J8Kd+5uIsCQQDMnohWRnrYC8unrWk/+wQYXV2kEfCjOXITGYPNoFZ4LV/gEYgh2WhAgh6O5Yykx3yL5FJfTGgq+rTvJdwDYNwnAkEAuqtB1R3DRFOPbahihE05u5g3zJjsvVLHK2b5ZujwHwSsoW9HbyD0q2J4yoi5cwhQLxk3y8L9u+U5PqMaqyDOmQJBAKjMX0xM+CoiEO9SbvEI8mfnHcirxAfi6+g1tDV9f9fEFsORsuu5nfcZYHwhgdStfGErCYj0Tzqld32Rjd57mSECQQC4eVOkmUNgui4mflQNmI8gLnHo8j5c9/lnnxWD1CJ+krW570dm9VLJIa0lzC+x0IjpVAqaWfVIMua1jvJwV83h
  ipList: *************

  #中邮
ptac:
  tokenurl: https://open.ptac.cn/oauth2/oauth/token
  apiurl: https://open.ptac.cn/api/aifgw/http