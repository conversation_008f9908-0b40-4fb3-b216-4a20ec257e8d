consul:
  host: 127.0.0.1
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://demo.img.9xun.com/
  upload:
    url: http://**************:9333
instance-zone: 10067
jiuji:
  sys:
    moa: https://demo.moa.9xun.com
    pc: https://demo.oa.9xun.com
    m: https://demo.m.9xun.com
    xtenant: 10067
  xtenant: 67000
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10067:6OsHHLnb@**************:27017,**************:27017,**************:27017/ch999oa__10067
  url1: ***************************************************************************************************************
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname: 
    password: 
    url: :3306
    username: 
  oa_core:
    dbname: oa_core__10067
    password: oa_corecIF
    url: mysql.serv.xn.saas.ch999.cn:3306
    username: oa_core__10067
  oa_nc:
    dbname: oa_nc__10067
    password: 'oa_ncukq'
    url: mysql.serv.xn.saas.ch999.cn:3306
    username: oa_nc__10067
  train:
    dbname: train__10067
    password: "trainIcX"
    url: mysql.serv.xn.saas.ch999.cn:3306
    username: train__10067
  oa_log:
    dbname: oa_log__10067
    password: "9uppJ355ffNc"
    url: main.tidb.ch999.cn:9383
    username: oa_log__10067
office:
  sys:
    xtenant: 10067
rabbitmq:
  master:
    password: qgaDt
    port: 5672
    url: rabbitmq.serv.xn.saas.ch999.cn
    username: oaAsync__10067
    vhost: oaAsync__10067
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: fJPSM
    port: 5672
    url: rabbitmq.serv.xn.saas.ch999.cn
    username: oa__10067
    vhost: oa__10067
  oaAsync:
    password: qgaDt
    port: 5672
    url: rabbitmq.serv.xn.saas.ch999.cn
    username: oaAsync__10067
    vhost: oaAsync__10067
  printer:
    password: LkKZG
    port: 5672
    url: rabbitmq.serv.xn.saas.ch999.cn
    username: printer__10067
    vhost: printer__10067
redis:
  oa:
    host: **************
    password: google99
    port: 6403
    url: google99@**************:6403
sms:
  send:
    email:
      url: http://demo.sms.9xun.com/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://demo.sms.9xun.com/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10067
sqlserver:
  after_write:
    dbname: ch999oanew__10067
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "ch999oanewccxvK"
    port: 1433
    username: ch999oanew__10067
  ch999oanew:
    dbname: ch999oanew__10067
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "ch999oanewccxvK"
    port: 1433
    username: ch999oanew__10067
  ch999oanewReport:
    dbname: ch999oanew__10067
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "ch999oanewccxvK"
    port: 1433
    username: ch999oanew__10067
  ch999oanewHis:
    dbname: ch999oanew__10067
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "ch999oanewccxvK"
    port: 1433
    username: ch999oanew__10067
  ch999oahis:
    dbname: ch999oanew__10067
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "ch999oanewccxvK"
    port: 1433
    username: ch999oanew__10067
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10067
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "officeajMAy"
    port: 1433
    username: office__10067
  oanewWrite:
    dbname: ch999oanew__10067
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "ch999oanewccxvK"
    port: 1433
    username: ch999oanew__10067
  office:
    dbname: office__10067
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "officeajMAy"
    port: 1433
    username: office__10067
  officeWrite:
    dbname: office__10067
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "officeajMAy"
    port: 1433
    username: office__10067
  smallpro_write:
    dbname: ch999oanew__10067
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "ch999oanewccxvK"
    port: 1433
    username: ch999oanew__10067
  web999:
    dbname: web999__10067
    host: sqlserver.serv.xn.saas.ch999.cn
    password: web999kvRHM
    port: 1433
    username: web999__10067
  web999_other:
    dbname: web999_other__10067
    host: sqlserver.serv.xn.saas.ch999.cn
    password: web999_otherTIHkl
    port: 1433
    username: web999_other__10067
  ershou:
    dbname: ershou__10067
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "ershougNXeE"
    port: 1433
    username: ershou__10067

url:
  delImgUrl: http://data3:5083
  oa-push-info: http://demo.inwcf.9xun.com:2988/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://demo.img.9xun.com/
  source:
    path: i18n/url
  uploadImgUrl: http://data3:9333

mqtt:
  host: tcp://iot.9xun.com:1883
  topic: oa2/nc-segments
  clientinid: nc-segments-${random.value}
  qoslevel: 1
  username: client
  password: ch999
  timeout: 10000
  keepalive: 20
  adminUrl: http://************:8081/api/v4
  adminUser: admin
  adminPassword: public
  logistics:
    host: tcp://iot.9xun.com:1883
    topic: oa2/nc-segments
    clientinid: nc-segments-${random.value}
    qoslevel: 1
    username: client
    password: ch999
    timeout: 10000
    keepalive: 20
    adminUrl: http://************:8081/api/v4
    adminUser: admin
    adminPassword: public

chw:
  tenant: https://manager.saas.ch999.cn/saasManager/api/thirdParty/getAllDomains/v1
  warehousing: https://chw.9xun.com/cloudapi_nc/pick/api/order/oa/warehousing/v1?xservicename=pick-web
  cancelCaigou: https://chw.9xun.com/cloudapi_nc/pick/api/order/oa/cancel/v1?xservicename=pick-web
  platformUrl: https://chw.9xun.com/cloudapi_nc/pick/api/user/partner/skip/v1?xservicename=pick-web&token=%s&xtenant=%d
  messageSend: https://moa.9ji.com/cloudapi_nc/logistics/api/ChwNoticeController/sendMessage/v1
  messageChannelSend: https://moa.9ji.com/cloudapi_nc/logistics/api/ChwNoticeController/channelErrorMonitor/v1
  purchaseRemark: '%s:<a href=https://chw.9xun.com/mall/admin/trads/order-detail/%s>%s</a>'
#盘点重构excl 导入文件模板下载

inventory:
  downloadExcl: https://img.9xun.com/newstatic/2370/03805dac15b8cd57.xls
purchase:
  downloadExcl: https://img.9xun.com/newstatic/2373/032742dc68215552.xls

#渠道接口调用
channel.approve: /kcApi/SystemGenerateApplySupplierShortlisted

web:
  synchronousBarcodeUrl: https://www.9ji.com/web/api/products/updateBarCode

subOutIn:
  url: ''
calcEmployeePirce:
  url: ''

#请求宝尊接口 url
baozun.url: 'https://dss-api-oms2wms-uat.baozun.com/order'

apollo:
  url: http://**************:8080
  file: application-stock.yml