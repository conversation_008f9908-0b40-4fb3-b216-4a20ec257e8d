data9=**************
data5=************
## \u6570\u636E\u5E93 sqlserver
sqlserver.data2.host=data2.ch999.cn
sqlserver.data2.port=1433
# data6 sqlserver
sqlserver.data6.host=data6.ch999.cn
sqlserver.data6.port=1433
# data7 sqlserver
sqlserver.data7.host=data7.ch999.cn
sqlserver.data7.port=1433
# oareadnodes db
sqlserver.oareadnodes.host=readnodes.oamssql.serv.ch999.cn
sqlserver.oareadnodes.port=1433
## sqlserver:ch999oahis
sqlserver.ch999oahis.host=his.sqlserver.ch999.cn
sqlserver.ch999oahis.port=${sqlserver.data2.port}
sqlserver.ch999oahis.dbname=ch999oahis
sqlserver.ch999oahis.username=ch999oahis
sqlserver.ch999oahis.password=VS35!Q7@CDGwFF91
## sqlserver:ch999oanew
sqlserver.ch999oanew.host=${sqlserver.oareadnodes.host}
sqlserver.ch999oanew.port=${sqlserver.oareadnodes.port}
sqlserver.ch999oanew.dbname=ch999oanew
sqlserver.ch999oanew.username=ch999oanewGreen
sqlserver.ch999oanew.password=SMns^!N@)!@y@#$dOpV3

## sqlserver:ch999oanew2
sqlserver.ch999oanew2.host=*************
sqlserver.ch999oanew2.port=1433
sqlserver.ch999oanew2.dbname=ch999oanew
sqlserver.ch999oanew2.username=oaUserChwM
sqlserver.ch999oanew2.password=o!@$^#!OnFkMgf

## sqlserver:office2
sqlserver.office2.host=*************
sqlserver.office2.port=1433
sqlserver.office2.dbname=office
sqlserver.office2.username=officeUserCg
sqlserver.office2.password=P$%*Jgd@#%!Bj$

## sqlserver:ch999oanewReport
sqlserver.ch999oanewReport.host=${sqlserver.oareadnodes.host}
sqlserver.ch999oanewReport.port=${sqlserver.oareadnodes.port}
sqlserver.ch999oanewReport.dbname=ch999oanew
sqlserver.ch999oanewReport.username=ch999oanewGreen
sqlserver.ch999oanewReport.password=SMns^!N@)!@y@#$dOpV3
## sqlserver:ch999oanewWrite
sqlserver.oanewWrite.host=${sqlserver.data6.host}
sqlserver.oanewWrite.port=${sqlserver.data6.port}
sqlserver.oanewWrite.dbname=ch999oanew
sqlserver.oanewWrite.username=ch999oanewGreen
sqlserver.oanewWrite.password=SMns^!N@)!@y@#$dOpV3
## sqlserver:oanewOfficeWrite
sqlserver.officeWrite.host=*************
sqlserver.officeWrite.port=${sqlserver.data2.port}
sqlserver.officeWrite.dbname=office
sqlserver.officeWrite.username=oaOffice
sqlserver.officeWrite.password=o#!@$DMas
## sqlserver:web99_other
sqlserver.web999_other.host=${sqlserver.data1.host}
sqlserver.web999_other.port=${sqlserver.data1.port}
sqlserver.web999_other.dbname=web999_other
sqlserver.web999_other.username=webOtherGreen
sqlserver.web999_other.password=K8x!4m@9j#L5z$2tB3^r
## sqlserver:ershou
sqlserver.ershou.host=${sqlserver.data1.host}
sqlserver.ershou.port=${sqlserver.data1.port}
sqlserver.ershou.dbname=ershou
sqlserver.ershou.username=ch999hsDataGreen
sqlserver.ershou.password=T5z#2u$8M^A9b%1nW4@j
# data1
sqlserver.data1.host=master.web.sqlserver.service.ch999.cn
sqlserver.data1.port=1433
## mysql
mysql.url=master.main.mysql8.service.ch999.cn:3306
## mysql:oa_nc
mysql.oa_nc.url=${mysql.url}
mysql.oa_nc.dbname=oa_nc
mysql.oa_nc.username=oa_nc
mysql.oa_nc.password=oa_nc#$%
## mysql:oa_log
mysql.oa_log.url=main.tidb.ch999.cn:9383
mysql.oa_log.dbname=oa_log
mysql.oa_log.username=oa_log
mysql.oa_log.password=ueSCNX3dIoM

## mysql:appleSn
mysql.appleSn.url=************:3306
mysql.appleSn.dbname=appleSn_9ji
mysql.appleSn.username=appleSn_9ji
mysql.appleSn.password=google00

## StarRocks
mysql.starrocks.url=dwcluster.ch999.cn:19030
mysql.starrocks.dbname=ods_jiuji
mysql.starrocks.username=dev_operation
mysql.starrocks.password=osPIRH2dT47

## midl
redis.oa.host=master.main.redis.service.ch999.cn
redis.oa.port=6379
redis.oa.password=JiujiOa2020
redis.oa.url=${redis.oa.password}@${redis.oa.host}:${redis.oa.port}
## image
image.upload.url=http://*************:9333
image.del.url=http://*************:5083
image.select.url=https://img2.ch999img.com/

## rabbitmq
rabbitmq.master.url=master.main.rabbitmq.service.ch999.cn
rabbitmq.oa.url=${rabbitmq.master.url}
rabbitmq.oa.port=5672
rabbitmq.oa.vhost=oa
rabbitmq.oa.username=oa
rabbitmq.oa.password=ch999

rabbitmq.oaAsync.url=${rabbitmq.master.url}
rabbitmq.oaAsync.port=5672
rabbitmq.oaAsync.vhost=oaAsync
rabbitmq.oaAsync.username=admin
rabbitmq.oaAsync.password=ch999

mongodb.url1=*************************************************************************
spring.messages.xtenant=0
consul.host=*************
consul.port=8500
spring.cloud.consul.discovery.instance-zone=9ji
instance-zone=9ji
sqlserver.datasource.max-pool-size=20
mysql.datasource.max-pool-size=20
jiuji.sys.m=
jiuji.sys.moa=https://moa.9ji.com
jiuji.sys.pc=https://oa.9ji.com
jiuji.sys.web=https://www.9ji.com
jiuji.sys.inWcf=http://inwcf.ch999.cn
jiuji.sys.oaWcf=http://oawcf2.ch999.cn
mqtt.host=tcp://iot.9xun.com:1883
mqtt.topic=jiuji/logistics
mqtt.username=admin
mqtt.password=ch999
mqtt.timeout=10000
mqtt.keepalive=20
mqtt.adminUrl=http://**************:8081/api/v4
mqtt.adminUser=admin
mqtt.adminPassword=public
pubkey=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCMfzP2eW8EDTffDZ8wHrG7eMvCpe4iTnLoEh2ofJBRGpY2eg6VS1Fzm0Gtk+QsHnhmG5WBcZuQH8q78VQUgQbcEDGhH/Dq+SkPwWRPyofZsEwB+1VZjfJMwm82Ml4D/HQEiVVho+GeigG55uelaZOyQSOeDZe8v2dWMu0qHKq/cwIDAQAB

# mqtt
mqtt.logistics.host=tcp://iot.9xun.com:1883
mqtt.logistics.topic=VoIP-gateway-server
mqtt.logistics.clientinid=oa-stock-call-${random.value}
mqtt.logistics.qoslevel=1
mqtt.logistics.username=admin
mqtt.logistics.password=ch999
mqtt.logistics.timeout=10000
mqtt.logistics.keepalive=20
mqtt.logistics.adminUrl=http://**************:8081/api/v4
mqtt.logistics.adminUser=admin
mqtt.logistics.adminPassword=public


#chw  \u8C03\u7528\u5730\u5740

chw.warehousing=https://chw.9xun.com/cloudapi_nc/pick/api/order/oa/warehousing/v1?xservicename=pick-web
chw.cancelCaigou=https://chw.9xun.com/cloudapi_nc/pick/api/order/oa/cancel/v1?xservicename=pick-web
chw.platformUrl=https://chw.9xun.com/cloudapi_nc/pick/api/user/partner/skip/v1?xservicename=pick-web&token=%s&xtenant=%d
chw.messageSend= https://moa.9ji.com/cloudapi_nc/logistics/api/ChwNoticeController/sendMessage/v1
chw.messageChannelSend= https://moa.9ji.com/cloudapi_nc/logistics/api/ChwNoticeController/channelErrorMonitor/v1
chw.purchaseRemark= %s:<a href=https://chw.9xun.com/mall/admin/trads/order-detail/%s>%s</a>
#\u76D8\u70B9\u91CD\u6784excl \u5BFC\u5165\u6587\u4EF6\u6A21\u677F\u4E0B\u8F7D
inventory.downloadExcl= https://img.9xun.com/newstatic/27122/06e7bfdaba9d0492.xls
purchase.downloadExcl= https://img.9xun.com/newstatic/2373/032742dc68215552.xls

#\u6E20\u9053\u63A5\u53E3\u8C03\u7528
channel.approve=/kcApi/SystemGenerateApplySupplierShortlisted

#\u91C7\u8D2D\u914D\u4EF6\u6279\u91CF\u4ED8\u6B3E\u6279\u7B7E\u5730\u5740
purchase.approve=/kcApi/SystemGenerateCommonApply
purchase.handleStock=http://inwcf.ch999.cn/oaApi.svc/rest/BatchProductKc
#ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½Ì¬ï¿½ï¿½ï¿½ï¿½Ñ¯
purchase.orderDynamicsUrl=http://inwcf.ch999.cn/oaapi.svc/rest/SubProductArrive

# TiDB
tidb.logdb.url=main.tidb.ch999.cn:9383
tidb.logdb.dbname=oa_log
tidb.logdb.username=oa_log
tidb.logdb.password=ueSCNX3dIoM

web.synchronousBarcodeUrl= https://www.9ji.com/web/api/products/updateBarCode

##Oa
subOutIn.url=http://oawcf2.ch999.cn/kcApi/wmsPickUpComplete
calcEmployeePirce.url=http://inwcf.ch999.cn/oaApi.svc/rest/CalcEmployeePirceForAccessory

apollo.url=http://192.168.254.8:8010,http://**************:8010,http://**************:8010
apollo.file=application-stock.yml
#å¤ªå
tlmall.url=https://www.tlmall.com/page/ka/kaPrimary.html
tlmall.saasCode=jiuxunyun
tlmall.custEncType=AES
tlmall.custEncKey=xBmyu0A5kgezTju7
tlmall.signType=RSA
tlmall.custSignPubkey=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQChQ1Nva1rmkA/SoxomqzXUk4s2vxzjKOjEGgaKqP8c1B2ux/hgVVFkN/xlUQpKkDNGKbtbOIUMI/aKjlspCVbkS7rZSm7r27xczm/dVQ0e9c648FEE9P11Jy1r4Kd0D4Pjq/JfBy6LoHe4JpQVg83YH87Mme2p/E8MY6fnhQKRLQIDAQAB
tlmall.custSignPrikey=MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAKFDU29rWuaQD9KjGiarNdSTiza/HOMo6MQaBoqo/xzUHa7H+GBVUWQ3/GVRCkqQM0Ypu1s4hQwj9oqOWykJVuRLutlKbuvbvFzOb91VDR71zrjwUQT0/XUnLWvgp3QPg+Or8l8HLougd7gmlBWDzdgfzsyZ7an8Twxjp+eFApEtAgMBAAECgYAD3t7QzM3YvNp0Xs/Q38kh+gycWsfxt9imZE2F5HqDEaBIwqn2ffW/JwzazbAmjAF/DJ9fmCKxYOeY+cO8X2oDdQDgj7wtxDTLCysdXFqe3J2bCUN473KZaVEaQMwNX4G+g69qviv37yviJbqh2tbJK517bMdY6JNo67LicIVl7QJBAMnBvMFCuYlgStnq5CNlO7OKeepaY21YdapfKlQTno7bJk9jdIBQZrqjhiBDBuxECX0Gdpss8cJOE5J8Kd+5uIsCQQDMnohWRnrYC8unrWk/+wQYXV2kEfCjOXITGYPNoFZ4LV/gEYgh2WhAgh6O5Yykx3yL5FJfTGgq+rTvJdwDYNwnAkEAuqtB1R3DRFOPbahihE05u5g3zJjsvVLHK2b5ZujwHwSsoW9HbyD0q2J4yoi5cwhQLxk3y8L9u+U5PqMaqyDOmQJBAKjMX0xM+CoiEO9SbvEI8mfnHcirxAfi6+g1tDV9f9fEFsORsuu5nfcZYHwhgdStfGErCYj0Tzqld32Rjd57mSECQQC4eVOkmUNgui4mflQNmI8gLnHo8j5c9/lnnxWD1CJ+krW570dm9VLJIa0lzC+x0IjpVAqaWfVIMua1jvJwV83h
tlmall.ipList=***************|***************|***************|***************|***************|***************|***************|***************|***************|***************|***************|***************|***************|***************|***************|***************
#ä¸­é®
ptac.tokenurl=https://open.ptac.cn/oauth2/oauth/token
ptac.apiurl=https://open.ptac.cn/api/aifgw/http