consul:
  host: 127.0.0.1
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://img.cnjinzhen.com/
  upload:
    url: http://**************:9333
instance-zone: 10100
jiuji:
  sys:
    moa: https://moa.cnjinzhen.com
    pc: https://oa.cnjinzhen.com
    m: https://m.cnjinzhen.com
    xtenant: 10100
  xtenant: 100000
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10100:0FbNYveB3ZR1@***********:27017,***********:27017,***********:27017/ch999oa__10100
  url1: **********************************************************************************************************
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname:
    password:
    url: :3306
    username:
  oa_core:
    dbname: oa_core__10100
    password: vGh9vlwIJxCB
    url: mysql.serv.hd.saas.ch999.cn:3306
    username: oa_core__10100
  oa_nc:
    dbname: oa_nc__10100
    password: 'A^F5wcbCoRNV'
    url: mysql.serv.hd.saas.ch999.cn:3306
    username: oa_nc__10100
  train:
    dbname: train__10100
    password: "8AFJwm^YfPWi"
    url: mysql.serv.hd.saas.ch999.cn:3306
    username: train__10100
  oa_log:
    dbname: oa_log__10100
    password: "oE6JQzfk^PKg"
    url: tidb.serv.hd.saas.ch999.cn:9383
    username: oa_log__10100
office:
  sys:
    xtenant: 10100
rabbitmq:
  master:
    password: KZZpz
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oaAsync__10100
    vhost: oaAsync__10100
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: aZBoP
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oa__10100
    vhost: oa__10100
  oaAsync:
    password: KZZpz
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oaAsync__10100
    vhost: oaAsync__10100
  printer:
    password: KxaPj
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: printer__10100
    vhost: printer__10100
redis:
  oa:
    host: ***********
    password: google99
    port: 6387
    url: google99@***********:6387
sms:
  send:
    email:
      url: http://sms.cnjinzhen.com/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://sms.cnjinzhen.com/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10100
sqlserver:
  after_write:
    dbname: ch999oanew__10100
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "1XcVcdTqgHEA"
    port: 1433
    username: ch999oanew__10100
  ch999oanew:
    dbname: ch999oanew__10100
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "1XcVcdTqgHEA"
    port: 1433
    username: ch999oanew__10100
  ch999oanewReport:
    dbname: ch999oanew__10100
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "1XcVcdTqgHEA"
    port: 1433
    username: ch999oanew__10100
  ch999oanewHis:
    dbname: ch999oanew__10100
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "1XcVcdTqgHEA"
    port: 1433
    username: ch999oanew__10100
  ch999oahis:
    dbname: ch999oanew__10100
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "1XcVcdTqgHEA"
    port: 1433
    username: ch999oanew__10100
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10100
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "XBXVIdHzTISe"
    port: 1433
    username: office__10100
  oanewWrite:
    dbname: ch999oanew__10100
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "1XcVcdTqgHEA"
    port: 1433
    username: ch999oanew__10100
  office:
    dbname: office__10100
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "XBXVIdHzTISe"
    port: 1433
    username: office__10100
  officeWrite:
    dbname: office__10100
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "XBXVIdHzTISe"
    port: 1433
    username: office__10100
  smallpro_write:
    dbname: ch999oanew__10100
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "1XcVcdTqgHEA"
    port: 1433
    username: ch999oanew__10100
  web999:
    dbname: web999__10100
    host: sqlserver.serv.hd.saas.ch999.cn
    password: JlKZq0iHdVzY
    port: 1433
    username: web999__10100
  web999_other:
    dbname: web999_other__10100
    host: sqlserver.serv.hd.saas.ch999.cn
    password: 83w0Zf0uvA2s
    port: 1433
    username: web999_other__10100
  ershou:
    dbname: ershou__10100
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "47V05etcVc^^"
    port: 1433
    username: ershou__10100

url:
  delImgUrl: http://data3:5083
  oa-push-info: http://inwcf.cnjinzhen.com:2988/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://img.cnjinzhen.com/
  source:
    path: i18n/url
  uploadImgUrl: http://data3:9333

mqtt:
  host: tcp://iot.9xun.com:1883
  topic: oa2/nc-segments
  clientinid: nc-segments-${random.value}
  qoslevel: 1
  username: client
  password: ch999
  timeout: 10000
  keepalive: 20
  adminUrl: http://************:8081/api/v4
  adminUser: admin
  adminPassword: public
  logistics:
    host: tcp://iot.9xun.com:1883
    topic: oa2/nc-segments
    clientinid: nc-segments-${random.value}
    qoslevel: 1
    username: client
    password: ch999
    timeout: 10000
    keepalive: 20
    adminUrl: http://************:8081/api/v4
    adminUser: admin
    adminPassword: public

chw:
  tenant: https://manager.saas.ch999.cn/saasManager/api/thirdParty/getAllDomains/v1
  warehousing: https://chw.9xun.com/cloudapi_nc/pick/api/order/oa/warehousing/v1?xservicename=pick-web
  cancelCaigou: https://chw.9xun.com/cloudapi_nc/pick/api/order/oa/cancel/v1?xservicename=pick-web
  platformUrl: https://chw.9xun.com/cloudapi_nc/pick/api/user/partner/skip/v1?xservicename=pick-web&token=%s&xtenant=%d
  messageSend: https://moa.9ji.com/cloudapi_nc/logistics/api/ChwNoticeController/sendMessage/v1
  messageChannelSend: https://moa.9ji.com/cloudapi_nc/logistics/api/ChwNoticeController/channelErrorMonitor/v1
  purchaseRemark: '%s:<a href=https://chw.9xun.com/mall/admin/trads/order-detail/%s>%s</a>'
#盘点重构excl 导入文件模板下载

inventory:
  downloadExcl: https://img.9xun.com/newstatic/2370/03805dac15b8cd57.xls
purchase:
  downloadExcl: https://img.9xun.com/newstatic/2373/032742dc68215552.xls

#渠道接口调用
channel.approve: /kcApi/SystemGenerateApplySupplierShortlisted

web:
  synchronousBarcodeUrl: https://www.9ji.com/web/api/products/updateBarCode

subOutIn:
  url: ''
calcEmployeePirce:
  url: ''

#请求宝尊接口 url
baozun.url: 'https://api-oms2wms.baozun.com/order'