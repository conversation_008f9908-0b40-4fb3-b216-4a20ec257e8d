consul:
  host: 127.0.0.1
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://img.hxbxtx.com/
  upload:
    url: http://**************:9333
instance-zone: 10112
jiuji:
  sys:
    moa: https://moa.hxbxtx.com
    pc: https://oa.hxbxtx.com
    m: https://m.hxbxtx.com
    xtenant: 10112
  xtenant: 112000
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10112:va3dI3PFK5uS@***********:27017,***********:27017,***********:27017/ch999oa__10112
  url1: **********************************************************************************************************
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname: 
    password: 
    url: :3306
    username: 
  oa_core:
    dbname: oa_core__10112
    password: k87qD03^^qWA
    url: mysql.serv.hd.saas.ch999.cn:3306
    username: oa_core__10112
  oa_nc:
    dbname: oa_nc__10112
    password: 'EkV#jOYuFqGY'
    url: mysql.serv.hd.saas.ch999.cn:3306
    username: oa_nc__10112
  train:
    dbname: train__10112
    password: "H9ec6SexYyHO"
    url: mysql.serv.hd.saas.ch999.cn:3306
    username: train__10112
  oa_log:
    dbname: 