consul:
  host: 127.0.0.1
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://img.1teng.com.cn/
  upload:
    url: http://**************:9333
instance-zone: 10002
jiuji:
  sys:
    moa: https://moa.1teng.com.cn
    pc: https://oa.1teng.com.cn
    m: https://m.1teng.com.cn
    xtenant: 10002
  xtenant: 2000
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10002:diDHhZnPteGwMkMt@************:27017,************:27017,************:27017/ch999oa__10002
  url1: *****************************************************************************************************************
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname:
    password:
    url: :3306
    username:
  oa_core:
    dbname: oa_core__10002
    password: 84YmPJZMdVDrvTnv
    url: master.mysql.serv.iteng.com:3306
    username: oa_core__10002
  oa_nc:
    dbname: oa_nc__10002
    password: 's0iB30eyBMxqt4pW'
    url: master.mysql.serv.iteng.com:3306
    username: oa_nc__10002
  train:
    dbname: train__10002
    password: "kpmb5iRgzTKflQ3q"
    url: master.mysql.serv.iteng.com:3306
    username: train__10002
  oa_log:
    dbname: oa_log__10002
    password: "kxbc$jPoSN"
    url: main.tidb.serv.iteng.com:8381
    username: oa_log__10002
office:
  sys:
    xtenant: 10002
rabbitmq:
  master:
    password: LUIVM
    port: 5672
    url: master.rabbitmq.serv.iteng.com
    username: oaAsync__10002
    vhost: oaAsync__10002
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: INhVR
    port: 5672
    url: master.rabbitmq.serv.iteng.com
    username: oa__10002
    vhost: oa__10002
  oaAsync:
    password: LUIVM
    port: 5672
    url: master.rabbitmq.serv.iteng.com
    username: oaAsync__10002
    vhost: oaAsync__10002
  printer:
    password: AtsqX
    port: 5672
    url: master.rabbitmq.serv.iteng.com
    username: printer__10002
    vhost: printer__10002
redis:
  oa:
    host: ************
    password: google99
    port: 6389
    url: google99@************:6389
sms:
  send:
    email:
      url: http://sms.1teng.com.cn/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://sms.1teng.com.cn/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10002
sqlserver:
  after_write:
    dbname: ch999oanew__10002
    host: oa.sqlserver.serv.iteng.com
    password: "Z*T4ZtxbL0SF"
    port: 1433
    username: ch999oanew__10002
  ch999oanew:
    dbname: ch999oanew__10002
    host: oa.sqlserver.serv.iteng.com
    password: "Z*T4ZtxbL0SF"
    port: 1433
    username: ch999oanew__10002
  ch999oanewReport:
    dbname: ch999oanew__10002
    host: oa.sqlserver.serv.iteng.com
    password: "Z*T4ZtxbL0SF"
    port: 1433
    username: ch999oanew__10002
  ch999oanewHis:
    dbname: ch999oanew__10002
    host: oa.sqlserver.serv.iteng.com
    password: "Z*T4ZtxbL0SF"
    port: 1433
    username: ch999oanew__10002
  ch999oahis:
    dbname: ch999oanew__10002
    host: oa.sqlserver.serv.iteng.com
    password: "Z*T4ZtxbL0SF"
    port: 1433
    username: ch999oanew__10002
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10002
    host: oa.sqlserver.serv.iteng.com
    password: "aPhHDXLCV*ZB"
    port: 1433
    username: office__10002
  oanewWrite:
    dbname: ch999oanew__10002
    host: oa.sqlserver.serv.iteng.com
    password: "Z*T4ZtxbL0SF"
    port: 1433
    username: ch999oanew__10002
  office:
    dbname: office__10002
    host: oa.sqlserver.serv.iteng.com
    password: "aPhHDXLCV*ZB"
    port: 1433
    username: office__10002
  officeWrite:
    dbname: office__10002
    host: oa.sqlserver.serv.iteng.com
    password: "aPhHDXLCV*ZB"
    port: 1433
    username: office__10002
  smallpro_write:
    dbname: ch999oanew__10002
    host: oa.sqlserver.serv.iteng.com
    password: "Z*T4ZtxbL0SF"
    port: 1433
    username: ch999oanew__10002
  web999:
    dbname: web999__10002
    host: oa.sqlserver.serv.iteng.com
    password: sCf_CPlMCKpJ
    port: 1433
    username: web999__10002
  web999_other:
    dbname: web999_other__10002
    host: oa.sqlserver.serv.iteng.com
    password: sxwnb!7WO_1Q
    port: 1433
    username: web999_other__10002
  ershou:
    dbname: ershou__10002
    host: oa.sqlserver.serv.iteng.com
    password: wHGw19yypDug
    port: 1433
    username: ershou__10002

url:
  delImgUrl: http://data3:5083
  oa-push-info: http://inwcf.1teng.com.cn:2988/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://img.1teng.com.cn/
  source:
    path: i18n/url
  uploadImgUrl: http://data3:9333

mqtt:
  host: tcp://iot.9xun.com:1883
  topic: oa2/nc-segments
  clientinid: nc-segments-${random.value}
  qoslevel: 1
  username: client
  password: ch999
  timeout: 10000
  keepalive: 20
  adminUrl: http://************:8081/api/v4
  adminUser: admin
  adminPassword: public

chw:
  tenant: https://manager.saas.ch999.cn/saasManager/api/thirdParty/getAllDomains/v1
  warehousing: https://chw.9xun.com/cloudapi_nc/pick/api/order/oa/warehousing/v1?xservicename=pick-web
  cancelCaigou: https://chw.9xun.com/cloudapi_nc/pick/api/order/oa/cancel/v1?xservicename=pick-web
  platformUrl: https://chw.9xun.com/cloudapi_nc/pick/api/user/partner/skip/v1?xservicename=pick-web&token=%s&xtenant=%d
  messageSend: https://moa.9ji.com/cloudapi_nc/logistics/api/ChwNoticeController/sendMessage/v1
  messageChannelSend: https://moa.9ji.com/cloudapi_nc/logistics/api/ChwNoticeController/channelErrorMonitor/v1
  purchaseRemark: '%s:<a href=https://chw.9xun.com/mall/admin/trads/order-detail/%s>%s</a>'
#盘点重构excl 导入文件模板下载

inventory:
  downloadExcl: https://img.9xun.com/newstatic/27122/06e7bfdaba9d0492.xls
purchase:
  downloadExcl: https://img.9xun.com/newstatic/2373/032742dc68215552.xls

#渠道接口调用
channel.approve: /kcApi/SystemGenerateApplySupplierShortlisted

web:
  synchronousBarcodeUrl: https://www.9ji.com/web/api/products/updateBarCode


#请求宝尊接口 url
baozun.url: 'https://api-oms2wms.baozun.com/order'

subOutIn:
  url: ''
calcEmployeePirce:
  url: ''