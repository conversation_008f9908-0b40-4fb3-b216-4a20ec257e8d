consul:
  host: 127.0.0.1
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://img.xinzhonglian.cn/
  upload:
    url: http://**************:9333
instance-zone: 10098
jiuji:
  sys:
    moa: https://moa.xinzhonglian.cn
    pc: https://oa.xinzhonglian.cn
    m: https://m.xinzhonglian.cn
    inWcf: http://inwcf.xinzhonglian.cn
    oaWcf: http://inwcf2.xinzhonglian.cn
    xtenant: 10098
  xtenant: 98000
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10098:6Y9PR7Sctiqh@***********:27017,***********:27017,***********:27017/ch999oa__10098
  url1: **********************************************************************************************************
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname: 
    password: 
    url: :3306
    username: 
  oa_core:
    dbname: oa_core__10098
    password: FwcbJitLf^jD
    url: mysql.serv.hd.saas.ch999.cn:3306
    username: oa_core__10098
  oa_nc:
    dbname: oa_nc__10098
    password: 'u#47w8oJfvBY'
    url: mysql.serv.hd.saas.ch999.cn:3306
    username: oa_nc__10098
  train:
    dbname: train__10098
    password: "uAra1MQmTrEp"
    url: mysql.serv.hd.saas.ch999.cn:3306
    username: train__10098
  oa_log:
    dbname: oa_log__10098
    password: "NJ1oZl4AwslY"
    url: tidb.serv.hd.saas.ch999.cn:9383
    username: oa_log__10098
  appleSn:
    dbname: appleSn_9ji
    password: google00
    url: ************:3306
    username: appleSn_9ji
office:
  sys:
    xtenant: 10098
rabbitmq:
  master:
    password: WicCJ
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oaAsync__10098
    vhost: oaAsync__10098
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: hjPDO
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oa__10098
    vhost: oa__10098
  oaAsync:
    password: WicCJ
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oaAsync__10098
    vhost: oaAsync__10098
  printer:
    password: TgSwm
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: printer__10098
    vhost: printer__10098
redis:
  oa:
    host: ***********
    password: google99
    port: 6385
    url: google99@***********:6385
sms:
  send:
    email:
      url: http://sms.xinzhonglian.cn/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://sms.xinzhonglian.cn/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10098
sqlserver:
  after_write:
    dbname: ch999oanew__10098
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "68ZKCHzYqb6F"
    port: 1433
    username: ch999oanew__10098
  ch999oanew:
    dbname: ch999oanew__10098
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "68ZKCHzYqb6F"
    port: 1433
    username: ch999oanew__10098
  ch999oanewReport:
    dbname: ch999oanew__10098
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "68ZKCHzYqb6F"
    port: 1433
    username: ch999oanew__10098
  ch999oanewHis:
    dbname: ch999oanew__10098
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "68ZKCHzYqb6F"
    port: 1433
    username: ch999oanew__10098
  ch999oahis:
    dbname: ch999oanew__10098
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "68ZKCHzYqb6F"
    port: 1433
    username: ch999oanew__10098
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10098
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "00cfgHBcF0pP"
    port: 1433
    username: office__10098
  oanewWrite:
    dbname: ch999oanew__10098
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "68ZKCHzYqb6F"
    port: 1433
    username: ch999oanew__10098
  office:
    dbname: office__10098
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "00cfgHBcF0pP"
    port: 1433
    username: office__10098
  officeWrite:
    dbname: office__10098
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "00cfgHBcF0pP"
    port: 1433
    username: office__10098
  smallpro_write:
    dbname: ch999oanew__10098
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "68ZKCHzYqb6F"
    port: 1433
    username: ch999oanew__10098
  web999:
    dbname: web999__10098
    host: sqlserver.serv.hd.saas.ch999.cn
    password: sv3SFZ4g8Nfj
    port: 1433
    username: web999__10098
  web999_other:
    dbname: web999_other__10098
    host: sqlserver.serv.hd.saas.ch999.cn
    password: geWlvkUMF1NY
    port: 1433
    username: web999_other__10098
  ershou:
    dbname: ershou__10098
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "12wBi6l4Rruc"
    port: 1433
    username: ershou__10098

url:
  delImgUrl: http://data3:5083
  oa-push-info: http://inwcf.xinzhonglian.cn:2988/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://img.xinzhonglian.cn/
  source:
    path: i18n/url
  uploadImgUrl: http://data3:9333

mqtt:
  host: tcp://iot.9xun.com:1883
  topic: oa2/nc-segments
  clientinid: nc-segments-${random.value}
  qoslevel: 1
  username: client
  password: ch999
  timeout: 10000
  keepalive: 20
  adminUrl: http://************:8081/api/v4
  adminUser: admin
  adminPassword: public
  logistics:
    host: tcp://iot.9xun.com:1883
    topic: oa2/nc-segments
    clientinid: nc-segments-${random.value}
    qoslevel: 1
    username: client
    password: ch999
    timeout: 10000
    keepalive: 20
    adminUrl: http://************:8081/api/v4
    adminUser: admin
    adminPassword: public

chw:
  tenant: https://manager.saas.ch999.cn/saasManager/api/thirdParty/getAllDomains/v1
  warehousing: https://chw.9xun.com/cloudapi_nc/pick/api/order/oa/warehousing/v1?xservicename=pick-web
  cancelCaigou: https://chw.9xun.com/cloudapi_nc/pick/api/order/oa/cancel/v1?xservicename=pick-web
  platformUrl: https://chw.9xun.com/cloudapi_nc/pick/api/user/partner/skip/v1?xservicename=pick-web&token=%s&xtenant=%d
  messageSend: https://moa.9ji.com/cloudapi_nc/logistics/api/ChwNoticeController/sendMessage/v1
  messageChannelSend: https://moa.9ji.com/cloudapi_nc/logistics/api/ChwNoticeController/channelErrorMonitor/v1
  purchaseRemark: '%s:<a href=https://chw.9xun.com/mall/admin/trads/order-detail/%s>%s</a>'
#盘点重构excl 导入文件模板下载

inventory:
  downloadExcl: https://img.9xun.com/newstatic/2370/03805dac15b8cd57.xls
purchase:
  downloadExcl: https://img.9xun.com/newstatic/2373/032742dc68215552.xls

#渠道接口调用
channel.approve: /kcApi/SystemGenerateApplySupplierShortlisted

web:
  synchronousBarcodeUrl: https://www.9ji.com/web/api/products/updateBarCode

subOutIn:
  url: ''
calcEmployeePirce:
  url: ''

#请求宝尊接口 url
baozun.url: 'https://api-oms2wms.baozun.com/order'


tlmall:
  url: https://www.tlmall.com/page/ka/kaPrimary.html
  saasCode: jiuxunyun
  custEncType: AES
  custEncKey: xBmyu0A5kgezTju7
  signType: RSA
  custSignPubkey: |
    MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQChQ1Nva1rmkA/SoxomqzXUk4s2vxzjKOjEGgaKqP8c1B2ux/hgVVFkN/xlUQpKkDNGKbtbOIUMI/aKjlspCVbkS7rZSm7r27xczm/dVQ0e9c648FEE9P11Jy1r4Kd0D4Pjq/JfBy6LoHe4JpQVg83YH87Mme2p/E8MY6fnhQKRLQIDAQAB
  custSignPrikey: |
    MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAKFDU29rWuaQD9KjGiarNdSTiza/HOMo6MQaBoqo/xzUHa7H+GBVUWQ3/GVRCkqQM0Ypu1s4hQwj9oqOWykJVuRLutlKbuvbvFzOb91VDR71zrjwUQT0/XUnLWvgp3QPg+Or8l8HLougd7gmlBWDzdgfzsyZ7an8Twxjp+eFApEtAgMBAAECgYAD3t7QzM3YvNp0Xs/Q38kh+gycWsfxt9imZE2F5HqDEaBIwqn2ffW/JwzazbAmjAF/DJ9fmCKxYOeY+cO8X2oDdQDgj7wtxDTLCysdXFqe3J2bCUN473KZaVEaQMwNX4G+g69qviv37yviJbqh2tbJK517bMdY6JNo67LicIVl7QJBAMnBvMFCuYlgStnq5CNlO7OKeepaY21YdapfKlQTno7bJk9jdIBQZrqjhiBDBuxECX0Gdpss8cJOE5J8Kd+5uIsCQQDMnohWRnrYC8unrWk/+wQYXV2kEfCjOXITGYPNoFZ4LV/gEYgh2WhAgh6O5Yykx3yL5FJfTGgq+rTvJdwDYNwnAkEAuqtB1R3DRFOPbahihE05u5g3zJjsvVLHK2b5ZujwHwSsoW9HbyD0q2J4yoi5cwhQLxk3y8L9u+U5PqMaqyDOmQJBAKjMX0xM+CoiEO9SbvEI8mfnHcirxAfi6+g1tDV9f9fEFsORsuu5nfcZYHwhgdStfGErCYj0Tzqld32Rjd57mSECQQC4eVOkmUNgui4mflQNmI8gLnHo8j5c9/lnnxWD1CJ+krW570dm9VLJIa0lzC+x0IjpVAqaWfVIMua1jvJwV83h
  ipList: >-
    ***************|***************|**************|***************|***************|***************|***************|***************|***************|***************|***************|***************|***************|***************|***************|***************

ptac:
  tokenurl: https://open.ptac.cn/oauth2/oauth/token
  apiurl: https://open.ptac.cn/api/aifgw/http

apollo:
  url: http://************:6000
  file: application-stock.yml

redis2:
  cluster:
    nodes: **************:6002,**************:6002,**************:6002,**************:6003,**************:6003,**************:6003
    password: JiujiRedisCluster99
