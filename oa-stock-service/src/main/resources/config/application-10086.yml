consul:
  host: 127.0.0.1
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://img.gaoyuan.cool/
  upload:
    url: http://**************:9333
instance-zone: 10086
jiuji:
  sys:
    moa: https://moa.gaoyuan.cool
    pc: https://oa.gaoyuan.cool
    m: https://m.gaoyuan.cool
    xtenant: 10086
  xtenant: 86000
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10086:G0S8yEf8zDJy@***********:27017,***********:27017,***********:27017/ch999oa__10086
  url1: **********************************************************************************************************
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname:
    password:
    url: :3306
    username:
  oa_core:
    dbname: oa_core__10086
    password: fDRqBvFeU7a4
    url: mysql.serv.hb.saas.ch999.cn:3306
    username: oa_core__10086
  oa_nc:
    dbname: oa_nc__10086
    password: '8qNvQ4p7kbd9'
    url: mysql.serv.hb.saas.ch999.cn:3306
    username: oa_nc__10086
  train:
    dbname: train__10086
    password: "rcOfPqR1zRcS"
    url: mysql.serv.hb.saas.ch999.cn:3306
    username: train__10086
  oa_log:
    dbname: oa_log__10086
    password: "2C1MJpGYsTcD"
    url: tidb.serv.hb.saas.ch999.cn:9383
    username: oa_log__10086
office:
  sys:
    xtenant: 10086
rabbitmq:
  master:
    password: XIRAj
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: oaAsync__10086
    vhost: oaAsync__10086
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: raSeC
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: oa__10086
    vhost: oa__10086
  oaAsync:
    password: XIRAj
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: oaAsync__10086
    vhost: oaAsync__10086
  printer:
    password: CDibv
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: printer__10086
    vhost: printer__10086
redis:
  oa:
    host: ***********
    password: google99
    port: 6385
    url: google99@***********:6385
sms:
  send:
    email:
      url: http://sms.gaoyuan.cool/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://sms.gaoyuan.cool/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10086
sqlserver:
  after_write:
    dbname: ch999oanew__10086
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "5UL9b1r8Ykq0"
    port: 1433
    username: ch999oanew__10086
  ch999oanew:
    dbname: ch999oanew__10086
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "5UL9b1r8Ykq0"
    port: 1433
    username: ch999oanew__10086
  ch999oanewReport:
    dbname: ch999oanew__10086
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "5UL9b1r8Ykq0"
    port: 1433
    username: ch999oanew__10086
  ch999oanewHis:
    dbname: ch999oanew__10086
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "5UL9b1r8Ykq0"
    port: 1433
    username: ch999oanew__10086
  ch999oahis:
    dbname: ch999oanew__10086
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "5UL9b1r8Ykq0"
    port: 1433
    username: ch999oanew__10086
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10086
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "dpqrSLl1sDGz"
    port: 1433
    username: office__10086
  oanewWrite:
    dbname: ch999oanew__10086
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "5UL9b1r8Ykq0"
    port: 1433
    username: ch999oanew__10086
  office:
    dbname: office__10086
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "dpqrSLl1sDGz"
    port: 1433
    username: office__10086
  officeWrite:
    dbname: office__10086
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "dpqrSLl1sDGz"
    port: 1433
    username: office__10086
  smallpro_write:
    dbname: ch999oanew__10086
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "5UL9b1r8Ykq0"
    port: 1433
    username: ch999oanew__10086
  web999:
    dbname: web999__10086
    host: sqlserver.serv.hb.saas.ch999.cn
    password: 13NxSesbAST2
    port: 1433
    username: web999__10086
  web999_other:
    dbname: web999_other__10086
    host: sqlserver.serv.hb.saas.ch999.cn
    password: pBIpZlM6PYJN
    port: 1433
    username: web999_other__10086
  ershou:
    dbname: ershou__10086
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "TPrm0h5cgs6X"
    port: 1433
    username: ershou__10086

url:
  delImgUrl: http://data3:5083
  oa-push-info: http://inwcf.gaoyuan.cool:2988/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://img.gaoyuan.cool/
  source:
    path: i18n/url
  uploadImgUrl: http://data3:9333

mqtt:
  host: tcp://iot.9xun.com:1883
  topic: oa2/nc-segments
  clientinid: nc-segments-${random.value}
  qoslevel: 1
  username: client
  password: ch999
  timeout: 10000
  keepalive: 20
  adminUrl: http://************:8081/api/v4
  adminUser: admin
  adminPassword: public
  logistics:
    host: tcp://iot.9xun.com:1883
    topic: oa2/nc-segments
    clientinid: nc-segments-${random.value}
    qoslevel: 1
    username: client
    password: ch999
    timeout: 10000
    keepalive: 20
    adminUrl: http://************:8081/api/v4
    adminUser: admin
    adminPassword: public

chw:
  tenant: https://manager.saas.ch999.cn/saasManager/api/thirdParty/getAllDomains/v1
  warehousing: https://chw.9xun.com/cloudapi_nc/pick/api/order/oa/warehousing/v1?xservicename=pick-web
  cancelCaigou: https://chw.9xun.com/cloudapi_nc/pick/api/order/oa/cancel/v1?xservicename=pick-web
  platformUrl: https://chw.9xun.com/cloudapi_nc/pick/api/user/partner/skip/v1?xservicename=pick-web&token=%s&xtenant=%d
  messageSend: https://moa.9ji.com/cloudapi_nc/logistics/api/ChwNoticeController/sendMessage/v1
  messageChannelSend: https://moa.9ji.com/cloudapi_nc/logistics/api/ChwNoticeController/channelErrorMonitor/v1
  purchaseRemark: '%s:<a href=https://chw.9xun.com/mall/admin/trads/order-detail/%s>%s</a>'
#盘点重构excl 导入文件模板下载

inventory:
  downloadExcl: https://img.9xun.com/newstatic/2370/03805dac15b8cd57.xls
purchase:
  downloadExcl: https://img.9xun.com/newstatic/2373/032742dc68215552.xls

#渠道接口调用
channel.approve: /kcApi/SystemGenerateApplySupplierShortlisted

web:
  synchronousBarcodeUrl: https://www.9ji.com/web/api/products/updateBarCode

subOutIn:
  url: ''
calcEmployeePirce:
  url: ''

#请求宝尊接口 url
baozun.url: 'https://api-oms2wms.baozun.com/order'