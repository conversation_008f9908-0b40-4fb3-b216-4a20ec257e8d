consul:
  host: 127.0.0.1
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://img.hlto2o.com/
  upload:
    url: http://**************:9333
instance-zone: 10053
jiuji:
  sys:
    moa: https://moa.hlto2o.com
    pc: https://oa.hlto2o.com
    m: https://m.hlto2o.com
    inWcf: http://inwcf.hlto2o.com
    oaWcf: http://inwcf2.hlto2o.com
    xtenant: 10053
  xtenant: 53000
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10053:fU2NEeGL@**************:27017,**************:27017,**************:27017/ch999oa__10053
  url1: ***************************************************************************************************************
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname: 
    password: 
    url: :3306
    username: 
  oa_core:
    dbname: oa_core__10053
    password: oa_coreWYg
    url: mysql.serv.xn.saas.ch999.cn:3306
    username: oa_core__10053
  oa_nc:
    dbname: oa_nc__10053
    password: 'oa_ncVXr'
    url: mysql.serv.xn.saas.ch999.cn:3306
    username: oa_nc__10053
  train:
    dbname: train__10053
    password: "trainKMd"
    url: mysql.serv.xn.saas.ch999.cn:3306
    username: train__10053
  oa_log:
    dbname: oa_log__10053
    password: "s4JU6caxahrp"
    url: main.tidb.ch999.cn:9383
    username: oa_log__10053
  appleSn:
    dbname: appleSn_9ji
    password: google00
    url: ************:3306
    username: appleSn_9ji
office:
  sys:
    xtenant: 10053
rabbitmq:
  master:
    password: xQEhD
    port: 5672
    url: rabbitmq.serv.xn.saas.ch999.cn
    username: oaAsync__10053
    vhost: oaAsync__10053
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: KyIHu
    port: 5672
    url: rabbitmq.serv.xn.saas.ch999.cn
    username: oa__10053
    vhost: oa__10053
  oaAsync:
    password: xQEhD
    port: 5672
    url: rabbitmq.serv.xn.saas.ch999.cn
    username: oaAsync__10053
    vhost: oaAsync__10053
  printer:
    password: KilJv
    port: 5672
    url: rabbitmq.serv.xn.saas.ch999.cn
    username: printer__10053
    vhost: printer__10053
redis:
  oa:
    host: **************
    password: google99
    port: 6380
    url: google99@**************:6380
sms:
  send:
    email:
      url: http://sms.hlto2o.com/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://sms.hlto2o.com/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10053
sqlserver:
  after_write:
    dbname: ch999oanew__10053
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "ch999oanewtlwgE"
    port: 1433
    username: ch999oanew__10053
  ch999oanew:
    dbname: ch999oanew__10053
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "ch999oanewtlwgE"
    port: 1433
    username: ch999oanew__10053
  ch999oanewReport:
    dbname: ch999oanew__10053
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "ch999oanewtlwgE"
    port: 1433
    username: ch999oanew__10053
  ch999oanewHis:
    dbname: ch999oanew__10053
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "ch999oanewtlwgE"
    port: 1433
    username: ch999oanew__10053
  ch999oahis:
    dbname: ch999oanew__10053
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "ch999oanewtlwgE"
    port: 1433
    username: ch999oanew__10053
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10053
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "officeHTHmR"
    port: 1433
    username: office__10053
  oanewWrite:
    dbname: ch999oanew__10053
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "ch999oanewtlwgE"
    port: 1433
    username: ch999oanew__10053
  office:
    dbname: office__10053
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "officeHTHmR"
    port: 1433
    username: office__10053
  officeWrite:
    dbname: office__10053
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "officeHTHmR"
    port: 1433
    username: office__10053
  smallpro_write:
    dbname: ch999oanew__10053
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "ch999oanewtlwgE"
    port: 1433
    username: ch999oanew__10053
  web999:
    dbname: web999__10053
    host: sqlserver.serv.xn.saas.ch999.cn
    password: web999TspNC
    port: 1433
    username: web999__10053
  web999_other:
    dbname: web999_other__10053
    host: sqlserver.serv.xn.saas.ch999.cn
    password: web999_otherweukc
    port: 1433
    username: web999_other__10053
  ershou:
    dbname: ershou__10053
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "ershouoWCxx"
    port: 1433
    username: ershou__10053

url:
  delImgUrl: http://data3:5083
  oa-push-info: http://inwcf.hlto2o.com:2988/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://img.hlto2o.com/
  source:
    path: i18n/url
  uploadImgUrl: http://data3:9333

mqtt:
  host: tcp://iot.9xun.com:1883
  topic: oa2/nc-segments
  clientinid: nc-segments-${random.value}
  qoslevel: 1
  username: client
  password: ch999
  timeout: 10000
  keepalive: 20
  adminUrl: http://************:8081/api/v4
  adminUser: admin
  adminPassword: public
  logistics:
    host: tcp://iot.9xun.com:1883
    topic: oa2/nc-segments
    clientinid: nc-segments-${random.value}
    qoslevel: 1
    username: client
    password: ch999
    timeout: 10000
    keepalive: 20
    adminUrl: http://************:8081/api/v4
    adminUser: admin
    adminPassword: public

chw:
  tenant: https://manager.saas.ch999.cn/saasManager/api/thirdParty/getAllDomains/v1
  warehousing: https://chw.9xun.com/cloudapi_nc/pick/api/order/oa/warehousing/v1?xservicename=pick-web
  cancelCaigou: https://chw.9xun.com/cloudapi_nc/pick/api/order/oa/cancel/v1?xservicename=pick-web
  platformUrl: https://chw.9xun.com/cloudapi_nc/pick/api/user/partner/skip/v1?xservicename=pick-web&token=%s&xtenant=%d
  messageSend: https://moa.9ji.com/cloudapi_nc/logistics/api/ChwNoticeController/sendMessage/v1
  messageChannelSend: https://moa.9ji.com/cloudapi_nc/logistics/api/ChwNoticeController/channelErrorMonitor/v1
  purchaseRemark: '%s:<a href=https://chw.9xun.com/mall/admin/trads/order-detail/%s>%s</a>'
#盘点重构excl 导入文件模板下载

inventory:
  downloadExcl: https://img.9xun.com/newstatic/2370/03805dac15b8cd57.xls
purchase:
  downloadExcl: https://img.9xun.com/newstatic/2373/032742dc68215552.xls

#渠道接口调用
channel.approve: /kcApi/SystemGenerateApplySupplierShortlisted

web:
  synchronousBarcodeUrl: https://www.9ji.com/web/api/products/updateBarCode

subOutIn:
  url: ''
calcEmployeePirce:
  url: ''

#请求宝尊接口 url
baozun.url: 'https://api-oms2wms.baozun.com/order'


#太力
tlmall:
  url: https://www.tlmall.com/page/ka/kaPrimary.html
  saasCode: jiuxunyun
  custEncType: AES
  custEncKey: xBmyu0A5kgezTju7
  signType: RSA
  custSignPubkey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQChQ1Nva1rmkA/SoxomqzXUk4s2vxzjKOjEGgaKqP8c1B2ux/hgVVFkN/xlUQpKkDNGKbtbOIUMI/aKjlspCVbkS7rZSm7r27xczm/dVQ0e9c648FEE9P11Jy1r4Kd0D4Pjq/JfBy6LoHe4JpQVg83YH87Mme2p/E8MY6fnhQKRLQIDAQAB
  custSignPrikey: MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAKFDU29rWuaQD9KjGiarNdSTiza/HOMo6MQaBoqo/xzUHa7H+GBVUWQ3/GVRCkqQM0Ypu1s4hQwj9oqOWykJVuRLutlKbuvbvFzOb91VDR71zrjwUQT0/XUnLWvgp3QPg+Or8l8HLougd7gmlBWDzdgfzsyZ7an8Twxjp+eFApEtAgMBAAECgYAD3t7QzM3YvNp0Xs/Q38kh+gycWsfxt9imZE2F5HqDEaBIwqn2ffW/JwzazbAmjAF/DJ9fmCKxYOeY+cO8X2oDdQDgj7wtxDTLCysdXFqe3J2bCUN473KZaVEaQMwNX4G+g69qviv37yviJbqh2tbJK517bMdY6JNo67LicIVl7QJBAMnBvMFCuYlgStnq5CNlO7OKeepaY21YdapfKlQTno7bJk9jdIBQZrqjhiBDBuxECX0Gdpss8cJOE5J8Kd+5uIsCQQDMnohWRnrYC8unrWk/+wQYXV2kEfCjOXITGYPNoFZ4LV/gEYgh2WhAgh6O5Yykx3yL5FJfTGgq+rTvJdwDYNwnAkEAuqtB1R3DRFOPbahihE05u5g3zJjsvVLHK2b5ZujwHwSsoW9HbyD0q2J4yoi5cwhQLxk3y8L9u+U5PqMaqyDOmQJBAKjMX0xM+CoiEO9SbvEI8mfnHcirxAfi6+g1tDV9f9fEFsORsuu5nfcZYHwhgdStfGErCYj0Tzqld32Rjd57mSECQQC4eVOkmUNgui4mflQNmI8gLnHo8j5c9/lnnxWD1CJ+krW570dm9VLJIa0lzC+x0IjpVAqaWfVIMua1jvJwV83h
  ipList: *************

  #中邮
ptac:
  tokenurl: https://open.ptac.cn/oauth2/oauth/token
  apiurl: https://open.ptac.cn/api/aifgw/http

apollo:
  url: 