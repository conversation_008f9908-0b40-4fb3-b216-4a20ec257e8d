## host
data1=************ 
data2=************
data3=************ 
data4=************ 
data5=************
web1=************ 
web2=************
linux1=************ 
linux2=************
vm1=************ 
logserver=************ 
## \u6570\u636E\u5E93 sqlserver
sqlserver.data2.host=oa.sqlserver.serv.zlf.co
sqlserver.data2.port=1433
## sqlserver:ch999oahis(\u667A\u4E50\u65B9\u6CA1\u6709ch999oahis\u5E93  \u4E3A\u4E86\u8FD0\u884C\u6B63\u5E38\u914D\u7F6E\u6210ch999oanew\u7684\u8FDE\u63A5\u7684\u5E93)
sqlserver.ch999oahis.host=${sqlserver.data2.host}
sqlserver.ch999oahis.port=${sqlserver.data2.port}
sqlserver.ch999oahis.dbname=ch999oanew
sqlserver.ch999oahis.username=jiahai_ch999oanew
sqlserver.ch999oahis.password=ZYEny7J!QtU4DGjw
## sqlserver:ch999oanew
sqlserver.ch999oanew.host=${sqlserver.data2.host}
sqlserver.ch999oanew.port=${sqlserver.data2.port}
sqlserver.ch999oanew.dbname=ch999oanew
sqlserver.ch999oanew.username=jiahai_ch999oanew
sqlserver.ch999oanew.password=ZYEny7J!QtU4DGjw
## sqlserver:ch999oanewReport
sqlserver.ch999oanewReport.host=${sqlserver.data2.host}
sqlserver.ch999oanewReport.port=${sqlserver.data2.port}
sqlserver.ch999oanewReport.dbname=ch999oanew
sqlserver.ch999oanewReport.username=jiahai_ch999oanew
sqlserver.ch999oanewReport.password=ZYEny7J!QtU4DGjw


## sqlserver:Financial
sqlserver.Financial.host=${sqlserver.data2.host}
sqlserver.Financial.port=${sqlserver.data2.port}
sqlserver.Financial.dbname=Financial
sqlserver.Financial.username=jiahai_Financial
sqlserver.Financial.password=PR$COmlE#oEhpOtm
## sqlserver:office
sqlserver.office.host=${sqlserver.data2.host}
sqlserver.office.port=${sqlserver.data2.port}
sqlserver.office.dbname=office
sqlserver.office.username=jiahai_office
sqlserver.office.password=bwhG1BFc@u2#Yq2t
## sqlserver:oaOffice
sqlserver.oaOffice.host=${sqlserver.data2.host}
sqlserver.oaOffice.port=${sqlserver.data2.port}
sqlserver.oaOffice.dbname=ch999oanew
sqlserver.oaOffice.username=oaOffice
sqlserver.oaOffice.password=o#!@$DMas
## sqlserver:oanewWrite
sqlserver.officeWrite.host=${sqlserver.data2.host}
sqlserver.officeWrite.port=${sqlserver.data2.port}
sqlserver.officeWrite.dbname=office
sqlserver.officeWrite.username=jiahai_office
sqlserver.officeWrite.password=bwhG1BFc@u2#Yq2t
## sqlserver:ch999oanewWrite
sqlserver.oanewWrite.host=${sqlserver.data2.host}
sqlserver.oanewWrite.port=${sqlserver.data2.port}
sqlserver.oanewWrite.dbname=ch999oanew
sqlserver.oanewWrite.username=jiahai_ch999oanew
sqlserver.oanewWrite.password=ZYEny7J!QtU4DGjw
## sqlserver:WorkTasks
sqlserver.WorkTasks.host=${sqlserver.data2.host}
sqlserver.WorkTasks.port=${sqlserver.data2.port}
sqlserver.WorkTasks.dbname=WorkTasks
sqlserver.WorkTasks.username=jiahai_WorkTasks
sqlserver.WorkTasks.password=5J0GTWIcsKMdHCyF
# data1
sqlserver.data1.host=web.sqlserver.serv.zlf.co
sqlserver.data1.port=1433
## sqlserver:ershou
sqlserver.ershou.host=${sqlserver.data1.host}
sqlserver.ershou.port=${sqlserver.data1.port}
sqlserver.ershou.dbname=ershou
sqlserver.ershou.username=jiahai_ershou
sqlserver.ershou.password=4D1zvo%^Z0VChFM5
## sqlserver:ipaddress
sqlserver.ipaddress.host=${sqlserverr.data1.host}
sqlserver.ipaddress.port=${sqlserver.data1.port}
sqlserver.ipaddress.dbname=ipaddress
sqlserver.ipaddress.username=jiahai_ipaddress
sqlserver.ipaddress.password=7K$RaZpMqHdSIhcF
## sqlserver:job39
sqlserver.Job39.host=${sqlserverr.data1.host}
sqlserver.Job39.port=${sqlserver.data1.port}
sqlserver.Job39.dbname=Job39
sqlserver.Job39.username=jiahai_Job39
sqlserver.Job39.password=NyDyNa!1XAY1AaGf
## sqlserver:sms
sqlserver.sms.host=${sqlserverr.data1.host}
sqlserver.sms.port=${sqlserver.data1.port}
sqlserver.sms.dbname=sms
sqlserver.sms.username=jiahai_sms
sqlserver.sms.password=0trZ^VD7qKopXjlT
## sqlserver:web99
sqlserver.web999.host=${sqlserverr.data1.host}
sqlserver.web999.port=${sqlserver.data1.port}
sqlserver.web999.dbname=web999
sqlserver.web999.username=jiahai_web999
sqlserver.web999.password=FL2nBkultoNdkcWQ
## sqserver:web99
sqlserver.web999_other.host=${sqlserver.data1.host}
sqlserver.web999_other.port=${sqlserver.data1.port}
sqlserver.web999_other.dbname=web999_other
sqlserver.web999_other.username=jiahai_web999_other
sqlserver.web999_other.password=nNUK%CXNDBvL5^Ly
## mysql
mysql.url=master.mysql.serv.zlf.co:3306
## mysql:geoservice
mysql.geoservice.url=${mysql.url}
mysql.geoservice.dbname=geoservice
mysql.geoservice.username=geoservice
mysql.geoservice.password=geoservice%^&
## mysql:rent
mysql.rent.url=${mysql.url}
mysql.rent.dbname=rent
mysql.rent.username=rent
mysql.rent.password=rent$%^
## mysql:web_user
mysql.web_user.url=${mysql.url}
mysql.web_user.dbname=web_user
mysql.web_user.username=web_user
mysql.web_user.password=web_user$%$#
## mysql:micro_lesson
mysql.micro_lesson.url=${mysql.url}
mysql.micro_lesson.dbname=micro_lesson
mysql.micro_lesson.username=micro_lesson
mysql.micro_lesson.password=micro_lesson!@#
## mysql:train
mysql.train.url=${mysql.url}
mysql.train.dbname=train
mysql.train.username=train
mysql.train.password=train$%^
## mysql:web_privilege
mysql.web_privilege.url=${mysql.url}
mysql.web_privilege.dbname=web_privilege
mysql.web_privilege.username=web_privilege
mysql.web_privilege.password=W2e0b1P9rivileges$@*^
## mysql:coach
mysql.coach.url=${mysql.url}
mysql.coach.dbname=coach
mysql.coach.username=coach
mysql.coach.password=Coach2019*##$!@*
## mysql:ApolloConfigDB
mysql.ApolloConfigDB.url=${mysql.url}
mysql.ApolloConfigDB.dbname=ApolloConfigDB
mysql.ApolloConfigDB.username=ApolloConfigDB
mysql.ApolloConfigDB.password=ApolloConfigDB%^&
## mysql:ApolloPortalDB
mysql.ApolloPortalDB.url=${mysql.url}
mysql.ApolloPortalDB.dbname=ApolloPortalDB
mysql.ApolloPortalDB.username=ApolloPortalDB
mysql.ApolloPortalDB.password=ApolloPortalDB%^&
## mysql:xxl_job
mysql.xxl_job.url=${mysql.url}
mysql.xxl_job.dbname=xxl_job
mysql.xxl_job.username=xxl_job
mysql.xxl_job.password=xxl_job$%$#
## mysql:oa_nc
mysql.oa_nc.url=${mysql.url}
mysql.oa_nc.dbname=oa_nc
mysql.oa_nc.username=oa_nc
mysql.oa_nc.password=OaNc2019!@#
## mysql:oa_log
mysql.oa_log.url=main.tidb.serv.zlf.co:8381
mysql.oa_log.dbname=oa_log
mysql.oa_log.username=oa_log_zlf
mysql.oa_log.password=7xvtrMMruxfHQ4zU

mysql.starrocks.url=*************:19030
mysql.starrocks.dbname=ods
mysql.starrocks.username=dev_operation
mysql.starrocks.password=osPIRH2dT47

## midl
redis.oa.host=oa.redis.serv.zlf.co
redis.oa.port=6379
redis.oa.password=google99
redis.oa.url=${redis.oa.password}@${redis.oa.host}:${redis.oa.port}
## image
image.upload.url=http://master.weedfs.serv.zlf.co:9333
image.del.url=http://volume.weedfs.serv.zlf.co:5083
image.select.url=https://img.zlf.co/

## rabbitmq
rabbitmq.master.url=master.rabbitmq.serv.zlf.co
rabbitmq.oaAsync.url=${rabbitmq.master.url}
rabbitmq.oaAsync.port=5672
rabbitmq.oaAsync.vhost=oaAsync
rabbitmq.oaAsync.username=admin
rabbitmq.oaAsync.password=ch999

rabbitmq.printer.url=${rabbitmq.master.url}
rabbitmq.printer.port=5672
rabbitmq.printer.vhost=printer
rabbitmq.printer.username=admin
rabbitmq.printer.password=ch999

rabbitmq.oa.url=${rabbitmq.master.url}
rabbitmq.oa.port=5672
rabbitmq.oa.vhost=oa
rabbitmq.oa.username=oa
rabbitmq.oa.password=ch999

jiuji.sys.authorizeEnable=false
jiuji.sys.moa=https://moa.zlf.co
jiuji.sys.pc=https://oa.zlf.co
jiuji.sys.m=https://m.zlf.co
jiuji.sys.inWcf=http://inwcf.zlf.co
jiuji.sys.oaWcf=http://inwcf2.zlf.co

#mongodb.url1=mongodb://ch999oa:google@${data3}:27017/ch999oa
mongodb.url1=*****************************************************************************************
spring.messages.xtenant=1000
consul.host=************
consul.port=8500
spring.cloud.consul.discovery.instance-zone=zlf
instance-zone=zlf
sqlserver.datasource.max-pool-size=100
mysql.datasource.max-pool-size=100
mqtt.host=tcp://iot.9xun.com:1883
mqtt.topic=oa/stock
mqtt.clientinid=oa-stock-${random.value}
mqtt.qoslevel=1
mqtt.username=client
mqtt.password=ch999
mqtt.timeout=10000
mqtt.keepalive=20
mqtt.adminUrl=http://************:8081/api/v4
mqtt.adminUser=admin
mqtt.adminPassword=public

# mqtt
mqtt.logistics.host=tcp://iot.9xun.com:1883
mqtt.logistics.topic=VoIP-gateway-server
mqtt.logistics.clientinid=oa-stock-call-${random.value}
mqtt.logistics.qoslevel=1
mqtt.logistics.username=admin
mqtt.logistics.password=ch999
mqtt.logistics.timeout=10000
mqtt.logistics.keepalive=20
mqtt.logistics.adminUrl=http://**************:8081/api/v4
mqtt.logistics.adminUser=admin
mqtt.logistics.adminPassword=public

#chw  \u8C03\u7528\u5730\u5740

chw.warehousing=https://chw.9xun.com/cloudapi_nc/pick/api/order/oa/warehousing/v1?xservicename=pick-web
chw.cancelCaigou=https://chw.9xun.com/cloudapi_nc/pick/api/order/oa/cancel/v1?xservicename=pick-web
chw.platformUrl=https://chw.9xun.com/cloudapi_nc/pick/api/user/partner/skip/v1?xservicename=pick-web&token=%s&xtenant=%d
chw.messageSend= https://moa.9ji.com/cloudapi_nc/logistics/api/ChwNoticeController/sendMessage/v1
chw.messageChannelSend= https://moa.9ji.com/cloudapi_nc/logistics/api/ChwNoticeController/channelErrorMonitor/v1
chw.purchaseRemark= %s:<a href=https://chw.9xun.com/mall/admin/trads/order-detail/%s>%s</a>
#\u76D8\u70B9\u91CD\u6784excl \u5BFC\u5165\u6587\u4EF6\u6A21\u677F\u4E0B\u8F7D
inventory.downloadExcl= https://img.9xun.com/newstatic/27122/06e7bfdaba9d0492.xls
purchase.downloadExcl= https://img.9xun.com/newstatic/2373/032742dc68215552.xls

#\u6E20\u9053\u63A5\u53E3\u8C03\u7528
channel.approve=/kcApi/SystemGenerateApplySupplierShortlisted
#\u91C7\u8D2D\u914D\u4EF6\u6279\u91CF\u4ED8\u6B3E\u6279\u7B7E\u5730\u5740
purchase.approve=/kcApi/SystemGenerateCommonApply

web.synchronousBarcodeUrl= https://www.9ji.com/web/api/products/updateBarCode

subOutIn.url=
calcEmployeePirce.url=

apollo.url=http://************:8081,http://************:8081,http://************:8081
apollo.file=application-stock.yml

## mysql:appleSn
mysql.appleSn.url=10.1.250.157:3306
mysql.appleSn.dbname=appleSn_9ji
mysql.appleSn.username=appleSn_9ji
mysql.appleSn.password=google00
#å¤ªå
tlmall.url=https://www.tlmall.com/page/ka/kaPrimary.html
tlmall.saasCode=jiuxunyun
tlmall.custEncType=AES
tlmall.custEncKey=xBmyu0A5kgezTju7
tlmall.signType=RSA
tlmall.custSignPubkey=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQChQ1Nva1rmkA/SoxomqzXUk4s2vxzjKOjEGgaKqP8c1B2ux/hgVVFkN/xlUQpKkDNGKbtbOIUMI/aKjlspCVbkS7rZSm7r27xczm/dVQ0e9c648FEE9P11Jy1r4Kd0D4Pjq/JfBy6LoHe4JpQVg83YH87Mme2p/E8MY6fnhQKRLQIDAQAB
tlmall.custSignPrikey=MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAKFDU29rWuaQD9KjGiarNdSTiza/HOMo6MQaBoqo/xzUHa7H+GBVUWQ3/GVRCkqQM0Ypu1s4hQwj9oqOWykJVuRLutlKbuvbvFzOb91VDR71zrjwUQT0/XUnLWvgp3QPg+Or8l8HLougd7gmlBWDzdgfzsyZ7an8Twxjp+eFApEtAgMBAAECgYAD3t7QzM3YvNp0Xs/Q38kh+gycWsfxt9imZE2F5HqDEaBIwqn2ffW/JwzazbAmjAF/DJ9fmCKxYOeY+cO8X2oDdQDgj7wtxDTLCysdXFqe3J2bCUN473KZaVEaQMwNX4G+g69qviv37yviJbqh2tbJK517bMdY6JNo67LicIVl7QJBAMnBvMFCuYlgStnq5CNlO7OKeepaY21YdapfKlQTno7bJk9jdIBQZrqjhiBDBuxECX0Gdpss8cJOE5J8Kd+5uIsCQQDMnohWRnrYC8unrWk/+wQYXV2kEfCjOXITGYPNoFZ4LV/gEYgh2WhAgh6O5Yykx3yL5FJfTGgq+rTvJdwDYNwnAkEAuqtB1R3DRFOPbahihE05u5g3zJjsvVLHK2b5ZujwHwSsoW9HbyD0q2J4yoi5cwhQLxk3y8L9u+U5PqMaqyDOmQJBAKjMX0xM+CoiEO9SbvEI8mfnHcirxAfi6+g1tDV9f9fEFsORsuu5nfcZYHwhgdStfGErCYj0Tzqld32Rjd57mSECQQC4eVOkmUNgui4mflQNmI8gLnHo8j5c9/lnnxWD1CJ+krW570dm9VLJIa0lzC+x0IjpVAqaWfVIMua1jvJwV83h
tlmall.ipList=*************
#ä¸­é®
ptac.tokenurl=https://open.ptac.cn/oauth2/oauth/token
ptac.apiurl=https://open.ptac.cn/api/aifgw/http