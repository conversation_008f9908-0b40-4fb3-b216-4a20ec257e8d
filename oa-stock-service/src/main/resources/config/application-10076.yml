consul:
  host: 127.0.0.1
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://img.life99.cn/
  upload:
    url: http://**************:9333
instance-zone: 10076
jiuji:
  sys:
    moa: https://moa.life99.cn
    pc: https://oa.life99.cn
    m: https://m.life99.cn
    inWcf: http://inwcf.life99.cn
    oaWcf: http://inwcf2.life99.cn
    xtenant: 10076
  xtenant: 76000
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10076:u3K3pgjctmIt@***********:27017,***********:27017,***********:27017/ch999oa__10076
  url1: **********************************************************************************************************
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname: 
    password: 
    url: :3306
    username: 
  oa_core:
    dbname: oa_core__10076
    password: f1i5XbEFwOm9
    url: mysql.serv.hb.saas.ch999.cn:3306
    username: oa_core__10076
  oa_nc:
    dbname: oa_nc__10076
    password: 'OYf3Vq0k3Bkt'
    url: mysql.serv.hb.saas.ch999.cn:3306
    username: oa_nc__10076
  train:
    dbname: train__10076
    password: "Z7zr1RCJyd8N"
    url: mysql.serv.hb.saas.ch999.cn:3306
    username: train__10076
  oa_log:
    dbname: oa_log__10076
    password: "FO4T#PtojR2F"
    url: tidb.serv.hb.saas.ch999.cn:9383
    username: oa_log__10076
  appleSn:
    dbname: appleSn_9ji
    password: google00
    url: ************:3306
    username: appleSn_9ji
office:
  sys:
    xtenant: 10076
rabbitmq:
  master:
    password: BtSmO
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: oaAsync__10076
    vhost: oaAsync__10076
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: jDQJn
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: oa__10076
    vhost: oa__10076
  oaAsync:
    password: BtSmO
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: oaAsync__10076
    vhost: oaAsync__10076
  printer:
    password: MaPQr
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: printer__10076
    vhost: printer__10076
redis:
  oa:
    host: ***********
    password: google99
    port: 6381
    url: google99@***********:6381
sms:
  send:
    email:
      url: http://sms.life99.cn/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://sms.life99.cn/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10076
sqlserver:
  after_write:
    dbname: ch999oanew__10076
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "Tp85as7v3yjT"
    port: 1433
    username: ch999oanew__10076
  ch999oanew:
    dbname: ch999oanew__10076
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "Tp85as7v3yjT"
    port: 1433
    username: ch999oanew__10076
  ch999oanewReport:
    dbname: ch999oanew__10076
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "Tp85as7v3yjT"
    port: 1433
    username: ch999oanew__10076
  ch999oanewHis:
    dbname: ch999oanew__10076
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "Tp85as7v3yjT"
    port: 1433
    username: ch999oanew__10076
  ch999oahis:
    dbname: ch999oanew__10076
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "Tp85as7v3yjT"
    port: 1433
    username: ch999oanew__10076
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10076
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "pKphX5A20Opi"
    port: 1433
    username: office__10076
  oanewWrite:
    dbname: ch999oanew__10076
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "Tp85as7v3yjT"
    port: 1433
    username: ch999oanew__10076
  office:
    dbname: office__10076
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "pKphX5A20Opi"
    port: 1433
    username: office__10076
  officeWrite:
    dbname: office__10076
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "pKphX5A20Opi"
    port: 1433
    username: office__10076
  smallpro_write:
    dbname: ch999oanew__10076
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "Tp85as7v3yjT"
    port: 1433
    username: ch999oanew__10076
  web999:
    dbname: web999__10076
    host: sqlserver.serv.hb.saas.ch999.cn
    password: l5s294FsYkeO
    port: 1433
    username: web999__10076
  web999_other:
    dbname: web999_other__10076
    host: sqlserver.serv.hb.saas.ch999.cn
    password: kSQcmnBzH6^c
    port: 1433
    username: web999_other__10076
  ershou:
    dbname: ershou__10076
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "BJ3dJRLboJ1l"
    port: 1433
    username: ershou__10076

url:
  delImgUrl: http://data3:5083
  oa-push-info: http://inwcf.life99.cn:2988/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://img.life99.cn/
  source:
    path: i18n/url
  uploadImgUrl: http://data3:9333

mqtt:
  host: tcp://iot.9xun.com:1883
  topic: oa2/nc-segments
  clientinid: nc-segments-${random.value}
  qoslevel: 1
  username: client
  password: ch999
  timeout: 10000
  keepalive: 20
  adminUrl: http://************:8081/api/v4
  adminUser: admin
  adminPassword: public
  logistics:
    host: tcp://iot.9xun.com:1883
    topic: oa2/nc-segments
    clientinid: nc-segments-${random.value}
    qoslevel: 1
    username: client
    password: ch999
    timeout: 10000
    keepalive: 20
    adminUrl: http://************:8081/api/v4
    adminUser: admin
    adminPassword: public

chw:
  tenant: https://manager.saas.ch999.cn/saasManager/api/thirdParty/getAllDomains/v1
  warehousing: https://chw.9xun.com/cloudapi_nc/pick/api/order/oa/warehousing/v1?xservicename=pick-web
  cancelCaigou: https://chw.9xun.com/cloudapi_nc/pick/api/order/oa/cancel/v1?xservicename=pick-web
  platformUrl: https://chw.9xun.com/cloudapi_nc/pick/api/user/partner/skip/v1?xservicename=pick-web&token=%s&xtenant=%d
  messageSend: https://moa.9ji.com/cloudapi_nc/logistics/api/ChwNoticeController/sendMessage/v1
  messageChannelSend: https://moa.9ji.com/cloudapi_nc/logistics/api/ChwNoticeController/channelErrorMonitor/v1
  purchaseRemark: '%s:<a href=https://chw.9xun.com/mall/admin/trads/order-detail/%s>%s</a>'
#盘点重构excl 导入文件模板下载

inventory:
  downloadExcl: https://img.9xun.com/newstatic/2370/03805dac15b8cd57.xls
purchase:
  downloadExcl: https://img.9xun.com/newstatic/2373/032742dc68215552.xls

#渠道接口调用
channel.approve: /kcApi/SystemGenerateApplySupplierShortlisted

web:
  synchronousBarcodeUrl: https://www.9ji.com/web/api/products/updateBarCode

subOutIn:
  url: ''
calcEmployeePirce:
  url: ''

#请求宝尊接口 url
baozun.url: 'https://api-oms2wms.baozun.com/order'

apollo:
  url: http://**************:8080
  file: application-stock.yml

#太力
tlmall:
  url: https://www.tlmall.com/page/ka/kaPrimary.html
  saasCode: jiuxunyun
  custEncType: AES
  custEncKey: xBmyu0A5kgezTju7
  signType: RSA
  custSignPubkey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQChQ1Nva1rmkA/SoxomqzXUk4s2vxzjKOjEGgaKqP8c1B2ux/hgVVFkN/xlUQpKkDNGKbtbOIUMI/aKjlspCVbkS7rZSm7r27xczm/dVQ0e9c648FEE9P11Jy1r4Kd0D4Pjq/JfBy6LoHe4JpQVg83YH87Mme2p/E8MY6fnhQKRLQIDAQAB
  custSignPrikey: MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAKFDU29rWuaQD9KjGiarNdSTiza/HOMo6MQaBoqo/xzUHa7H+GBVUWQ3/GVRCkqQM0Ypu1s4hQwj9oqOWykJVuRLutlKbuvbvFzOb91VDR71zrjwUQT0/XUnLWvgp3QPg+Or8l8HLougd7gmlBWDzdgfzsyZ7an8Twxjp+eFApEtAgMBAAECgYAD3t7QzM3YvNp0Xs/Q38kh+gycWsfxt9imZE2F5HqDEaBIwqn2ffW/JwzazbAmjAF/DJ9fmCKxYOeY+cO8X2oDdQDgj7wtxDTLCysdXFqe3J2bCUN473KZaVEaQMwNX4G+g69qviv37yviJbqh2tbJK517bMdY6JNo67LicIVl7QJBAMnBvMFCuYlgStnq5CNlO7OKeepaY21YdapfKlQTno7bJk9jdIBQZrqjhiBDBuxECX0Gdpss8cJOE5J8Kd+5uIsCQQDMnohWRnrYC8unrWk/+wQYXV2kEfCjOXITGYPNoFZ4LV/gEYgh2WhAgh6O5Yykx3yL5FJfTGgq+rTvJdwDYNwnAkEAuqtB1R3DRFOPbahihE05u5g3zJjsvVLHK2b5ZujwHwSsoW9HbyD0q2J4yoi5cwhQLxk3y8L9u+U5PqMaqyDOmQJBAKjMX0xM+CoiEO9SbvEI8mfnHcirxAfi6+g1tDV9f9fEFsORsuu5nfcZYHwhgdStfGErCYj0Tzqld32Rjd57mSECQQC4eVOkmUNgui4mflQNmI8gLnHo8j5c9/lnnxWD1CJ+krW570dm9VLJIa0lzC+x0IjpVAqaWfVIMua1jvJwV83h
  ipList: *************