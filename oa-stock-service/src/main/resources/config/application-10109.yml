consul:
  host: 127.0.0.1
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://img.919wx.com/
  upload:
    url: http://**************:9333
instance-zone: 10109
jiuji:
  sys:
    moa: https://moa.919wx.com
    pc: https://oa.919wx.com
    m: https://m.919wx.com
    xtenant: 10109
  xtenant: 109000
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10109:IaWQJDpBcGtE@***********:27017,***********:27017,***********:27017/ch999oa__10109
  url1: **********************************************************************************************************
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname:
    password:
    url: :3306
    username:
  oa_core:
    dbname: oa_core__10109
    password: B4x5zqsWpDne
    url: mysql.serv.hb.saas.ch999.cn:3306
    username: oa_core__10109
  oa_nc:
    dbname: oa_nc__10109
    password: 'kd7QdeDfBuRR'
    url: mysql.serv.hb.saas.ch999.cn:3306
    username: oa_nc__10109
  train:
    dbname: train__10109
    password: "NynJ7iWBZqsD"
    url: mysql.serv.hb.saas.ch999.cn:3306
    username: train__10109
  oa_log:
    dbname: oa_log__10109
    password: "0#YQet36oJfJ"
    url: tidb.serv.hb.saas.ch999.cn:9383
    username: oa_log__10109
office:
  sys:
    xtenant: 10109
rabbitmq:
  master:
    password: lVLDE
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: oaAsync__10109
    vhost: oaAsync__10109
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: FQsYD
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: oa__10109
    vhost: oa__10109
  oaAsync:
    password: lVLDE
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: oaAsync__10109
    vhost: oaAsync__10109
  printer:
    password: JGhjY
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: printer__10109
    vhost: printer__10109
redis:
  oa:
    host: ***********
    password: google99
    port: 6395
    url: google99@***********:6395
sms:
  send:
    email:
      url: http://sms.919wx.com/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://sms.919wx.com/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10109
sqlserver:
  after_write:
    dbname: ch999oanew__10109
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: "9ZZPrmUfqQ4G"
    port: 1433
    username: ch999oanew__10109
  ch999oanew:
    dbname: ch999oanew__10109
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: "9ZZPrmUfqQ4G"
    port: 1433
    username: ch999oanew__10109
  ch999oanewReport:
    dbname: ch999oanew__10109
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: "9ZZPrmUfqQ4G"
    port: 1433
    username: ch999oanew__10109
  ch999oanewHis:
    dbname: ch999oanew__10109
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: "9ZZPrmUfqQ4G"
    port: 1433
    username: ch999oanew__10109
  ch999oahis:
    dbname: ch999oanew__10109
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: "9ZZPrmUfqQ4G"
    port: 1433
    username: ch999oanew__10109
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10109
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: "mIG3Ilbd6DVy"
    port: 1433
    username: office__10109
  oanewWrite:
    dbname: ch999oanew__10109
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: "9ZZPrmUfqQ4G"
    port: 1433
    username: ch999oanew__10109
  office:
    dbname: office__10109
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: "mIG3Ilbd6DVy"
    port: 1433
    username: office__10109
  officeWrite:
    dbname: office__10109
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: "mIG3Ilbd6DVy"
    port: 1433
    username: office__10109
  smallpro_write:
    dbname: ch999oanew__10109
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: "9ZZPrmUfqQ4G"
    port: 1433
    username: ch999oanew__10109
  web999:
    dbname: web999__10109
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: xk8kTAOuVIFV
    port: 1433
    username: web999__10109
  web999_other:
    dbname: web999_other__10109
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: v4F1TdQktssa
    port: 1433
    username: web999_other__10109

url:
  delImgUrl: http://data3:5083
  oa-push-info: http://inwcf.919wx.com:2988/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://img.919wx.com/
  source:
    path: i18n/url
  uploadImgUrl: http://data3:9333

mqtt:
  host: tcp://iot.9xun.com:1883
  topic: oa2/nc-segments
  clientinid: nc-segments-${random.value}
  qoslevel: 1
  username: client
  password: ch999
  timeout: 10000
  keepalive: 20
  adminUrl: http://************:8081/api/v4
  adminUser: admin
  adminPassword: public
  logistics:
    host: tcp://iot.9xun.com:1883
    topic: oa2/nc-segments
    clientinid: nc-segments-${random.value}
    qoslevel: 1
    username: client
    password: ch999
    timeout: 10000
    keepalive: 20
    adminUrl: http://************:8081/api/v4
    adminUser: admin
    adminPassword: public

chw:
  tenant: https://manager.saas.ch999.cn/saasManager/api/thirdParty/getAllDomains/v1
  warehousing: https://chw.9xun.com/cloudapi_nc/pick/api/order/oa/warehousing/v1?xservicename=pick-web
  cancelCaigou: https://chw.9xun.com/cloudapi_nc/pick/api/order/oa/cancel/v1?xservicename=pick-web
  platformUrl: https://chw.9xun.com/cloudapi_nc/pick/api/user/partner/skip/v1?xservicename=pick-web&token=%s&xtenant=%d
  messageSend: https://moa.9ji.com/cloudapi_nc/logistics/api/ChwNoticeController/sendMessage/v1
  messageChannelSend: https://moa.9ji.com/cloudapi_nc/logistics/api/ChwNoticeController/channelErrorMonitor/v1
  purchaseRemark: '%s:<a href=https://chw.9xun.com/mall/admin/trads/order-detail/%s>%s</a>'
#盘点重构excl 导入文件模板下载

inventory:
  downloadExcl: https://img.9xun.com/newstatic/2370/03805dac15b8cd57.xls
purchase:
  downloadExcl: https://img.9xun.com/newstatic/2373/032742dc68215552.xls

#渠道接口调用
channel.approve: /kcApi/SystemGenerateApplySupplierShortlisted

web:
  synchronousBarcodeUrl: https://www.9ji.com/web/api/products/updateBarCode

subOutIn:
  url: ''
calcEmployeePirce:
  url: ''

#请求宝尊接口 url
baozun.url: 'https://api-oms2wms.baozun.com/order'