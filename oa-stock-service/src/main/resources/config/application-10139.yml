consul:
  host: 127.0.0.1
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://img.99fsm.com/
  upload:
    url: http://**************:9333
instance-zone: 10139
jiuji:
  sys:
    moa: https://moa.99fsm.com
    pc: https://oa.99fsm.com
    m: https://m.99fsm.com
    xtenant: 10139
  xtenant: 139000
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10139:jLWvc675Ig84@***********:27017,***********:27017,***********:27017/ch999oa__10139
  url1: **********************************************************************************************************
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname: 
    password: 
    url: :3306
    username: 
  oa_core:
    dbname: oa_core__10139
    password: EcrIoUp9sQjB
    url: mysql.serv.hd.saas.ch999.cn:3306
    username: oa_core__10139
  oa_nc:
    dbname: oa_nc__10139
    password: 'ip0qXB6lw8Ni'
    url: mysql.serv.hd.saas.ch999.cn:3306
    username: oa_nc__10139
  train:
    dbname: train__10139
    password: "R0Q0WCGTpM5t"
    url: mysql.serv.hd.saas.ch999.cn:3306
    username: train__10139
  oa_log:
    dbname: oa_log__10139
    password: "FMtR9hWAaYBr"
    url: tidb.serv.hd.saas.ch999.cn:9383
    username: oa_log__10139
office:
  sys:
    xtenant: 10139
rabbitmq:
  master:
    password: dlYYL
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oaAsync__10139
    vhost: oaAsync__10139
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: YAknG
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oa__10139
    vhost: oa__10139
  oaAsync:
    password: dlYYL
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oaAsync__10139
    vhost: oaAsync__10139
  printer:
    password: pJeEF
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: printer__10139
    vhost: printer__10139
redis:
  oa:
    host: ***********
    password: google99
    port: 6398
    url: google99@***********:6398
sms:
  send:
    email:
      url: http://sms.99fsm.com/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://sms.99fsm.com/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10139
sqlserver:
  after_write:
    dbname: ch999oanew__10139
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: "ExJ13KT8rF2L"
    port: 1433
    username: ch999oanew__10139
  ch999oanew:
    dbname: ch999oanew__10139
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: "ExJ13KT8rF2L"
    port: 1433
    username: ch999oanew__10139
  ch999oanewReport:
    dbname: ch999oanew__10139
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: "ExJ13KT8rF2L"
    port: 1433
    username: ch999oanew__10139
  ch999oanewHis:
    dbname: ch999oanew__10139
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: "ExJ13KT8rF2L"
    port: 1433
    username: ch999oanew__10139
  ch999oahis:
    dbname: ch999oanew__10139
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: "ExJ13KT8rF2L"
    port: 1433
    username: ch999oanew__10139
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10139
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: "b1utgQXOSTI3"
    port: 1433
    username: office__10139
  oanewWrite:
    dbname: ch999oanew__10139
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: "ExJ13KT8rF2L"
    port: 1433
    username: ch999oanew__10139
  office:
    dbname: office__10139
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: "b1utgQXOSTI3"
    port: 1433
    username: office__10139
  officeWrite:
    dbname: office__10139
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: "b1utgQXOSTI3"
    port: 1433
    username: office__10139
  smallpro_write:
    dbname: ch999oanew__10139
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: "ExJ13KT8rF2L"
    port: 1433
    username: ch999oanew__10139
  web999:
    dbname: web999__10139
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: 2n64O#osC^vE
    port: 1433
    username: web999__10139
  web999_other:
    dbname: web999_other__10139
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: JcgTwLd0dwfc
    port: 1433
    username: web999_other__10139
  ershou:
    dbname: ershou__10139
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: "h98whg2ZnhBF"
    port: 1433
    username: ershou__10139

url:
  delImgUrl: http://data3:5083
  oa-push-info: http://inwcf.99fsm.com:2988/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://img.99fsm.com/
  source:
    path: i18n/url
  uploadImgUrl: http://data3:9333

mqtt:
  host: tcp://iot.9xun.com:1883
  topic: oa2/nc-segments
  clientinid: nc-segments-${random.value}
  qoslevel: 1
  username: client
  password: ch999
  timeout: 10000
  keepalive: 20
  adminUrl: http://************:8081/api/v4
  adminUser: admin
  adminPassword: public
  logistics:
    host: tcp://iot.9xun.com:1883
    topic: oa2/nc-segments
    clientinid: nc-segments-${random.value}
    qoslevel: 1
    username: client
    password: ch999
    timeout: 10000
    keepalive: 20
    adminUrl: http://************:8081/api/v4
    adminUser: admin
    adminPassword: public

chw:
  tenant: https://manager.saas.ch999.cn/saasManager/api/thirdParty/getAllDomains/v1
  warehousing: https://chw.9xun.com/cloudapi_nc/pick/api/order/oa/warehousing/v1?xservicename=pick-web
  cancelCaigou: https://chw.9xun.com/cloudapi_nc/pick/api/order/oa/cancel/v1?xservicename=pick-web
  platformUrl: https://chw.9xun.com/cloudapi_nc/pick/api/user/partner/skip/v1?xservicename=pick-web&token=%s&xtenant=%d
  messageSend: https://moa.9ji.com/cloudapi_nc/logistics/api/ChwNoticeController/sendMessage/v1
  messageChannelSend: https://moa.9ji.com/cloudapi_nc/logistics/api/ChwNoticeController/channelErrorMonitor/v1
  purchaseRemark: '%s:<a href=https://chw.9xun.com/mall/admin/trads/order-detail/%s>%s</a>'
#盘点重构excl 导入文件模板下载

inventory:
  downloadExcl: https://img.9xun.com/newstatic/2370/03805dac15b8cd57.xls
purchase:
  downloadExcl: https://img.9xun.com/newstatic/2373/032742dc68215552.xls

#渠道接口调用
channel.approve: /kcApi/SystemGenerateApplySupplierShortlisted

web:
  synchronousBarcodeUrl: https://www.9ji.com/web/api/products/updateBarCode

subOutIn:
  url: ''
calcEmployeePirce:
  url: ''

#请求宝尊接口 url
baozun.url: 'https://api-oms2wms.baozun.com/order'

apollo:
  url: 