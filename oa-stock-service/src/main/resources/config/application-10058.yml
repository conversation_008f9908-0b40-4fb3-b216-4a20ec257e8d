consul:
  host: 127.0.0.1
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://img.cs7x.cn/
  upload:
    url: http://**************:9333
instance-zone: 10058
jiuji:
  sys:
    moa: https://moa.cs7x.cn
    pc: https://oa.cs7x.cn
    m: https://m.cs7x.cn
    xtenant: 10058
  xtenant: 58000
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10058:7qmnZtDP@**************:27017,**************:27017,**************:27017/ch999oa__10058
  url1: ***************************************************************************************************************
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname:
    password:
    url: :3306
    username:
  oa_core:
    dbname: oa_core__10058
    password: oa_corewjR
    url: mysql.serv.xn.saas.ch999.cn:3306
    username: oa_core__10058
  oa_nc:
    dbname: oa_nc__10058
    password: 'oa_nciFm'
    url: mysql.serv.xn.saas.ch999.cn:3306
    username: oa_nc__10058
  train:
    dbname: train__10058
    password: "trainVOL"
    url: mysql.serv.xn.saas.ch999.cn:3306
    username: train__10058
  oa_log:
    dbname: oa_log__10058
    password: "d7afIt3nODku"
    url: main.tidb.ch999.cn:9383
    username: oa_log__10058
office:
  sys:
    xtenant: 10058
rabbitmq:
  master:
    password: fznEp
    port: 5672
    url: rabbitmq.serv.xn.saas.ch999.cn
    username: oaAsync__10058
    vhost: oaAsync__10058
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: jcKIJ
    port: 5672
    url: rabbitmq.serv.xn.saas.ch999.cn
    username: oa__10058
    vhost: oa__10058
  oaAsync:
    password: fznEp
    port: 5672
    url: rabbitmq.serv.xn.saas.ch999.cn
    username: oaAsync__10058
    vhost: oaAsync__10058
  printer:
    password: RrOqG
    port: 5672
    url: rabbitmq.serv.xn.saas.ch999.cn
    username: printer__10058
    vhost: printer__10058
redis:
  oa:
    host: **************
    password: google99
    port: 6392
    url: google99@**************:6392
sms:
  send:
    email:
      url: http://sms.cs7x.cn/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://sms.cs7x.cn/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10058
sqlserver:
  after_write:
    dbname: ch999oanew__10058
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "ch999oanewTREtr"
    port: 1433
    username: ch999oanew__10058
  ch999oanew:
    dbname: ch999oanew__10058
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "ch999oanewTREtr"
    port: 1433
    username: ch999oanew__10058
  ch999oanewReport:
    dbname: ch999oanew__10058
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "ch999oanewTREtr"
    port: 1433
    username: ch999oanew__10058
  ch999oanewHis:
    dbname: ch999oanew__10058
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "ch999oanewTREtr"
    port: 1433
    username: ch999oanew__10058
  ch999oahis:
    dbname: ch999oanew__10058
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "ch999oanewTREtr"
    port: 1433
    username: ch999oanew__10058
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10058
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "officeoFDIa"
    port: 1433
    username: office__10058
  oanewWrite:
    dbname: ch999oanew__10058
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "ch999oanewTREtr"
    port: 1433
    username: ch999oanew__10058
  office:
    dbname: office__10058
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "officeoFDIa"
    port: 1433
    username: office__10058
  officeWrite:
    dbname: office__10058
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "officeoFDIa"
    port: 1433
    username: office__10058
  smallpro_write:
    dbname: ch999oanew__10058
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "ch999oanewTREtr"
    port: 1433
    username: ch999oanew__10058
  web999:
    dbname: web999__10058
    host: sqlserver.serv.xn.saas.ch999.cn
    password: web999kyzPp
    port: 1433
    username: web999__10058
  web999_other:
    dbname: web999_other__10058
    host: sqlserver.serv.xn.saas.ch999.cn
    password: web999_otherLEFfN
    port: 1433
    username: web999_other__10058
  ershou:
    dbname: ershou__10058
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "ershouttnWr"
    port: 1433
    username: ershou__10058

url:
  delImgUrl: http://data3:5083
  oa-push-info: http://inwcf.cs7x.cn:2988/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://img.cs7x.cn/
  source:
    path: i18n/url
  uploadImgUrl: http://data3:9333

mqtt:
  host: tcp://iot.9xun.com:1883
  topic: oa2/nc-segments
  clientinid: nc-segments-${random.value}
  qoslevel: 1
  username: client
  password: ch999
  timeout: 10000
  keepalive: 20
  adminUrl: http://************:8081/api/v4
  adminUser: admin
  adminPassword: public
  logistics:
    host: tcp://iot.9xun.com:1883
    topic: oa2/nc-segments
    clientinid: nc-segments-${random.value}
    qoslevel: 1
    username: client
    password: ch999
    timeout: 10000
    keepalive: 20
    adminUrl: http://************:8081/api/v4
    adminUser: admin
    adminPassword: public

chw:
  tenant: https://manager.saas.ch999.cn/saasManager/api/thirdParty/getAllDomains/v1
  warehousing: https://chw.9xun.com/cloudapi_nc/pick/api/order/oa/warehousing/v1?xservicename=pick-web
  cancelCaigou: https://chw.9xun.com/cloudapi_nc/pick/api/order/oa/cancel/v1?xservicename=pick-web
  platformUrl: https://chw.9xun.com/cloudapi_nc/pick/api/user/partner/skip/v1?xservicename=pick-web&token=%s&xtenant=%d
  messageSend: https://moa.9ji.com/cloudapi_nc/logistics/api/ChwNoticeController/sendMessage/v1
  messageChannelSend: https://moa.9ji.com/cloudapi_nc/logistics/api/ChwNoticeController/channelErrorMonitor/v1
  purchaseRemark: '%s:<a href=https://chw.9xun.com/mall/admin/trads/order-detail/%s>%s</a>'
#盘点重构excl 导入文件模板下载

inventory:
  downloadExcl: https://img.9xun.com/newstatic/2370/03805dac15b8cd57.xls
purchase:
  downloadExcl: https://img.9xun.com/newstatic/2373/032742dc68215552.xls

#渠道接口调用
channel.approve: /kcApi/SystemGenerateApplySupplierShortlisted

web:
  synchronousBarcodeUrl: https://www.9ji.com/web/api/products/updateBarCode

subOutIn:
  url: ''
calcEmployeePirce:
  url: ''

#请求宝尊接口 url
baozun.url: 'https://api-oms2wms.baozun.com/order'

apollo:
  url: