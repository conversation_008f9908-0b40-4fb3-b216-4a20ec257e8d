data9=**************
data5=************
## \u6570\u636E\u5E93 sqlserver
sqlserver.data.host=sqlserver.dev.ch999.cn
sqlserver.data.port=1433

## sqlserver:ch999oanew
sqlserver.ch999oanew.host=${sqlserver.data.host}
sqlserver.ch999oanew.port=${sqlserver.data.port}
sqlserver.ch999oanew.dbname=ch999oanew
sqlserver.ch999oanew.username=devops
sqlserver.ch999oanew.password=devops00
## sqlserver:ch999oanewReport
sqlserver.ch999oanewReport.host=${sqlserver.data.host}
sqlserver.ch999oanewReport.port=${sqlserver.data.port}
sqlserver.ch999oanewReport.dbname=ch999oanew
sqlserver.ch999oanewReport.username=devops
sqlserver.ch999oanewReport.password=devops00
## sqlserver:ch999oanewWrite
sqlserver.oanewWrite.host=${sqlserver.data.host}
sqlserver.oanewWrite.port=${sqlserver.data.port}
sqlserver.oanewWrite.dbname=ch999oanew
sqlserver.oanewWrite.username=devops
sqlserver.oanewWrite.password=devops00
## sqlserver:office
sqlserver.office.host=${sqlserver.data.host}
sqlserver.office.port=${sqlserver.data.port}
sqlserver.office.dbname=office
sqlserver.office.username=devops
sqlserver.office.password=devops00
## sqlserver:oaOffice
sqlserver.oaOffice.host=${sqlserver.data.host}
sqlserver.oaOffice.port=${sqlserver.data.port}
sqlserver.oaOffice.dbname=ch999oanew
sqlserver.oaOffice.username=devops
sqlserver.oaOffice.password=devops00
## sqlserver:oanewWrite
sqlserver.officeWrite.host=${sqlserver.data.host}
sqlserver.officeWrite.port=${sqlserver.data.port}
sqlserver.officeWrite.dbname=office
sqlserver.officeWrite.username=devops
sqlserver.officeWrite.password=devops00

## sqlserver:ershou
sqlserver.ershou.host=${sqlserverr.data.host}
sqlserver.ershou.port=${sqlserver.data.port}
sqlserver.ershou.dbname=ershou
sqlserver.ershou.username=devops
sqlserver.ershou.password=devops00
## sqlserver:web99
sqlserver.web999.host=${sqlserverr.data.host}
sqlserver.web999.port=${sqlserver.data.port}
sqlserver.web999.dbname=web999
sqlserver.web999.username=devops
sqlserver.web999.password=devops00
## sqlserver:web99_other
sqlserver.web999_other.host=${sqlserver.data.host}
sqlserver.web999_other.port=${sqlserver.data.port}
sqlserver.web999_other.dbname=web999_other
sqlserver.web999_other.username=devops
sqlserver.web999_other.password=devops00
## sqlserver:smallpro_write
sqlserver.smallpro_write.host=${sqlserver.data.host}
sqlserver.smallpro_write.port=${sqlserver.data.port}
sqlserver.smallpro_write.dbname=ch999oanew
sqlserver.smallpro_write.username=devops
sqlserver.smallpro_write.password=devops00
## sqlserver:ch999oahis
sqlserver.ch999oahis.host=${sqlserver.data.host}
sqlserver.ch999oahis.port=${sqlserver.data.port}
sqlserver.ch999oahis.dbname=ch999oahis
sqlserver.ch999oahis.username=devops
sqlserver.ch999oahis.password=devops00

## mysql
mysql.url=mysql.dev.ch999.cn:3306
## mysql:oa_nc
mysql.oa_nc.url=${mysql.url}
mysql.oa_nc.dbname=oa_nc
mysql.oa_nc.username=oa_nc
mysql.oa_nc.password=oa_nc2020#


## mysql:appleSn
mysql.appleSn.url=************:3306
mysql.appleSn.dbname=appleSn_9ji
mysql.appleSn.username=appleSn_9ji
mysql.appleSn.password=google00


## mysql:train
mysql.train.url=${mysql.url}
mysql.train.dbname=train
mysql.train.username=train
mysql.train.password=train2020#

# mysql oa_log
mysql.oa_log.url=************:9383
mysql.oa_log.dbname=logistics
mysql.oa_log.username=logistics
mysql.oa_log.password=logistics!@#

# TiDB
tidb.logdb.url=************:9383
tidb.logdb.dbname=logistics
tidb.logdb.username=logistics
tidb.logdb.password=logistics!@#

## StarRocks
mysql.starrocks.url=************:29030
mysql.starrocks.dbname=ods_jiuji
mysql.starrocks.username=dev_operation
mysql.starrocks.password=osPIRH2dT47


## midl
redis.oa.host=redis.dev.ch999.cn
redis.oa.port=6379
redis.oa.password=google00
redis.oa.url=${redis.oa.password}@${redis.oa.host}:${redis.oa.port}
## image
image.upload.url=http://weedfs.dev.ch999.cn:9333
image.del.url=http://weedfs.dev.ch999.cn:5083
image.select.url=https://img.dev.9ji.com/

## rabbitmq
rabbitmq.master.url=rabbitmq.dev.ch999.cn
rabbitmq.oaAsync.url=${rabbitmq.master.url}
rabbitmq.oaAsync.port=5672
rabbitmq.oaAsync.vhost=oaAsync
rabbitmq.oaAsync.username=admin
rabbitmq.oaAsync.password=ch999

rabbitmq.printer.url=${rabbitmq.master.url}
rabbitmq.printer.port=5672
rabbitmq.printer.vhost=printer
rabbitmq.printer.username=admin
rabbitmq.printer.password=ch999

rabbitmq.oa.url=${rabbitmq.master.url}
rabbitmq.oa.port=5672
rabbitmq.oa.vhost=oa
rabbitmq.oa.username=oa
rabbitmq.oa.password=ch999

mongodb.url1=mongodb://ch999oa:<EMAIL>:27017/ch999oa
spring.messages.xtenant=0
consul.host=************
consul.port=8500
spring.cloud.consul.discovery.instance-zone=9ji
instance-zone=9ji
sqlserver.datasource.max-pool-size=5
mysql.datasource.max-pool-size=5
jiuji.sys.xtenant=0
jiuji.sys.m=
jiuji.sys.moa=https://moa.9ji.com
jiuji.sys.pc=https://oa.9ji.com
jiuji.sys.inWcf=http://inwcf.dev.9ji.com
jiuji.sys.oaWcf=http://oawcf.dev.9ji.com
mqtt.host=tcp://************:1883
mqtt.topic=oa2/nc-segments
mqtt.clientinid=nc-segments-${random.value}
mqtt.qoslevel=1
mqtt.username=nc-segments
mqtt.password=password@312
mqtt.timeout=10000
mqtt.keepalive=20
mqtt.adminUrl=http://************:8081/api/v4
mqtt.adminUser=admin
mqtt.adminPassword=public

# ç©æµçµè¯mqtt
mqtt.logistics.host=tcp://iot.9xun.com:1883
mqtt.logistics.topic=VoIP-gateway-server
mqtt.logistics.clientinid=oa-stock-call-${random.value}
mqtt.logistics.qoslevel=1
mqtt.logistics.username=admin
mqtt.logistics.password=ch999
mqtt.logistics.timeout=10000
mqtt.logistics.keepalive=20
mqtt.logistics.adminUrl=http://**************:8081/api/v4
mqtt.logistics.adminUser=admin
mqtt.logistics.adminPassword=public


# éè´§ç è°ç¨å°å
chw.warehousing=https://chwdev.9xun.com/cloudapi_nc/pick/api/order/oa/warehousing/v1?xservicename=pick-web
chw.cancelCaigou=https://chwdev.9xun.com/cloudapi_nc/pick/api/order/oa/cancel/v1?xservicename=pick-web
chw.platformUrl=https://chwdev.9xun.com/cloudapi_nc/pick/api/user/partner/skip/v1?xservicename=pick-web&token=%s&xtenant=%d
chw.messageSend=https://moa.9ji.com/cloudapi_nc/logistics/api/ChwNoticeController/sendMessage/v1
chw.messageChannelSend= https://moa.9ji.com/cloudapi_nc/logistics/api/ChwNoticeController/channelErrorMonitor/v1
chw.purchaseRemark= %s:<a href=https://chwdev.9xun.com/mall/admin/trads/order-detail/%s>%s</a>
#çç¹éæexcl å¯¼å¥æä»¶æ¨¡æ¿ä¸è½½
inventory.downloadExcl= https://img.9xun.com/newstatic/27122/06e7bfdaba9d0492.xls
purchase.downloadExcl= https://img.9xun.com/newstatic/2373/032742dc68215552.xls

#æ¸ éæ¥å£è°ç¨
channel.approve=/kcApi/SystemGenerateApplySupplierShortlisted
#å®å°
baozun.apiurl = https://test-api-oms2wms.baozun.com/order

# è¯·æ±éè®¯å½ä¸çº§æ¥å£
userChiefs.url = https://moa.dev.9ji.com/app/GetUserChiefs?ch999Id=
#éè´­éä»¶æ¹éä»æ¬¾æ¹ç­¾å°å
purchase.approve=/kcApi/SystemGenerateCommonApply

web.synchronousBarcodeUrl= https://www.dev.9ji.com/web/api/products/updateBarCode

##Oa
subOutIn.url=http://oawcf2.ch999.cn/kcApi/wmsPickUpComplete
calcEmployeePirce.url=http://inwcf.dev.9ji.com/oaApi.svc/rest/CalcEmployeePirceForAccessory

apollo.url=http://************:6000
apollo.file=application-stock.yml
springdoc.swagger-ui.enabled=true
