consul:
  host: 127.0.0.1
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://img.stsmt.com/
  upload:
    url: http://**************:9333
instance-zone: 10128
jiuji:
  sys:
    moa: https://moa.stsmt.com
    pc: https://oa.stsmt.com
    m: https://m.stsmt.com
    xtenant: 10128
  xtenant: 128000
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10128:f2sKYGP71UcO@************:27017,************:27017,************:27017/ch999oa__10128
  url1: *************************************************************************************************************
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname: 
    password: 
    url: :3306
    username: 
  oa_core:
    dbname: oa_core__10128
    password: v0I35LAJZhhd
    url: mysql.serv.hn.saas.ch999.cn:3306
    username: oa_core__10128
  oa_nc:
    dbname: oa_nc__10128
    password: 'y9ey9^CUHQTK'
    url: mysql.serv.hn.saas.ch999.cn:3306
    username: oa_nc__10128
  train:
    dbname: train__10128
    password: "15z3wakEN^HG"
    url: mysql.serv.hn.saas.ch999.cn:3306
    username: train__10128
  oa_log:
    dbname: oa_log__10128
    password: "aAVOgFNT9jFN"
    url: tidb.serv.hn.saas.ch999.cn:9383
    username: oa_log__10128
office:
  sys:
    xtenant: 10128
rabbitmq:
  master:
    password: XSvWz
    port: 5672
    url: rabbitmq.serv.hn.saas.ch999.cn
    username: oaAsync__10128
    vhost: oaAsync__10128
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: qkMmh
    port: 5672
    url: rabbitmq.serv.hn.saas.ch999.cn
    username: oa__10128
    vhost: oa__10128
  oaAsync:
    password: XSvWz
    port: 5672
    url: rabbitmq.serv.hn.saas.ch999.cn
    username: oaAsync__10128
    vhost: oaAsync__10128
  printer:
    password: bLglE
    port: 5672
    url: rabbitmq.serv.hn.saas.ch999.cn
    username: printer__10128
    vhost: printer__10128
redis:
  oa:
    host: ************
    password: google99
    port: 6384
    url: google99@************:6384
sms:
  send:
    email:
      url: http://sms.stsmt.com/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://sms.stsmt.com/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10128
sqlserver:
  after_write:
    dbname: ch999oanew__10128
    host: sqlserver.serv.hn.saas.ch999.cn
    password: "fhv^4jr7xhuZ"
    port: 1433
    username: ch999oanew__10128
  ch999oanew:
    dbname: ch999oanew__10128
    host: sqlserver.serv.hn.saas.ch999.cn
    password: "fhv^4jr7xhuZ"
    port: 1433
    username: ch999oanew__10128
  ch999oanewReport:
    dbname: ch999oanew__10128
    host: sqlserver.serv.hn.saas.ch999.cn
    password: "fhv^4jr7xhuZ"
    port: 1433
    username: ch999oanew__10128
  ch999oanewHis:
    dbname: ch999oanew__10128
    host: sqlserver.serv.hn.saas.ch999.cn
    password: "fhv^4jr7xhuZ"
    port: 1433
    username: ch999oanew__10128
  ch999oahis:
    dbname: ch999oanew__10128
    host: sqlserver.serv.hn.saas.ch999.cn
    password: "fhv^4jr7xhuZ"
    port: 1433
    username: ch999oanew__10128
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10128
    host: sqlserver.serv.hn.saas.ch999.cn
    password: "qSy9i7FhCR5j"
    port: 1433
    username: office__10128
  oanewWrite:
    dbname: ch999oanew__10128
    host: sqlserver.serv.hn.saas.ch999.cn
    password: "fhv^4jr7xhuZ"
    port: 1433
    username: ch999oanew__10128
  office:
    dbname: office__10128
    host: sqlserver.serv.hn.saas.ch999.cn
    password: "qSy9i7FhCR5j"
    port: 1433
    username: office__10128
  officeWrite:
    dbname: office__10128
    host: sqlserver.serv.hn.saas.ch999.cn
    password: "qSy9i7FhCR5j"
    port: 1433
    username: office__10128
  smallpro_write:
    dbname: ch999oanew__10128
    host: sqlserver.serv.hn.saas.ch999.cn
    password: "fhv^4jr7xhuZ"
    port: 1433
    username: ch999oanew__10128
  web999:
    dbname: web999__10128
    host: sqlserver.serv.hn.saas.ch999.cn
    password: 4fuqeHPfb3Os
    port: 1433
    username: web999__10128
  web999_other:
    dbname: web999_other__10128
    host: sqlserver.serv.hn.saas.ch999.cn
    password: 0ujosh53PF5y
    port: 1433
    username: web999_other__10128
  ershou:
    dbname: ershou__10128
    host: sqlserver.serv.hn.saas.ch999.cn
    password: "REkwPQE9j#po"
    port: 1433
    username: ershou__10128

url:
  delImgUrl: http://data3:5083
  oa-push-info: http://inwcf.stsmt.com:2988/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://img.stsmt.com/
  source:
    path: i18n/url
  uploadImgUrl: http://data3:9333

mqtt:
  host: tcp://iot.9xun.com:1883
  topic: oa2/nc-segments
  clientinid: nc-segments-${random.value}
  qoslevel: 1
  username: client
  password: ch999
  timeout: 10000
  keepalive: 20
  adminUrl: http://************:8081/api/v4
  adminUser: admin
  adminPassword: public
  logistics:
    host: tcp://iot.9xun.com:1883
    topic: oa2/nc-segments
    clientinid: nc-segments-${random.value}
    qoslevel: 1
    username: client
    password: ch999
    timeout: 10000
    keepalive: 20
    adminUrl: http://************:8081/api/v4
    adminUser: admin
    adminPassword: public

chw:
  tenant: https://manager.saas.ch999.cn/saasManager/api/thirdParty/getAllDomains/v1
  warehousing: https://chw.9xun.com/cloudapi_nc/pick/api/order/oa/warehousing/v1?xservicename=pick-web
  cancelCaigou: https://chw.9xun.com/cloudapi_nc/pick/api/order/oa/cancel/v1?xservicename=pick-web
  platformUrl: https://chw.9xun.com/cloudapi_nc/pick/api/user/partner/skip/v1?xservicename=pick-web&token=%s&xtenant=%d
  messageSend: https://moa.9ji.com/cloudapi_nc/logistics/api/ChwNoticeController/sendMessage/v1
  messageChannelSend: https://moa.9ji.com/cloudapi_nc/logistics/api/ChwNoticeController/channelErrorMonitor/v1
  purchaseRemark: '%s:<a href=https://chw.9xun.com/mall/admin/trads/order-detail/%s>%s</a>'
#盘点重构excl 导入文件模板下载

inventory:
  downloadExcl: https://img.9xun.com/newstatic/2370/03805dac15b8cd57.xls
purchase:
  downloadExcl: https://img.9xun.com/newstatic/2373/032742dc68215552.xls

#渠道接口调用
channel.approve: /kcApi/SystemGenerateApplySupplierShortlisted

web:
  synchronousBarcodeUrl: https://www.9ji.com/web/api/products/updateBarCode

subOutIn:
  url: ''
calcEmployeePirce:
  url: ''

#请求宝尊接口 url
baozun.url: 'https://api-oms2wms.baozun.com/order'

apollo:
  url: http://**************:8080
  file: application-stock.yml