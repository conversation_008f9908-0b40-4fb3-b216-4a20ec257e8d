consul:
  host: 127.0.0.1
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://img.iteng.com/
  upload:
    url: http://**************:9333
instance-zone: 10004
jiuji:
  sys:
    moa: https://moa.iteng.com
    pc: https://oa.iteng.com
    m: https://m.iteng.com
    xtenant: 10004
  xtenant: 4000
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10004:KhedYeBZcURacY2N@************:27017,************:27017,************:27017/ch999oa__10004
  url1: *****************************************************************************************************************
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname: 
    password: 
    url: :3306
    username: 
  oa_core:
    dbname: oa_core__10004
    password: NgPj%EQkjXb$*17T
    url: master.mysql.serv.iteng.com:3306
    username: oa_core__10004
  oa_nc:
    dbname: oa_nc__10004
    password: 'sfHc6MgmQ7!p^3Mm'
    url: master.mysql.serv.iteng.com:3306
    username: oa_nc__10004
  train:
    dbname: train__10004
    password: "9DeVmZoZ%RNhY$2v"
    url: master.mysql.serv.iteng.com:3306
    username: train__10004
  oa_log:
    dbname: oa_log__10004
    password: "DG&Oti9SvM"
    url: main.tidb.serv.iteng.com:8381
    username: oa_log__10004
office:
  sys:
    xtenant: 10004
rabbitmq:
  master:
    password: VjTWb
    port: 5672
    url: master.rabbitmq.serv.iteng.com
    username: oaAsync__10004
    vhost: oaAsync__10004
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: kjxNx
    port: 5672
    url: master.rabbitmq.serv.iteng.com
    username: oa__10004
    vhost: oa__10004
  oaAsync:
    password: VjTWb
    port: 5672
    url: master.rabbitmq.serv.iteng.com
    username: oaAsync__10004
    vhost: oaAsync__10004
  printer:
    password: yQGMw
    port: 5672
    url: master.rabbitmq.serv.iteng.com
    username: printer__10004
    vhost: printer__10004
redis:
  oa:
    host: ************
    password: google99
    port: 6394
    url: google99@************:6394
sms:
  send:
    email:
      url: http://sms.iteng.com/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://sms.iteng.com/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10004
sqlserver:
  after_write:
    dbname: ch999oanew__10004
    host: shoa.sqlserver.serv.iteng.com
    password: "Z*T4Zt5xb2Y7SF"
    port: 1433
    username: ch999oanew__10004
  ch999oanew:
    dbname: ch999oanew__10004
    host: shoa.sqlserver.serv.iteng.com
    password: "Z*T4Zt5xb2Y7SF"
    port: 1433
    username: ch999oanew__10004
  ch999oanewReport:
    dbname: ch999oanew__10004
    host: shoa.sqlserver.serv.iteng.com
    password: "Z*T4Zt5xb2Y7SF"
    port: 1433
    username: ch999oanew__10004
  ch999oanewHis:
    dbname: ch999oanew__10004
    host: shoa.sqlserver.serv.iteng.com
    password: "Z*T4Zt5xb2Y7SF"
    port: 1433
    username: ch999oanew__10004
  ch999oahis:
    dbname: ch999oanew__10004
    host: shoa.sqlserver.serv.iteng.com
    password: "Z*T4Zt5xb2Y7SF"
    port: 1433
    username: ch999oanew__10004
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10004
    host: shoa.sqlserver.serv.iteng.com
    password: "aPh60HDweCV*ZB"
    port: 1433
    username: office__10004
  oanewWrite:
    dbname: ch999oanew__10004
    host: shoa.sqlserver.serv.iteng.com
    password: "Z*T4Zt5xb2Y7SF"
    port: 1433
    username: ch999oanew__10004
  office:
    dbname: office__10004
    host: shoa.sqlserver.serv.iteng.com
    password: "aPh60HDweCV*ZB"
    port: 1433
    username: office__10004
  officeWrite:
    dbname: office__10004
    host: shoa.sqlserver.serv.iteng.com
    password: "aPh60HDweCV*ZB"
    port: 1433
    username: office__10004
  smallpro_write:
    dbname: ch999oanew__10004
    host: shoa.sqlserver.serv.iteng.com
    password: "Z*T4Zt5xb2Y7SF"
    port: 1433
    username: ch999oanew__10004
  web999:
    dbname: web999__10004
    host: shoa.sqlserver.serv.iteng.com
    password: sCf_CPl53hwKpJ
    port: 1433
    username: web999__10004
  web999_other:
    dbname: web999_other__10004
    host: shoa.sqlserver.serv.iteng.com
    password: sxe33kb!7WO_1Q
    port: 1433
    username: web999_other__10004
  ershou:
    dbname: ershou__10004
    host: oa.sqlserver.serv.iteng.com
    password: wHGw19x92apDug
    port: 1433
    username: ershou__10004
url:
  delImgUrl: http://data3:5083
  oa-push-info: http://inwcf.iteng.com:2988/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://img.iteng.com/
  source:
    path: i18n/url
  uploadImgUrl: http://data3:9333

mqtt:
  host: tcp://iot.9xun.com:1883
  topic: oa2/nc-segments
  clientinid: nc-segments-${random.value}
  qoslevel: 1
  username: client
  password: ch999
  timeout: 10000
  keepalive: 20
  adminUrl: http://************:8081/api/v4
  adminUser: admin
  adminPassword: public

chw:
  tenant: https://manager.saas.ch999.cn/saasManager/api/thirdParty/getAllDomains/v1
  warehousing: https://chw.9xun.com/cloudapi_nc/pick/api/order/oa/warehousing/v1?xservicename=pick-web
  cancelCaigou: https://chw.9xun.com/cloudapi_nc/pick/api/order/oa/cancel/v1?xservicename=pick-web
  platformUrl: https://chw.9xun.com/cloudapi_nc/pick/api/user/partner/skip/v1?xservicename=pick-web&token=%s&xtenant=%d
  messageSend: https://moa.9ji.com/cloudapi_nc/logistics/api/ChwNoticeController/sendMessage/v1
#盘点重构excl 导入文件模板下载

inventory:
  downloadExcl: https://img.9xun.com/newstatic/27122/06e7bfdaba9d0492.xls
purchase:
  downloadExcl: https://img.9xun.com/newstatic/2373/032742dc68215552.xls

#渠道接口调用
channel.approve: /kcApi/SystemGenerateApplySupplierShortlisted

#请求宝尊接口 url
baozun.url: 'https://api-oms2wms.baozun.com/order'

subOutIn:
  url: ''
calcEmployeePirce:
  url: ''