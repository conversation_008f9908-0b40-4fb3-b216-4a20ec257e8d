consul:
  host: 127.0.0.1
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://img.gzchang.net/
  upload:
    url: http://**************:9333
instance-zone: 10003
jiuji:
  sys:
    moa: https://moa.gzchang.net
    pc: https://oa.gzchang.net
    m: https://apple.gzchang.net
    inWcf: http://inwcf.gzchang.net
    oaWcf: http://inwcf2.gzchang.net
    xtenant: 10003
  xtenant: 3000
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10003:of4uFxkADiHgrnnN@************:27017,************:27017,************:27017/ch999oa__10003
  url1: *****************************************************************************************************************
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname:
    password:
    url: :3306
    username:
  oa_core:
    dbname: oa_core__10003
    password: m%BZ7nh0R$f9Pwlt
    url: master.mysql.serv.iteng.com:3306
    username: oa_core__10003
  oa_nc:
    dbname: oa_nc__10003
    password: '^F0ZYzSp7SzR46oP'
    url: master.mysql.serv.iteng.com:3306
    username: oa_nc__10003
  train:
    dbname: train__10003
    password: "%lgQX72CtXrPu*2s"
    url: master.mysql.serv.iteng.com:3306
    username: train__10003
  oa_log:
    dbname: oa_log__10003
    password: "dG@XBV@$Ft"
    url: main.tidb.serv.iteng.com:8381
    username: oa_log__10003
  appleSn:
    dbname: appleSn_9ji
    password: google00
    url: ************:3306
    username: appleSn_9ji
office:
  sys:
    xtenant: 10003
rabbitmq:
  master:
    password: qsdQc
    port: 5672
    url: master.rabbitmq.serv.iteng.com
    username: oaAsync__10003
    vhost: oaAsync__10003
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: scbnr
    port: 5672
    url: master.rabbitmq.serv.iteng.com
    username: oa__10003
    vhost: oa__10003
  oaAsync:
    password: qsdQc
    port: 5672
    url: master.rabbitmq.serv.iteng.com
    username: oaAsync__10003
    vhost: oaAsync__10003
  printer:
    password: UqPQR
    port: 5672
    url: master.rabbitmq.serv.iteng.com
    username: printer__10003
    vhost: printer__10003
redis:
  oa:
    host: ************
    password: google99
    port: 6392
    url: google99@************:6392
sms:
  send:
    email:
      url: http://sms.gzchang.net/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://sms.gzchang.net/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10003
sqlserver:
  after_write:
    dbname: ch999oanew__10003
    host: web.sqlserver.serv.iteng.com
    password: "Z*T4ZtxbY7SF"
    port: 1433
    username: ch999oanew__10003
  ch999oanew:
    dbname: ch999oanew__10003
    host: web.sqlserver.serv.iteng.com
    password: "Z*T4ZtxbY7SF"
    port: 1433
    username: ch999oanew__10003
  ch999oanewReport:
    dbname: ch999oanew__10003
    host: web.sqlserver.serv.iteng.com
    password: "Z*T4ZtxbY7SF"
    port: 1433
    username: ch999oanew__10003
  ch999oanewHis:
    dbname: ch999oanew__10003
    host: web.sqlserver.serv.iteng.com
    password: "Z*T4ZtxbY7SF"
    port: 1433
    username: ch999oanew__10003
  ch999oahis:
    dbname: ch999oanew__10003
    host: web.sqlserver.serv.iteng.com
    password: "Z*T4ZtxbY7SF"
    port: 1433
    username: ch999oanew__10003
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10003
    host: web.sqlserver.serv.iteng.com
    password: "aPhHDweCV*ZB"
    port: 1433
    username: office__10003
  oanewWrite:
    dbname: ch999oanew__10003
    host: web.sqlserver.serv.iteng.com
    password: "Z*T4ZtxbY7SF"
    port: 1433
    username: ch999oanew__10003
  office:
    dbname: office__10003
    host: web.sqlserver.serv.iteng.com
    password: "aPhHDweCV*ZB"
    port: 1433
    username: office__10003
  officeWrite:
    dbname: office__10003
    host: web.sqlserver.serv.iteng.com
    password: "aPhHDweCV*ZB"
    port: 1433
    username: office__10003
  smallpro_write:
    dbname: ch999oanew__10003
    host: web.sqlserver.serv.iteng.com
    password: "Z*T4ZtxbY7SF"
    port: 1433
    username: ch999oanew__10003
  web999:
    dbname: web999__10003
    host: web.sqlserver.serv.iteng.com
    password: sCf_CPlhwKpJ
    port: 1433
    username: web999__10003
  web999_other:
    dbname: web999_other__10003
    host: web.sqlserver.serv.iteng.com
    password: sxekb!7WO_1Q
    port: 1433
    username: web999_other__10003
  ershou:
    dbname: ershou__10003
    host: web.sqlserver.serv.iteng.com
    password: "wHGw19xapDug"
    port: 1433
    username: ershou__10003

url:
  delImgUrl: http://data3:5083
  oa-push-info: http://inwcf.gzchang.net:2988/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://img.gzchang.net/
  source:
    path: i18n/url
  uploadImgUrl: http://data3:9333

mqtt:
  host: tcp://iot.9xun.com:1883
  topic: oa2/nc-segments
  clientinid: nc-segments-${random.value}
  qoslevel: 1
  username: client
  password: ch999
  timeout: 10000
  keepalive: 20
  adminUrl: http://************:8081/api/v4
  adminUser: admin
  adminPassword: public
  logistics:
    host: tcp://iot.9xun.com:1883
    topic: oa2/nc-segments
    clientinid: nc-segments-${random.value}
    qoslevel: 1
    username: client
    password: ch999
    timeout: 10000
    keepalive: 20
    adminUrl: http://************:8081/api/v4
    adminUser: admin
    adminPassword: public

chw:
  tenant: https://manager.saas.ch999.cn/saasManager/api/thirdParty/getAllDomains/v1
  warehousing: https://chw.9xun.com/cloudapi_nc/pick/api/order/oa/warehousing/v1?xservicename=pick-web
  cancelCaigou: https://chw.9xun.com/cloudapi_nc/pick/api/order/oa/cancel/v1?xservicename=pick-web
  platformUrl: https://chw.9xun.com/cloudapi_nc/pick/api/user/partner/skip/v1?xservicename=pick-web&token=%s&xtenant=%d
  messageSend: https://moa.9ji.com/cloudapi_nc/logistics/api/ChwNoticeController/sendMessage/v1
  messageChannelSend: https://moa.9ji.com/cloudapi_nc/logistics/api/ChwNoticeController/channelErrorMonitor/v1
  purchaseRemark: '%s:<a href=https://chw.9xun.com/mall/admin/trads/order-detail/%s>%s</a>'
#盘点重构excl 导入文件模板下载

inventory:
  downloadExcl: https://img.9xun.com/newstatic/2370/03805dac15b8cd57.xls
purchase:
  downloadExcl: https://img.9xun.com/newstatic/2373/032742dc68215552.xls

#渠道接口调用
channel.approve: /kcApi/SystemGenerateApplySupplierShortlisted

web:
  synchronousBarcodeUrl: https://www.9ji.com/web/api/products/updateBarCode

subOutIn:
  url: ''
calcEmployeePirce:
  url: ''

#请求宝尊接口 url
baozun.url: 'https://api-oms2wms.baozun.com/order'

apollo:
  url:

#太力
tlmall:
  url: https://www.tlmall.com/page/ka/kaPrimary.html
  saasCode: jiuxunyun
  custEncType: AES
  custEncKey: xBmyu0A5kgezTju7
  signType: RSA
  custSignPubkey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQChQ1Nva1rmkA/SoxomqzXUk4s2vxzjKOjEGgaKqP8c1B2ux/hgVVFkN/xlUQpKkDNGKbtbOIUMI/aKjlspCVbkS7rZSm7r27xczm/dVQ0e9c648FEE9P11Jy1r4Kd0D4Pjq/JfBy6LoHe4JpQVg83YH87Mme2p/E8MY6fnhQKRLQIDAQAB
  custSignPrikey: MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAKFDU29rWuaQD9KjGiarNdSTiza/HOMo6MQaBoqo/xzUHa7H+GBVUWQ3/GVRCkqQM0Ypu1s4hQwj9oqOWykJVuRLutlKbuvbvFzOb91VDR71zrjwUQT0/XUnLWvgp3QPg+Or8l8HLougd7gmlBWDzdgfzsyZ7an8Twxjp+eFApEtAgMBAAECgYAD3t7QzM3YvNp0Xs/Q38kh+gycWsfxt9imZE2F5HqDEaBIwqn2ffW/JwzazbAmjAF/DJ9fmCKxYOeY+cO8X2oDdQDgj7wtxDTLCysdXFqe3J2bCUN473KZaVEaQMwNX4G+g69qviv37yviJbqh2tbJK517bMdY6JNo67LicIVl7QJBAMnBvMFCuYlgStnq5CNlO7OKeepaY21YdapfKlQTno7bJk9jdIBQZrqjhiBDBuxECX0Gdpss8cJOE5J8Kd+5uIsCQQDMnohWRnrYC8unrWk/+wQYXV2kEfCjOXITGYPNoFZ4LV/gEYgh2WhAgh6O5Yykx3yL5FJfTGgq+rTvJdwDYNwnAkEAuqtB1R3DRFOPbahihE05u5g3zJjsvVLHK2b5ZujwHwSsoW9HbyD0q2J4yoi5cwhQLxk3y8L9u+U5PqMaqyDOmQJBAKjMX0xM+CoiEO9SbvEI8mfnHcirxAfi6+g1tDV9f9fEFsORsuu5nfcZYHwhgdStfGErCYj0Tzqld32Rjd57mSECQQC4eVOkmUNgui4mflQNmI8gLnHo8j5c9/lnnxWD1CJ+krW570dm9VLJIa0lzC+x0IjpVAqaWfVIMua1jvJwV83h
  ipList: *************
