consul:
  host: 127.0.0.1
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://test01.img.saas.ch999.cn/
  upload:
    url: http://**************:9333
instance-zone: 10050
jiuji:
  sys:
    moa: https://test01.moa.saas.ch999.cn
    pc: https://test01.oa.saas.ch999.cn
    m: https://test01.m.saas.ch999.cn
    inWcf: http://test01.inwcf.saas.ch999.cn
    xtenant: 10050
  xtenant: 50000
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10050:Y4MQUAQR@**************:27017,**************:27017,**************:27017/ch999oa__10050
  url1: ***************************************************************************************************************
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname:
    password:
    url: :3306
    username:
  appleSn:
    url: ************:3306
    dbname: appleSn_9ji
    username: appleSn_9ji
    password: google00
  oa_core:
    dbname: oa_core__10050
    password: oa_coreMtd
    url: mysql.serv.xn.saas.ch999.cn:3306
    username: oa_core__10050
  oa_nc:
    dbname: oa_nc__10050
    password: 'oa_ncHsi'
    url: mysql.serv.xn.saas.ch999.cn:3306
    username: oa_nc__10050
  train:
    dbname: train__10050
    password: "trainXsO"
    url: mysql.serv.xn.saas.ch999.cn:3306
    username: train__10050
  oa_log:
    dbname: oa_log__10050
    password: "012qAUX6m5#l"
    url: main.tidb.ch999.cn:9383
    username: oa_log__10050
office:
  sys:
    xtenant: 10050
rabbitmq:
  master:
    password: ZHESa
    port: 5672
    url: rabbitmq.serv.xn.saas.ch999.cn
    username: oaAsync__10050
    vhost: oaAsync__10050
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: FunWO
    port: 5672
    url: rabbitmq.serv.xn.saas.ch999.cn
    username: oa__10050
    vhost: oa__10050
  oaAsync:
    password: ZHESa
    port: 5672
    url: rabbitmq.serv.xn.saas.ch999.cn
    username: oaAsync__10050
    vhost: oaAsync__10050
  printer:
    password: NkQHX
    port: 5672
    url: rabbitmq.serv.xn.saas.ch999.cn
    username: printer__10050
    vhost: printer__10050
redis:
  oa:
    host: **************
    password: google99
    port: 6387
    url: google99@**************:6387
sms:
  send:
    email:
      url: http://test01.sms.saas.ch999.cn/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://test01.sms.saas.ch999.cn/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10050
sqlserver:
  after_write:
    dbname: ch999oanew__10050
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "ch999oanewbEdGR"
    port: 1433
    username: ch999oanew__10050
  ch999oanew:
    dbname: ch999oanew__10050
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "ch999oanewbEdGR"
    port: 1433
    username: ch999oanew__10050
  ch999oanewReport:
    dbname: ch999oanew__10050
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "ch999oanewbEdGR"
    port: 1433
    username: ch999oanew__10050
  ch999oanewHis:
    dbname: ch999oanew__10050
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "ch999oanewbEdGR"
    port: 1433
    username: ch999oanew__10050
  ch999oahis:
    dbname: ch999oanew__10050
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "ch999oanewbEdGR"
    port: 1433
    username: ch999oanew__10050
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10050
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "officeFYnpU"
    port: 1433
    username: office__10050
  oanewWrite:
    dbname: ch999oanew__10050
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "ch999oanewbEdGR"
    port: 1433
    username: ch999oanew__10050
  office:
    dbname: office__10050
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "officeFYnpU"
    port: 1433
    username: office__10050
  officeWrite:
    dbname: office__10050
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "officeFYnpU"
    port: 1433
    username: office__10050
  smallpro_write:
    dbname: ch999oanew__10050
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "ch999oanewbEdGR"
    port: 1433
    username: ch999oanew__10050
  web999:
    dbname: web999__10050
    host: sqlserver.serv.xn.saas.ch999.cn
    password: web999izklc
    port: 1433
    username: web999__10050
  web999_other:
    dbname: web999_other__10050
    host: sqlserver.serv.xn.saas.ch999.cn
    password: web999_othervHhoo
    port: 1433
    username: web999_other__10050
  ershou:
    dbname: ershou__10050
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "ershounCYDV"
    port: 1433
    username: ershou__10050

url:
  delImgUrl: http://data3:5083
  oa-push-info: http://test01.inwcf.saas.ch999.cn:2988/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://test01.img.saas.ch999.cn/
  source:
    path: i18n/url
  uploadImgUrl: http://data3:9333

mqtt:
  host: tcp://iot.9xun.com:1883
  topic: oa2/nc-segments
  clientinid: nc-segments-${random.value}
  qoslevel: 1
  username: client
  password: ch999
  timeout: 10000
  keepalive: 20
  adminUrl: http://************:8081/api/v4
  adminUser: admin
  adminPassword: public
  logistics:
    host: tcp://iot.9xun.com:1883
    topic: oa2/nc-segments
    clientinid: nc-segments-${random.value}
    qoslevel: 1
    username: client
    password: ch999
    timeout: 10000
    keepalive: 20
    adminUrl: http://************:8081/api/v4
    adminUser: admin
    adminPassword: public

chw:
  tenant: https://manager.saas.ch999.cn/saasManager/api/thirdParty/getAllDomains/v1
  warehousing: https://chw.9xun.com/cloudapi_nc/pick/api/order/oa/warehousing/v1?xservicename=pick-web
  cancelCaigou: https://chw.9xun.com/cloudapi_nc/pick/api/order/oa/cancel/v1?xservicename=pick-web
  platformUrl: https://chw.9xun.com/cloudapi_nc/pick/api/user/partner/skip/v1?xservicename=pick-web&token=%s&xtenant=%d
  messageSend: https://moa.9ji.com/cloudapi_nc/logistics/api/ChwNoticeController/sendMessage/v1
  messageChannelSend: https://moa.9ji.com/cloudapi_nc/logistics/api/ChwNoticeController/channelErrorMonitor/v1
  purchaseRemark: '%s:<a href=https://chw.9xun.com/mall/admin/trads/order-detail/%s>%s</a>'
#盘点重构excl 导入文件模板下载

inventory:
  downloadExcl: https://img.9xun.com/newstatic/2370/03805dac15b8cd57.xls
purchase:
  downloadExcl: https://img.9xun.com/newstatic/2373/032742dc68215552.xls

#渠道接口调用
channel.approve: /kcApi/SystemGenerateApplySupplierShortlisted

web:
  synchronousBarcodeUrl: https://www.9ji.com/web/api/products/updateBarCode

subOutIn:
  url: ''
calcEmployeePirce:
  url: ''

#请求宝尊接口 url
baozun.url: 'https://dss-api-oms2wms-uat.baozun.com/order'

springdoc:
  swagger-ui:
    enabled: true

#太力
tlmall:
  url: https://vr-dev.tlmall.com/page/ka/kaPrimary.html
  saasCode: jiuxunyun
  custEncType: AES
  custEncKey: GSA9UPS709ACC3D9
  signType: RSA
  custSignPubkey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCacoVWr80kBUeW0U3qcDSzeoI+0cFI+agj6itEUYX0XO36YpJqkv6mmTccNPnfEfOkRPHFX65c0TFQCRWPWqaMU+SGiaQ6osHiej/LCttjdNlK3vVw19mvIWSFLD/qW10WQ1y6egrbspvu2xn5ozU2fhSjo87euXCBo6PDt3DDMwIDAQAB
  custSignPrikey: MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAJpyhVavzSQFR5bRTepwNLN6gj7RwUj5qCPqK0RRhfRc7fpikmqS/qaZNxw0+d8R86RE8cVfrlzRMVAJFY9apoxT5IaJpDqiweJ6P8sK22N02Ure9XDX2a8hZIUsP+pbXRZDXLp6Ctuym+7bGfmjNTZ+FKOjzt65cIGjo8O3cMMzAgMBAAECgYAArVkxpMUCJWvHmNSonyP8Ast+DwQJQome2VtQighLpqSnIQuB9XGxtUuX5LuRDiPCp/K6FM36O4iQe0RhmS97EukQsErLgsa5z9upSGTeFtMvvoQR0ch/M6eUNz/+L7h7LPX2WQvmcb2ZySJP/AuKA1+qhP+gQEIfae/rsP8RcQJBAPL3pFFAUwdyNb8zWuecro5LuaXqvqRo/zpOLCnd4v/wYOamH2XblOgZZPKsv19z8bIngXEssrWU9rOAF0u2L4UCQQCiu1j+DCxLgjtbS5Hxz8xW3T7tLgkNJPg7VDxasrdtPiynIiLK59wyagOcT7h2EpcjhnjrOk1hXxoH5aNJyjlXAkAoKVkm+RwAYheSSFnhaRvNd6TzUQ88PALcT2mrbfsE1WYfGhyciKRfjTsLhhSIIbE1AqS54WqU60+wMh8nq7DFAkEAipYs2uZ5DrFRZZW9LAeBCXUQcyNid/xFHxnE8KUFC5cIpimvIchGQfUBMCzAdmWsq4eJESMs5UD9xT3vSxdarwJBALKN9g+EL0qAcrjftWj/F8cSV7lDDeS4VcqiZ6df+VRcD+pR2x7a/vqUC9YFxo+6iDV+BcFQ/ooIKhwKNkABg+Y=
  ipList: *************

ptac:
  tokenurl: https://anygw.ch999.cn/ptac/oauth2/oauth/token
  apiurl: https://anygw.ch999.cn/ptac/api/aifgw/http