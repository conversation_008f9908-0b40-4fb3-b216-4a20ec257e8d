package com.jiuji.oa.apollo;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.jiuji.oa.nc.common.enums.XtenantEnum;

import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023/3/20 16:12
 */
public interface ApolloKeys {

    /**
     * 加急送的ppid
     */
    String PAY_MONEY_DELIVERY_PPID = "pay_money_delivery.ppid";

    /**
     * 获取apollo的配置
     * @param key
     * @param defaultValue
     * @return
     */
    static String getApolloProperty(String key, String defaultValue){
        return Optional.ofNullable(SpringUtil.getApplicationContext().getEnvironment())
                .map(env -> env.getProperty(key,defaultValue)).orElse(defaultValue);
    }

    static String getApolloWithMainTenant(String key, String defaultValue){
        String tenantName = XtenantEnum.getTenantName();
        return getApolloProperty(StrUtil.format("{}.{}", key, tenantName), getApolloProperty(key, defaultValue));
    }
}
