package com.jiuji.oa.apollo;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;

/**
 * <AUTHOR>
 */
@Data
public class WuliuApolloConfig {


    /**
     * 物流工具查询待办开关
     */
    @Value("${appMenu.pendFlag:true}")
    private Boolean pendFlag;


    /**
     * 物流工具查询待办开关
     */
    @Value("${NationalSupplement.closeSysUpdate:true}")
    private Boolean closeSysUpdate;


    /**
     * 导出费用报表v3开关
     */
    @Value("${wuliu.consumerecord.switch:false}")
    private Boolean wuliuConsumerecordSwitch;

    /**
     * 资产门店
     * GC_zcc2、GC_zcc、GCZX
     */
    @Value("${wuliu.zc.areaIds:271,763,953}")
    private String zcAreaIds;

    /**
     * 资产返仓签收提醒推送msg
     */
    @Value("${wuliu.zc.pushMsg:您有一个从%s发往%s的物流单:%s,已到达DC,请及时到一楼九机快送处取货}")
    private String zcPushMsg;

    /**
     * m版物流单url
     */
    @Value("${wuliu.murl:/staticnew/#/logistics/logistics-bill/%s}")
    private String wuliuMurl;

    /**
     * 资产门店
     * GCZX
     */
    @Value("${wuliu.zc.gczxAreaId:953}")
    private Integer gczxAreaId;

    /**
     * GCZX签收提醒推送消息
     */
    @Value("${wuliu.zc.gczxPushMsg:您好！您有从%s寄回的物品，物流单[%s]已送达行政组（GCZX），请及时到行政组取货，谢谢！}")
    private String gczxPushMsg;

    /**
     * 设置质保金查询开关
     */
    @Value("${appMenu.assuranceDepositFlag:false}")
    private Boolean assuranceDepositFlag;

    /**
     * 美团订单收货码短信通道id
     */
    @Value("${sms.meituan.channelid:81}")
    private Integer smsMeituanChannelid;

    /**
     * 物流未投妥短信通道id
     */
    @Value("${sms.meituan.channelid:9}")
    private Integer smsWuliuChannelid;

    /**
     * 价保返利开关
     */
    @Value("${stock.price_cashback.switch:true}")
    private Boolean priceCashbackSwitch;
    /**
     * 价保返利库存数量限制
     */
    @Value("${stock.price_cashback.count:30000}")
    private Integer priceCashbackCount;


    @Value("${wuliu.pushSecond:86400}")
    private Integer  pushSecond;

    @Value("${sms.accountingSendCountMax:200}")
    private Integer accountingSendCountMax;


    @Value("${wuliu.paotui.ch999_ids: 21242,432}")
    private String  wuliuPaoTuiCh999Ids;

    /**
     * 导出费用报表v3开关
     */
    @Value("${stock.accounting.records.switch:true}")
    private Boolean accountingRecordsSwitch;

    /**
     * 西南 太力ip白名单
     */
    @Value("${stock.tl.xn.iplist:*************}")
    private String tlXnIplist;
    /**
     *  华东太力ip白名单
     */
    @Value("${stock.tl.xn.iplist:**************|*************|**************|***************|***************|*************|************|*************}")
    private String tlHdIplist;
    /**
     * 华北 太力ip白名单
     */
    @Value("${stock.tl.xn.iplist:************|************|************|***********|************|***********|*************|************}")
    private String tlHbIplist;
    /**
     * 华南 太力ip白名单
     */
    @Value("${stock.tl.xn.iplist:************|************|************|*************}")
    private String tlHnIplist;

    /**
     * 其他ip白名单
     */
    @Value("${stock.tl.xn.iplist:*************|************|************}")
    private String tlItIplist;
}
