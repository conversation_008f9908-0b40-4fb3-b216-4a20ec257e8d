package com.jiuji.oa.orderdynamics.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("wuliu_route_track")
public class WuliuRouteTrack extends BaseEntity {

    /**
     * 订单id
     */
    @TableField(value = "sub_id")
    private Long subId;

    /**
     * 商品图片
     */
    @TableField(value = "image")
    private String image;

    /**
     * 订单类型（1-新机单，2-良品单）
     */
    @TableField(value = "sub_type")
    private Integer subType;

    /**
     * 配送状态（1-运输中，2-配送中）
     */
    @TableField(value = "dispatch_status")
    private Integer dispatchStatus;

    /**
     * 线路类型（1-调拨，2-配送）
     */
    @TableField(value = "line_type")
    private Integer lineType;

    /**
     * 物流id
     */
    @TableField(value = "wuliu_id")
    private Long wuliuId;

    /**
     * 状态
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 当前纬度
     */
    @TableField(value = "current_latitude")
    private String currentLatitude;

    /**
     * 当前经度
     */
    @TableField(value = "current_longitude")
    private String currentLongitude;

    /**
     *寄件的纬度
     */
    @TableField(value = "send_latitude")
    private String sendLatitude;

    /**
     *寄件的经度
     */
    @TableField(value = "send_longitude")
    private String sendLongitude;

    /**
     * 收件的纬度
     */
    @TableField(value = "receiver_latitude")
    private String receiverLatitude;

    /**
     * 收件的经度
     */
    @TableField(value = "receiver_longitude")
    private String receiverLongitude;

    /**
     * 路由途径点
     */
    @TableField(value = "waypoints")
    private String waypoints;

    /**
     * basket_id
     */
    @TableField(value = "basket_id")
    private Long basketId;
}
