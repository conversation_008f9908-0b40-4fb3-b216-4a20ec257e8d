package com.jiuji.oa.orderdynamics.util;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.jiuji.oa.baozun.common.util.StringUtils;
import com.jiuji.oa.nc.common.config.rabbitmq.RabbitMqConfig;
import com.jiuji.oa.nc.dict.service.ISysConfigService;
import com.jiuji.oa.orderdynamics.dto.MqInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class JournalPush {


    @Resource(name = "oaAsyncRabbitTemplate")
    private RabbitTemplate rabbitTemplate;
    @Resource
    private ISysConfigService sysConfigService;

    private static final String ACT="orderDynamicsAsync";


    /**
     * 订单日志推送
     * @param mqInfo
     */
    public void orderLogPush(MqInfo mqInfo){
        //设置默认AC以及Xtenant
        if(StringUtils.isEmpty(mqInfo.getAct())){
            mqInfo.setAct(ACT);
        }
        if(ObjectUtil.isEmpty(mqInfo.getXtenant())){
            Long xtenantId = sysConfigService.getXtenantId();
            mqInfo.setXtenant(xtenantId);
        }
        log.warn("订单动态日志MQ推送：{}",JSONUtil.toJsonStr(mqInfo));
        rabbitTemplate.convertAndSend(RabbitMqConfig.QUEUE_ORDER_DYNAMICS, JSONUtil.toJsonStr(mqInfo));
    }
}
