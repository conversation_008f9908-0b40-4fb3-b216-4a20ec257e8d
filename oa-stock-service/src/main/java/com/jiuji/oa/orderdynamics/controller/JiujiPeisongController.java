package com.jiuji.oa.orderdynamics.controller;

import com.jiuji.oa.meituan.vo.req.LngLatReportReqVO;
import com.jiuji.oa.meituan.vo.res.HasSendingResVO;
import com.jiuji.oa.meituan.vo.res.LngLatReportResVO;
import com.jiuji.oa.orderdynamics.service.IJiujiPeisongBusService;
import com.jiuji.tc.common.vo.R;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 九机快送配送
 * <AUTHOR>
 * @date 2022/10/12 16:01
 */
@RestController
@RequestMapping("/api/jiuji-peisong")
public class JiujiPeisongController {
    @Resource
    private IJiujiPeisongBusService jiujiPeisongBusService;

    /**
     * 配送经纬度上报
     * @param reqVo
     * @return
     */
    @PostMapping("/syncLngLatReport/v1")
    public R<LngLatReportResVO> lngLatReport (@RequestBody LngLatReportReqVO reqVo) {
        return R.success(jiujiPeisongBusService.lngLatReport(reqVo));
    }

    /**
     * 是否有正在派送的查询接口
     * @return
     */
    @GetMapping("/getHasSending/v1")
    public R<HasSendingResVO> checkLngLatReport () {
        return R.success(jiujiPeisongBusService.queryHasSending());
    }


    /**
     * 定时查询处理九机订单
     * @return
     */
    @GetMapping("/handleJiujiPeisong/v1")
    public R<Boolean> handleJiujiPeisong () {
        return R.success(jiujiPeisongBusService.handleJiujiPeisong());
    }

}
