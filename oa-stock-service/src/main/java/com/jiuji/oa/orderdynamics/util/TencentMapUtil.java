package com.jiuji.oa.orderdynamics.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.http.HttpUtil;
import com.jiuji.oa.nc.common.exception.CustomizeException;
import com.jiuji.oa.orderdynamics.enums.PlanWayEnum;
import com.jiuji.oa.orderdynamics.vo.response.LongitudeAndLatitude;
import com.jiuji.oa.stock.common.util.JacksonJsonUtils;
import com.jiuji.oa.wuliu.vo.tencentmap.TencentMapDrivingResVO;
import com.jiuji.tc.utils.enums.order.DeliveryTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 腾讯地图工具
 *
 * <AUTHOR>
 * @date 2022/10/9 20:19
 */
@Slf4j
public class TencentMapUtil {
    public static final Integer BICYCLING_RATE_LIMIT = 120;
    private static final Integer SUCCESS_CODE=0;
    private static final String KEY = "3XTBZ-Y2TKR-OAVWG-WRAB2-XWWQH-O6BYJ";
    private static final String TENCENT_DRIVING_URL = "https://apis.map.qq.com/ws/direction/v1/driving/?from=%s&to=%s&waypoints=%s&output=json&callback=cb&key=%s";
    private static final String TENCENT_BICYCLING_URL = "https://apis.map.qq.com/ws/direction/v1/bicycling/?from=%s&to=%s&output=json&callback=cb&key=%s";


    public static BranchHandle isTureOrFalse(boolean b){
        return (trueHandle, falseHandle) -> {
            if (b){
                trueHandle.run();
            } else {
                falseHandle.run();
            }
        };
    }
    /**
     * 驾车
     * https://lbs.qq.com/service/webService/webServiceGuide/webServiceRoute
     *
     * @param from      起点位置坐标 格式：lat,lng
     * @param to        终点位置坐标，格式：lat,lng
     * @param waypoints 途经点，格式：lat1,lng1;lat2,lng2;… 最大支持30个，超过30个之后的将被忽略
     * @return
     */
    public static TencentMapDrivingResVO tencentMapDriving(String from, String to, String waypoints) {
        try {
            String result = HttpUtil.get(String.format(TENCENT_DRIVING_URL, from, to, waypoints, KEY));
            log.warn("调用腾讯地图路径规划参数以及返回结果，from={}，to={},waypoints={},result={}", from, to, waypoints, result);
            if (StringUtils.isBlank(result)) {
                return null;
            }
            return JacksonJsonUtils.toClass(result, TencentMapDrivingResVO.class);
        } catch (Exception e) {
            log.error("调用腾讯地图路径规划异常，from={}，to={},waypoints={}", from, to, waypoints, e);
            return null;
        }
    }

    /**
     * 解析Polyline
     * @param tencentMapDrivingResVO
     * @return
     */
    public static List<LongitudeAndLatitude> analysisPolyline(TencentMapDrivingResVO tencentMapDrivingResVO) {
        List<LongitudeAndLatitude> longitudeAndLatitudesList = new ArrayList<>();
        if(tencentMapDrivingResVO!=null){
            Integer status = tencentMapDrivingResVO.getStatus();
            if(!SUCCESS_CODE.equals(status)){
                throw new CustomizeException(tencentMapDrivingResVO.getMessage());
            }
        }
        TencentMapDrivingResVO.ResultData result = tencentMapDrivingResVO.getResult();
        if(result==null){
            return longitudeAndLatitudesList;
        }
        List<TencentMapDrivingResVO.ResultData.RouteData> routes = result.getRoutes();
        if(CollectionUtil.isEmpty(routes)){
            return longitudeAndLatitudesList;
        }
        List<Double> polyline = routes.get(0).getPolyline();
        if(CollectionUtil.isEmpty(polyline)){
            return longitudeAndLatitudesList;
        }
        //polyline 坐标解压
        for (int i = 2; i < polyline.size(); i++) {
            polyline.set(i,polyline.get(i-2)+polyline.get(i)/1000000);
        }
        //进行数据的封装
        for (int i = 0; i < polyline.size(); i++) {
            if(i%2==1){
                LongitudeAndLatitude longitudeAndLatitude = new LongitudeAndLatitude(polyline.get(i).toString(),polyline.get(i-1).toString());
                longitudeAndLatitudesList.add(longitudeAndLatitude);
            }
        }
        return longitudeAndLatitudesList;
    }


    /**
     * https://lbs.qq.com/service/webService/webServiceGuide/webServiceRoute
     *
     * @param from 起点位置坐标 格式：lat,lng
     * @param to   终点位置坐标，格式：lat,lng
     * @return
     */
    public static TencentMapDrivingResVO tencentMapBicycling(String from, String to) {
        try {
            String result = HttpUtil.get(String.format(TENCENT_BICYCLING_URL, from, to, KEY));
            if (StringUtils.isBlank(result)) {
                return null;
            }
            //log.info("调用腾讯地图路径规划，from={}，to={}，result={}", from, to, result)
            return JacksonJsonUtils.toClass(result, TencentMapDrivingResVO.class);
        } catch (Exception e) {
            log.error("调用腾讯地图路径规划异常，from={}，to={}", from, to);
            return null;
        }
    }

    /**
     * 判断当前经纬度是否等于收件经纬度
     *
     * @param currentLatitude
     * @param currentLongitude
     * @param receiverLatitude
     * @param receiverLongitude
     * @return
     */
    public static Boolean currentEqreceiver(String currentLatitude, String currentLongitude, String receiverLatitude, String receiverLongitude) {
        if (StringUtils.isNotEmpty(currentLatitude) && StringUtils.isNotEmpty(currentLongitude)
                && StringUtils.isNotEmpty(receiverLatitude) && StringUtils.isNotEmpty(receiverLongitude)) {
            if (currentLatitude.equals(receiverLatitude) && currentLongitude.equals(receiverLongitude)) {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }


    /**
     * 路线规划
     * @param delivery
     * @return
     */
    public static Integer getPlanWayEnum(Integer delivery){
        //快递运输--显示货车
        if(DeliveryTypeEnum.TO_HOME.getCode().equals(delivery)){
            return PlanWayEnum.DRIVE.getCode();
        }
        //九机快递/加急配送/第三方派送 -- 显示摩托车
        List<Integer> list = Arrays.asList(DeliveryTypeEnum.JIUJI_FAST.getCode(), DeliveryTypeEnum.EMERGENCY.getCode(), DeliveryTypeEnum.THIRD_PARTY.getCode());
        if(list.contains(delivery)){
            return PlanWayEnum.CYCLING.getCode();
        }
        //到店自取--显示客户端规划
        if(DeliveryTypeEnum.TO_STORE.getCode().equals(delivery)){
            return PlanWayEnum.CLIENT_PLAN.getCode();
        }
        //默认显示火车
        return PlanWayEnum.DRIVE.getCode();
    }
}
