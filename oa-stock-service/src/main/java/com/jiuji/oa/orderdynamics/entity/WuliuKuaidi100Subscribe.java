package com.jiuji.oa.orderdynamics.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "wuliu_kuaidi100_subscribe")
public class WuliuKuaidi100Subscribe extends BaseEntity {

    @TableField(value = "xtenant")
    private Long xtenant;

    /**
     * 物流id
     */
    @TableField(value = "wuliu_id")
    private Long wuliuId;

    /**
     * 订单id
     */
    @TableField(value = "sub_id")
    private Long subId;

    /**
     * 快递公司
     */
    @TableField(value = "com")
    private String com;

    /**
     * 快递单号
     */
    @TableField(value = "nu")
    private String nu;

    /**
     * 寄件地址
     */
    @TableField(value = "send_address")
    private String sendAddress;

    /**
     * 收件地址
     */
    @TableField(value = "receiver_address")
    private String receiverAddress;

    /**
     * 寄件或收件号码
     */
    @TableField(value = "phone")
    private String phone;

    /**
     * 快递100订阅结果
     */
    @TableField(value = "result")
    private Integer result;

    /**
     * 快递100返回码
     */
    @TableField(value = "return_code")
    private String returnCode;


}
