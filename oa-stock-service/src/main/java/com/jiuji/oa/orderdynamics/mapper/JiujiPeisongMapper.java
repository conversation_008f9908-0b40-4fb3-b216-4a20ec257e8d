package com.jiuji.oa.orderdynamics.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.jiuji.oa.wuliu.entity.WuLiuEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 九机配送订单
 */
@Mapper
public interface JiujiPeisongMapper {


    /**
     * 派送人查询正在派送的九机配送订单
     * @param paijianren
     * @return
     */
    @DS("ch999oanew")
    List<Long> selectJiujiPeisongWuLiuidByPaijianren(@Param("paijianren") String paijianren);

    @DS("ch999oanew")
    List<String> selectJiujiPeisongPaijianrenByPaijianren();

    /**
     * 九机快送查询需要派送的物流单
     * @param paijianren
     * @return
     */
    List<WuLiuEntity> selectWuLiuByPaijianren(@Param("paijianren") String paijianren);

    /**
     * 查询物流单派件人
     * @param wuliuId
     * @return
     */
    String selectPaijianrenIdByWuliuId(@Param("wuliuId") Integer wuliuId);
}




