package com.jiuji.oa.orderdynamics.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.orderdynamics.entity.WuliuRouteTrack;
import com.jiuji.oa.orderdynamics.mapper.WuliuRouteTrackMapper;
import com.jiuji.oa.orderdynamics.service.WuliuRouteTrackService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/10 20:02
 */

@DS("oa_log")
@Service
public class WuliuRouteTrackServiceImpl extends ServiceImpl<WuliuRouteTrackMapper, WuliuRouteTrack> implements WuliuRouteTrackService {
    /**
     * 查询订单查询物流路由轨迹
     *
     * @param subIdList
     * @return
     */
    @Override
    public List<WuliuRouteTrack> getWuliuRouteTrackBySubIds(List<Long> subIdList) {
        return this.lambdaQuery()
                .in(WuliuRouteTrack::getSubId, subIdList)
                .list();
    }

    /**
     * 查询物流单查询物流路由轨迹
     *
     * @param wuLiuIdList
     * @return
     */
    @Override
    public List<WuliuRouteTrack> getWuliuRouteTrackByWuliuIds(List<Long> wuLiuIdList) {
        return this.lambdaQuery()
                .in(WuliuRouteTrack::getWuliuId, wuLiuIdList)
                .list();
    }

    /**
     * 根据订单id和 订单类型查询
     * @param subId
     * @param subType
     * @return
     */
    @Override
    public List<WuliuRouteTrack> getWuliuRouteTrackList(Long subId,Integer subType){
        return this.lambdaQuery().eq(WuliuRouteTrack::getSubId, subId).eq(WuliuRouteTrack::getSubType, subType).list();
    }

    /**
     * 完成订单动态线路
     */
    @Override
    public boolean completeWuliuRouteTrack(WuliuRouteTrack wuliuRouteTrack){
        return this.lambdaUpdate().eq(WuliuRouteTrack::getId, wuliuRouteTrack.getId())
                .set(WuliuRouteTrack::getCurrentLongitude, wuliuRouteTrack.getReceiverLongitude())
                .set(WuliuRouteTrack::getCurrentLatitude, wuliuRouteTrack.getReceiverLatitude())
                .set(WuliuRouteTrack::getStatus, 1)
                .update();
    }
}
