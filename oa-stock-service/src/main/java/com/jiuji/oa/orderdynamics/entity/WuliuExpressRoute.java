package com.jiuji.oa.orderdynamics.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "wuliu_express_route")
public class WuliuExpressRoute extends BaseEntity {

    /**
     * 快递单号
     */
    @TableField(value = "nu")
    private String nu;

    /**
     * 快递路由操作码
     */
    @TableField(value = "opcode")
    private String opcode;

    /**
     * 快递路由详情
     */
    @TableField(value = "context")
    private String context;

    /**
     * 快递路由城市
     */
    @TableField(value = "city")
    private String city;

    /**
     * 快递路由当前位置
     */
    @TableField(value = "location")
    private String location;

    /**
     * 快递路由时间
     */
    @TableField(value = "optime")
    private LocalDateTime optime;
}
