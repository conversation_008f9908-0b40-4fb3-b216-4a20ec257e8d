package com.jiuji.oa.orderdynamics.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum PointSubTypeEnum implements CodeMessageEnumInterface {

    ZERO_TYPE(0, "默认"),
    ONE_TYPE(1, "白色底 文本"),
    TWO_TYPE(2, "图片"),
    THREE_TYPE(3, "渐变底 文本"),
    FOUR_TYPE(4, "用户");

    /**
     * 编码WuliuRouteTrack
     */
    private final Integer code;
    /**
     * 编码对应信息
     */
    private final String message;
}
