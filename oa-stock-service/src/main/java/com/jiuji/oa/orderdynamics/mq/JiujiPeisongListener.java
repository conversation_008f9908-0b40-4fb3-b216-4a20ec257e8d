package com.jiuji.oa.orderdynamics.mq;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.jiuji.oa.meituan.vo.req.LngLatReportReqVO;
import com.jiuji.oa.nc.common.config.rabbitmq.RabbitMqConfig;
import com.jiuji.oa.nc.common.enums.XtenantEnum;
import com.jiuji.oa.orderdynamics.dto.MqInfoData;
import com.jiuji.oa.orderdynamics.dto.WuliuRouteTrackMessage;
import com.jiuji.oa.orderdynamics.dto.ZtoRoutePushDataDTO;
import com.jiuji.oa.orderdynamics.enums.ZtoActionStatusEnum;
import com.jiuji.oa.orderdynamics.mapstruct.IWuliuSubMapStruct;
import com.jiuji.oa.orderdynamics.service.IJiujiPeisongBusService;
import com.jiuji.oa.orderdynamics.service.IWuliuMapTrackBusService;
import com.jiuji.oa.stock.common.util.JacksonJsonUtils;
import com.jiuji.oa.stock.common.util.ProfileUtil;
import com.jiuji.oa.stock.common.util.SysConfigUtils;
import com.jiuji.oa.stock.logistics.order.service.ILogisticsOrderService;
import com.jiuji.oa.stock.logistics.order.vo.ExpressPushVO;
import com.jiuji.oa.stock.logisticscenter.vo.req.PushExpressReq;
import com.jiuji.tc.foundation.message.send.service.MessageSendService;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.enums.order.SubDynamicsBusinessNodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/5/5 15:14
 */
@Slf4j
@Component
public class JiujiPeisongListener {
    @Resource
    private IJiujiPeisongBusService jiujiPeisongBusService;
    @Resource
    private IWuliuMapTrackBusService wuliuMapTrackBusService;
    @Resource
    private ILogisticsOrderService logisticsOrderService;
    @Resource
    private IWuliuSubMapStruct wuliuSubMapStruct;
    @Resource
    private MessageSendService messageSendService;

    /**
     * 经纬度上报
     * @param message
     */
    @RabbitListener(queues = RabbitMqConfig.JIUJI_LNGLAT_REPORT, containerFactory = "limitContainerFactory")
    public void synJiujiReportLngLat(Message message) {
        try {
            String xtenant = SysConfigUtils.getValueWithNoXtenant(SysConfigConstant.XTENANT);
            if (!XtenantEnum.isJiujiXtenant(Convert.toInt(xtenant, 0))) {
                return;
            }
            String msg = new String(message.getBody(), StandardCharsets.UTF_8);
            log.info("从rabbitmq获取经纬度上报信息：{}", msg);
            if (StringUtils.isEmpty(msg)) {
                log.error("从rabbitmq获取经纬度上报信息为空！");
                return;
            }
            LngLatReportReqVO dto = JacksonJsonUtils.toClass(msg, LngLatReportReqVO.class);
            jiujiPeisongBusService.saveJiuJiPeisong(dto);
        } catch (Exception e) {
            log.error("上报经纬度信息异常，message={}", message, e);
        }
    }

    /**
     * 订单物流单推送
     * @param message
     */
    @RabbitListener(queues = RabbitMqConfig.QUEUE_ORDER_DYNAMICS_WULIU, containerFactory = "oaAsyncLimitContainerFactory")
    public void orderDynamicsWuliu(Message message) {
        try {
            String xtenant = SysConfigUtils.getValueWithNoXtenant(SysConfigConstant.XTENANT);
            if (!XtenantEnum.isJiujiXtenant(Convert.toInt(xtenant, 0))) {
                return;
            }
            String msg = new String(message.getBody(), StandardCharsets.UTF_8);
            log.info("从rabbitmq获取订单推送信息：{}", msg);
            if (StringUtils.isEmpty(msg)) {
                log.error("从rabbitmq获取订单推送信息为空！");
                return;
            }
            MqInfoData mqInfoData = JacksonJsonUtils.toClass(msg, MqInfoData.class);
            if (Objects.isNull(mqInfoData) || Objects.isNull(mqInfoData.getBusinessNode())) {
                JSONObject jsonObject = JSONUtil.parseObj(msg);
                mqInfoData = jsonObject.get("data", MqInfoData.class);
            }
            if (Objects.isNull(mqInfoData)) {
                log.warn("解析MqInfoData失败msg={}", msg);
                return;
            }
            if (SubDynamicsBusinessNodeEnum.EXPRESS_CALLBACK.getCode().equals(mqInfoData.getBusinessNode())) {
                ZtoRoutePushDataDTO ztoRoutePushData = JacksonJsonUtils.toClass(mqInfoData.getMessage(), ZtoRoutePushDataDTO.class);
                PushExpressReq expressReq = new PushExpressReq();
                ExpressPushVO expressPush = wuliuSubMapStruct.toExpressPush(ztoRoutePushData);
                expressPush.setOpTime(DateUtil.parseLocalDateTime(ztoRoutePushData.getActionTime()));
                expressPush.setOpCode(ZtoActionStatusEnum.getOpCode(ztoRoutePushData.getAction()));
                expressReq.setData(JacksonJsonUtils.toJson(expressPush));
                log.info("处理中通快递路由推送信息expressReq={}", JacksonJsonUtils.toJson(expressPush));
                logisticsOrderService.handlePushExpress(expressReq);
            } else {
                wuliuMapTrackBusService.handleWuliuInfoMsg(mqInfoData);
            }
        } catch (Exception e) {
            log.error("处理订单推送信息异常，message={}", message, e);
        }
    }

    /**
     * 物流轨迹信息
     * @param message
     */
    @RabbitListener(queues = RabbitMqConfig.QUEUE_WULIU_ROUTE_TRACK, containerFactory = "defaultListenerContainerFactory")
    public void wuliuRouteTrack(Message message) {
        try {
            String xtenant = SysConfigUtils.getValueWithNoXtenant(SysConfigConstant.XTENANT);
            if (!XtenantEnum.isJiujiXtenant(Convert.toInt(xtenant, 0))) {
                return;
            }
            String msg = new String(message.getBody(), StandardCharsets.UTF_8);
            log.info("从rabbitmq获取物流轨迹信息：{}", msg);
            if (StringUtils.isEmpty(msg)) {
                return;
            }
            WuliuRouteTrackMessage wuliuRouteTrackMessage = JacksonJsonUtils.toClass(msg, WuliuRouteTrackMessage.class);
            if (Objects.isNull(wuliuRouteTrackMessage)) {
                return;
            }
            wuliuMapTrackBusService.handleWuliuRouteTrack(wuliuRouteTrackMessage);
        } catch (Exception e) {
            String profile = ProfileUtil.getActiveProfile();
            messageSendService.sendOaMessage(0, "", "13682", profile + "处理订单物流轨迹信息异常");
            log.error("处理物流轨迹信息异常，message={}", message, e);
        }
    }

    /**
     * 物流轨迹信息
     * @param message
     */
    @RabbitListener(queues = RabbitMqConfig.QUEUE_WULIU_ROUTE_TRACK_JIUJI, containerFactory = "defaultListenerContainerFactory")
    public void wuliuRouteTrackJiuji(Message message) {
        try {
            String xtenant = SysConfigUtils.getValueWithNoXtenant(SysConfigConstant.XTENANT);
            if (!XtenantEnum.isJiujiXtenant(Convert.toInt(xtenant, 0))) {
                return;
            }
            String msg = new String(message.getBody(), StandardCharsets.UTF_8);
            log.info("从rabbitmq获取九机快送物流轨迹信息：{}", msg);
            if (StringUtils.isEmpty(msg)) {
                log.error("从rabbitmq获取物流轨迹信息为空！");
                return;
            }
            WuliuRouteTrackMessage wuliuRouteTrackMessage = JacksonJsonUtils.toClass(msg, WuliuRouteTrackMessage.class);
            if (Objects.isNull(wuliuRouteTrackMessage)) {
                return;
            }
            wuliuMapTrackBusService.handleWuliuRouteTrack(wuliuRouteTrackMessage);
        } catch (Exception e) {
            String profile = ProfileUtil.getActiveProfile();
            messageSendService.sendOaMessage(0, "", "13682", profile + "处理九机快送订单物流轨迹信息异常");
            log.error("处理物流轨迹信息异常，message={}", message, e);
        }
    }
}
