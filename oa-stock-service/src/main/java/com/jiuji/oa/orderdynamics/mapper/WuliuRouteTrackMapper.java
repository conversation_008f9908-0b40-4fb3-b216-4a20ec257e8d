package com.jiuji.oa.orderdynamics.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.orderdynamics.entity.WuliuRouteTrack;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 */
@Mapper
public interface WuliuRouteTrackMapper extends BaseMapper<WuliuRouteTrack> {


    /**
     * 判断良品订单是否存在调拨（如果如果过有数据那就说明调拨完成）
     * @param subId
     * @return
     */
    @DS("ch999oanew")
    @Select("SELECT 1 FROM recover_marketInfo s with(nolock)\n" +
            "where s.sub_id = #{subId} and s.sub_check in (1,2,3,6)\n" +
            "and NOT EXISTS(SELECT 1 from recover_mkc k with(nolock)\n" +
            "inner join recover_marketSubInfo b with(nolock) on b.basket_id = k.to_basket_id \n" +
            "where b.sub_id = s.sub_id and k.areaid <> s.areaid)\n" +
            "and NOT EXISTS(SELECT 1 from recover_mkc k with(nolock)\n" +
            "inner join recover_marketSubInfo b with(nolock) on b.basket_id = k.to_basket_id \n" +
            "where b.sub_id = s.sub_id and k.mkc_check not in (3,5,8))")
    Integer allocationGoodProducts(@Param(value = "subId") Long subId);




}
