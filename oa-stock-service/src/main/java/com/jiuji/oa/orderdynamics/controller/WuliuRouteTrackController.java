package com.jiuji.oa.orderdynamics.controller;

import com.jiuji.oa.orderdynamics.dto.MqInfo;
import com.jiuji.oa.orderdynamics.service.IWuliuMapTrackBusService;
import com.jiuji.oa.orderdynamics.service.LongitudeAndLatitudeService;
import com.jiuji.oa.orderdynamics.util.JournalPush;
import com.jiuji.oa.orderdynamics.vo.request.LongitudeAndLatitudeCondition;
import com.jiuji.oa.orderdynamics.vo.request.QueryWuliuBySubReqVO;
import com.jiuji.oa.orderdynamics.vo.response.LongitudeAndLatitudeResult;
import com.jiuji.oa.orderdynamics.vo.response.QueryWuliuBySubResVO;
import com.jiuji.oa.stock.publiccheck.annotation.AddLog;
import com.jiuji.oa.stock.publiccheck.entity.AddLogKind;
import com.jiuji.oa.wuliu.enums.DeliveryEnum;
import com.jiuji.tc.common.vo.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/wuliumaptrack")
public class WuliuRouteTrackController {

    /**
     * 送货上门实现
     */
    @Resource(name = "ProvideHomeDeliveryServiceImpl")
    private LongitudeAndLatitudeService provideHomeDeliveryService;
    /**
     * 到店自取实现
     */
    @Resource(name = "SelfCollectionAtheStoreServiceImpl")
    private LongitudeAndLatitudeService selfCollectionAtheStoreService;
    /**
     * 父类实现
     */
    @Resource(name="LongitudeAndLatitudeCommonServiceImpl")
    private LongitudeAndLatitudeService longitudeAndLatitudeService;

    @Resource
    private IWuliuMapTrackBusService wuliuMapTrackBusService;

    @Resource
    private JournalPush journalPush;

    @PostMapping("/orderLogPush/v1")
    public R<LongitudeAndLatitudeResult> queryLongitudeAndLatitude(@RequestBody MqInfo mqInfo){
        journalPush.orderLogPush(mqInfo);
        return R.success("推送成功");
    }

    /**
     * 查询经纬度信息
     * @param longitudeAndLatitudeCondition
     * @return
     */
    @AddLog(type = AddLogKind.ORDER_DYNAMIC)
    @PostMapping("/queryLongitudeAndLatitude/v1")
    public R<LongitudeAndLatitudeResult> queryLongitudeAndLatitude(@RequestBody LongitudeAndLatitudeCondition longitudeAndLatitudeCondition) {
        Integer delivery = longitudeAndLatitudeCondition.getDelivery();
        if(delivery==null){
            return R.error("配送方式不能为空");
        }
        Long subId = longitudeAndLatitudeCondition.getSubId();
        if(subId==null){
            return R.error("订单号不能为空");
        }
        Integer subType = longitudeAndLatitudeCondition.getSubType();
        if(subType==null){
            return R.error("订单类型不能为空");
        }
        LongitudeAndLatitudeResult longitudeAndLatitudeResult;
        //判断是否为到店自取
        if(DeliveryEnum.AREA_SELF_PICK.getCode().equals(delivery)){
            longitudeAndLatitudeResult = selfCollectionAtheStoreService.queryLongitudeAndLatitude(longitudeAndLatitudeCondition);
        } else {
             longitudeAndLatitudeResult = provideHomeDeliveryService.queryLongitudeAndLatitude(longitudeAndLatitudeCondition);
        }
        //处理点重合的情况
        longitudeAndLatitudeService.handleOverlapPoint(longitudeAndLatitudeResult);
        return R.success(longitudeAndLatitudeResult);
    }

    /**
     * 订单查询物流单信息
     * @param req
     * @return
     */
    @PostMapping("/queryWuliuBySubId/v1")
    public R<QueryWuliuBySubResVO> queryWuliuBySubId(@RequestBody @Valid QueryWuliuBySubReqVO req) {
        return R.success(wuliuMapTrackBusService.queryWuliuBySubId(req));
    }

    /**
     * 定时查询第三方配送信息
     * @return
     */
    @GetMapping("/handleThirdDelivery/v1")
    public R<String> handleCompleteMeituanOrder() {
        wuliuMapTrackBusService.handleThirdDelivery();
        return R.success("查询成功");
    }
}
