package com.jiuji.oa.orderdynamics.service.impl;

import cn.hutool.core.convert.Convert;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.ch999.common.util.atlas.AtlasUtil;
import com.ch999.common.util.atlas.CoordinateUtil;
import com.ch999.common.util.vo.atlas.Coordinate;
import com.jiuji.oa.meituan.vo.res.MeituanOrderVO;
import com.jiuji.oa.nc.common.config.rabbitmq.RabbitMqConfig;
import com.jiuji.oa.nc.common.constant.RedisKeys;
import com.jiuji.oa.nc.common.exception.CustomizeException;
import com.jiuji.oa.nc.stock.entity.MkcToarea;
import com.jiuji.oa.nc.stock.service.IMkcToareaService;
import com.jiuji.oa.nc.user.service.IAreaInfoService;
import com.jiuji.oa.orderdynamics.dto.DiySubDTO;
import com.jiuji.oa.orderdynamics.dto.MqInfoData;
import com.jiuji.oa.orderdynamics.dto.WuliuRouteTrackMessage;
import com.jiuji.oa.orderdynamics.dto.WuliuSubDTO;
import com.jiuji.oa.orderdynamics.entity.WuliuRouteTrack;
import com.jiuji.oa.orderdynamics.enums.LineTypeEnum;
import com.jiuji.oa.orderdynamics.enums.WebSubTypeEnum;
import com.jiuji.oa.orderdynamics.mapstruct.IWuliuSubMapStruct;
import com.jiuji.oa.orderdynamics.service.IJiujiPeisongBusService;
import com.jiuji.oa.orderdynamics.service.IWuliuMapTrackBusService;
import com.jiuji.oa.orderdynamics.service.WuliuRouteTrackService;
import com.jiuji.oa.orderdynamics.vo.request.QueryWuliuBySubReqVO;
import com.jiuji.oa.orderdynamics.vo.response.QueryWuliuBySubResVO;
import com.jiuji.oa.stock.common.util.Builder;
import com.jiuji.oa.stock.common.util.JacksonJsonUtils;
import com.jiuji.oa.stock.ershou.service.ISalfgoodsService;
import com.jiuji.oa.stock.logistics.dada.service.IDadaService;
import com.jiuji.oa.stock.logistics.order.constant.LogisticsExpressStatusContant;
import com.jiuji.oa.stock.logistics.order.enums.WuliuStatusEnum;
import com.jiuji.oa.stock.logistics.order.vo.ExpressPushVO;
import com.jiuji.oa.stock.logisticscenter.enums.DaDaOrderStatusEnum;
import com.jiuji.oa.stock.logisticscenter.serive.ILogisticsExpressService;
import com.jiuji.oa.stock.logisticscenter.vo.req.QueryOrderInfoReqVO;
import com.jiuji.oa.stock.logisticscenter.vo.res.QueryOrderInfoResVO;
import com.jiuji.oa.wuliu.constant.WuLiuConstant;
import com.jiuji.oa.wuliu.dto.req.SubPositionReq;
import com.jiuji.oa.wuliu.entity.WuLiuEntity;
import com.jiuji.oa.wuliu.entity.WuliuAddress;
import com.jiuji.oa.wuliu.enums.UuOrderStatusEnum;
import com.jiuji.oa.wuliu.enums.WuLiuTypeEnum;
import com.jiuji.oa.wuliu.service.IRecoverMarketinfoService;
import com.jiuji.oa.wuliu.service.IWuLiuService;
import com.jiuji.oa.wuliu.service.IWuLiuSubService;
import com.jiuji.oa.wuliu.service.IWuliuAddressService;
import com.jiuji.oa.wuliu.utils.WuliuAddressUtil;
import com.jiuji.oa.wuliu.vo.CityIdListDTO;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.enums.order.SubDynamicsBusinessNodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/10/9 20:12
 */
@Service
@Slf4j
@DS("ch999oanew")
public class WuliuMapTrackBusServiceImpl implements IWuliuMapTrackBusService {
    private static final Integer WAYPOINTS_MAX_LENG = 400;
    private static final String WULIU_MAP_TRACK_LOCK = "stock:wuliumaptrack:lock:";
    @Resource
    private IWuLiuService wuLiuService;
    @Resource
    private IWuLiuSubService wuLiuSubService;
    @Resource
    private WuliuRouteTrackService wuliuRouteTrackService;
    @Resource
    private IAreaInfoService areaInfoService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private IDadaService dadaService;
    @Resource
    private ILogisticsExpressService logisticsExpressService;
    @Resource
    private IRecoverMarketinfoService recoverMarketinfoService;
    @Resource
    private IWuliuAddressService wuliuAddressService;
    @Resource
    private IJiujiPeisongBusService jiujiPeisongBusService;
    @Resource
    private IMkcToareaService mkcToareaService;
    @Resource
    private IWuliuSubMapStruct wuliuSubMapStruct;
    @Resource
    private ISalfgoodsService salfgoodsService;
    @Resource
    @Lazy
    private RedissonClient redissonClient;
    @Resource
    private RabbitTemplate rabbitTemplate;

    /**
     * 处理快递轨迹推送
     */
    @Override
    public void handleExpressTrack(ExpressPushVO expressPush) {
        if (Objects.equals(0, expressPush.getBusinessType())) {
            return;
        }
        List<WuLiuEntity> wuliuList = wuLiuService.getWuliuByNu(expressPush.getWaybillNo());
        if (CollectionUtils.isNotEmpty(wuliuList)) {
            Coordinate currentCoordinate = WuliuAddressUtil.getLocationByAddress(expressPush);
            WuliuRouteTrackMessage wuliuRouteTrackMessage = new WuliuRouteTrackMessage();
            wuliuRouteTrackMessage.setType(-1);
            wuliuRouteTrackMessage.setCurrentCoordinate(currentCoordinate);
            for (WuLiuEntity wuLiuEntity : wuliuList) {
                wuliuRouteTrackMessage.setWuLiuEntity(wuLiuEntity);
                rabbitTemplate.convertAndSend(RabbitMqConfig.QUEUE_WULIU_ROUTE_TRACK, JacksonJsonUtils.toJson(wuliuRouteTrackMessage));
            }
        } else {
            this.handleDiyExpressTrack(expressPush, -1);
        }
    }

    /**
     * Diy订单处理
     */
    @Override
    @DSTransactional()
    public void handleDiyExpressTrack(ExpressPushVO expressPush, Integer type) {
        log.warn("处理DIY快递发货和送达轨迹信息,expressPush={},type={}", JacksonJsonUtils.toJson(expressPush), type);
        if (Objects.isNull(expressPush) || StringUtils.isBlank(expressPush.getWaybillNo())) {
            return;
        }
        Coordinate currentCoordinate = WuliuAddressUtil.getLocationByAddress(expressPush);
        RLock lock = redissonClient.getLock(WULIU_MAP_TRACK_LOCK + "diy" + expressPush.getWaybillNo());
        try {
            lock.lock();
            List<WuliuRouteTrack> wuliuRouteList = getDiyWuliuRouteTrack(expressPush.getWaybillNo(), currentCoordinate);
            if (CollectionUtils.isNotEmpty(wuliuRouteList)) {
                if (Objects.equals(1, type)) {
                    wuliuRouteList.forEach(v -> {
                        v.setStatus(1);
                        v.setCurrentLatitude(v.getReceiverLatitude());
                        v.setCurrentLongitude(v.getReceiverLongitude());
                    });
                }
                log.warn("保存更新diy订单线路信息expressPush={},type={},wuliuRouteList={}", JacksonJsonUtils.toJson(expressPush), type,JacksonJsonUtils.toJson(wuliuRouteList));
                wuliuRouteTrackService.saveOrUpdateBatch(wuliuRouteList);
            }
        } finally {
            lock.unlock();
        }
    }

    /**
     * 处理订单物流轨迹信息
     *
     * @param wuliuRouteTrackMessage
     */
    @Override
    @DSTransactional()
    public void handleWuliuRouteTrack(WuliuRouteTrackMessage wuliuRouteTrackMessage) {
        Coordinate currentCoordinate = wuliuRouteTrackMessage.getCurrentCoordinate();
        WuLiuEntity wuLiuEntity = wuliuRouteTrackMessage.getWuLiuEntity();
        Integer type = wuliuRouteTrackMessage.getType();
        if (Objects.isNull(wuLiuEntity)) {
            return;
        }
        List<WuliuRouteTrack> wuliuRouteTrackList = getWuliuRouteTrack(currentCoordinate, wuLiuEntity, type);
        //更新路由线路信息
        if (CollectionUtils.isNotEmpty(wuliuRouteTrackList)) {
            wuliuRouteTrackList.forEach(v -> {
                if (Objects.equals(1,type) && Objects.equals(LineTypeEnum.DELIVERY.getCode(), v.getLineType())) {
                    v.setStatus(1);
                }
            });
            log.warn("保存更新订单线路信息wuliuRouteTrackMessage={},wuliuRouteList={}",JacksonJsonUtils.toJson(wuliuRouteTrackMessage),JacksonJsonUtils.toJson(wuliuRouteTrackList));
            wuliuRouteTrackService.saveOrUpdateBatch(wuliuRouteTrackList);
        }
    }

    /**
     * 定时查询跑腿骑手实时位置
     */
    @Override
    public void handleThirdDelivery() {
        handelDadaOrderRoute();
        handelUuOrderRoute();
        handelThirdOrderRoute();
    }

    /**
     * 未发货/送达
     */
    @Override
    public void handleDeliveryExpress(String nu, Integer type) {
        log.warn("处理快递发货和送达轨迹信息,nu={},type={}", nu, type);
        if (StringUtils.isBlank(nu)) {
            return;
        }
        List<WuLiuEntity> wuliuList = wuLiuService.getWuliuByNu(nu);
        if (CollectionUtils.isEmpty(wuliuList)) {
            log.warn("快递单未查询到对应物流单信息，nu={},type={}", nu, type);
            return;
        }
        Coordinate currentCoordinate = null;
        WuliuAddress wuliuAddress = wuliuAddressService.queryByNu(nu);
        if (Objects.nonNull(wuliuAddress) && StringUtils.isNotBlank(wuliuAddress.getRaddress())) {
            if(Objects.equals(0,type)) {
                currentCoordinate = wuliuAddressService.getAreaCoordinate(0,wuliuAddress.getSaddress(),0, 0);
            } else {
                currentCoordinate = wuliuAddressService.getAreaCoordinate(0,wuliuAddress.getRaddress(),0, 0);
            }
        } else {
            WuLiuEntity wuLiuEntity = wuliuList.get(0);
            if(Objects.equals(0,type)) {
                currentCoordinate = wuliuAddressService.getAreaCoordinate(0,wuLiuEntity.getSAddress(),wuLiuEntity.getSCityId(), 0);
            } else {
                currentCoordinate = wuliuAddressService.getAreaCoordinate(0,wuLiuEntity.getRAddress(),wuLiuEntity.getRCityId(), 0);
            }
        }
        if (Objects.isNull(currentCoordinate)) {
            log.warn("获取位置信息失败nu={},type={}", nu, type);
            return;
        }

        log.warn("处理快递发货和送达轨迹信息,nu={},type={},wuliuList={}", nu, type, JacksonJsonUtils.toJson(wuliuList));
        for (WuLiuEntity wuLiuEntity : wuliuList) {
            WuliuRouteTrackMessage wuliuRouteTrackMessage = new WuliuRouteTrackMessage()
                    .setCurrentCoordinate(currentCoordinate).setType(type).setWuLiuEntity(wuLiuEntity);
            rabbitTemplate.convertAndSend(RabbitMqConfig.QUEUE_WULIU_ROUTE_TRACK, JacksonJsonUtils.toJson(wuliuRouteTrackMessage));
            //美团闪购订单物流信息同步
            if (Objects.equals(1, type)
                    && !Objects.equals(0, Optional.ofNullable(wuLiuEntity.getDanHaoBind()).orElse(0))
                    && Arrays.asList(WuLiuTypeEnum.ORDER.getCode(),WuLiuTypeEnum.ORDER_EXPRESS.getCode()).contains(Optional.ofNullable(wuLiuEntity.getWuType()).orElse(0))) {
                MeituanOrderVO meituanOrderSub = new MeituanOrderVO();
                meituanOrderSub.setSubId(wuLiuEntity.getDanHaoBind().longValue());
                meituanOrderSub.setNu(wuLiuEntity.getNu());
                meituanOrderSub.setCom(wuLiuEntity.getCom());
                meituanOrderSub.setWuliuId(Convert.toStr(wuLiuEntity.getId()));
                rabbitTemplate.convertAndSend(RabbitMqConfig.MEITUAN_ORDER_REPORT, JacksonJsonUtils.toJson(meituanOrderSub));
            }
        }
    }

    /**
     * 未发货/送达
     * type = 0 未发货 type=1送达
     */
    @Override
    public void handleDeliveryExpress(Integer wuliuId, Integer type, Integer transferType) {
        if (Objects.isNull(wuliuId)) {
            return;
        }
        log.warn("处理物流单发货和送达轨迹信息,wuliuId={},type={},transferType={}", wuliuId, type, transferType);
        String key = RedisKeys.ORDER_DYNAMICS_TRACK + wuliuId+":"+type +":"+ transferType;
        String res = stringRedisTemplate.opsForValue().get(key);
        if (StringUtils.isNotBlank(res)) {
            stringRedisTemplate.opsForValue().set(key, ""+wuliuId, NumberConstant.THIRTY, TimeUnit.SECONDS);
            log.info("物流单发货和送达轨迹信息重复,wuliuId={},type={},transferType={}", wuliuId, type, transferType);
            return;
        }
        stringRedisTemplate.opsForValue().set(key, ""+wuliuId, NumberConstant.THIRTY, TimeUnit.SECONDS);
        WuLiuEntity wuliu = wuLiuService.getById(wuliuId);
        if (Objects.isNull(wuliu)) {
            return;
        }
        Coordinate currentCoordinate = null;
        if(Objects.equals(0,type)) {
            currentCoordinate = wuliuAddressService.getAreaCoordinate(wuliu.getSAreaId(), wuliu.getSAddress(), wuliu.getSCityId(), transferType);
        } else {
            currentCoordinate = wuliuAddressService.getAreaCoordinate(wuliu.getRAreaId(), wuliu.getRAddress(), wuliu.getRCityId(), transferType);
        }

        WuliuRouteTrackMessage wuliuRouteTrackMessage = new WuliuRouteTrackMessage()
                .setCurrentCoordinate(currentCoordinate).setType(type).setWuLiuEntity(wuliu);
        rabbitTemplate.convertAndSend(RabbitMqConfig.QUEUE_WULIU_ROUTE_TRACK, JacksonJsonUtils.toJson(wuliuRouteTrackMessage));
    }

    /**
     * 订单生成路线信息
     */
    @Override
    @DSTransactional()
    public List<WuliuRouteTrack> initWuliuRouteTrack(Integer subId, Integer subType) {
        log.warn("初始化订单配送线路信息，subId={}，subType={}", subId, subType);
        List<WuliuRouteTrack> wuliuRouteList = new ArrayList<>();
        List<WuLiuEntity> wuliuList = null;
        if (WebSubTypeEnum.ORDER.getCode().equals(subType)) {
            wuliuList = wuLiuSubService.getWuliuEntityBySubId(subId);
        } else if (WebSubTypeEnum.GOOD_PRODUCT_ORDER.getCode().equals(subType)) {
            wuliuList = recoverMarketinfoService.getWuliuEntityBySubId(subId);
        }
        if (CollectionUtils.isEmpty(wuliuList)) {
            return wuliuRouteList;
        }

        String lock = WULIU_MAP_TRACK_LOCK + subId + ":" + subType;
        RLock rLock = redissonClient.getLock(lock);
        try {
            rLock.lock();
            for (WuLiuEntity wuLiuEntity : wuliuList) {
                wuliuRouteList.addAll(getWuliuRouteTrack(null, wuLiuEntity, 0));
            }
            //更新路由线路信息
            if (CollectionUtils.isNotEmpty(wuliuRouteList)) {
                log.warn("订单初始化线路信息subId={}，subType={},wuliuRouteList={}", subId,subType,JacksonJsonUtils.toJson(wuliuRouteList));
                wuliuRouteTrackService.saveOrUpdateBatch(wuliuRouteList);
            }
        } finally {
            rLock.unlock();
        }
        return wuliuRouteList;
    }

    /**
     * 订单查询物流单
     *
     * @return
     */
    @Override
    public QueryWuliuBySubResVO queryWuliuBySubId(QueryWuliuBySubReqVO req) {
        QueryWuliuBySubResVO res = null;
        //订单
        if (WebSubTypeEnum.ORDER.getCode().equals(req.getSubType())) {
            res = wuLiuSubService.queryWuliuBySubId(req.getSubId());
        } else if (WebSubTypeEnum.GOOD_PRODUCT_ORDER.getCode().equals(req.getSubType())) {
            res = recoverMarketinfoService.queryWuliuBySubId(req.getSubId());
        } else {
            throw new CustomizeException("订单类型错误");
        }
        return res;
    }

    /**
     * 订单推送物流节点信息
     *
     * @param mqInfoData
     */
    @Override
    public void handleWuliuInfoMsg(MqInfoData mqInfoData) {
        log.warn("订单动态物流状态推送mqInfoData={}", mqInfoData);
        SubDynamicsBusinessNodeEnum businessNodeByCode = SubDynamicsBusinessNodeEnum.getBusinessNodeByCode(mqInfoData.getBusinessNode());
        if (Objects.isNull(businessNodeByCode)) {
            return;
        }
        switch (SubDynamicsBusinessNodeEnum.getBusinessNodeByCode(mqInfoData.getBusinessNode())) {
            //订单关联调拨单
            case MKC_DIAOBO_DELIVERY:
            case ORDER_ASSOCIATED_DIAOBO:
                if (Objects.isNull(mqInfoData.getWuliuId()) || Objects.equals(0, mqInfoData.getWuliuId())) {
                    MkcToarea mkToarea = mkcToareaService.getById(mqInfoData.getBusinessId());
                    if (Objects.nonNull(mkToarea)) {
                        this.handleDeliveryExpress(mkToarea.getWuliuid(), 0, 1);
                    }
                } else {
                    this.handleDeliveryExpress(mqInfoData.getWuliuId(), 0, 1);
                }
                break;
            case SECOND_GOODS_DIAOBO_DELIVERY:
            case SECOND_HAND_ORDER_ASSOCIATED_DIAOBO:
            case LOGISTICS_PICKUP:
            case LOGISTICS_DELIVERY:
            case DIAOBO_DELIVERY:
                this.handleDeliveryExpress(mqInfoData.getWuliuId(), 0, 1);
                break;
            case LOGISTICS_ARRIVED:
            case MKC_DIAOBO_RECEIVED:
            case DIAOBO_RECEIVED:
            case SECOND_GOODS_DIAOBO_RECEIVED:
                this.handleDeliveryExpress(mqInfoData.getWuliuId(), 1, 1);
                break;
            case EXPRESS_ARRIVED:
                this.handleDeliveryExpress(mqInfoData.getWuliuId(), 1, 0);
                break;
            case EXPRESS_DELIVERING:
                this.handleDeliveryExpress(mqInfoData.getWuliuId(), 0, 0);
                break;
            case ORDER_STOCK_OUT:
                this.initWuliuRouteTrack(mqInfoData.getSubId().intValue(), WebSubTypeEnum.ORDER.getCode());
                break;
            case SECOND_HAND_ORDER_STOCK_OUT:
                this.initWuliuRouteTrack(mqInfoData.getSubId().intValue(), WebSubTypeEnum.GOOD_PRODUCT_ORDER.getCode());
                break;
            default:
                break;
        }
        jiujiPeisongBusService.refreshHasSendingCache(mqInfoData.getWuliuId());
    }

    /**
     * 处理uu跑腿订单轨迹信息
     */
    private void handelUuOrderRoute() {
        Set<String> uuOrderSet = stringRedisTemplate.opsForSet().members(RedisKeys.UU_DISPATCHING_ORDER_LIST);
        log.warn("定时处理uu订单位置信息uuOrderSet={}", uuOrderSet);
        if (CollectionUtils.isEmpty(uuOrderSet)) {
            return;
        }
        try {
            QueryOrderInfoReqVO reqVO = new QueryOrderInfoReqVO();
            reqVO.setExpressType(WuLiuConstant.UU_PAOTUI_EXPRESS_TYPE);
            reqVO.setTenantScale(0);
            reqVO.setXTenantId(0L);
            for (String waybillNo :  uuOrderSet) {
                reqVO.setWaybillNo(waybillNo);
                R<QueryOrderInfoResVO> queryOrderInfoResResult = logisticsExpressService.queryOrderInfo(reqVO);
                if (Objects.isNull(queryOrderInfoResResult)) {
                    stringRedisTemplate.opsForSet().remove(RedisKeys.UU_DISPATCHING_ORDER_LIST, waybillNo);
                    continue;
                }
                if (Objects.equals(0, queryOrderInfoResResult.getCode()) && Objects.nonNull(queryOrderInfoResResult.getData())) {
                    QueryOrderInfoResVO queryOrderInfoRes = queryOrderInfoResResult.getData();
                    //配送完成,取消
                    if (Objects.isNull(queryOrderInfoRes.getStatusCode()) || Arrays.asList(UuOrderStatusEnum.DELIVERED.getCode(),UuOrderStatusEnum.CANCELED.getCode()).contains(queryOrderInfoRes.getStatusCode())) {
                        stringRedisTemplate.opsForSet().remove(RedisKeys.UU_DISPATCHING_ORDER_LIST, waybillNo);
                        continue;
                    }
                    if (StringUtils.isNotBlank(queryOrderInfoRes.getLongitude()) && StringUtils.isNotBlank(queryOrderInfoRes.getLatitude())) {
                        Coordinate coordinate = CoordinateUtil.bd2gcj(new Coordinate(queryOrderInfoRes.getLongitude() + "," + queryOrderInfoRes.getLatitude()));
                        coordinate = CoordinateUtil.gcj2wgs(coordinate);
                        this.handleExpressTrack(waybillNo, coordinate.getLongitude() +","+ coordinate.getLatitude());
                    }
                }
            }
        } catch (Exception e) {
            log.error("定时处理uu订单轨迹信息异常param={}",uuOrderSet, e);
        }
    }

    /**
     * 处理uu跑腿订单轨迹信息
     */
    private void handelThirdOrderRoute() {
        Set<String> orderSet = stringRedisTemplate.opsForSet().members(RedisKeys.THIRD_PLATFORM_DELIVERY_DISPATCHING_ORDER_LIST);
        log.warn("定时处理三方跑腿订单位置信息={}", orderSet);
        if (CollectionUtils.isEmpty(orderSet)) {
            return;
        }
        QueryOrderInfoReqVO reqVO = new QueryOrderInfoReqVO();
        for (String waybillNo :  orderSet) {
            reqVO.setWaybillNo(waybillNo);
            R<QueryOrderInfoResVO> queryOrderInfoResResult = logisticsExpressService.queryOrderInfoV2(reqVO);
            if (Objects.isNull(queryOrderInfoResResult)) {
                stringRedisTemplate.opsForSet().remove(RedisKeys.THIRD_PLATFORM_DELIVERY_DISPATCHING_ORDER_LIST, waybillNo);
                continue;
            }
            if (Objects.equals(0, queryOrderInfoResResult.getCode()) && Objects.nonNull(queryOrderInfoResResult.getData())) {
                QueryOrderInfoResVO queryOrderInfoRes = queryOrderInfoResResult.getData();
                //配送完成,取消
                if (Objects.isNull(queryOrderInfoRes.getStatusCode()) || Arrays.asList(LogisticsExpressStatusContant.DELIVERED,LogisticsExpressStatusContant.CANCELED).contains(queryOrderInfoRes.getLogisticsOrderStatus())) {
                    stringRedisTemplate.opsForSet().remove(RedisKeys.THIRD_PLATFORM_DELIVERY_DISPATCHING_ORDER_LIST, waybillNo);
                    continue;
                }
                if (StringUtils.isNotBlank(queryOrderInfoRes.getLongitude()) && StringUtils.isNotBlank(queryOrderInfoRes.getLatitude())) {
                    Coordinate coordinate = CoordinateUtil.bd2gcj(new Coordinate(queryOrderInfoRes.getLongitude() + "," + queryOrderInfoRes.getLatitude()));
                    coordinate = CoordinateUtil.gcj2wgs(coordinate);
                    this.handleExpressTrack(waybillNo, coordinate.getLongitude() +","+ coordinate.getLatitude());
                }
            }
        }
    }

    /**
     * 处理达达订单轨迹信息
     */
    private void handelDadaOrderRoute() {
        Set<String> dadaOrderSet = stringRedisTemplate.opsForSet().members(RedisKeys.DADA_DISPATCHING_ORDER_LIST);
        try {
            log.warn("定时处理达达订单位置信息dadaOrderSet={}", dadaOrderSet);
            if (Objects.nonNull(dadaOrderSet)) {
                for (String waybillNo : dadaOrderSet) {
                    QueryOrderInfoReqVO reqVO = new QueryOrderInfoReqVO();
                    reqVO.setWaybillNo(waybillNo);
                    QueryOrderInfoResVO queryOrderInfoRes = dadaService.queryOrder(reqVO);
                    //取消,完成
                    if (Objects.isNull(queryOrderInfoRes.getStatusCode()) || Arrays.asList(DaDaOrderStatusEnum.COMPLETED.getCode(),DaDaOrderStatusEnum.ASSIGNMENT.getCode()).contains(queryOrderInfoRes.getStatusCode())) {
                        stringRedisTemplate.opsForSet().remove(RedisKeys.DADA_DISPATCHING_ORDER_LIST, waybillNo);
                        continue;
                    }
                    if (StringUtils.isNotBlank(queryOrderInfoRes.getLongitude()) && StringUtils.isNotBlank(queryOrderInfoRes.getLatitude())) {
                        Coordinate coordinate = CoordinateUtil.gcj2wgs(new Coordinate(queryOrderInfoRes.getLongitude() + "," + queryOrderInfoRes.getLatitude()));
                        this.handleExpressTrack(waybillNo, coordinate.getLongitude()+","+coordinate.getLatitude());
                    }
                }
            }
        } catch (Exception e) {
            log.error("定时处理达达快递轨迹异常param={}", dadaOrderSet, e);
        }
    }

    /**
     * 九机快送位置信息
     */
    @Override
    public void handleJiujiExpressTrack(List<WuLiuEntity> wuliuList, String currentLocation) {
        log.warn("处理九机快送轨迹信息，wuliuList={}, currentLocation={}", wuliuList, currentLocation);
        if (CollectionUtils.isEmpty(wuliuList)) {
            return;
        }
        Coordinate currentCoordinate = new Coordinate(currentLocation);

        for (WuLiuEntity wuLiuEntity : wuliuList) {
            WuliuRouteTrackMessage wuliuRouteTrackMessage = new WuliuRouteTrackMessage()
                    .setCurrentCoordinate(currentCoordinate).setWuLiuEntity(wuLiuEntity);
            rabbitTemplate.convertAndSend(RabbitMqConfig.QUEUE_WULIU_ROUTE_TRACK_JIUJI, JacksonJsonUtils.toJson(wuliuRouteTrackMessage));
        }
    }

    /**
     * 处理跑腿
     */
    public void handleExpressTrack(String nu,String currentLocation) {
        if (StringUtils.isBlank(nu)) {
            return;
        }
        List<WuLiuEntity> wuliuList = wuLiuService.getWuliuByNu(nu);
        if (CollectionUtils.isEmpty(wuliuList)) {
            stringRedisTemplate.opsForSet().remove(RedisKeys.DADA_DISPATCHING_ORDER_LIST, nu);
            stringRedisTemplate.opsForSet().remove(RedisKeys.UU_DISPATCHING_ORDER_LIST, nu);
            stringRedisTemplate.opsForSet().remove(RedisKeys.THIRD_PLATFORM_DELIVERY_DISPATCHING_ORDER_LIST, nu);
            return;
        }
        Coordinate currentCoordinate = new Coordinate(currentLocation);

        for (WuLiuEntity wuLiuEntity : wuliuList) {
            if (Objects.equals(WuliuStatusEnum.COMPLETE.getCode(), wuLiuEntity.getStats())
                    || Objects.equals(WuliuStatusEnum.RECEIVED.getCode(), wuLiuEntity.getStats())
                    || Objects.equals(WuliuStatusEnum.INVALID.getCode(), wuLiuEntity.getStats())) {
                stringRedisTemplate.opsForSet().remove(RedisKeys.DADA_DISPATCHING_ORDER_LIST, nu);
                stringRedisTemplate.opsForSet().remove(RedisKeys.UU_DISPATCHING_ORDER_LIST, nu);
                stringRedisTemplate.opsForSet().remove(RedisKeys.THIRD_PLATFORM_DELIVERY_DISPATCHING_ORDER_LIST, nu);
            }
            WuliuRouteTrackMessage wuliuRouteTrackMessage = new WuliuRouteTrackMessage()
                    .setCurrentCoordinate(currentCoordinate).setWuLiuEntity(wuLiuEntity);
            rabbitTemplate.convertAndSend(RabbitMqConfig.QUEUE_WULIU_ROUTE_TRACK, JacksonJsonUtils.toJson(wuliuRouteTrackMessage));
        }
    }

    /**
     * 获取地图轨迹路线信息
     * @param currentCoordinate
     * @param wuLiuEntity
     * @return
     */
    private List<WuliuRouteTrack> getWuliuRouteTrack(Coordinate currentCoordinate,
                                                    WuLiuEntity wuLiuEntity,
                                                     Integer type) {

        List<WuliuRouteTrack> wuliuRouteTrackList = wuliuRouteTrackService.getWuliuRouteTrackByWuliuIds(Collections.singletonList(wuLiuEntity.getId().longValue()));
        Map<Long, List<WuliuRouteTrack>> wuliuRouteTrackMap = Optional.ofNullable(wuliuRouteTrackList).orElse(new ArrayList<>())
                .stream().collect(Collectors.groupingBy(WuliuRouteTrack::getWuliuId));
        List<WuliuRouteTrack> updateWuliuRouteTrackList = wuliuRouteTrackMap.get(wuLiuEntity.getId().longValue());
        if (CollectionUtils.isEmpty(wuliuRouteTrackList)) {
            return addWuliuRouteTrackList(currentCoordinate,wuLiuEntity,type);
        }

        List<WuliuRouteTrack> resRouteTrackList = new ArrayList<>();
        for(WuliuRouteTrack wuliuRouteTrack : updateWuliuRouteTrackList) {
            //线路已经完成
            if (Objects.equals(1,wuliuRouteTrack.getStatus())) {
                continue;
            }
            WuliuRouteTrack updateRouteTrack = new WuliuRouteTrack();
            updateRouteTrack.setId(wuliuRouteTrack.getId());
            updateRouteTrack.setStatus(0);
            //更新起点终点
            Coordinate sendCoordinate = wuliuAddressService.getAreaCoordinate(wuLiuEntity.getSAreaId(), wuLiuEntity.getSAddress(), wuLiuEntity.getSCityId(), 1);
            Coordinate receiverCoordinate = wuliuAddressService.getAreaCoordinate(wuLiuEntity.getRAreaId(), wuLiuEntity.getRAddress(), wuLiuEntity.getRCityId(), wuliuRouteTrack.getLineType());
            if (Objects.isNull(sendCoordinate) || Objects.isNull(receiverCoordinate)) {
                continue;
            }
            wuliuRouteTrack.setSendLatitude(Convert.toStr(sendCoordinate.getLatitude()));
            wuliuRouteTrack.setSendLongitude(Convert.toStr(sendCoordinate.getLongitude()));
            wuliuRouteTrack.setReceiverLatitude(Convert.toStr(receiverCoordinate.getLatitude()));
            wuliuRouteTrack.setReceiverLongitude(Convert.toStr(receiverCoordinate.getLongitude()));
            //物流单签收，物流单完成
            if (WuliuStatusEnum.RECEIVED.getCode().equals(wuLiuEntity.getStats())
                    || WuliuStatusEnum.COMPLETE.getCode().equals(wuLiuEntity.getStats())) {
                currentCoordinate = new Coordinate(wuliuRouteTrack.getReceiverLongitude() + "," + wuliuRouteTrack.getReceiverLatitude());
                updateRouteTrack.setStatus(1);
            } else if (Objects.isNull(currentCoordinate)) {
                return resRouteTrackList;
            }

            updateRouteTrack.setCurrentLatitude(String.valueOf(currentCoordinate.getLatitude()));
            updateRouteTrack.setCurrentLongitude(String.valueOf(currentCoordinate.getLongitude()));
            if (Objects.equals(-1, type)) {
                if (StringUtils.isBlank(wuliuRouteTrack.getWaypoints())) {
                    updateRouteTrack.setWaypoints(currentCoordinate.getLatitude() + "," + currentCoordinate.getLongitude());
                } else if (wuliuRouteTrack.getWaypoints().length() < WAYPOINTS_MAX_LENG
                        && !wuliuRouteTrack.getWaypoints().contains(currentCoordinate.getLatitude()+","+currentCoordinate.getLongitude())){
                    updateRouteTrack.setWaypoints(wuliuRouteTrack.getWaypoints() + ";" + currentCoordinate.getLatitude() + "," + currentCoordinate.getLongitude());
                }
            }
            //调拨时统一显示货车
            if (LineTypeEnum.ALLOCATION.getCode().equals(wuliuRouteTrack.getLineType())) {
                updateRouteTrack.setDispatchStatus(1);
            }
            resRouteTrackList.add(updateRouteTrack);
        }

        log.warn("获取订单路线信息currentCoordinate={}，wuLiuEntity={}，type={}，resRouteTrackList={}"
                , JacksonJsonUtils.toJson(currentCoordinate),
                JacksonJsonUtils.toJson(wuLiuEntity),type,JacksonJsonUtils.toJson(resRouteTrackList));
        return resRouteTrackList;
    }

    /**
     * 新增订单路线
     * @param currentCoordinate
     * @param wuLiuEntity
     * @param type
     * @return
     */
    private List<WuliuRouteTrack> addWuliuRouteTrackList(Coordinate currentCoordinate, WuLiuEntity wuLiuEntity, Integer type) {
        List<WuliuRouteTrack> resultList = new ArrayList<>();
        List<WuliuSubDTO> wuliuSubByWuliuList = wuLiuSubService.getWuliuSubByWuliuId(wuLiuEntity.getId());
        log.warn("订单新增路线信息currentCoordinate={}，wuLiuEntity={}，type={}，wuliuSubByWuliuList={}"
                , JacksonJsonUtils.toJson(currentCoordinate),
                JacksonJsonUtils.toJson(wuLiuEntity),type,JacksonJsonUtils.toJson(wuliuSubByWuliuList));
        if (CollectionUtils.isEmpty(wuliuSubByWuliuList)) {
            return resultList;
        }

        for (WuliuSubDTO wuliuSub : wuliuSubByWuliuList) {
            Coordinate sendCoordinate = wuliuAddressService.getAreaCoordinate(wuLiuEntity.getSAreaId(), wuLiuEntity.getSAddress(), wuLiuEntity.getSCityId(), 1);

            String raddressPosition = WuliuAddressUtil.getSubPosition(Builder.of(SubPositionReq::new)
                    .with(SubPositionReq::setSubId, wuLiuEntity.getDanHaoBind())
                    .with(SubPositionReq::setWuliuId, wuLiuEntity.getId())
                    .with(SubPositionReq::setAddress, wuLiuEntity.getRAddress())
                    .with(SubPositionReq::setWuType, wuLiuEntity.getWuType())
                    .build());
            Coordinate receiverCoordinate;
            if (StringUtils.isBlank(raddressPosition)) {
                receiverCoordinate = wuliuAddressService.getAreaCoordinate(wuLiuEntity.getRAreaId(), wuLiuEntity.getRAddress(), wuLiuEntity.getRCityId(), wuliuSub.getLineType());
            } else {
                receiverCoordinate = new Coordinate(raddressPosition);
            }

            if (Objects.equals(0, type)) {
                currentCoordinate = sendCoordinate;
            }
            //物流单签收，物流单完成
            if (WuliuStatusEnum.RECEIVED.getCode().equals(wuLiuEntity.getStats())
                    || WuliuStatusEnum.COMPLETE.getCode().equals(wuLiuEntity.getStats())) {
                currentCoordinate = receiverCoordinate;
            }
            if (Objects.isNull(sendCoordinate) || Objects.isNull(receiverCoordinate) || Objects.isNull(currentCoordinate)) {
                continue;
            }
            //处理良品调拨轨迹图片地址
            if (LineTypeEnum.ALLOCATION.getCode().equals(wuliuSub.getLineType())
                    && WebSubTypeEnum.GOOD_PRODUCT_ORDER.getCode().equals(wuliuSub.getSubType())) {
                String pic = salfgoodsService.getPicByPpriceId(wuliuSub.getPpid());
                wuliuSub.setProductImage(pic);
            }

            WuliuRouteTrack wuliuRouteTrack = wuliuSubMapStruct.toWuliuRouteTrack(wuliuSub);
            wuliuRouteTrack.setWuliuId(wuLiuEntity.getId().longValue());
            wuliuRouteTrack.setSendLatitude(""+sendCoordinate.getLatitude());
            wuliuRouteTrack.setSendLongitude(""+sendCoordinate.getLongitude());
            wuliuRouteTrack.setReceiverLatitude(""+receiverCoordinate.getLatitude());
            wuliuRouteTrack.setReceiverLongitude(""+receiverCoordinate.getLongitude());
            wuliuRouteTrack.setCurrentLatitude(""+currentCoordinate.getLatitude());
            wuliuRouteTrack.setCurrentLongitude(""+currentCoordinate.getLongitude());
            //调拨时统一显示货车
            if (LineTypeEnum.ALLOCATION.getCode().equals(wuliuRouteTrack.getLineType())) {
                wuliuRouteTrack.setDispatchStatus(1);
            }
            resultList.add(wuliuRouteTrack);
        }
        return resultList;
    }

    /**
     * 获取Diy订单地图轨迹路线信息
     * @param nu
     * @param currentCoordinate
     * @return
     */
    private List<WuliuRouteTrack> getDiyWuliuRouteTrack(String nu, Coordinate currentCoordinate) {
        List<WuliuRouteTrack> resRouteTrackList = new ArrayList<>();
        if (Objects.isNull(currentCoordinate)) {
            return resRouteTrackList;
        }
        List<DiySubDTO> wuliuSubList = wuLiuSubService.getDiySubByNu(nu);
        if (CollectionUtils.isNotEmpty(wuliuSubList)) {
            List<Long> subIdList = wuliuSubList.stream().map(DiySubDTO::getSubId).collect(Collectors.toList());
            List<WuliuRouteTrack> wuliuRouteTrackList = wuliuRouteTrackService.getWuliuRouteTrackBySubIds(subIdList);
            Map<Long, WuliuRouteTrack> routeTrackMap = wuliuRouteTrackList.stream()
                    .filter(v -> Objects.isNull(v.getWuliuId()) || Objects.equals(0L, v.getWuliuId()))
                    .collect(Collectors.toMap(WuliuRouteTrack::getSubId, v -> v, (v1, v2) -> v2));

            for (DiySubDTO diySub : wuliuSubList) {
                CityIdListDTO cityInfo = wuLiuService.getAreaIdByCityId(diySub.getCityId(), 1);
                String address = WuliuAddressUtil.getAddress(diySub.getReceiverAdress(), cityInfo);
                Coordinate receiverCoordinate = Optional.ofNullable(AtlasUtil.translateAddressByThreeMapBase(address,"")).orElse(new Coordinate());
                WuliuRouteTrack wuliuRouteTrack = new WuliuRouteTrack();
                if (Objects.nonNull(routeTrackMap.get(diySub.getSubId()))) {
                    wuliuRouteTrack = routeTrackMap.get(diySub.getSubId());
                } else {
                    wuliuRouteTrack.setStatus(0);
                    wuliuRouteTrack.setSendLatitude("" + currentCoordinate.getLatitude());
                    wuliuRouteTrack.setSendLongitude("" + currentCoordinate.getLongitude());
                    wuliuRouteTrack.setReceiverLatitude("" + receiverCoordinate.getLatitude());
                    wuliuRouteTrack.setReceiverLongitude("" + receiverCoordinate.getLongitude());
                    wuliuRouteTrack.setSubId(diySub.getSubId());
                    wuliuRouteTrack.setSubType(1);
                    wuliuRouteTrack.setLineType(diySub.getLineType());
                    wuliuRouteTrack.setDispatchStatus(1);
                }
                wuliuRouteTrack.setCurrentLatitude("" + currentCoordinate.getLatitude());
                wuliuRouteTrack.setCurrentLongitude("" + currentCoordinate.getLongitude());
                resRouteTrackList.add(wuliuRouteTrack);
            }
        }
        return resRouteTrackList;
    }

}
