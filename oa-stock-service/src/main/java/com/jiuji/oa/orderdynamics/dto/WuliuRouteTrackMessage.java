package com.jiuji.oa.orderdynamics.dto;

import com.ch999.common.util.vo.atlas.Coordinate;
import com.jiuji.oa.wuliu.entity.WuLiuEntity;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/11/10 11:51
 */
@Data
@Accessors(chain = true)
public class WuliuRouteTrackMessage implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 物流单
     */
    private WuLiuEntity wuLiuEntity;
    /**
     * 当前位置
     */
    private Coordinate currentCoordinate;
    /**
     * 0、发货 1、签收
     * 其他值、运输中
     */
    private Integer type;
}
