package com.jiuji.oa.orderdynamics.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ZtoActionStatusEnum {
    GOT("GOT","收件", "网点揽收", 50),
    DEPARTURE("DEPARTURE","发件", "从网点或分拨中心发出", 3),
    ARRIVAL("ARRIVAL","到件", "到达网点或分拨中心", 3),
    DISPATCH("DISPATCH","派件", "业务员派送", 44),
    RETURN_SCAN("RETURN_SCAN","退件", "准备退回", 5),
    RETURN_SIGNED("RETURN_SIGNED","退件签收", "已经退回至寄件客户", 5),
    INBOUND("INBOUND","入站", "放入快递超市/自提柜/第三方代理点等", 4),
    HANDOVERSCAN_SIGNED("HANDOVERSCAN_SIGNED","第三方妥投", "入站后妥投成功", 4),
    DEPARTURE_SIGNED("DEPARTURE_SIGNED","出站签收", "客户从快递超市/自提柜/第三方代理点等取出" ,80),
    SIGNED("SIGNED","签收", "客户正常签收", 80),
    PROBLEM("PROBLEM","问题件", "网点或中心登记的问题件，问题件类型在问题件编码（problemCode）字段体现", 7),

    CREATE("CREATE","下单", "下单", 1),
    ;

    /**
     * 编码
     */
    private final String code;
    /**
     * 编码对应信息
     */
    private final String message;
    /**
     * 编码对应信息
     */
    private final String des;
    /**
     * status
     */
    private final Integer status;

    /**
     * 获取status
     * @param statusCode
     * @return
     */
    public static Integer getApiStatus(String statusCode){
        return Arrays.stream(ZtoActionStatusEnum.values())
                .filter(x -> x.getCode().equals(statusCode))
                .map(ZtoActionStatusEnum::getStatus)
                .findFirst().orElse(0);
    }

    /**
     * opcode
     * @param statusCode
     * @return
     */
    public static String getOpCode(String statusCode){
        for (ZtoActionStatusEnum ztoActionStatusEnum : values()) {
            if (ztoActionStatusEnum.getCode().equals(statusCode)) {
                return ztoActionStatusEnum.getStatus().toString();
            }
        }
        return statusCode;
    }

    public static String getMsgStatus(String statusCode){
        return Arrays.stream(ZtoActionStatusEnum.values())
                .filter(x -> x.getCode().equals(statusCode))
                .map(ZtoActionStatusEnum::getMessage)
                .findFirst().orElse("");
    }
}
