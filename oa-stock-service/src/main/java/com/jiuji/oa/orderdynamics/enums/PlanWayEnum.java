package com.jiuji.oa.orderdynamics.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum PlanWayEnum implements CodeMessageEnumInterface {

    CYCLING(1, "骑行"),
    DRIVE(2, "驾车"),
    CLIENT_PLAN(3, "客户端规划");

    /**
     * 编码WuliuRouteTrack
     */
    private final Integer code;
    /**
     * 编码对应信息
     */
    private final String message;
}
