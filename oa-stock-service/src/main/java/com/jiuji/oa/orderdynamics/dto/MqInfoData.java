package com.jiuji.oa.orderdynamics.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class MqInfoData {
    /**
     * 订单id
     */
    private Long subId;
    /**
     * 1：新机订单，3：良品订单
     */
    private Integer subType;
    /**
     * 业务id
     */
    private Long businessId;
    /**
     * 业务类型
     */
    private Integer businessNode;
    /**
     * 业务节点
     */
    private Integer node;

    /**
     * 物流单号
     */
    private Integer wuliuId;

    /**
     * 日志内容
     */
    private String message ;

    /**
     * 扩展接口
     */
    private ExtendInfo extend;



}
