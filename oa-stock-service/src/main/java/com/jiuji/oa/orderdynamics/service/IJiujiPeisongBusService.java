package com.jiuji.oa.orderdynamics.service;

import com.jiuji.oa.meituan.vo.req.LngLatReportReqVO;
import com.jiuji.oa.meituan.vo.res.HasSendingResVO;
import com.jiuji.oa.meituan.vo.res.LngLatReportResVO;
import com.jiuji.oa.nc.common.bo.OaUserBO;

/**
 * 九机配送信息服务
 * <AUTHOR>
 */
public interface IJiujiPeisongBusService {

    /**
     * 九机配送订单九机配送经纬度上报
     * @param reqVo
     * @return
     */
    LngLatReportResVO lngLatReport(LngLatReportReqVO reqVo);

    /**
     * 是否有正在派送的查询接口
     * @return
     */
    HasSendingResVO queryHasSending();

    /**
     * 用户是否有正在派送的查询接口
     * @return
     */
    boolean queryHasSendingCache(OaUserBO user);

    /**
     * 定时处理九机配送订单
     * @return
     */
    boolean handleJiujiPeisong();

    /**
     * 九机快送
     * @param dto
     */
    void saveJiuJiPeisong(LngLatReportReqVO dto);

    /**
     * 刷新配送缓存
     * @param user
     * @return
     */
    void refreshHasSendingCache(OaUserBO user, Integer type);
    /**
     * 刷新配送缓存
     * @param wuliuId
     * @return
     */
    void refreshHasSendingCache(Integer wuliuId);
}
