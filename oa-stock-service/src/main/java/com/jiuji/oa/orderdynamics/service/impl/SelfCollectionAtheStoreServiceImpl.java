package com.jiuji.oa.orderdynamics.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.ch999.common.util.vo.atlas.Coordinate;
import com.jiuji.oa.nc.common.exception.CustomizeException;
import com.jiuji.oa.nc.user.po.Areainfo;
import com.jiuji.oa.nc.user.service.IAreaInfoService;
import com.jiuji.oa.orderdynamics.entity.WuliuRouteTrack;
import com.jiuji.oa.orderdynamics.enums.*;
import com.jiuji.oa.orderdynamics.mapper.WuliuRouteTrackMapper;
import com.jiuji.oa.orderdynamics.service.IWuliuMapTrackBusService;
import com.jiuji.oa.orderdynamics.service.LongitudeAndLatitudeService;
import com.jiuji.oa.orderdynamics.service.WuliuRouteTrackService;
import com.jiuji.oa.orderdynamics.vo.request.LongitudeAndLatitudeCondition;
import com.jiuji.oa.orderdynamics.vo.response.AppPoint;
import com.jiuji.oa.orderdynamics.vo.response.LongitudeAndLatitude;
import com.jiuji.oa.orderdynamics.vo.response.LongitudeAndLatitudeResult;
import com.jiuji.oa.orderdynamics.vo.response.Point;
import com.jiuji.oa.stock.purchase.config.PurchaseImportConfig;
import com.jiuji.oa.wuliu.entity.RecoverMarketinfo;
import com.jiuji.oa.wuliu.service.IRecoverMarketinfoService;
import com.jiuji.oa.wuliu.service.IWuLiuService;
import com.jiuji.oa.wuliu.vo.WuLiuSubDTO;
import com.jiuji.tc.common.vo.R;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 到店自取实现
 * <AUTHOR>
 */
@Slf4j
@Service(value = "SelfCollectionAtheStoreServiceImpl")
public class SelfCollectionAtheStoreServiceImpl extends LongitudeAndLatitudeCommonServiceImpl implements LongitudeAndLatitudeService {
    @Resource
    private WuliuRouteTrackService wuliuRouteTrackService;
    @Resource
    private WuliuRouteTrackMapper wuliuRouteTrackMapper;
    @Resource
    private IWuliuMapTrackBusService wuliuMapTrackBusService;
    @Resource
    private PurchaseImportConfig purchaseImportConfig;
    @Resource
    private IRecoverMarketinfoService marketinfoService;

    @Resource
    private IWuLiuService wuLiuService;
    @Resource
    private IAreaInfoService areaInfoService;
    private static final Integer TWO = 2;
    private static final Integer ZERO = 0;

    /**
     * 判断是否存在调拨单
     * @param longitudeAndLatitudeCondition
     * @return
     */
    private Boolean haveAllocation(LongitudeAndLatitudeCondition longitudeAndLatitudeCondition){
        Long subId = longitudeAndLatitudeCondition.getSubId();
        Integer subType = longitudeAndLatitudeCondition.getSubType();
        //进行良品订单和新机订单的判断是否存在调拨的情况
        if(WuliuRouteTrackTypeEnum.GOOD_PRODUCTS.getCode().equals(subType)){
            //良品单逻辑处理(如果如果过有数据那就说明调拨完成,)
            Integer number = wuliuRouteTrackMapper.allocationGoodProducts(subId);
            if(ObjectUtil.isEmpty(number)){
                return Boolean.TRUE;
            } else {
                return Boolean.FALSE;
            }
        } else if(WuliuRouteTrackTypeEnum.NEW_MACHINE.getCode().equals(subType)) {
            HashMap<String, Object> parm = new HashMap<>(TWO);
            parm.put("subId",subId);
            String result;
            try {
                //结果返回true表示存在没有调拨
                 result = HttpUtil.post(purchaseImportConfig.getOrderDynamicsUrl(), JSONUtil.toJsonStr(parm));
                log.warn("订单动态接口调用OA判断是否存在调拨情况传入参数：{}，返回结果：{}", JSONUtil.toJsonStr(parm),result);
            } catch (Exception e){
                log.warn("订单动态接口调用OA判断是否存在调拨情况错误 传入参数：{}", JSONUtil.toJsonStr(parm),e);
                throw new CustomizeException("订单动态接口调用OA判断是否存在调拨情况错误,订单号："+subId);
            }
            if(StringUtils.isEmpty(result)){
                throw new CustomizeException("订单动态接口调用OA判断是否存在调拨情况-返回结果为空,订单号："+subId);
            }
            R<Boolean> booleanR = JSON.parseObject(result, new TypeReference<R<Boolean>>() {});
            int code = booleanR.getCode();
            if(code!=ZERO){
                throw new CustomizeException(booleanR.getMsg()+","+booleanR.getUserMsg());
            }
            return !booleanR.getData();
        }
        return Boolean.TRUE;
    }


    /**
     * 到店自取的情况下 调拨的逻辑的和送货上门的逻辑一致，所以在父类方里创建公共使用的方法
     * @param longitudeAndLatitudeCondition
     * @return
     */
    @Override
    public LongitudeAndLatitudeResult queryLongitudeAndLatitude(LongitudeAndLatitudeCondition longitudeAndLatitudeCondition) {
        Long subId = longitudeAndLatitudeCondition.getSubId();
        Integer subType = longitudeAndLatitudeCondition.getSubType();
        LongitudeAndLatitudeResult longitudeAndLatitudeResult = new LongitudeAndLatitudeResult();
        longitudeAndLatitudeResult.setAppPoints(new ArrayList<>());
        //判断该订单是否存在调拨的情况
        Boolean isAllocation = haveAllocation(longitudeAndLatitudeCondition);
        if(isAllocation){
            //存在调拨那就行进行调拨的规划,根据订单查询是否存在经纬度信息（不存在直接返回空对象）
            List<WuliuRouteTrack> list = wuliuRouteTrackService.getWuliuRouteTrackList(subId,subType);
            if (CollectionUtil.isEmpty(list) || list.stream().noneMatch(wuliuRouteTrack -> LineTypeEnum.ALLOCATION.getCode().equals(wuliuRouteTrack.getLineType()))) {
                list = wuliuMapTrackBusService.initWuliuRouteTrack(subId.intValue(), subType);
            }
            if (CollectionUtil.isNotEmpty(list)) {
                for (WuliuRouteTrack item: list) {
                    if (LineTypeEnum.ALLOCATION.getCode().equals(item.getLineType()) && !Objects.equals(1, item.getStatus())) {
                        Coordinate coordinate = wuLiuService.getSendPositionByWuliuId(item.getWuliuId());
                        if (Objects.nonNull(coordinate)) {
                            item.setSendLongitude(Convert.toStr(coordinate.getLongitude()));
                            item.setSendLatitude(Convert.toStr(coordinate.getLatitude()));
                        }
                        //父类调拨逻辑
                        handleAllocation(longitudeAndLatitudeResult, item);
                    }
                }
            }
        } else {
            //不存在调拨那就进行到店自取的规划
            longitudeAndLatitudeResult=selfRetrieval(longitudeAndLatitudeCondition,longitudeAndLatitudeResult);
        }
        return longitudeAndLatitudeResult;
    }
    /**
     * 到店自取的路线规划
     * @param longitudeAndLatitudeCondition
     * @param longitudeAndLatitudeResult
     * @return
     */
    private LongitudeAndLatitudeResult selfRetrieval(LongitudeAndLatitudeCondition longitudeAndLatitudeCondition,LongitudeAndLatitudeResult longitudeAndLatitudeResult){
        Long subId = longitudeAndLatitudeCondition.getSubId();
        //当前经度
        String longitude = longitudeAndLatitudeCondition.getLongitude();
        //当前维度
        String latitude = longitudeAndLatitudeCondition.getLatitude();
        //判断该订单是良品单还是新机单
        Integer subType = longitudeAndLatitudeCondition.getSubType();
        Integer areaId = Integer.MAX_VALUE;
        if(WebSubTypeEnum.ORDER.getCode().equals(subType)){
            WuLiuSubDTO sub = Optional.ofNullable(wuLiuService.getWuLiuSub(subId.intValue())).orElse(new WuLiuSubDTO());
            areaId = sub.getAreaid();
        } else if (WebSubTypeEnum.GOOD_PRODUCT_ORDER.getCode().equals(subType)){
            RecoverMarketinfo recoverMarketinfo = Optional.ofNullable(marketinfoService.getById(subId)).orElse(new RecoverMarketinfo());
            areaId = recoverMarketinfo.getAreaid();
        }
        //获取到门店经纬度
        Areainfo area = Optional.ofNullable(areaInfoService.getOne(areaId)).orElse(new Areainfo());
        String position = area.getPosition();
        //如果过门店没有设置经纬度数据那就直接不显示
        if(StringUtils.isEmpty(position)){
            return longitudeAndLatitudeResult;
        }
        //通过工具类进行经纬度的转换
        String[] split = position.split(",");
        if(split.length!=TWO){
            return longitudeAndLatitudeResult;
        }
        //门店的点
        Point pointEnd = new Point();
        LongitudeAndLatitude andLatitude = new LongitudeAndLatitude(split[0], split[1]);
        pointEnd.setLongitudeAndLatitude(andLatitude)
                .setSubType(PointSubTypeEnum.ZERO_TYPE.getCode())
                .setType(PointTypeEnum.STORE.getCode());
        //如果经纬度都不为空的情况就开始绘制用户上门自取的路线
        if(StringUtils.isNotEmpty(longitude) && StringUtils.isNotEmpty(latitude)){
            handleLongitudeAndLatitude(longitude,latitude,pointEnd,longitudeAndLatitudeResult);
        }
        //如果没有传经纬度的情况做返回空数组的处理
        if(StringUtils.isEmpty(longitude) && StringUtils.isEmpty(latitude)){
            handleNoLongitudeAndLatitude(longitudeAndLatitudeResult);
        }
        return longitudeAndLatitudeResult;
    }


    /**
     * 处理有经纬度的情况
     * @param longitude
     * @param latitude
     * @param pointEnd
     * @param longitudeAndLatitudeResult
     */
    private void handleLongitudeAndLatitude(String longitude,String latitude,Point pointEnd,LongitudeAndLatitudeResult longitudeAndLatitudeResult){
        //客户点的规划
        Point pointUser = new Point();
        LongitudeAndLatitude longitudeAndLatitude = new LongitudeAndLatitude(longitude,latitude);
        pointUser.setLongitudeAndLatitude(longitudeAndLatitude)
                .setSubType(PointSubTypeEnum.FOUR_TYPE.getCode())
                .setType(PointTypeEnum.USER.getCode());
        /**
         * 处理app所需要数据
         */
        AppPoint appPoint = new AppPoint();
        appPoint.setInProcessPoint(pointUser)
                .setStartIngPoint(pointUser.clone())
                .setEndPoint(pointEnd);
        List<AppPoint> appPoints = longitudeAndLatitudeResult.getAppPoints();
        appPoints.add(appPoint);
    }

    /**
     * 处理没有经纬度的情况
     * @param longitudeAndLatitudeResult
     */
    private void handleNoLongitudeAndLatitude(LongitudeAndLatitudeResult longitudeAndLatitudeResult){
        longitudeAndLatitudeResult.setAppPoints(new ArrayList<>());
    }
}
