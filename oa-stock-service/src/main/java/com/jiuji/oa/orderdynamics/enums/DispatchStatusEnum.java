package com.jiuji.oa.orderdynamics.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum DispatchStatusEnum implements CodeMessageEnumInterface {

    ALLOCATION(1, "运输中"),
    DELIVERY(2, "配送中");

    /**
     * 编码WuliuRouteTrack
     */
    private final Integer code;
    /**
     * 编码对应信息
     */
    private final String message;
}
