package com.jiuji.oa.orderdynamics.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/6/17 16:17
 */
@Data
public class ZtoRoutePushDataDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 运单号
     */
    private String billCode;
    /**
     * 事件类型
     */
    private String action;
    /**
     * 操作节点编码
     */
    private String facilityCode;
    /**
     * 操作节点名称
     */
    private String facilityName;
    /**
     * 操作节点服务电话
     */
    private String facilityContactPhone;
    /**
     * 操作节点所属城市
     */
    private String city;
    /**
     * 操作时间
     */
    private String actionTime;
    /**
     * 下一站编码
     */
    private String nextNodeCode;
    /**
     * 下一站名称
     */
    private String nextNodeName;
    /**
     * 下一站城市
     */
    private String nextCity;
    /**
     * 物流详情描述
     */
    private String desc;
    /**
     * 快递员名称
     */
    private String courier;
    /**
     * 快递员电话
     */
    private String courierPhone;
    /**
     * 签收人名称
     */
    private String expressSigner;
    /**
     * 地址
     */
    private String address;
    /**
     * 问题件编码
     *     A1: 送无人，电话联系不上
     *     A2:地址不详，电话联系不上
     *     A3:地址错误，电话错误/停机
     *     A4:无电话、无收件人/无此人
     *     A5: 客户拒收
     *     A6: 拒付费用
     *     A7: 客户强制验视内件
     *     A8: 两次免费派送，第三次有偿派送
     *     A9: 已电联，按预约时间派送
     *     A16:  节假日快件，假后派送
     *     A10:  自提件
     *     A11:  自取未取
     *     A12:  客户要求改地址
     *     A14:  客户地址禁止快递出入
     *     C1:  自然灾害
     *     C2:  政府干涉
     *     C5:  疫情管控区域快件退回
     */
    private String problemCode;
    /**
     * 备注
     */
    private String remark;
    /**
     * 备注1
     */
    private String remark1;
    /**
     * 备注2
     */
    private String remark2;


}
