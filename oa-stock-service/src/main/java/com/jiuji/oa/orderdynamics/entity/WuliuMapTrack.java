package com.jiuji.oa.orderdynamics.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "wuliu_map_track")
public class WuliuMapTrack extends BaseEntity {


    /**
     * wuliu_route_track表的id
     */
    @TableField(value = "route_track_id")
    private Long routeTrackId;

    /**
     * 当前纬度
     */
    @TableField(value = "current_latitude")
    private String currentLatitude;

    /**
     * 当前经度
     */
    @TableField(value = "current_longitude")
    private String currentLongitude;

    /**
     * 腾讯地图路线规划坐标点串
     */
    @TableField(value = "polyline")
    private String polyline;



}
