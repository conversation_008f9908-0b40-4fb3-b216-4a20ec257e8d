package com.jiuji.oa.orderdynamics.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum WuLiuRouteTrackStatusEnum implements CodeMessageEnumInterface {

   NO_SIGN_IN(0, "未签收"),
    SIGN_IN (1, "已签收");

    /**
     * 编码WuliuRouteTrack
     */
    private final Integer code;
    /**
     * 编码对应信息
     */
    private final String message;
}
