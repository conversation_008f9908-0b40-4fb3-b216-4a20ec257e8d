package com.jiuji.oa.orderdynamics.service;

import com.jiuji.oa.orderdynamics.dto.MqInfoData;
import com.jiuji.oa.orderdynamics.dto.WuliuRouteTrackMessage;
import com.jiuji.oa.orderdynamics.entity.WuliuRouteTrack;
import com.jiuji.oa.orderdynamics.vo.request.QueryWuliuBySubReqVO;
import com.jiuji.oa.orderdynamics.vo.response.QueryWuliuBySubResVO;
import com.jiuji.oa.stock.logistics.order.vo.ExpressPushVO;
import com.jiuji.oa.wuliu.entity.WuLiuEntity;

import java.util.List;

/**
 * 物流地图轨迹
 * <AUTHOR>
 */
public interface IWuliuMapTrackBusService {
    /**
     *处理快递轨迹推送
     */
    void handleExpressTrack(ExpressPushVO expressPush);

    /**
     *处理Diy定单轨迹推送
     */
    void handleDiyExpressTrack(ExpressPushVO expressPush, Integer type);

    /**
     * 初始化轨迹节点
     * @param subId
     * @param subType
     * @return
     */
    List<WuliuRouteTrack> initWuliuRouteTrack(Integer subId, Integer subType);

    /**
     * 定时查询跑腿骑手实时位置
     */
    void handleThirdDelivery();

    /**
     * 九机快送处理订单轨迹
     * @param wuliuList
     * @param currentLocation
     */
    void handleJiujiExpressTrack(List<WuLiuEntity> wuliuList, String currentLocation);

    /**
     * 快递单
     * 未发货/送达
     * @param nu
     */
    void handleDeliveryExpress(String nu, Integer type);

    /**
     * 物流单
     * 未发货/送达
     * @param wuliuId
     * @param type
     */
    void handleDeliveryExpress(Integer wuliuId, Integer type, Integer transferType);

    /**
     * 订单查询物流单
     * @return
     */
    QueryWuliuBySubResVO queryWuliuBySubId(QueryWuliuBySubReqVO req);

    /**
     * 订单推送物流节点信息
     * @param mqInfoData
     */
    void handleWuliuInfoMsg(MqInfoData mqInfoData);

    /**
     * 处理订单物流轨迹信息
     * @param wuliuRouteTrackMessage
     */
    void handleWuliuRouteTrack(WuliuRouteTrackMessage wuliuRouteTrackMessage);
}
