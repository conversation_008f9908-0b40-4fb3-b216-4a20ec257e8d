package com.jiuji.oa.orderdynamics.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.orderdynamics.entity.WuliuRouteTrack;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface WuliuRouteTrackService extends IService<WuliuRouteTrack> {
    /**
     * 查询订单查询物流路由轨迹
     *
     * @param subIdList
     * @return
     */
     List<WuliuRouteTrack> getWuliuRouteTrackBySubIds(List<Long> subIdList);

    /**
     * 物流单查询物流路由轨迹
     *
     * @param wuLiuIdList
     * @return
     */
    List<WuliuRouteTrack> getWuliuRouteTrackByWuliuIds(List<Long> wuLiuIdList);

    /**
     * 根据订单id和 订单类型查询
     * @param subId
     * @param subType
     * @return
     */
    List<WuliuRouteTrack> getWuliuRouteTrackList(Long subId,Integer subType);


    boolean completeWuliuRouteTrack(WuliuRouteTrack wuliuRouteTrack);
}
