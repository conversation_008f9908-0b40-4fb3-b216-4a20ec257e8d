package com.jiuji.oa.orderdynamics.service.impl;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.jiuji.oa.meituan.service.IMeituanShangouPeisongBusService;
import com.jiuji.oa.meituan.vo.req.LngLatReportReqVO;
import com.jiuji.oa.meituan.vo.res.HasSendingResVO;
import com.jiuji.oa.meituan.vo.res.LngLatReportResVO;
import com.jiuji.oa.nc.common.bo.OaUserBO;
import com.jiuji.oa.nc.common.config.rabbitmq.RabbitMqConfig;
import com.jiuji.oa.nc.common.constant.DataSourceConstants;
import com.jiuji.oa.nc.common.exception.CustomizeException;
import com.jiuji.oa.nc.user.service.Ch999UserService;
import com.jiuji.oa.orderdynamics.mapper.JiujiPeisongMapper;
import com.jiuji.oa.orderdynamics.service.IJiujiPeisongBusService;
import com.jiuji.oa.orderdynamics.service.IWuliuMapTrackBusService;
import com.jiuji.oa.stock.common.util.JacksonJsonUtils;
import com.jiuji.oa.stock.common.util.SysUtils;
import com.jiuji.oa.wuliu.entity.WuLiuEntity;
import com.jiuji.oa.wuliu.service.IWuLiuService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * 九机配送信息服务
 *
 * <AUTHOR>
 * @date 2022/4/29 14:02
 */
@Service
@Slf4j
@DS("ch999oanew")
public class JiujiPeisongBusServiceImpl implements IJiujiPeisongBusService {
    private static final String PEISONG_REN_ALL_KEY = "stock:jiuji:peisong:all";

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private RabbitTemplate rabbitTemplate;

    @Resource
    private Ch999UserService ch999UserService;

    @Resource
    private JiujiPeisongMapper jiujiPeisongMapper;
    @Resource
    private IMeituanShangouPeisongBusService meituanShangouPeisongBusService;
    @Resource
    private IWuliuMapTrackBusService wuliuMapTrackBusService;


    /**
     * 九机配送经纬度上报
     *
     * @param reqVo
     * @return
     */
    @Override
    public LngLatReportResVO lngLatReport(LngLatReportReqVO reqVo) {
        OaUserBO user = SysUtils.getUser();
        if (Objects.isNull(user)) {
            throw new CustomizeException("当前用户没有登录，请登录后操作");
        }
        reqVo.setUserId(user.getUserId());
        reqVo.setUserName(user.getUserName());
        reqVo.setPlatform(user.getPlatformName());
        String jsonMesg = JacksonJsonUtils.toJson(reqVo);
        log.info("经纬度上报，发送消息jsonMesg={}", jsonMesg);
        //美团闪购订单
        rabbitTemplate.convertAndSend(RabbitMqConfig.JIUJI_MEITUAN_LNGLAT_REPORT, jsonMesg);
        //九机快送，定时配送
        rabbitTemplate.convertAndSend(RabbitMqConfig.JIUJI_LNGLAT_REPORT, jsonMesg);
        LngLatReportResVO res = new LngLatReportResVO();
        HasSendingResVO hasSendingRes = this.queryHasSending();
        res.setNeedToReport(hasSendingRes.getNeedToReport());
        return res;
    }

    /**
     * 是否有正在派送的查询接口
     *
     * @return
     */
    @Override
    public HasSendingResVO queryHasSending() {
        OaUserBO user = SysUtils.getUser();
        if (Objects.isNull(user)) {
            throw new CustomizeException("当前用户没有登录，请登录后操作");
        }
        HasSendingResVO res = new HasSendingResVO();
        //查询是否存在正在配送的美团订单
        boolean meituanHasSending = meituanShangouPeisongBusService.queryHasSendingCache(user);
        if (meituanHasSending) {
            res.setNeedToReport(true);
            log.info("查询是否需要上报经纬度，userName={},res={}", user.getUserName(), res);
            return res;
        }
        //订单、良品单，九机快送，定时配送
        boolean hasSending = queryHasSendingCache(user);
        res.setNeedToReport(hasSending);
        log.info("查询是否需要上报经纬度，userName={},res={}", user.getUserName(), res);
        return res;
    }

    /**
     * 定时处理美团闪购订单
     *
     * @return
     */
    @Override
    public boolean handleJiujiPeisong() {
        List<String> paijianrenList = jiujiPeisongMapper.selectJiujiPeisongPaijianrenByPaijianren();
        if (CollectionUtils.isNotEmpty(paijianrenList)) {
            String[] stringArray = new String[paijianrenList.size()];
            stringRedisTemplate.delete(PEISONG_REN_ALL_KEY);
            stringRedisTemplate.opsForSet().add(PEISONG_REN_ALL_KEY, paijianrenList.toArray(stringArray));
        }
        return true;
    }

    /**
     * 九机快送
     *
     * @param dto
     */
    @Override
    @DS("ch999oanew")
    public void saveJiuJiPeisong(LngLatReportReqVO dto) {
        if (Objects.isNull(dto)) {
            return;
        }
        //物流单完成
        if (Objects.nonNull(dto.getSubId()) && dto.getSubId() > 0) {
            WuLiuEntity wuLiuEntity = SpringUtil.getBean(IWuLiuService.class).getById(dto.getSubId());
            if (Objects.isNull(wuLiuEntity) || Objects.isNull(wuLiuEntity.getDanHaoBind())) {
                return;
            }
            //处理订单动态
            wuliuMapTrackBusService.handleDeliveryExpress(wuLiuEntity.getId(), 1, 0);
        }
        //查询需要派送的物流单
        List<WuLiuEntity> wuliuIdList = jiujiPeisongMapper.selectWuLiuByPaijianren(dto.getUserName());
        if (CollectionUtils.isEmpty(wuliuIdList)) {
            stringRedisTemplate.opsForSet().remove(PEISONG_REN_ALL_KEY, dto.getUserId().toString());
            return;
        }
        if (!NumberUtil.isNumber(dto.getLongitude()) || !NumberUtil.isNumber(dto.getLatitude())) {
            return;
        }
        BigDecimal lon = new BigDecimal(dto.getLongitude());
        BigDecimal lat = new BigDecimal(dto.getLongitude());
        if (lon.signum() > 0 && lat.signum() > 0) {
            wuliuMapTrackBusService.handleJiujiExpressTrack(wuliuIdList, dto.getLongitude() + StrUtil.COMMA + dto.getLatitude());
        }
    }

    /**
     * 用户正在派送的九机订单
     *
     * @param user
     * @return
     */
    @Override
    @DS(DataSourceConstants.OANEW_WRITE)
    public boolean queryHasSendingCache(OaUserBO user) {
        Boolean isMember = stringRedisTemplate.opsForSet().isMember(PEISONG_REN_ALL_KEY, user.getUserId().toString());
        if (Boolean.TRUE.equals(isMember)) {
            return true;
        }
        List<WuLiuEntity> wuliuIdList = jiujiPeisongMapper.selectWuLiuByPaijianren(user.getUserName());
        if (CollectionUtils.isNotEmpty(wuliuIdList)) {
            stringRedisTemplate.opsForSet().add(PEISONG_REN_ALL_KEY, user.getUserId().toString());
            return true;
        }
        return false;
    }

    /**
     * 刷新缓存
     *
     * @param user
     * @return
     */
    @Override
    public void refreshHasSendingCache(OaUserBO user, Integer type) {
        if (Objects.equals(0, type)) {
            stringRedisTemplate.opsForSet().remove(PEISONG_REN_ALL_KEY, user.getUserId().toString());
        } else {
            stringRedisTemplate.opsForSet().add(PEISONG_REN_ALL_KEY, user.getUserId().toString());
        }
    }

    /**
     * 刷新缓存
     *
     * @return
     */
    @Override
    @DS("ch999oanew")
    public void refreshHasSendingCache(Integer wuliuId) {
        String userId = jiujiPeisongMapper.selectPaijianrenIdByWuliuId(wuliuId);
        if (StringUtils.isNotBlank(userId)) {
            stringRedisTemplate.opsForSet().remove(PEISONG_REN_ALL_KEY, userId);
        }
    }

}
