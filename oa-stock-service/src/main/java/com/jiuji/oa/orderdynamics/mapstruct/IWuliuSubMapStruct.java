package com.jiuji.oa.orderdynamics.mapstruct;

import com.jiuji.oa.orderdynamics.dto.DiaoboWuliuSubDTO;
import com.jiuji.oa.orderdynamics.dto.MqInfoData;
import com.jiuji.oa.orderdynamics.dto.WuliuSubDTO;
import com.jiuji.oa.orderdynamics.dto.ZtoRoutePushDataDTO;
import com.jiuji.oa.orderdynamics.entity.WuliuRouteTrack;
import com.jiuji.oa.stock.logistics.order.vo.ExpressPushVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface IWuliuSubMapStruct {


    /**
     * WuliuSubDTO toMqInfoData
     * @param req
     * @return
     */
    @Mapping(target = "extend.expressNo", source = "nu")
    @Mapping(target = "extend.expressComCode", source = "com")
    MqInfoData toMqInfoData(WuliuSubDTO req);

    /**
     * DiaoboWuliuSubDTO toMqInfoData
     * @param req
     * @return
     */
    @Mapping(target = "extend.expressNo", source = "nu")
    @Mapping(target = "extend.ppid", source = "ppid")
    @Mapping(target = "extend.expressComCode", source = "com")
    @Mapping(target = "extend.diaoboSubId", source = "diaoboSubId")
    MqInfoData toMqInfoData(DiaoboWuliuSubDTO req);

    /**
     * ZtoRoutePushDataDTO toExpressPush
     * @param req
     * @return
     */
    @Mapping(target = "waybillNo", source = "billCode")
    @Mapping(target = "statusMsg", source = "desc")
    @Mapping(target = "businessType", constant = "1")
    @Mapping(target = "acceptAddress", source = "city")
    ExpressPushVO toExpressPush(ZtoRoutePushDataDTO req);

    /**
     * WuliuSubDTO toWuliuRouteTrack
     * @param req
     * @return
     */
    @Mapping(target = "dispatchStatus", constant = "2")
    @Mapping(target = "status", constant = "0")
    @Mapping(target = "image", source = "productImage")
    WuliuRouteTrack toWuliuRouteTrack(WuliuSubDTO req);
}
