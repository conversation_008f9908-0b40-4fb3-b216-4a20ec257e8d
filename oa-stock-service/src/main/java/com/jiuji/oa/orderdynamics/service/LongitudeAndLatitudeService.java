package com.jiuji.oa.orderdynamics.service;


import com.jiuji.oa.orderdynamics.entity.WuliuRouteTrack;
import com.jiuji.oa.orderdynamics.vo.request.LongitudeAndLatitudeCondition;
import com.jiuji.oa.orderdynamics.vo.response.LongitudeAndLatitudeResult;

/**
 * <AUTHOR>
 */
public interface LongitudeAndLatitudeService {

    /**
     * 查询经纬度信息
     * @param longitudeAndLatitudeCondition
     * @return
     */
    default LongitudeAndLatitudeResult queryLongitudeAndLatitude(LongitudeAndLatitudeCondition longitudeAndLatitudeCondition){
        return new LongitudeAndLatitudeResult();
    }

    /**
     * 处理调拨情况的点线
     * @param longitudeAndLatitudeResult
     * @param wuliuRouteTrack
     */
    default void handleAllocation(LongitudeAndLatitudeResult longitudeAndLatitudeResult, WuliuRouteTrack wuliuRouteTrack) {}
    /**
     * 处理重叠点的问题
     * @param longitudeAndLatitudeResult
     */
    default void handleOverlapPoint(LongitudeAndLatitudeResult longitudeAndLatitudeResult){}
}
