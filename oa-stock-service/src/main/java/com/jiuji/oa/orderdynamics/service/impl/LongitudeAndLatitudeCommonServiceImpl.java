package com.jiuji.oa.orderdynamics.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import com.jiuji.oa.orderdynamics.entity.WuliuRouteTrack;
import com.jiuji.oa.orderdynamics.enums.LineTypeEnum;
import com.jiuji.oa.orderdynamics.enums.PointSubTypeEnum;
import com.jiuji.oa.orderdynamics.enums.PointTypeEnum;
import com.jiuji.oa.orderdynamics.service.LongitudeAndLatitudeService;
import com.jiuji.oa.orderdynamics.util.TencentMapUtil;
import com.jiuji.oa.orderdynamics.vo.response.AppPoint;
import com.jiuji.oa.orderdynamics.vo.response.LongitudeAndLatitude;
import com.jiuji.oa.orderdynamics.vo.response.LongitudeAndLatitudeResult;
import com.jiuji.oa.orderdynamics.vo.response.Point;
import com.jiuji.oa.wuliu.enums.SubTypeEnum;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;


/**
 * <AUTHOR>
 */
@Service(value = "LongitudeAndLatitudeCommonServiceImpl")
public class LongitudeAndLatitudeCommonServiceImpl implements LongitudeAndLatitudeService {
    
    private static final String WEBIMGEURL="https://img.9xun.com/newstatic/";
    private static final String DEFAULT_LONGITUDEANDLATITUDE="0.000";

    /**
     * 处理重叠点的问题
     * @param longitudeAndLatitudeResult
     */
    @Override
    public void handleOverlapPoint(LongitudeAndLatitudeResult longitudeAndLatitudeResult){
        List<AppPoint> appPoints = longitudeAndLatitudeResult.getAppPoints();
        if(CollectionUtil.isNotEmpty(appPoints)){
            for (AppPoint appPoint: appPoints) {
                //获取到行进中的经纬度
                Point inProcessPoint = Optional.ofNullable(appPoint.getInProcessPoint()).orElse(new Point());
                LongitudeAndLatitude longitudeAndLatitudeInProcess = Optional.ofNullable(inProcessPoint.getLongitudeAndLatitude()).orElse(new LongitudeAndLatitude());
                String latitudeInProcess  = Optional.ofNullable(longitudeAndLatitudeInProcess.getLatitude()).orElse(DEFAULT_LONGITUDEANDLATITUDE);
                String longitudeInProcess = Optional.ofNullable(longitudeAndLatitudeInProcess.getLongitude()).orElse(DEFAULT_LONGITUDEANDLATITUDE);
                //获取到开始经纬度
                Point startIngPoint = Optional.ofNullable(appPoint.getStartIngPoint()).orElse(new Point());
                LongitudeAndLatitude longitudeAndLatitudeStart = Optional.ofNullable(startIngPoint.getLongitudeAndLatitude()).orElse(new LongitudeAndLatitude());
                String latitudeStart = Optional.ofNullable(longitudeAndLatitudeStart.getLatitude()).orElse(DEFAULT_LONGITUDEANDLATITUDE);
                String longitudeStart = Optional.ofNullable(longitudeAndLatitudeStart.getLongitude()).orElse(DEFAULT_LONGITUDEANDLATITUDE);
                //获取终点经纬度
                Point endPoint = Optional.ofNullable(appPoint.getEndPoint()).orElse(new Point());
                LongitudeAndLatitude longitudeAndLatitudeEnd = Optional.ofNullable(endPoint.getLongitudeAndLatitude()).orElse(new LongitudeAndLatitude());
                String latitudeEnd = Optional.ofNullable(longitudeAndLatitudeEnd.getLatitude()).orElse(DEFAULT_LONGITUDEANDLATITUDE);
                String longitudeEnd = Optional.ofNullable(longitudeAndLatitudeEnd.getLongitude()).orElse(DEFAULT_LONGITUDEANDLATITUDE);
                //判断开始点是否和行进中的点重合
                Boolean startAndInProcess = TencentMapUtil.currentEqreceiver(latitudeStart, longitudeStart, latitudeInProcess, longitudeInProcess);
                //判断行进中的点是否和终点重合
                Boolean endAndInProcess = TencentMapUtil.currentEqreceiver(latitudeEnd, longitudeEnd, latitudeInProcess, longitudeInProcess);
                if(endAndInProcess || startAndInProcess){
                    inProcessPoint.setType(PointTypeEnum.DEFAULT.getCode())
                            .setSubType(PointSubTypeEnum.ZERO_TYPE.getCode());
                }
            }
        }
    }
    
    /**
     * 处理调拨情况的点线
     *  @param longitudeAndLatitudeResult
     * @param wuliuRouteTrack
     */
    @Override
    public void handleAllocation(LongitudeAndLatitudeResult longitudeAndLatitudeResult, WuliuRouteTrack wuliuRouteTrack) {
        //对于点的处理
        List<AppPoint> appPoints = longitudeAndLatitudeResult.getAppPoints();
        collectAllocationPointList(wuliuRouteTrack,appPoints);

    }

    /**
     * 收集调拨点
     * @param wuliuRouteTrack
     * @return
     */
    private void collectAllocationPointList(WuliuRouteTrack wuliuRouteTrack,List<AppPoint> appPoints) {
        AppPoint appPoint = new AppPoint();
        /**
         * 调拨路线下的寄件点就是 3 -- 发货地
         */
        Point pointPlaceOfShipment = new Point();
        LongitudeAndLatitude longitudeAndLatitude = new LongitudeAndLatitude(wuliuRouteTrack.getSendLongitude(),wuliuRouteTrack.getSendLatitude());
        pointPlaceOfShipment.setLongitudeAndLatitude(longitudeAndLatitude)
                .setType(PointTypeEnum.PLACE_OF_SHIPMENT.getCode())
                .setSubType(PointSubTypeEnum.TWO_TYPE.getCode());
        TencentMapUtil.isTureOrFalse(SubTypeEnum.NEW_ORDER.getCode().equals(wuliuRouteTrack.getSubType()))
                .trueOrFalseHandle(()-> pointPlaceOfShipment.setImage(WEBIMGEURL+wuliuRouteTrack.getImage())
                        ,()-> pointPlaceOfShipment.setImage(wuliuRouteTrack.getImage()));


        /**
         * 调拨路线下的收件点就是 4 -- 门店
         */
        Point pointStore = new Point();
        LongitudeAndLatitude longitudeAndLatitudeStore = new LongitudeAndLatitude(wuliuRouteTrack.getReceiverLongitude(),wuliuRouteTrack.getReceiverLatitude());
        pointStore.setLongitudeAndLatitude(longitudeAndLatitudeStore)
                .setSubType(PointSubTypeEnum.ZERO_TYPE.getCode())
                .setType(PointTypeEnum.STORE.getCode());
        /**
         * 调拨路线下的当前点   6 -- 进行中 （货车--调拨）
         */
        Point pointHaveInHand = new Point();
        LongitudeAndLatitude longitudeAndLatitudeHaveInHand = new LongitudeAndLatitude(wuliuRouteTrack.getCurrentLongitude(),wuliuRouteTrack.getCurrentLatitude());
        pointHaveInHand.setLongitudeAndLatitude(longitudeAndLatitudeHaveInHand)
                .setType(PointTypeEnum.HAVE_IN_HAND.getCode())
                .setSubType(PointSubTypeEnum.THREE_TYPE.getCode())
                .setInfo(LineTypeEnum.ALLOCATION.getMessage());
        /**
         * 处理app所需要的点数据
         */
        appPoint.setInProcessPoint(pointHaveInHand)
                .setStartIngPoint(pointPlaceOfShipment)
                .setEndPoint(pointStore);
        appPoints.add(appPoint);

    }

}
