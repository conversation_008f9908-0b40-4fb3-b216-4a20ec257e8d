package com.jiuji.oa.orderdynamics.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/10/10 19:34
 */
@Data
public class WuliuSubDTO {
    /**
     * 物流id
     */
    private Long wuliuId;
    /**
     * 订单id
     */
    private Long subId;
    /**
     * 订单类型
     */
    private Integer subType;
    /**
     * 快递单号
     */
    private String nu;
    /**
     * 快递公司
     */
    private String com;
    /**
     * 线路类型
     */
    private Integer lineType;
    /**
     * 商品图片地址
     */
    private String productImage;
    /**
     * 订单状态
     */
    private Integer subCheck;
    /**
     * ppriceid
     */
    private Integer ppid;
    /**
     * basketId
     */
    private Long basketId;
}
