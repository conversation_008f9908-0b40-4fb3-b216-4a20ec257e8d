package com.jiuji.oa.orderdynamics.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum WuliuRouteTrackTypeEnum implements CodeMessageEnumInterface {

    NEW_MACHINE(1, "新机单"),
    GOOD_PRODUCTS(3, "良品单");

    /**
     * 编码WuliuRouteTrack
     */
    private final Integer code;
    /**
     * 编码对应信息
     */
    private final String message;
}
