package com.jiuji.oa.orderdynamics.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import com.ch999.common.util.vo.atlas.Coordinate;
import com.jiuji.oa.orderdynamics.entity.WuliuRouteTrack;
import com.jiuji.oa.orderdynamics.enums.*;
import com.jiuji.oa.orderdynamics.service.IWuliuMapTrackBusService;
import com.jiuji.oa.orderdynamics.service.LongitudeAndLatitudeService;
import com.jiuji.oa.orderdynamics.service.WuliuRouteTrackService;
import com.jiuji.oa.orderdynamics.util.TencentMapUtil;
import com.jiuji.oa.orderdynamics.vo.request.LongitudeAndLatitudeCondition;
import com.jiuji.oa.orderdynamics.vo.response.AppPoint;
import com.jiuji.oa.orderdynamics.vo.response.LongitudeAndLatitude;
import com.jiuji.oa.orderdynamics.vo.response.LongitudeAndLatitudeResult;
import com.jiuji.oa.orderdynamics.vo.response.Point;
import com.jiuji.oa.wuliu.entity.RecoverMarketinfo;
import com.jiuji.oa.wuliu.enums.SubCheckEnum;
import com.jiuji.oa.wuliu.service.IRecoverMarketinfoService;
import com.jiuji.oa.wuliu.service.IWuLiuService;
import com.jiuji.oa.wuliu.vo.WuLiuSubDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;


/**
 * 送货上门实现
 *
 * <AUTHOR>
 */
@Service("ProvideHomeDeliveryServiceImpl")
public class ProvideHomeDeliveryServiceImpl extends LongitudeAndLatitudeCommonServiceImpl implements LongitudeAndLatitudeService {

    @Resource
    private WuliuRouteTrackService wuliuRouteTrackService;
    @Resource
    private IWuLiuService wuLiuService;
    @Resource
    private IWuliuMapTrackBusService wuliuMapTrackBusService;
    @Resource
    private IRecoverMarketinfoService marketinfoService;

    private static final Long ONE=1L;

    /**
     * 查询经纬度信息
     *
     * @param longitudeAndLatitudeCondition
     * @return
     */
    @Override
    public LongitudeAndLatitudeResult queryLongitudeAndLatitude(LongitudeAndLatitudeCondition longitudeAndLatitudeCondition) {
        Long subId = longitudeAndLatitudeCondition.getSubId();
        Integer subType = longitudeAndLatitudeCondition.getSubType();
        Integer delivery = longitudeAndLatitudeCondition.getDelivery();
        LongitudeAndLatitudeResult longitudeAndLatitudeResult = new LongitudeAndLatitudeResult();
        longitudeAndLatitudeResult.setAppPoints(new ArrayList<>());
        //根据订单查询是否存在经纬度信息（不存在直接返回空对象）
        List<WuliuRouteTrack> list = wuliuRouteTrackService.getWuliuRouteTrackList(subId, subType);
        if (CollectionUtil.isEmpty(list) || list.stream().noneMatch((WuliuRouteTrack item) -> Objects.equals(LineTypeEnum.DELIVERY.getCode(), item.getLineType()))) {
            list = wuliuMapTrackBusService.initWuliuRouteTrack(subId.intValue(),subType);
        }
        if (CollectionUtil.isEmpty(list)) {
            return longitudeAndLatitudeResult;
        }
        list.forEach((WuliuRouteTrack item) -> {

            //判断该路线是（调拨路线还是配送路线）
            Integer lineType = item.getLineType();
            //订单修改下单门店或出货门店后，重新设置发货位置
            if (LineTypeEnum.DELIVERY.getCode().equals(lineType) && !Objects.equals(1, item.getStatus())) {
                Coordinate coordinate = wuLiuService.getSendPositionByWuliuId(item.getWuliuId());
                if (Objects.nonNull(coordinate)) {
                    item.setSendLongitude(Convert.toStr(coordinate.getLongitude()));
                    item.setSendLatitude(Convert.toStr(coordinate.getLatitude()));
                }
            }
            if (LineTypeEnum.ALLOCATION.getCode().equals(lineType)) {
                //调拨逻辑
                handleAllocation(longitudeAndLatitudeResult, item);
            } else if (LineTypeEnum.DELIVERY.getCode().equals(lineType)) {
                handleDelivery(longitudeAndLatitudeResult, item,delivery,subType);
            }
        });
        return longitudeAndLatitudeResult;
    }

    /**
     * 处理配送情况的点线
     *
     * @param longitudeAndLatitudeResult
     * @param wuliuRouteTrack
     */
    private void handleDelivery(LongitudeAndLatitudeResult longitudeAndLatitudeResult, WuliuRouteTrack wuliuRouteTrack,Integer delivery,Integer subType) {
        //对于点的处理
        List<AppPoint> appPoints = longitudeAndLatitudeResult.getAppPoints();
        collectDeliveryPointList(wuliuRouteTrack, appPoints,delivery,subType);
    }

    /**
     * 设置时间文案
     * @param pointStore
     * @param expectTime
     * @return
     */
    private void setExpectTimeInfo(Point pointStore,LocalDateTime expectTime){
        //判断有没有预计到货时间
        if(ObjectUtil.isEmpty(expectTime)){
            pointStore.setSubType(PointSubTypeEnum.ZERO_TYPE.getCode());
            return;
        }
        pointStore.setSubType(PointSubTypeEnum.THREE_TYPE.getCode());
        String format = expectTime.format(DateTimeFormatter.ofPattern("HH:mm"));
        String time=format+"送达";
        LocalDateTime startTime = LocalDate.now().atTime(0, 0, 0);
        LocalDateTime endTime = LocalDate.now().atTime(23, 59, 59);
        if (expectTime.isAfter(startTime) && expectTime.isBefore(endTime)) {
            pointStore.setInfo("预计今天"+time);
        } else if(expectTime.isAfter(startTime.plusDays(ONE)) && expectTime.isBefore(endTime.plusDays(ONE))){
            pointStore.setInfo("预计明天"+time);
        } else {
            int dayOfMonth = expectTime.getDayOfMonth();
            int monthValue = expectTime.getMonthValue();
            pointStore.setInfo("预计" + monthValue + "月" + dayOfMonth + "日"+time);
        }
    }

    /**
     * 收集配送点
     *
     * @param wuliuRouteTrack
     * @return
     */
    private void collectDeliveryPointList(WuliuRouteTrack wuliuRouteTrack, List<AppPoint> appPoints,Integer delivery,Integer subType) {

        /**
         *  配送路线下的寄件点就是  4 -- 门店  文案显示预计到货时间
         */
        Point pointPlaceOfShipment = new Point();
        LongitudeAndLatitude longitudeAndLatitude = new LongitudeAndLatitude(wuliuRouteTrack.getSendLongitude(), wuliuRouteTrack.getSendLatitude());
        pointPlaceOfShipment.setLongitudeAndLatitude(longitudeAndLatitude)
                .setType(PointTypeEnum.STORE.getCode())
                .setSubType(PointSubTypeEnum.ZERO_TYPE.getCode());

        /**
         * 配送路线下的收件点就是 （需要根据配送方式进行判断货车和摩托车的显示）
         */
        Point pointStore = new Point();
        LongitudeAndLatitude longitudeAndLatitudeStore = new LongitudeAndLatitude(wuliuRouteTrack.getCurrentLongitude(), wuliuRouteTrack.getCurrentLatitude());
        pointStore.setLongitudeAndLatitude(longitudeAndLatitudeStore);
        //配送方式进行判断货车和摩托车的显示
        Integer planWayEnum = TencentMapUtil.getPlanWayEnum(delivery);
        if (PlanWayEnum.DRIVE.getCode().equals(planWayEnum)) {
            pointStore.setType(PointTypeEnum.HAVE_IN_HAND.getCode())
                    .setSubType(PointSubTypeEnum.THREE_TYPE.getCode())
                    .setInfo(LineTypeEnum.DELIVERY.getMessage());
        }
        if (PlanWayEnum.CYCLING.getCode().equals(planWayEnum)) {
            //查询预计到货时间
            Long subId = Optional.ofNullable(wuliuRouteTrack.getSubId()).orElse(0L);
            //判断订单类型是新机单还是良品单
            LocalDateTime expectTime = null;
            if(WebSubTypeEnum.ORDER.getCode().equals(subType)){
                WuLiuSubDTO wuLiuSub = Optional.ofNullable(wuLiuService.getWuLiuSub(subId.intValue())).orElse(new WuLiuSubDTO());
                expectTime = wuLiuSub.getExpectTime();
            }else if(WebSubTypeEnum.GOOD_PRODUCT_ORDER.getCode().equals(subType)) {
                RecoverMarketinfo recoverMarketinfo = Optional.ofNullable(marketinfoService.getById(subId)).orElse(new RecoverMarketinfo());
                expectTime = recoverMarketinfo.getExpecttime();
            }
            //设置时间文案
            setExpectTimeInfo(pointStore,expectTime);
            pointStore.setType(PointTypeEnum.DELIVERY.getCode());
        }


        /**
         * （判断配送状态（wuliu_route_track.dispatch_status）
         */
        Point pointHaveInHand = new Point();
        LongitudeAndLatitude longitudeAndLatitudeHaveInHand = new LongitudeAndLatitude(wuliuRouteTrack.getReceiverLongitude(), wuliuRouteTrack.getReceiverLatitude());
        pointHaveInHand.setLongitudeAndLatitude(longitudeAndLatitudeHaveInHand);
        //判断当前是否已经签收根据
        Integer status = wuliuRouteTrack.getStatus();
        if (WuLiuRouteTrackStatusEnum.SIGN_IN.getCode().equals(status)) {
            pointHaveInHand.setType(PointTypeEnum.SIGNED_IN.getCode())
                    .setSubType(PointSubTypeEnum.ONE_TYPE.getCode())
                    .setInfo(WuLiuRouteTrackStatusEnum.SIGN_IN.getMessage());
        } else {
            //查询订单状态
            Long subId = Optional.ofNullable(wuliuRouteTrack.getSubId()).orElse(0L);
            //判断订单类型是新机单还是良品单
            Integer subCheck = null;
            if(WebSubTypeEnum.ORDER.getCode().equals(subType)){
                WuLiuSubDTO wuLiuSub = Optional.ofNullable(wuLiuService.getWuLiuSub(subId.intValue())).orElse(new WuLiuSubDTO());
                subCheck = wuLiuSub.getSubCheck();
            }else if(WebSubTypeEnum.GOOD_PRODUCT_ORDER.getCode().equals(subType)) {
                RecoverMarketinfo recoverMarketinfo = Optional.ofNullable(marketinfoService.getById(subId)).orElse(new RecoverMarketinfo());
                subCheck = recoverMarketinfo.getSubCheck();
            }
            //订单完成，路线修改签收
            if (Objects.equals(SubCheckEnum.ALREADY_COMPLETE.getCode(), subCheck)) {
                pointHaveInHand.setType(PointTypeEnum.SIGNED_IN.getCode())
                        .setSubType(PointSubTypeEnum.ONE_TYPE.getCode())
                        .setInfo(WuLiuRouteTrackStatusEnum.SIGN_IN.getMessage());
                status = 1;
                //修改路线为完成状态
                wuliuRouteTrackService.completeWuliuRouteTrack(wuliuRouteTrack);
            }
        }
        if (WuLiuRouteTrackStatusEnum.NO_SIGN_IN.getCode().equals(status)) {
            pointHaveInHand.setType(PointTypeEnum.UNSIGNED.getCode())
                    .setInfo(WuLiuRouteTrackStatusEnum.NO_SIGN_IN.getMessage())
                    .setSubType(PointSubTypeEnum.ONE_TYPE.getCode());
        }
        /**
         * 处理app所需要的数据类型
         */
        AppPoint appPoint = new AppPoint();
        appPoint.setStartIngPoint(pointPlaceOfShipment)
                .setInProcessPoint(pointStore)
                .setEndPoint(pointHaveInHand);
        appPoints.add(appPoint);
    }



}
