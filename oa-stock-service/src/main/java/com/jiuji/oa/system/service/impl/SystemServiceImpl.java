package com.jiuji.oa.system.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RuntimeUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.system.SystemUtil;
import com.jiuji.oa.nc.stock.service.ISmsService;
import com.jiuji.oa.system.service.SystemService;
import com.jiuji.tc.message.constant.OaMesTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.lang.management.ManagementFactory;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 系统工具
 * <AUTHOR>
 * @since 2022/6/14 15:29
 */
@Service
@Profile(value = {"jiuji","dev"})
@Slf4j
public class SystemServiceImpl implements SystemService {
    @Autowired
    private ISmsService smsService;
    @Autowired
    Environment environment;
    /***
     * 定时执行的线程池
     */
    private ScheduledThreadPoolExecutor scheduledThreadPoolExecutor = ThreadUtil.createScheduledExecutor(1);

    /**
     * 最近一次通知时间(毫秒)
     */
    private Long lastNoticeTime;
    /**
     * 预警通知的工号
     */
    private String ch999Ids = "13495,13682,13685,13774";

    /**
     * 内存预警的百分比
     */
    private int memoryWarn = 60;

    /**
     * 内存预警的时间间隔(分钟)
     */
    private int warnPeriod = 10;

    /**
     * 服务名称
     */
    private String serviceName = "stock";

    @PostConstruct
    public void initialize() {
        log.debug("初始化内存检测定时器完成");
        ThreadUtil.schedule(scheduledThreadPoolExecutor,()->this.earlyWarn(),60,10, TimeUnit.SECONDS,true);
    }

        /**
         * 系统异常预警
         */
    @Override
    public void earlyWarn(){
        Runtime runtime = Runtime.getRuntime();
        double mPer = (runtime.totalMemory() - runtime.freeMemory()) * 100.0 / runtime.maxMemory();
        // 发消息通知Java同事，{刘昊楠，张友奎,谢熊坤}
        // 时间间隔内只通知一次
        if(mPer > memoryWarn && (lastNoticeTime == null || Duration.ofMillis(System.currentTimeMillis() - lastNoticeTime).toMinutes()>warnPeriod)){
            lastNoticeTime = System.currentTimeMillis();
            smsService.sendOaMsgTo9Ji(StrUtil.format("stock-{}项目内存占比:{}%,服务器: {}:{}",environment.getProperty("spring.profiles.active"),
                    NumberUtil.roundStr(mPer,2),SystemUtil.getHostInfo().getAddress(),environment.getProperty("local.server.port")), ch999Ids, OaMesTypeEnum.YCTZ.getCode().toString());
        }
        //达到80%以上,尝试生成dump文件,每分钟一个
        String filePath = StrUtil.format("{}_jmap.dump",LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmm")));
        if(mPer > 80 && !FileUtil.exist(filePath)){
            String cmdStr = StrUtil.format("jcmd {} Dump.heap {}",StrUtil.subBefore(ManagementFactory.getRuntimeMXBean().getName(),"@",false),
                    filePath);
            //log.error("dump命令:{}",cmdStr)
            //log.error(RuntimeUtil.execForStr(cmdStr))
            smsService.sendOaMsgTo9Ji(StrUtil.format("{}-{},服务器: {}:{} ,dump命令: {}",serviceName,environment.getProperty("spring.profiles.active")
                    ,SystemUtil.getHostInfo().getAddress(),environment.getProperty("local.server.port"), cmdStr), ch999Ids, OaMesTypeEnum.YCTZ.getCode().toString());
        }

    }
}
