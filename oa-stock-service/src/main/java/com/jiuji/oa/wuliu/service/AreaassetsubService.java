package com.jiuji.oa.wuliu.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.wuliu.entity.Areaassetsub;

import java.util.List;


/**
 * AssetCommon
 * 常用资产
 *
 * <AUTHOR>
 * @date 2021-12-15
 */
public interface AreaassetsubService extends IService<Areaassetsub> {

    /**
     * C# subWLService.getCZResult 2931-2934 行
     * var query = "UPDATE AreaAssetSub SET confirmTime=GETDATE(),confirmUser=@user, subCheck=@subCheck OUTPUT inserted.pzid WHERE id=" + dr["danhaobind"].ToLongDB();
     * var assetDt = new OfficeDB().Query(query,
     * new SqlParameter("@subCheck", (int)oa999Model.AssetCommonModel.AssetSubCheckType.已收货),
     * new SqlParameter("@user", loginfo.UserName)).Tables[0];
     * @param userName
     * @param subChenkType
     * @param danHaoBind
     * @return
     */
    List<Areaassetsub> updateAreaAssetSub(String userName, Integer subChenkType, Integer danHaoBind);

    /**
     * C# AssetCommon.CreateAreaAssetSubPingzheng
     * @param subid
     * @param user
     */
    void createAreaAssetSubPingzheng(Integer subid, String user);

}
