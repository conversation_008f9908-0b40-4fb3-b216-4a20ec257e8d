package com.jiuji.oa.wuliu.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/13 15:49
 */
@Data
@Accessors(chain = true)
public class ZtoCancelDTO {
    /**
     * 返回信息
     */
    private String msg;

    /**
     * 返回code
     */
    private String statusCode;

    /**
     * 返回状态
     */
    private Boolean status;

    /**
     * 返回结果
     */
    private List<ZtoCancelItemDTO> data;
}
