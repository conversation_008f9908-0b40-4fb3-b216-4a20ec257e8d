package com.jiuji.oa.wuliu.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.wuliu.entity.WuLiuCardLogsEntity;
import com.jiuji.oa.wuliu.mapper.WuLiuCardLogsMapper;
import com.jiuji.oa.wuliu.service.IWuLiuCardLogsService;
import org.springframework.stereotype.Service;

/**
 * 银行卡日志[责任小组:财务] 服务实现类
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-11-04
 */
@Service
public class WuLiuCardLogsServiceImpl extends ServiceImpl<WuLiuCardLogsMapper, WuLiuCardLogsEntity> implements IWuLiuCardLogsService {

    /**
     * 获取预约单使用的优惠码
     *
     * @param id
     * @return String
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-04
     */
    @Override
    @DS("ch999oanew")
    public String getYuyueYouHuiMa(Integer id) {
        return baseMapper.getYuyueYouHuiMa(id);
    }
}
