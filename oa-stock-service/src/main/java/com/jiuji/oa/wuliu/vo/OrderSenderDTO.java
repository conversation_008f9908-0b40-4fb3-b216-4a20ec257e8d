package com.jiuji.oa.wuliu.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * zhongtongApiServices.OrderSender
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-16
 */
@Data
@Accessors(chain = true)
public class OrderSenderDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("company")
    private String company;

    /**
     * 发件人姓名
     */
    @ApiModelProperty("name")
    private String name;

    /**
     * 发件人手机号码
     */
    @ApiModelProperty("mobile")
    private String mobile;

    /**
     * 发件人所在城市，必须逐级指定，用英文半角逗号分隔，目前至少需要指定到区县级，如能往下精确更好，如“上海市,上海市,青浦区,华新镇,华志路,123号”
     */
    @ApiModelProperty("city")
    private String city;

    /**
     * 发件人详细地址
     */
    @ApiModelProperty("address")
    private String address;


    // EMS 创建快递单需要额外增加以下字段 开始
    /**
     *
     */
    @ApiModelProperty("prov")
    private String prov;

    /**
     *
     */
    @ApiModelProperty("county")
    private String county;
    // EMS 创建快递单需要额外增加以下字段 结束

}
