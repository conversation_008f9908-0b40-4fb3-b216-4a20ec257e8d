package com.jiuji.oa.wuliu.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * QuerySuccessDataVO
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-12-13
 */
@Setter
@Getter
public class QuerySuccessDataVO implements Serializable {

    /**
     * mt_peisong_id
     * 美团配送内部订单id
     */
    private String mtPeisongId;

    /**
     * delivery_id
     * 配送活动标识
     */
    private String deliveryId;

    /**
     * 外部订单id
     */
    private Integer status;

    /**
     * courier_name
     * 配送员姓名（订单已被骑手接单后会返回骑手信息）
     */
    private String courierName;

    /**
     * courier_phone
     * 配送员电话（订单已被骑手接单后会返回骑手信息）
     */
    private String courierPhone;

    /**
     * cancel_reason
     * 取消原因
     */
    private String cancelReason;

    /**
     * operate_time
     * 订单状态变更时间，格式为unix-timestamp
     */
    private Integer operateTime;

}
