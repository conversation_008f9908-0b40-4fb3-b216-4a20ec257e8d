package com.jiuji.oa.wuliu.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.wuliu.constant.WuLiuConstant;
import com.jiuji.oa.wuliu.entity.ShansongStoreConfigsEntity;
import com.jiuji.oa.wuliu.mapper.ShansongStoreConfigsMapper;
import com.jiuji.oa.wuliu.service.IShansongStoreConfigsService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 闪送配置
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-12-05
 */
@Service
@Slf4j
@DS("ch999oanew")
@AllArgsConstructor
public class ShansongStoreConfigsServiceImpl extends ServiceImpl<ShansongStoreConfigsMapper, ShansongStoreConfigsEntity>
implements IShansongStoreConfigsService {
    private final StringRedisTemplate stringRedisTemplate;

    /**
     * C# ShansongStoreConfigService.GetStores
     * 获取闪送配置（带redis缓存）
     *
     * @return
     */
    @Override
    public List<ShansongStoreConfigsEntity> getStoreConfigs() {
        String shansongConfigStr = stringRedisTemplate.opsForValue().get(WuLiuConstant.SHANSONG_STORE_CONFIG_KEY);

        if (StringUtils.isEmpty(shansongConfigStr)) {
            List<ShansongStoreConfigsEntity> list = this.baseMapper.selectConfigList();
            shansongConfigStr = JSONUtil.toJsonStr(list);
            stringRedisTemplate.opsForValue().set(WuLiuConstant.SHANSONG_STORE_CONFIG_KEY,shansongConfigStr,1, TimeUnit.DAYS);
            return list;
        }

        return JSONUtil.toList(shansongConfigStr, ShansongStoreConfigsEntity.class);
    }
}




