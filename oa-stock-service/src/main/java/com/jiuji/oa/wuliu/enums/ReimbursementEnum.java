package com.jiuji.oa.wuliu.enums;

import io.swagger.models.auth.In;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 报销枚举
 *
 * <AUTHOR>
 * @date 2021/10/09
 */
@Getter
@AllArgsConstructor
public enum ReimbursementEnum {
    /**
     * 等待检查
     */
    WAIT_CHECK(1,"待审核"),
    /**
     * 批准
     */
    APPROVE(2,"已同意"),
    /**
     * 拒绝
     */
    REFUSE(3,"已驳回"),
    /**
     * 结算中
     */
    ACCOUNTING(4,"结算中"),
    /**
     * 已结算
     */
    ACCOUNTED(5,"已结算");

    /**
     * 代码
     */
    private Integer code;
    /**
     * 消息
     */
    private String message;
}
