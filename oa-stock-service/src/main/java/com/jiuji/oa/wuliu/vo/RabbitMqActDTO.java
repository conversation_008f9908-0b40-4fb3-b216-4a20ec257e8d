package com.jiuji.oa.wuliu.vo;


import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;


/**
 * 通过队列调用 C# 方法数据传输实体
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-11-12
 */
@Data
@Accessors(chain = true)
public class RabbitMqActDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 调用方法
     */
    private String act;

    /**
     * 传输参数数据
     */
    private Object data;

}
