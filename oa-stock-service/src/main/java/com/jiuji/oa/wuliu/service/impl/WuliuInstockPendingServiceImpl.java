package com.jiuji.oa.wuliu.service.impl;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.jiuji.oa.nc.common.exception.CustomizeException;
import com.jiuji.oa.nc.dict.service.ISysConfigService;
import com.jiuji.oa.nc.stock.entity.ProductMkc;
import com.jiuji.oa.nc.stock.service.IProductMkcService;
import com.jiuji.oa.nc.stock.service.ISmsService;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.oa.stock.stockmanage.vo.req.WuliuInstockPendingCaigouVO;
import com.jiuji.oa.stock.stockmanage.vo.req.WuliuInstockPendingSumVO;
import com.jiuji.oa.stock.stockmanage.vo.req.WuliuInstockPendingUpdateVO;
import com.jiuji.oa.wuliu.mapper.WuliuInstockPendingMapper;
import com.jiuji.oa.wuliu.service.IWuliuInstockPendingService;
import com.jiuji.oa.wuliu.vo.TodoListMqVo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR> yao yao
 * @since 2023-10-19
 */
@Service
@RequiredArgsConstructor
@Slf4j
@DS("oanewWrite")
public class WuliuInstockPendingServiceImpl implements IWuliuInstockPendingService {

    private static final Integer[] VIP_USERS = {2, 500};
    @Resource(name = "oaAsyncRabbitTemplate")
    private final RabbitTemplate rabbitTemplate;
    @Resource
    private ISmsService smsService;

    private final ISysConfigService sysConfigService;
    @Resource
    private SysConfigClient sysConfigClient;
    @Resource
    private final IProductMkcService productMkcService;
    @Resource
    private final WuliuInstockPendingMapper wuliuInstockPendingMapper;

    @Override
    public Boolean dealPending(WuliuInstockPendingUpdateVO req) {
        Boolean jiuJi = sysConfigService.isJiuJi();
        if (!jiuJi) {
            return true;
        }
        Integer mkcId = req.getMkcId();
        if (Objects.nonNull(mkcId)){
            ProductMkc productMkc = productMkcService.getProductMkc(Convert.toLong(mkcId));
            List<WuliuInstockPendingSumVO> sum = wuliuInstockPendingMapper.getPendingSum(req);
            Map<Integer, Integer> map = sum.stream().collect(Collectors.toMap(WuliuInstockPendingSumVO::getAreaId, WuliuInstockPendingSumVO::getCount));
            Integer areaId = productMkc.getAreaid();
            Integer count = Objects.isNull(map.get(areaId)) ? 0 : map.get(areaId);
            if (count == 0){
                //删除待办 （下架因为添加该代办的功能已经下架）
              //  this.pushTodoList(areaId,areaId,2,"采购机器已签收，请在PC端在途界面转到货");
            }
        }
        Integer wuLiuId = req.getWuliuId();
        if (Objects.nonNull(wuLiuId)){
//            下架添加代办功能因为已经自动入库
//            List<WuliuInstockPendingSumVO> sum = wuliuInstockPendingMapper.getPendingSum(req);
//            for (WuliuInstockPendingSumVO w : sum) {
//                Integer areaId = w.getAreaId();
//                Integer count = w.getCount();
//                if (count > 0){
//                    //新增待办
//                    this.pushTodoList(areaId,areaId,1,"采购机器已签收，请在PC端在途界面转到货");
//                }
//            }

            List<WuliuInstockPendingCaigouVO> list =  wuliuInstockPendingMapper.getPendingCaigouList(wuLiuId);
            if (CollectionUtils.isEmpty(list)){
                return true;
            }
            String ch999Ids = Optional.ofNullable(wuliuInstockPendingMapper.selectThirdProductConfigNotifyUserIds()).orElse("");
            if (StringUtils.isEmpty(ch999Ids)){
                return true;
            }
            String moaHost = Optional.of(sysConfigClient.getValueByCode(SysConfigConstant.MOA_URL))
                    .filter(t -> t.getCode() == ResultCode.SUCCESS)
                    .map(R::getData)
                    .orElseThrow(() -> new CustomizeException("获取M端域名出错"));
            for (WuliuInstockPendingCaigouVO w : list) {
                StringBuilder sb = new StringBuilder();
                if (StringUtils.isNotEmpty(w.getNu())){
                    sb.append("快递单号：").append(w.getNu()).append(",");
                }
                sb.append("采购单：").append(w.getCaigouId()).append("（").append(w.getArea()).append("）已签收，请督促门店入库");
                String link = moaHost + "/new/#/logistics/groceries/" + w.getCaigouId();
                smsService.sendOaMsg(sb.toString(), link, ch999Ids, com.jiuji.tc.foundation.message.send.constants.OaMesTypeEnum.SYSTEM);
            }
        }
        return true;
    }

    private void pushTodoList(Integer areaId, Integer id, Integer mqKind, String msg) {
        TodoListMqVo vo = new TodoListMqVo()
                .setBusinessNo(Convert.toStr(id)) // 业务主键
                .setMqKind(mqKind) // mq消息处理类型 1 生成待办 2 删除待办 3更新
                .setMsg(msg) // 待办描述
                .setAreaId(areaId) // 审核人工号
                .setType(58); // 待办类型
        String msgMq = JSONObject.toJSONString(vo);
        rabbitTemplate.convertAndSend("office.direct.to_do_list", msgMq);
    }

}
