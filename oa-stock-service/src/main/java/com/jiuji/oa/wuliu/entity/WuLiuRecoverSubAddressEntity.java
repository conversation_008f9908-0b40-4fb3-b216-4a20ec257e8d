package com.jiuji.oa.wuliu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 良品订单地址[责任小组:回收] 实体类
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-27
 */
@Data
@Accessors(chain = true)
@TableName("RecoverSubAddress")
@ApiModel(value = "WuLiuRecoverSubAddressEntity 实体类", description = "良品订单地址[责任小组:回收] 实体类")
public class WuLiuRecoverSubAddressEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键Id
     */
    @ApiModelProperty("主键Id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 单号
     */
    @ApiModelProperty("单号")
    @TableField("sub_id")
    private Long subId;

    /**
     * 收货地址
     */
    @ApiModelProperty("收货地址")
    @TableField("Address")
    private String address;

    /**
     * 收货区域ID
     */
    @ApiModelProperty("收货区域ID")
    @TableField("cityid")
    private Integer cityid;

    /**
     * 配送人员
     */
    @ApiModelProperty("配送人员")
    @TableField("psuser")
    private String psuser;

    /**
     * 物流公司
     */
    @ApiModelProperty("物流公司")
    @TableField("wuliucompany")
    private String wuliucompany;

    /**
     * 物流号
     */
    @ApiModelProperty("物流号")
    @TableField("wuliuNo")
    private String wuliuNo;

    /**
     * 配送时间
     */
    @ApiModelProperty("配送时间")
    @TableField("paisongdtime")
    private LocalDateTime paisongdtime;

    /**
     * 配送状态
     */
    @ApiModelProperty("配送状态")
    @TableField("paisongState")
    private Integer paisongState;

    /**
     * 短信是否发送
     */
    @ApiModelProperty("短信是否发送")
    @TableField("issms_send")
    private Boolean issmsSend;

    /**
     * 等待配送时间
     */
    @ApiModelProperty("等待配送时间")
    @TableField("waitTime")
    private LocalDateTime waitTime;

    /**
     * 送达时间
     */
    @ApiModelProperty("送达时间")
    @TableField("sendTime")
    private LocalDateTime sendTime;

    /**
     * 标识 ：1、地址异常
     */
    @ApiModelProperty("标识 ：1、地址异常")
    @TableField("isSpecial")
    private Integer isSpecial;

    /**
     * 投拓类别
     */
    @ApiModelProperty("投拓类别")
    @TableField("TuotouType")
    private Integer tuotouType;

    /**
     * 添加时间
     */
    @ApiModelProperty("添加时间")
    @TableField("addTime")
    private LocalDateTime addTime;

    /**
     * 客户指定送货日期
     */
    @ApiModelProperty("客户指定送货日期")
    @TableField("userDate")
    private LocalDate userDate;

    /**
     * 客户指定送货时间段
     */
    @ApiModelProperty("客户指定送货时间段")
    @TableField("userTime")
    private Integer userTime;

    @TableField("position")
    private String position;

    @TableField("inOneHourRange")
    private Boolean inOneHourRange;

}