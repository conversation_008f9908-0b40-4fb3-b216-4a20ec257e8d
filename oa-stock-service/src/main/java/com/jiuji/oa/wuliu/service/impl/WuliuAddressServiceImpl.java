package com.jiuji.oa.wuliu.service.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ch999.common.util.atlas.AtlasUtil;
import com.ch999.common.util.vo.atlas.Coordinate;
import com.jiuji.oa.nc.user.po.Areainfo;
import com.jiuji.oa.nc.user.service.IAreaInfoService;
import com.jiuji.oa.stock.common.util.GeoCoordinateUtils;
import com.jiuji.oa.wuliu.entity.WuliuAddress;
import com.jiuji.oa.wuliu.service.IWuLiuService;
import com.jiuji.oa.wuliu.service.IWuliuAddressService;
import com.jiuji.oa.wuliu.mapper.WuliuAddressMapper;
import com.jiuji.oa.wuliu.utils.WuliuAddressUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 *
 */
@Slf4j
@Service
@DS("ch999oanew")
public class WuliuAddressServiceImpl extends ServiceImpl<WuliuAddressMapper, WuliuAddress>
implements IWuliuAddressService {
    @Resource
    private IAreaInfoService areaInfoService;

    /**
     * 快递单查询地址信息
     *
     * @param nu
     * @return
     */
    @Override
    @DS("ch999oanew")
    public WuliuAddress queryByNu(String nu) {
        return this.baseMapper.selectByNu(nu);
    }

    /**
     * 地址获取经纬度
     * @param areaId
     * @param address
     * @param cityId
     * @param type 0、优先地址，1、优先门店信息
     * @return
     */
    @Override
    @Cached(name = "stock:wuliuAddressService:getAreaCoordinate", expire = 5, timeUnit = TimeUnit.MINUTES, cacheType = CacheType.REMOTE)
    public Coordinate getAreaCoordinate(Integer areaId,
                                        String address,
                                        Integer cityId,
                                        Integer type) {
        Coordinate coordinate = null;
        Areainfo areainfo = Optional.ofNullable(areaInfoService.getAreaInfoByAreaId2(areaId)).orElseGet(Areainfo::new);
        IWuLiuService wuLiuService = SpringUtil.getBean(IWuLiuService.class);
        if (Objects.equals(1, type)) {
            if (StringUtils.isNotBlank(areainfo.getPosition())) {
                coordinate = new Coordinate(areainfo.getPosition());
            } else if (StringUtils.isNotBlank(areainfo.getCompanyAddress())){
                coordinate = AtlasUtil.translateAddress2CoordinateUsingTencent(areainfo.getCompanyAddress(),
                        WuliuAddressUtil.getCityAdress(wuLiuService.getAreaIdByCityId(areainfo.getCityid(), 1)));
            }
            if (Objects.isNull(coordinate) && StringUtils.isNotBlank(address)) {
                coordinate = AtlasUtil.translateAddress2CoordinateUsingTencent(address,
                        WuliuAddressUtil.getCityAdress(wuLiuService.getAreaIdByCityId(cityId, 1)));
            }
        } else {
            if (StringUtils.isNotBlank(address)) {
                coordinate = AtlasUtil.translateAddress2CoordinateUsingTencent(address,
                        WuliuAddressUtil.getCityAdress(wuLiuService.getAreaIdByCityId(cityId, 1)));
            }
            if (Objects.isNull(coordinate)) {
                if (StringUtils.isNotBlank(areainfo.getPosition())) {
                    coordinate = new Coordinate(areainfo.getPosition());
                } else if (StringUtils.isNotBlank(areainfo.getCompanyAddress())){
                    coordinate = AtlasUtil.translateAddress2CoordinateUsingTencent(areainfo.getCompanyAddress(),
                            WuliuAddressUtil.getCityAdress(wuLiuService.getAreaIdByCityId(areainfo.getCityid(), 1)));
                }
            }
        }
        log.info("获取位置信息areaId={}，address={},cityId={},type={},coordinate={}",areaId,address,cityId,type,coordinate);
        return coordinate;
    }

    /**
     * 地址获取经纬度
     * @param areaId
     * @param address
     * @param cityId
     * @param type 0、优先地址，1、优先门店信息
     * @return
     */
    @Override
    @Cached(name = "stock:wuliuAddressService:getAreaCoordinateV2", expire = 5, timeUnit = TimeUnit.MINUTES, cacheType = CacheType.REMOTE)
    public Coordinate getAreaCoordinateV2(Integer areaId,
                                        String address,
                                        Integer cityId,
                                        Integer type) {
        Coordinate coordinate = null;
        Areainfo areainfo = Optional.ofNullable(areaInfoService.getAreaInfoByAreaId2(areaId)).orElseGet(Areainfo::new);
        IWuLiuService wuLiuService = SpringUtil.getBean(IWuLiuService.class);
        if (Objects.equals(1, type)) {
            if (StringUtils.isNotBlank(areainfo.getPosition())) {
                coordinate = new Coordinate(areainfo.getPosition());
            } else if (StringUtils.isNotBlank(areainfo.getCompanyAddress())){
                coordinate = GeoCoordinateUtils.tencentCoordinate(areainfo.getCompanyAddress(),
                        WuliuAddressUtil.getCityAdress(wuLiuService.getAreaIdByCityId(areainfo.getCityid(), 1)));
            }
            if (Objects.isNull(coordinate) && StringUtils.isNotBlank(address)) {
                coordinate = GeoCoordinateUtils.tencentCoordinate(address,
                        WuliuAddressUtil.getCityAdress(wuLiuService.getAreaIdByCityId(cityId, 1)));
            }
        } else {
            if (StringUtils.isNotBlank(address)) {
                coordinate = GeoCoordinateUtils.tencentCoordinate(address,
                        WuliuAddressUtil.getCityAdress(wuLiuService.getAreaIdByCityId(cityId, 1)));
            }
            if (Objects.isNull(coordinate)) {
                if (StringUtils.isNotBlank(areainfo.getPosition())) {
                    coordinate = new Coordinate(areainfo.getPosition());
                } else if (StringUtils.isNotBlank(areainfo.getCompanyAddress())){
                    coordinate = GeoCoordinateUtils.tencentCoordinate(areainfo.getCompanyAddress(),
                            WuliuAddressUtil.getCityAdress(wuLiuService.getAreaIdByCityId(areainfo.getCityid(), 1)));
                }
            }
        }
        log.info("获取位置信息areaId={}，address={},cityId={},type={},coordinate={}",areaId,address,cityId,type,coordinate);
        return coordinate;
    }
}




