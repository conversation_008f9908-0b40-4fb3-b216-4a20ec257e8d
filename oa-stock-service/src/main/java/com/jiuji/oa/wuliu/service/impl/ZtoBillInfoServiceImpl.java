package com.jiuji.oa.wuliu.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.stock.common.util.JacksonJsonUtils;
import com.jiuji.oa.stock.logisticscenter.vo.res.CreateOrderResV2;
import com.jiuji.oa.wuliu.entity.ZtoBillInfo;
import com.jiuji.oa.wuliu.service.ZtoBillInfoService;
import com.jiuji.oa.wuliu.mapper.ZtoBillInfoMapper;
import com.jiuji.oa.wuliu.vo.req.WuLiuAddOrUpdateReqVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 *
 */
@Service
@Slf4j
@DS("oanewWrite")
public class ZtoBillInfoServiceImpl extends ServiceImpl<ZtoBillInfoMapper, ZtoBillInfo> implements ZtoBillInfoService{

    /**
     * 保存中通面单
     *
     * @param model
     * @param data
     */
    @Override
    @DS("oanewWrite")
    public void saveBillInfo(WuLiuAddOrUpdateReqVO model, CreateOrderResV2 data) {
        ZtoBillInfo ztoBillInfo = new ZtoBillInfo();
        ztoBillInfo.setBillCode(data.getWaybillNo());
        ztoBillInfo.setWuliuid(model.getWuliuid().longValue());
        ztoBillInfo.setBigMark(JacksonJsonUtils.toJson(data.getBigMarkInfo()));
        this.save(ztoBillInfo);
    }

    /**
     * 保存中通面单
     *
     * @param wuliuId
     * @param data
     */
    @Override
    @DS("oanewWrite")
    public void saveBillInfo(Integer wuliuId, CreateOrderResV2 data) {
        ZtoBillInfo ztoBillInfo = new ZtoBillInfo();
        ztoBillInfo.setBillCode(data.getWaybillNo());
        ztoBillInfo.setWuliuid(wuliuId.longValue());
        ztoBillInfo.setBigMark(JacksonJsonUtils.toJson(data.getBigMarkInfo()));
        this.save(ztoBillInfo);
    }
}




