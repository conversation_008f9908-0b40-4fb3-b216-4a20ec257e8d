package com.jiuji.oa.wuliu.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 顺丰快递
 *
 * <AUTHOR>
 * @date 2021-12-04
 */
@AllArgsConstructor
@Getter
public enum JiuJiSfExpressTypeEnum implements CodeMessageEnumInterface {

    /**
     * 顺丰标快（发全国0-20KG）
     */
    STANDARD(2, "顺丰标快（发全国0-20KG）"),

    /**
     * 顺丰重货（发全国20-100KG）
     */
    HEAVY_CARGO(154, "顺丰重货（发全国20-100KG）"),

    /**
     * 顺丰零担（发全国100KG+）
     */
    PARTNER(155, "顺丰零担（发全国100KG+）");


    private final Integer code;
    private final String message;

    /**
     * 顺丰子快递类型
     * @return
     */
    public static List<Integer> getDropMenuExpressType() {
        return Arrays.stream(values()).map(JiuJiSfExpressTypeEnum::getCode).collect(Collectors.toList());
    }
}
