package com.jiuji.oa.wuliu.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName wuliu_related_record
 */
@TableName(value ="wuliu_related_record")
@Data
public class WuliuRelatedRecord implements Serializable {
    /**
     * 
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 新增时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 1表示删除，0表示未删除
     */
    @TableLogic
    @TableField(value = "is_del")
    private Boolean isDel;

    /**
     * 物流单号
     */
    @TableField(value = "wuliu_id")
    private Integer wuliuId;

    /**
     * 业务类型
     */
    @TableField(value = "business_type")
    private Integer businessType;

    /**
     * 业务单号
     */
    @TableField(value = "business_id")
    private Integer businessId;

    /**
     * 预计备货时间
     */
    @TableField(value = "estimated_stocking_time")
    private LocalDateTime estimatedStockingTime;

    /**
     * 实际备货时间
     */
    @TableField(value = "actual_stocking_time")
    private LocalDateTime actualStockingTime;

    /**
     * 预计发货时间
     */
    @TableField(value = "estimated_shipping_time")
    private LocalDateTime estimatedShippingTime;

    /**
     * 实际发货时间
     */
    @TableField(value = "actual_shipping_time")
    private LocalDateTime actualShippingTime;

    /**
     * 预计送达时间
     */
    @TableField(value = "estimated_delivery_time")
    private LocalDateTime estimatedDeliveryTime;

    /**
     * 实际送达时间
     */
    @TableField(value = "actual_delivery_time")
    private LocalDateTime actualDeliveryTime;

    /**
     * 预计收货时间
     */
    @TableField(value = "estimated_receipt_time")
    private LocalDateTime estimatedReceiptTime;

    /**
     * 实际收货时间
     */
    @TableField(value = "actual_receipt_time")
    private LocalDateTime actualReceiptTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}