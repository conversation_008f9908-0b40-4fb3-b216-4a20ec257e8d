package com.jiuji.oa.wuliu.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 
 * @TableName wuliu_extend_info
 */
@TableName(value ="wuliu_extend_info")
@Data
public class WuliuExtendInfo implements Serializable {
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 物流id
     */
    private Integer wuliuId;

    /**
     * 寄件地址经纬度
     */
    private String sendPosition;

    /**
     * 收件地址经纬度
     */
    private String receivePosition;

    /**
     * 骑行距离
     */
    private Long distance;

    /**
     * 配送成本
     */
    private BigDecimal distributionCost;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 寄详细地址
     */
    @TableField(value = "sdetailed_address")
    private String sdetailedAddress;
    /**
     * 收详细地址
     */
    @TableField(value = "rdetailed_address")
    private String rdetailedAddress;

    /**
     * 删除标识
     */
    @TableLogic
    @TableField(value = "is_delete")
    private Boolean deleteFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}