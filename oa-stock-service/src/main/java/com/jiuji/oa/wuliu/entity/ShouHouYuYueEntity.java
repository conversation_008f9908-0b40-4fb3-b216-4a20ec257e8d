package com.jiuji.oa.wuliu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 售后预约表,责任小组：销售
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-13
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@TableName("shouhou_yuyue")
public class ShouHouYuYueEntity extends Model<ShouHouYuYueEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键Id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 服务方式
     */
    private Integer stype;

    /**
     * 客望处理方式
     */
    private Integer kind;

    /**
     * 状态  1未确认，2已确认，3完成
     */
    private Integer stats;

    /**
     * 客户名称
     */
    private String username;

    /**
     * 客户手机
     */
    private String mobile;

    /**
     * 预约开始时间
     */
    private LocalDateTime stime;

    /**
     * 预约结束时间
     */
    private LocalDateTime etime;

    /**
     * 配送方式
     */
    private Integer delivery;

    /**
     * 定单号
     */
    private Integer subId;

    /**
     * 商品规格id
     */
    private Integer basketId;

    /**
     * 商品型 号规格ID
     */
    private Integer ppriceid;

    /**
     * 串号
     */
    private String imei;

    /**
     * 是否大件
     */
    @TableField("ismobile")
    private Boolean mobileFlag;

    /**
     * 备注
     */
    private String comment;

    /**
     * 提交时间
     */
    private LocalDateTime dtime;

    /**
     * 故障描述
     */
    private String problem;

    /**
     * 预约录入人
     */
    private String inuser;

    /**
     * 售后id
     */
    private Integer shouhouId;

    /**
     * 机型名称
     */
    private String name;

    /**
     * 机型颜色
     */
    private String color;

    /**
     * 处理地区
     */
    private String area;

    /**
     * 刪除标识
     */
    @TableField("isdel")
    private Boolean del;

    /**
     * 关联物流单
     */
    private Integer wuliuid;

    /**
     * 退款信息  ， 用YuyueTuiData类系列化
     */
    private String tuidata;

    /**
     * 用户id
     */
    private Integer userid;

    /**
     * 确认人
     */
    private String checkUser;

    /**
     * 预出库PPID
     */
    private Integer yPpriceid;

    /**
     * 预出库地区
     */
    @TableField("y_mkcArea")
    private String yMkcarea;

    /**
     * 小件维修ID
     */
    private Integer smallproid;

    /**
     * 地区id
     */
    private Integer areaid;

    /**
     * 预出库地区
     */
    @TableField("y_mkcAreaId")
    private Integer yMkcareaid;

    /**
     * 无用字段
     */
    private Integer oldyuyueid;

    /**
     * 是否备份数据
     */
    @TableField("isBakData")
    private Integer isBakData;

    /**
     * 是否已发送短信
     */
    @TableField("issend")
    private Boolean send;

    /**
     * 未使用字段
     */
    private Boolean xunitui;

    /**
     * 订单类型
     */
    @TableField("orderType")
    private Integer orderType;

    /**
     * 预约维修ppid
     */
    @TableField("yuyuePPids")
    private String yuyuePpids;

    /**
     * 上门维修人
     */
    @TableField("enterUser")
    private String enterUser;

    /**
     * 上门维修时间
     */
    @TableField("enterTime")
    private LocalDateTime enterTime;

    /**
     * 机型ID
     */
    @TableField("productId")
    private Integer productId;

    /**
     * 客服确认时间
     */
    private LocalDateTime fchecktime;

    /**
     * 是否中邮入口
     */
    @TableField("iszy")
    private Boolean zy;

    /**
     * 中邮预约商品类型（1：按数量，2：按机型）
     */
    private Integer zyptype;

    /**
     * 中邮预约商品数量
     */
    private Integer zypnum;

    /**
     * 中油售后ID,已无用
     */
    @TableField("zyShouhouIDs")
    private String zyShouhouIds;

    /**
     * 上门取件时间
     */
    private LocalDateTime kdtime;

    /**
     * 快递类型
     */
    private Integer kdtype;

    /**
     * 快递单
     */
    private String kuaididan;

    /**
     * 物流
     */
    private String wuliyou;

    /**
     * 是否免费
     */
    private Boolean onfree;

    /**
     * 快递公司
     */
    private String kuaidigongsi;

    /**
     * 提交统计参数
     */
    private String record;

    /**
     * 服务码
     */
    private String fuwuma;

    /**
     * 取消备注信息
     */
    @TableField("cancelRemark")
    private String cancelRemark;

    /**
     * 服务客服
     */
    @TableField("guideStaffId")
    private Integer guideStaffId;

    /**
     * 业务确认时间
     */
    private LocalDateTime ychecktime;

    /**
     * 审核 上门时间、到店时间
     */
    @TableField("checkTime")
    private LocalDateTime checkTime;

    /**
     * 预约单取消原因
     */
    @TableField("cancelKind")
    private String cancelKind;

    /**
     * 是否进行过消息推送
     */
    @TableField("is_push")
    private Boolean push;

    private Integer imeiFromtype;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
