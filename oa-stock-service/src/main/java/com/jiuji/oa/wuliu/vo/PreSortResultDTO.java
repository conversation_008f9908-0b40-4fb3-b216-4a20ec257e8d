package com.jiuji.oa.wuliu.vo;


import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * JdCreateOrderResult.PreSortResult
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-16
 */
@Data
@Accessors(chain = true)
public class PreSortResultDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("collectionAddress")
    @JSONField(name = "collectionAddress")
    private String collectionAddress;

    @JsonProperty("sourceSortCenterId")
    @JSONField(name = "sourceSortCenterId")
    private String sourceSortCenterId;

    @JsonProperty("targetSortCenterId")
    @JSONField(name = "targetSortCenterId")
    private String targetSortCenterId;

    @JsonProperty("siteName")
    @JSONField(name = "siteName")
    private String siteName;

    @JsonProperty("sourceCrossCode")
    @JSONField(name = "sourceCrossCode")
    private String sourceCrossCode;

    @JsonProperty("sourceTabletrolleyCode")
    @JSONField(name = "sourceTabletrolleyCode")
    private String sourceTabletrolleyCode;

    @JsonProperty("distributeCode")
    @JSONField(name = "distributeCode")
    private String distributeCode;

    @JsonProperty("isHideContractNumbers")
    @JSONField(name = "isHideContractNumbers")
    private String isHideContractNumbers;

    @JsonProperty("targetTabletrolleyCode")
    @JSONField(name = "targetTabletrolleyCode")
    private String targetTabletrolleyCode;

    @JsonProperty("isHideName")
    @JSONField(name = "isHideName")
    private String isHideName;

    @JsonProperty("siteId")
    @JSONField(name = "siteId")
    private String siteId;

    @JsonProperty("aging")
    @JSONField(name = "aging")
    private String aging;

    @JsonProperty("slideNo")
    @JSONField(name = "slideNo")
    private String slideNo;

    @JsonProperty("coverCode")
    @JSONField(name = "coverCode")
    private String coverCode;

    @JsonProperty("sourceSortCenterName")
    @JSONField(name = "sourceSortCenterName")
    private String sourceSortCenterName;

    @JsonProperty("targetSortCenterName")
    @JSONField(name = "targetSortCenterName")
    private String targetSortCenterName;

    @JsonProperty("agingName")
    @JSONField(name = "agingName")
    private String agingName;

    @JsonProperty("siteType")
    @JSONField(name = "siteType")
    private String siteType;

    @JsonProperty("qrcodeUrl")
    @JSONField(name = "qrcodeUrl")
    private String qrcodeUrl;

    @JsonProperty("transType")
    @JSONField(name = "transType")
    private String transType;

    @JsonProperty("needRetry")
    @JSONField(name = "needRetry")
    private Boolean needRetry;

}
