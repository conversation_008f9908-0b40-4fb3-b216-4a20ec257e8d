package com.jiuji.oa.wuliu.vo;


import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * EmsApiServices.OrderResult
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-16
 */
@Data
@Accessors(chain = true)
public class OrderResultDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("code")
    @JSONField(name = "code")
    private Integer code;

    @JsonProperty("message")
    @JSONField(name = "message")
    private String message = "";

    @JsonProperty("orderids")
    @JSONField(name = "orderids")
    private String orderids = "";

    @JsonProperty("lastOrderid")
    @JSONField(name = "lastOrderid")
    private String lastOrderid;

    public String getLastOrderid() {
        String[] split = orderids.split(",");
        return StringUtils.isBlank(orderids) ? "" : split[split.length - 1];
    }

}
