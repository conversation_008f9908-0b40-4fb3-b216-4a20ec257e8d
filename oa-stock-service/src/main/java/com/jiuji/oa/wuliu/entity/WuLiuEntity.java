package com.jiuji.oa.wuliu.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.jiuji.infra.common.annotation.FieldCompare;
import com.jiuji.oa.stock.logistics.order.enums.WuliuStatusEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 物流单 Entity
 *
 * <AUTHOR> liu ming
 * @since 2021-08-13
 */
@Data
@Accessors(chain = true)
@TableName("wuliu")
public class WuLiuEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键 ID
     */
    private Integer id;

    /**
     * (发送方)姓名
     */
    @FieldCompare(name = "寄件人", format = "{0}由【{1}】修改为【{2}】", nullValue = "")
    @TableField("sname")
    private String sName;

    /**
     * (发送方)手机号
     */
    @FieldCompare(name = "寄件手机", format = "{0}由【{1}】修改为【{2}】", nullValue = "")
    @TableField("smobile")
    private String sMobile;

    /**
     * (发送方)地址
     */
    @FieldCompare(name = "寄件地址", format = "{0}由【{1}】修改为【{2}】", nullValue = "")
    @TableField("saddress")
    private String sAddress;

    /**
     * (发送方)大区
     */
    @FieldCompare(name = "寄件门店", format = "{0}由【{1}】修改为【{2}】", nullValue = "")
    @TableField("sarea")
    private String sArea;

    /**
     * (发送方)城市ID（did）
     */
    @TableField("scityid")
    private Integer sCityId;

    /**
     * (接收方)姓名
     */
    @FieldCompare(name = "收件人", format = "{0}由【{1}】修改为【{2}】", nullValue = "")
    @TableField("rname")
    private String rName;

    /**
     * (接收方)手机号
     */
    @FieldCompare(name = "收件手机", format = "{0}由【{1}】修改为【{2}】", nullValue = "")
    @TableField("rmobile")
    private String rMobile;

    /**
     * (接收方)地址
     */
    @FieldCompare(name = "收件地址", format = "{0}由【{1}】修改为【{2}】", nullValue = "")
    @TableField("raddress")
    private String rAddress;

    /**
     * 接收地区
     */
    @FieldCompare(name = "收件门店", format = "{0}由【{1}】修改为【{2}】", nullValue = "")
    @TableField("rarea")
    private String rArea;

    /**
     * (接收方)城市ID（did）
     */
    @TableField("rcityid")
    private Integer rCityId;

    /**
     * 物流单生成地区
     */
    private String area;

    /**
     * 登记时间
     */
    @TableField("dtime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonProperty("dtime")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime dTime;

    /**
     * 完成时间
     */
    @TableField("ctime")
    @JsonProperty("ctime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime cTime;

    /**
     * 价格
     */
    @FieldCompare(name = "价格", format = "{0}由【{1}】修改为【{2}】", nullValue = "")
    private BigDecimal price;

    /**
     * 成本价
     */
    @FieldCompare(name = "成本", format = "{0}由【{1}】修改为【{2}】", nullValue = "")
    @TableField("inprice")
    private BigDecimal inPrice;

    /**
     * 收件人
     */
    @FieldCompare(name = "取件人", format = "{0}由【{1}】修改为【{2}】", nullValue = "")
    @TableField("shoujianren")
    private String shouJianRen;

    /**
     * 派件人
     */
    @FieldCompare(name = "派送人", format = "{0}由【{1}】修改为【{2}】", nullValue = "")
    @TableField("paijianren")
    private String paiJianRen;

    /**
     * 状态
     * @see WuliuStatusEnum
     */
    private Integer stats;

    /**
     * linkType=2 || linkType=3 预约单
     * wuType=4 || wuType=6 订单
     * shouhouid != 0 售后单 ?
     * wuType=9 良品订单
     * wuType=7 && linkType=7 上门回收
     */
    @TableField("danhaobind")
    private Integer danHaoBind;

    /**
     * 类别
     */
    @FieldCompare(name = "分类", format = "{0}由【{1}】变为【{2}】", nullValue = "")
    @TableField("wutype")
    private Integer wuType;

    /**
     * 备注
     */
    private String comment;

    /**
     * 第三方快递
     */
    private String com;

    /**
     * 快递单号
     */
    @FieldCompare(name = "快递单号", format = "{0}由【{1}】变为【{2}】", nullValue = "")
    private String nu;

    /**
     * 重量
     */
    @FieldCompare(name = "重量", format = "{0}由【{1}】变为【{2}】", nullValue = "")
    private BigDecimal weight;

    /**
     * 操作录入人
     */
    @TableField("inuser")
    private String inUser;

    /**
     * 关联类型 关联yuyueid（2:上门取件，3:送件上门） 关联sub_id(其它) 5 好像是维修  6 发票物流单  7 回收上门取件,11九机集市, 13固定资产调拨 ，20 配件调拨，21大件调拨
     */
    @TableField("linktype")
    private Integer linkType;

    @TableField("sendtime")
    @JsonProperty("sendTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime sendTime;

    /**
     * 地区ID
     */
    @TableField("areaid")
    private Integer areaId;

    /**
     * 寄的地区ID
     */
    @TableField("sareaid")
    private Integer sAreaId;

    /**
     * 收的地区ID
     */
    @TableField("rareaid")
    private Integer rAreaId;

    /**
     * 签收人
     */
    @TableField("receiveUser")
    private String receiveUser;

    /**
     * 签收时间
     */
    @TableField("receiveTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonProperty("receiveTime")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime receiveTime;

    /**
     * 通知类型 0 不通知(手动填加的默认不通知) 1和null 系统生成的物流单 2 售后转地区生成的物流单
     */
    @TableField("notifytype")
    private Integer notifyType;

    /**
     * 订单类型 1 普通订单 2良品单 3售后单
     */
    @TableField("subKinds")
    private Integer subKinds;

    /**
     * 支付方式
     */
    private Integer payMethod;

    /**
     * 合并后父级保留物流单号
     */
    @TableField("wpid")
    private Integer wPid;

    /**
     * 物流分类 ID
     */
    @TableField("wCateId")
    private Integer wCateId;

    /**
     *
     */
    @TableField("LastRouteTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonProperty("lastRouteTime")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime lastRouteTime;

    /**
     *
     */
    @TableField("EstimatedArrivalTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonProperty("estimatedArrivalTime")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime estimatedArrivalTime;

    /**
     *
     */
    @TableField("ProductPriceTotal")
    private BigDecimal productPriceTotal;

    /**
     * 是否手动创建物流单：true=是，false=否
     */
    @TableField("isCreateManually")
    private Boolean isCreateManually;

    /**
     * 来源 0其他 1物流单列表添加
     */
    @TableField("source")
    private Integer source;

    /**
     * 跑腿送货人
     */
    @TableField("pt_user_name")
    private String ptUserName;

    /**
     * 跑腿送货人联系电话跑腿送货人联系电话
     */
    @TableField("pt_user_mobile")
    private String ptUserMobile;

    /**
     * 快递公司列名
     */
    public static String COM_COLUMN_NAME = "com";

    /**
     * 派件人列名
     */
    public static String PAI_JIAN_REN_COLUMN_NAME = "paijianren";
}
