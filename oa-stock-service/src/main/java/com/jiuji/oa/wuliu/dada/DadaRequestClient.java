package com.jiuji.oa.wuliu.dada;

import com.ch999.common.util.utils.Exceptions;
import com.jiuji.oa.nc.common.util.NumUtil;
import com.jiuji.oa.stock.common.util.OptionalUtils;
import com.jiuji.oa.stock.logistics.dada.utils.HttpClientUtil;
import com.jiuji.oa.stock.logisticscenter.utils.JsonParseUtil;
import com.jiuji.oa.wuliu.constant.DadaAppConstant;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.util.*;

/**
 * DATE: 18/9/3
 *
 * @author: wan
 */
@Slf4j
public class DadaRequestClient {

    private String apiUrl;

    private String paramsJson;

    private AppConfig appConfig;

    public DadaRequestClient(String apiUrl, String paramsJson,Boolean isOnline) {
        appConfig = new AppConfig(isOnline);
        this.apiUrl = apiUrl;
        this.paramsJson = paramsJson;
    }

    public DadaApiResponse callRpc() {
        String requestUrl = this.appConfig.getHost().concat(this.apiUrl);
        String requestParams = this.getRequestParams();
        log.info("达达调用，请求路径:{},请求参数:{}",requestUrl,requestParams);
        try {
            String resp = HttpClientUtil.postRequest(requestUrl, requestParams);
            log.info("达达调用，返回参数:{}",resp);
            return JsonParseUtil.toBean(resp, DadaApiResponse.class);
        }catch (IOException e){
            log.error("达达调用异常，请求路径:{},请求参数:{}",requestUrl, requestParams, e);
        }
        return DadaApiResponse.except();
    }

    private String getRequestParams() {
        Map<String, String> requestParams = new HashMap<>(NumUtil.SEVEN);
        requestParams.put("source_id", this.appConfig.getSourceId());
        requestParams.put("app_key", this.appConfig.getAppKey());
        requestParams.put("timestamp", String.valueOf(System.currentTimeMillis()));
        requestParams.put("format", DadaAppConstant.FORMAT);
        requestParams.put("v", DadaAppConstant.V);
        requestParams.put("body", paramsJson);
        requestParams.put("signature", this.getSign(requestParams));
        return JsonParseUtil.toJson(requestParams);
    }

    private String getSign(Map<String, String> requestParams) {
        //请求参数键值升序排序
        Map<String, String> sortedParams = new TreeMap<>(requestParams);
        Set<Map.Entry<String, String>> entrySets = sortedParams.entrySet();

        //拼参数字符串。
        StringBuilder signStr = new StringBuilder();
        for (Map.Entry<String, String> entry : entrySets) {
            signStr.append(entry.getKey()).append(entry.getValue());
        }

        //MD5签名并校验
        String toSign = this.appConfig.getAppSecret() + signStr.toString() + this.appConfig.getAppSecret();
        String sign = encrypt(toSign);
        return OptionalUtils.ifNotNull(sign, String::toUpperCase);
    }

    private static String encrypt(String inbuf) {
        String s = null;
        // 用来将字节转换成 16 进制表示的字符
        char[] hexDigits = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};
        try {
            java.security.MessageDigest md = java.security.MessageDigest.getInstance("MD5");
            md.update(inbuf.getBytes(StandardCharsets.UTF_8));
            // MD5 的计算结果是一个 128 位的长整数，
            byte[] tmp = md.digest();
            // 用字节表示就是 16 个字节
            // 每个字节用 16 进制表示的话，使用两个字符，
            char[] str = new char[16 * 2];
            // 所以表示成 16 进制需要 32 个字符
            int k = 0; // 表示转换结果中对应的字符位置
            // 从第一个字节开始，对 MD5 的每一个字节
            for (int i = 0; i < 16; i++) {
                // 转换成 16 进制字符的转换
                // 取第 i 个字节
                byte byte0 = tmp[i];
                // 取字节中高 4 位的数字转换,
                str[k++] = hexDigits[byte0 >>> 4 & 0xf];
                // >>> 为逻辑右移，将符号位一起右移
                // 取字节中低 4 位的数字转换
                str[k++] = hexDigits[byte0 & 0xf];
            }
            s = new String(str); // 换后的结果转换为字符串

        } catch (NoSuchAlgorithmException e) {
            log.error("DadaRequestClient encrypt 报错: {}", Exceptions.getStackTraceAsString(e), e);
        }
        return s;
    }
}
