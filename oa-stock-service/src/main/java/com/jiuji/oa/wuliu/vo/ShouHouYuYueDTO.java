package com.jiuji.oa.wuliu.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.jiuji.wcf.wcfclient.csharp.gen.oa999model.shouhou.attachModel;
import com.jiuji.wcf.wcfclient.csharp.gen.oa999model.shouhou.shouhou_yuyueproductinfo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 售后预约
 *
 * @description: ShouHouYuYueDTO
 * </p>
 * @author: David
 * @create: 2021-10-13 15:41
 */
@Data
@Accessors(chain = true)
public class ShouHouYuYueDTO {

    private Integer id;

    /**
     * 机型名称
     */
    private String name;

    /**
     * 机型颜色
     */
    private String color;

    /**
     * 服务方式
     */
    private Integer stype;

    /**
     * 客望处理方式
     */
    private Integer kind;

    /**
     * 状态  1未确认，2已确认，3完成
     */
    private Integer stats;

    /**
     * userid
     */
    private Integer userid;

    /**
     * 客户名称
     */
    private String username;

    /**
     * 客户手机
     */
    private String mobile;

    /**
     * 处理地区
     */
    private String area;

    /**
     * 处理地区
     */
    @JsonProperty("areaCode_")
    @JSONField(name = "areaCode_")
    private String areaCode;

    /**
     * 原地区
     */
    private String oldarea;

    /**
     * 预约形如时间
     */
    private LocalDateTime stime;

    /**
     * 预约结束时间
     */
    private LocalDateTime etime;

    /**
     * 配送方式
     */
    private Integer delivery;

    /**
     * 定单号
     */
    @JsonProperty("sub_id")
    @JSONField(name = "sub_id")
    private Integer subId;

    @JsonProperty("basket_id")
    @JSONField(name = "basket_id")
    private Integer basketId;

    /**
     * 商品型 号规格ID
     */
    private Integer ppriceid;

    /**
     * 串号
     */
    private String imei;

    /**
     * 是否大件
     */
    private Boolean ismobile;

    /**
     * 备注
     */
    private String comment;

    private String wuliyou;

    /**
     * 提交时间
     */
    private LocalDateTime dtime;

    /**
     * 故障描述
     */
    private String problem;

    /**
     * 预约录入人
     */
    private String inuser;

    /**
     * 售后单号
     */
    @JsonProperty("shouhou_id")
    @JSONField(name = "shouhou_id")
    private Integer shouhouId;

    /**
     * 是否删除
     */
    private Boolean isdel;

    /**
     * 关联物流单
     */
    private Integer wuliuid;

    /**
     * 退款信息  ， 用YuyueTuiData类系列化
     */
    private String tuiData;

    // 取件/收货地处  表单提交

    /**
     * 收货人
     */
    private String consignee1;

    /**
     * 取件城市
     */
    private Integer cityid1;

    /**
     * 取件联系人
     */
    private String reciver1;

    /**
     * 取件地址
     */
    private String address1;

    /**
     * 地址同上
     */
    private Boolean isaddress1;

    /**
     * 送货人
     */
    private String consignee2;

    /**
     * 收货城市
     */
    private Integer cityid2;

    /**
     * 收货人
     */
    private String reciver2;

    /**
     * 收货地址
     */
    private String address2;

    /**
     * 预出库地区
     */
    @JsonProperty("y_mkcAreaId")
    @JSONField(name = "y_mkcAreaId")
    private Integer yMkcAreaId;

    /**
     * 预出库地区
     */
    @JsonProperty("y_mkcArea")
    @JSONField(name = "y_mkcArea")
    private String yMkcArea;

    /**
     * 预出库PPID
     */
    @JsonProperty("y_ppriceid")
    @JSONField(name = "y_ppriceid")
    private Integer yPpriceid;

    /**
     * 小件维修ID
     */
    private Integer smallproid;

    private Integer areaid;

    /**
     * 客户等级
     */
    @JsonProperty("UserClass")
    @JSONField(name = "UserClass")
    private Integer userClass;

    /**
     * 客户等级名称
     */
    @JsonProperty("UserClassName")
    @JSONField(name = "UserClassName")
    private String userClassName;

    /**
     * 故障类型
     */
    @JsonProperty("TroubleIds")
    @JSONField(name = "TroubleIds")
    private String troubleIds;

    /**
     * 是否需备份数据
     */
    @JsonProperty("isBakDATA")
    @JSONField(name = "isBakDATA")
    private Integer isBakData;

    /**
     *
     */
    private Integer productid;

    /**
     *
     */
    private String enterUser;

    /**
     * 是否中邮入口
     */
    private Boolean iszy;

    /**
     * 中邮预约商品类型（1：按数量，2：按机型）
     */
    private Integer zyptype;

    /**
     * 中邮预约商品数量
     */
    private Integer zypnum;

    /**
     * 中邮按机型预约商品列表
     */
    private List<shouhou_yuyueproductinfo> yyproducts;

    /**
     *
     */
    private List<attachModel> pics;

    private String files;

    private String kuaididan;

    private LocalDateTime kdtime;

    private Integer kdtype;

    @JsonProperty("yuyuePPids")
    @JSONField(name = "yuyuePPids")
    private String yuyuePpids;

    /**
     * 预约优惠码
     */
    private String youhuima;

    private String fuwuma;

    private String kuaidigongsi;

    @JsonProperty("check_user")
    @JSONField(name = "check_user")
    private String checkUser;

    private Integer guideStaffId;

}
