package com.jiuji.oa.wuliu.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.wuliu.entity.AddInfoPsEntity;
import com.jiuji.oa.wuliu.mapper.AddInfoPsMapper;
import com.jiuji.oa.wuliu.service.IAddInfoPsService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户地址操作记录,责任小组：会员 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-13
 */
@Service
@DS("oanewWrite")
public class AddInfoPsServiceImpl extends ServiceImpl<AddInfoPsMapper, AddInfoPsEntity> implements IAddInfoPsService {

    /**
     * getByShouHouId
     *
     * @param shouHouId
     * @param yuyueId
     * @return AddInfoPsEntity
     */
    @Override
    @DS("ch999oanew")
    public AddInfoPsEntity getByShouHouId(Integer shouHouId, Integer yuyueId) {
        return this.baseMapper.getByShouHouId(shouHouId, yuyueId);
    }
}
