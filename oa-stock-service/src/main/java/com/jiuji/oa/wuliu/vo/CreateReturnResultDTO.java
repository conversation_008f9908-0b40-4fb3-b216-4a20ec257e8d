package com.jiuji.oa.wuliu.vo;


import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * createReturnResult
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-16
 */
@Data
@Accessors(chain = true)
public class CreateReturnResultDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("code")
    @JSONField(name = "code")
    private Integer code;

    /**
     * 美团配送内部订单id
     */
    @JsonProperty("mt_peisong_id")
    @JSONField(name = "mt_peisong_id")
    private String mtPeisongId;

    /**
     * 配送活动标识
     */
    @JsonProperty("delivery_id")
    @JSONField(name = "delivery_id")
    private String deliveryId;

    /**
     * 外部订单id
     */
    @JsonProperty("order_id")
    @JSONField(name = "order_id")
    private String orderId;

    /**
     * 错误信息提示
     */
    @JsonProperty("message")
    @JSONField(name = "message")
    private String message;

}
