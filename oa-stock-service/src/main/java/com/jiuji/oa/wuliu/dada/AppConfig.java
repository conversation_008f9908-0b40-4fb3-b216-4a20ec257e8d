package com.jiuji.oa.wuliu.dada;

import com.jiuji.oa.wuliu.constant.DadaAppConstant;

/**
 *
 * DATE: 18/9/3
 *
 * @author: wan
 */
public class AppConfig {

    /**
     * appKey
     */
    private String appKey;

    /**
     * appSecret
     */
    private String appSecret;

    /**
     * 接口地址
     */
    private String host;

    /**
     * 商户id
     */
    private String sourceId;

    public AppConfig(boolean isOnline) {
        this.appKey = DadaAppConstant.APP_KEY;
        this.appSecret = DadaAppConstant.APP_SECRET;
        this.host = isOnline ? DadaAppConstant.ONLINE_HOST : DadaAppConstant.QA_HOST;
        this.sourceId = isOnline ? DadaAppConstant.SOURCE_ID : DadaAppConstant.QA_SOURCE_ID;
    }

    /**
     * @param appKey
     * @param appSecret
     * @param host
     * @param sourceId
     */
    public AppConfig(String appKey,String appSecret,String host,String sourceId){
        this.appKey = appKey;
        this.appSecret = appSecret;
        this.host = host;
        this.sourceId = sourceId;
    }

    public String getAppKey() {
        return appKey;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public String getHost() {
        return host;
    }

    public String getSourceId() {
        return sourceId;
    }
}
