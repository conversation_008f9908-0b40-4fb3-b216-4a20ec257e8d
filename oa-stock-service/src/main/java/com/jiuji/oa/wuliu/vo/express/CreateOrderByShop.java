package com.jiuji.oa.wuliu.vo.express;

import com.jiuji.oa.stock.logisticscenter.vo.LogisticsBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 说明：物流订单创建通用Request
 * （支持：美团）
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@ApiModel("订单创建参数")
public class CreateOrderByShop extends LogisticsBase {

    @ApiModelProperty("物流单Id (美团,闪送)")
    private Long deliveryId;

    @ApiModelProperty("订单Id (美团,达达)")
    private String orderId;

    @ApiModelProperty("订单来源 (美团)")
    private String outerOrderSourceDesc;

    @ApiModelProperty("原平台订单号 (美团)")
    private String outerOrderSourceNo;

    @ApiModelProperty("配送服务代码 (美团)")
    private Integer deliveryServiceCode;

    @ApiModelProperty("货物详情列表 (美团)")
    private String goodsDetailList;

    @ApiModelProperty("收件人名称 (美团,闪送,达达)")
    private String receiverName;

    @ApiModelProperty("收件人地址 (美团,闪送,达达)")
    private String receiverAddress;

    @ApiModelProperty("收件人地址省 (美团,达达)")
    private String receiverProvince;

    @ApiModelProperty("收件人区 (美团,达达)")
    private String receiverCountry;

    @ApiModelProperty("收件人地址市 (美团,达达)")
    private String receiverCity;

    @ApiModelProperty("收件人电话 (美团,闪送)")
    private String receiverPhone;

    @ApiModelProperty("收件人经度 (美团,闪送)")
    private Integer receiverLng;

    @ApiModelProperty("收件人纬度 (美团,闪送)")
    private Integer receiverLat;

    @ApiModelProperty("寄件人姓名(闪送)")
    private String sendName;

    @ApiModelProperty("寄件人姓名(闪送)")
    private String sendPhone;

    @ApiModelProperty("寄件人地址(闪送)")
    private String sendAddress;

    @ApiModelProperty("寄件人经度(闪送)")
    private String sendLng;

    @ApiModelProperty("寄件人纬度(闪送)")
    private String sendLat;

    @ApiModelProperty("货物价格 (美团)")
    private String goodsValue;

    @ApiModelProperty("货物重量 (美团,闪送)")
    private String goodsWeight;

    @ApiModelProperty("备注 (美团,闪送)")
    private String comment;

    @ApiModelProperty("订单类型 (美团)")
    private Integer orderType;

    @ApiModelProperty("物流类型")
    private Integer logisticsType;
}
