package com.jiuji.oa.wuliu.mapstruct;

import com.jiuji.cloud.logistics.vo.base.CreateOrderNoBase;
import com.jiuji.cloud.logistics.vo.request.CreateOrderReq;
import com.jiuji.oa.wuliu.vo.CityIdListDTO;
import com.jiuji.oa.wuliu.vo.paimai.req.AddPaimaiWuliuReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface PaiMaiWuliuMapStruct {
    /**
     * AddPaimaiWuliuReqVO to CreateOrderReq
     * @param paimaiWuliuReq
     * @return
     */
    @Mapping(target = "sendShopId", source = "paimaiWuliuReq.senderAreaId")
    @Mapping(target = "expressItemName", constant = "电子产品")
    @Mapping(target = "packageCount", source = "paimaiWuliuReq.parcelQuantity", defaultValue = "1")
    @Mapping(target = "senderProvinceName", source = "senderCity.pname")
    @Mapping(target = "senderCityName", source = "senderCity.zname")
    @Mapping(target = "senderCountyName", source = "senderCity.dname")
    @Mapping(target = "receiverProvinceName", source = "receverCity.pname")
    @Mapping(target = "receiverCityName", source = "receverCity.zname")
    @Mapping(target = "receiverCountyName", source = "receverCity.dname")
    CreateOrderReq toCreateOrderReq(AddPaimaiWuliuReqVO paimaiWuliuReq, CityIdListDTO senderCity, CityIdListDTO receverCity);

    /**
     * AddPaimaiWuliuReqVO to CreateOrderNoBase
     * @param paimaiWuliuReq
     * @return
     */
    @Mapping(target = "sendName", source = "paimaiWuliuReq.senderName")
    @Mapping(target = "sendTel", source = "paimaiWuliuReq.senderMobile")
    @Mapping(target = "sendAddress", source = "paimaiWuliuReq.senderAddress")
    @Mapping(target = "receiveName", source = "paimaiWuliuReq.receiverName")
    @Mapping(target = "receiveTel", source = "paimaiWuliuReq.receiverMobile")
    @Mapping(target = "receiveAddress", source = "paimaiWuliuReq.receiverAddress")
    @Mapping(target = "expressType", source = "paimaiWuliuReq.dropMenuExpressType")
    CreateOrderNoBase toCreateOrderNoBase(AddPaimaiWuliuReqVO paimaiWuliuReq);
}
