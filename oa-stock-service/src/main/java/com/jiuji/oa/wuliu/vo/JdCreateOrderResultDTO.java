package com.jiuji.oa.wuliu.vo;


import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * JdCreateOrderResult
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-16
 */
@Data
@Accessors(chain = true)
public class JdCreateOrderResultDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("jingdong")
    @JSONField(name = "jingdong")
    private DataResultDTO dataResult;

    @JsonProperty("lwbNo")
    @JSONField(name = "lwbNo")
    private String expressNumber;

}
