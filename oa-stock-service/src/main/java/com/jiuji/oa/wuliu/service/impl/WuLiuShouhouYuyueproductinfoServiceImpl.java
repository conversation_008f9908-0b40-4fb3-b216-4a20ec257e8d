package com.jiuji.oa.wuliu.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.wuliu.entity.WuLiuShouhouYuyueproductinfoEntity;
import com.jiuji.oa.wuliu.mapper.WuLiuShouhouYuyueproductinfoMapper;
import com.jiuji.oa.wuliu.service.IWuLiuShouhouYuyueproductinfoService;
import org.springframework.stereotype.Service;

/**
 * 售后预约产品信息[责任小组:销售] 服务实现类
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-11-04
 */
@Service
public class WuLiuShouhouYuyueproductinfoServiceImpl extends ServiceImpl<WuLiuShouhouYuyueproductinfoMapper, WuLiuShouhouYuyueproductinfoEntity> implements IWuLiuShouhouYuyueproductinfoService {
}
