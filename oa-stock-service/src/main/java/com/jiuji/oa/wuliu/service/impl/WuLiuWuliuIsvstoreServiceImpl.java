package com.jiuji.oa.wuliu.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.wuliu.entity.WuLiuWuliuIsvstoreEntity;
import com.jiuji.oa.wuliu.mapper.WuLiuWuliuIsvstoreMapper;
import com.jiuji.oa.wuliu.service.IWuLiuWuliuIsvstoreService;
import org.springframework.stereotype.Service;

/**
 * 服务实现类
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-12-15
 */
@Service
public class WuLiuWuliuIsvstoreServiceImpl extends ServiceImpl<WuLiuWuliuIsvstoreMapper, WuLiuWuliuIsvstoreEntity> implements IWuLiuWuliuIsvstoreService {

    /**
     * getWuLiuIsvstoreByWuliuId
     *
     * @param wuliuId
     * @return WuLiuWuliuIsvstoreEntity
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-15
     */
    @Override
    public WuLiuWuliuIsvstoreEntity getWuLiuIsvstoreByWuliuId(Integer wuliuId) {
        return baseMapper.getWuLiuIsvstoreByWuliuId(wuliuId);
    }

}
