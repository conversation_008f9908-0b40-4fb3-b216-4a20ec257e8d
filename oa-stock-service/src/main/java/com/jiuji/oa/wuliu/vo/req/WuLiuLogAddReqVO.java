package com.jiuji.oa.wuliu.vo.req;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 物流单日志新增 req VO
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-08
 */
@Data
@Accessors(chain = true)
public class WuLiuLogAddReqVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("wuliuid")
    @JSONField(name = "wuliuid")
    private Integer wuLiuId;

    /**
     * 录入信息
     */
    private String msg;

}
