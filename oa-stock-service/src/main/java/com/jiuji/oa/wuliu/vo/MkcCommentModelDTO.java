package com.jiuji.oa.wuliu.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * ParModel.mkcCommentModel
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-16
 */
@Data
@Accessors(chain = true)
public class MkcCommentModelDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("basket_id")
    @JSONField(name = "basket_id")
    private String basketId;

    @JsonProperty("otherSql")
    @JSONField(name = "otherSql")
    private String otherSql;

    @JsonProperty("comment")
    @JSONField(name = "comment")
    private String comment;

    @JsonProperty("username")
    @J<PERSON><PERSON>ield(name = "username")
    private String username;

    @JsonProperty("showType")
    @JSONField(name = "showType")
    private Integer showType;

}
