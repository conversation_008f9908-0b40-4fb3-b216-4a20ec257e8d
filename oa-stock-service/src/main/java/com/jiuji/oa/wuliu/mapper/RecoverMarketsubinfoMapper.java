package com.jiuji.oa.wuliu.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.wuliu.bo.SellerInfoBO;
import com.jiuji.oa.wuliu.entity.RecoverMarketsubinfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 转售详情单（良品详情订单）[责任小组:回收] Mapper 接口
 * </p>
 *
 * <AUTHOR> yao yao
 * @since 2022-06-01
 */
@Mapper
public interface RecoverMarketsubinfoMapper extends BaseMapper<RecoverMarketsubinfo> {

    SellerInfoBO getSellerInfoBySubId(@Param("subId") Integer subId);
}
