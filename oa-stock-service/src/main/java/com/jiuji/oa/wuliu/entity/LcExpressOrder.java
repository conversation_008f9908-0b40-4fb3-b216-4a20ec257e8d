package com.jiuji.oa.wuliu.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 中台快递详情表
 * @TableName lc_express_order
 */
@TableName(value ="lc_express_order")
@Data
public class LcExpressOrder implements Serializable {
    /**
     * 
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 收件人详细地址
     */
    @TableField(value = "receiver_detail_address")
    private String receiverDetailAddress;

    /**
     * 收件人名称
     */
    @TableField(value = "receiver_name")
    private String receiverName;

    /**
     * 收件人电话
     */
    @TableField(value = "receiver_phone")
    private String receiverPhone;

    /**
     * 寄件人详细地址
     */
    @TableField(value = "sender_detail_address")
    private String senderDetailAddress;

    /**
     * 寄件人名称
     */
    @TableField(value = "sender_name")
    private String senderName;

    /**
     * 寄件人电话
     */
    @TableField(value = "sender_phone")
    private String senderPhone;

    /**
     * 物流单分类
     */
    @TableField(value = "logistics_type")
    private Integer logisticsType;

    /**
     * 雪花id
     */
    @TableField(value = "logistics_id")
    private Long logisticsId;

    /**
     * 运单号
     */
    @TableField(value = "waybill_no")
    private String waybillNo;

    /**
     * 物流单号
     */
    @TableField(value = "delivery_id")
    private String deliveryId;

    /**
     * 收货门店id
     */
    @TableField(value = "receive_shop_id")
    private String receiveShopId;

    /**
     * 收货门店名称
     */
    @TableField(value = "receive_shop_name")
    private String receiveShopName;

    /**
     * 发货门店id
     */
    @TableField(value = "send_shop_id")
    private String sendShopId;

    /**
     * 发货门店名称
     */
    @TableField(value = "send_shop_name")
    private String sendShopName;

    /**
     * 快递类型
     */
    @TableField(value = "express_type")
    private Integer expressType;

    /**
     * 租户id
     */
    @TableField(value = "xtenant")
    private Long xtenant;

    /**
     * 规模
     */
    @TableField(value = "tenant_scale")
    private Integer tenantScale;

    /**
     * 套餐
     */
    @TableField(value = "combo")
    private String combo;

    /**
     * 套餐id
     */
    @TableField(value = "combo_id")
    private Integer comboId;

    /**
     * 充值用户id
     */
    @TableField(value = "bbsxp_user_id")
    private Integer bbsxpUserId;

    /**
     * 创建日期
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 修改日期
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}