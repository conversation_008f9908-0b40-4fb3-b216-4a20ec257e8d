package com.jiuji.oa.wuliu.enums;

import com.jiuji.oa.nc.abnormal.vo.ShowPrintingEnumVOV2;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 物流快递枚举
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-09-29
 */
@Getter
@RequiredArgsConstructor
public enum WuLiuExpressEnum {

    /**
     * 美团
     */
    MEI_TUAN("meituan", "美团"),

    /**
     * 达达
     */
    DA_DA("dada", "达达"),

    /**
     * 顺丰快递
     */
    SHUN_FENG("shunfeng", "顺丰快递"),

    /**
     * 顺丰快递（九机特惠）
     */
    SHUN_FENG_JIU_JI("shunfeng_jiuji", "顺丰快递（九机特惠）"),

    /**
     * 京东物流(九机特惠)
     */
    JINGDONG_JIUJI_NEW("jingdong-jiuji-new", "京东物流(九机特惠)"),

    /**
     * 美团(九机特惠)
     */
    MEITUAN_JIUJI("meituan_jiuji", "美团(九机特惠)"),

    /**
     * 达达(九机特惠)
     */
    DADA_JIUJI("dada-jiuji", "达达(九机特惠)"),

    /**
     * 中通快递
     */
    ZHONG_TONG("zhongtong", "中通快递");

    /**
     * 枚举编码
     */
    private final String code;

    /**
     * 枚举信息
     */
    private final String message;

    /**
     * 中台快递九机特惠ExpressCode
     *
     * @return
     */
    public static List<WuLiuExpressEnum> logisticsExpressCode() {
        return new ArrayList<>(Arrays.asList(SHUN_FENG_JIU_JI, JINGDONG_JIUJI_NEW, MEITUAN_JIUJI, DADA_JIUJI));
    }

    public static String getWuLiuExpressEnum(String code) {
        for (WuLiuExpressEnum enums : WuLiuExpressEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getMessage();
            }
        }
        return "";
    }

    public static List<ShowPrintingEnumVOV2> getAllPrintingEnum() {
        WuLiuExpressEnum[] array = WuLiuExpressEnum.values();
        List<ShowPrintingEnumVOV2> arrayList = new ArrayList<>();
        for (WuLiuExpressEnum t : array) {
            ShowPrintingEnumVOV2 ShowPrintingEnumVOV2 = new ShowPrintingEnumVOV2()
                    .setLabel(t.getMessage())
                    .setValue(t.getCode());
            arrayList.add(ShowPrintingEnumVOV2);
        }
        return arrayList;
    }
}
