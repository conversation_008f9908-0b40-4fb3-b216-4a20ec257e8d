package com.jiuji.oa.wuliu.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 日期类型的枚举
 *
 * <AUTHOR>
 * @date 2021/10/09
 */
@Getter
@AllArgsConstructor
public enum FinanceCheckStateEnum implements CodeMessageEnumInterface {
    /**
     * 未复核
     */
    PENDING(0, "未复核"),
    /**
     * 已复核
     */
    APPROVED(1, "已复核");

    /**
     * 代码
     */
    private Integer code;
    /**
     * 消息
     */
    private String message;

    /**
     * message
     * @param deliveryCode
     * @return
     */
    public static String getMessageByCode (Integer code) {
        for (FinanceCheckStateEnum stateEnum : values()) {
            if (stateEnum.getCode().equals(code)) {
                return stateEnum.getMessage();
            }
        }
        return "";
    }
}
