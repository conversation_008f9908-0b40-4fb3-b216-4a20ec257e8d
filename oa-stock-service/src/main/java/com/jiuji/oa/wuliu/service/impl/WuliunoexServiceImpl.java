package com.jiuji.oa.wuliu.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.wuliu.entity.Wuliunoex;
import com.jiuji.oa.wuliu.service.IWuliunoexService;
import com.jiuji.oa.wuliu.mapper.WuliunoexMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 *
 */
@Service
@DS("oanewWrite")
public class WuliunoexServiceImpl extends ServiceImpl<WuliunoexMapper, Wuliunoex> implements IWuliunoexService {

    /**
     * 物流id查询子单号
     *
     * @param wuliuId
     * @return
     */
    @Override
    @DS("ch999oanew")
    public List<Wuliunoex> queryByWuliuId(Integer wuliuId) {
        return this.lambdaQuery().eq(Wuliunoex::getWuliuid, wuliuId).list();
    }

    @Override
    @DS("oanewWrite")
    public void deleteByWuliuIds(List<Integer> wuliuIdList) {
        List<Wuliunoex> wuliunoexList = this.lambdaQuery().in(Wuliunoex::getWuliuid, wuliuIdList).list();
        if (CollectionUtils.isNotEmpty(wuliunoexList)) {
            List<Integer> idList = wuliunoexList.stream().map(Wuliunoex::getId).collect(Collectors.toList());
            this.removeByIds(idList);
        }
    }
}


