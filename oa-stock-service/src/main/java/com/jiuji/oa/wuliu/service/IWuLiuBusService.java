package com.jiuji.oa.wuliu.service;

import com.jiuji.oa.nc.common.bo.OaUserBO;
import com.jiuji.oa.stock.logistics.order.vo.ExpressPushVO;
import com.jiuji.oa.stock.logistics.order.vo.req.JdDeliveryPreFeedbackReq;
import com.jiuji.oa.stock.logistics.order.vo.req.WuLiuWebReq;
import com.jiuji.oa.wuliu.bo.*;
import com.jiuji.oa.wuliu.dto.req.WuliuInvalidReqV2;
import com.jiuji.oa.wuliu.vo.req.*;
import com.jiuji.oa.wuliu.vo.res.*;
import com.jiuji.tc.common.vo.R;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IWuLiuBusService {

    /**
     * OA创建物流单
     *
     * @param req
     * @return
     */
    Integer oaAdd(WuLiuWebReq req);

    /**
     * 获取物流单
     *
     * @param vo
     * @param currentUser
     * @return
     */
    WuLiuInfoReqVO getWuLiuInfo(WuLiuInfoReqVO vo, OaUserBO currentUser);

    /**
     * 物料申请完成
     * @param wuliuId
     * @param user
     * @return
     */
    boolean completeMaterialApply(Integer wuliuId, String user, Integer type);

    /**
     * 查询物流单
     * @param req
     * @param currentUser
     * @return
     */
    WuLiuInfoReqVO getWuLiuInfoV2(WuLiuInfoReqVO req, OaUserBO currentUser);

    /**
     * 创建物流单
     * @param vo
     * @return
     */
    R<WuLiuAddResV2VO> add(WuLiuAddReqV2VO vo);

    /**
     * 更新物流单
     * @param user
     * @param vo
     * @return
     */
    R<String> updateWuliuV2(OaUserBO user, WuLiuUpdateReqV2 vo);

    /**
     * 更新物流单快递信息
     * @param vo
     * @return
     */
    R<Boolean> updateWuliuV3(WuLiuUpdateReqV3 vo);

    /**
     * 获取物流单关联单据寄件收件信息
     * @param vo
     * @return
     */
    WuliuAddressInfoRes getWuliuAddressInfo(WuliuAddressInfoReq vo);

    /**
     * 查询物流单关联订单、良品单规定送达时间
     * @param wuliuId
     * @return
     */
    WuliuAdvanceDeliveryRes getWuliuAdvanceDelivery(Integer wuliuId,Integer wutype,Integer danhaobind);

    /**
     * 作废物流单
     * @param wuliuInvalid
     */
    void wuliuInvalid(WuliuInvalidBO wuliuInvalid);

    /**
     * 终止物流单
     * @param req
     * @return
     */
    boolean terminateWuliu(TerminateWuliuReq req);

    /**
     * 计算物流单成本
     * @param req
     * @return
     */
    boolean calculateDistributionCost(WuliuDistributtionCostBO req);

    /**
     * 处理物流单骑行距离
     * @param days
     */
    void handleDistributionCost(Integer days);

    /**
     * 作废物流单
     * @param id
     * @return
     */
    R<Boolean> invalidV2(WuliuInvalidReqV2 req);

    /**
     * 变更物流单状态
     * @param wuliuStats
     * @return
     */
    boolean changeWuliuStats(ChangWuliuStatsBO wuliuStats);

    /**
     * 处理超时物流单
     * @param days
     * @return
     */
    boolean handleTimeoutWuliu(Integer days);

    /**
     * 物流单，快递收货地址是否一致
     * @param req
     * @return
     */
    SameWuliuExpressAddressRes sameReceiveAddress(SameWuliuExpressAddressReq req);

    /**
     * 快递签收物流单处理
     */
    void handleWuliuExpressSigned(ExpressPushVO expressPush);
    /**
     * 达达快递签收物流单处理
     */
    void handleWuliuExpressSigned(String nu);
    /**
     * 快递签收物流单处理
     */
    void wuliuExpressSigned(WuliuExpressSignedBO wuliuExpress);

    WuliuOrderInfoRes getOrderByPayId(Integer payId);

    /**
     * 新机，良品，售后 查询调拨单对应的物流单
     * @param vo
     * @return
     */
    List<InnerWuliuInfoRes> getInnerWuliuInfo(InnerWuliuInfoReq vo);

    WuliuOrderInfoRes getOrderByPayDes(String payDes);

    /**
     * 发送物流单卡片消息
     * @param vo
     * @return
     */
    Boolean sendWuliuOrder(SendWuliuOrderReq vo);

    /**
     * 查询物流单门店营业时间
     * @param vo
     * @return
     */
    WuliuAreaOpeningTimeRes getWuliuAreaOpeningTime(WuliuAreaOpeningTimeReq vo);

    /**
     * 处理快递单取消
     * @param expressPush
     */
    void handleWuliuExpressCanceled(ExpressPushVO expressPush);

    /**
     * 苹果采购物流单处理
     * @param data
     */
    void applePurchaseWuliu(ApplePurchaseWuliuBO data);

    /**
     * 查询用户与门店的距离
     * @param req
     * @return
     */
    DistanceAreaAndPositionRes getDistanceAreaAndPosition(DistanceAreaAndPositionReq req);

    /**
     * 更新物流单跑腿信息
     * @param nu
     * @param ptUserName
     * @param ptUserMobile
     */
    void updateWuliuPtInfo(String nu,String ptUserName, String ptUserMobile);

    /**
     * 保存物流单代签和揽收
     * @param req
     * @return
     */
    R<String> signatureAndPickUpWuliu(WuliuSignatureAndPickUpReq req);

    /**
     * 获取物流单信息
     * @param req
     * @return
     */
    GetWuliuInfoRes getWuLiuInfo(GetWuliuInfoReq req);

    /**
     * 签收物流单发送OA消息
     * @return
     */
    Boolean signatureWuliuSendOaMsg();

    /**
     * 调拨单已发货状态，物流单叫跑腿后，推送OA消息给调拨单发货操作人
     * @param data
     */
    void diaoboPaotuiWuliu(DiaoboPaotuiWuliuBO data);

    /**
     * 物流单未投妥
     * @param req
     * @return
     */
    R<String> wuliuNotDelivered(WuliuNotDeliveredReq req);

    /**
     * 跑腿配送异常
     */
    void abnormalDeliver(String waybillNo, String exceptionMsg);

    void jdDeliveryPreFeedback(JdDeliveryPreFeedbackReq req);
}
