package com.jiuji.oa.wuliu.controller;


import com.jiuji.oa.wuliu.service.IWuLiuCategoryService;
import com.jiuji.oa.wuliu.vo.res.WuLiuCategoryResVO;
import com.jiuji.tc.common.vo.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 物流单分类
 * </p>
 *
 * <AUTHOR> liu ming
 * @since 2021-09-27
 */
@RestController
@RequestMapping("/api/wuliu/category")
@RequiredArgsConstructor
@Api(value = "logistics-wuliu-category", tags = "物流单-分类")
public class WuLiuCategoryController {

    @Resource
    private IWuLiuCategoryService wuLiuCategoryService;

    /**
     * 查询物流单分类
     *
     * @return 物流单分类
     */
    @ApiOperation("查询物流单分类")
    @GetMapping("/list/v1")
    public R<List<WuLiuCategoryResVO>> listAllV1() {
        // 查询查询物流单分类
        List<WuLiuCategoryResVO> wuLiuCategoryResVO = wuLiuCategoryService.queryWuLiuCategoryList();
        return R.success(wuLiuCategoryResVO);
    }

}

