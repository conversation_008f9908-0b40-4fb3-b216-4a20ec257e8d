package com.jiuji.oa.wuliu.bo;
import lombok.Data;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/6/15 15:41
 */
@Data
public class ExpressOrderAddBO {
    private Long id;
    /**
     * 收件人详细地址
     */
    @NotNull(message = "收件人详细地址不能为空")
    private String receiverDetailAddress;
    /**
     * 收件人名称
     */
    @NotNull(message = "收件人名称不能为空")
    private String receiverName;
    /**
     * 收件人电话
     */
    @NotNull(message = "收件人电话不能为空")
    private String receiverPhone;
    /**
     * 寄件人详细地址
     */
    @NotNull(message = "寄件人详细地址不能为空")
    private String senderDetailAddress;
    /**
     * 寄件人名称
     */
    @NotNull(message = "寄件人名称不能为空")
    private String senderName;
    /**
     * 寄件人电话
     */
    @NotNull(message = "寄件人电话不能为空")
    private String senderPhone;
    /**
     * 物流单分类
     */
    private Integer logisticsType;
    /**
     * 雪花id
     */
    private Long logisticsId;
    /**
     * 运单号
     */
    @NotNull(message = "快递单号不能为空")
    private String waybillNo;
    /**
     * 物流单号
     */
    private String deliveryId;
    /**
     * 收货门店id
     */
    private String receiveShopId;
    /**
     * 收货门店名称
     */
    private String receiveShopName;
    /**
     * 发货门店id
     */
    private String sendShopId;
    /**
     * 发货门店名称
     */
    private String sendShopName;
    /**
     * 快递类型
     */
    @NotNull(message = "快递类型不能为空")
    private Integer expressType;
    /**
     * 租户id
     */
    private Long xtenant;
    /**
     * 租户规模
     **/
    private Integer tenantScale;
    /**
     * 套餐
     */
    private String combo;
    /**
     * 套餐id
     */
    private Integer comboId;
    /**
     * 充值用户id
     */
    private Integer bbsxpUserId;
    /**
     * 保价金额
     */
    private BigDecimal guaranteeValueAmount;
    /**
     * 配置id
     */
    private Long configId;

    /**
     * 寄件人省
     */
    private String senderProvinceName;
    /**
     * 寄件人市
     */
    private String senderCityName;
    /**
     * 寄件人区
     */
    private String senderCountyName;
    /**
     *收货人省
     */
    private String receiverProvinceName;
    /**
     *收货人市
     */
    private String receiverCityName;
    /**
     *收货人区
     */
    private String receiverCountyName;
}
