package com.jiuji.oa.wuliu.vo.req;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.jiuji.oa.nc.common.req.OaAttachmentsAddOrUpdateReqVO;
import com.jiuji.oa.wuliu.vo.res.WuLiuLogResVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 物流单新增/更新 req VO
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-08
 */
@ApiModel(description = "物流单新增/更新 req VO")
@Data
@Accessors(chain = true)
public class WuLiuAddOrUpdateReqVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 包裹数
     */
    @ApiModelProperty("包裹数")
    @JsonProperty("PackageCount")
    @JSONField(name = "PackageCount")
    private String packageCount;

    /**
     * 体积
     */
    @ApiModelProperty("体积")
    @JsonProperty("Vloumn")
    @JSONField(name = "Vloumn")
    private BigDecimal vloumn;

    /**
     * 快递公司原寄地编码
     */
    @ApiModelProperty("快递公司原寄地编码")
    @JsonProperty("orgcode")
    @JSONField(name = "orgcode")
    private String orgcode;

    /**
     * 快递公司目的地编码
     */
    @ApiModelProperty("快递公司目的地编码")
    @JsonProperty("destcode")
    @JSONField(name = "destcode")
    private String destcode;

    /**
     * 运单类型  1:标准快递  2:顺风特惠  3:电商特惠
     */
    @ApiModelProperty(" 运单类型  1:标准快递  2:顺风特惠  3:电商特惠")
    @JsonProperty("express_type")
    @JSONField(name = "express_type")
    private String expressType;

    /**
     * 第三方支付方式 1:寄付月结 2:收件方付 3:第三方付
     */
    @ApiModelProperty("第三方支付方式 1:寄付月结 2:收件方付 3:第三方付")
    @JsonProperty("paytype")
    @JSONField(name = "paytype")
    private String paytype;

    /**
     * 订单ID
     */
    @ApiModelProperty("订单ID")
    @JsonProperty("sub_id")
    @JSONField(name = "sub_id")
    private Integer subId;

    /**
     * (收件方)pid
     */
    @ApiModelProperty("(收件方)pid")
    @JsonProperty("spid")
    @JSONField(name = "spid")
    private Integer spid;

    /**
     * (收件方)zid
     */
    @ApiModelProperty("(收件方)zid")
    @JsonProperty("szid")
    @JSONField(name = "szid")
    private Integer szid;

    /**
     * (接件方)pid
     */
    @ApiModelProperty("(接件方)pid")
    @JsonProperty("rpid")
    @JSONField(name = "rpid")
    private Integer rpid;

    /**
     * (接件方)zid
     */
    @ApiModelProperty("(接件方)zid")
    @JsonProperty("rzid")
    @JSONField(name = "rzid")
    private Integer rzid;

    /**
     * 预约ID
     */
    @ApiModelProperty("预约ID")
    @JsonProperty("yuyueID")
    @JSONField(name = "yuyueID")
    private Integer yuyueId;

    /**
     * 预计送达时间
     */
    @ApiModelProperty("预计送达时间")
    @JsonProperty("expectTime")
    @JSONField(name = "expectTime")
    private LocalDateTime expectTime;

    /**
     * 目的地
     */
    @ApiModelProperty("目的地")
    @JsonProperty("destRouteLabel")
    @JSONField(name = "destRouteLabel")
    private String destRouteLabel;

    /**
     * 日志消息
     */
    @ApiModelProperty("日志消息")
    @JsonProperty("msg")
    @JSONField(name = "msg")
    private String msg;

    /**
     * 顺风免单二维码信息
     */
    @ApiModelProperty("美团光速达标识")
    @JsonProperty("twoDimensionCode")
    @JSONField(name = "twoDimensionCode")
    private String twoDimensionCode;

    /**
     * 美团光速达标识
     */
    @ApiModelProperty("美团光速达标识")
    @JsonProperty("meiTuanFastFlag")
    @JSONField(name = "meiTuanFastFlag")
    private Boolean meiTuanFastFlag;

    /**
     * 用于传递三方物流接口的订单编号
     */
    @ApiModelProperty("用于传递三方物流接口的订单编号")
    @JsonProperty("ordercode_id")
    @JSONField(name = "ordercode_id")
    private String ordercodeId;

    /**
     * 是否显示物流单报销按钮
     */
    @ApiModelProperty("是否显示物流单报销按钮")
    @JsonProperty("isShowWuliuClaim")
    @JSONField(name = "isShowWuliuClaim")
    private Boolean isShowWuliuClaim;

    /**
     * 操作标识
     */
    @ApiModelProperty("操作标识")
    @JsonProperty("actionName")
    @JSONField(name = "actionName")
    private String actionName;

    @ApiModelProperty("物流单 ID")
    @JsonProperty("wuliuid")
    @JSONField(name = "wuliuid")
    private Integer wuliuid;

    /**
     * (发送方)姓名
     */
    @ApiModelProperty("(发送方)姓名")
    @JsonProperty("sname")
    @JSONField(name = "sname")
    private String sName;

    /**
     * (发送方)手机号
     */
    @ApiModelProperty("(发送方)手机号")
    @NotBlank(message = "寄件人手机不能为空")
    @JsonProperty("smobile")
    @JSONField(name = "smobile")
    private String sMobile;

    /**
     * (发送方)地址
     */
    @ApiModelProperty("(发送方)地址")
    @JsonProperty("saddress")
    @JSONField(name = "saddress")
    @Length(max = 250, message = "寄件方地址不能超过 250 个字符")
    private String sAddress;

    /**
     * (发送方)大区
     */
    @ApiModelProperty("(发送方)大区")
    @JsonProperty("sarea_M")
    @JSONField(name = "sarea_M")
    private String sAreaM;

    /**
     * (发送方)城市ID（did）
     */
    @ApiModelProperty("(发送方)城市ID（did）")
    @JsonProperty("scityid")
    @JSONField(name = "scityid")
    private Integer sCityId;

    /**
     * (接收方)姓名
     */
    @ApiModelProperty("(接收方)姓名")
    @JsonProperty("rname")
    @JSONField(name = "rname")
    private String rName;

    /**
     * (接收方)手机号
     */
    @ApiModelProperty("(接收方)手机号")
    @NotBlank(message = "收件人手机不能为空")
    @JsonProperty("rmobile")
    @JSONField(name = "rmobile")
    private String rMobile;

    /**
     * (接收方)地址
     */
    @ApiModelProperty("(接收方)地址")
    @JsonProperty("raddress")
    @JSONField(name = "raddress")
    @Length(max = 250, message = "收件方地址不能超过 250 个字符")
    private String rAddress;

    /**
     * 接收地区
     */
    @ApiModelProperty("接收地区")
    @JsonProperty("rarea_M")
    @JSONField(name = "rarea_M")
    private String rAreaM;

    /**
     * (接收方)城市ID（did）
     */
    @ApiModelProperty("(接收方)城市ID（did）")
    @JsonProperty("rcityid")
    @JSONField(name = "rcityid")
    private Integer rCityId;

    /**
     * 价格
     */
    @ApiModelProperty("价格")
    @JsonProperty("price")
    @JSONField(name = "price")
    private BigDecimal price;

    /**
     * 成本价
     */
    @ApiModelProperty("成本价")
    @JsonProperty("inprice")
    @JSONField(name = "inprice")
    private BigDecimal inPrice;

    /**
     * 重量
     */
    @ApiModelProperty("重量")
    @JsonProperty("weight")
    @JSONField(name = "weight")
    private BigDecimal weight;

    /**
     * 收件人
     */
    @ApiModelProperty("收件人")
    @JsonProperty("shoujianren")
    @JSONField(name = "shoujianren")
    private String shouJianRen;

    /**
     * 派件人
     */
    @ApiModelProperty("派件人")
    @JsonProperty("paijianren")
    @JSONField(name = "paijianren")
    private String paiJianRen;

    /**
     * linkType=2 || linkType=3 预约单
     * wuType=4 || wuType=6 订单
     * shouhouid != 0 售后单 ?
     * wuType=9 良品订单
     * wuType=7 && linkType=7 上门回收
     */
    @ApiModelProperty("linkType=2 || linkType=3 预约单\n" +
            "wuType=4 || wuType=6 订单\n" +
            "shouhouid != 0 售后单 ?\n" +
            "wuType=9 良品订单\n" +
            "wuType=7 && linkType=7 上门回收")
    @JsonProperty("danhaobind")
    @JSONField(name = "danhaobind")
    private Integer danHaoBind;

    /**
     * 类别
     */
    @ApiModelProperty("类别")
    @JsonProperty("wutype")
    @JSONField(name = "wutype")
    private Integer wuType;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    @Length(max = 1500, message = "备注不能超过1500字！")
    private String comment;

    /**
     * 批签和凭证
     */
    @JsonProperty("result1")
    @JSONField(name = "result1")
    private String result1;

    /**
     * 支付方式
     */
    @ApiModelProperty("支付方式")
    @JsonProperty("pay_method")
    @JSONField(name = "pay_method")
    private Integer payMethod;

    /**
     * 第三方快递
     */
    @ApiModelProperty("第三方快递")
    private String com;

    /**
     * 快递单号
     */
    @ApiModelProperty("快递单号")
    private String nu;

    @JsonProperty("JiujiSfExpressType")
    @JSONField(name = "JiujiSfExpressType")
    private String jiuJiSfExpressType;

    @JsonProperty("JiujiJdExpressType")
    @JSONField(name = "JiujiJdExpressType")
    private String jiujiJdExpressType;

    /**
     * 第三方付 月结卡号
     */
    @ApiModelProperty("第三方付 月结卡号")
    @JsonProperty("yuejiekahao")
    @JSONField(name = "yuejiekahao")
    private String yuejiekahao;

    /**
     * 登记时间
     */
    @ApiModelProperty("登记时间")
    @JsonProperty("dtime")
    @JSONField(name = "dtime")
    private LocalDateTime dTime;

    /**
     * 状态
     */
    @ApiModelProperty("状态")
    @JsonProperty("stats")
    @JSONField(name = "stats")
    private Integer stats;

    /**
     * (收件方)did
     */
    @ApiModelProperty("(收件方)did")
    @JsonProperty("sdid")
    @JSONField(name = "sdid")
    private Integer sDid;

    /**
     * (接件方)did
     */
    @ApiModelProperty("(接件方)did")
    @JsonProperty("rdid")
    @JSONField(name = "rdid")
    private Integer rDid;

    /**
     * 操作录入人
     */
    @ApiModelProperty("操作录入人")
    @JsonProperty("inuser")
    @JSONField(name = "inuser")
    private String inUser;

    /**
     * 关联类型 关联yuyueid（2:上门取件，3:送件上门） 关联sub_id(其它) 5 好像是维修  6 发票物流单  7 回收上门取件,11九机集市, 13固定资产调拨 ，20 配件调拨，21大件调拨
     */
    @ApiModelProperty("关联类型 关联yuyueid（2:上门取件，3:送件上门） 关联sub_id(其它) 5 好像是维修  6 发票物流单  7 回收上门取件,11九机集市, 13固定资产调拨 ，20 配件调拨，21大件调拨")
    @JsonProperty("linkType")
    @JSONField(name = "linkType")
    private String linkType;

    /**
     * 地区 ID
     */
    @ApiModelProperty("地区 ID")
    @JsonProperty("areaid")
    @JSONField(name = "areaid")
    private Integer areaId;

    /**
     * 寄的地区 ID
     */
    @ApiModelProperty("寄的地区 ID")
    @JsonProperty("sareaid")
    @JSONField(name = "sareaid")
    private Integer sareaid;

    @JsonProperty("sareaid_")
    @JSONField(name = "sareaid_")
    private String sareaid2;

    /**
     * 收的地区 ID
     */
    @ApiModelProperty("收的地区 ID")
    @JsonProperty("rareaid")
    @JSONField(name = "rareaid")
    private Integer rareaid;

    @JsonProperty("rareaid_")
    @JSONField(name = "rareaid_")
    private String rareaid2;

    /**
     * 签收人
     */
    @ApiModelProperty("签收人")
    @JsonProperty("receiveUser")
    @JSONField(name = "receiveUser")
    private String receiveUser;

    /**
     * 签收时间
     */
    @ApiModelProperty("签收时间")
    @JsonProperty("receiveTime")
    @JSONField(name = "receiveTime")
    private LocalDateTime receiveTime;

    /**
     * 其它接收地区（用于批量添加物流单）
     */
    @ApiModelProperty("其它接收地区（用于批量添加物流单）")
    @JsonProperty("otherarea_")
    @JSONField(name = "otherarea_")
    private String otherarea;

    /**
     * 通知类型 0 不通知(手动填加的默认不通知) 1和null 系统生成的物流单 2 售后转地区生成的物流单
     */
    @ApiModelProperty("通知类型 0 不通知(手动填加的默认不通知) 1和null 系统生成的物流单 2 售后转地区生成的物流单")
    @JsonProperty("notifyType")
    @JSONField(name = "notifyType")
    private Integer notifyType;

    /**
     * 订单类型 1 普通订单 2良品单 3售后单
     */
    @ApiModelProperty("订单类型 1 普通订单 2良品单 3售后单")
    @JsonProperty("subKinds")
    @JSONField(name = "subKinds")
    private Integer subKinds;

    /**
     * 合并后父级保留物流单号
     */
    @ApiModelProperty("合并后父级保留物流单号")
    @JsonProperty("wpid")
    @JSONField(name = "wpid")
    private Integer wuPid;

    /**
     * 物流分类 ID
     */
    @ApiModelProperty("物流分类 ID")
    @JsonProperty("wCateId")
    @JSONField(name = "wCateId")
    private Integer wCateId;

    /**
     * 打印次数
     */
    @ApiModelProperty("打印次数")
    @JsonProperty("printCnt")
    @JSONField(name = "printCnt")
    private Integer printCnt;

    /**
     * 是否是异常物流
     */
    @ApiModelProperty("是否是异常物流")
    @JsonProperty("isExceptionSub")
    @JSONField(name = "isExceptionSub")
    private Boolean isExceptionSub;

    /**
     * 是否手动创建物流单：true=是，false=否
     */
    @ApiModelProperty("是否手动创建物流单：true=是，false=否")
    @JsonProperty("isCreateManually")
    @JSONField(name = "isCreateManually")
    private Boolean isCreateManually;

    /**
     * 物流单生成地区
     */
    @ApiModelProperty("物流单生成地区")
    private String area;

    /**
     * 完成时间
     */
    @ApiModelProperty("完成时间")
    @JsonProperty("ctime")
    @JSONField(name = "ctime")
    private LocalDateTime cTime;

    /**
     * 异常备注
     */
    @ApiModelProperty("异常备注")
    @JsonProperty("exceptionRemark")
    @JSONField(name = "exceptionRemark")
    private String exceptionRemark;

    /**
     * 物流单附件 list
     */
    @ApiModelProperty("物流单附件 list")
    @JsonProperty("files")
    @JSONField(name = "files")
    private List<OaAttachmentsAddOrUpdateReqVO.FileBO> files;

    /**
     * 物流单日志 list
     */
    @ApiModelProperty("物流单日志 list")
    @JsonProperty("logs")
    @JSONField(name = "logs")
    private List<WuLiuLogResVO> logs;

    /**
     * sessionAreaId（自动创建物流单使用）
     */
    @JsonProperty("sessionAreaId")
    private Integer sessionAreaId;

    /**
     * username（自动创建物流单使用）
     */
    @JsonProperty("username")
    private String username;

    /**
     * 来源 0其他 1物流单列表添加
     */
    private Integer source;

    /**
     * 包裹数
     */
    private Integer packagesNumber;

    /**
     * 寄件地址经纬度
     * 腾讯地图坐标系
     */
    private String sendPosition;

    /**
     * 收件地址经纬度
     * 腾讯地图坐标系
     */
    private String receivePosition;

    /**
     * 寄详细地址
     */
    private String sdetailedAddress;
    /**
     * 收详细地址
     */
    private String rdetailedAddress;
    /**
     * 不校验单号
     */
    private Boolean noCheckSub;

    /**
     * 内部流转数据 start
     */
    /**
     * 是否为新建物流单
     */
    @JSONField(serialize = false, deserialize = false)
    @JsonIgnore
    private transient Boolean isSuccessInsertWuLiu;
    /**
     * 是否异步呼叫跑腿
     */
    @JSONField(serialize = false, deserialize = false)
    @JsonIgnore
    private transient Boolean isCallIngPaoTui;
    /**
     * 内部流转数据 end
     */

    public String toUpdateLockKey(){
        if(ObjectUtil.defaultIfNull(this.getWuliuid(), 0) >0){
            return StrUtil.format("wuliuId: {}", this.getWuliuid());
        }
        if(ObjectUtil.defaultIfNull(this.getSubId(), 0) >0){
            return StrUtil.format("subId: {}", this.getSubId());
        }
        return DigestUtil.md5Hex(JSON.toJSONString(this));
    }
}
