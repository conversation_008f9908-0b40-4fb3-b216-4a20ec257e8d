package com.jiuji.oa.wuliu.service;

import com.jiuji.oa.wuliu.entity.ShansongStoreConfigsEntity;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 获取闪送配置
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-12-05
 */
public interface IShansongStoreConfigsService extends IService<ShansongStoreConfigsEntity> {

    /**
     * 获取闪送配置（带redis缓存）
     * @return
     */
    List<ShansongStoreConfigsEntity> getStoreConfigs();
}
