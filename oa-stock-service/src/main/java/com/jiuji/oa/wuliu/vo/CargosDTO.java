package com.jiuji.oa.wuliu.vo;


import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Cargos
 * 包裹信息
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-16
 */
@Data
@Accessors(chain = true)
public class CargosDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商品名称
     */
    @JsonProperty("cargo_name")
    @JSONField(name = "cargo_name")
    private String cargoName;

    /**
     * 商品类型
     */
    @JsonProperty("cargo_category")
    @JSONField(name = "cargo_category")
    private String cargoCategory;

    /**
     * 商品数量
     */
    @JsonProperty("cargo_quantity")
    @JSONField(name = "cargo_quantity")
    private Integer cargoQuantity;

    /**
     * 商品单价
     */
    @JsonProperty("cargo_value")
    @JSONField(name = "cargo_value")
    private BigDecimal cargoValue;

    /**
     * 商品重量
     */
    @JsonProperty("cargo_weight")
    @JSONField(name = "cargo_weight")
    private BigDecimal cargoWeight;

}
