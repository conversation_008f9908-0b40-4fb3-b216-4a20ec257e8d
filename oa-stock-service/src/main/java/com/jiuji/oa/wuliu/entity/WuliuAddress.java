package com.jiuji.oa.wuliu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 
 * @TableName wuliu_address
 */
@TableName(value ="wuliu_address")
@Data
public class WuliuAddress implements Serializable {
    /**
     * 
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 
     */
    @TableField(value = "wlid")
    private Integer wlid;

    /**
     * 
     */
    @TableField(value = "sareaid")
    private Integer sareaid;

    /**
     * 
     */
    @TableField(value = "sname")
    private String sname;

    /**
     * 
     */
    @TableField(value = "smobile")
    private String smobile;

    /**
     * 
     */
    @TableField(value = "saddress")
    private String saddress;

    /**
     * 
     */
    @TableField(value = "rareaid")
    private Integer rareaid;

    /**
     * 
     */
    @TableField(value = "rname")
    private String rname;

    /**
     * 
     */
    @TableField(value = "rmobile")
    private String rmobile;

    /**
     * 
     */
    @TableField(value = "raddress")
    private String raddress;

    /**
     * 
     */
    @TableField(value = "kdorder")
    private String kdorder;

    /**
     * 
     */
    @TableField(value = "inuser")
    private String inuser;

    /**
     * 
     */
    @TableField(value = "addtime")
    private LocalDateTime addtime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}