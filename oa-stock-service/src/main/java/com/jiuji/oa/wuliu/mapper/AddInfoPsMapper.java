package com.jiuji.oa.wuliu.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.wuliu.entity.AddInfoPsEntity;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 用户地址操作记录,责任小组：会员 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-13
 */
public interface AddInfoPsMapper extends BaseMapper<AddInfoPsEntity> {

    /**
     * getByShouHouId
     *
     * @param shouHouId
     * @param yuyueId
     * @return AddInfoPsEntity
     */
    AddInfoPsEntity getByShouHouId(@Param("shouHouId") Integer shouHouId, @Param("yuyueId") Integer yuyueId);
}
