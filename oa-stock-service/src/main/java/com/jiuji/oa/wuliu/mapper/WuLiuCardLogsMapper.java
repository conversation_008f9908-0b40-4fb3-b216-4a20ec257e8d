package com.jiuji.oa.wuliu.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.wuliu.entity.WuLiuCardLogsEntity;
import org.apache.ibatis.annotations.Mapper;

/**
 * 银行卡日志[责任小组:财务] Mapper 接口
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-11-04
 */
@Mapper
public interface WuLiuCardLogsMapper extends BaseMapper<WuLiuCardLogsEntity> {

    /**
     * 获取预约单使用的优惠码
     *
     * @param id
     * @return String
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-04
     */
    String getYuyueYouHuiMa(Integer id);

}
