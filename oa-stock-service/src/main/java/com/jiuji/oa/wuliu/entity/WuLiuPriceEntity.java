package com.jiuji.oa.wuliu.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 物流价格 Entity
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-09-29
 */
@Data
@Accessors(chain = true)
@TableName("wuliuPriceConfigure")
public class WuLiuPriceEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Integer id;

    /**
     * 发送地
     */
    @TableField("cityid")
    private Integer cityId;

    /**
     * 接受地
     */
    @TableField("tocityid")
    private Integer toCityId;

    /**
     * 是否同城
     */
    @TableField("istonchen")
    private Boolean tonChenFlag;

    /**
     * 首重
     */
    @TableField("unitPrice")
    private BigDecimal unitPrice;

    /**
     * 续重
     */
    @TableField("advancePrice")
    private BigDecimal advancePrice;

    /**
     * 类型
     */
    @TableField("type_")
    private Integer type;

}
