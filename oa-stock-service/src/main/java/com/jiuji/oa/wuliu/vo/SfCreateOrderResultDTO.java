package com.jiuji.oa.wuliu.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * SfCreateOrderResult.SfCreateOrderResult
 * 顺丰中台创建订单返回结果
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-16
 */
@Data
@Accessors(chain = true)
public class SfCreateOrderResultDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 物流单号
     */
    @JsonProperty("customerOrderNo")
    @JSONField(name = "customerOrderNo")
    private String customerOrderNo;

    /**
     * 顺丰面单
     */
    @JsonProperty("mainMailNo")
    @JSONField(name = "mainMailNo")
    private String mainMailNo;

    /**
     * 收件人地址
     */
    @JsonProperty("receiveAddress")
    @JSONField(name = "receiveAddress")
    private String receiveAddress;

    /**
     * 收件人名称
     */
    @JsonProperty("receiveName")
    @JSONField(name = "receiveName")
    private String receiveName;

    /**
     * 收件人电话
     */
    @JsonProperty("receiveTel")
    @JSONField(name = "receiveTel")
    private String receiveTel;

    /**
     * 寄件人地址
     */
    @JsonProperty("sendAddress")
    @JSONField(name = "sendAddress")
    private String sendAddress;

    /**
     * 寄件人名称
     */
    @JsonProperty("sendName")
    @JSONField(name = "sendName")
    private String sendName;

    /**
     * 寄件人电话
     */
    @JsonProperty("sendTel")
    @JSONField(name = "sendTel")
    private String sendTel;

    /**
     *
     */
    @JsonProperty("shopName")
    @JSONField(name = "shopName")
    private String shopName;

    /**
     * 打单时的路由标签信息
     */
    @JsonProperty("destRouteLabel")
    @JSONField(name = "destRouteLabel")
    private String destRouteLabel;
    /**
     * 二维码
     */
    @JsonProperty("twoDimensionCode")
    @JSONField(name = "twoDimensionCode")
    private String twoDimensionCode;
    /**
     * 原寄地编码
     */
    @JsonProperty("originCode")
    @JSONField(name = "originCode")
    private String originCode;
    /**
     * 目的地编码
     */
    @JsonProperty("destCode")
    @JSONField(name = "destCode")
    private String destCode;

    /**
     * 子运单号
     */
    List<SubWaybillNoDataRes> subWaybillNoList;
}
