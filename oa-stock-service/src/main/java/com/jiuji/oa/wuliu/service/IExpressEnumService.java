package com.jiuji.oa.wuliu.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.nc.dict.bo.LogisticsExpressConfigBo;
import com.jiuji.oa.wuliu.entity.ExpressEnumEntity;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IExpressEnumService extends IService<ExpressEnumEntity> {

    /**
     * 查询快递公司
     * @param expressCode
     * @return
     */
    String getWuliuCompanyName(String expressCode);

    /**
     * 查询ExpressEnum，包含isdel=1
     * @return
     */
    List<ExpressEnumEntity> queryAllExpressEnum();

    /**
     * 更新九机特惠快递配置
     * @param logisticsExpressConfigBo
     */
    void configLogisticsExpressEnum(LogisticsExpressConfigBo logisticsExpressConfigBo);
}
