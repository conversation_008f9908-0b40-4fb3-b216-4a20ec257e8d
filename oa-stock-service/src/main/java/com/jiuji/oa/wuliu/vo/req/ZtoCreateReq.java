package com.jiuji.oa.wuliu.vo.req;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * zto创建请求
 *
 * <AUTHOR>
 * @date 2021/11/04
 */
@Data
@Accessors(chain = true)
public class ZtoCreateReq implements Serializable {

    private static final long serialVersionUID = 1L;

    private ZopSdkProperty zopSdkProperty;
    private Boolean needBigMark;
    private Boolean isSaveVas;
    /**
     * 网点code
     */
    private String prepareSiteCode;
    /**
     * 网点名称
     */
    private String prepareSiteName;
    /**
     * 渠道,传合作商编码
     */
    private String partnerId;
    private String partnerOrderCode;
    private PreOrderWaybillAccountReqDto preOrderWaybillAccountReqDto;
    private String receiveProvince;
    private String receiveCity;
    private String receiveDistrict;
    private String receiveAddress;
    private String receiveName;
    private String receivePhone;
    private Boolean saveVas;
    private String sendProvince;
    private String sendCity;
    private String sendDistrict;
    private String sendAddress;
    /**
     * 寄件人ID，传寄件地区id
     */
    private String sendId;
    private String sendMobile;
    private String sendName;
    private String sendCompany;


    @Data
    @Accessors(chain = true)
    public static class ZopSdkProperty implements Serializable {

        private static final long serialVersionUID = 1L;

        private String companyId;
        private String key;
        private String url;
    }

    @Data
    @Accessors(chain = true)
    public static class PreOrderWaybillAccountReqDto implements Serializable {

        private static final long serialVersionUID = 1L;

        private String account;
    }
}
