package com.jiuji.oa.wuliu.utils;

import com.ch999.common.util.atlas.CoordinateUtil;
import com.ch999.common.util.vo.atlas.Coordinate;
import com.jiuji.oa.nc.common.exception.CustomizeException;
import com.jiuji.oa.stock.common.util.GeoCoordinateUtils;
import com.jiuji.oa.stock.common.vo.AddressToCoordinateVO;
import com.jiuji.oa.wuliu.vo.LocationVO;
import lombok.extern.slf4j.Slf4j;

import javax.validation.constraints.NotNull;
import java.util.Objects;

/**
 * 百度地图工具
 *
 * <AUTHOR>
 * @date 2021 /7/30 16:41
 */
@Slf4j
public final class BaiduMapUtil {

    private static final String AK = "2E9486ae97437e84c519f008952b67ae";
    /**
     * 地理编码 V2.0 接口
     */
    //private static final String GEOCODING_URL_V2 = "http://api.map.baidu.com/geocoder/v2/?address=%s&output=json&ak=%s";
    /**
     * status 返回结果状态值， 成功返回0，其他值请查看下方返回码状态表。
     */
    private static final int SUCCESS_STATUS = 0;

    private BaiduMapUtil() {
    }

    /**
     * C# Range.GetGPS
     * 地址获取经纬度
     * <p>
     * 百度接口文档
     * https://lbsyun.baidu.com/index.php?title=webapi/guide/webservice-geocoding
     *
     * @param address 待解析的地址。最多支持84个字节。
     *                可以输入两种样式的值，分别是：
     *                1、标准的结构化地址信息，如北京市海淀区上地十街十号 【推荐，地址结构越完整，解析精度越高】
     *                2、支持“*路与*路交叉口”描述方式，如北一环路和阜阳路的交叉路口
     *                第二种方式并不总是有返回结果，只有当地址库中存在该地址描述时才有返回。
     * @return LocationVO
     * <AUTHOR>
     * @date 2021-11-17
     */
    /*public static LocationVO getLocation(@NotNull String address) {
        LocationVO res = new LocationVO();
        String body = HttpUtil.get(String.format(GEOCODING_URL_V2, address, AK));
        if (StringUtils.isNotBlank(body)) {
            BaiduMapVO baiduMap = JacksonJsonUtils.toClass(body, BaiduMapVO.class);
            OptionalUtils.ifNotNull(baiduMap, item ->
                    OptionalUtils.ifTrue(Objects.equals(SUCCESS_STATUS, baiduMap.getStatus()),
                            () -> res.setLongitude(item.getResult().getLocation().getLng())
                                    .setLatitude(item.getResult().getLocation().getLat())));
        }
        return res;
    }*/

    /**
     * C# Range.GetGPS
     * 地址获取经纬度
     * <p>
     * 百度接口文档
     * https://lbsyun.baidu.com/index.php?title=webapi/guide/webservice-geocoding
     *
     * @param address 待解析的地址。最多支持84个字节。
     *                可以输入两种样式的值，分别是：
     *                1、标准的结构化地址信息，如北京市海淀区上地十街十号 【推荐，地址结构越完整，解析精度越高】
     *                2、支持“*路与*路交叉口”描述方式，如北一环路和阜阳路的交叉路口
     *                第二种方式并不总是有返回结果，只有当地址库中存在该地址描述时才有返回。
     * @return LocationVO
     * <AUTHOR>
     * @date 2021-11-17
     */
    public static LocationVO getLocation(@NotNull String address, String city) {
        AddressToCoordinateVO addressToCoordinate = GeoCoordinateUtils.addressToCoordinateV2(address, city);
        if (Objects.isNull(addressToCoordinate)) {
            log.error("地址转经纬度错误，address={}，city={}",address,city);
            throw new CustomizeException("请检查寄件和收件地址是否正确");
        }
        Coordinate baiduCoordinate = CoordinateUtil.wgs2bd(addressToCoordinate.getMinSegmentMiddleCoordinate());
        LocationVO locationVO = new LocationVO();
        locationVO.setLongitude(baiduCoordinate.getLongitude());
        locationVO.setLatitude(baiduCoordinate.getLatitude());
        return locationVO;
    }

}
