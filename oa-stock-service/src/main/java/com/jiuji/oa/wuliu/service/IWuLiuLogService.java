/*
 *     Copyright © 2006 - 2020 九机网 All Rights Reserved
 *
 */

package com.jiuji.oa.wuliu.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.stock.logistics.order.vo.req.SaveWuLiuLogReq;
import com.jiuji.oa.wuliu.entity.WuLiuLogEntity;
import com.jiuji.oa.wuliu.vo.res.WuLiuLogResVO;

import java.util.List;

/**
 * 物流单日志Service
 *
 * <AUTHOR> liu ming
 * @date 2021-05-24 18:40:13
 */
public interface IWuLiuLogService extends IService<WuLiuLogEntity> {

    /**
     * 根据物流id保存物流日志
     *
     * @param saveWuLiuLogReq
     */
    void saveWuliuLog(SaveWuLiuLogReq saveWuLiuLogReq);

    /**
     * 新增物流日志
     *
     * @param wuLiuId
     * @param inUser
     * @param msg
     * @return boolean
     * @date 2021-10-05
     * <AUTHOR> [<EMAIL>]
     */
    boolean addOne(Integer wuLiuId, String inUser, String msg);

    /**
     * 根据物流id保存物流日志
     *
     * @param saveWuLiuLogReq
     * @return WuLiuLogEntity
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-05
     */
    WuLiuLogEntity writeLogs(SaveWuLiuLogReq saveWuLiuLogReq);

    /**
     * getWuLiuLog
     *
     * @param wuliuid
     * @return List<WuLiuLogResVO>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-05
     */
    List<WuLiuLogResVO> getWuLiuLog(String wuliuid);

    /**
     * getWuLiuLog
     *
     * @param wuliuid
     * @return List<WuLiuLogEntity>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-10
     */
    List<WuLiuLogEntity> getWuLiuLog(Integer wuliuid);

    boolean saveLog(Integer wuLiuId, String inUser, String msg, Integer kind);

    /**
     * 批量保存日志
     */
    Integer saveLogBatch(List<WuLiuLogEntity> logs);
}
