package com.jiuji.oa.wuliu.vo.req;

import com.jiuji.oa.nc.common.req.OaAttachmentsAddOrUpdateReqVO;
import lombok.Data;
import lombok.experimental.Accessors;
import com.jiuji.oa.wuliu.vo.res.WuLiuLogResVO;


import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 物流单新增/更新 req VO
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-08
 */
@Data
@Accessors(chain = true)
public class WuLiuAddOrUpdateReqV2VO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 包裹数
     */
    private String packageCount;

    /**
     * 体积
     */
    private BigDecimal vloumn;

    /**
     * 快递公司原寄地编码
     */
    private String orgcode;

    /**
     * 快递公司目的地编码
     */
    private String destcode;

    /**
     * 运单类型  1:标准快递  2:顺风特惠  3:电商特惠
     */
    private String expressType;

    /**
     * 第三方支付方式 1:寄付月结 2:收件方付 3:第三方付
     */
    private String paytype;

    /**
     * 订单ID
     */
    private Integer subId;

    /**
     * (收件方)pid
     */
    private Integer spid;

    /**
     * (收件方)zid
     */
    private Integer szid;

    /**
     * (接件方)pid
     */
    private Integer rpid;

    /**
     * (接件方)zid
     */
    private Integer rzid;

    /**
     * 预约ID
     */
    private Integer yuyueId;

    /**
     * 预计送达时间
     */
    private LocalDateTime expectTime;

    /**
     * 目的地
     */
    private String destRouteLabel;

    /**
     * 日志消息
     */
    private String msg;

    /**
     * 顺风免单二维码信息
     */
    private String twoDimensionCode;

    /**
     * 美团光速达标识
     */
    private Boolean meiTuanFastFlag;

    /**
     * 用于传递三方物流接口的订单编号
     */
    private String ordercodeId;

    /**
     * 是否显示物流单报销按钮，true=是，false=否
     */
    private Boolean showWuliuClaimFlag;

    /**
     * 操作标识
     */
    private String actionName;

    /**
     * 物流单 ID
     */
    private Integer wuliuid;

    /**
     * (发送方)姓名
     */
    private String sname;

    /**
     * (发送方)手机号
     */
    private String smobile;

    /**
     * (发送方)地址，寄件方地址不能超过 250 个字符
     */
    private String saddress;

    /**
     * (发送方)大区
     */
    private String sareaM;

    /**
     * (发送方)城市ID（did）
     */
    private Integer scityId;

    /**
     * (接收方)姓名
     */
    private String rname;

    /**
     * (接收方)手机号
     */
    private String rmobile;

    /**
     * (接收方)地址，收件方地址不能超过 250 个字符
     */
    private String raddress;

    /**
     * 接收地区
     */
    private String rareaM;

    /**
     * (接收方)城市ID（did）
     */
    private Integer rcityId;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 成本价
     */
    private BigDecimal inPrice;

    /**
     * 重量
     */
    private BigDecimal weight;

    /**
     * 收件人
     */
    private String shouJianRen;

    /**
     * 派件人
     */
    private String paiJianRen;

    /**
     * linkType=2 || linkType=3 预约单
     * wuType=4 || wuType=6 订单
     * shouhouid != 0 售后单 ?
     * wuType=9 良品订单
     * wuType=7 && linkType=7 上门回收
     */
    private Integer danHaoBind;

    /**
     * 类别
     */
    private Integer wuType;

    /**
     * 备注，备注不能超过1500字！
     */
    private String comment;

    /**
     * 批签和凭证
     */
    private String result1;

    /**
     * 支付方式
     */
    private Integer payMethod;

    /**
     * 第三方快递
     */
    private String com;

    /**
     * 快递单号
     */
    private String nu;

    private String jiuJiSfExpressType;

    private String jiujiJdExpressType;

    /**
     * 第三方付 月结卡号
     */
    private String yuejiekahao;

    /**
     * 登记时间
     */
    private LocalDateTime dtime;

    /**
     * 状态
     */
    private Integer stats;

    /**
     * (收件方)did
     */
    private Integer sdid;

    /**
     * (接件方)did
     */
    private Integer rdid;

    /**
     * 操作录入人
     */
    private String inUser;

    /**
     * 关联类型 关联yuyueid（2:上门取件，3:送件上门） 关联sub_id(其它) 5 好像是维修  6 发票物流单  7 回收上门取件,11九机集市, 13固定资产调拨 ，20 配件调拨，21大件调拨
     */
    private String linkType;

    /**
     * 地区 ID
     */
    private Integer areaId;

    /**
     * 寄的地区 ID
     */
    private Integer sareaid;

    private String sareaid2;

    /**
     * 收的地区 ID
     */
    private Integer rareaid;

    private String rareaid2;

    /**
     * 签收人
     */
    private String receiveUser;

    /**
     * 签收时间
     */
    private LocalDateTime receiveTime;

    /**
     * 其它接收地区（用于批量添加物流单）
     */
    private String otherarea;

    /**
     * 通知类型 0 不通知(手动填加的默认不通知) 1和null 系统生成的物流单 2 售后转地区生成的物流单
     */
    private Integer notifyType;

    /**
     * 订单类型 1 普通订单 2良品单 3售后单
     */
    private Integer subKinds;

    /**
     * 合并后父级保留物流单号
     */
    private Integer wuPid;

    /**
     * 物流分类 ID
     */
    private Integer wcateId;

    /**
     * 打印次数
     */
    private Integer printCnt;

    /**
     * 是否是异常物流，true=是，false=否
     */
    private Boolean exceptionSubFlag;

    /**
     * 是否手动创建物流单：true=是，false=否
     */
    private Boolean createManuallyFlag;

    /**
     * 物流单生成地区
     */
    private String area;

    /**
     * 完成时间
     */
    private LocalDateTime ctime;

    /**
     * 异常备注
     */
    private String exceptionRemark;

    /**
     * 物流单附件 list
     */
    private List<OaAttachmentsAddOrUpdateReqVO.FileBO> files;

    /**
     * 物流单日志 list
     */
    private List<WuLiuLogResVO> logs;

    /**
     * sessionAreaId（自动创建物流单使用）
     */
    private Integer sessionAreaId;

    /**
     * username（自动创建物流单使用）
     */
    private String username;

}
