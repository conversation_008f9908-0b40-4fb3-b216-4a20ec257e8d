package com.jiuji.oa.wuliu.vo;


import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * JdCreateOrderParam.JdOrderParam
 * 包裹信息
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-16
 */
@Data
@Accessors(chain = true)
public class JdOrderParamDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("WuliuId")
    @JSONField(name ="wuliuId")
    private Integer wuliuId;

    @JsonProperty("SendAreaId")
    @JSONField(name ="SendAreaId")
    private Integer sendAreaId;

    @JsonProperty("ReceiveAreaId")
    @JSONField(name ="ReceiveAreaId")
    private Integer receiveAreaId;

    @JsonProperty("ReceiverName")
    @JSONField(name ="ReceiverName")
    private String receiverName;

    @JsonProperty("ExpressNumber")
    @JSONField(name ="ExpressNumber")
    private String expressNumber;

    @JsonProperty("ReceiverMobile")
    @JSONField(name ="ReceiverMobile")
    private String receiverMobile;

    @JsonProperty("ReceiverAddress")
    @JSONField(name ="ReceiverAddress")
    private String receiverAddress;

    @JsonProperty("SendName")
    @JSONField(name ="SendName")
    private String sendName;

    @JsonProperty("SendMobile")
    @JSONField(name ="SendMobile")
    private String sendMobile;

    @JsonProperty("SendAddress")
    @JSONField(name ="SendAddress")
    private String sendAddress;

    @JsonProperty("GoodsWeight")
    @JSONField(name ="GoodsWeight")
    private String goodsWeight;

    @JsonProperty("PackageCount")
    @JSONField(name ="PackageCount")
    private String packageCount;

    @JsonProperty("ChildExpressType")
    @JSONField(name ="ChildExpressType")
    private Integer childExpressType;

    @JsonProperty("Vloumn")
    @JSONField(name ="Vloumn")
    private BigDecimal vloumn;

    @JsonProperty("SenderProvinceName")
    @JSONField(name ="SenderProvinceName")
    private String senderProvinceName;

    @JsonProperty("SenderCityName")
    @JSONField(name ="SenderCityName")
    private String senderCityName;

    @JsonProperty("SenderCountryName")
    @JSONField(name ="SenderCountryName")
    private String senderCountryName;

    @JsonProperty("ReceiverProvinceName")
    @JSONField(name ="ReceiverProvinceName")
    private String receiverProvinceName;

    @JsonProperty("ReceiverCityName")
    @JSONField(name ="ReceiverCityName")
    private String receiverCityName;

    @JsonProperty("ReceiverCountryName")
    @JSONField(name ="ReceiverCountryName")
    private String receiverCountryName;

}
