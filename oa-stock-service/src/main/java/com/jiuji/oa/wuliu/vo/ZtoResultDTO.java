package com.jiuji.oa.wuliu.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 中通接口返回结果
 *
 * <AUTHOR>
 * @date 2021/11/04
 */
@Data
@Accessors(chain = true)
public class ZtoResultDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 状态
     */
    private Boolean status;
    /**
     * 消息
     */
    private String message;
    /**
     * 状态码
     */
    private String statusCode;
    /**
     * 结果
     */
    private ZtoInfoDTO result;
    /**
     * 错误信息
     */
    private String errorMsg;
}
