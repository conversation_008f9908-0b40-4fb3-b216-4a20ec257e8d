package com.jiuji.oa.wuliu.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.wuliu.dto.ShouHouAddInfoDTO;
import com.jiuji.oa.wuliu.dto.YuYueAddInfoDTO;
import com.jiuji.oa.wuliu.entity.ShouHouYuYueEntity;
import com.jiuji.oa.wuliu.entity.ShouHouYuYueEntity2;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 售后预约表,责任小组：销售 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-09
 */
public interface ShouHouYuYueMapper extends BaseMapper<ShouHouYuYueEntity> {

    /**
     * 获取一条预约信息
     *
     * @param id
     * @return
     */
    ShouHouYuYueEntity getOne(@Param("id") Integer id);

    /**
     * 获取一条预约信息
     *
     * @param id
     * @return ShouHouYuYueEntity2
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-04
     */
    ShouHouYuYueEntity2 getOne2(@Param("id") Integer id);

    /**
     * 查询预约物流收寄件信息
     */
    YuYueAddInfoDTO getYuYueAddInfo(@Param("yuyueId") Integer yuyueId);

    /**
     * 查询售后物流收寄件信息
     * @param shouhouId
     * @return
     */
    ShouHouAddInfoDTO getShouhouAddInfo(@Param("shouhouId") Integer shouhouId);
}
