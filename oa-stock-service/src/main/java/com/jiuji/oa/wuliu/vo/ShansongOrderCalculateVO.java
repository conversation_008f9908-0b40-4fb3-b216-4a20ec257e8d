package com.jiuji.oa.wuliu.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/8 15:16
 */
@Data
public class ShansongOrderCalculateVO {

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 寄件人信息
     */
    private ShansongSenderVO sender;

    /**
     * 收件人信息
     */
    private List<ShansongReceiverVO> receiverList;

    /**
     * 预约类别
     * 0立即单，1预约单
     */
    private Integer appointType;

    /**
     * 预约时间(指的是预约取件时间)
     */
    private String appointmentDate;

    /**
     * 店铺ID(对应闪送门店ID)
     */
    private Long storeId;

    /**
     * 可指定的交通工具方式
     * 0未指定; 2摩托车; 8汽车；默认为0
     */
    private Integer travelWay;

    /**
     * 帮我取 帮我送
     * 1.帮我送 2.帮我取 ；默认为1
     */
    private Integer deliveryType;

}
