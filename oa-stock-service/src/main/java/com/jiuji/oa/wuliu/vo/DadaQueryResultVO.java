package com.jiuji.oa.wuliu.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * DadaQueryResultVO
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-12-13
 */
@Setter
@Getter
public class DadaQueryResultVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String orderId;

    /**
     * 订单状态(待接单＝1,待取货＝2,配送中＝3,已完成＝4,已取消＝5, 指派单=8,妥投异常之物品返回中=9, 妥投异常之物品返回完成=10, 骑士到店=100,创建达达运单失败=1000）
     */
    private Integer statusCode;
    private String statusMsg;
    private String transporterName;
    private String transporterPhone;
    private BigDecimal deliveryFee;
    private String createTime;
    private String acceptTime;
    private String fetchTime;
    private String finishTime;
    private String cancelTime;
    private BigDecimal deductFee;

}
