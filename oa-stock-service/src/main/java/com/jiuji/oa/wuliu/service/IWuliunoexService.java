package com.jiuji.oa.wuliu.service;

import com.jiuji.oa.wuliu.entity.Wuliunoex;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 *
 */
public interface IWuliunoexService extends IService<Wuliunoex> {

    /**
     * 物流id查询子单号
     * @param wuliuId
     * @return
     */
    List<Wuliunoex> queryByWuliuId(Integer wuliuId);

    void deleteByWuliuIds(List<Integer> wuliuIdList);
}
