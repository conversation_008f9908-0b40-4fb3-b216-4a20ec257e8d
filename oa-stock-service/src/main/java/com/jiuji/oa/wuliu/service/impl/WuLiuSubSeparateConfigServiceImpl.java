package com.jiuji.oa.wuliu.service.impl;

import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.stock.common.cache.RedisUtils;
import com.jiuji.oa.wuliu.entity.WuLiuSubSeparateConfigEntity;
import com.jiuji.oa.wuliu.mapper.WuLiuSubSeparateConfigMapper;
import com.jiuji.oa.wuliu.service.IWuLiuSubSeparateConfigService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 服务实现类
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-15
 */
@Service
public class WuLiuSubSeparateConfigServiceImpl extends ServiceImpl<WuLiuSubSeparateConfigMapper, WuLiuSubSeparateConfigEntity> implements IWuLiuSubSeparateConfigService {

    @Override
    public List<WuLiuSubSeparateConfigEntity> subSeparateConfigCache() {
        List<WuLiuSubSeparateConfigEntity> list;
        String subSeparateConfigKey = "subSeparateConfigCache2";
        if (RedisUtils.hasKey(subSeparateConfigKey)) {
            list = RedisUtils.get(subSeparateConfigKey, new TypeReference<List<WuLiuSubSeparateConfigEntity>>() {
            });
        } else {
            list = lambdaQuery().list();
            if (!list.isEmpty()) {
                RedisUtils.set(subSeparateConfigKey, list, 190);
            } else {
                RedisUtils.set(subSeparateConfigKey, list, 11);
            }
        }
        return Optional.ofNullable(list).orElseGet(ArrayList::new);
    }
}
