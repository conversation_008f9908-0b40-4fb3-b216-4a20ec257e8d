package com.jiuji.oa.wuliu.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.wuliu.entity.ShansongAppConfigs;
import com.jiuji.oa.wuliu.service.IShansongAppConfigsService;
import com.jiuji.oa.wuliu.mapper.ShansongAppConfigsMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

/**
 * ShansongAppConfigsServiceImpl
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-12-05
 */
@Service
@DS("ch999oanew")
public class ShansongAppConfigsServiceImpl extends ServiceImpl<ShansongAppConfigsMapper, ShansongAppConfigs>
implements IShansongAppConfigsService {
    private ShansongAppConfigs config;

    @Override
    public ShansongAppConfigs getShansongAppConfigs() {
        if (ObjectUtils.isEmpty(config)) {
            config = this.baseMapper.selectShansongAppConfigs();
        }
        return config;
    }
}




