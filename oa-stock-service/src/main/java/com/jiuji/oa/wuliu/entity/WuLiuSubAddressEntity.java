package com.jiuji.oa.wuliu.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 收货地址表 Entity
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-08
 */
@Data
@Accessors(chain = true)
@TableName("SubAddress")
public class WuLiuSubAddressEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    /**
     * 单号
     */
    private Integer subId;

    /**
     * 收货地址
     */
    @TableField("Address")
    private String address;

    /**
     * 收货区域ID
     */
    @TableField("cityid")
    private Integer cityId;

    /**
     * 配送人员
     */
    @TableField("psuser")
    private String psUser;

    /**
     * 物流公司
     */
    @TableField("wuliucompany")
    private String wuLiuCompany;

    /**
     * 物流号
     */
    @TableField("wuliuNo")
    private String wuLiuNo;

    /**
     * 配送时间
     */
    @TableField("paisongdtime")
    private LocalDateTime paiSongTime;

    /**
     * 配送状态
     */
    @TableField("paisongState")
    private Integer paiSongState;

    /**
     * 短信是否发送
     */
    @TableField("issms_send")
    private Boolean smsSendFlag;

    @TableField("waitTime")
    private LocalDateTime waitTime;

    @TableField("sendTime")
    private LocalDateTime sendTime;

    /**
     * 删单时间
     */
    @TableField("expectTime")
    private LocalDateTime expectTime;

    @TableField("isXianHuo")
    private Boolean xianHuoFlag;

    /**
     * 用户选择配送日期
     */
    @TableField("userDate")
    private LocalDate userDate;

    /**
     * 用户配送时段
     */
    @TableField("userTime")
    private Integer userTime;

    /**
     * 标识 ：1、地址异常
     */
    @TableField("isSpecial")
    private Integer specialFlag;

    @TableField("TuotouType")
    private Integer tuoTouType;

    @TableField("ch999userid")
    private Integer ch999UserId;

    /**
     * 派送门店
     */
    @TableField("paisongAreaId")
    private Integer paiSongAreaId;

    /**
     * 加入派送单时间
     */
    @TableField("addTime")
    private LocalDateTime addTime;

    private String position;

    @TableField("inOneHourRange")
    private Boolean inOneHourRange;

    /**
     * 线上下单位置坐标
     */
    private String subAddPosition;

    /**
     * 线上下单位置坐标最近门店
     */
    @TableField("near_areaId")
    private Integer nearAreaId;

    /**
     * 线上下单导购员姓名
     */
    private String subProvide;

    @TableField("SubAddress_rv")
    private Byte[] subAddressRv;

}
