package com.jiuji.oa.wuliu.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 闪送-物品标签枚举
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum ShansongGoodsTypeEnum {

    /**
     * 文件广告
     */
    FILE_OR_AD(1, "文件广告"),

    /**
     * 电子产品
     */
    ELECTRONIC_PRODUCT(3, "电子产品"),

    /**
     * 蛋糕
     */
    CAKE(5, "蛋糕"),

    /**
     * 快餐水果
     */
    FAST_FOOD_FRUIT(6, "快餐水果"),

    /**
     * 鲜花绿植
     */
    FRESH_FLOWERS_GREEN_PLANTS(7, "鲜花绿植"),

    /**
     * 海鲜水产
     */
    SEA_FOOD(8, "海鲜水产"),

    /**
     * 汽车配件
     */
    AUTO_PARTS(9, "汽车配件"),

    /**
     * 其他
     */
    OTHER(10, "其他"),

    /**
     * 宠物
     */
    PETS(11, "宠物"),

    /**
     * 母婴
     */
    MOTHER_AND_BABY(12, "母婴"),

    /**
     * 医药健康
     */
    MEDICAL_HEALTH(13, "医药健康"),

    /**
     * 教育
     */
    EDUCATION(14, "教育");

    private final Integer code;
    private final String message;
}
