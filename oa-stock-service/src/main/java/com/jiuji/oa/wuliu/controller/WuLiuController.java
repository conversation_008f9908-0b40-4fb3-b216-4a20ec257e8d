/*
 *    Copyright © 2006 - 2020 九机网 All Rights Reserved
 *
 */

package com.jiuji.oa.wuliu.controller;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.cloud.logistics.enums.DePonTransportTypeEnum;
import com.jiuji.oa.nc.common.bo.OaUserBO;
import com.jiuji.oa.nc.common.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.stock.common.aspect.RepeatSubmitCheck;
import com.jiuji.oa.stock.common.enums.JiuJiOrderTypeEnum;
import com.jiuji.oa.stock.common.util.SysUtils;
import com.jiuji.oa.stock.logistics.order.enums.WuliuStatusEnum;
import com.jiuji.oa.stock.publiccheck.annotation.CheckHeader;
import com.jiuji.oa.wuliu.dto.req.WuLiuNoteReq;
import com.jiuji.oa.wuliu.dto.req.WuliuInvalidReqV2;
import com.jiuji.oa.wuliu.dto.res.LogisticsMessage;
import com.jiuji.oa.wuliu.dto.res.WuLiuNoteRes;
import com.jiuji.oa.wuliu.entity.ShunfengCustidConfigEntity;
import com.jiuji.oa.wuliu.enums.*;
import com.jiuji.oa.wuliu.service.*;
import com.jiuji.oa.wuliu.vo.*;
import com.jiuji.oa.wuliu.vo.req.*;
import com.jiuji.oa.wuliu.vo.res.*;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.enums.EnumUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 物流单 Controller
 *
 * <AUTHOR>
 * @date 2021-05-17 11:24:40
 */
@RestController
@RequestMapping("/api/wuliu")
@RequiredArgsConstructor
@Api(value = "logistics-wuliu", tags = "物流单")
public class WuLiuController {

    private final IWuLiuService wuLiuService;
    private final IWuLiuBusService wuLiuBusService;
    private final IWuLiuNoteService wuLiuNoteService;
    private final IWuLiuDeliveryService wuLiuDeliveryService;
    private final AbstractCurrentRequestComponent component;
    private final IWuLiuCategoryService wuLiuCategoryService;
    private final IWuLiuSubService wuLiuSubService;


    /**
     * 根据物流单号查询
     * @return
     */
    @CheckHeader
    @ApiOperation(value = "根据物流单号查询")
    @PostMapping("/getLogisticsMessage/v1")
    public R<List<LogisticsMessage>> getLogisticsMessage(@RequestBody @Valid LogisticsMessageVo logisticsMessageVo) {
        List<LogisticsMessage> logisticsMessage = wuLiuService.getLogisticsMessage(logisticsMessageVo);
        return R.success(logisticsMessage);
    }

    /**
     * 物流单枚举
     *
     * @return R<Map < String, Object>>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-19
     */
    @ApiOperation(value = "物流单枚举")
    @GetMapping("/enums/v1")
    public R<Map<String, Object>> enumsV1(@RequestParam(required = false, defaultValue = "0") Integer allStatus,
                                          @RequestParam(required = false, name = "wuLiuId") String wuLiuId) {
        Map<String, Object> map = new HashMap<>(MapUtil.DEFAULT_INITIAL_CAPACITY);
        map.put("type", WuliuStatusEnum.getListV2(allStatus));
        map.put("wuLiuType", EnumUtil.toEnumVOList(WuLiuTypeEnum.class));
        map.put("expressEnum", wuLiuService.listExpressEnumV2(Convert.toInt(wuLiuId)));
        map.put("expressPayMode", EnumUtil.toEnumVOList(ExpressPayModeEnum.class));
        map.put("orderType", EnumUtil.toEnumVOList(JiuJiOrderTypeEnum.class));
        map.put("JiujiSfExpressType", EnumUtil.toEnumVOList(JiuJiSfExpressTypeEnum.class));
        map.put("JiujiJdExpressType", EnumUtil.toEnumVOList(JiujiJdxpressTypeEnum.class));
        map.put("delivery", wuLiuDeliveryService.listAllEnumVo());
        map.put("dePonTransportTypeEnum", EnumUtil.toEnumVOList(DePonTransportTypeEnum.class));
        return R.success(map);
    }

    /**
     * 物流单枚举
     *
     * @return R<Map < String, Object>>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-19
     */
    @ApiOperation(value = "物流单枚举")
    @GetMapping("/enums/v2")
    public R<Map<String, Object>> enumsV2(@RequestParam(required = false, defaultValue = "0") Integer allStatus,
                                          @RequestParam(required = false, name = "wuLiuId") String wuLiuId) {
        Map<String, Object> map = new HashMap<>(MapUtil.DEFAULT_INITIAL_CAPACITY);
        map.put("wuLiuType", EnumUtil.toEnumVOList(WuLiuTypeEnum.class));
        map.put("expressEnum", wuLiuService.listExpressEnumV2(Convert.toInt(wuLiuId)));
        map.put("expressPayMode", EnumUtil.toEnumVOList(ExpressPayModeEnum.class));
        map.put("orderType", EnumUtil.toEnumVOList(JiuJiOrderTypeEnum.class));
        map.put("delivery", wuLiuDeliveryService.listAllEnumVo());
        map.put("searchKindEnum", SearchKindEnum.getEnumVoList());
        map.put("wuliuSatusEnum", WuliuStatusEnum.getListV2(allStatus));
        map.put("wCateEnum", wuLiuCategoryService.getEnumVoList());
        map.put("jiujiSfExpressType", EnumUtil.toEnumVOList(JiuJiSfExpressTypeEnum.class));
        map.put("jiujiJdExpressType", JiujiJdxpressTypeEnum.getEnumList());
        map.put("dePonTransportTypeEnum", EnumUtil.toEnumVOList(DePonTransportTypeEnum.class));
        return R.success(map);
    }

    /**
     * /addOrder/wuliu
     * 新增/更新物流单
     *
     * @param vo WuLiuAddOrUpdateReqVO
     * @return R<Integer>
     * @date 2021-09-29
     * <AUTHOR> [<EMAIL>]
     */
    @ApiOperation(value = "新增/更新物流单")
    @PostMapping("/add-or-update/v1")
    @RepeatSubmitCheck(expression = "#{classFullName}:#{methodSignName}:#{vo.toUpdateLockKey()}",message = "物流单生成中...")
    public R<Integer> addOrUpdateV1(@Valid @RequestBody WuLiuAddOrUpdateReqVO vo) {
        return wuLiuService.addOrUpdate(SysUtils.getUser(), vo);
    }

    /**
     * 新增物流单
     *
     * @param vo WuLiuAddOrUpdateReqVO
     * @return R<Integer>
     * @date 2021-09-29
     * <AUTHOR> [<EMAIL>]
     */
    @ApiOperation(value = "新增物流单")
    @PostMapping("/add/v2")
    @RepeatSubmitCheck
    public R<WuLiuAddResV2VO> add(@Valid @RequestBody WuLiuAddReqV2VO vo) {
        return wuLiuBusService.add(vo);
    }

    /**
     * /addOrder/wuliu
     * 新增/更新物流单
     *
     * @param vo WuLiuAddOrUpdateReqVO
     * @return R<Integer>
     * @date 2021-09-29
     * <AUTHOR> [<EMAIL>]
     */
    @ApiOperation(value = "更新物流单")
    @PostMapping("/update/v2")
    public R<String> updateV2(@Valid @RequestBody WuLiuUpdateReqV2 vo) {
        return wuLiuBusService.updateWuliuV2(SysUtils.getUser(), vo);
    }

    /**
     * /addOrder/wuliu
     * 新增/更新物流单快递信息
     *
     * @param vo WuLiuAddOrUpdateReqVO
     * @return R<Integer>
     * @date 2021-09-29
     * <AUTHOR> [<EMAIL>]
     */
    @ApiOperation(value = "更新物流单")
    @PostMapping("/update/v3")
    public R<Boolean> updateV3(@Valid @RequestBody WuLiuUpdateReqV3 vo) {
        return wuLiuBusService.updateWuliuV3(vo);
    }

    /**
     * 查询业务单据寄件，收件信息
     */
    @ApiOperation(value = "查询业务单据寄件，收件信息")
    @PostMapping("/getWuliuAddInfo/v1")
    public R<WuliuAddressInfoRes> getWuliuAddInfo(@Valid @RequestBody WuliuAddressInfoReq vo) {
        return R.success(wuLiuBusService.getWuliuAddressInfo(vo));
    }

    /**
     * 提前配送提示
     */
    @ApiOperation(value = "提前配送提示")
    @GetMapping("/getAdvanceDelivery/v1")
    public R<WuliuAdvanceDeliveryRes> getAdvanceDelivery(Integer wuliuId,Integer wutype,Integer danhaobind) {
        return R.success(wuLiuBusService.getWuliuAdvanceDelivery(wuliuId,wutype,danhaobind));
    }

    /**
     * 自动创建物流单，非手动创建物流单调用此接口
     *
     * @param vo WuLiuAddOrUpdateReqV2VO
     * @return R<Integer>
     * <AUTHOR> [<EMAIL>]
     * @date 2022-01-12
     */
    @PostMapping("/save-wuliu/v1")
    public R<Integer> saveWuliuV1(@Valid @RequestBody WuLiuAddOrUpdateReqV2VO vo) {
        return wuLiuService.saveWuliuV1(vo, vo.getSessionAreaId(), vo.getUsername());
    }

    /**
     * 获取单个物流单
     *
     * @param id 物流单 ID
     * @return R<WuLiuInfoReqVO>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-19
     */
    @ApiOperation(value = "获取单个物流单")
    @GetMapping("/get/v1")
    public R<WuLiuInfoReqVO> getOneV1(@RequestParam(required = false) Integer id, @RequestParam(required = false) Integer subId, @RequestParam(required = false) String actionName) {
        OaUserBO currentUser = component.getCurrentStaffId();
        WuLiuInfoReqVO vo = new WuLiuInfoReqVO();
        vo.setSessionAreaId(currentUser.getAreaId());
        vo.setWuliuid(id);
        if(subId != null) {
            vo.setSubId(subId);
        }
        vo.setActionName(actionName);
        return R.success(wuLiuBusService.getWuLiuInfo(vo, currentUser));
    }

    /**
     * 获取单个物流单
     *
     * @param req 物流单 ID
     * @return R<WuLiuInfoReqVO>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-19
     */
    @ApiOperation(value = "获取单个物流单")
    @GetMapping("/get/v2")
    public R<WuLiuInfoReqVO> getOneV2(WuLiuInfoReqVO req) {
        OaUserBO currentUser = component.getCurrentStaffId();
        req.setSessionAreaId(currentUser.getAreaId());
        return R.success(wuLiuBusService.getWuLiuInfoV2(req, currentUser));
    }

    /**
     * 终止派送物流单
     *
     * @param req 物流单 ID
     * @date 2021-11-19
     */
    @ApiOperation(value = "终止派送")
    @PostMapping("/terminateWuliu/v1")
    public R<String> terminateWuliu(@Valid @RequestBody TerminateWuliuReq req) {
        boolean flag = wuLiuBusService.terminateWuliu(req);
        if (flag) {
            return R.success("操作成功");
        }
        return R.error("操作失败");
    }

    /**
     * 作废操作/取货/开始送货/确认送达/签收/送达
     *
     * @param vo WuliucaozuoVO
     * @return R<String>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-19
     */
    @ApiOperation(value = "作废操作/取货/开始送货/确认送达/签收/送达")
    @PostMapping("/operate/v1")
    public R<String> operateV1(@RequestBody WuliucaozuoVO vo) {
        return wuLiuService.operateV1(SysUtils.getUser(), vo);
    }

    /**
     * 完成物料制作申请订单
     * @param vo
     * @return
     */
    @PostMapping("/completeMaterialApply/v1")
    public R<Boolean> completeMaterialApply(@RequestBody WuliucaozuoVO vo) {
        return R.success(wuLiuBusService.completeMaterialApply(vo.getWuliuid(), SysUtils.getUser().getUserName(), 0));
    }

    /**
     * 批量生成中通快递单号(子母件)
     *
     * @param vo WuLiuGenerateMoreWuLiuNoReqVO
     * @return R<String>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-13
     */
    @ApiOperation(value = "批量生成中通快递单号(子母件)")
    @PostMapping("/generate-more-wuliu-no/v1")
    public R<String> generateMoreWuLiuNoV1(@Valid @RequestBody WuLiuGenerateMoreWuLiuNoReqVO vo) {
        return wuLiuService.generateMoreWuLiuNoV1(SysUtils.getUser(), vo);
    }

    /**
     * 获取物流单列表
     *
     * @param wuLiuNoteReq WuLiuNoteReq
     * @return R<Page < WuLiuNoteRes>>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-19
     */
    @ApiOperation(value = "获取物流单列表")
    @PostMapping("/list/v1")
    public R<Page<WuLiuNoteRes>> listAllV1(@Valid @RequestBody WuLiuNoteReq wuLiuNoteReq) {
        Page<WuLiuNoteRes> wuLiuNoteResListByReceiveAreaId;
        wuLiuNoteResListByReceiveAreaId = wuLiuNoteService.pageList(wuLiuNoteReq);
        return R.success(wuLiuNoteResListByReceiveAreaId);
    }

    @ApiOperation(value = "获取物流单列表")
    @PostMapping("/list/v2")
    public R<Page<WuLiuNoteRes>> listV2(@Valid @RequestBody WuLiuNoteReq wuLiuNoteReq) {
        wuLiuNoteReq.setExtend(true);
        return R.success(wuLiuNoteService.pageListV2(wuLiuNoteReq));
    }

    /**
     * 批量导出物流单
     *
     * @param wuLiuNoteReq WuLiuNoteReq
     * @param response     HttpServletResponse
     * <AUTHOR> @date 2021-11-19
     */
    @ApiOperation(value = "批量导出物流单")
    @PostMapping("/export/v1")
    public void exportAllV1(@Valid @RequestBody WuLiuNoteReq wuLiuNoteReq, HttpServletResponse response) {
        wuLiuNoteService.exportList(wuLiuNoteReq, response);
    }

    /**
     * 作废物流单
     *
     * @param id      物流单 ID
     * @param request HttpServletRequest
     * @return R<Boolean>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-01
     */
    @ApiOperation(value = "作废物流单")
    @GetMapping("/invalid/v1")
    public R<Boolean> invalidV1(@Valid @NotNull(message = "物流单 ID 不能为空") Integer id, HttpServletRequest request) {
        return wuLiuService.invalidV1(id, request);
    }

    /**
     * 作废物流单V2
     *
     * @param req      物流单 ID
     * @return R<Boolean>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-01
     */
    @ApiOperation(value = "作废物流单")
    @PostMapping("/invalid/v2")
    public R<Boolean> invalidV2(@RequestBody WuliuInvalidReqV2 req) {
        return wuLiuBusService.invalidV2(req);
    }

    /**
     * 物流单取消快递，暂时支持顺丰快递
     *
     * @param id 物流单号
     * @return R<Boolean>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-02
     */
    @ApiOperation(value = "批量导出物流单")
    @GetMapping("/cancel-express/v1")
    public R<Boolean> cancelExpress(@Valid @NotNull(message = "物流单号不能为空") Integer id) {
        return wuLiuService.cancelExpress(id);
    }

    @ApiOperation(value = "批量作废物流单")
    @PostMapping("/invalid/batch/v1")
    public R<String> invalidBatch(@Valid @NotNull(message = "物流单号不能为空") String idList) {
        return wuLiuService.invalidBatch(idList);
    }

    @ApiOperation(value = "批量生成中通快递单号(子母件)")
    @PostMapping("/create-express/batch/v1")
    public R<String> generateMoreWuLiuNo(@RequestBody WuLiuDTO wuLiu) {
        return wuLiuService.generateMoreWuLiuNo(wuLiu, null);
    }

    @ApiOperation(value = "通知打印")
    @PostMapping("/print/v1")
    public R<String> print(String printId, Integer type, String clientNo, Integer printCount) {
        return wuLiuService.doPrint(printId, type, clientNo, printCount);
    }

    /**
     * 自定义推送
     **/
    @PostMapping("/sendDiyMsg/v1")
    public R<Boolean> sendDiyMsg(@RequestBody SendDIYMsgDTO dto) {
        return wuLiuService.sendDIYMsg(dto.getIds(), dto.getMsg(), dto.getUserIds(), dto.getUserName());
    }

    /**
     * 消息推送、发货提醒
     **/
    @PostMapping("/sendMsg/v1")
    public R<Boolean> sendMsg(@RequestBody sendMsgDTO dto) {
        return wuLiuService.sendMsg(dto.getIds(), dto.getType(), dto.getUserName());
    }

    /**
     * 物流轨迹
     *
     * @param id 物流单号
     * @return R<Boolean>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-02
     */
    @ApiOperation(value = "物流轨迹")
    @GetMapping("/trace/v1")
    public R<String> traceV1(@Valid @NotNull(message = "物流单号不能为空") Integer id) {
        return wuLiuService.traceV1(id);
    }

    /**
     * 获取顺丰账号配置信息列表 v1
     *
     * @param authorization md5 日期鉴权值
     * @return R<List < ShunfengCustidConfigEntity>>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-28
     */
    @ApiOperation(value = "获取顺丰配置信息")
    @GetMapping("/shunfeng-custid-config/list/v1")
    public R<List<ShunfengCustidConfigEntity>> shunfengCustidConfigListAllV1(@Valid @NotBlank(message = "鉴权值不能为空") @RequestHeader String authorization) {
        return wuLiuService.shunfengCustidConfigListAllV1(authorization);
    }

    /**
     * 发货扫描日志
     **/
    @PostMapping("/saveScanLog/v1")
    public R<Boolean> saveScanLog(@RequestBody WuLiuLogReqVO req) {
        OaUserBO currentUser = component.getCurrentStaffId();
        return wuLiuService.saveScanLog(req,currentUser);
    }

    /**
     * 计算物流单距离
     **/
    @GetMapping("/handleDistributionCost/v1")
    public R<Boolean> handleDistributionCost(Integer days) {
        wuLiuBusService.handleDistributionCost(days);
        return R.success("操作成功");
    }

    /**
     * 支付商户订单号查询订单物流信息
     **/
    @GetMapping("/getOrderByPayId/v1")
    public R<WuliuOrderInfoRes> getOrderByPayId(@RequestParam(value = "payId") Integer payId) {
        return R.success(wuLiuBusService.getOrderByPayId(payId));
    }
    /**
     * 支付商户订单号查询订单物流信息
     **/
    @GetMapping("/getOrderByPayDes/v1")
    public R<WuliuOrderInfoRes> getOrderByPayDes(@RequestParam(value = "payDes") String payDes) {
        return R.success(wuLiuBusService.getOrderByPayDes(payDes));
    }

    /**
     * 定时处理超15天未完成的物流单
     * @param days
     * @return
     */
    @GetMapping("/handleTimeoutWuliu/v1")
    public R<Boolean> handleTimeoutWuliu(@RequestParam(value = "days", defaultValue = "15") Integer days) {
        return R.success(wuLiuBusService.handleTimeoutWuliu(days));
    }

    /**
     * 判断物流单收货地址是否与快递收货地址一致
     * @return
     */
    @PostMapping("/sameReceiveAddress/v1")
    public R<SameWuliuExpressAddressRes> sameReceiveAddress(@RequestBody SameWuliuExpressAddressReq req) {
        return R.success(wuLiuBusService.sameReceiveAddress(req));
    }

    /**
     * 查询业务单据调拨物流单信息
     *
     */
    @ApiOperation(value = "查询业务单据寄件，收件信息")
    @PostMapping("/getInnerWuliuInfo/v1")
    public R<List<InnerWuliuInfoRes>> getInnerWuliuInfo(@Valid @RequestBody InnerWuliuInfoReq vo) {
        return R.success(wuLiuBusService.getInnerWuliuInfo(vo));
    }

    /**
     * 发送物流单聊天卡片
     */
    @ApiOperation(value = "发送物流单聊天卡片")
    @PostMapping("/sendWuliuOrder/v1")
    public R<String> sendWuliuOrder(@Valid @RequestBody SendWuliuOrderReq vo) {
        boolean flag = wuLiuBusService.sendWuliuOrder(vo);
        if (flag) {
            return R.success("发送成功", "成功");
        } else {
            return R.error("发送失败");
        }
    }

    /**
     * 查询物流单发货，收货门店营业时间
     */
    @ApiOperation(value = "查询物流单发货，收货门店营业时间")
    @PostMapping("/getWuliuAreaOpeningTime/v1")
    public R<WuliuAreaOpeningTimeRes> getWuliuAreaOpeningTime(@Valid @RequestBody WuliuAreaOpeningTimeReq vo) {
        return R.success(wuLiuBusService.getWuliuAreaOpeningTime(vo));
    }

    /**
     * 校验用户的位置与门店的距离
     */
    @ApiOperation(value = "校验用户的位置与门店的距离")
    @PostMapping("/getDistanceAreaAndPosition/v1")
    public R<DistanceAreaAndPositionRes> getDistanceAreaAndPosition(@Valid @RequestBody DistanceAreaAndPositionReq req) {
        return R.success(wuLiuBusService.getDistanceAreaAndPosition(req));
    }

    /**
     * 查询物流单信息
     */
    @ApiOperation(value = "查询物流单信息")
    @PostMapping("/getWuLiuInfo/v1")
    public R<GetWuliuInfoRes> getWuLiuInfo(@Valid @RequestBody GetWuliuInfoReq req) {
        return R.success(wuLiuBusService.getWuLiuInfo(req));
    }

    /**
     * 物流单
     * 代签，揽收
     */
    @ApiOperation(value = "物流单代签，揽收扫描")
    @PostMapping("/signatureAndPickUpWuliu/v1")
    public R<String> signatureAndPickUpWuliu(@Valid @RequestBody WuliuSignatureAndPickUpReq req) {
        return wuLiuBusService.signatureAndPickUpWuliu(req);
    }

    /**
     * 物流单代签，定时发送通知
     */
    @ApiOperation(value = "物流单代签，定时发送通知")
    @GetMapping("/signatureWuliuSendOaMsg/v1")
    public R<Boolean> signatureWuliuSendOaMsg() {
        return R.success(wuLiuBusService.signatureWuliuSendOaMsg());
    }

    /**
     * 物流单未投妥
     */
    @ApiOperation(value = "物流单未投妥")
    @PostMapping("/wuliuNotDelivered/v1")
    public R<String> wuliuNotDelivered(@RequestBody WuliuNotDeliveredReq req) {
        return wuLiuBusService.wuliuNotDelivered(req);
    }
}
