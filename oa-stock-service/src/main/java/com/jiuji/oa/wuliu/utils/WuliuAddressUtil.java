package com.jiuji.oa.wuliu.utils;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.ch999.common.util.atlas.AtlasUtil;
import com.ch999.common.util.atlas.CoordinateUtil;
import com.ch999.common.util.vo.atlas.Coordinate;
import com.ch999.common.util.vo.atlas.TencentMapResultVO;
import com.jiuji.oa.nc.user.po.Areainfo;
import com.jiuji.oa.orginfo.areainfo.vo.res.AreaListRes;
import com.jiuji.oa.stock.common.util.JacksonJsonUtils;
import com.jiuji.oa.stock.logistics.order.service.WuliuService;
import com.jiuji.oa.stock.logistics.order.vo.ExpressPushVO;
import com.jiuji.oa.wuliu.dto.SubPositionDTO;
import com.jiuji.oa.wuliu.dto.req.SubPositionReq;
import com.jiuji.oa.wuliu.entity.AddInfoPsEntity;
import com.jiuji.oa.wuliu.enums.WuLiuTypeEnum;
import com.jiuji.oa.wuliu.service.IAddInfoPsService;
import com.jiuji.oa.wuliu.service.IWuLiuRecoverMarketInfoService;
import com.jiuji.oa.wuliu.service.IWuLiuService;
import com.jiuji.oa.wuliu.service.IWuLiuSubService;
import com.jiuji.oa.wuliu.vo.CityIdListDTO;
import com.jiuji.tc.utils.common.build.LambdaBuild;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 地址处理
 * <AUTHOR>
 * @date 2022/5/25 11:19
 */
@Slf4j
public class WuliuAddressUtil {
    /**
     * 九机综合店
     */
    public static final int JIUJI_ATTRIBUTE = 1101;

    /**
     * 地址提取省市区正则表达式
     */
    private static final Pattern ADDRESS_PATTERN = Pattern.compile(
            "(?<province>[^省]+省|.+自治区|上海市|上海|北京市|北京|天津市|天津|重庆市|重庆)?" +
                    "\\s*(?<city>[^市]+市|.+自治州|市辖区)?" +
                    "\\s*(?<district>[^市]+市|[^县]+县|[^区]+区)?",
            Pattern.CASE_INSENSITIVE // 添加不区分大小写的标志
    );

    private WuliuAddressUtil() {
    }

    /**
     * 获取完整地址信息
     * @param addressStr 地址信息
     * @param cityInfo 省市区三级地址信息
     * @return
     */
    public static String getAddress(String addressStr, CityIdListDTO cityInfo) {
        if (StringUtils.isEmpty(addressStr) || Objects.isNull(cityInfo)) {
            return addressStr;
        }
        String pname = Optional.ofNullable(cityInfo.getPname()).orElse("");
        String dname = Optional.ofNullable(cityInfo.getDname()).orElse("");
        String zname = Optional.ofNullable(cityInfo.getZname()).orElse("");
        if (addressStr.contains(pname + dname + zname)) {
            return addressStr;
        }
        StringBuilder sb = new StringBuilder();
        if (!addressStr.startsWith(pname)) {
            if (addressStr.startsWith(zname)) {
                sb.append(pname);
            } else {
                if (addressStr.startsWith(dname)) {
                    sb.append(pname).append(zname);
                } else {
                    sb.append(pname).append(zname).append(dname);

                }
            }
        }
        sb.append(addressStr);
        return sb.toString();
    }

    /**
     * 获取位置信息
     * @param expressPush
     * @return
     */
    public static Coordinate getLocationByAddress(ExpressPushVO expressPush) {
        Coordinate coordinate = null;
        if (StringUtils.isNotBlank(expressPush.getLocation())) {
            coordinate = new Coordinate(expressPush.getLocation());
        }
        if (Objects.isNull(coordinate)) {
            if (StringUtils.isBlank(expressPush.getAcceptAddress())) {
                log.warn("快递单轨迹信息未获取到位置信息expressPush={}", JacksonJsonUtils.toJson(expressPush));
                return coordinate;
            }
            TencentMapResultVO mapInfoUsingTencent = AtlasUtil.getMapInfoUsingTencent(expressPush.getAcceptAddress());
            if (Objects.nonNull(mapInfoUsingTencent)
                    && Objects.nonNull(mapInfoUsingTencent.getResult())
                    && Objects.nonNull(mapInfoUsingTencent.getResult().getLocation())) {
                TencentMapResultVO.Result.Location location = mapInfoUsingTencent.getResult().getLocation();
                coordinate = new Coordinate();
                coordinate.setLongitude(location.getLng());
                coordinate.setLatitude(location.getLat());
            }
        }
        if (Objects.nonNull(coordinate)) {
            coordinate = CoordinateUtil.gcj2wgs(coordinate);
        }
        log.warn("查询快递位置信息expressPush={}，coordinate={}", JacksonJsonUtils.toJson(expressPush), JacksonJsonUtils.toJson(coordinate));
        return coordinate;
    }

    /**
     * 地址信息
     * @param cityInfo 省市区三级地址信息
     * @return
     */
    public static String getCityAdress(CityIdListDTO cityInfo) {
        if (Objects.isNull(cityInfo)) {
            return "";
        }
        return cityInfo.getPname() + cityInfo.getZname() + cityInfo.getDname();
    }

    /**
     * 校验省市区信息
     * @return
     */
    public static String checkAddress(CityIdListDTO cityInfo) {
        if (Objects.isNull(cityInfo)) {
            return "请检查收件地址,省市区信息是否正确";
        }
        if (StringUtils.isEmpty(cityInfo.getPname())) {
            return "请检查收件地址,省信息是否正确";
        }
        if (StringUtils.isEmpty(cityInfo.getZname())) {
            return "请检查收件地址,市信息是否正确";
        }
        if (StringUtils.isEmpty(cityInfo.getDname())) {
            return "请检查收件地址,区信息是否正确";
        }
        return "";
    }

    /**
     * 获取发货收货门店或地址门牌号
     * @return
     */
    public static String getUsernote(Areainfo areainfo) {
        if (Objects.isNull(areainfo)) {
            return "";
        }
        if (Objects.equals(JIUJI_ATTRIBUTE,areainfo.getAreaAttribute())) {
            return "九机" + areainfo.getAreaName();
        }
        return areainfo.getAreaName();
    }

    /**
     * 查询订单收货位置信息
     * @param req
     * @return
     */
    public static String getSubPosition(SubPositionReq req) {
        SubPositionDTO subPositionBySub = null;
        if (req.getSubId() != null && req.getSubId() != 0) {
            if (WuLiuTypeEnum.ORDER.getCode().equals(req.getWuType()) || WuLiuTypeEnum.ORDER_EXPRESS.getCode().equals(req.getWuType())) {
                subPositionBySub = Optional.ofNullable(SpringUtil.getBean(IWuLiuSubService.class).getSubAddressPositionBySubId(req)).orElse(new SubPositionDTO());
                if (outOfChina(subPositionBySub.getAddressPosition())) {
                    subPositionBySub = SpringUtil.getBean(IWuLiuSubService.class).getSubPositionBySub(req);
                }
            } else if (WuLiuTypeEnum.FOURTEEN_DAY.getCode().equals(req.getWuType())) {
                subPositionBySub = Optional.ofNullable(SpringUtil.getBean(IWuLiuRecoverMarketInfoService.class).getSubAddressPositionBySubId(req)).orElse(new SubPositionDTO());
                if (outOfChina(subPositionBySub.getAddressPosition())) {
                    subPositionBySub = SpringUtil.getBean(IWuLiuRecoverMarketInfoService.class).getSubPositionBySub(req);
                }
            } else if (WuLiuTypeEnum.AFTER_SERVICE.getCode().equals(req.getWuType())) {
                subPositionBySub = Optional.ofNullable(SpringUtil.getBean(IAddInfoPsService.class).getByShouHouId(req.getSubId(), null))
                        .map(v -> LambdaBuild.create(SubPositionDTO.class).set(SubPositionDTO::setAddressPosition, v.getPosition()).build()).orElse(null);
            }
        }
        if (Objects.nonNull(subPositionBySub)) {
            return subPositionBySub.getAddressPosition();
        }
        return null;
    }

    /**
     * 校验经纬度是否国外
     * @param position
     * @return
     */
    public static boolean outOfChina(String position) {
        //校验经纬度是否正常
        Coordinate coordinate = new Coordinate(position);
        return CoordinateUtil.outOfChina(coordinate.getLongitude(), coordinate.getLatitude());
    }

    /**
     * 获取地址中省市区
     * @param address
     * @return
     */
    public static Integer getCityIdByAddress (String address) {
        Matcher matcher = ADDRESS_PATTERN.matcher(address);
        Integer cityId = 0;
        if (matcher.find()) {
            String province = Optional.ofNullable(matcher.group("province")).orElse("").replace("省", "");
            String city = Optional.ofNullable(matcher.group("city")).orElse("");
            String district = Optional.ofNullable(matcher.group("district")).orElse("");
            List<AreaListRes> areaList = SpringUtil.getBean(IWuLiuService.class).getAreaList();
            if (StringUtils.isBlank(province) || StringUtils.isBlank(district)) {
                return 0;
            }
            //省级信息
            Integer provinceCode = areaList.stream().filter(v -> Objects.equals(1, v.getLevel()))
                    .filter(v -> v.getName().contains(province) || v.getName1().contains(province))
                    .findFirst().map(AreaListRes::getCode).orElse(null);
            if (Objects.isNull(provinceCode)) {
                return 0;
            }
            Integer cityCode = areaList.stream().filter(v -> Objects.equals(provinceCode, v.getParentCode()))
                    .filter(v -> StringUtils.isNotBlank(city) && (v.getName().contains(city) || v.getName1().contains(city)))
                    .findFirst().map(AreaListRes::getCode).orElse(null);
            if (Objects.isNull(cityCode)) {
                cityId = areaList.stream().filter(v -> StrUtil.startWith(Convert.toStr(v.getCode()), Convert.toStr(provinceCode)) && Objects.equals(3, v.getLevel()))
                        .filter(v -> v.getName().contains(district) || v.getName1().contains(district))
                        .findFirst().map(AreaListRes::getCode).orElse(0);
            } else {
                cityId = areaList.stream().filter(v -> Objects.equals(cityCode, v.getParentCode()))
                        .filter(v -> v.getName().contains(district) || v.getName1().contains(district))
                        .findFirst().map(AreaListRes::getCode).orElse(0);
            }
        }
        return cityId;
    }
}
