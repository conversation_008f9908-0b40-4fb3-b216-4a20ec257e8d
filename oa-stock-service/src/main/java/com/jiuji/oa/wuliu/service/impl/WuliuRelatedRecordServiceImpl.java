package com.jiuji.oa.wuliu.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.wuliu.entity.WuliuRelatedRecord;
import com.jiuji.oa.wuliu.service.WuliuRelatedRecordService;
import com.jiuji.oa.wuliu.mapper.WuliuRelatedRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *
 */
@Service
@Slf4j
@DS("oanewWrite")
public class WuliuRelatedRecordServiceImpl extends ServiceImpl<WuliuRelatedRecordMapper, WuliuRelatedRecord>
implements WuliuRelatedRecordService{

    /**
     * 根据物流单号查询关联记录
     *
     * @param wuliuId
     * @return
     */
    @Override
    public WuliuRelatedRecord getWuLiuRelatedRecordByWuliuId(Integer wuliuId) {
        return this.baseMapper.getWuLiuRelatedRecordByWuliuId(wuliuId);
    }
}




