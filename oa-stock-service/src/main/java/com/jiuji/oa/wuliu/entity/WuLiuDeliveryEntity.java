package com.jiuji.oa.wuliu.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 物流单配送方式
 *
 * <AUTHOR>
 * @date 2021/10/18
 */
@Data
@TableName("delivery")
public class WuLiuDeliveryEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Integer id;
    /**
     * 名字
     */
    @TableField("name_")
    private String name;
    /**
     * 费用描述
     */
    @TableField("feeDsc")
    private String feeDsc;
    /**
     * 日期描述
     */
    @TableField("dateDsc")
    private String dateDsc;
    /**
     * 描述
     */
    private String dsc;
    /**
     * 排名
     */
    @TableField("rank_")
    private Integer rank;
    /**
     * 费用
     */
    private BigDecimal fee;

}
