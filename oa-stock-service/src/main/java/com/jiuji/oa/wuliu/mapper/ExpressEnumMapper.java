package com.jiuji.oa.wuliu.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.wuliu.entity.ExpressEnumEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <p>
 * 物流单快递类型 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-30
 */
public interface ExpressEnumMapper extends BaseMapper<ExpressEnumEntity> {

    /**
     * 查询ExpressEnum包含isdel=1
     * @return
     */
    @Select("SELECT id,expressName,expressCode,expressType,isdel del FROM dbo.expressenum with(nolock) order by isdel, showRank")
    List<ExpressEnumEntity> selectAllExpressEnum();

    @Update("update expressenum set status = #{isdel} where id = #{id}")
    void updateExpressEnumById(@Param("id") Long id, @Param("isdel") boolean isdel);
}
