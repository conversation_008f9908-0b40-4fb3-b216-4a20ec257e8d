package com.jiuji.oa.wuliu.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.jiuji.tc.common.vo.R;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Objects;

/**
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-16
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class Ch99ResultDataDTO<T> extends R<T> {

    private static final long serialVersionUID = 1L;

    private static final int FIVE_THOUSAND = 5000;

    @JsonProperty("stats")
    @JSONField(name = "stats")
    private Integer stats;

    @Override
    public int getCode() {
        return Objects.equals(stats, 1) ? 0 : FIVE_THOUSAND;
    }

}
