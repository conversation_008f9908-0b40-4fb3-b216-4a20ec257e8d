package com.jiuji.oa.wuliu.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * uu 订单状态
 * <AUTHOR>
 * @date 2022/3/10 14:27
 */
@Getter
@AllArgsConstructor
public enum UuOrderStatusEnum {
    /**
     * 状态 1下单成功 3跑男抢单 4已到达 5已取件 6到达目的地 10收件人已收货 -1订单取消
     **/
    CREATED(1, "下单成功"),
    ORDER_RECEIVED(3, "跑男抢单"),
    ARRIVED(4, "已到达"),
    PICKED_UP(5, "已取件"),
    REACH(6, "到达目的地"),
    DELIVERED(10, "收件人已收货"),
    CANCELED(-1, "订单取消"),

    OTHER(0, "")
    ;

    private final Integer code;
    private final String message;

    public static String getMessageByCode(Integer c) {
        return Arrays.stream(values()).filter(v -> v.getCode().equals(c)).findFirst().orElse(UuOrderStatusEnum.OTHER).getMessage();
    }
}
