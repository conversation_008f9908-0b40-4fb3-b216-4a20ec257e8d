package com.jiuji.oa.wuliu.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * QueryReturnResultVO
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-12-13
 */
@Setter
@Getter
@Accessors(chain = true)
public class QueryReturnResultVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer code;

    /**
     * mt_peisong_id
     * 美团配送内部订单id
     */
    private String mtPeisongId;

    /**
     * delivery_id
     * 配送活动标识
     */
    private String deliveryId;

    /**
     * 外部订单id
     */
    private Integer status;

    /**
     * statusName
     */
    private String statusName;

    /**
     * courier_name
     * 配送员姓名（订单已被骑手接单后会返回骑手信息）
     */
    private String courierName;

    /**
     * courier_phone
     * 配送员电话（订单已被骑手接单后会返回骑手信息）
     */
    private String courierPhone;

    /**
     * cancel_reason
     * 取消原因
     */
    private String cancelReason;

    /**
     * operate_time
     * 订单状态变更时间，格式为unix-timestamp
     */
    private Integer operateTime2;

    /**
     * operateTime
     * 操作时间
     */
    private String operateTime;

    /**
     * 错误信息提示
     */
    private String message;

}
