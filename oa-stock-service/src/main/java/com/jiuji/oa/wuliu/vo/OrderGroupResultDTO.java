package com.jiuji.oa.wuliu.vo;


import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * zhongtongApiServices.orderGroupResult
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-16
 */
@Data
@Accessors(chain = true)
public class OrderGroupResultDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("result")
    @JSONField(name = "result")
    private Boolean result;

    @JsonProperty("message")
    @JSONField(name = "message")
    private String message;

    @JsonProperty("data")
    @JSONField(name = "data")
    private OrderGroupResultItemDTO data;

}
