package com.jiuji.oa.wuliu.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.cloud.stock.vo.request.SelectAgencyReq;
import com.jiuji.oa.stock.nationalSupplement.req.DeclarationDataSaasReq;
import com.jiuji.oa.stock.nationalSupplement.req.DeclarationNotApprovedReq;
import com.jiuji.oa.stock.nationalSupplement.req.NationalAttachmentReq;
import com.jiuji.oa.stock.nationalSupplement.req.SubReturnDateReq;
import com.jiuji.oa.stock.nationalSupplement.res.*;
import com.jiuji.oa.wuliu.entity.WuLiuSubFlagRecordEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 订单团单状态记录表,责任小组：销售 Mapper 接口
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-18
 */
@Mapper
public interface WuLiuSubFlagRecordMapper extends BaseMapper<WuLiuSubFlagRecordEntity> {

    /**
     * 查询V区人员
     * @param userIdList
     * @return
     */
    List<Integer> selectDepartVUser(@Param(value = "userIdList")List<Integer> userIdList);

    /**
     * 查询V区门店
     * @param userIdList
     * @return
     */
    List<Integer> selectDepartVAreaId();


    /**
     * 查询申报不通过
     * @return
     */
    List<DeclarationNotApprovedRes> selectDeclarationNotApproved(@Param(value = "req") DeclarationNotApprovedReq req);
    Integer selectAgencyCount(@Param(value = "req")SelectAgencyReq selectAgencyReq);
    List<Integer> selectDeclarationDataSaas(@Param(value = "req")DeclarationDataSaasReq dataSaasReq);

    /**
     * 分页查询
     * @param page
     * @param req
     * @return
     */
    Page<NationalSupplementAttachmentRes> pageNationalSupplementState(Page<NationalSupplementAttachmentRes> page, @Param(value = "req") NationalAttachmentReq req);



    List <NationalSupplementAttachmentRes> selectNationalSupplementState(@Param(value = "req") NationalAttachmentReq req);


    NationalAttachmentCount selectNationalSupplementCount(@Param(value = "req") NationalAttachmentReq req);

    /**
     * 查询国补附件总数
     */
    Integer selectNationalSupplementStateCount(@Param(value = "req") NationalAttachmentReq req);


    /**
     * 国补附件查询
     * @param subIdList
     * @return
     */
    List<NationalAttachmentInfo> selectNationalAttachmentInfo(@Param(value = "subIdList") List<Integer> subIdList);

    /**
     * 部门查询
     * @return
     */
    List<DepartNameInfo> selectDepartNameInfo(@Param(value = "areaIdList") List<Integer> areaIdList);

    /**
     * 国补类型
     * @param subIds
     * @return
     */
    List<NationalSupplementKindRes> getNationalSupplementKindList(@Param(value = "subIds") List<String> subIds);
    
    /**
     * 根据退货日期范围查询订单
     * @param req 包含开始时间和结束时间的请求对象
     * @return 订单号和退货日期列表
     */
    List<SubReturnDateRes> querySubsByReturnDate(@Param(value = "req") SubReturnDateReq req);

    Integer getTenantId(@Param(value = "req")DeclarationDataSaasReq dataSaasReq);

}
