package com.jiuji.oa.wuliu.enums;

import com.jiuji.oa.stock.common.util.Builder;
import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import com.jiuji.tc.utils.enums.EnumVO;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 搜索类枚举
 *
 * <AUTHOR>
 * @date 2021/10/09
 */
@AllArgsConstructor
@Getter
public enum SearchKindEnum implements CodeMessageEnumInterface {
    /**
     * 发件人手机号
     */
    SEND_MOBILE(1,"smobile","发件人手机号",true,11),
    /**
     * 收件人手机号
     */
    RECEIVE_MOBILE(2,"rmobile","收件人手机号",true, 11),
    /**
     * 物流单号
     */
    WU_LIU_NUM(3,"id","物流单号",true, 9),
    /**
     * 快递单号
     */
    TRACK_NUM(4,"nu","快递单号",false, null),
    /**
     * 订单号
     */
    TRACK_NUM_BIND(5,"danhaobind","订单号",true, 9),
    /**
     * 发货地区
     */
    SEND_AREA(6,"sareaid","发货地区",false, null),
    /**
     * 收货地区
     */
    RECEIVE_AREA(7,"rareaid","收货地区",false, null),
    /**
     * 取件人
     */
    RECIPIENT(8,"shoujianren","取件人",false, null),
    /**
     * 派件人
     */
    COURIER(9,"paijianren","派件人",false, null),
    /**
     * 寄件人
     */
    SEND_NAME(10,"sname","寄件人",false, null),
    /**
     * 收件人
     */
    RECEIVE_NAME(11,"rname","收件人",false, null),
    /**
     * 备注
     */
    COMMENT(12,"comment","备注",false, null),
    /**
     * 进程
     */
    PROCESS(13,"","进程",false, null),
    /**
     * 箱号
     */
    BOX_NUMBER(14,"","箱号",false, null);

    /**
     * 代码
     */
    private Integer code;

    /**
     * 数据库中字段名
     */
    private String column;
    /**
     * 消息
     */
    private String message;

    /**
     * 是否只接受数字格式
     */
    private Boolean isNum;

    /**
     * 当前参数最大长度
     */
    private Integer maxLength;

    /**
     * 查询枚举
     * @return
     */
    public static List<EnumVO> getEnumVoList() {
        return Arrays.stream(values())
                .filter(v -> !SearchKindEnum.SEND_AREA.equals(v) && !SearchKindEnum.RECEIVE_AREA.equals(v))
                .map(v -> Builder.of(EnumVO::new).with(EnumVO::setLabel, v.getMessage()).with(EnumVO::setValue, v.getCode()).build())
                .collect(Collectors.toList());
    }
}
