package com.jiuji.oa.wuliu.service.impl;

import com.jiuji.oa.nc.common.exception.CustomizeException;
import com.jiuji.oa.stock.logisticscenter.utils.JsonParseUtil;
import com.jiuji.oa.wuliu.entity.ShansongAppConfigs;
import com.jiuji.oa.wuliu.service.IShansongAppConfigsService;
import com.jiuji.oa.wuliu.service.IShansongService;
import com.jiuji.oa.wuliu.utils.ShanSongUtil;
import com.jiuji.oa.wuliu.vo.ShansongOrderCalculateModelVO;
import com.jiuji.oa.wuliu.vo.ShansongOrderCalculateVO;
import com.jiuji.oa.wuliu.vo.ShansongReceiverVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 闪送service
 * <AUTHOR>
 * @date 2021/11/8 18:53
 */
@Service
@Slf4j
@AllArgsConstructor
public class ShansongServiceImpl implements IShansongService {
    private static final Integer SUCCESS_CODE = 200;
    private IShansongAppConfigsService shansongAppConfigsService;

    /**
     * C# ShansongApiService.OrderCalculate
     * 订单计费
     * @param data
     * @return
     */
    @Override
    public ShansongOrderCalculateModelVO orderCalculate(ShansongOrderCalculateVO data) {
        ShansongAppConfigs config = shansongAppConfigsService.getShansongAppConfigs();
        verifyConfig(config);
        verifyOrderInfo(data);

        String url = config.getServeraddress()+"/openapi/merchants/v5/orderCalculate";
        String resposeObj = ShanSongUtil.post(config, url, data);
        Integer status = JsonParseUtil.getStatus(resposeObj);
        String msg = JsonParseUtil.getMsg(resposeObj);
        if (!SUCCESS_CODE.equals(status)) {
            throw new CustomizeException(msg);
        }
        String resData = JsonParseUtil.getData(resposeObj);
        return JsonParseUtil.toBean(resData, ShansongOrderCalculateModelVO.class);
    }

    /**
     * C# ShansongApiService.PlaceOrder
     * 下单
     * @param issOrderNo
     * @return
     */
    @Override
    public ShansongOrderCalculateModelVO placeOrder(String issOrderNo) {
        ShansongAppConfigs config = shansongAppConfigsService.getShansongAppConfigs();
        verifyConfig(config);
        if (StringUtils.isEmpty(issOrderNo)) {
            throw new CustomizeException("订单号不能为空");
        }
        String url = config.getServeraddress()+"/openapi/merchants/v5/orderPlace";

        // 调用提交订单接口
        Map<String, String> data = new HashMap<>(2);
        data.put("issOrderNo", issOrderNo);
        String resposeObj = ShanSongUtil.post(config, url, data);

        Integer status = JsonParseUtil.getStatus(resposeObj);
        String msg = JsonParseUtil.getMsg(resposeObj);
        if (!SUCCESS_CODE.equals(status)) {
            throw new CustomizeException(msg);
        }

        String resData = JsonParseUtil.getData(resposeObj);
        return JsonParseUtil.toBean(resData, ShansongOrderCalculateModelVO.class);
    }

    /**
     * C# ShansongApiService.VerifyOrderInfo
     * 验证下单信息
     * @param data
     */
    private static void verifyOrderInfo(ShansongOrderCalculateVO data) {
        if (ObjectUtils.isEmpty(data)) {
            throw new CustomizeException("下单参数为空");
        }

        if (data.getSender() == null || StringUtils.isEmpty(data.getSender().getFromAddress())
                || StringUtils.isEmpty(data.getSender().getFromAddressDetail())
                || StringUtils.isEmpty(data.getSender().getFromMobile())
                || StringUtils.isEmpty(data.getSender().getFromSenderName())) {
            throw new CustomizeException("寄件人信息不能为空或不全");
        }

        if (CollectionUtils.isEmpty(data.getReceiverList())) {
            for (ShansongReceiverVO receiver : data.getReceiverList()) {
                if (StringUtils.isEmpty(receiver.getToAddress())
                        || StringUtils.isEmpty(receiver.getToAddressDetail())
                        || StringUtils.isEmpty(receiver.getToReceiverName())
                        || StringUtils.isEmpty(receiver.getToMobile())) {
                    throw new CustomizeException("收件人信息不能为空或不全");
                }
            }
        }
    }

    /**
     * C# ShansongApiService.VerifyConfig
     * 验证配置信息
     */
    public static void verifyConfig(ShansongAppConfigs config) throws CustomizeException{
        if (config == null) {
            throw new CustomizeException("闪送配置信息为空");
        }

        if (StringUtils.isEmpty(config.getServeraddress())) {
            throw new CustomizeException("闪送服务器地址为空");
        }

        if (StringUtils.isEmpty(config.getShopid())) {
            throw new CustomizeException("闪送服务商户号为空");
        }

        if (StringUtils.isEmpty(config.getAppsecrty())) {
            throw new CustomizeException("闪送服务APP密钥为空");
        }
        if (StringUtils.isEmpty(config.getClientid())) {
            throw new CustomizeException("应用客户端ID密钥为空");
        }
    }
}
