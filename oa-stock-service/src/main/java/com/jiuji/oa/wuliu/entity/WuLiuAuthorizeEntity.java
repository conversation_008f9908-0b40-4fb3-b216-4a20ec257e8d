package com.jiuji.oa.wuliu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 实体类
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-19
 */
@Data
@Accessors(chain = true)
@TableName("authorize")
@ApiModel(value = "WuLiuAuthorizeEntity 实体类", description = " 实体类")
public class WuLiuAuthorizeEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("rank")
    private Integer rank;

    @TableField("YapingAreaId")
    private Integer yapingAreaId;

    @TableField("HQAreaId")
    private Integer hqAreaId;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("H1AreaId")
    private Integer h1AreaId;

    @TableField("marketChannel")
    private Integer marketChannel;

    @TableField("D1AreaId")
    private Integer d1AreaId;

    @TableField("InStockUserId")
    private Long inStockUserId;

    @TableField("dcAreaId")
    private Integer dcAreaId;

    @TableField("ztid")
    private Integer ztid;

    @TableField("vCodeChannel")
    private Integer vCodeChannel;

    @TableField("name")
    private String name;

}