/*
 *    Copyright © 2006 - 2020 九机网 All Rights Reserved
 *
 */

package com.jiuji.oa.wuliu.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.wuliu.entity.WuLiuShouHouEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 售后主表,责任小组：销售 Mapper
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-09-29
 */
@Mapper
public interface WuLiuShouHouMapper extends BaseMapper<WuLiuShouHouEntity> {

    /**
     * getShouHou
     *
     * @param subId
     * @param xianShi
     * @return WuLiuShouHouEntity
     * @date 2021-10-11
     * <AUTHOR> [<EMAIL>]
     */
    WuLiuShouHouEntity getShouHou(@Param("subId") Integer subId, @Param("xianShi") Integer xianShi);

    /**
     * getShouHou2
     *
     * @param subId
     * @param xianShi
     * @param areaId
     * @return WuLiuShouHouEntity
     * @date 2021-10-11
     * <AUTHOR> [<EMAIL>]
     */
    WuLiuShouHouEntity getShouHou2(@Param("subId") Integer subId, @Param("xianShi") Integer xianShi, @Param("areaId") Integer areaId);

    /**
     * getShouHou3
     *
     * @param subId
     * @param areaId
     * @return WuLiuShouHouEntity
     * @date 2021-10-11
     * <AUTHOR> [<EMAIL>]
     */
    WuLiuShouHouEntity getShouHou3(@Param("subId") Integer subId, @Param("areaId") Integer areaId);

}
