package com.jiuji.oa.wuliu.vo.req;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class WuliuNotDeliveredReq implements Serializable {
    /**
     * 物流单号
     */
    private Integer wuliuId;
    /**
     * 原因
     */
    private String reason;
    /**
     * 预计送达时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expectTime;

    private static final long serialVersionUID = 1L;
}
