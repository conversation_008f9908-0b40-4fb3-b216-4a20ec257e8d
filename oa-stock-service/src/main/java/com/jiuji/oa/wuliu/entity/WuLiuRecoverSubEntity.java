package com.jiuji.oa.wuliu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 回收子订单,责任小组：回收 实体类
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-15
 */
@Data
@Accessors(chain = true)
@TableName("recover_sub")
@ApiModel(value = "WuLiuRecoverSubEntity 实体类", description = "回收子订单,责任小组：回收 实体类")
public class WuLiuRecoverSubEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单id
     */
    @ApiModelProperty("订单id")
    @TableId(value = "sub_id", type = IdType.AUTO)
    private Integer subId;

    /**
     * 收货人
     */
    @ApiModelProperty("收货人")
    @TableField("sub_to")
    private String subTo;

    /**
     * 联系电话
     */
    @ApiModelProperty("联系电话")
    @TableField("sub_tel")
    private String subTel;

    /**
     * 支付方式
     */
    @ApiModelProperty("支付方式")
    @TableField("sub_pay")
    private Integer subPay;

    /**
     * 取货地址
     */
    @ApiModelProperty("取货地址")
    @TableField("sub_address")
    private String subAddress;

    /**
     * 交易方式
     */
    @ApiModelProperty("交易方式")
    @TableField("sub_delivery")
    private Integer subDelivery;

    /**
     * 银行名称
     */
    @ApiModelProperty("银行名称")
    @TableField("sub_bank")
    private String subBank;

    /**
     * 开户姓名
     */
    @ApiModelProperty("开户姓名")
    @TableField("bank_user")
    private String bankUser;

    /**
     * 银行卡号
     */
    @ApiModelProperty("银行卡号")
    @TableField("bank_num")
    private String bankNum;

    /**
     * 订单状态
     */
    @ApiModelProperty("订单状态")
    @TableField("sub_check")
    private Integer subCheck;

    /**
     * 地址的ID
     */
    @ApiModelProperty("地址的ID ")
    @TableField("cityid")
    private Integer cityid;

    /**
     * 用户ID
     */
    @ApiModelProperty("用户ID")
    @TableField("userid")
    private Long userid;

    /**
     * 地区【店面简码 km  】
     */
    @ApiModelProperty("地区【店面简码 km  】")
    @TableField("area")
    private String area;

    /**
     * 操作人 这里传【网络】
     */
    @ApiModelProperty("操作人 这里传【网络】")
    @TableField("inuser")
    private String inuser;

    /**
     * 添加时间
     */
    @ApiModelProperty("添加时间")
    @TableField("dtime")
    private LocalDateTime dtime;

    /**
     * 订单备注
     */
    @ApiModelProperty("订单备注")
    @TableField("comment")
    private String comment;

    /**
     * 入库时间
     */
    @ApiModelProperty("入库时间")
    @TableField("ruku_time")
    private LocalDateTime rukuTime;

    /**
     * 支付时间
     */
    @ApiModelProperty("支付时间")
    @TableField("pay_time")
    private LocalDateTime payTime;

    /**
     * 物流单打印次數
     */
    @ApiModelProperty("物流单打印次數")
    @TableField("printCount")
    private Integer printCount;

    /**
     * 凭证id
     */
    @ApiModelProperty("凭证id")
    @TableField("pzid")
    private Integer pzid;

    /**
     * 快递单号
     */
    @ApiModelProperty("快递单号")
    @TableField("kuaididan")
    private String kuaididan;

    /**
     * 正常订单单号
     */
    @ApiModelProperty("正常订单单号")
    @TableField("sub_ido")
    private Integer subIdo;

    /**
     * 订单金额多出部分 退款方式
     */
    @ApiModelProperty("订单金额多出部分 退款方式")
    @TableField("tui_way")
    private Integer tuiWay;

    /**
     * 上门服务具体时间
     */
    @ApiModelProperty("上门服务具体时间")
    @TableField("smtime")
    private String smtime;

    /**
     * 门店id
     */
    @ApiModelProperty("门店id")
    @TableField("areaid")
    private Integer areaid;

    /**
     * 是否网络订单
     */
    @ApiModelProperty("是否网络订单")
    @TableField("isnetsub")
    private Boolean isnetsub;

    /**
     * 实际回收人身份证号
     */
    @ApiModelProperty("实际回收人身份证号")
    @TableField("userCard")
    private String userCard;

    /**
     * 回收商员工ID
     */
    @ApiModelProperty("回收商员工ID")
    @TableField("reUserid")
    private Integer reUserid;

    /**
     * 回收商员工姓名
     */
    @ApiModelProperty("回收商员工姓名")
    @TableField("reUserName")
    private String reUserName;

    /**
     * 回收商门店ID
     */
    @ApiModelProperty("回收商门店ID")
    @TableField("reareaid")
    private Integer reareaid;

    /**
     * 实际回收人姓名
     */
    @ApiModelProperty("实际回收人姓名")
    @TableField("rehsUserName")
    private String rehsUserName;

    /**
     * 实际回收人电话
     */
    @ApiModelProperty("实际回收人电话")
    @TableField("rehsUserMobile")
    private String rehsUserMobile;

    /**
     * 质检员的id
     */
    @ApiModelProperty("质检员的id")
    @TableField("reZjyId")
    private Integer reZjyId;

    /**
     * 是否乐收单
     */
    @ApiModelProperty("是否乐收单")
    @TableField("isleshou")
    private Boolean isleshou;

    /**
     * 微信支付OPENID
     */
    @ApiModelProperty("微信支付OPENID")
    @TableField("payopenid")
    private String payopenid;

    /**
     * 移动端，外部订单号
     */
    @ApiModelProperty("移动端，外部订单号")
    @TableField("externalCode")
    private String externalCode;

    /**
     * 乐收备注信息
     */
    @ApiModelProperty("乐收备注信息")
    @TableField("leshouRemark")
    private String leshouRemark;

    /**
     * 快递公司
     */
    @ApiModelProperty("快递公司")
    @TableField("kuaidigongsi")
    private String kuaidigongsi;

    /**
     * 上门回收人
     */
    @ApiModelProperty("上门回收人")
    @TableField("recover_ch999name")
    private String recoverCh999name;

    /**
     * 回收日期
     */
    @ApiModelProperty("回收日期")
    @TableField("recover_datetime")
    private LocalDateTime recoverDatetime;

    /**
     * 上门回收业务是否确认
     */
    @ApiModelProperty("上门回收业务是否确认")
    @TableField("recover_confirm")
    private Boolean recoverConfirm;

    /**
     * 回购换其他订单类型
     */
    @ApiModelProperty("回购换其他订单类型")
    @TableField("recover_subType")
    private Integer recoverSubtype;

    /**
     * 回收业务确认人id
     */
    @ApiModelProperty("回收业务确认人id")
    @TableField("recover_confirmCh999Id")
    private Integer recoverConfirmch999id;

    /**
     * 小店订单标识
     */
    @ApiModelProperty("小店订单标识")
    @TableField("zitidianID")
    private Integer zitidianId;

    /**
     * 快递时间
     */
    @ApiModelProperty("快递时间")
    @TableField("kdTime")
    private LocalDateTime kdTime;

    /**
     * 回购换其他订单类型
     */
    @ApiModelProperty("回购换其他订单类型")
    @TableField("subIdoType")
    private Integer subIdoType;

    /**
     * 快递类型
     */
    @ApiModelProperty("快递类型")
    @TableField("kdtype")
    private Integer kdtype;

    /**
     * 支付人
     */
    @ApiModelProperty("支付人")
    @TableField("payUser")
    private String payUser;

    /**
     * 订单锁定
     */
    @ApiModelProperty("订单锁定")
    @TableField("islock")
    private Integer islock;

    /**
     * 以旧换新加价金额
     */
    @ApiModelProperty("以旧换新加价金额")
    @TableField("couponPrice")
    private BigDecimal couponPrice;
//
//    /**
//     * 回收邮寄地址
//     */
//    @ApiModelProperty("回收邮寄地址")
//    @TableField("jiujiHsAddress")
//    private String jiujiHsAddress;

    @TableField("jiujiHsName")
    private String jiujiHsName;

    @TableField("kinds")
    private Integer kinds;

    @TableField("payopenidKind")
    private Integer payopenidKind;


    @TableField("jiujiHsMobile")
    private String jiujiHsMobile;

    @TableField("orgin_id")
    private Integer orginId;

    @TableField("cancel_type")
    private Integer cancelType;

    @TableField("alipayUser")
    private String alipayUser;

}