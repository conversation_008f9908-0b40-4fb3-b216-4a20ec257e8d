/*
 *     Copyright © 2006 - 2020 九机网 All Rights Reserved
 *
 */

package com.jiuji.oa.wuliu.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.wuliu.entity.WuLiuShouHouEntity;

/**
 * 售后主表,责任小组：销售 Service
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-09
 */
public interface IWuLiuShouHouService extends IService<WuLiuShouHouEntity> {

    /**
     * 获取 shouhou
     *
     * @param subId
     * @param xianShi
     * @return WuLiuShouHouEntity
     * @date 2021-10-11
     * <AUTHOR> [<EMAIL>]
     */
    WuLiuShouHouEntity getShouHou(Integer subId, Integer xianShi);

    /**
     * 获取 shouhou2
     *
     * @param subId
     * @param xianShi
     * @param areaId
     * @return WuLiuShouHouEntity
     * @date 2021-10-11
     * <AUTHOR> [<EMAIL>]
     */
    WuLiuShouHouEntity getShouHou2(Integer subId, Integer xianShi, Integer areaId);

    /**
     * 获取 shouhou3
     *
     * @param subId
     * @param areaId
     * @return WuLiuShouHouEntity
     * @date 2021-10-11
     * <AUTHOR> [<EMAIL>]
     */
    WuLiuShouHouEntity getShouHou3(Integer subId, Integer areaId);

}
