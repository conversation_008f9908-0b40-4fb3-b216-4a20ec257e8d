package com.jiuji.oa.wuliu.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.wuliu.entity.PrintCountEntity;
import com.jiuji.oa.wuliu.vo.ShelveDTO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;

/**
 * <p>
 * 打印次数,责任小组：运营 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-11
 */
public interface PrintCountMapper extends BaseMapper<PrintCountEntity> {

    /**
     * 用订单号查询打印次数
     *
     * @param subId
     * @return
     */
    @Select("select top 1 printCount from PrintCount with(nolock) where printType=33 and subId=#{subId} order by id desc")
    Integer getPrintCountBySubId(@Param("subId") Integer subId);

    /**
     * 订单应付金额
     *
     * @param subId
     * @return
     */
    @Select("select top 1 yingfum from sub with(nolock) where sub_id=#{subId}")
    BigDecimal getOrderPayAble(@Param("subId") Integer subId);


    /**
     * 调拨单应付金额
     *
     * @param wuliuId
     * @return
     */
    @Select("select sum(memberprice) from("
            + "select isnull(p.memberprice,0) memberprice "
            + "from mkc_toarea mk with(nolock) "
            + "left join product_mkc pk with(nolock) on mk.mkc_id=pk.id "
            + "left join productinfo p with(nolock) on p.ppriceid=pk.ppriceid "
            + "where mk.wuliuid=#{wuliuId} "
            + "union all "
            + "select isnull(p.memberprice,0) memberprice from diaobo_sub d with(nolock) "
            + "left join diaobo_basket b with(nolock) on b.sub_id=d.id "
            + "left join productinfo p with(nolock) on p.ppriceid=b.ppriceid "
            + "where d.wuliuid=#{wuliuId} "
            + ") tmp")
    BigDecimal getTransferPayAble(@Param("wuliuId") Integer wuliuId);

    /**
     * 回收订单应付金额
     *
     * @param subId
     * @return
     */
    @Select("select top 1 yingfum from recover_marketInfo with(nolock) where sub_id=#{subId}")
    BigDecimal getReturnPayAble(@Param("subId") Integer subId);


    /**
     * 子订单的数量
     *
     * @param subId
     * @return
     */
    @Select("select count(1) from dbo.basket b with(nolock) where isnull(b.isdel,0)=0 and b.ismobile=1 and b.sub_id=#{subId}")
    Integer countBasket(@Param("subId") Integer subId);

    /**
     * 根据转售订单号 获取货位号及数量
     *
     * @param subId
     * @return
     */
    ShelveDTO getShelvesNumBySubId(@Param("subId") Integer subId);
}
