package com.jiuji.oa.wuliu.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.nc.common.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.nc.common.exception.CustomizeException;
import com.jiuji.oa.nc.common.util.ExcelUtils;
import com.jiuji.oa.nc.user.po.Areainfo;
import com.jiuji.oa.nc.user.service.IAreaInfoService;
import com.jiuji.oa.stock.common.easyexcel.NormalHeadStyleWriteHandler;
import com.jiuji.oa.stock.common.util.DecimalFormatUtils;
import com.jiuji.oa.wuliu.constant.WuLiuConstant;
import com.jiuji.oa.wuliu.dto.req.WuLiuNoteReq;
import com.jiuji.oa.wuliu.dto.res.WuLiuNoteRes;
import com.jiuji.oa.wuliu.dto.res.WuLiuNoteRes2;
import com.jiuji.oa.wuliu.entity.WuLiuLogEntity;
import com.jiuji.oa.wuliu.entity.WuLiuNote;
import com.jiuji.oa.wuliu.enums.SearchKindEnum;
import com.jiuji.oa.wuliu.enums.WuLiuStatusEnum;
import com.jiuji.oa.wuliu.enums.WuLiuTypeEnum;
import com.jiuji.oa.wuliu.mapper.WuLiuNoteMapper;
import com.jiuji.oa.wuliu.service.IWuLiuCategoryService;
import com.jiuji.oa.wuliu.service.IWuLiuLogService;
import com.jiuji.oa.wuliu.service.IWuLiuNoteService;
import com.jiuji.tc.utils.enums.EnumUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * WuLiuNoteServiceImpl
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-12-05
 */
@Slf4j
@DS("ch999oanew")
@Service
public class WuLiuNoteServiceImpl extends ServiceImpl<WuLiuNoteMapper, WuLiuNote> implements IWuLiuNoteService {
    @Resource
    private IWuLiuCategoryService wuLiuCategoryService;
    @Resource
    private IAreaInfoService areaInfoService;
    @Resource
    private IWuLiuLogService wuLiuLogService;

    @Resource
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;

    @DS("ch999oanew")
    @Override
    public Page<WuLiuNoteRes> pageList(WuLiuNoteReq wuLiuNoteReq) {
        if (StringUtils.isBlank(wuLiuNoteReq.getKey()) || StringUtils.isBlank(wuLiuNoteReq.getKey().trim())) {
            wuLiuNoteReq.setOptionType(null);
        }
        if (Objects.equals(wuLiuNoteReq.getStartTime(), LocalDateTime.MIN)) {
            wuLiuNoteReq.setEndTime(LocalDateTime.now());
            wuLiuNoteReq.setStartTime(wuLiuNoteReq.getEndTime().minusDays(1).plusSeconds(1));
        }

        if (wuLiuNoteReq.getEndTime() == null) {
            wuLiuNoteReq.setEndTime(LocalDateTime.now());
        }
        wuLiuNoteReq = wuLiuNoteReq.uniqueClearOtherVariable(wuLiuNoteReq);
        if (wuLiuNoteReq.getOptionType() != null) {
            SearchKindEnum searchKeyOption = EnumUtil.getEnumByCode(SearchKindEnum.class, wuLiuNoteReq.getOptionType());
            wuLiuNoteReq.setKey(wuLiuNoteReq.getKey().trim());
            if (Boolean.TRUE.equals(searchKeyOption.getIsNum()) && !NumberUtil.isNumber(wuLiuNoteReq.getKey())) {
                throw new CustomizeException(searchKeyOption.getMessage() + "只能填写数字！");
            }
            if (Objects.nonNull(searchKeyOption.getMaxLength()) && searchKeyOption.getMaxLength() < wuLiuNoteReq.getKey().length()) {
                throw new CustomizeException(searchKeyOption.getMessage() + "长度不能超过" + searchKeyOption.getMaxLength() + "!");
            }
            //是否需要将门店Code转化为id
            boolean flag = (SearchKindEnum.SEND_AREA == searchKeyOption ||
                    SearchKindEnum.RECEIVE_AREA == searchKeyOption) && !NumberUtil.isInteger(wuLiuNoteReq.getKey());
            if (flag) {
                List<Areainfo> areaInfoList = areaInfoService.lambdaQuery().eq(Areainfo::getArea, wuLiuNoteReq.getKey()).list();
                if (CollectionUtils.isEmpty(areaInfoList)) {
                    wuLiuNoteReq.setKey(null);
                } else {
                    wuLiuNoteReq.setKey(areaInfoList.get(0).getId().toString());
                }
            }
            wuLiuNoteReq.setKeyTypeName(searchKeyOption.getColumn());
        }

        if (wuLiuNoteReq.getOptionType() != null) {
            wuLiuNoteReq.setKeyTypeName("w." + EnumUtil.getEnumByCode(SearchKindEnum.class, wuLiuNoteReq.getOptionType()).getColumn());

            if (wuLiuNoteReq.getOptionType() == 1 || wuLiuNoteReq.getOptionType() == 2 || wuLiuNoteReq.getOptionType() == 11 || wuLiuNoteReq.getOptionType() == 13) {
                wuLiuNoteReq.setKey("%" + wuLiuNoteReq.getKey() + "%");
            }
        }

        Page<WuLiuNoteRes> resPage = new Page<>();
        int current = Optional.ofNullable(wuLiuNoteReq.getCurrent()).orElse(1);
        int size = Optional.ofNullable(wuLiuNoteReq.getSize()).orElse(30);
        int end = current * size;
        int begin = end - size + 1;
        int total = Optional.ofNullable(baseMapper.countAll(wuLiuNoteReq)).orElse(0);
        List<WuLiuNoteRes> wuLiuNoteResPage = this.baseMapper.pageList( wuLiuNoteReq, begin, end);

        if (CollectionUtils.isEmpty(wuLiuNoteResPage)) {
            return resPage;
        }

        Map<Integer, String> wuLiuCateNameList = wuLiuCategoryService.getWuLiuCateNameList();
        wuLiuNoteResPage.forEach(p -> p.setWuLiuCate(wuLiuCateNameList.get(p.getWCateId())));

        List<Long> wuliuidList = wuLiuNoteResPage.stream().map(WuLiuNoteRes::getId).collect(Collectors.toList());
        Map<String, WuLiuLogEntity> wuLiuIdToLogEntityMap = wuLiuLogService.lambdaQuery().in(WuLiuLogEntity::getWuliuid, wuliuidList)
                .orderByDesc(WuLiuLogEntity::getId)
                .orderByDesc(WuLiuLogEntity::getDtime)
                .list().stream().collect(Collectors.toMap(wuLiuLogEntity1 -> wuLiuLogEntity1.getWuliuid().toString(), Function.identity(), (v1, v2) -> v1));
        for (WuLiuNoteRes wuLiuNoteRes : wuLiuNoteResPage) {
            wuLiuNoteRes.setComment(StringUtils.EMPTY);
            computeOverTime(wuLiuNoteRes);
            if (WuLiuConstant.ZERO_STR.equals(wuLiuNoteRes.getTrackNumBind())) {
                wuLiuNoteRes.setTrackNumBind(StringUtils.EMPTY);
            }
            wuLiuNoteRes.setFee(DecimalFormatUtils.decimalFormat(Optional.ofNullable(wuLiuNoteRes.getFee1()).orElse(BigDecimal.ZERO)));
            WuLiuLogEntity wuLiuLogEntity = wuLiuIdToLogEntityMap.get(wuLiuNoteRes.getId().toString());
            if (Objects.isNull(wuLiuLogEntity)) {
                continue;
            }
            String comment = wuLiuLogEntity.getMsg() + " 【" +
                    wuLiuLogEntity.getInuser() + "】 " +
                    (Objects.isNull(wuLiuLogEntity.getDtime()) ? StringUtils.EMPTY
                            : wuLiuLogEntity.getDtime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            wuLiuNoteRes.setComment(comment);
        }
        resPage.setRecords(wuLiuNoteResPage);
        resPage.setTotal(total);
        resPage.setCurrent(current);
        resPage.setSize(size);
        return resPage;
    }

    /**
     * 查询物流单列表
     * @param wuLiuNoteReq
     * @return
     */
    @DS("ch999oanew")
    @Override
    public Page<WuLiuNoteRes> pageListV2(WuLiuNoteReq wuLiuNoteReq) {
        if (StringUtils.isBlank(wuLiuNoteReq.getKey()) || StringUtils.isBlank(wuLiuNoteReq.getKey().trim())) {
            wuLiuNoteReq.setOptionType(null);
        }

        if (StringUtils.isNotEmpty(wuLiuNoteReq.getKey())) {
            wuLiuNoteReq.setKey(wuLiuNoteReq.getKey().trim());
        }

        wuLiuNoteReq = wuLiuNoteReq.uniqueClearOtherVariable(wuLiuNoteReq);
        if (wuLiuNoteReq.getReceiveAreaId() == null && wuLiuNoteReq.getOptionType() == null) {
            wuLiuNoteReq.setReceiveAreaId(Collections.singletonList(abstractCurrentRequestComponent.getCurrentStaffId().getAreaId()));
        }

        if (wuLiuNoteReq.getOptionType() != null) {
            SearchKindEnum searchKeyOption = EnumUtil.getEnumByCode(SearchKindEnum.class, wuLiuNoteReq.getOptionType());
            wuLiuNoteReq.setKey(wuLiuNoteReq.getKey().trim());
            if (Boolean.TRUE.equals(searchKeyOption.getIsNum()) && !NumberUtil.isNumber(wuLiuNoteReq.getKey())) {
                throw new CustomizeException(searchKeyOption.getMessage() + "只能填写数字！");
            }
            if (Objects.nonNull(searchKeyOption.getMaxLength()) && searchKeyOption.getMaxLength() < wuLiuNoteReq.getKey().length()) {
                throw new CustomizeException(searchKeyOption.getMessage() + "长度不能超过" + searchKeyOption.getMaxLength() + "!");
            }
            //是否需要将门店Code转化为id
            boolean flag = (SearchKindEnum.SEND_AREA == searchKeyOption ||
                    SearchKindEnum.RECEIVE_AREA == searchKeyOption) && !NumberUtil.isInteger(wuLiuNoteReq.getKey());
            if (flag) {
                List<Areainfo> areaInfoList = areaInfoService.lambdaQuery().eq(Areainfo::getArea, wuLiuNoteReq.getKey()).list();
                if (CollectionUtils.isEmpty(areaInfoList)) {
                    wuLiuNoteReq.setKey(null);
                } else {
                    wuLiuNoteReq.setKey(areaInfoList.get(0).getId().toString());
                }
            }
        }

        Page<WuLiuNoteReq> condition = new Page<>();
        Page<WuLiuNoteRes> resPage = new Page<>();
        condition.setCurrent(wuLiuNoteReq.getCurrent());
        condition.setSize(wuLiuNoteReq.getSize());
        Page<WuLiuNoteRes> wuLiuNoteResPage = this.baseMapper.getWuLiuDataV2(condition, wuLiuNoteReq);
        if (CollectionUtils.isEmpty(wuLiuNoteResPage.getRecords())) {
            return resPage;
        }

        Map<Integer, String> wuLiuCateNameList = wuLiuCategoryService.getWuLiuCateNameList();
        wuLiuNoteResPage.getRecords().forEach(p -> {
            p.setWuLiuCate(wuLiuCateNameList.get(p.getWCateId()));
            if (StringUtils.isNumeric(p.getStatus())) {
                p.setStatusName(EnumUtil.getMessageByCode(WuLiuStatusEnum.class, Integer.valueOf(p.getStatus())));
            } else {
                p.setStatusName("");
            }
        });

        resPage.setRecords(wuLiuNoteResPage.getRecords());
        resPage.setTotal(wuLiuNoteResPage.getTotal());
        resPage.setCurrent(wuLiuNoteResPage.getCurrent());
        resPage.setSize(wuLiuNoteResPage.getSize());
        return resPage;
    }

    private static void computeOverTime(WuLiuNoteRes wuLiuNoteRes) {
        String overHourTime = wuLiuNoteRes.getOvertime();
        List<Integer> wuLiuStatusList = Arrays.asList(WuLiuStatusEnum.FINISH.getCode(), WuLiuStatusEnum.INVALID.getCode());
        List<Integer> wuLiuTypeList = Arrays.asList(WuLiuTypeEnum.INNER.getCode(), WuLiuTypeEnum.ORDER.getCode(),
                WuLiuTypeEnum.ORDER_EXPRESS.getCode(), WuLiuTypeEnum.FOURTEEN_DAY.getCode());
        if (Objects.isNull(overHourTime)
                || Integer.parseInt(overHourTime) <= 0
                || wuLiuStatusList.contains(Integer.valueOf(wuLiuNoteRes.getStatus()))
                || !wuLiuTypeList.contains(Integer.valueOf(wuLiuNoteRes.getWuLiuType()))) {
            wuLiuNoteRes.setOvertime(StringUtils.EMPTY);
            return;
        }
        int overHourTimeInt = Integer.parseInt(overHourTime);
        int dayHour = 24;
        if (overHourTimeInt < dayHour) {
            wuLiuNoteRes.setOvertime(overHourTime + "h");
            return;
        }
        wuLiuNoteRes.setOvertime((overHourTimeInt / dayHour) + "天");
    }

    /**
     * LogisticsExport
     *
     * @param wuLiuNoteReq
     * @param response
     */
    @Override
    public void exportList(WuLiuNoteReq wuLiuNoteReq, HttpServletResponse response) {
        wuLiuNoteReq.setSize(10000);
        wuLiuNoteReq.setCurrent(1);
        Page<WuLiuNoteRes> data = getWuLiuData(wuLiuNoteReq);
        List<WuLiuNoteRes> records = data.getRecords();
        records.forEach(WuLiuNoteServiceImpl::computeOverTime);
        List<WuLiuNoteRes2> collect1 = records.stream().map(this::toWuLiuNoteRes2).collect(Collectors.toList());
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        try {
            String fileName = ExcelUtils.getExportFileName("物流单管理");
            fileName = URLEncoder.encode(fileName, "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName);
            EasyExcelFactory.write(response.getOutputStream(), WuLiuNoteRes2.class).sheet("Sheet1").registerWriteHandler(new NormalHeadStyleWriteHandler()).doWrite(collect1);
        } catch (IOException e) {
            log.error("物流单数据导出报错：{}", e.getMessage());
            throw new CustomizeException("物流单数据导出报错：", e);
        }
    }

    private WuLiuNoteRes2 toWuLiuNoteRes2(WuLiuNoteRes wuLiuNoteRes) {
        WuLiuNoteRes2 wuLiuNoteRes2 = new WuLiuNoteRes2();
        wuLiuNoteRes2.setWuLiuCate(Optional.ofNullable(wuLiuNoteRes.getWuLiuCate()).orElse(StringUtils.EMPTY));
        wuLiuNoteRes2.setId(Optional.ofNullable(wuLiuNoteRes.getId()).orElse(0L));
        wuLiuNoteRes2.setSendName(Optional.ofNullable(wuLiuNoteRes.getSendName()).orElse(StringUtils.EMPTY));
        wuLiuNoteRes2.setSendArea(Optional.of(wuLiuNoteRes.getSendArea() + wuLiuNoteRes.getSendAreaName()).orElse(StringUtils.EMPTY));
        wuLiuNoteRes2.setSendSmallArea(Optional.ofNullable(wuLiuNoteRes.getSendSmallArea()).orElse(StringUtils.EMPTY));
        wuLiuNoteRes2.setSendBigArea(Optional.ofNullable(wuLiuNoteRes.getSendBigArea()).orElse(StringUtils.EMPTY));
        wuLiuNoteRes2.setReceiveName(Optional.ofNullable(wuLiuNoteRes.getReceiveName()).orElse(StringUtils.EMPTY));
        wuLiuNoteRes2.setReceiveAddress(Optional.ofNullable(makeAddress(wuLiuNoteRes)).orElse(StringUtils.EMPTY));
        wuLiuNoteRes2.setReceiveSmallArea(Optional.ofNullable(wuLiuNoteRes.getReceiveSmallArea()).orElse(StringUtils.EMPTY));
        wuLiuNoteRes2.setReceiveBigArea(Optional.ofNullable(wuLiuNoteRes.getReceiveBigArea()).orElse(StringUtils.EMPTY));
        wuLiuNoteRes2.setOvertime(Optional.ofNullable(wuLiuNoteRes.getOvertime()).orElse(StringUtils.EMPTY));
        wuLiuNoteRes2.setComment(Optional.ofNullable(wuLiuNoteRes.getComment()).orElse(StringUtils.EMPTY));
        wuLiuNoteRes2.setOperator(Optional.ofNullable(wuLiuNoteRes.getOperator()).orElse(StringUtils.EMPTY));
        wuLiuNoteRes2.setFee(wuLiuNoteRes.getFee1());
        wuLiuNoteRes2.setDistributionCost(wuLiuNoteRes.getDistributionCost());
        wuLiuNoteRes2.setDistance(wuLiuNoteRes.getDistance());
        if (Objects.isNull(wuLiuNoteRes.getStatus())) {
            wuLiuNoteRes2.setStatus(StringUtils.EMPTY);
        } else {
            wuLiuNoteRes2.setStatus(EnumUtil.getMessageByCode(WuLiuStatusEnum.class, Integer.valueOf(wuLiuNoteRes.getStatus())));
        }
        return wuLiuNoteRes2;
    }
    private Page<WuLiuNoteRes> getWuLiuData(WuLiuNoteReq wuLiuNoteReq) {
        if (StringUtils.isBlank(wuLiuNoteReq.getKey()) || StringUtils.isBlank(wuLiuNoteReq.getKey().trim())) {
            wuLiuNoteReq.setOptionType(null);
        }

        if (StringUtils.isNotEmpty(wuLiuNoteReq.getKey())) {
            wuLiuNoteReq.setKey(wuLiuNoteReq.getKey().trim());
        }

        if (LocalDateTime.MIN.equals(wuLiuNoteReq.getStartTime())) {
            wuLiuNoteReq.setStartTime(LocalDateTime.now());
            wuLiuNoteReq.setEndTime(wuLiuNoteReq.getStartTime().plusDays(1).minusSeconds(1));
        }

        if (Objects.isNull(wuLiuNoteReq.getEndTime())) {
            wuLiuNoteReq.setEndTime(LocalDateTime.now());
        }

        wuLiuNoteReq = wuLiuNoteReq.uniqueClearOtherVariable(wuLiuNoteReq);
        if (wuLiuNoteReq.getReceiveAreaId() == null && wuLiuNoteReq.getOptionType() == null) {
            wuLiuNoteReq.setReceiveAreaId(Collections.singletonList(abstractCurrentRequestComponent.getCurrentStaffId().getAreaId()));
        }

        if (wuLiuNoteReq.getOptionType() != null) {
            wuLiuNoteReq.setKeyTypeName("w." + EnumUtil.getEnumByCode(SearchKindEnum.class, wuLiuNoteReq.getOptionType()).getColumn());

            if (wuLiuNoteReq.getOptionType() == 1 || wuLiuNoteReq.getOptionType() == 2 || wuLiuNoteReq.getOptionType() == 11 || wuLiuNoteReq.getOptionType() == 13) {
                wuLiuNoteReq.setKey("%" + wuLiuNoteReq.getKey() + "%");
            }
        }

        Page<WuLiuNoteReq> condition = new Page<>();
        Page<WuLiuNoteRes> resPage = new Page<>();
        condition.setCurrent(wuLiuNoteReq.getCurrent());
        condition.setSize(wuLiuNoteReq.getSize());
        Page<WuLiuNoteRes> wuLiuNoteResPage = this.baseMapper.getWuLiuData(condition, wuLiuNoteReq);
        if (CollectionUtils.isEmpty(wuLiuNoteResPage.getRecords())) {
            return resPage;
        }

        Map<Integer, String> wuLiuCateNameList = wuLiuCategoryService.getWuLiuCateNameList();
        wuLiuNoteResPage.getRecords().forEach(p -> p.setWuLiuCate(wuLiuCateNameList.get(p.getWCateId())));

        resPage.setRecords(wuLiuNoteResPage.getRecords());
        resPage.setTotal(wuLiuNoteResPage.getTotal());
        resPage.setCurrent(wuLiuNoteResPage.getCurrent());
        resPage.setSize(wuLiuNoteResPage.getSize());
        return resPage;
    }

    private String makeAddress(WuLiuNoteRes wuLiuNoteRes) {
        String receiveArea = wuLiuNoteRes.getReceiveArea();
        String receiveAreaName = wuLiuNoteRes.getReceiveAreaName();
        // wuLiuNoteRes.getReceiveArea() + wuLiuNoteRes.getReceiveAreaName()
        if (StringUtils.isBlank(receiveArea) || StringUtils.isBlank(receiveAreaName)) {
            return wuLiuNoteRes.getReceiveAddress();
        }
        return receiveArea + receiveAreaName;
    }

}
