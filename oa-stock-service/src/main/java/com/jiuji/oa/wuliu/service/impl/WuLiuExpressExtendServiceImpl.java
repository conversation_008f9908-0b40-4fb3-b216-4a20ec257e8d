package com.jiuji.oa.wuliu.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.wuliu.bo.WuliuExpressMqBO;
import com.jiuji.oa.wuliu.entity.WuLiuExpressExtendEntity;
import com.jiuji.oa.wuliu.enums.JiujiJdxpressTypeEnum;
import com.jiuji.oa.wuliu.mapper.WuLiuExpressExtendMapper;
import com.jiuji.oa.wuliu.service.IWuLiuExpressExtendService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 *  服务实现类
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-22
 */
@Service
public class WuLiuExpressExtendServiceImpl extends ServiceImpl<WuLiuExpressExtendMapper, WuLiuExpressExtendEntity> implements IWuLiuExpressExtendService {

    /**
     * 查询京东快递类型
     * @param wuliuId
     * @return
     */
    @DS("ch999oanew")
    @Override
    public String getJdExpressType(Integer wuliuId) {
        String jdExpressType = this.baseMapper.selectJdExpressType(wuliuId);
        if (StringUtils.isBlank(jdExpressType) || !StringUtils.isNumeric(jdExpressType)) {
            return "";
        }
        return JiujiJdxpressTypeEnum.getMessage(Integer.valueOf(jdExpressType));
    }

    /**
     * 查询京东快递类型
     * @param wuliuId
     * @return
     */
    @DS("ch999oanew")
    @Override
    public Integer getJdDropMenuExpressType(Integer wuliuId) {
        String jdExpressType = this.baseMapper.selectJdExpressType(wuliuId);
        if (StringUtils.isBlank(jdExpressType) || !StringUtils.isNumeric(jdExpressType)) {
            return 1;
        } else {
            return Integer.valueOf(jdExpressType);
        }
    }

    /**
     * saveWuliuExpressExtend
     *
     * @param wuLiuExpressExtend
     */
    @Override
    @DS("oanewWrite")
    public void saveWuliuExpressExtend(WuliuExpressMqBO<WuLiuExpressExtendEntity> wuLiuExpressExtend) {
        if (Objects.nonNull(wuLiuExpressExtend) && Objects.nonNull(wuLiuExpressExtend.getData())) {
            this.save(wuLiuExpressExtend.getData());
        }
    }
}
