package com.jiuji.oa.wuliu.vo;


import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * ShunfengOrderInfoVO
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-12-14
 */
@Setter
@Getter
@Accessors(chain = true)
public class ShunfengOrderInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 物流单号生成时间
     */
    private LocalDateTime addDate;

    /**
     * 发件门店ID
     */
    private Integer sareaid;
    private String custid;

    private String num1;
    private String num2;

}
