package com.jiuji.oa.wuliu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Smallpro 实体类
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-15
 */
@Data
@Accessors(chain = true)
@TableName("Smallpro")
@ApiModel(value = "WuLiuSmallProEntity 实体类", description = "Smallpro 实体类")
public class WuLiuSmallProEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 小件接件单号
     */
    @ApiModelProperty("小件接件单号")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 商品名称
     */
    @ApiModelProperty("商品名称")
    @TableField("Name")
    private String name;

    /**
     * 地区
     */
    @ApiModelProperty("地区")
    @TableField("Area")
    private String area;

    /**
     * 会员id
     */
    @ApiModelProperty("会员id")
    @TableField("userid")
    private Integer userid;

    /**
     * 订单号
     */
    @ApiModelProperty("订单号")
    @TableField("sub_id")
    private Integer subId;

    /**
     * 购买时间
     */
    @ApiModelProperty("购买时间")
    @TableField("Buydate")
    private LocalDateTime buydate;

    /**
     * 外观
     */
    @ApiModelProperty("外观")
    @TableField("Outward")
    private String outward;

    /**
     * 保修 1在保，0非保
     */
    @ApiModelProperty("保修 1在保，0非保")
    @TableField("IsBaoxiu")
    private Boolean isBaoxiu;

    /**
     * 分组ID
     */
    @ApiModelProperty("分组ID")
    @TableField("Groupid")
    private Integer groupid;

    /**
     * 接件人
     */
    @ApiModelProperty("接件人")
    @TableField("Inuser")
    private String inuser;

    /**
     * 接件时间
     */
    @ApiModelProperty("接件时间")
    @TableField("Indate")
    private LocalDateTime indate;

    /**
     * 处理方式[1修 2换 3退 4现货]
     */
    @ApiModelProperty("处理方式[1修 2换 3退 4现货]")
    @TableField("Kind")
    private Integer kind;

    /**
     * 状态  0处理中，1处理完成，2已删除，3送修中
     */
    @ApiModelProperty("状态  0处理中，1处理完成，2已删除，3送修中")
    @TableField("Stats")
    private Integer stats;

    /**
     * 客户名称
     */
    @ApiModelProperty("客户名称")
    @TableField("Username")
    private String username;

    /**
     * 客户手机
     */
    @ApiModelProperty("客户手机")
    @TableField("Mobile")
    private String mobile;

    /**
     * 故障描述
     */
    @ApiModelProperty("故障描述")
    @TableField("Problem")
    private String problem;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    @TableField("Comment")
    private String comment;

    /**
     * 转到门店
     */
    @ApiModelProperty("转到门店")
    @TableField("toarea")
    private String toarea;

    /**
     * 是否转地区中
     */
    @ApiModelProperty("是否转地区中")
    @TableField("istoarea")
    private Boolean istoarea;

    /**
     * 接件确认
     */
    @ApiModelProperty("接件确认")
    @TableField("yuyueCheck")
    private Boolean yuyueCheck;

    /**
     * 预约ID
     */
    @ApiModelProperty("预约ID")
    @TableField("yuyueid")
    private Integer yuyueid;

    /**
     * 门店id
     */
    @ApiModelProperty("门店id")
    @TableField("areaid")
    private Integer areaid;

    /**
     * 转门店areaid
     */
    @ApiModelProperty("转门店areaid")
    @TableField("toareaid")
    private Integer toareaid;

    /**
     * 现货中关联的订单id
     */
    @ApiModelProperty("现货中关联的订单id")
    @TableField("oldid")
    private Integer oldid;

    /**
     * 验证码
     */
    @ApiModelProperty("验证码")
    @TableField("codeMsg")
    private String codeMsg;

    /**
     * 是否收银标志
     */
    @ApiModelProperty("是否收银标志")
    @TableField("isshouyinglock")
    private Boolean isshouyinglock;

    /**
     * 维修费
     */
    @ApiModelProperty("维修费")
    @TableField("feiyong")
    private BigDecimal feiyong;

    /**
     * 维修成本
     */
    @ApiModelProperty("维修成本")
    @TableField("costprice")
    private BigDecimal costprice;

    /**
     * 收银用户
     */
    @ApiModelProperty("收银用户")
    @TableField("shouyinuser")
    private String shouyinuser;

    /**
     * 收银时间
     */
    @ApiModelProperty("收银时间")
    @TableField("shouyindate")
    private LocalDateTime shouyindate;

    /**
     * 取件时间
     */
    @ApiModelProperty("取件时间")
    @TableField("qujiandate")
    private LocalDateTime qujiandate;

    /**
     * 维修渠道
     */
    @ApiModelProperty("维修渠道")
    @TableField("wxqudao")
    private String wxqudao;

    /**
     * 维修状态
     */
    @ApiModelProperty("维修状态")
    @TableField("wxState")
    private Integer wxState;

    /**
     * 维修人
     */
    @ApiModelProperty("维修人")
    @TableField("wxuser")
    private String wxuser;

    /**
     * 生成物流单Id
     */
    @ApiModelProperty("生成物流单Id")
    @TableField("wuliuid")
    private Integer wuliuid;

    /**
     * 配置
     */
    @ApiModelProperty("配置")
    @TableField("config")
    private String config;

    /**
     * 数据解绑状态
     */
    @ApiModelProperty("数据解绑状态")
    @TableField("dataRelease")
    private Integer dataRelease;

    /**
     * 串号/SN
     */
    @ApiModelProperty("串号/SN")
    @TableField("imei")
    private String imei;

    /**
     * 验证码图片fId
     */
    @ApiModelProperty("验证码图片fId")
    @TableField("fid")
    private String fid;

    /**
     * 备货订单Id
     */
    @ApiModelProperty("备货订单Id")
    @TableField("hhSubId")
    private Integer hhSubId;

    /**
     * 九机服务类型
     */
    @ApiModelProperty("九机服务类型")
    @TableField("ServiceType")
    private Integer serviceType;

    /**
     * 置换PPid
     */
    @ApiModelProperty("置换PPid")
    @TableField("changePpriceid")
    private Integer changePpriceid;

    /**
     * 凭证id
     */
    @ApiModelProperty("凭证id")
    @TableField("voucherId")
    private Integer voucherId;

    /**
     * 维修人
     */
    @ApiModelProperty("维修人")
    @TableField("wx_time")
    private LocalDateTime wxTime;

    @TableField("istui")
    private Boolean istui;

    @TableField("owenStats")
    private Integer owenStats;

    @TableField("intertime")
    private LocalDateTime intertime;

    @TableField("tongzhitime")
    private LocalDateTime tongzhitime;

    @TableField("toareatime")
    private LocalDateTime toareatime;

    @TableField("isdel")
    private Boolean isdel;

    @TableField("isth")
    private Boolean isth;

    @TableField("fcWuliuId")
    private Integer fcWuliuId;

    @TableField("isCheckmsg")
    private Integer isCheckmsg;

    @TableField("Smallpro_rv")
    private LocalDateTime smallproRv;

}