package com.jiuji.oa.wuliu.vo.express.req;

import com.jiuji.oa.stock.logisticscenter.vo.LogisticsBase;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> liu ming
 * @date 2021-11-03 下午 2:26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class JdCreateOrderParamReq extends LogisticsBase {
    /**
     * 物流单号
     **/
    private Integer orderNo;
    /**
     * 收件人名称
     **/
    private String receiverName;
    /**
     * 寄件所在省份
     **/
    private String receiverProvinceName;
    /**
     * 寄件人所在城市
     **/
    private String receiverCityName;
    /**
     * 收件人地址
     **/
    private String receiverAddress;
    /**
     * 收件人电话
     **/
    private String receiverMobile;
    /**
     * 寄件人姓名
     **/
    private String senderName;
    /**
     * 寄件人电话
     **/
    private String senderMobile;
    /**
     * 寄件所在省份
     **/
    private String senderProvinceName;
    /**
     * 寄件人所在城市
     **/
    private String senderCityName;
    /**
     * 寄件人地址
     **/
    private String senderAddress;
    /**
     * 包裹名称
     **/
    private String expressItemName;
    /**
     * 包裹数量
     **/
    private Integer expressItemQty;
    /**
     * 商品重量
     **/
    private Double grossWeight;
    /**
     * 包裹体积
     **/
    private Double grossVolume;
    /**
     * 物流子类型
     **/
    private Integer dropMenuExpressType;
}
