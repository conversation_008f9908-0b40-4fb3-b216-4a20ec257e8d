package com.jiuji.oa.wuliu.utils.map;


import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 高德地图 Web服务 API 搜索POI 请求参数 DTO
 * https://lbs.amap.com/api/webservice/guide/api/search
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2022-01-29
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
public class AmapPoiDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 请求服务权限标识
     */
    private String key;

    /**
     * 查询关键字
     */
    private String keywords;

    /**
     * 查询POI类型
     */
    private String types;

    /**
     * 查询城市
     */
    private String city;

    /**
     * 仅返回指定城市数据
     */
    private String citylimit;

    /**
     * 是否按照层级展示子POI数据
     */
    private String children;

    /**
     * 每页记录数据
     */
    private Integer offset;

    /**
     * 当前页数
     */
    private Integer page;

    /**
     * 返回结果控制
     */
    private String extensions;
    /**
     * 数字签名
     * 此项默认返回基本地址信息；取值为all返回地址信息、附近POI、道路以及道路交叉口信息。
     */
    private String sig;
    /**
     * 返回数据格式类型
     * 可选值：JSON，XML
     */
    private String output;

    /**
     * 回调函数
     * callback值是用户定义的函数名称，此参数只在output=JSON时有效
     */
    private String callback;

}
