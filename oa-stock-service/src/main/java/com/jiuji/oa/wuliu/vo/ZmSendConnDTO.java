package com.jiuji.oa.wuliu.vo;


import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR> [<EMAIL>]
 * @date 2021-11-05
 */
@Data
@Accessors(chain = true)
public class ZmSendConnDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * smsnumber 表ID
     */
    private Integer tmp;

    private String title;

    /**
     * 会员编号集合
     */
    private String smsnumber;

    /**
     * 内容
     */
    private String content;

    /**
     * 超链接
     */
    private String link;

    /**
     * 活动图片
     */
    private String hdimg;

    /**
     * 消息类型
     */
    private Integer kind;

    /**
     * 如果是活动，活动结束时间
     */
    private String endtime;

    /**
     * 文章推送指定平台
     */
    @JsonProperty("PlatForm")
    @JSONField(name = "PlatForm")
    private String platForm;

    @JsonProperty("ExtraData")
    @JSONField(name = "ExtraData")
    private String extraData;

    @JsonProperty("AppLink")
    @JSONField(name = "AppLink")
    private String appLink;

}
