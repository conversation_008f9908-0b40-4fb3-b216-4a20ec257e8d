package com.jiuji.oa.wuliu.enums;

import cn.hutool.core.convert.Convert;
import com.jiuji.oa.nc.abnormal.vo.ShowPrintingEnumVOV2;
import com.jiuji.oa.wuliu.constant.WuLiuTypeConstant;
import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum WuLiuTypeEnum implements CodeMessageEnumInterface {
    /**
     * 物流类型枚举
     * https://jiuji.yuque.com/docs/share/ee608be3-474f-4dfd-ad67-89564e726dea?#
     **/
    INNER(WuLiuTypeConstant.INNER, "内部调拨"),
    ORDER(WuLiuTypeConstant.ORDER, "新品订单"),
    AFTER_SERVICE(WuLiuTypeConstant.AFTER_SERVICE, "维修订单"),
    ORDER_EXPRESS(WuLiuTypeConstant.ORDER_EXPRESS, "加急订单"),
    VISIT(WuLiuTypeConstant.VISIT, "上门订单"),
    OTHERS(WuLiuTypeConstant.OTHERS, "其他业务"),
    FOURTEEN_DAY(WuLiuTypeConstant.FOURTEEN_DAY, "良品订单"),
    ACCESSORY(WuLiuTypeConstant.ACCESSORY, "物料调拨"),
    SECOND_HAND(WuLiuTypeConstant.SECOND_HAND, "拍靓机派送"),
    INVOICE(WuLiuTypeConstant.INVOICE, "发票派送"),
    RENT(WuLiuTypeConstant.RENT, "租机派送");


    private final Integer code;
    private final String message;

    public static String getMessage(Integer code) {
        for (WuLiuTypeEnum typeEnum : values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum.getMessage();
            }
        }
        return null;
    }

    public static List<ShowPrintingEnumVOV2> getAllPrintingEnum() {
        WuLiuTypeEnum[] array = WuLiuTypeEnum.values();
        List<ShowPrintingEnumVOV2> arrayList = new ArrayList<>();
        for (WuLiuTypeEnum t : array) {
            ShowPrintingEnumVOV2 ShowPrintingEnumVOV2 = new ShowPrintingEnumVOV2()
                    .setLabel(t.getMessage())
                    .setValue(Convert.toStr(t.getCode()));
            arrayList.add(ShowPrintingEnumVOV2);
        }
        return arrayList;
    }
}
