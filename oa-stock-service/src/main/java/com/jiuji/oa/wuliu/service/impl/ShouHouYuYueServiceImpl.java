package com.jiuji.oa.wuliu.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.wuliu.dto.ShouHouAddInfoDTO;
import com.jiuji.oa.wuliu.dto.YuYueAddInfoDTO;
import com.jiuji.oa.wuliu.entity.ShouHouYuYueEntity;
import com.jiuji.oa.wuliu.entity.ShouHouYuYueEntity2;
import com.jiuji.oa.wuliu.mapper.ShouHouYuYueMapper;
import com.jiuji.oa.wuliu.service.IShouHouYuYueService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 售后预约表,责任小组：销售 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-09
 */
@Service
@DS("oanewWrite")
public class ShouHouYuYueServiceImpl extends ServiceImpl<ShouHouYuYueMapper, ShouHouYuYueEntity> implements IShouHouYuYueService {

    /**
     * 获取一条预约信息
     *
     * @param id
     * @return
     */
    @Override
    public ShouHouYuYueEntity getOne(Integer id) {
        return this.baseMapper.getOne(id);
    }

    /**
     * 获取一条预约信息 2
     *
     * @param id
     * @return ShouHouYuYueEntity2
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-17
     */
    @Override
    @DS("ch999oanew")
    public ShouHouYuYueEntity2 getOne2(Integer id) {
        return baseMapper.getOne2(id);
    }

    /**
     * 查询预约物流收寄件信息
     */
    @Override
    @DS("ch999oanew")
    public YuYueAddInfoDTO getYuYueAddInfo(Integer yuyueId) {
        return baseMapper.getYuYueAddInfo(yuyueId);
    }

    /**
     * 查询预约物流收寄件信息
     */
    @Override
    @DS("ch999oanew")
    public ShouHouAddInfoDTO getShouhouAddInfo(Integer shouhouId) {
        return baseMapper.getShouhouAddInfo(shouhouId);
    }
}
