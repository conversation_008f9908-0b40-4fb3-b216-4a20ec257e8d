package com.jiuji.oa.wuliu.service;

import com.jiuji.oa.wuliu.vo.ShansongOrderCalculateModelVO;
import com.jiuji.oa.wuliu.vo.ShansongOrderCalculateVO;

/**
 * <AUTHOR>
 */
public interface IShansongService {

    /**
     * orderCalculate
     *
     * @param data
     * @return ShansongOrderCalculateModelVO
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-05
     */
    ShansongOrderCalculateModelVO orderCalculate(ShansongOrderCalculateVO data);

    /**
     * placeOrder
     *
     * @param issOrderNo
     * @return ShansongOrderCalculateModelVO
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-05
     */
    ShansongOrderCalculateModelVO placeOrder(String issOrderNo);
}
