package com.jiuji.oa.wuliu.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 门店创建订单
 * <AUTHOR>
 * @date 2022/3/17 17:34
 */
@Data
public class CreateOrderShopResDTO {
    /**
     * 物流单id
     */
    private String deliveryId;
    /**
     * 运单号
     **/
    private String waybillNo;

    /**
     * 快递费用
     */
    private BigDecimal fee;

    /**
     * 距离
     */
    private BigDecimal distance;
}
