package com.jiuji.oa.wuliu.vo.paimai.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/8 11:32
 */
@Data
public class AddPaimaiWuliuReqVO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 快递类型
     */
    @NotNull(message = "expressType不能为空")
    private Integer expressType;
    /**
     * 快递子分类
     */
    @NotNull(message = "dropMenuExpressType不能为空")
    private Integer dropMenuExpressType;
    /**
     * 租户Id
     */
    private Long xtenantId;
    /**
     * 月结卡号
     */
    private String monthlyCard;
    /**
     * 是否上门揽件
     * 是否通过手持终端通知顺丰收派员上门收件，支持以下值：1：要求 0：不要求
     */
    private Integer isDocall;
    /**
     * 京东：保价金额(保留小数点后两位)
     **/
    private BigDecimal guaranteeValueAmount;
    /**
     *京东：重量，单位：kg，保留小数点后两位
     *京东(零担)：总重量(grossWeight)；单位 kg（非冷链专车，必填）
     */
    private BigDecimal weight;
    /**
     * 京东：体积，单位：cm3，保留小数点后两位
     * 京东(零担)：总体积(grossVolume)；单位立方米（非冷链专车，必填）
     */
    private BigDecimal vloumn;
    /**
     * 开始上门揽件时间
     */
    private LocalDateTime sendStartTime;

    /**
     * 包裹数
     */
    private Integer parcelQuantity;
    /**
     * 订单备注
     */
    @Size(max = 20, message = "快递备注信息不能超过20个字符")
    private String remark;
    /**
     * 发放方门店id
     */
    private Integer senderAreaId;
    /**
     * 寄件联系人
     */
    private String senderName;
    /**
     * 寄件人手机
     */
    private String senderMobile;
    /**
     * 寄件cityId
     */
    private Integer senderCityId;
    /**
     * 寄件方详细地址
     */
    private String senderAddress;
    /**
     * 收件人
     */
    private String receiverName;
    /**
     * 收件CityId
     */
    private Integer receiverCityId;
    /**
     * 收件方手机号码
     */
    private String receiverMobile;
    /**
     * 收件人 详细地址
     */
    private String receiverAddress;
    /**
     * 寄送清单
     */
    private List<AddPaimaiWuliuDetailVO> list;
    /**
     * 物流单上的备注
     */
    @Size(max = 500, message = "物流单备注信息不能超过500字符")
    private String comment;
    /**
     * 提交人
     */
    private String inuser;
    /**
     * 自定义地址
     */
    @JsonProperty("isNewAdress")
    private Boolean newJadress;
}
