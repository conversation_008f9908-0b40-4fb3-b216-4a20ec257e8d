package com.jiuji.oa.wuliu.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.wuliu.bo.SellerInfoBO;
import com.jiuji.oa.wuliu.dto.OnlineNationalSupplementStockDTO;
import com.jiuji.oa.wuliu.entity.WuLiuBasket2Entity;
import com.jiuji.oa.wuliu.entity.WuLiuBasketEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 订单商品表,责任小组：销售 Mapper 接口
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-15
 */
@Mapper
public interface WuLiuBasketMapper extends BaseMapper<WuLiuBasketEntity> {

    /**
     * orderServices.getSubBasket
     * 获取订单商品
     *
     * @param subId
     * @param redPacketPpriceIdList 微信红包 ppriceid
     * @param xcAreaId              瑕疵库存地区（用于查询瑕疵库存查询）
     * @param showDel             Integer
     * @return List<WuLiuBasket2Entity>
     * @date 2021-10-15
     * <AUTHOR> [<EMAIL>]
     */
    List<WuLiuBasket2Entity> getSubBasket(Integer subId, List<Integer> redPacketPpriceIdList, Integer xcAreaId, Integer showDel);

    SellerInfoBO getSellerInfoBySubId(@Param("subId") Integer subId);
    /**
     * 获取线上国补库存信息
     * @param subId
     * @return
     */
    OnlineNationalSupplementStockDTO getOnlineNationalSupplementStockBySubId(@Param("subId") Integer subId);
}
