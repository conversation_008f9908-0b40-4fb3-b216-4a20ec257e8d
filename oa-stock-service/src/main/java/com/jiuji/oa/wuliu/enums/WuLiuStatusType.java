package com.jiuji.oa.wuliu.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2021/10/11
 */
@Getter
@AllArgsConstructor

public enum WuLiuStatusType {
    /**
     * 等待取货
     */
    WAIT_PICKUP(1,"等待取货"),
    /**
     * 等待派送
     */
    WAIT_DELIVERY(2,"等待派送"),
    /**
     * 派送中
     */
    DELIVERING(3,"派送中"),
    /**
     * 已签收
     */
    SIGNED(4,"已签收"),
    /**
     * 完成
     */
    FINISH(5,"完成"),
    /**
     * 作废
     */
    NULLIFY(6,"作废");

    /**
     * 编码
     */
    private Integer code;
    /**
     * 信息
     */
    private String message;
}
