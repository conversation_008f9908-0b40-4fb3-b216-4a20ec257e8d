package com.jiuji.oa.wuliu.dto.req;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.jiuji.cloud.org.vo.enums.RoleTermModuleEnum;
import com.jiuji.cloud.org.vo.response.RoleTermRes;
import com.jiuji.oa.nc.common.exception.CustomizeException;
import com.jiuji.oa.stock.common.util.BusinessUtil;
import com.jiuji.oa.stock.common.vo.CheckDataViewScopeReq;
import com.jiuji.oa.wuliu.enums.SearchKindEnum;
import com.jiuji.tc.common.vo.R;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 * @date 2021/10/12
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WuLiuNoteReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull
    @Min(value = 1)
    @ApiModelProperty(value = "当前第几页", required = true)
    private Integer current ;

    @NotNull
    @Min(value = 1)
    @Max(value = 100)
    @ApiModelProperty(value = "每页数量", required = true)
    private Integer size ;

    @ApiModelProperty(value = "升序字段")
    private List<String> ascs;

    @ApiModelProperty(value = "降序字段")
    private List<String> descs;

    private Integer optionType;

    /**
     * 搜索关键字
     */
    private String key;

    /**
     * 发送地区
     */
    private List<Integer> sendAreaId;

    /**
     * 收件地区id
     */
    private List<Integer> receiveAreaId;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 物流单分类
     */
    private Integer wuLiuType;

    /**
     * 按时间搜索类型
     */
    private Integer dateType;

    /**
     * 搜索开始时间
     */
    private LocalDateTime startTime;

    /**
     * 搜索截止时间
     */
    private LocalDateTime endTime;

    /**
     * 超时时间，以小时计算
     */
    private Integer overTimeHours;

    /**
     * 是否是系统创建
     */
    private Boolean systemCreate;

    /**
     * 是否异常
     */
    private Boolean abnormal;

    /**
     * 配送方式,对应 sub 表中的 delivery 字段
     */
    private Integer delivery;

    /**
     * 用于定义数据库表列的字段，控制器调用无需传参
     */
    @Size(max = 0,message = "keyTypeName 不需要参数！")
    private String keyTypeName;

    /**
     * 物流分类id
     */
    @JsonProperty("wCateId")
    @JSONField(name = "wCateId")
    private List<Integer> wCateId;

    /**
     * 是否有附件
     */
    private Boolean attachment;

    /**
     * 报销审核状态
     */
    private Integer reimbursement;

    /**
     * 是否启用高级
     */
    private Boolean extend;

    /**
     * 是否启用高级
     */
    private List<String> expressComList;

    /**
     * 查询条件为唯一标识去掉其他查询条件
     * @param req
     * @return
     */
    public WuLiuNoteReq uniqueClearOtherVariable(WuLiuNoteReq req) {
        boolean uniqueFlag = Arrays.asList(SearchKindEnum.WU_LIU_NUM.getCode(),SearchKindEnum.TRACK_NUM_BIND.getCode()).contains(req.getOptionType());
        //物流单号
        if (uniqueFlag && StringUtils.isNotBlank(req.getKey())) {
            WuLiuNoteReq newReq = new WuLiuNoteReq();
            newReq.setSize(req.getSize());
            newReq.setCurrent(req.getCurrent());
            newReq.setKey(req.getKey());
            newReq.setOptionType(req.getOptionType());
            return newReq;
        }
        //状态为空，或者签收、删除
        if (req.getStatus() == null || Arrays.asList(4,5,6).contains(req.getStatus())) {
            Boolean extend = req.getExtend();
            if (Boolean.FALSE.equals(req.getExtend())) {
                req.setStartTime(null);
                req.setEndTime(null);
                req.setExtend(true);
            }
            //角色数据查询
            AtomicReference<RoleTermRes> roleTermRes = new AtomicReference<>();
            R dataViewRes = BusinessUtil.checkDataViewScope(CheckDataViewScopeReq.builder().moduleEnum(RoleTermModuleEnum.LOGISTICS)
                    .getStartTimeFun(req::getStartTime).getEndTimeFun(req::getEndTime)
                    .setStartTimeFun(req::setStartTime)
                    .setEndTimeFun(req::setEndTime)
                    .build(), roleTermRes::set);
            if (!dataViewRes.isSuccess()) {
                throw new CustomizeException(dataViewRes.getUserMsg());
            }
            if (roleTermRes.get() == null) {
                req.setExtend(extend);
            }
        }
        return req;
    }
}
