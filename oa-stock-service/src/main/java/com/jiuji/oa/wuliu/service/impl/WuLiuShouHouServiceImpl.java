/*
 *     Copyright © 2006 - 2020 九机网 All Rights Reserved
 *
 */
package com.jiuji.oa.wuliu.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.wuliu.entity.WuLiuShouHouEntity;
import com.jiuji.oa.wuliu.mapper.WuLiuShouHouMapper;
import com.jiuji.oa.wuliu.service.IWuLiuShouHouService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * 售后主表,责任小组：销售 ServiceImpl
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-09
 */
@RequiredArgsConstructor
@Slf4j
@DS("oanewWrite")
@Service
public class WuLiuShouHouServiceImpl extends ServiceImpl<WuLiuShouHouMapper, WuLiuShouHouEntity> implements IWuLiuShouHouService {

    @Override
    public WuLiuShouHouEntity getShouHou(Integer subId, Integer xianShi) {
        return baseMapper.getShouHou(subId, xianShi);
    }

    @Override
    public WuLiuShouHouEntity getShouHou2(Integer subId, Integer xianShi, Integer areaId) {
        return baseMapper.getShouHou2(subId, xianShi, areaId);
    }

    @Override
    public WuLiuShouHouEntity getShouHou3(Integer subId, Integer areaId) {
        return baseMapper.getShouHou3(subId, areaId);
    }

}