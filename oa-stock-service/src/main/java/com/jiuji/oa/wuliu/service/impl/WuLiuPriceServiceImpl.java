/*
 *     Copyright © 2006 - 2020 九机网 All Rights Reserved
 *
 */
package com.jiuji.oa.wuliu.service.impl;

import com.alibaba.fastjson.TypeReference;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.stock.common.cache.RedisUtils;
import com.jiuji.oa.wuliu.constant.WuLiuConstant;
import com.jiuji.oa.wuliu.entity.WuLiuPriceEntity;
import com.jiuji.oa.wuliu.mapper.WuLiuPriceMapper;
import com.jiuji.oa.wuliu.service.IWuLiuPriceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;


/**
 * 物流价格 ServiceImpl
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-09-30
 */
@Service
@RequiredArgsConstructor
@Slf4j
@DS("oanewWrite")
public class WuLiuPriceServiceImpl extends ServiceImpl<WuLiuPriceMapper, WuLiuPriceEntity> implements IWuLiuPriceService {

    @Override
    public List<WuLiuPriceEntity> listAll() {
        return Optional.ofNullable(RedisUtils.get(WuLiuConstant.WU_LIU_PRICE_REDIS_KEY, new TypeReference<List<WuLiuPriceEntity>>() {
        })).orElseGet(() -> {
            List<WuLiuPriceEntity> list = list();
            if (CollectionUtils.isNotEmpty(list)) {
                RedisUtils.set(WuLiuConstant.WU_LIU_PRICE_REDIS_KEY, list, WuLiuConstant.WU_LIU_PRICE_REDIS_KEY_EXPIRE);
                return list;
            } else {
                RedisUtils.set(WuLiuConstant.WU_LIU_PRICE_REDIS_KEY, null, WuLiuConstant.WU_LIU_PRICE_REDIS_KEY_EXPIRE);
                return Collections.emptyList();
            }
        });
    }

    @Override
    public WuLiuPriceEntity getOne(Integer startCityId, Integer endCityId, Boolean isSameCity) {
        Stream<WuLiuPriceEntity> stream = listAll().stream().filter(item2 -> startCityId.equals(item2.getCityId()) && endCityId.equals(item2.getToCityId()));
        return (isSameCity == null ? stream : stream.filter(item2 -> isSameCity.equals(item2.getTonChenFlag()))).findFirst().orElse(null);
    }

    @Override
    public WuLiuPriceEntity getOne(String startCityId, String endCityId, Boolean isSameCity) {
        return getOne(Integer.valueOf(startCityId), Integer.valueOf(endCityId), isSameCity);
    }

    @Override
    public void rebuildCache() {
        List<WuLiuPriceEntity> list = list();
        if (CollectionUtils.isNotEmpty(list)) {
            RedisUtils.set(WuLiuConstant.WU_LIU_PRICE_REDIS_KEY, list, WuLiuConstant.WU_LIU_PRICE_REDIS_KEY_EXPIRE);
        }
    }

}