package com.jiuji.oa.wuliu.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * 物流单状态
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-12-04
 */
@AllArgsConstructor
@Getter
public enum WuLiuStatusEnum implements CodeMessageEnumInterface {

    /**
     * 等待取货
     */
    WAITING_PICK(1, "等待取货"),

    /**
     * 等待派送
     */
    WAITING_DELIVERY(2, "等待派送"),

    /**
     * 派送中
     */
    DELIVERY(3, "运输中"),

    /**
     * 完成
     */
    FINISH(4, "已签收"),

    /**
     * 作废
     */
    INVALID(5, "已删除"),

    /**
     * 已签收
     */
    SIGHED(6, "已签收"),

    /**
     * 等待处理
     */
    PENDING_PROCESSING(7, "等待处理");

    private Integer code;
    private String message;
}
