package com.jiuji.oa.wuliu.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.nc.abnormal.vo.ShowPrintingEnumVOV2;
import com.jiuji.oa.nc.common.constant.RedisKeys;
import com.jiuji.oa.nc.common.db.MyDynamicRoutingDataSource;
import com.jiuji.oa.stock.common.constant.CommonConst;
import com.jiuji.oa.wuliu.entity.WuLiuCategoryEntity;
import com.jiuji.oa.wuliu.mapper.WuLiuCategoryMapper;
import com.jiuji.oa.wuliu.mapstruct.WuLiuCategoryStruct;
import com.jiuji.oa.wuliu.service.IWuLiuCategoryService;
import com.jiuji.oa.wuliu.vo.res.WuLiuCategoryResVO;
import com.jiuji.tc.utils.enums.EnumVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR> liu ming
 * @since 2021-09-27
 */
@Service
@RequiredArgsConstructor
@Slf4j
@DS("ch999oanew")
public class WuLiuCategoryServiceImpl extends ServiceImpl<WuLiuCategoryMapper, WuLiuCategoryEntity> implements
        IWuLiuCategoryService {

    private static final String DASHED = "-";
    private final StringRedisTemplate redisTemplate;
    @Resource
    private WuLiuCategoryStruct wuLiuCategoryStruct;

    /**
     * 查询物流单分类列表
     *
     * @return 物流单分类列表
     */
    @Override
    public List<WuLiuCategoryResVO> queryWuLiuCategoryList() {
        String cacheKey = StrUtil.format("{}_{}", RedisKeys.WULIU_CATEGORY_KEY,
                MyDynamicRoutingDataSource.isTaxModel());
        if (Boolean.TRUE.equals(redisTemplate.hasKey(cacheKey))) {
            return JSONUtil.toList(redisTemplate.opsForValue().get(cacheKey), WuLiuCategoryResVO.class);
        }
        synchronized (IWuLiuCategoryService.class) {
            if (Boolean.TRUE.equals(redisTemplate.hasKey(cacheKey))) {
                return JSONUtil.toList(redisTemplate.opsForValue().get(cacheKey), WuLiuCategoryResVO.class);
            }
            List<WuLiuCategoryEntity> wuLiuCategoryEntityList = this.lambdaQuery().orderByAsc(WuLiuCategoryEntity::getLeve)
                    .orderByAsc(WuLiuCategoryEntity::getCatRank).list();
            List<WuLiuCategoryResVO> wuLiuCategoryResVOList = new ArrayList<>();
            if (CollectionUtils.isEmpty(wuLiuCategoryEntityList)) {
                return wuLiuCategoryResVOList;
            }
            wuLiuCategoryResVOList = wuLiuCategoryStruct.toWuLiuCategoryResVO(wuLiuCategoryEntityList);
            redisTemplate.opsForValue()
                    .set(cacheKey, JSONUtil.toJsonStr(wuLiuCategoryResVOList), CommonConst.SEVEN, TimeUnit.DAYS);
            return wuLiuCategoryResVOList;
        }
    }

    @Override
    public List<WuLiuCategoryEntity> getWuLiuCategoryList() {
        return this.baseMapper.selectList(null);
    }

    @Override
    public String getWuLiuCateName(Integer wCateId) {
        // 避免循环查询数据库，所以一次性查询所有数据
        List<WuLiuCategoryEntity> wuLiuCategoryList = this.getWuLiuCategoryList();
        Map<Integer, WuLiuCategoryEntity> wuLiuCategoryMap = wuLiuCategoryList.stream().distinct()
                .collect(Collectors.toMap(WuLiuCategoryEntity::getCateId, p -> p));
        StringBuilder result = new StringBuilder();
        WuLiuCategoryEntity wuLiuCategory = wuLiuCategoryMap.get(wCateId);
        // 拼接格式 xxx-xxx
        result.append(wuLiuCategoryMap.get(wuLiuCategory.getPId()).getCateName()).append(DASHED).append(wuLiuCategory.getCateName());
        return result.toString();
    }

    @Override
    public Map<Integer, String> getWuLiuCateNameList() {
        List<WuLiuCategoryEntity> wuLiuCategoryList = this.getWuLiuCategoryList();
        Map<Integer, WuLiuCategoryEntity> wuLiuCategoryMap = wuLiuCategoryList.stream().distinct()
                .collect(Collectors.toMap(WuLiuCategoryEntity::getCateId, p -> p));

        // 将每个 id 的完整名称全部拼接好，放入 map 中、
        Map<Integer, String> nameMap = new HashMap<>(wuLiuCategoryMap.size());
        for (Map.Entry<Integer, WuLiuCategoryEntity> entry : wuLiuCategoryMap.entrySet()) {
            Integer nowWuLiuCateId = entry.getKey();
            StringBuilder result = new StringBuilder();
            WuLiuCategoryEntity wuLiuCategory = wuLiuCategoryMap.get(nowWuLiuCateId);

            if (wuLiuCategory.getPId().equals(0)) {
                nameMap.put(entry.getKey(), wuLiuCategory.getCateName());
                continue;
            }
            // 拼接格式 xxx-xxx
            result.append(wuLiuCategoryMap.get(wuLiuCategory.getPId()).getCateName()).append(DASHED).append(wuLiuCategory.getCateName());
            nameMap.put(entry.getKey(), result.toString());
        }
        return nameMap;
    }

    /**
     * 查询分类枚举
     */
    @Override
    public List<EnumVO> getEnumVoList() {
        List<WuLiuCategoryResVO> wuLiuCategoryRes = this.queryWuLiuCategoryList();
        if (CollectionUtils.isNotEmpty(wuLiuCategoryRes)) {
            return wuLiuCategoryRes.stream().map(wuLiuCategoryStruct::toEnumVO).collect(Collectors.toList());
        } else {
            return new ArrayList<>();
        }
    }

    @Override
    public List<ShowPrintingEnumVOV2> getShowPrintingEnumVOList() {
        List<ShowPrintingEnumVOV2> arrayList = new ArrayList<>();
        List<WuLiuCategoryResVO> wuLiuCategoryRes = this.queryWuLiuCategoryList();
        if (CollectionUtils.isNotEmpty(wuLiuCategoryRes)) {
            for (WuLiuCategoryResVO t : wuLiuCategoryRes) {
                ShowPrintingEnumVOV2 ShowPrintingEnumVOV2 = new ShowPrintingEnumVOV2()
                        .setLabel(t.getCateName())
                        .setValue(Convert.toStr(t.getCateId()));
                arrayList.add(ShowPrintingEnumVOV2);
            }
        } else {
            return new ArrayList<>();
        }
        return arrayList;
    }



    /**
     * 查询分类枚举
     */
    @Override
    public Map<Integer,String> getWuLiuCateNameMap() {
        List<WuLiuCategoryResVO> wuLiuCategoryRes = this.queryWuLiuCategoryList();
        Map<Integer,String> cateNameMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(wuLiuCategoryRes)) {
            return wuLiuCategoryRes.stream().collect(Collectors.toMap(WuLiuCategoryResVO::getCateId, WuLiuCategoryResVO::getCateName));
        }
        return cateNameMap;
    }

}
