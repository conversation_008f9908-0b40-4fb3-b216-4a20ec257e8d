package com.jiuji.oa.wuliu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 转售单（良品订单）[责任小组:回收]
 * </p>
 *
 * <AUTHOR> yao yao
 * @since 2022-06-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("recover_marketInfo")
public class RecoverMarketinfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单号
     */
    @TableId(value = "sub_id", type = IdType.AUTO)
    private Long subId;

    /**
     * 订单日期
     */
    private LocalDateTime subDate;

    /**
     * 订单状态
     */
    private Integer subCheck;

    /**
     * 收货人
     */
    private String subTo;

    /**
     * 收货人电话
     */
    private String subTel;

    /**
     * 支付方式
     */
    private Integer subPay;

    /**
     * 备注信息
     */
    private String comment;

    /**
     * 添加人
     */
    @TableField("Inuser")
    private String inuser;

    /**
     * 收货人手机号
     */
    private String subMobile;

    /**
     * 销货单打印次数
     */
    private Integer printxcount;

    /**
     * 地区
     */
    private String area;

    /**
     * 自提点ID
     */
    @TableField("zitidianID")
    private Integer zitidianid;

    /**
     * 会员ID
     */
    private Long userid;

    /**
     * 无用字段
     */
    private String onlinePay;

    /**
     * 营销活动ID
     */
    @TableField("Marketingid")
    private Integer marketingid;

    /**
     * 是否开过发票
     */
    private Boolean ispiao;

    /**
     * 订单类型
     */
    private Integer subtype;

    /**
     * 配送方式
     */
    private Integer delivery;

    /**
     * 应付金额
     */
    @TableField("yingfuM")
    private Double yingfum;

    /**
     * 已付金额
     */
    @TableField("yifuM")
    private Double yifum;

    /**
     * 运费
     */
    @TableField("feeM")
    private Double feem;

    /**
     * 优惠码
     */
    @TableField("youhui1M")
    private Double youhui1m;

    /**
     * 手续费
     */
    @TableField("shouxuM")
    private Double shouxum;

    /**
     * 积点支付金额
     */
    @TableField("jidianM")
    private Double jidianm;

    /**
     * 出库时间
     */
    @TableField("tradeDate")
    private LocalDateTime tradedate;

    /**
     * 交易时间
     */
    @TableField("tradeDate1")
    private LocalDateTime tradedate1;

    /**
     * 定金
     */
    private Double dingjing;

    /**
     * 主单号
     */
    @TableField("subPID")
    private Integer subpid;

    /**
     * 交易人员
     */
    private String trader;

    /**
     * 返款
     */
    private Double fankuan;

    /**
     * 良品订单类型
     */
    @TableField("saleType")
    private Integer saletype;

    /**
     * 门店id
     */
    private Integer areaid;

    /**
     * 快递单
     */
    private String kuaididan;

    /**
     * 九机币
     */
    @TableField("coinM")
    private Double coinm;

    /**
     * 订单是否锁定
     */
    private Integer islock;

    /**
     * 渠道名称
     */
    private String qudaoname;

    /**
     * 发货状态
     */
    private Integer isfahuo;

    /**
     * 预计送达时间
     */
    @TableField("expectTime")
    private LocalDateTime expecttime;

    /**
     * 新订单号
     */
    @TableField("newSubId")
    private Integer newsubid;

    /**
     * 拍卖支付时间
     */
    @TableField("paimai_pay_time")
    private LocalDateTime paimaiPaytime;

    /**
     * 拍卖支付时间
     */
    private LocalDateTime paimaiPayTime;

    /**
     * 该条记录是否被标记生成凭证
     */

    private Integer u8VoucherId;


    /**
     * 九机u8上账标识
     */
    private Integer u8VoucherId2;

    @TableField("returnDate")
    private LocalDateTime returndate;

    @TableField("voucherId")
    private Integer voucherid;


    @TableField("subApartDate")
    private LocalDateTime subapartdate;


}
