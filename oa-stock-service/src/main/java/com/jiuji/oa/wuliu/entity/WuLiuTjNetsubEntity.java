package com.jiuji.oa.wuliu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 实体类
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-19
 */
@Data
@Accessors(chain = true)
@TableName("tj_netsub")
@ApiModel(value = "WuLiuTjNetsubEntity 实体类", description = " 实体类")
public class WuLiuTjNetsubEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("dtime")
    private LocalDateTime dtime;

    @TableField("minutes")
    private Integer minutes;

    @TableField("inuser")
    private String inuser;

    @TableField("sub_id")
    private Long subId;

}