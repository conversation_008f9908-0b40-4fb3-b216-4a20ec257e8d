package com.jiuji.oa.wuliu.vo;


import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * JdCreateOrderResult.DataResult
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-16
 */
@Data
@Accessors(chain = true)
public class DataResultDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("resultCode")
    @JSONField(name = "resultCode")
    private String resultCode;

    @JsonProperty("promiseTimeType")
    @JSONField(name = "promiseTimeType")
    private Integer promiseTimeType;

    @JsonProperty("orderId")
    @JSONField(name = "orderId")
    private String orderId;

    @JsonProperty("deliveryId")
    @JSONField(name = "deliveryId")
    private String expressNumber;

    @JsonProperty("lwbNo")
    @JSONField(name = "lwbNo")
    private String expressNumber2;

    @JsonProperty("resultMessage")
    @JSONField(name = "resultMessage")
    private String resultMessage;

    @JsonProperty("preSortResult")
    @JSONField(name = "preSortResult")
    private PreSortResultDTO preSortResult;
    /**
     * 运输方式
     */
    @JsonProperty("transType")
    @JSONField(name = "transType")
    private Integer transType;
    /**
     * 需要重试
     */
    @JsonProperty("needRetry")
    @JSONField(name = "needRetry")
    private Boolean needRetry;
    /**
     *
     */
    @JsonProperty("expressOperationMode")
    @JSONField(name = "expressOperationMode")
    private Integer expressOperationMode;
}
