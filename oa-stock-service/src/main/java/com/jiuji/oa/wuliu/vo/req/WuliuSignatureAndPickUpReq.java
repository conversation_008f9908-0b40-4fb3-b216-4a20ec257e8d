package com.jiuji.oa.wuliu.vo.req;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class WuliuSignatureAndPickUpReq {
    /**
     * 物流单号
     */
    @NotEmpty(message = "物流单号不能为空")
    private List<Integer> wuliuIdList;

    /**
     * 2：签收，3：取货
     */
    @NotNull(message = "操作类型不能为空")
    private Integer kind;
}
