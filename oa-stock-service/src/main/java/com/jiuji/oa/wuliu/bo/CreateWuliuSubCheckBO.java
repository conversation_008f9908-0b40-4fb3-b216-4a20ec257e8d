package com.jiuji.oa.wuliu.bo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/12/15 16:08
 */
@Data
@Accessors(chain = true)
public class CreateWuliuSubCheckBO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 订单id
     */
    private Integer subId;
    /**
     * 订单类型
     */
    private Integer subType;
    /**
     * 物流单id
     */
    private Integer wuliuId;
    /**
     * 操作人
     */
    private String inUser;
}
