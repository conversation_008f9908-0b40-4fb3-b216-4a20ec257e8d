package com.jiuji.oa.wuliu.vo.express;

import com.jiuji.oa.stock.logisticscenter.vo.LogisticsBase;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 说明： 物流创建订单公共 Res （支持：美团）
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-12-05
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class MeiTuanResultDTO extends LogisticsBase {

    /**
     * 平台物流单号(美团)
     **/
    private String platformInsideId;

    /**
     * 物流平台(美团)
     **/
    private String expressName;

    /**
     * 配送活动标识Id(美团)
     **/
    private Long deliveryId;

    /**
     * 全局物流单号
     **/
    private Long logisticsId;

    /**
     * 配送距离(美团)
     **/
    private Integer deliveryDistance;


}
