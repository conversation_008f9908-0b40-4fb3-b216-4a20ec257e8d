package com.jiuji.oa.wuliu.utils;

import java.nio.ByteOrder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;

/**
 * 数字转字节数组工具类
 *
 * <AUTHOR> @date 2021-11-17
 */
public final class BitConverterUtils {

    private static final int ZERO = 0;
    private static final int ONE = 1;
    private static final int TWO = 2;
    private static final int THREE = 3;
    private static final int FOUR = 4;
    private static final int FIVE = 5;
    private static final int SIX = 6;
    private static final int SEVEN = 7;
    private static final int EIGHT = 8;
    private static final int SIXTEEN = 16;
    private static final int TWENTY_FOUR = 24;
    private static final int THIRTY_TWO = 32;
    private static final int FORTY = 40;
    private static final int FORTY_EIGHT = 48;
    private static final int FIFTY_SIX = 56;

    private BitConverterUtils() {
    }

    /**
     * 以字节数组的形式返回指定的布尔值
     *
     * @param data 一个布尔值
     * @return 长度为 1 的字节数组
     */

    public static byte[] getBytes(boolean data) {
        byte[] bytes = new byte[ONE];

        bytes[ZERO] = (byte) (data ? ONE : ZERO);

        return bytes;
    }

    /**
     * 以字节数组的形式返回指定的 16 位有符号整数值
     *
     * @param data 要转换的数字
     * @return 长度为 2 的字节数组
     */
    public static byte[] getBytes(short data) {
        byte[] bytes = new byte[TWO];

        if (isLittleEndian()) {
            bytes[ZERO] = (byte) (data & 0xff);
            bytes[ONE] = (byte) ((data & 0xff00) >> EIGHT);
        } else {
            bytes[ONE] = (byte) (data & 0xff);

            bytes[ZERO] = (byte) ((data & 0xff00) >> EIGHT);
        }
        return bytes;
    }

    /**
     * 以字节数组的形式返回指定的 Unicode 字符值
     *
     * @param data 要转换的字符
     * @return 长度为 2 的字节数组
     */

    public static byte[] getBytes(char data) {
        byte[] bytes = new byte[TWO];
        if (isLittleEndian()) {
            bytes[0] = (byte) (data);
            bytes[1] = (byte) (data >> EIGHT);
        } else {
            bytes[1] = (byte) (data);
            bytes[0] = (byte) (data >> EIGHT);
        }
        return bytes;
    }

    /**
     * 以字节数组的形式返回指定的 32 位有符号整数值
     *
     * @param data 要转换的数字
     * @return 长度为 4 的字节数组
     */

    public static byte[] getBytes(int data) {
        byte[] bytes = new byte[FOUR];
        if (isLittleEndian()) {
            bytes[0] = (byte) (data & 0xff);
            bytes[1] = (byte) ((data & 0xff00) >> EIGHT);
            bytes[TWO] = (byte) ((data & 0xff0000) >> SIXTEEN);
            bytes[THREE] = (byte) ((data & 0xff000000) >> TWENTY_FOUR);
        } else {
            bytes[THREE] = (byte) (data & 0xff);
            bytes[TWO] = (byte) ((data & 0xff00) >> EIGHT);
            bytes[1] = (byte) ((data & 0xff0000) >> SIXTEEN);
            bytes[0] = (byte) ((data & 0xff000000) >> TWENTY_FOUR);
        }
        return bytes;
    }

    /**
     * 以字节数组的形式返回指定的 64 位有符号整数值
     *
     * @param data 要转换的数字
     * @return 长度为 8 的字节数组
     */
    public static byte[] getBytes(long data) {
        byte[] bytes = new byte[EIGHT];

        if (isLittleEndian()) {
            bytes[0] = (byte) (data & 0xff);
            bytes[1] = (byte) ((data >> EIGHT) & 0xff);
            bytes[TWO] = (byte) ((data >> SIXTEEN) & 0xff);
            bytes[THREE] = (byte) ((data >> TWENTY_FOUR) & 0xff);
            bytes[FOUR] = (byte) ((data >> THIRTY_TWO) & 0xff);
            bytes[FIVE] = (byte) ((data >> FORTY) & 0xff);
            bytes[SIX] = (byte) ((data >> FORTY_EIGHT) & 0xff);
            bytes[SEVEN] = (byte) ((data >> FIFTY_SIX) & 0xff);
        } else {
            bytes[SEVEN] = (byte) (data & 0xff);
            bytes[SIX] = (byte) ((data >> EIGHT) & 0xff);
            bytes[FIVE] = (byte) ((data >> SIXTEEN) & 0xff);
            bytes[FOUR] = (byte) ((data >> TWENTY_FOUR) & 0xff);
            bytes[THREE] = (byte) ((data >> THIRTY_TWO) & 0xff);
            bytes[TWO] = (byte) ((data >> FORTY) & 0xff);
            bytes[1] = (byte) ((data >> FORTY_EIGHT) & 0xff);
            bytes[0] = (byte) ((data >> FIFTY_SIX) & 0xff);
        }
        return bytes;
    }

    /**
     * 以字节数组的形式返回指定的单精度浮点值
     *
     * @param data 要转换的数字
     * @return 长度为 4 的字节数组
     */

    public static byte[] getBytes(float data) {
        return getBytes(Float.floatToIntBits(data));
    }

    /**
     * 以字节数组的形式返回指定的双精度浮点值
     *
     * @param data 要转换的数字
     * @return 长度为 8 的字节数组
     */

    public static byte[] getBytes(double data) {
        return getBytes(Double.doubleToLongBits(data));
    }

    /**
     * 将指定字符串中的所有字符编码为一个字节序列
     *
     * @param data 包含要编码的字符的字符串
     * @return 一个字节数组，包含对指定的字符集进行编码的结果
     */

    public static byte[] getBytes(String data) {
        return data.getBytes(StandardCharsets.UTF_8);
    }

    /**
     * 将指定字符串中的所有字符编码为一个字节序列
     *
     * @param data        包含要编码的字符的字符串
     * @param charsetName 字符集编码
     * @return 一个字节数组，包含对指定的字符集进行编码的结果
     */

    public static byte[] getBytes(String data, String charsetName) {
        return data.getBytes(Charset.forName(charsetName));
    }

    /**
     * 返回由字节数组转换来的布尔值
     *
     * @param bytes 字节数组
     * @return 布尔值
     */

    public static boolean toBoolean(byte[] bytes) {
        return bytes[0] != 0;
    }

    /**
     * 返回由字节数组中的指定的一个字节转换来的布尔值
     *
     * @param bytes      字节数组
     * @param startIndex 起始下标
     * @return 布尔值
     */
    public static boolean toBoolean(byte[] bytes, int startIndex) {
        return toBoolean(copyFrom(bytes, startIndex, 1));
    }

    /**
     * 返回由字节数组转换来的 16 位有符号整数
     *
     * @param bytes 字节数组
     * @return 由两个字节构成的 16 位有符号整数
     */
    public static short toShort(byte[] bytes) {
        if (isLittleEndian()) {
            return (short) ((0xff & bytes[0]) | (0xff00 & (bytes[1] << EIGHT)));
        } else {
            return (short) ((0xff & bytes[1]) | (0xff00 & (bytes[0] << EIGHT)));
        }
    }

    /**
     * 返回由字节数组中的指定的两个字节转换来的 16 位有符号整数
     *
     * @param bytes      字节数组
     * @param startIndex 起始下标
     * @return 由两个字节构成的 16 位有符号整数
     */
    public static short toShort(byte[] bytes, int startIndex) {
        return toShort(copyFrom(bytes, startIndex, TWO));
    }

    /**
     * 返回由字节数组转换来的 Unicode 字符
     *
     * @param bytes 字节数组
     * @return 由两个字节构成的字符
     */
    public static char toChar(byte[] bytes) {
        if (isLittleEndian()) {
            return (char) ((0xff & bytes[0]) | (0xff00 & (bytes[1] << EIGHT)));
        } else {
            return (char) ((0xff & bytes[1]) | (0xff00 & (bytes[0] << EIGHT)));
        }
    }

    /**
     * 返回由字节数组中的指定的两个字节转换来的 Unicode 字符
     *
     * @param bytes      字节数组
     * @param startIndex 起始下标
     * @return 由两个字节构成的字符
     */
    public static char toChar(byte[] bytes, int startIndex) {
        return toChar(copyFrom(bytes, startIndex, TWO));
    }

    /**
     * 返回由字节数组转换来的 32 位有符号整数
     *
     * @param bytes 字节数组
     * @return 由四个字节构成的 32 位有符号整数
     */
    public static int toInt(byte[] bytes) {
        if (isLittleEndian()) {
            return (0xff & bytes[0])
                    | (0xff00 & (bytes[1] << EIGHT))
                    | (0xff0000 & (bytes[TWO] << SIXTEEN))
                    | (0xff000000 & (bytes[THREE] << TWENTY_FOUR));
        } else {
            return (0xff & bytes[THREE])
                    | (0xff00 & (bytes[TWO] << EIGHT))
                    | (0xff0000 & (bytes[1] << SIXTEEN))
                    | (0xff000000 & (bytes[0] << TWENTY_FOUR));
        }
    }

    /**
     * 返回由字节数组中的指定的四个字节转换来的 32 位有符号整数
     *
     * @param bytes      字节数组
     * @param startIndex 起始下标
     * @return 由四个字节构成的 32 位有符号整数
     */
    public static int toInt(byte[] bytes, int startIndex) {
        return toInt(copyFrom(bytes, startIndex, FOUR));
    }

    /**
     * 返回由字节数组转换来的 64 位有符号整数
     *
     * @param bytes 字节数组
     * @return 由八个字节构成的 64 位有符号整数
     */
    public static long toLong(byte[] bytes) {
        if (isLittleEndian()) {
            return (0xffL & bytes[0])
                    | (0xff00L & ((long) bytes[1] << EIGHT))
                    | (0xff0000L & ((long) bytes[TWO] << SIXTEEN))
                    | (0xff000000L & ((long) bytes[THREE] << TWENTY_FOUR))
                    | (0xff00000000L & ((long) bytes[FOUR] << THIRTY_TWO))
                    | (0xff0000000000L & ((long) bytes[FIVE] << FORTY))
                    | (0xff000000000000L & ((long) bytes[SIX] << FORTY_EIGHT))
                    | (0xff00000000000000L & ((long) bytes[SEVEN] << FIFTY_SIX));
        } else {
            return (0xffL & bytes[SEVEN])
                    | (0xff00L & ((long) bytes[SIX] << EIGHT))
                    | (0xff0000L & ((long) bytes[FIVE] << SIXTEEN))
                    | (0xff000000L & ((long) bytes[FOUR] << TWENTY_FOUR))
                    | (0xff00000000L & ((long) bytes[THREE] << THIRTY_TWO))
                    | (0xff0000000000L & ((long) bytes[TWO] << FORTY))
                    | (0xff000000000000L & ((long) bytes[1] << FORTY_EIGHT))
                    | (0xff00000000000000L & ((long) bytes[0] << FIFTY_SIX));
        }
    }

    /**
     * 返回由字节数组中的指定的八个字节转换来的 64 位有符号整数
     *
     * @param bytes      字节数组
     * @param startIndex 起始下标
     * @return 由八个字节构成的 64 位有符号整数
     */
    public static long toLong(byte[] bytes, int startIndex) {
        return toLong(copyFrom(bytes, startIndex, EIGHT));
    }

    /**
     * 返回由字节数组转换来的单精度浮点数
     *
     * @param bytes 字节数组
     * @return 由四个字节构成的单精度浮点数
     */
    public static float toFloat(byte[] bytes) {
        return Float.intBitsToFloat(toInt(bytes));
    }

    /**
     * 返回由字节数组中的指定的四个字节转换来的单精度浮点数
     *
     * @param bytes      字节数组
     * @param startIndex 起始下标
     * @return 由四个字节构成的单精度浮点数
     */
    public static float toFloat(byte[] bytes, int startIndex) {
        return Float.intBitsToFloat(toInt(copyFrom(bytes, startIndex, FOUR)));
    }

    /**
     * 返回由字节数组转换来的双精度浮点数
     *
     * @param bytes 字节数组
     * @return 由八个字节构成的双精度浮点数
     */
    public static double toDouble(byte[] bytes) {
        return Double.longBitsToDouble(toLong(bytes));
    }

    /**
     * 返回由字节数组中的指定的八个字节转换来的双精度浮点数
     *
     * @param bytes      字节数组
     * @param startIndex 起始下标
     * @return 由八个字节构成的双精度浮点数
     */
    public static double toDouble(byte[] bytes, int startIndex) {
        return Double.longBitsToDouble(toLong(copyFrom(bytes, startIndex, EIGHT)));
    }

    /**
     * 返回由字节数组转换来的字符串
     *
     * @param bytes 字节数组
     * @return 字符串
     */
    public static String toString(byte[] bytes) {
        return new String(bytes, StandardCharsets.UTF_8);
    }

    /**
     * 返回由字节数组转换来的字符串
     *
     * @param bytes       字节数组
     * @param charsetName 字符集编码
     * @return 字符串
     */
    public static String toString(byte[] bytes, String charsetName) {
        return new String(bytes, Charset.forName(charsetName));
    }

    /**
     * 以字符串表示形式返回字节数组的内容
     *
     * @param bytes 字节数组
     * @return 字符串形式的 bytes
     */
    public static String toHexString(byte[] bytes) {
        if (bytes == null) {
            return "null";
        }
        int iMax = bytes.length - 1;
        if (iMax == -1) {
            return "[]";
        }
        StringBuilder b = new StringBuilder();
        b.append('[');
        for (int i = 0; ; i++) {
            b.append(String.format("%02x", bytes[i] & 0xFF));
            if (i == iMax) {
                return b.append(']').toString();
            }
            b.append(", ");
        }
    }

// --------------------------------------------------------------------------------------------

    /**
     * 数组拷贝。
     *
     * @param src 字节数组。
     * @param off 起始下标。
     * @param len 拷贝长度。
     * @return 指定长度的字节数组。
     */
    private static byte[] copyFrom(byte[] src, int off, int len) {
        byte[] bits = new byte[len];
        for (int i = off, j = 0; i < src.length && j < len; i++, j++) {
            bits[j] = src[i];
        }
        return bits;
    }

    /**
     * 判断 CPU Endian 是否为 Little
     *
     * @return 判断结果
     */
    private static boolean isLittleEndian() {
        return ByteOrder.nativeOrder() == ByteOrder.LITTLE_ENDIAN;
    }

}
