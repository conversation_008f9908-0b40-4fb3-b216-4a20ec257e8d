package com.jiuji.oa.wuliu.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.jiuji.oa.nc.common.constant.RedisKeys;
import com.jiuji.oa.nc.common.db.MyDynamicRoutingDataSource;
import com.jiuji.oa.stock.common.constant.CommonConst;
import com.jiuji.oa.stock.logisticscenter.utils.JsonParseUtil;
import com.jiuji.oa.wuliu.entity.ExpressTypeEntity;
import com.jiuji.oa.wuliu.mapper.ExpressTypeMapper;
import com.jiuji.oa.wuliu.mapstruct.ExpressTypeMapStruct;
import com.jiuji.oa.wuliu.service.IExpressTypeService;
import com.jiuji.oa.wuliu.vo.req.ExpressTypeVO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 物流单快递类型 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-30
 */
@Service
@DS("oa_nc")
@RequiredArgsConstructor
public class ExpressTypeServiceImpl extends ServiceImpl<ExpressTypeMapper, ExpressTypeEntity> implements IExpressTypeService {

    private final StringRedisTemplate redisTemplate;
    private final ExpressTypeMapStruct expressTypeMapStruct;

    /**
     * 列出所有快递类型
     *
     * @return List<ExpressTypeVO>
     */
    @Override
    public List<ExpressTypeVO> listExpressType() {
        String cacheKey = StrUtil.format("{}_{}", RedisKeys.EXPRESS_TYPE,
                MyDynamicRoutingDataSource.isTaxModel());
        if (Boolean.TRUE.equals(redisTemplate.hasKey(cacheKey))) {
            return JsonParseUtil.json2Object(redisTemplate.opsForValue().get(cacheKey), new TypeReference<List<ExpressTypeVO>>() {
            });
        }
        List<ExpressTypeEntity> list = this.list();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<ExpressTypeVO> expressTypeVOList = list.stream().sorted(Comparator.comparing(ExpressTypeEntity::getRankOrder))
                .map(expressTypeMapStruct::toExpressTypeVO)
                .collect(Collectors.toList());
        redisTemplate.opsForValue()
                .set(cacheKey, JSONUtil.toJsonStr(expressTypeVOList), CommonConst.SEVEN, TimeUnit.DAYS);
        return expressTypeVOList;
    }
}
