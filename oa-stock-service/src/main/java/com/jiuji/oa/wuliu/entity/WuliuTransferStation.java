package com.jiuji.oa.wuliu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 物流中转信息记录表
 * @TableName wuliu_transfer_station
 */
@TableName(value ="wuliu_transfer_station")
@Data
public class WuliuTransferStation implements Serializable {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 物流单号
     */
    @TableField(value = "wuliu_id")
    private Integer wuliuId;

    /**
     * 中转站门店id
     */
    @TableField(value = "station_area_id")
    private Integer stationAreaId;

    /**
     * 中转状态；0 未到达  1已到达
     */
    @TableField(value = "state")
    private Integer state;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 删除标识
     */
    @TableField(value = "is_del")
    private Integer isDel;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}