package com.jiuji.oa.wuliu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;


/**
 * ShansongStoreConfigsEntity
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-12-05
 */
@TableName(value = "ShansongStoreConfigs")
@Data
public class ShansongStoreConfigsEntity implements Serializable {
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * OA系统门店id
     */
    @TableField("areaid")
    private Integer areaId;
    /**
     * OA系统门店名称
     */
    private String area;
    /**
     * 闪送系统门店名称
     */
    @TableField("storename")
    private String storeName;
    /**
     * 闪送系统门店id
     */
    @TableField("storeid")
    private String storeId;
}