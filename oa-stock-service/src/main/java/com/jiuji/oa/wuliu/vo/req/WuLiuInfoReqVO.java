package com.jiuji.oa.wuliu.vo.req;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.jiuji.oa.stock.logistics.paotui.entity.WuliuPaijianInfoEntry;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 *
 * @description: AppointmentInfoReqVO
 * </p>
 * @author: David
 * @create: 2021-10-09 13:43
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class WuLiuInfoReqVO extends WuLiuAddOrUpdateReqVO {

    private static final long serialVersionUID = 1L;

    /**
     * 售后Id
     */
    @JsonProperty("shouhouid")
    @JSONField(name = "shouhouid")
    private Integer shouHouId;

    /**
     * 会话当前门店
     */
    private Integer sessionAreaId;

    /**
     * 寄件省市区
     */
    private String sendCityName;

    /**
     * 收件省市区
     */
    private String receiverCityName;

    /**
     * 寄件门店
     */
    private String sendAreaName;

    /**
     * 收件门店
     */
    private String receiverAreaName;

    /**
     * 订单配送方式
     */
    private Integer delivery;
    /**
     * 订单配送方式名称
     */
    private String deliveryName;

    /**
     * 骑行距离
     */
    private Long distance;

    /**
     * 配送成本
     */
    private BigDecimal distributionCost;

    /**
     * 默认快递类型
     */
    private String defaultCom;

    /**
     * 是否开通美团
     */
    private Boolean openMeituanFlag;

    /**
     * 客户取消订单申请
     */
    private Integer cancelApplication;
    /**
     * 异常锁单
     */
    private Integer isLock;
    /**
     * 线上国补订单标识
     */
    private Integer onlineNationalSupplement;

    private WuliuPaijianInfoEntry paiJain;
    /**
     * 快递列表
     */
    private List<NuVo> nuList;

    //定义一个静态内部类, String com, String nu
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class NuVo {
        /**
         * 快递方式
         */
        @ApiModelProperty("快递方式")
        private String com;
        /**
         * 快递方式名称
         */
        @ApiModelProperty("快递方式名称")
        private String comName;

        /**
         * 快递单号
         */
        @ApiModelProperty("快递单号")
        private String nu;
    }
}
