package com.jiuji.oa.wuliu.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.stock.common.vo.IPage;
import com.jiuji.oa.wuliu.dto.req.WuLiuNoteReq;
import com.jiuji.oa.wuliu.dto.res.WuLiuNoteRes;
import com.jiuji.oa.wuliu.entity.WuLiuNote;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 物理管理界面服务接口
 *
 * <AUTHOR>
 * @date 2021/10/08
 */
public interface IWuLiuNoteService extends IService<WuLiuNote> {
    /**
     * 通过收货门店id查询物流信息
     *
     * @param wuLiuNoteReq 请求体
     * @return {@link IPage}<{@link List}<{@link WuLiuNoteRes}>>
     */
    Page<WuLiuNoteRes> pageList(WuLiuNoteReq wuLiuNoteReq);

    /**
     * 查询物流单V2
     * @param wuLiuNoteReq
     * @return
     */
    Page<WuLiuNoteRes> pageListV2(WuLiuNoteReq wuLiuNoteReq);

    /**
     * exportList
     *
     * @param wuLiuNoteReq
     * @param response
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-05
     */
    void exportList(WuLiuNoteReq wuLiuNoteReq, HttpServletResponse response);
}
