/*
 *    Copyright © 2006 - 2020 九机网 All Rights Reserved
 *
 */

package com.jiuji.oa.wuliu.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.wuliu.dto.SubPositionDTO;
import com.jiuji.oa.wuliu.dto.req.SubPositionReq;
import com.jiuji.oa.wuliu.entity.WuLiuRecoverMarketInfo2Entity;
import com.jiuji.oa.wuliu.entity.WuLiuRecoverMarketInfoEntity;
import com.jiuji.oa.wuliu.vo.res.WuliuOrderInfoRes;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 转售单（良品订单）,责任小组：回收 Mapper
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-09-29
 */
@Mapper
public interface WuLiuRecoverMarketInfoMapper extends BaseMapper<WuLiuRecoverMarketInfoEntity> {

    /**
     * getRecoverMarketInfo
     *
     * @param subId
     * @return WuLiuRecoverMarketInfoEntity
     * @date 2021-10-15
     * <AUTHOR> [<EMAIL>]
     */
    WuLiuRecoverMarketInfoEntity getRecoverMarketInfo(@Param("subId") Integer subId, @Param("areaId") Integer areaId);

    /**
     * recoverServices.getSubBasket
     * 获取订单商品
     *
     * @param subId Integer
     * @param showDel  Integer
     * @return List<WuLiuRecoverMarketInfo2Entity>
     * @date 2021-10-15
     * <AUTHOR> [<EMAIL>]
     */
    List<WuLiuRecoverMarketInfo2Entity> getSubBasket(@Param("subId") Integer subId, Integer showDel);

    /**
     * getBasketId
     *
     * @param danHaoBind
     * @return Integer
     * <AUTHOR> [<EMAIL>]
     * @date 2021-10-27
     */
    Integer getBasketId(Integer danHaoBind);


    /**
     * selectSubdate
     *
     * @param subId
     * @return
     */
    List<LocalDateTime> selectSubdate(Long subId);

    /**
     * updateSellerBySubId
     *
     * @param subId
     * @param userName
     * @return boolean
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-05
     */
    boolean updateSellerBySubId(@Param("subId") Long subId, @Param("userName") String userName);
    /**
     * 查询良品订单收货位置信息
     *
     * @param req
     * @return
     */
    SubPositionDTO getSubPositionBySub(@Param("req") SubPositionReq req);

    /**
     * 支付商户订单号查询订单物流信息
     *
     * @param payId
     * @return
     */
    WuliuOrderInfoRes getOrderByPayId(@Param("payId") Integer payId);

    WuliuOrderInfoRes getOrderByPayDes(@Param("payDes") String payDes);

    WuLiuRecoverMarketInfoEntity getSubBySubId(@Param("subId") Integer subId);

    /**
     * 查询良品订单经纬度
     * @param req
     * @return
     */
    SubPositionDTO getSubAddressPositionBySubId(@Param("req") SubPositionReq req);
}
