package com.jiuji.oa.wuliu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 微信用户记录,责任小组：会员组 实体类
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-19
 */
@Data
@Accessors(chain = true)
@TableName("WeixinUser")
@ApiModel(value = "WuLiuWeixinUserEntity 实体类", description = "微信用户记录,责任小组：会员组 实体类")
public class WuLiuWeixinUserEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 会员id
     */
    @ApiModelProperty("会员id")
    private Integer userid;

    /**
     * 微信/qq的openid
     */
    @ApiModelProperty("微信/qq的openid")
    @TableField("openid")
    private String openid;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField("dtime")
    private LocalDateTime dtime;

    /**
     * 类型 1：微信 2:QQ 3:alipay 4:appleid
     */
    @ApiModelProperty("类型 1：微信 2:QQ 3:alipay 4:appleid")
    private Integer kinds;

    /**
     * unionid
     */
    @ApiModelProperty("unionid")
    @TableField("unionid")
    private String unionid;

    /**
     * 微信Id
     */
    @ApiModelProperty("微信Id")
    private Integer wxid;

    /**
     * 昵称
     */
    @ApiModelProperty("昵称")
    @TableField("nickname")
    private String nickname;

    /**
     * 租户
     */
    @ApiModelProperty("租户")
    @TableField("xtenant")
    private Integer xtenant;

    /**
     * 应用ID
     */
    @ApiModelProperty("应用ID")
    @TableField("appleid")
    private String appleid;

    @TableField("id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("follow")
    private Boolean follow;

    @TableField("type")
    private Integer type;

    @TableField("followCheckTime")
    private LocalDateTime followCheckTime;

    @TableField("WeixinUser_rv")
    private LocalDateTime weixinuserRv;

}