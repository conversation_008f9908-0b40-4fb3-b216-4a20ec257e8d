package com.jiuji.oa.wuliu.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


/**
 * SubLog
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-12-05
 */
@Data
@Accessors(chain = true)
public class SubLog {

    @JsonProperty("_id")
    @JSONField(name = "_id")
    private Integer id2;
    private Integer id;
    private Long subId;
    private String comment;
    private String inUser;

    @JsonProperty("_DTime")
    @JSONField(name = "_DTime")
    private LocalDateTime dTime2;
    private LocalDateTime dTime;
    private Integer type;
    private Boolean showType;
}
