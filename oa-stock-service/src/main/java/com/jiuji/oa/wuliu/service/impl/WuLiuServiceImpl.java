/*
 *     Copyright © 2006 - 2020 九机网 All Rights Reserved
 *
 */
package com.jiuji.oa.wuliu.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONException;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.dynamic.datasource.tx.TransactionContext;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ch999.common.util.atlas.AtlasUtil;
import com.ch999.common.util.atlas.CoordinateUtil;
import com.ch999.common.util.secure.MD5Util;
import com.ch999.common.util.utils.Exceptions;
import com.ch999.common.util.vo.atlas.Coordinate;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import com.jd.open.api.sdk.DefaultJdClient;
import com.jd.open.api.sdk.domain.etms.TraceQueryJsf.response.get.TraceDTO;
import com.jd.open.api.sdk.domain.etms.TraceQueryJsf.response.get.TraceQueryResultDTO;
import com.jd.open.api.sdk.request.etms.LdopReceiveTraceGetRequest;
import com.jd.open.api.sdk.response.etms.LdopReceiveTraceGetResponse;
import com.jiuji.cloud.logistics.enums.LogisticsTypeEnum;
import com.jiuji.cloud.logistics.vo.request.CancelOrderReq;
import com.jiuji.cloud.logistics.vo.request.CreateOrderByShop;
import com.jiuji.cloud.logistics.vo.request.CreateOrderReq;
import com.jiuji.cloud.logistics.vo.response.CreateOrderRes;
import com.jiuji.infra.common.utils.FieldCompareUtils;
import com.jiuji.oa.baozun.common.util.JsonUtil;
import com.jiuji.oa.logapi.pojo.entity.MkcLogNew;
import com.jiuji.oa.logapi.service.IMkcLogNewService;
import com.jiuji.oa.nc.abnormal.vo.ShowPrintingEnumVOV2;
import com.jiuji.oa.nc.common.bo.OaUserBO;
import com.jiuji.oa.nc.common.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.nc.common.config.rabbitmq.RabbitMqConfig;
import com.jiuji.oa.nc.common.constant.RedisKeys;
import com.jiuji.oa.nc.common.enums.XtenantEnum;
import com.jiuji.oa.nc.common.exception.CustomizeException;
import com.jiuji.oa.nc.common.exception.RRExceptionHandler;
import com.jiuji.oa.nc.common.req.OaAttachmentsAddOrUpdateReqVO;
import com.jiuji.oa.nc.common.util.NumUtil;
import com.jiuji.oa.nc.common.util.OaAuthUtil;
import com.jiuji.oa.nc.dict.enums.ConfigEnum;
import com.jiuji.oa.nc.dict.service.ISysConfigService;
import com.jiuji.oa.nc.dict.utils.ApplicationContextUtil;
import com.jiuji.oa.nc.oaapp.po.SysConfig;
import com.jiuji.oa.nc.stock.entity.ProductMkc;
import com.jiuji.oa.nc.stock.service.IProductMkcService;
import com.jiuji.oa.nc.stock.service.ISmsService;
import com.jiuji.oa.nc.user.po.Areainfo;
import com.jiuji.oa.nc.user.po.Ch999User;
import com.jiuji.oa.nc.user.po.DepartInfo;
import com.jiuji.oa.nc.user.service.Ch999UserService;
import com.jiuji.oa.nc.user.service.DepartInfoService;
import com.jiuji.oa.nc.user.service.IAreaInfoService;
import com.jiuji.oa.oacore.csharp.cloud.CsharpInWcfCloud;
import com.jiuji.oa.oacore.csharp.vo.req.AreaCourierReq;
import com.jiuji.oa.oacore.csharp.vo.res.AreaCourierRes;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.client.AreaListClient;
import com.jiuji.oa.orginfo.areainfo.vo.res.AreaListRes;
import com.jiuji.oa.orginfo.userinfo.client.UserInfoClient;
import com.jiuji.oa.orginfo.userinfo.vo.Ch999UserVo;
import com.jiuji.oa.stock.area.enums.DepartTypeEnum;
import com.jiuji.oa.stock.common.cache.CacheKey;
import com.jiuji.oa.stock.common.cache.RedisUtils;
import com.jiuji.oa.stock.common.constant.ModuleVersionKeys;
import com.jiuji.oa.stock.common.enums.YesOrNoEnum;
import com.jiuji.oa.stock.common.util.*;
import com.jiuji.oa.stock.common.vo.AddressToCoordinateVO;
import com.jiuji.oa.stock.logistics.order.enums.ExpressTypeEnum;
import com.jiuji.oa.stock.logistics.order.enums.WuliuStatusEnum;
import com.jiuji.oa.stock.logistics.order.service.AttachmentsService;
import com.jiuji.oa.stock.logistics.order.vo.CancelOrderDTO;
import com.jiuji.oa.stock.logistics.order.vo.req.SaveWuLiuLogReq;
import com.jiuji.oa.stock.logistics.order.vo.req.WuliuWayBillNoReq;
import com.jiuji.oa.stock.logistics.paotui.entity.WuliuPaijianInfoEntry;
import com.jiuji.oa.stock.logistics.paotui.service.IPaoTuiService;
import com.jiuji.oa.stock.logistics.paotui.service.IWuliuPaijianInfoService;
import com.jiuji.oa.stock.logistics.paotui.vo.WuLiuPaoTuiMqReq;
import com.jiuji.oa.stock.logistics.zhongtong.entity.WuliuZtoYuyue;
import com.jiuji.oa.stock.logistics.zhongtong.service.WuliuZtoYuyueService;
import com.jiuji.oa.stock.logisticscenter.enums.LogisticsExpressTypeEnum;
import com.jiuji.oa.stock.logisticscenter.serive.ICancelOrderService;
import com.jiuji.oa.stock.logisticscenter.serive.ILogisticsExpressService;
import com.jiuji.oa.stock.logisticscenter.utils.JiuJiApi;
import com.jiuji.oa.stock.logisticscenter.utils.JsonParseUtil;
import com.jiuji.oa.stock.logisticscenter.utils.LogisticsHttpClient;
import com.jiuji.oa.stock.logisticscenter.vo.LogisticsBase;
import com.jiuji.oa.stock.logisticscenter.vo.req.CancelOrderReqV2;
import com.jiuji.oa.stock.logisticscenter.vo.req.OaSignReq;
import com.jiuji.oa.stock.logisticscenter.vo.res.BigMarkInfoRes;
import com.jiuji.oa.stock.logisticscenter.vo.res.CreateOrderResV2;
import com.jiuji.oa.stock.nationalSupplement.res.NationalSupplementKindRes;
import com.jiuji.oa.stock.nationalSupplement.service.NationalSupplementService;
import com.jiuji.oa.stock.outputweb.service.OutPutWebService;
import com.jiuji.oa.stock.secretconfig.service.SecretCodeConfigService;
import com.jiuji.oa.stock.shouhou.enums.OrderSubTypeEnum;
import com.jiuji.oa.stock.sub.entity.Sub;
import com.jiuji.oa.stock.sub.service.ISubService;
import com.jiuji.oa.wuliu.bo.*;
import com.jiuji.oa.wuliu.constant.DadaAppConstant;
import com.jiuji.oa.wuliu.constant.DadaUrlConstant;
import com.jiuji.oa.wuliu.constant.WuLiuConstant;
import com.jiuji.oa.wuliu.constant.WuliuExpressConstant;
import com.jiuji.oa.wuliu.dada.DadaApiResponse;
import com.jiuji.oa.wuliu.dada.DadaRequestClient;
import com.jiuji.oa.wuliu.dto.req.SubPositionReq;
import com.jiuji.oa.wuliu.dto.res.LogisticsMessage;
import com.jiuji.oa.wuliu.entity.*;
import com.jiuji.oa.wuliu.enums.*;
import com.jiuji.oa.wuliu.mapper.ShunfengCustidConfigMapper;
import com.jiuji.oa.wuliu.mapper.WuLiuMapper;
import com.jiuji.oa.wuliu.mapstruct.WuLiuMapStruct;
import com.jiuji.oa.wuliu.service.*;
import com.jiuji.oa.wuliu.utils.*;
import com.jiuji.oa.wuliu.vo.*;
import com.jiuji.oa.wuliu.vo.express.SfCancelOrderParamDTO;
import com.jiuji.oa.wuliu.vo.express.res.SfCancelOrderResultRes;
import com.jiuji.oa.wuliu.vo.req.*;
import com.jiuji.oa.wuliu.vo.res.*;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.foundation.rabbitmq.config.MultipleRabbitProperties;
import com.jiuji.tc.foundation.rabbitmq.config.RabbitFactory;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.common.build.LambdaBuild;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.enums.EnumUtil;
import com.jiuji.tc.utils.enums.EnumVO;
import com.jiuji.tc.utils.enums.order.ProcessBusinessTypeEnum;
import com.jiuji.tc.utils.enums.order.SubDynamicsBusinessNodeEnum;
import com.zto.zop.ZopClient;
import com.zto.zop.ZopProperties;
import com.zto.zop.ZopPublicRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Attribute;
import org.dom4j.Document;
import org.dom4j.Element;
import org.springframework.amqp.AmqpException;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.RabbitListenerContainerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.MessageFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 物流单ServiceImpl
 *
 * <AUTHOR>
 * @date 2021-05-17 11:24:40
 */
@Service
@RequiredArgsConstructor
@Slf4j
@DS("oanewWrite")
public class WuLiuServiceImpl extends ServiceImpl<WuLiuMapper, WuLiuEntity> implements IWuLiuService {

    /********************** 九机云南中通配置***************************/
    public static final String ZTO_COMPANY_ID = "02ce66d8979744528b75580509451a5d";
    public static final String ZTO_USER = "1000231431";
    public static final String ZTO_KEY = "5f6299b18e45";
    public static final String ZTO_PWD = "14F4HYZD5H";
    private static final String JD_EXPRESS_ITEM_NAME = "电子产品";
    private static final int SECRET_BY_CODE = 15;
    public static Object authConfigObj = new Object();
    public static Object allDcAreaIdObj = new Object();
    private final ExpressTypeServiceImpl expressTypeService;
    private final IWuLiuAuthorizeService wuLiuAuthorizeService;
    private final IAreaInfoService areainfoService;
    private final IWuLiuSubSeparateConfigService wuLiuSubSeparateConfigService;
    private final IWuLiuLogService wuLiuLogService;
    private final AreaInfoClient areaInfoClient;
    private final MongoTemplate mongoTemplate;
    private final IWuLiuSubAddressService wuLiuSubAddressService;
    private final AttachmentsService attachmentsService;
    private final IWuLiuSubService wuLiuSubService;
    private final ISubService subService;
    private final IWuLiuRecoverMarketInfoService wuLiuRecoverMarketInfoService;
    private final IWuLiuShouHouService wuLiuShouHouService;
    private final IWuLiuZiTiDianService wuLiuZiTiDianService;
    private final Ch999UserService ch999UserService;
    private final AreaListClient areaListClient;
    private final IShouHouYuYueService shouHouYuYueService;
    private final IWuLiuRecoverSubService wuLiuRecoverSubService;
    private final IWuLiuSmallProService wuLiuSmallProService;
    private final IWuLiuBasketService wuLiuBasketService;
    private final IWuLiuSecretCodeConfigService wuLiuSecretCodeConfigService;
    private final SecretCodeConfigService secretCodeConfigService;
    private final UserInfoClient userInfoClient;
    private final IWuLiuExpressExtendService wuLiuExpressExtendService;
    private final IWuLiuPriceService wuLiuPriceService;
    private final IWuLiuWuliuwangdianService wuLiuWuliuwangdianService;
    private final AbstractCurrentRequestComponent currentRequestComponent;
    private final ISysConfigService sysConfigService;
    private final IMkcLogNewService mkcLogNewService;
    private final IAreaInfoService areaInfoService;
    private final IMarkAbnomalWuLiuRecordService markAbnomalWuLiuRecordService;
    private final IWuLiuShunfengNoInfoService wuLiuShunfengNoInfoService;
    private final IShansongStoreConfigsService shansongStoreConfigsService;
    private final StringRedisTemplate stringRedisTemplate;
    private final IShansongService shansongService;
    private final IProductMkcService productMkcService;
    private final ICancelOrderService cancelOrderService;
    private final IShansongAppConfigsService shansongAppConfigsService;
    private final OutPutWebService outPutWebService;
    private final DepartInfoService departInfoService;
    private final AreaassetsubService areaassetsubService;
    private final WuliuZtoYuyueService wuliuZtoYuyueService;
    private final IWuLiuWuliuIsvstoreService wuLiuWuliuIsvstoreService;
    private final ShunfengCustidConfigMapper shunfengCustidConfigMapper;
    private final ILogisticsExpressService logisticsExpressService;
    private final ZtoBillInfoService ztoBillInfoService;
    private final IExpressEnumService expressEnumService;
    private final IWuLiuCategoryService wuLiuCategoryService;
    private final IWuliuExpressNuService wuliuExpressNuService;
    private final IWuliunoexService wuliunoexService;
    @Resource
    @Lazy
    private IWuLiuService wuLiuService;

    @Override
    @DS("ch999oanew")
    public List<LogisticsMessage> getLogisticsMessage(LogisticsMessageVo logisticsMessageVo) {
        if (StringUtils.isBlank(logisticsMessageVo.getLogisticsOrderNo())) {
            throw new CustomizeException("快递单号不能为空");
        }
        LambdaQueryChainWrapper<WuLiuEntity> select = this.lambdaQuery().eq(WuLiuEntity::getNu, logisticsMessageVo.getLogisticsOrderNo())
                .likeRight(WuLiuEntity::getCom, logisticsMessageVo.getExpress())
                .select(WuLiuEntity::getId, WuLiuEntity::getCom, WuLiuEntity::getNu, WuLiuEntity::getWuType, WuLiuEntity::getWCateId);
        //判断是否要查拍靓机的数据
        Boolean aBoolean = Optional.ofNullable(logisticsMessageVo.getIsPaiLiangJi()).orElse(Boolean.FALSE);
        if(aBoolean){
            //判断是否为九机
            Integer xtenant = logisticsMessageVo.getXtenant();
            if(xtenant==0){
                select.eq(WuLiuEntity::getWCateId, WuLiuCateEnum.PAILIANGJI_PAISONG.getCode());
            }
        }
        List<WuLiuEntity> list = select.list();
        if(CollectionUtils.isNotEmpty(list)){
            return list.stream().map((WuLiuEntity item) -> {
                LogisticsMessage logisticsMessage = new LogisticsMessage();
                logisticsMessage.setCom(item.getCom()).setWutype(item.getWuType())
                        .setWuCateId(item.getWCateId())
                        .setNu(item.getNu()).setId(item.getId());
                return logisticsMessage;
            }).collect(Collectors.toList());
        } else {
            return new ArrayList<>();
        }

    }

    /**
     * 达达接口调用环境 true正式环境，false：测试环境
     */
    private Boolean dadaIsOnline = false;
    @Autowired
    @Qualifier("oaAsyncRabbitTemplate")
    private RabbitTemplate rabbitTemplate;
    private final ThirdPlatformOrderServiceImpl thirdPlatformOrderService;

    /**
     * Fun.getNumCode
     * 获取固定位数随机码 纯数字
     *
     * @param length int
     * @return String
     * @date 2021-10-13
     * <AUTHOR> [<EMAIL>]
     */
    public static String getNumCode(int length) {
        StringBuilder result = new StringBuilder();
        // 字符列表
        String[] strArray = {"0", "1", "2", "3", "4", "5", "6", "7", "8", "9"};
        ThreadLocalRandom randNum = ThreadLocalRandom.current();
        for (int i = 0; i < length; i++) {
            result.append(strArray[randNum.nextInt(9)]);
        }
        return result.toString();
    }

    /**
     * zhongtongApiServices.PostDate2
     * 发送数据获取返回值
     *
     * @param url        String
     * @param postValues Map<String, Object>
     * @return String
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-03
     */
    public static String postDate2(String url, Map<String, Object> postValues) {
        String returns;
        try {
            returns = HttpRequest.post(url).form(postValues).execute().body();
        } catch (Exception e) {
            log.error("WuLiuServiceImpl.postDate2 报错: {}", Exceptions.getStackTraceAsString(e), e);
            returns = (e.getMessage());
        }
        return returns;
    }

    /**
     * shunfengApiServices.MD5ToBase64String
     * 加密
     *
     * @param str 需加密的字符串
     * @return string 加密后的字符
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-03
     */
    public static String shunfengMd5ToBase64String(String str) {
        try {
            return md5DigestToBase64(str);
        } catch (NoSuchAlgorithmException e) {
            log.error("WuLiuServiceImpl.shunfengMd5ToBase64String 报错={}", Exceptions.getStackTraceAsString(e), e);
            return "";
        }
    }

    /**
     * md5DigestToBase64
     *
     * @param str String
     * @return String
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-16
     */
    public static String md5DigestToBase64(String str) throws NoSuchAlgorithmException {
        MessageDigest md = MessageDigest.getInstance("MD5");
        byte[] bytes = str.getBytes(StandardCharsets.UTF_8);
        return Base64.getEncoder().encodeToString(md.digest(bytes));
    }

    /**
     * shunfengApiServices.GetWebXml
     * 报文拼装
     * 下单报文
     *
     * @param item       SfOrderInfoVO
     * @param clientCode String
     * @return string
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-03
     */
    public static String shunfengGetWebXml(SfOrderInfoVO item, String clientCode) {
        return "<Request service='OrderService' lang='zh-CN'>" +//申请服务及语言
                shunfengGetHead(clientCode) + //客户编码 V3.2
                "<Body><Order  " + shunfengGetSendMessage(item.getJCompany(), item.getJContact(), item.getJTel(), item.getJMobile(), item.getJProvince(), item.getJCity(), item.getJCounty(), item.getJAddress()) + " "
                + shunfengGetReceiveMessage(item.getDCompany(), item.getDContact(), item.getDTel(), item.getDMobile(), item.getDProvince(), item.getDCity(), item.getDCounty(), item.getDAddress()) + " "
                + shunfengGetOrderMessage(item.getOrderId(), shunfengExpressTypeMapping(OptionalUtils.ifNotNull(item.getExpressType(), Integer::valueOf)), item.getPayMethod(), String.valueOf(Optional.ofNullable(item.getParcelQuantity()).orElse(0)), item.getCustId(), Optional.ofNullable(item.getCargoTotalWeight()).orElse("0"), item.getSendStartTime(), item.getOrderSource(), item.getRemark(), String.valueOf(item.getHhtWayDil()), String.valueOf(item.getDoCall())) + ">" +
                shunfengGetCargoMessage(item.getList()) +
                "</Order></Body></Request>";

    }

    /**
     * shunfengApiServices.GetCargoMessage
     * <p>
     * 获取货品信息
     * CargoName 商品名称
     * count 商品数量
     * unit 单位
     * weight 单重
     * amount 单价
     * currency 结算货币（国际件用）
     * source_area 原产地（国际件用）
     *
     * @param list
     * @return string 货品信息
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-03
     */
    public static String shunfengGetCargoMessage(List<SfCargoVO> list) {
        StringBuilder strXml = new StringBuilder();
        for (SfCargoVO item : list) {
            strXml.append(" <Cargo    name='").append(item.getCargoName())
                    .append("' count='").append(item.getCount())
                    .append("' unit='").append(item.getUnit())
                    .append("' weight='").append(Optional.ofNullable(item.getWeight()).orElse("0"))
                    .append("' amount='").append(item.getAmount())
                    .append("' currency='").append(item.getCurrency())
                    .append("' source_area='").append(item.getSourceArea())
                    .append("'   ></Cargo> ");
        }

        return strXml.toString();
    }

    /**
     * shunfengApiServices.ExpressTypeMapping
     * 处理同一个值在OA里对应多个名称的特殊情况
     *
     * @param type int
     * @return int
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-03
     */
    private static int shunfengExpressTypeMapping(int type) {
        if (type < 10000) {
            return type;
        } else {
            return type - 10000;
        }
    }

    /**
     * shunfengApiServices.GetOrderMessage
     * 获取订单信息
     *
     * @param orderid          订单号
     * @param expressType      业务类型
     * @param payMethod        支付方式
     * @param parcelQuantity   包裹数
     * @param custid           月结卡号
     * @param cargoTotalWeight 快件总重量
     * @param sendstarttime    发货时间
     * @param orderSource      订单来源
     * @param remark           备注信息
     * @param hhtwaydil        String
     * @param docall           String
     * @return String 获取订单信息
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-03
     */
    public static String shunfengGetOrderMessage(String orderid, Integer expressType, Integer payMethod, String parcelQuantity, String custid, String cargoTotalWeight, String sendstarttime, String orderSource, String remark, String hhtwaydil, String docall) {
        String hhtwaydil2 = Optional.ofNullable(hhtwaydil).orElse("");
        String docall2 = Optional.ofNullable(docall).orElse("");

        StringBuilder strXml = new StringBuilder();
        strXml.append("orderid ='").append(orderid).append("' ")
                .append("express_type ='").append(expressType).append("' ")
                .append("pay_method ='").append(payMethod).append("' ")
                .append("parcel_quantity ='").append(parcelQuantity).append("' ")
                .append("custid ='").append(custid).append("' ")
                .append("cargo_total_weight ='").append(cargoTotalWeight)
                .append("' ").append("sendstarttime ='").append(sendstarttime).append("' ")
                .append("order_source ='").append(Optional.ofNullable(orderSource).orElse("")).append("' ")
                .append("remark ='").append(remark).append("' ");

        if (WuLiuConstant.ONE_STR.equals(hhtwaydil2)) {
            strXml.append("HHTwaydil ='").append(hhtwaydil2).append("' ");
        } else {
            strXml.append("HHTwaydil ='0' ");
        }

        if (WuLiuConstant.ONE_STR.equals(docall2)) {
            strXml.append("is_docall ='").append(docall2).append("' ");
        } else {
            strXml.append("is_docall ='0' ");
        }

        strXml.append("routelabelService='1'");

        return strXml.toString();
    }

    /**
     * shunfengApiServices.GetReceiveMessage
     * 获取收件信息
     *
     * @param dCompany  收件公司
     * @param dContact  联系人
     * @param dTel      联系电话
     * @param dMobile   手机号码
     * @param dProvince 省
     * @param dCity     市
     * @param dCounty   区/县
     * @param dAddress  详细地址
     * @return String 收件信息
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-03
     */
    public static String shunfengGetReceiveMessage(String dCompany, String dContact, String dTel, String dMobile, String dProvince, String dCity, String dCounty, String dAddress) {
        return " d_company='" + Optional.ofNullable(dCompany).orElse("")
                + "' " + "d_contact='" + Optional.ofNullable(dContact).orElse("") + "' "
                + "d_tel='" + Optional.ofNullable(dTel).orElse("") + "' "
                + "d_mobile='" + Optional.ofNullable(dMobile).orElse("") + "' "
                + "d_province='" + Optional.ofNullable(dProvince).orElse("") + "' "
                + "d_city='" + Optional.ofNullable(dCity).orElse("") + "' "
                + "d_county='" + Optional.ofNullable(dCounty).orElse("") + "' "
                + "d_address='" + Optional.ofNullable(dAddress).orElse("") + "' ";
    }

    /**
     * shunfengApiServices.GetSendMessage
     * 获取寄件信息
     *
     * @param jCompany  寄件公司
     * @param jContact  联系人
     * @param jTel      联系电话
     * @param jMobile   联系手机
     * @param jProvince 省
     * @param jCity     市
     * @param jCounty   区/县
     * @param jAddress  详细地址
     * @return String 寄件信息
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-03
     */
    public static String shunfengGetSendMessage(String jCompany, String jContact, String jTel, String jMobile, String jProvince, String jCity, String jCounty, String jAddress) {
        return " j_company='" + Optional.ofNullable(jCompany).orElse("") + "' " +
                "j_contact='" + Optional.ofNullable(jContact).orElse("") + "' " +
                "j_tel='" + Optional.ofNullable(jTel).orElse("") + "' " +
                "j_mobile='" + Optional.ofNullable(jMobile).orElse("") + "' " +
                "j_province='" + Optional.ofNullable(jProvince).orElse("") + "' " +
                "j_city='" + Optional.ofNullable(jCity).orElse("") + "' " +
                "j_county='" + Optional.ofNullable(jCounty).orElse("") + "' " +
                "j_address='" + Optional.ofNullable(jAddress).orElse("") + "' ";

    }

    /**
     * shunfengApiServices.GetHead
     * 获取头文件（WebService）
     *
     * @param clientNo 接口编码或客户编码
     * @return string 报头文件
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-03
     */
    public static String shunfengGetHead(String clientNo) {
        return "<Head>" + clientNo + "</Head>";
    }

    /**
     * meituanServices.getMeiTuanSign
     *
     * @param conn MeituanItemDTO
     * @return String
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-15
     */
    public static String getMeiTuanSign(MeituanItemDTO conn) {
        Map<String, Object> stringObjectMap = JacksonJsonUtils.toMap(conn, String.class, Object.class);
        Set<String> keySet = stringObjectMap.keySet();
        ArrayList<String> keyList = new ArrayList<>(keySet);
        Collections.sort(keyList);

        StringBuilder str = new StringBuilder();

        for (String key : keyList) {
            if ("sign".equals(key)) {
                continue;
            }

            Object value = stringObjectMap.get(key);

            if (value == null) {
                continue;
            }

            if (value.getClass().isArray() && byte.class.isAssignableFrom(value.getClass().getComponentType())) {
                continue;
            }

            String valueString = value.toString();

            if (StringUtils.isEmpty(valueString)) {
                continue;
            }
            str.append(key).append(value);
        }

        String secret = SysUtils.isJiuJiProd()
                ? "zcY_w]T}%?0C5Yx$)k}R4gyKpI7d54!+/QXlF2w@)y+{Li-&piIt,>vL^dDxX170"
                : "u1SpL{_8.!PTYCXw@1moQ$JtR#7.;n{dzykcO*3+7rT;4[WLEw88N*Bd${33@|[h";
        str.insert(0, secret);
        return DigestUtils.sha1Hex(str.toString());
    }

    /**
     * meituanServices.getMeiTuanSign
     *
     * @param conn MeituanItemDTO
     * @return String
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-15
     */
    public static String getMeiTuanSign(QueryItemVO conn) {
        Map<String, Object> stringObjectMap = JacksonJsonUtils.toMap(conn, String.class, Object.class);
        Set<String> keySet = stringObjectMap.keySet();
        ArrayList<String> keyList = new ArrayList<>(keySet);
        Collections.sort(keyList);

        StringBuilder str = new StringBuilder();

        for (String key : keyList) {
            if ("sign".equals(key)) {
                continue;
            }

            Object value = stringObjectMap.get(key);

            if (value == null) {
                continue;
            }

            if (value.getClass().isArray() && byte.class.isAssignableFrom(value.getClass().getComponentType())) {
                continue;
            }

            String valueString = value.toString();

            if (StringUtils.isEmpty(valueString)) {
                continue;
            }
            str.append(key).append(value);
        }

        String secret = SysUtils.isJiuJiProd()
                ? "zcY_w]T}%?0C5Yx$)k}R4gyKpI7d54!+/QXlF2w@)y+{Li-&piIt,>vL^dDxX170"
                : "u1SpL{_8.!PTYCXw@1moQ$JtR#7.;n{dzykcO*3+7rT;4[WLEw88N*Bd${33@|[h";
        str.insert(0, secret);
        return DigestUtils.sha1Hex(str.toString());
    }

    /**
     * 订单预约通知
     *
     * @param companyid String
     * @param key       String
     * @param data      OrderGroupDTO
     * @param url       String
     * @return ZtoResultDTO
     */
    public static ZtoResultDTO orderYuyue(String companyid, String key, OrderGroupDTO data, String url) {
        ZtoResultDTO redata = null;
        ZopProperties property = new ZopProperties(companyid, key);
        ZopClient client = new ZopClient(property);
        ZopPublicRequest request = new ZopPublicRequest();
        request.setUrl(url);
        request.addParam("orderGroup", JSONUtil.toJsonStr(data));

        try {
            String rValue = client.execute(request);
            redata = JSONUtil.toBean(rValue, ZtoResultDTO.class);
        } catch (IOException e) {
            log.error("订阅错误,无法解析Json数据：{}", Exceptions.getStackTraceAsString(e), e);
            return redata;
        }
        return redata;
    }

    /**
     * 根据平台 租户 获取默认短信通道
     *
     * @param xtenant Integer
     * @param type    Integer
     * @return int
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-08
     */
    public static Integer getSmsChannelByXtenant(Integer xtenant, Integer type) {
        if (xtenant == null) {
            return null;
        }

        int type2 = Optional.ofNullable(type).orElse(1);
        int result = 9;
        if (type2 == 1) {
            switch (xtenant) {
                case 0:
                    result = 9;
                    break;
                case 1:
                    result = 23;
                    break;
                case 2:
                    result = 27;
                    break;
                case 3:
                    result = 37;
                    break;
                case 4:
                    result = 39;
                    break;
                case 8:
                    result = 47;
                    break;
                case 9:
                    result = 49;
                    break;
                case 7:
                    result = 51;
                    break;
                case 6:
                    result = 53;
                    break;
                case 10:
                    result = 55;
                    break;
                case 11:
                    result = 57;
                    break;
                default:
                    String channle = SysConfigUtils.getValue(SysConfigConstant.VERIFICATION_CODE_CHANNEL);
                    result = channle == null ? 9 : Integer.parseInt(channle);
                    break;
            }
        } else if (type2 == NumUtil.TWO) {
            switch (xtenant) {
                case 0:
                    result = 18;
                    break;
                case 1:
                    result = 24;
                    break;
                case 2:
                    result = 27;
                    break;
                case 3:
                    result = 38;
                    break;
                case 4:
                    result = 40;
                    break;
                case 8:
                    result = 48;
                    break;
                case 9:
                    result = 50;
                    break;
                case 7:
                    result = 52;
                    break;
                case 6:
                    result = 54;
                    break;
                case 10:
                    result = 56;
                    break;
                case 11:
                    result = 58;
                    break;
                default:
                    String channle = SysConfigUtils.getValue(SysConfigConstant.MARKETING_CHANNEL);
                    result = channle == null ? 18 : Integer.parseInt(channle);
                    break;
            }
        }

        return result;
    }

    /**
     * 中通物流单订阅
     * oa999DAL.zhongtongApiServices.SubOrderInfo
     *
     * @param compayId  String
     * @param cKey      String
     * @param billCodes List<String>
     * @return String
     */
    public static String subOrderInfo(String compayId, String cKey, List<String> billCodes) {
        if (billCodes.isEmpty()) {
            return "";
        }

        try {
            ZopProperties property = new ZopProperties(compayId, cKey);
            ZopClient client = new ZopClient(property);
            ZopPublicRequest request = new ZopPublicRequest();
            request.setUrl("http://japi.zto.cn/subscribeData");

            String[] actionArray = {"1", "4", "5", "6", "7", "8", "44", "45"};
            List<String> action = Arrays.asList(actionArray);

            request.addParam("billCode", JsonUtil.object2JsonNonNull(billCodes));
            request.addParam("action", JsonUtil.object2JsonNonNull(action));
            request.addParam("problemCode", "");
            request.addParam("pushUrl", "https://www.9ji.com/webview/routepush.aspx?com=zhongtong");
            request.addParam("token", "TWAYYEES");

            try {
                String rValue = client.execute(request);
                JSONObject jsonObject = JSONUtil.parseObj(rValue);
                if (Boolean.TRUE.equals(jsonObject.get("status"))) {
                    return "1";
                } else {
                    return jsonObject.getStr("errorMsg");
                }
            } catch (IOException e) {
                log.error("订阅错误,无法解析Json数据: {}", Exceptions.getStackTraceAsString(e), e);
                return "订阅错误,无法解析Json数据";
            }
        } catch (Exception e) {
            log.error("订阅错误: {}", Exceptions.getStackTraceAsString(e), e);
            return "订阅错误：" + e.getMessage();
        }
    }

    /**
     * georegeoApi.translate
     * 高德坐标转换是一类简单的HTTP接口，能够将用户输入的非高德坐标（GPS坐标、mapbar坐标、baidu坐标）转换成高德坐标
     *
     * @param location String
     * @return Map<Double, Double>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-10-27
     */
    private static Map<Double, Double> translate(String location) {
        Map<Double, Double> result = new HashMap<>(NumUtil.THREE);
        String geoKey = "b13d162fd50ec0329eb1754705042bcd";
        String apiAddr = "/assistant/coordinate/convert";
        String geoBaseUrl = "https://restapi.amap.com/v3";
        Map<String, String> param = new HashMap<>(NumUtil.THREE);
        param.put("key", geoKey);
        param.put("locations", location);
        param.put("coordsys", "gps");
        StringBuilder paramString = new StringBuilder();
        //https://restapi.amap.com/v3/assistant/coordinate/convert?key=b13d162fd50ec0329eb1754705042bcd&coordsys=gps&locations=116.481499,39.990475|116.481499,39.990375
        param.forEach((key, value) -> {
            paramString.append("&");
            paramString.append(key);
            paramString.append("=");
            paramString.append(value);
        });
        String url = geoBaseUrl + apiAddr + "?" + paramString;
        String reqResult = HttpUtil.get(url);
        JSONObject reqResultJson;
        try {
            reqResultJson = JSONUtil.parseObj(reqResult);
            if (null != reqResultJson.get("status") && "1".equals(reqResultJson.get("status"))) {
                String locationsString = reqResultJson.get("locations").toString();
                String[] split = locationsString.split(StrPool.COMMA);
                result.put(Double.valueOf(split[0]), Double.valueOf(split[1]));
            }
        } catch (JSONException e) {
            log.error("高德坐标转换返回数据：{}", reqResult);
            log.error("解析高德坐标转换数据异常：{}", Exceptions.getStackTraceAsString(e));
            return result;
        }
        return result;
    }

    /***
     * georegeoApi.translateToLocation
     * 高德地理编码 API 是通过 HTTP/HTTPS 协议访问远程服务的接口，提供结构化地址与经纬度之间的相互转化的能力。
     * @param address String
     * @return Map<Double, Double>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-10-27
     */
    private static Map<Double, Double> translateToLocation(String address) {
        Map<Double, Double> result = new HashMap<>(NumUtil.TWO);
        String geoKey = "b13d162fd50ec0329eb1754705042bcd";
        String apiAddr = "/geocode/geo";
        String geoBaseUrl = "https://restapi.amap.com/v3";
        Map<String, String> param = new HashMap<>();
        param.put("key", geoKey);
        param.put("address", address);
        StringBuilder paramString = new StringBuilder();
        // https://restapi.amap.com/v3/geocode/geo?key=b13d162fd50ec0329eb1754705042bcd&address=%E5%8C%97%E4%BA%AC%E5%B8%82%E6%9C%9D%E9%98%B3%E5%8C%BA%E9%98%9C%E9%80%9A%E4%B8%9C%E5%A4%A7%E8%A1%976%E5%8F%B7
        param.forEach((key, value) -> {
            paramString.append("&");
            paramString.append(key);
            paramString.append("=");
            paramString.append(value);
        });
        String url = geoBaseUrl + apiAddr + "?" + paramString;
        String reqResult = HttpUtil.get(url);
        JSONObject reqResultJson;
        try {
            reqResultJson = JSONUtil.parseObj(reqResult);
            if (null != reqResultJson.get("status") && "1".equals(reqResultJson.get("status"))) {
                JSONArray geocodes = reqResultJson.getJSONArray("geocodes");
                String locationsString = geocodes.getJSONObject(0).get("location").toString();
                String[] split = locationsString.split(StrPool.COMMA);
                result.put(Double.valueOf(split[0]), Double.valueOf(split[1]));
            }
        } catch (JSONException e) {
            log.error("高德地理编码返回数据：{}", reqResult);
            log.error("解析高德地理编码数据异常：{}", Exceptions.getStackTraceAsString(e));
            return result;
        }
        return result;
    }

    @Override
    public R<String> generateMoreWuLiuNoV1(OaUserBO currentUser, WuLiuGenerateMoreWuLiuNoReqVO model) {
        int eCount = Optional.ofNullable(model.getECount()).orElse(0);
        R<String> json = new R<>();
        AreaSubjectVO areaSubject = getAreaSubject(model.getSareaid());
        Areainfo areainfo = Optional.ofNullable(areainfoService.getAreaInfoByAreaId2(model.getSareaid())).orElseGet(Areainfo::new);
        if (WuLiuConstant.ZHONGTONG.equals(model.getCom()) && StringUtils.isNotBlank(model.getNu())) {
            if (Objects.equals(model.getSareaid(), WuLiuConstant.AREA_DC)) {
                model.setSName(areaSubject.getPrintName());
                model.setSMobile(WuLiuConstant.DC_S_MOBLIE);
            }

            if (StringUtils.isBlank(model.getSMobile()) && Objects.equals(model.getWuType(), 4)) {
                model.setSName(areaSubject.getPrintName());
                model.setSMobile(areainfo.getCompanyTel1());
            }

            OrderGroupDTO conmodel = new OrderGroupDTO();
            conmodel.setId(model.getDanHaoBind() + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));//订单号
            if (Objects.equals(model.getWuType(), 1)) {
                conmodel.setId(model.getWuliuid() + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
            }

            conmodel.setRemark("请勿摔打,保持整洁。");//订单备注

            //寄件人信息
            CityIdListDTO jinfo;
            //上门取件  其他派送
            if (Arrays.asList(NumUtil.SEVEN, NumUtil.EIGHT).contains(model.getWuType())) {
                jinfo = getAreaIdByCityId(model.getSDid(), null);
                jinfo.setAddress(model.getSAddress());
            } else {
                jinfo = getAreaInfoByArea(model.getSareaid());
            }
            conmodel.getSender().setName(model.getSName() + getWuliuStatsArea(model.getWuType(), model.getSareaid(), 0));//发货人姓名  *
            conmodel.getSender().setCompany(areaSubject.getPrintName());//发货人所在公司
            conmodel.getSender().setMobile(model.getSMobile());//发货人手机号   *
            conmodel.getSender().setCity(jinfo.getPname() + "," + jinfo.getZname() + "," + jinfo.getDname());//发货人所在城市  *
            conmodel.getSender().setAddress(jinfo.getAddress());//发货人地址   *

            //收件人信息
            CityIdListDTO cittInfo;
            if (Objects.equals(model.getWuType(), 1)) {
                cittInfo = getAreaDetailInfoByArea(model.getRareaid());
            } else {
                cittInfo = getAreaDetailIdByCityId(Objects.equals(model.getRCityId(), 0) ? model.getRDid() : model.getRCityId());
            }
            conmodel.getReceiver().setName(model.getRName() + getWuliuStatsArea(model.getWuType(), model.getRareaid(), 1));//收件人
            conmodel.getReceiver().setMobile(model.getRMobile());
            conmodel.getReceiver().setCity(cittInfo.getPname() + "," + cittInfo.getZname() + "," + cittInfo.getDname());//收件人所在地 *
            conmodel.getReceiver().setAddress(model.getRAddress());//详细地址    *

            if (Objects.equals(model.getWuType(), 1) && StringUtils.isBlank(model.getRAddress())) {
                conmodel.getReceiver().setAddress(cittInfo.getAddress());
            }
            //商品信息
            //根据订单获取bakset
            List<WuLiuBasket2Entity> subBasket = wuLiuBasketService.getSubBasket(model.getDanHaoBind(), null, null);

            BigDecimal totlaCount = BigDecimal.ZERO;
            for (WuLiuBasket2Entity dr : subBasket) {
                totlaCount = totlaCount.add(dr.getPrice().multiply(new BigDecimal(dr.getBasketCount())));
            }

            conmodel.setOrderSum(totlaCount);//订单总金额
            String jsonStr = "";
            try {
                if (eCount > 0) {
                    for (int i = 0; i < eCount; i++) {
                        conmodel.setId(conmodel.getId() + 1);

                        OrderGroupResultDTO data;

                        //新接口，暂时不启用
                        boolean isNewPort = false;
                        if (isNewPort) {
                            data = createOrderGroupNew(conmodel, model.getSareaid());
                        } else {
                            jsonStr = createOrderGroup(conmodel, model.getAreaId());
                            data = JSON.parseObject(jsonStr, OrderGroupResultDTO.class);
                        }

                        if (Boolean.TRUE.equals(data.getResult())) {
                            //快递单号
                            model.setNu(data.getData().getBillCode());
                        }
                    }
                    json.setCode(1);
                    json.setMsg("生成成功");
                } else {
                    json.setCode(0);
                    json.setMsg("生成数量为0");
                }
            } catch (Exception ex) {
                json.setCode(0);
                if (jsonStr.contains(":") && (jsonStr.length() > jsonStr.lastIndexOf(":") + 2)) {
                    String tmsg = jsonStr.substring(jsonStr.lastIndexOf(":") + 2);
                    json.setMsg(tmsg.substring(0, tmsg.length() - 2));
                } else {
                    json.setMsg(ex.getMessage());
                }
            }
        } else if (WuLiuConstant.SHUNFENG.equals(model.getCom()) && StringUtils.isNotBlank(model.getNu())) {
            try {
                json.setCode(1);
            } catch (Exception e) {
                json.setCode(0);
                json.setMsg(e.getMessage());
            }
        } else if (WuLiuConstant.JINGDONG_JIUJI.equals(model.getCom()) && StringUtils.isNotBlank(model.getNu())) {
            try {
                json.setCode(1);
                json.setMsg(generateMoreNuJd(model));
            } catch (Exception e) {
                json.setCode(0);
                json.setMsg(e.getMessage());
            }
        }

        // EMS 快递单生产接入 开始
        else if (WuLiuConstant.EMS.equals(model.getCom()) && StringUtils.isNotBlank(model.getNu())) {
            boolean isHq = getAllAreaHqDcH1D1().contains(model.getSareaid());
            if (!isHq) {
                json.setCode(0);
                json.setMsg("不可生成EMS快递单号");
                return json;
            }

            if (Objects.equals(model.getSareaid(), WuLiuConstant.AREA_DC)) {
                model.setSName(areaSubject.getPrintName());
                model.setSMobile(WuLiuConstant.DC_S_MOBLIE);
            }

            if (StringUtils.isBlank(model.getSMobile()) && Objects.equals(model.getWuType(), 4)) {
                model.setSName(areaSubject.getPrintName());
                model.setSMobile(areainfo.getCompanyTel1());
            }

            //寄件人信息
            CityIdListDTO jinfo;
            //上门取件  其他派送
            if (Arrays.asList(NumUtil.SEVEN, NumUtil.EIGHT).contains(model.getWuType())) {
                jinfo = getAreaIdByCityId(model.getSDid(), null);
            } else {
                jinfo = getAreaInfoByArea(model.getSareaid());
            }

            AddressDTO sender = new AddressDTO();
            sender.setName(model.getSName());
            sender.setMobile(model.getSMobile());
            sender.setProv(jinfo.getPname());
            sender.setCity(jinfo.getZname());
            sender.setCounty(jinfo.getDname());
            sender.setAddress(jinfo.getAddress());

            if (StringUtils.isBlank(sender.getAddress())) {
                sender.setAddress(model.getSAddress());
            }

            //收件人信息
            CityIdListDTO cittInfo;
            if (Objects.equals(model.getWuType(), 1)) {
                cittInfo = getAreaDetailInfoByArea(model.getRareaid());
            } else {
                cittInfo = getAreaDetailIdByCityId(Objects.equals(model.getRCityId(), 0) ? model.getRDid() : model.getRCityId());
            }

            if (Objects.equals(model.getWuType(), 1)) {
                model.setRAddress(cittInfo.getAddress());
            }
            AddressDTO receiver = new AddressDTO();
            receiver.setName(model.getRName());
            receiver.setMobile(model.getRMobile());
            receiver.setProv(cittInfo.getPname());
            receiver.setCity(cittInfo.getZname());
            receiver.setCity(cittInfo.getDname());
            receiver.setAddress(model.getRAddress());


            String orderId = model.getWuliuid() + "_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyMMddHHmmss"));


            if (Objects.equals(model.getWuType(), 8) && Objects.equals(model.getDanHaoBind(), 0)) {
                orderId = String.valueOf(model.getWuliuid());
            }

            OrderInfoDTO orderInfo = new OrderInfoDTO();
            orderInfo.setOrderId(orderId);
            orderInfo.setSender(sender);
            orderInfo.setReceiver(receiver);

            if (eCount > 0) {
                for (int i = 0; i < eCount; i++) {
                    orderInfo.setOrderId(orderInfo.getOrderId() + "-" + i);
                    OrderResultDTO emsResult = emsCreateOrder(orderInfo);
                    if (Objects.equals(emsResult.getCode(), 0)) {
                        model.setNu(emsResult.getLastOrderid());
                    }
                }
                json.setCode(1);
                json.setMsg("生成成功");
            } else {
                json.setCode(0);
                json.setMsg("生成数量为0");
            }
        }
        // EMS 快递单生产接入 结束

        return json;
    }

    /**
     * authConfigServices.getALLAreaHQ_DC_H1_D1
     * 获取所有总部、大仓、售后、d1
     *
     * @return List<Integer>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-13
     */
    public List<Integer> getAllAreaHqDcH1D1() {
        List<Integer> result = new ArrayList<>();
        List<AuthModelDTO> all = getAuthConfig();
        if (CollectionUtils.isNotEmpty(all)) {
            for (AuthModelDTO m : all) {
                if (!Objects.equals(m.getHqAreaId(), 0)) {
                    result.add(m.getHqAreaId());
                }
                if (!Objects.equals(m.getDcAreaId(), 0)) {
                    result.add(m.getDcAreaId());
                }
                if (!Objects.equals(m.getH1AreaId(), 0)) {
                    result.add(m.getH1AreaId());
                }
                if (!Objects.equals(m.getD1AreaId(), 0)) {
                    result.add(m.getD1AreaId());
                }
            }
        }
        result.addAll(getAllDcAreaId());
        return result;
    }

    /**
     * addOrderController.GenerateMoreNuJD
     *
     * @param model WuLiuGenerateMoreWuLiuNoReqVO
     * @return String
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-13
     */
    private String generateMoreNuJd(WuLiuGenerateMoreWuLiuNoReqVO model) {
        String msg = "";
        AreaSubjectVO areaSubject = getAreaSubject(model.getSareaid());
        Areainfo areainfo = areainfoService.getAreaInfoByAreaId2(model.getSareaid());
        if (Objects.equals(model.getSareaid(), WuLiuConstant.AREA_DC)) {
            model.setSName(areaSubject.getPrintName());
            model.setSMobile(WuLiuConstant.DC_S_MOBLIE);
        }

        if (StringUtils.isBlank(model.getSMobile()) && Objects.equals(model.getWuType(), 4)) {
            model.setSName(areaSubject.getPrintName());
            model.setSMobile(areainfo.getCompanyTel1());
        }

        //寄件人信息
        CityIdListDTO jinfo;
        //上门取件  其他派送
        if (Arrays.asList(NumUtil.SEVEN, NumUtil.EIGHT).contains(model.getWuType())) {

            jinfo = getAreaIdByCityId(model.getSDid(), null);
        } else {
            jinfo = getAreaInfoByArea(model.getSareaid());
        }
        AddressDTO sender = new AddressDTO();

        sender.setName(model.getSName());
        sender.setMobile(model.getSMobile());
        sender.setProv(jinfo.getPname());
        sender.setCity(jinfo.getZname());
        sender.setCounty(jinfo.getDname());
        sender.setAddress(jinfo.getAddress());

        if (StringUtils.isBlank(sender.getAddress())) {
            sender.setAddress(model.getSAddress());
        }

        //收件人信息
        CityIdListDTO cittInfo;
        if (Objects.equals(model.getWuType(), 1)) {
            cittInfo = getAreaDetailInfoByArea(model.getRareaid());
        } else {
            cittInfo = getAreaDetailIdByCityId(Objects.equals(model.getRCityId(), 0) ? model.getRDid() : model.getRCityId());
        }

        if (Objects.equals(model.getWuType(), 1)) {
            model.setRAddress(cittInfo.getAddress());
        }
        AddressDTO receiver = new AddressDTO();
        receiver.setName(model.getRName());
        receiver.setMobile(model.getRMobile());
        receiver.setProv(cittInfo.getPname());
        receiver.setCity(cittInfo.getZname());
        receiver.setCounty(cittInfo.getDname());
        receiver.setAddress(model.getRAddress());

        return msg;
    }

    @Override
    public WuLiuClaimEntity getWuLiuClaim(Integer wuliuid) {
        return baseMapper.getWuLiuClaim(wuliuid);
    }

    /**
     * 给 C# 发送 RabbitMQ 队列消息以调用 C# 方法
     *
     * @param message 要发送的消息
     * @date 2021-10-20
     * <AUTHOR> [<EMAIL>]
     */
    @Override
    public void setRabbitMqMessageForCsharp(String message) {
        log.warn("给 C# 发送 RabbitMQ 队列消息入参: {}", message);
        try {
            rabbitTemplate.convertAndSend("oaAsync", message);
        } catch (AmqpException e) {
            log.error("给 C# 发送 RabbitMQ 队列消息报错: {}, message: {}, e: {}", e.getMessage(), message, e);
        }
    }

    @Bean(name = "wuliuForCsharpListenerContainer")
    public RabbitListenerContainerFactory<?> wuliuForCsharpListenerContainer(
            MultipleRabbitProperties multipleRabbitProperties,
            @Qualifier("oaAsyncConnectionFactory") ConnectionFactory connectionFactory) {
        return RabbitFactory.createRabbitListenerContainerFactory(
                multipleRabbitProperties.getMultiple().get("oaAsync"), connectionFactory);
    }

    /**
     * JdLogisticServices.CreateOrder
     * 京东中台创建订单
     *
     * @param param JdOrderParamDTO
     * @return R<JdCreateOrderResultDTO>
     * @date 2021-10-22
     * <AUTHOR> [<EMAIL>]
     */
    public R<JdCreateOrderResultDTO> jdCreateOrder(JdOrderParamDTO param) {
        IWuLiujdService wuLiujdService = ApplicationContextUtil.getBean(IWuLiujdService.class);
        JdCreateOrderResultDTO jdCreateOrderResultDTO = wuLiujdService.jdCreateOrder(param);
        return R.success(jdCreateOrderResultDTO);
    }

    /**
     * EmsApiServices.CreateOrder
     *
     * @param orderInfo OrderInfoDTO
     * @return OrderResultDTO
     * @date 2021-10-22
     * <AUTHOR> [<EMAIL>]
     */
    @Override
    public OrderResultDTO emsCreateOrder(OrderInfoDTO orderInfo) {
        R<EmsRequestResultDTO> json = new R<>();

        OrderResultDTO result = new OrderResultDTO();
        result.setCode(0);

        String url = SysConfigUtils.getMUrl() + "/cloudapi_nc/ncSegments/api/EMS/receive?xservicename=oa-ncSegments";

        try {
            //生产环境
            String emsurl = "https://211.156.195.15/iwaybillno-web/a/iwaybill/receive";
            String emskey = "CDw4xO4ctW84";
            if (SysUtils.isDev() || SysUtils.isSaasTest()) {
                emsurl = "https://211.156.195.199/iwaybillno-web/a/iwaybill/receive";
                emskey = "key123xydJDPT";
            }
            EmsRequestDataDTO requestData = new EmsRequestDataDTO();
            requestData.setUrl(emsurl);
            requestData.setParentId(emskey);
            EmsOrderNormalDTO emsOrderNormalDTO = new EmsOrderNormalDTO();
            emsOrderNormalDTO.setCreatedTime(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(LocalDateTime.now()));
            emsOrderNormalDTO.setLogisticsOrderNo(orderInfo.getOrderId());
            emsOrderNormalDTO.setSender(new OrderSenderDTO().setName(orderInfo.getSender().getName()).setMobile(orderInfo.getSender().getMobile()).setProv(orderInfo.getSender().getProv()).setCity(orderInfo.getSender().getCity()).setCounty(orderInfo.getSender().getCounty()).setAddress(orderInfo.getSender().getAddress()));
            emsOrderNormalDTO.setReceiver(new OrderReceiverDTO().setName(orderInfo.getReceiver().getName()).setMobile(orderInfo.getReceiver().getMobile()).setProv(orderInfo.getReceiver().getProv()).setCity(orderInfo.getReceiver().getCity()).setCounty(orderInfo.getReceiver().getCounty()).setAddress(orderInfo.getReceiver().getAddress()));
            requestData.setOrderNormal(emsOrderNormalDTO);
            if (!SysUtils.isDev()) {
                String s = JacksonJsonUtils.toJson(requestData);
                // 测试环境的接口存在问题，所以用临时数据代替
                try {
                    HttpResponse httpResponse = HttpRequest.post(url).body(s).execute();
                    if (httpResponse != null && httpResponse.getStatus() == 200) {
                        json = JSON.parseObject(httpResponse.body(), new TypeReference<R<EmsRequestResultDTO>>() {
                        });
                        if (json != null && Objects.equals(json.getCode(), 0)) {
                            String waybillNo = json.getData().getWaybillNo();
                            result.setOrderids(waybillNo);
                            log.info("EMS 快递单创建接口单号={}", waybillNo);
                        } else if (json != null) {
                            result.setCode(2005);
                            result.setMessage(json.getUserMsg() == null ? json.getMsg() : json.getUserMsg());
                        }
                    }

                } catch (Exception e) {
                    log.error("EMS 快递单创建接口请求报错={}", e.getMessage(), e);
                }
            } else {
                json.setData(new EmsRequestResultDTO());
                json.getData().setRouteCode("250-云A-宣威20-西片D01-*");
                json.getData().setMarkDestinationCode("250-云A");
                json.getData().setPackageCode("");
                json.getData().setWaybillNo("1159176548629");
                json.getData().setPackageCodeName("250-云A");
                json.getData().setMarkDestinationName("250-云A");
                result.setOrderids(json.getData().getWaybillNo());
            }

        } catch (Exception e) {
            String errorMessage = String.format(WuLiuConstant.EMS_EXCEPTION_MESSAGE, Exceptions.getStackTraceAsString(e));
            log.error("{}", errorMessage, e);
            result.setCode(5001);
            result.setMessage(String.format(WuLiuConstant.EMS_EXCEPTION_MESSAGE, e.getMessage()));
            sendTextMessage(errorMessage);
        }

        return result;
    }

    /**
     * @return List<AuthModelDTO>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-02
     */
    public List<AuthModelDTO> getAuthConfig() {
        String cacheKey = "authConfigCacheKeys2";
        synchronized (authConfigObj) {
            if (RedisUtils.hasKey(cacheKey)) {
                return Optional.ofNullable(RedisUtils.get(cacheKey, new TypeReference<List<AuthModelDTO>>() {
                })).orElseGet(ArrayList::new);
            }
            List<AuthModelDTO> list = new ArrayList<>();
            List<WuLiuAuthorizeEntity> list1 = wuLiuAuthorizeService.lambdaQuery().orderByAsc(WuLiuAuthorizeEntity::getRank).list();
            for (WuLiuAuthorizeEntity entity : list1) {
                list.add(new AuthModelDTO().setId(entity.getId())
                        .setName(entity.getName())
                        .setRank(entity.getRank())
                        .setZtid(OptionalUtils.ifTrue(entity.getZtid() != null, () -> String.valueOf(entity.getZtid())))
                        .setVCodeChannel(OptionalUtils.ifTrue(entity.getVCodeChannel() != null, () -> String.valueOf(entity.getVCodeChannel())))
                        .setMarketChannel(OptionalUtils.ifTrue(entity.getMarketChannel() != null, () -> String.valueOf(entity.getMarketChannel())))
                        .setDcAreaId(entity.getDcAreaId())
                        .setHqAreaId(entity.getHqAreaId())
                        .setH1AreaId(entity.getH1AreaId())
                        .setD1AreaId(entity.getD1AreaId())
                        .setYapingAreaId(entity.getYapingAreaId()));
            }
            RedisUtils.set(cacheKey, list, 1440);
            return list;
        }
    }

    /**
     * authConfigServices.getAllDCAreaId
     * 获取所有仓库门店
     *
     * @return List<int>
     * @date 2021-10-19
     * <AUTHOR> [<EMAIL>]
     */
    private List<Integer> getAllDcAreaId() {
        List<Integer> areaDcId = getAllAreaDcAreaId().stream().map(DcAreaModelDTO::getAreaid).collect(Collectors.toList());
        List<Integer> authDcAreaId = getAuthConfig().stream().map(AuthModelDTO::getDcAreaId).filter(dcAreaId -> dcAreaId != null && dcAreaId != 0).collect(Collectors.toList());
        areaDcId.addAll(authDcAreaId);
        return areaDcId.stream().distinct().collect(Collectors.toList());
    }

    /**
     * authConfigServices.getAllAreaDcAreaId
     *
     * @return List<DcAreaModelDTO>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-09
     */
    public List<DcAreaModelDTO> getAllAreaDcAreaId() {
        String cacheKey = "AllDcAreaIdCacheKeys2";
        synchronized (allDcAreaIdObj) {
            if (RedisUtils.hasKey(cacheKey)) {
                return Optional.ofNullable(RedisUtils.get(cacheKey, new TypeReference<List<DcAreaModelDTO>>() {
                })).orElseGet(ArrayList::new);
            }
            List<DcAreaModelDTO> list = new ArrayList<>();
            List<Areainfo> list1 = areainfoService.getAreaInfoDistinct();
            for (Areainfo areainfo : list1) {
                list.add(new DcAreaModelDTO()
                        .setAreaid(areainfo.getDcAreaID())
                        .setAuthId(areainfo.getAuthorizeid()));
            }
            list = list.stream().filter(t -> t.getAreaid() != null && t.getAreaid() != 0).collect(Collectors.toList());
            RedisUtils.set(cacheKey, list, 1440);
            return list;
        }
    }

    @Override
    @DS("oanewWrite")
    @DSTransactional()
    public Boolean cancelOrder(CancelOrderDTO cancelOrderDTO) {
        Integer deliveryId = cancelOrderDTO.getDeliveryId();
        Boolean status = cancelOrderDTO.getStatus();
        Integer expressType = cancelOrderDTO.getExpressType();
        String message = cancelOrderDTO.getMessage();
        OaUserBO oaUserBO = cancelOrderDTO.getOaUserBO();
        // 调用失败时记录日志
        if (Boolean.FALSE.equals(status)) {
            String errorMsg = ExpressTypeEnum.getExpressMessage(expressType) + ",取消订单失败," + message;
            wuLiuLogService.save(new WuLiuLogEntity().setWuliuid(deliveryId).setInuser(oaUserBO.getUserName()).setMsg(errorMsg)
                    .setDtime(LocalDateTime.now()));
            return false;
        }
        return cancelOrder(oaUserBO, deliveryId);
    }

    /**
     * 取消快递
     * @param oaUserBO
     * @param deliveryId
     * @return
     */
    @Override
    @DS("oanewWrite")
    @DSTransactional()
    public Boolean cancelOrder(OaUserBO oaUserBO, Integer deliveryId) {
        WuLiuEntity wuLiu = this.getById(deliveryId);
        if (Objects.isNull(wuLiu)) {
            log.warn("取消快递，查询物流单信息为空，物流id=" + deliveryId);
            return true;
        }
        //发货扫描不同物流单对应一个快递单情况
        List<WuLiuEntity> wuLiuEntityList = new ArrayList<>();
        if(StringUtils.isNotBlank(wuLiu.getNu())) {
            wuLiuEntityList = this.lambdaQuery().select(WuLiuEntity::getId, WuLiuEntity::getNu, WuLiuEntity::getWuType, WuLiuEntity::getDanHaoBind)
                    .eq(WuLiuEntity::getNu, wuLiu.getNu()).eq(WuLiuEntity::getCom, wuLiu.getCom()).list();
        }
        if (CollectionUtils.isEmpty(wuLiuEntityList)) {
            log.warn("取消物流单，查询物流单信息为空，param={}", wuLiu);
            return true;
        }
        List<Integer> wuliuIdList = wuLiuEntityList.stream().map(WuLiuEntity::getId).collect(Collectors.toList());
        //删除子单号
        wuliunoexService.deleteByWuliuIds(wuliuIdList);
        //清除物流单中的快递单
        this.lambdaUpdate().set(WuLiuEntity::getNu, "").set(WuLiuEntity::getCom, "").set(WuLiuEntity::getPtUserName, null).set(WuLiuEntity::getPtUserMobile, null).in(WuLiuEntity::getId, wuliuIdList).eq(WuLiuEntity::getNu, wuLiu.getNu()).update();
        // 保存日志
        String successMsg = LogisticsExpressTypeEnum.getExpressMessage(wuLiu.getCom()) + "取消订单成功,单号" + wuLiu.getNu();
        List<WuLiuLogEntity> logs = wuliuIdList.stream().map(v ->
                Builder.of(WuLiuLogEntity::new)
                        .with(WuLiuLogEntity::setWuliuid, v)
                        .with(WuLiuLogEntity::setInuser, oaUserBO.getUserName())
                        .with(WuLiuLogEntity::setMsg, successMsg)
                        .with(WuLiuLogEntity::setDtime, LocalDateTime.now())
                        .build())
                .collect(Collectors.toList());
        wuLiuLogService.saveLogBatch(logs);
        //通知订单取消快递
        for (WuLiuEntity wuLiuEntity : wuLiuEntityList) {
            if (WuLiuConstant.SUB_WULIU_TYPE_ARRY.contains(wuLiuEntity.getWuType()) || WuLiuConstant.LP_SUB_WULIU_TYPE_ARRY.contains(wuLiuEntity.getWuType())) {
                setRabbitMqMessageForCsharp(JacksonJsonUtils.toJson(new RabbitMqActDTO().setAct("ExpressWaybillCancelMqAct")
                        .setData(new CancelWuliuNoDTO()
                                .setWuliuId(wuLiuEntity.getId().longValue())
                                .setNu(wuLiuEntity.getNu())
                                .setWuliuType(wuLiuEntity.getWuType())
                                .setSubId(wuLiuEntity.getDanHaoBind().longValue())
                                .setUser(oaUserBO.getUserName()))
                ));
            }
        }
        //派送类物流单取消快递后，物流单状态由等待派送变为等待取货
        if (!Objects.equals(WuLiuType.INTERNAL_LOGISTICS.getCode(), wuLiu.getWuType())) {
            ChangWuliuStatsBO wuliuStats = LambdaBuild.create(ChangWuliuStatsBO.class).set(ChangWuliuStatsBO::setWuliuId, wuLiu.getId()).set(ChangWuliuStatsBO::setStats, WuLiuStatusEnum.WAITING_PICK.getCode()).build();
            WuliuUtil.sendWuliuExpressMessage(new WuliuExpressMqBO<ChangWuliuStatsBO>().setAct(WuliuExpressConstant.ACT_WULIU_CHANGE_STATS)
                    .setData(wuliuStats));
        }
        return true;
    }

    @Override
    @DS("ch999oanew")
    public void getDeliveryId(Integer deliveryId, String deliveryNumber, OaUserBO bo) {
        WuLiuEntity wuliu = this.lambdaQuery().select(WuLiuEntity::getId, WuLiuEntity::getNu, WuLiuEntity::getStats)
                .eq(WuLiuEntity::getId, deliveryId).one();
        Assert.notNull(wuliu, "未查询到物流单!");
        Assert.isTrue(deliveryNumber.equals(wuliu.getNu()), "快递单号:{},和数据库中快递单号:{},不匹配!", deliveryNumber, wuliu.getNu());

        Integer wuliuStats = wuliu.getStats();
        boolean validStatus = WuliuStatusEnum.COMPLETE.getCode().equals(wuliuStats) ||
                WuliuStatusEnum.INVALID.getCode().equals(wuliuStats) ||
                WuliuStatusEnum.RECEIVED.getCode().equals(wuliuStats);
        if (Boolean.TRUE.equals(validStatus)) {
            String errorMsg = "物流单状态为 [完成],[作废],[已签收] 时不能取消";
            wuLiuLogService.save(new WuLiuLogEntity().setWuliuid(deliveryId).setInuser("系统").setMsg(errorMsg)
                    .setDtime(LocalDateTime.now()));
            throw new IllegalStateException(errorMsg);
        }
    }

    @Override
    public WuLiuEntity getWuliuBywCateId(Integer wCateId, Integer wutype, Integer danhaobind) {
        return this.lambdaQuery().eq(WuLiuEntity::getWCateId, wCateId).eq(WuLiuEntity::getWuType, wutype)
                .eq(WuLiuEntity::getDanHaoBind, danhaobind).one();
    }

    /**
     * addOrderController.wuliu
     * 物流单
     *
     * @param currentUser OaUserBO
     * @param model       WuLiuAddOrUpdateReqVO
     * @return R<Integer>
     * @date 2021-10-11
     * <AUTHOR> [<EMAIL>]
     */
    @DSTransactional
    @Override
    public R<Integer> addOrUpdate(OaUserBO currentUser, WuLiuAddOrUpdateReqVO model) {

        model.setWuliuid(Optional.ofNullable(model.getWuliuid()).orElse(0));
        model.setDanHaoBind(Optional.ofNullable(model.getDanHaoBind()).orElse(0));
        model.setRareaid(Optional.ofNullable(model.getRareaid()).orElse(0));
        model.setWuType(Optional.ofNullable(model.getWuType()).orElse(0));
        model.setOtherarea(Optional.ofNullable(model.getOtherarea()).orElse(""));
        model.setSubKinds(Optional.ofNullable(model.getSubKinds()).orElse(0));
        Integer subKinds = model.getSubKinds();

        //校验快递单是否取消，如果未取消不允许修改地址信息
        checkWuLiuNo(model);
        // 分辨新增或更新操作
        boolean isAdd = Objects.equals(model.getWuliuid(), 0);

        String emsg = "";

        boolean isCopy = Objects.equals(model.getWuliuid(), 0) && Objects.equals(currentUser.getAreaId(), WuLiuConstant.AREA_DC)
                && Objects.equals(model.getWuType(), 1)
                && StringUtils.isNotBlank(model.getOtherarea());

        model.setSareaid(OptionalUtils.ifTrue(StringUtils.isNotBlank(model.getSareaid2()), () -> Integer.valueOf(model.getSareaid2())));
        model.setRareaid(OptionalUtils.ifTrue(StringUtils.isNotBlank(model.getRareaid2()), () -> Integer.valueOf(model.getRareaid2())));

        // 当用户下拉选择框‘类别’中选择值为‘内部物流 = 1’，‘订单派= 4’，‘维修单派送=5’，‘订单加急送=6’，‘良品订单派送=9’，‘物料派送 =10’中任意一个，
        // 并且‘第三方快递’值为‘美团=meituan’、‘达达=dada’、‘uu跑腿=uupt’
        List<String> paotuiList = Arrays.asList(WuLiuConstant.MEITUAN, WuLiuConstant.DADA, WuLiuConstant.UU_PAOTUI, WuLiuConstant.PAO_TUI, WuLiuConstant.JIUJI_KUAI_SONG, "sftc");
        if ((Arrays.asList(1, 4, 5, 6, 9, 10).contains(model.getWuType())) && paotuiList.contains(model.getCom())) {
            isCopy = false;
            /*if (SysUtils.hasNotRank(WuLiuConstant.RANK_WLMT)) {
                return R.error("您没有权值wlmt，不能添加美团或达达、uu内部物流单！");
            }*/
            //订货调拨单关联的物流单无需校验单号
            boolean noCheckSub = Boolean.TRUE.equals(model.getNoCheckSub());
            if (!noCheckSub) {
            if (model.getDanHaoBind() <= 0) {
                return R.error("必须填写订单号才能使用九机快送和跑腿！");
            }
            if (Arrays.asList(1,10).contains(model.getWuType()) && (subKinds < 1 || subKinds > 3)) {
                return R.error("无效的订单类型！");
            }

            //内部物流填入的必须是收货门店未完成的订单
            //订单派送、维修单派送、订单加急送填入单号必须是发货门店未完成订单
            Integer areaId = !Objects.equals(model.getWuType(), 1) ? 0 : model.getRareaid();
            if (Arrays.asList(1, 10).contains(model.getWuType())) {
                String eroMsg = checkWuLiuSub(model.getDanHaoBind(), subKinds, areaId, null);
                if (StringUtils.isNotBlank(eroMsg)) {
                    return R.error(eroMsg);
                }
            }
            }
        }

        if (Boolean.TRUE.equals(model.getIsExceptionSub()) && StringUtils.isBlank(model.getExceptionRemark())) {
            return R.error("异常备注不能为空");
        }

        Integer wlId = -1;
        AtomicReference<Integer> wuLiuId = new AtomicReference<>();
        AtomicReference<String> errorMsg = new AtomicReference<>("");
        try {
            if (!Objects.equals(model.getWuType(), NumUtil.ONE) || (Objects.equals(NumUtil.ONE, model.getWuType()) && Optional.ofNullable(model.getRareaid()).orElse(0) > NumUtil.ZERO)) {
                Map<String, Object> map = saveWuliuAction(model, currentUser);
                Optional.ofNullable(map.get("wl_id")).ifPresent(item -> wuLiuId.set((Integer) item));
                Optional.ofNullable(map.get("emsg")).ifPresent(item -> errorMsg.set((String) item));
                wlId = wuLiuId.get();
                emsg = errorMsg.get();
            }

            // 批量生成物流单
            if (isCopy) {
                List<Integer> areaids = Optional.ofNullable(OptionalUtils.ifTrue(StringUtils.isNotBlank(model.getOtherarea()),
                        () -> Arrays.stream(model.getOtherarea().split(","))
                                .filter(StringUtils::isNotBlank)
                                .map(Integer::valueOf)
                                .filter(item -> !item.equals(model.getRareaid()))
                                .collect(Collectors.toList()))).orElseGet(ArrayList::new);
                List<String> zhiWuNameList = Arrays.asList("店长", "副店长");
                Map<Integer, Ch999User> userMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(areaids)) {
                    userMap = ch999UserService
                            .listAllUserByAreaIdListAndZhiWuList(zhiWuNameList, areaids).stream()
                            .collect(Collectors.toMap(Ch999User::getArea1id, Function.identity(), (k1, k2) -> k1));
                }
                for (Integer m : areaids) {
                    Map<String, Object> map = OptionalUtils.ifNotNull(userMap.get(m), item -> {
                        model.setWuliuid(0);
                        model.setRareaid(m);
                        model.setRName(Optional.ofNullable(item.getCh999Name()).orElse(""));
                        model.setRMobile(Optional.ofNullable(item.getMobile()).orElse(""));
                        model.setNu("");
                        model.setOrgcode("");
                        model.setDestcode("");
                        model.setYuejiekahao("");
                        model.setDanHaoBind(0);
                        return saveWuliuAction(model, currentUser);
                    });
                    if (Optional.ofNullable(map).orElse(new HashMap<>()).get("wl_id") != null) {
                        wlId = Integer.valueOf(String.valueOf(map.get("wl_id")));
                    }
                }
            }
        } catch (Exception e) {
            if(!(isAdd && Boolean.TRUE.equals(model.getIsSuccessInsertWuLiu()))){
                throw e;
            }else{
                log.warn("物流单[{}]插入成功, 但是后续流程发生异常", model, e);
            }
        }
        if(isAdd && Boolean.TRUE.equals(model.getIsSuccessInsertWuLiu())){
            if(StringUtils.isNotBlank(emsg)){
                log.warn("物流单[{}]发生{}异常", model, emsg);
            }
            R<Integer> r = R.success(ObjectUtil.defaultIfBlank(emsg, "新增成功"), model.getWuliuid());
            r.put("isCallPaoTui", model.getIsCallIngPaoTui());
            return r;
        }

        R<Integer> r = StringUtils.isBlank(emsg) ? R.success(isAdd ? "新增成功" : "更新成功", wlId) : R.error(isAdd ? "新增失败，" + emsg : "更新失败，" + emsg);
        r.put("isCallPaoTui", model.getIsCallIngPaoTui());
        return r;
    }

    /**
     * 校验物流单修改
     * @param model
     */
    private void checkWuLiuNo(WuLiuAddOrUpdateReqVO model) {
        /*if (Objects.equals(1, model.getWuType()) && Objects.equals(1, model.getSource())) {
            if (WuLiuConstant.WULIU_DELIVERY_COM_ARRY.contains(model.getCom())
                    && StringUtils.isBlank(model.getNu())) {
                throw new CustomizeException("手动添加的内部调拨物流单不允许使用跑腿，请联系物流负责人或物流运营同事协助处理");
            }
        }*/
        if (StringUtils.isNotBlank(model.getCom()) && StringUtils.isBlank(model.getNu())) {
            if (WuLiuTypeEnum.ORDER.getCode().equals(model.getWuType()) || WuLiuTypeEnum.ORDER_EXPRESS.getCode().equals(model.getWuType())) {
                WuLiuSubDTO wuLiuSub = wuLiuService.getWuLiuSub(model.getDanHaoBind());
                Integer cancelApplication = Optional.ofNullable(wuLiuSub).map(WuLiuSubDTO::getCancelApplication).orElse(0);
                if (Objects.equals(1, cancelApplication) || Boolean.TRUE.equals(RedisUtils.hashHasKey(RedisKeys.EXCEPTION_SUB_REDISKEY, Convert.toStr(model.getDanHaoBind())))) {
                    throw new CustomizeException("订单已被锁定，无法生成快递或跑腿，请联系呼叫中心处理");
                }
                Integer subCheck = Optional.ofNullable(wuLiuSub).map(WuLiuSubDTO::getSubCheck).orElse(0);
                //线上国补订单校验
                List<NationalSupplementKindRes> nationalSupplementKindList = SpringUtil.getBean(NationalSupplementService.class).getNationalSupplementKindList(Collections.singletonList(Convert.toStr(model.getDanHaoBind())));
                if (CollUtil.isNotEmpty(nationalSupplementKindList) && !SubCheckEnum.ALREADY_OUT.getCode().equals(subCheck)) {
                    throw new CustomizeException("线上国补订单需订单先出库后，再使用快递");
                }
                //线上国补判断是否使用顺丰
                if (CollUtil.isNotEmpty(nationalSupplementKindList) && !LogisticsExpressTypeEnum.SHUN_FENG_LAAS.getCode().equals(model.getCom())) {
                    throw new CustomizeException("线上国补订单只支持顺丰快递，请选择顺丰快递后再操作");
                }
            } else if (WuLiuTypeEnum.FOURTEEN_DAY.getCode().equals(model.getWuType())) {
                Integer cancelApplication = Optional.ofNullable(wuLiuService.getWuLiuReSub(model.getDanHaoBind())).map(WuLiuSubDTO::getCancelApplication).orElse(0);
                if (Objects.equals(1, cancelApplication) || Boolean.TRUE.equals(RedisUtils.hashHasKey(RedisKeys.EXCEPTION_LPRECOVER_SUB_REDISKEY, Convert.toStr(model.getDanHaoBind())))) {
                    throw new CustomizeException("订单已被锁定，无法生成快递或跑腿，请联系呼叫中心处理");
                }
            }
        }
        if (!Objects.equals(0, Optional.ofNullable(model.getWuliuid()).orElse(0))) {
            WuLiuEntity wuLiu = wuLiuService.getById(model.getWuliuid());
            boolean isNotUpdate = Objects.nonNull(wuLiu) && Arrays.asList(WuLiuStatusEnum.FINISH.getCode(), WuLiuStatusEnum.INVALID.getCode(), WuLiuStatusEnum.SIGHED.getCode()).contains(Optional.ofNullable(wuLiu.getStats()).orElse(0));
            if (isNotUpdate) {
                throw new CustomizeException("物流单已经签收或删除不允许修改");
            }
        }

        if (Objects.equals(0, Optional.ofNullable(model.getWuliuid()).orElse(0))
                && Arrays.asList(WuLiuTypeEnum.ORDER.getCode(), WuLiuTypeEnum.ORDER_EXPRESS.getCode(),
                WuLiuTypeEnum.FOURTEEN_DAY.getCode(),WuLiuTypeEnum.AFTER_SERVICE.getCode(),WuLiuTypeEnum.VISIT.getCode()).contains(Optional.ofNullable(model.getWuType()).orElse(0))
                && !Objects.equals(0, Optional.ofNullable(model.getDanHaoBind()).orElse(0))) {
            WuLiuEntity wuliu = wuLiuService.getWuLiuByWuTypeAndDanhaobind(model.getWuType(), model.getDanHaoBind());
            if (Objects.nonNull(wuliu)) {
                throw new CustomizeException("该订单已存在关联的物流单（"+wuliu.getId()+"），请核对");
            }
        }
        if (!Objects.equals(model.getWuliuid(), 0)) {
            WuLiuEntity oldWuliu = this.getById(model.getWuliuid());
            if (Objects.isNull(oldWuliu) || StringUtils.isBlank(oldWuliu.getNu())) {
                return;
            }
            if (!oldWuliu.getSAddress().equals(model.getSAddress()) || !oldWuliu.getRAddress().equals(model.getRAddress())) {
                throw new CustomizeException("物流单已生成快递，不能修改订单地址。如要修改地址信息，请先取消快递后修改");
            }
            //物流单快递方式修改限制
            if (!Objects.equals(model.getCom(), oldWuliu.getCom()) && StringUtils.isNotBlank(oldWuliu.getNu())) {
                throw new CustomizeException("请取消或删除快递单号后再修改快递方式");
            }
        }
    }

    /**
     * subWLService.saveWuliuAction
     * 保存物流信息
     *
     * @param model       WuLiuAddOrUpdateReqVO
     * @param currentUser 当前用户信息
     * @return Map<String, Object>
     * @date 2021-10-11
     * <AUTHOR> [<EMAIL>]
     */
    public Map<String, Object> saveWuliuAction(WuLiuAddOrUpdateReqVO model, OaUserBO currentUser) {
        String username = currentUser.getUserName();
        Integer userId = currentUser.getUserId();
        Integer areaId = currentUser.getAreaId();
        Integer xTenant = currentUser.getXTenant();
        Integer wlId = 0;
        String emsg = "";
        Map<String, Object> map = new HashMap<>(2);
        map.put("wl_id", wlId);
        map.put("emsg", emsg);

        SfOrderInfoVO m = new SfOrderInfoVO();
        // 如果是顺风，中通，并且没有关联的物流单号，则自动生成一个物流单号
        String changeComment = ""; // 数据变更记录
        WuLiu2Entity old = new WuLiu2Entity();
        boolean isChangeComOrPaiJianRen = false;
        if (model.getWuliuid() > 0) {
            old = getWuLiuInfoById(0, model.getWuliuid());

            if (!Objects.equals(model.getComment(), old.getComment())) {
                changeComment = "备注信息由【" + old.getComment() + "】修改为【" + model.getComment() + "】";
                List<WuLiuLogResVO> logList = Optional.ofNullable(model.getLogs()).orElseGet(ArrayList::new);
                logList.add(new WuLiuLogResVO()
                        .setDtime(LocalDateTime.now())
                        .setInuser(username)
                        .setMsg(changeComment));
                model.setLogs(logList);
            }

            Integer innerWuliuCount = OptionalUtils.ifTrue(WuLiuTypeEnum.INNER.getCode().equals(model.getWuType()),
                    () -> wuLiuService.getDiaobWuliuCount(model.getWuliuid()));
            boolean flag = innerWuliuCount != null && innerWuliuCount > 0;
            //物流单打印后，状态为等待取货或等待派送，可以叫跑腿快递
            flag = flag || Objects.equals(WuliuStatusEnum.WAITING_DELIVERY.getCode(), old.getStats())
                    || Objects.equals(WuliuStatusEnum.WAITING_GETTING_GOODS.getCode(), old.getStats());
            if (!flag && (Optional.ofNullable(model.getPrintCnt()).orElse(0) > 0) &&
                    (!Objects.equals(model.getSareaid(), old.getSAreaId())
                            || !Objects.equals(model.getRareaid(), old.getRAreaId())
                            || !Objects.equals(model.getSName(), old.getSName())
                            || !Objects.equals(model.getRName(), old.getRName())
                            || !Objects.equals(model.getSMobile(), old.getSMobile())
                            || !Objects.equals(model.getRMobile(), old.getRMobile())
                            || !Objects.equals(model.getSDid(), old.getSCityId())
                            || !Objects.equals(model.getRDid(), old.getRCityId())
                            || !Objects.equals(model.getSAddress(), old.getSAddress())
                            || !Objects.equals(model.getRAddress(), old.getRAddress()))) {
                wlId = model.getWuliuid();
                map.put("wl_id", wlId);
                map.put("emsg", "已经打印的物流单禁止修改收寄信息！");
                return map;
            }
            isChangeComOrPaiJianRen = old.getId() != null && (ObjectUtil.notEqual(old.getCom(), model.getCom())
                    || ObjectUtil.notEqual(old.getPaiJianRen(), model.getPaiJianRen()));
            if(isChangeComOrPaiJianRen){
                //自动呼叫跑腿限制
                String changeColumn;
                String changeContent;
                if(ObjectUtil.notEqual(old.getCom(), model.getCom())){
                    changeColumn = WuLiuEntity.COM_COLUMN_NAME;
                    changeContent = model.getCom();
                }else if(ObjectUtil.notEqual(old.getPaiJianRen(), model.getPaiJianRen())){
                    changeColumn = WuLiuEntity.PAI_JIAN_REN_COLUMN_NAME;
                    changeContent = model.getPaiJianRen();
                }else{
                    changeColumn = "";
                    changeContent = "";
                }

                R canChangeR = SpringUtil.getBean(IPaoTuiService.class).canChangeWuLiu(model.getWuliuid(), changeColumn, changeContent);
                if(!canChangeR.isSuccess()){
                    throw new CustomizeException(canChangeR);
                }
            }
        }

        boolean needSaveWuliu = true;
        boolean isCreateWuliu = false;
        AreaSubjectVO areaSubject = getAreaSubject(model.getSareaid());
        String wuliudanRunErrmsg = "";
        Areainfo areaInfo = Optional.ofNullable(areainfoService.getAreaInfoByAreaId2(model.getSareaid())).orElseGet(Areainfo::new);

        // 是否能够生产第三方物流单
        // 注意：订单派送的物流单关联订单的配送方式是“第三方派送”、“快递运输”的必须是应收货款为0才能生成。
        boolean isCreateOrder = true;
        boolean isCreateNu = StringUtils.isBlank(model.getNu());
        if (StringUtils.isBlank(model.getNu()) && WuliuUtil.isOrderWutype(model.getWuType()) && model.getDanHaoBind() > 0) {
            Integer count = 0;
            //销售单、良品单支付方式为货到付款时
            if (Objects.equals(WuLiuTypeEnum.FOURTEEN_DAY.getCode(), model.getWuType())) {
                count = wuLiuRecoverMarketInfoService.lambdaQuery().eq(WuLiuRecoverMarketInfoEntity::getSubId, model.getDanHaoBind()).in(WuLiuRecoverMarketInfoEntity::getDelivery, Arrays.asList(4, 6)).ne(WuLiuRecoverMarketInfoEntity::getSubPay, 2).apply("yingfuM<>yifuM").count();
            } else {
                count = wuLiuSubService.lambdaQuery().eq(WuLiuSubEntity::getSubId, model.getDanHaoBind()).in(WuLiuSubEntity::getDelivery, Arrays.asList(4, 6)).ne(WuLiuSubEntity::getSubPay, 2).apply("yingfuM<>yifuM").count();
            }
            if (count > 0) {
                isCreateOrder = false;
                List<WuLiuLogResVO> logList = Optional.ofNullable(model.getLogs()).orElseGet(ArrayList::new);
                logList.add(new WuLiuLogResVO()
                        .setDtime(LocalDateTime.now())
                        .setInuser(username)
                        .setMsg("订单应收货款为0才能生成快递单号"));
                model.setLogs(logList);
            }
        }
        //验销售单和良品单是否已出库或欠款状态
        /*if (StringUtils.isBlank(model.getNu())
                && WuliuUtil.isOrderWutype(model.getWuType())
                && (Optional.ofNullable(model.getDanHaoBind()).orElse(0) > 0)) {
            Integer subCheck = 0;
            if (Objects.equals(WuLiuTypeEnum.FOURTEEN_DAY.getCode(), model.getWuType())) {
                subCheck = Optional.ofNullable(wuLiuRecoverMarketInfoService.getSubBySubId(model.getDanHaoBind())).map(WuLiuRecoverMarketInfoEntity::getSubCheck).orElse(0);
            } else {
                subCheck = Optional.ofNullable(wuLiuSubService.getSubBySubId(model.getDanHaoBind())).map(WuLiuSubEntity::getSubCheck).orElse(0);
            }
            if (!Arrays.asList(SubCheckEnum.ALREADY_OUT.getCode(),SubCheckEnum.DEBT.getCode()).contains(Optional.ofNullable(subCheck).orElse(0))) {
                isCreateOrder = false;
                List<WuLiuLogResVO> logList = Optional.ofNullable(model.getLogs()).orElseGet(ArrayList::new);
                logList.add(new WuLiuLogResVO()
                        .setDtime(LocalDateTime.now())
                        .setInuser(username)
                        .setMsg("订单状态不符合，需已出库或欠款才能生成快递单和跑腿"));
                model.setLogs(logList);
            }
        }*/
        // 物流单已经存在快递单号时不创建快递
        if (StringUtils.isNotBlank(old.getNu())) {
            isCreateOrder = false;
        }

        // 更新地址 开始
        if (!WuLiuTypeEnum.OTHERS.getCode().equals(model.getWuType())) {
            Optional.ofNullable(areainfoService.getAreaInfoByAreaId2(model.getSareaid()))
                    .ifPresent(areaInfo3 -> model.setSAddress(areaInfo3.getCompanyAddress())
                            .setSCityId(areaInfo3.getCityid()));
        }

        Optional.ofNullable(areainfoService.getAreaInfoByAreaId2(model.getRareaid())).ifPresent(areaInfo3 -> {
            model.setRCityId(0);
            if (StringUtils.isBlank(model.getRAddress())) {
                model.setRAddress(areaInfo3.getCompanyAddress()).setRCityId(areaInfo3.getCityid());
            }
        });
        if (WuLiuTypeEnum.INNER.getCode().equals(model.getWuType())) {
            Optional.ofNullable(areainfoService.getAreaInfoByAreaId2(model.getRareaid())).ifPresent(areaInfo3 -> {
                model.setSDid(areaInfo3.getCityid());
            });
            Optional.ofNullable(areainfoService.getAreaInfoByAreaId2(model.getRareaid())).ifPresent(areaInfo3 -> {
                model.setRDid(areaInfo3.getCityid());
            });
        }
        if(Optional.ofNullable(model.getSDid()).orElse(0) > 0) {
            model.setSCityId(model.getSDid());
        }
        if (Optional.ofNullable(model.getRDid()).orElse(0) > 0) {
            model.setRCityId(model.getRDid());
        }
        // 更新地址 结束

        //手动添加物流单快递方式增加权限管控
        boolean kdscRank = checkKdscRank(model);
        if (isCreateOrder && kdscRank) {
            if (Objects.equals(model.getWuliuid(), 0)) {
                wlId = wuLiuService.newTransSaveWuLiu(model, areaId, username);
                if (wlId > 0) {
                    model.setWuliuid(wlId);
                }
            } else {
                wlId = model.getWuliuid();
            }

            map.put("wl_id", wlId);
            map.put("emsg", emsg);
            writewuliulogs(wlId, username, "手动添加物流单无kdsc权值，生成快递单失败，请联系物流同事处理", null);
            return map;
        }

        //m版物流单京东快递未选细分类型
        model.setJiujiJdExpressType(StringUtils.isNotBlank(model.getJiujiJdExpressType()) ? model.getJiujiJdExpressType() : "1");
        //物流单生成子运单交互优化
        //德邦快递
        String com = Optional.ofNullable(model.getCom()).orElse("");
        boolean comFlag = Arrays.asList(LogisticsExpressTypeEnum.DEP_PON_9JI.getCode(),
                LogisticsExpressTypeEnum.SHUN_FENG_LAAS.getCode(),
                LogisticsExpressTypeEnum.MEI_TUAN.getCode(),LogisticsExpressTypeEnum.SFTC.getCode()).contains(com);
        boolean isWuliuNoFlag = comFlag || Optional.ofNullable(model.getPackagesNumber()).orElse(1) > 1;
        if (isCreateOrder && isWuliuNoFlag) {
            wlId = wuLiuService.newTransSaveWuLiu(model, areaId, username);
            if (wlId > 0) {
                model.setWuliuid(wlId);
                needSaveWuliu = false;
            }
            WuLiuEntity wuliu = this.getById(model.getWuliuid());
            wuliu.setCom(model.getCom());
            WuliuExpressBO wuliuExpress = new WuliuExpressBO();
            wuliuExpress.setWuliu(wuliu);
            wuliuExpress.setPackageCount(model.getPackagesNumber());
            wuliuExpress.setExpressType(model.getExpressType());
            if (Arrays.asList(WuLiuConstant.JINGDONG, WuLiuConstant.JINGDONG_JIUJI).contains(model.getCom())) {
                wuliuExpress.setExpressType(model.getJiujiJdExpressType());
            }
            wuliuExpress.setInUser(currentUser.getUserName());
            wuliuExpress.setReceivePosition(model.getReceivePosition());
            wuliuExpress.setSendPosition(model.getSendPosition());
            try {
                isCreateOrder = false;
                GenerateMoreWuliuNoRes generateMoreWuliuNoRes = wuliuExpressNuService.generateMoreWuliuNo(wuliuExpress);
                String nu = Optional.ofNullable(generateMoreWuliuNoRes).map(GenerateMoreWuliuNoRes::getNu).orElse("");
                if (StringUtils.isNotEmpty(nu)) {
                    model.setNu(nu);
                }
            } catch (CustomizeException e) {
                emsg = e.getMessage();
                log.error("创建快递失败model={}", model, e);
            }
        }

        // 顺丰逻辑 开始
        String expressShunfeng = model.getExpressType();
        if (isCreateOrder && (Arrays.asList(WuLiuConstant.SHUNFENG, WuLiuConstant.SHUNFENG_JIUJI, WuLiuConstant.SHUNFENG_LAAS).contains(model.getCom())) && StringUtils.isBlank(model.getNu())) {

            // 特殊业务处理，特殊地区，寄件人强制 开始
            // 如果是DC，并且是订单派送
            if (Objects.equals(model.getSareaid(), WuLiuConstant.AREA_DC)) {
                model.setSName(areaSubject.getPrintName()).setSMobile(WuLiuConstant.DC_S_MOBLIE);
            }
            // 如果是 H1
            if (Objects.equals(model.getSareaid(), WuLiuConstant.AREA_H1)) {
                // 易涛的电话
                model.setSName(areaSubject.getPrintName()).setSMobile("18860789139");
            }
            // 如果是 dc1
            if (Objects.equals(model.getSareaid(), WuLiuConstant.AREA_DC1)) {
                // 陶彪的电话
                model.setSName(areaSubject.getPrintName()).setSMobile("18508501195");
            }
            // 如果是 dc2
            if (Objects.equals(model.getSareaid(), WuLiuConstant.AREA_DC2)) {
                // 缪青的电话
                model.setSName(areaSubject.getPrintName()).setSMobile("18613223881");
            }
            // sz
            if (Objects.equals(model.getSareaid(), WuLiuConstant.AREA_SZ)) {
                model.setSName(areaSubject.getPrintName()).setSMobile("13760162987");
            }
            if (StringUtils.isBlank(model.getSMobile()) && Objects.equals(model.getWuType(), 4)) {
                model.setSName(areaSubject.getPrintName()).setSMobile(areaInfo.getCompanyTel1());
            }
            // 特殊业务处理，特殊地区，寄件人强制 结束

            // 订单基本信息
            LocalDateTime now = LocalDateTime.now();
            m.setOrderId(Optional.ofNullable(model.getWCateId()).orElse(0) + "_"
                    + DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS").format(now) + "_" + getNumCode(3))

                    // 包裹数
                    .setParcelQuantity(1)
                    //支付类型：（页面下啦选择：1:寄方付  2:收方付 3:第三方付 。默认第三方付）
                    .setPayMethod(3)
                    .setExpressType(model.getExpressType())
                    // 订单备注。因是电子产品，记得写勿摔打之类的
                    .setRemark("请勿摔打,保持整洁。")
                    .setSendStartTime(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(now));

            if (isCurAreaHqDcH1D1(model.getSareaid()) || Objects.equals(model.getSareaid(), WuLiuConstant.AREA_GCB)) {
                // 如果是dc，则是寄付月结
                m.setPayMethod(1);
            }
            List<Integer> sfpayMethod = Arrays.asList(1, 2, 3);
            if (sfpayMethod.contains(model.getPayMethod())) {
                m.setPayMethod(model.getPayMethod());
            }

            if (WuLiuConstant.SHUNFENG.equals(model.getCom()) &&
                    ((StringUtils.isNotBlank(m.getExpressType()) && !m.getExpressType().contains("9386")) || StringUtils.isBlank(m.getExpressType()))) {
                m.setCustId(getYueJieKaHao(model.getSareaid(), m.getExpressType()).getCustId());
            }

            // 寄件人信息
            m.setJCompany(areaSubject.getPrintName())
                    .setJContact(model.getSName() + getWuliuStatsArea(model.getWuType(), model.getSareaid(), 0))
                    .setJMobile(model.getSMobile());

            CityIdListDTO jInfo;
            //上门取件  其他派送
            if (Arrays.asList(NumUtil.SEVEN, NumUtil.EIGHT).contains(model.getWuType())) {
                jInfo = getAreaIdByCityId(model.getSDid(), 1);
                jInfo.setAddress(model.getSAddress());
            } else {
                jInfo = getAreaInfoByArea(model.getSareaid());
            }
            m.setJCounty(jInfo.getDname())
                    .setJCity(jInfo.getZname())
                    .setJProvince(jInfo.getPname())
                    .setJAddress(jInfo.getAddress())
                    // 收件人信息
                    .setDContact(model.getRName() + getWuliuStatsArea(model.getWuType(), model.getRareaid(), 1));
            CityIdListDTO cittInfo;
            if (Objects.equals(model.getWuType(), 1)) {
                cittInfo = getAreaInfoByArea(model.getRareaid());
            } else {
                cittInfo = getAreaIdByCityId(Objects.equals(Optional.ofNullable(model.getRCityId()).orElse(0), 0) ? model.getRDid() : model.getRCityId(), null);
            }
            m.setDCounty(cittInfo.getDname())
                    .setDCity(cittInfo.getZname())
                    .setDProvince(cittInfo.getPname())
                    .setDMobile(model.getRMobile())
                    .setDAddress(model.getRAddress());
            if (Objects.equals(model.getWuType(), 1) && StringUtils.isBlank(model.getRAddress())) {
                m.setDAddress(cittInfo.getAddress());
            }

            // 寄方地址为空，则为填写地址
            if (StringUtils.isBlank(m.getJAddress())) {
                m.setJAddress(model.getSAddress());
            }
            // 内部物流 = 1’，‘订单派= 4’，‘维修单派送=5’，‘订单加急送=6’，
            // ‘良品订单派送=9’，‘物料派送 =10’ 13:租机派送
            if (!Arrays.asList(9, 10, 13).contains(model.getWuType())) {
                // 货品信息
                m.setList(Collections.emptyList());
                // 根据订单获取 bakset
                List<WuLiuBasket2Entity> dr = wuLiuBasketService.getSubBasket(model.getDanHaoBind(), null, null);
                if (CollectionUtils.isNotEmpty(dr)) {
                    List<SfCargoVO> list = new ArrayList<>();
                    dr.forEach(item ->
                            //添加到货品列表
                            list.add(new SfCargoVO()
                                    // 单价
                                    .setAmount(String.valueOf(Optional.ofNullable(item.getPrice()).orElse(BigDecimal.ZERO)))
                                    // 商品名称
                                    .setCargoName(Optional.ofNullable(item.getProductName()).orElse("").replace("&", ""))
                                    // 数量
                                    .setCount(String.valueOf(Optional.ofNullable(item.getBasketCount()).orElse(0)))
                                    // 单位
                                    .setUnit("")));
                    m.setList(list);
                }
            } else if (!Objects.equals(model.getWuType(), 10)) {
                // 获取二手良品商品信息
                m.setList(Collections.emptyList());
                // 根据订单获取 bakset
                List<WuLiuRecoverMarketInfo2Entity> dr = wuLiuRecoverMarketInfoService.getSubBasket(model.getDanHaoBind(), null);
                if (CollectionUtils.isNotEmpty(dr)) {
                    List<SfCargoVO> list = new ArrayList<>();
                    dr.forEach(item -> {
                        //添加到货品列表
                        list.add(new SfCargoVO()
                                // 单价
                                .setAmount(String.valueOf(Optional.ofNullable(item.getPrice()).orElse(BigDecimal.ZERO)))
                                // 商品名称
                                .setCargoName(item.getProductName())
                                // 数量
                                .setCount(String.valueOf(Optional.ofNullable(item.getBasketCount()).orElse(0)))    // 单位
                                .setUnit(""));
                    });
                    m.setList(list);
                }
            }

            //新增  添加物流单
            if (Objects.equals(model.getWuliuid(), 0)) {
                wlId = wuLiuService.newTransSaveWuLiu(model, areaId, username);
                if (wlId > 0) {
                    model.setWuliuid(wlId);
                    needSaveWuliu = false;
                }
            } else {
                wlId = model.getWuliuid();
            }

            // 顺丰快递 xw 开始
            // 顺丰重货/零担判断，并走顺丰快递（九机特惠）
            boolean bool = StringUtils.isNotBlank(m.getExpressType()) && m.getExpressType().contains("9386");
            if (WuLiuConstant.SHUNFENG_JIUJI.equals(model.getCom()) || bool || WuLiuConstant.JIUJI_MONTH_CUST_ID.equals(m.getCustId())) {
                String expressType = "2";
                if (StringUtils.isNotBlank(m.getExpressType()) && m.getExpressType().contains("_")) {
                    String[] expressTypeArr = m.getExpressType().split("_");
                    expressType = expressTypeArr[0];
                }
                m.setSendAreaId(model.getSareaid())
                        .setExpressType(expressType);
                m.setReceiveAreaId(model.getRareaid());

                try {
                    // hasCreateOrderAuth 为测试代码,上线时需要删除
                    R<List<SfCreateOrderResultDTO>> result = new R<>();
                    if (hasCreateOrderAuth()) {
                        result = sfLogisticsCenterCreateOrder(m, OptionalUtils.ifNotNull(model.getWuliuid(), String::valueOf));
                    }

                    if (result != null && CollectionUtils.isNotEmpty(result.getData())) {
                        for (SfCreateOrderResultDTO item : result.getData()) {
                            model.setNu(item.getMainMailNo());
                            model.setMsg(expressEnumService.getWuliuCompanyName(model.getCom()) + "运单号生成成功：" + model.getNu());
                            model.setDestcode(item.getDestCode());
                            //记录顺风电子面单信息
                            List<WuLiuShunfengNoInfoEntity> cmdList = new ArrayList<>();
                            cmdList.add(new WuLiuShunfengNoInfoEntity()
                                    .setMailNo(item.getMainMailNo())
                                    .setWuLiuId(OptionalUtils.ifNotNull(model.getWuliuid(), String::valueOf))
                                    .setDestRouteLabel(item.getDestRouteLabel())
                                    .setTwoDimensionCode(item.getTwoDimensionCode())
                                    .setSareaid(model.getSareaid())
                                    .setCustId(WuLiuConstant.JIUJI_MONTH_CUST_ID)
                                    .setAddDate(LocalDateTime.now())
                                    .setJMobile(m.getJMobile())
                                    .setDMobile(m.getDMobile())
                                    .setCkind(m.getCkind()));

                            if (CollectionUtils.isNotEmpty(cmdList)) {
                                wuLiuShunfengNoInfoService.addAll(cmdList);
                            }

                            if ("null".equals(model.getLinkType())
                                    || StringUtils.isBlank(model.getLinkType())
                                    || WuLiuConstant.ZERO_STR.equals(Optional.ofNullable(model.getLinkType()).orElse("0"))
                                    && Optional.ofNullable(model.getDanHaoBind()).orElse(0) > 0) {
                                if (WuLiuConstant.SUB_WULIU_TYPE_ARRY.contains(model.getWuType())) {
                                    // 订单派送的物流类型
                                    updteWuliuNo(expressEnumService.getWuliuCompanyName(model.getCom()), model.getNu(), Long.valueOf(model.getDanHaoBind()), username);
                                } else if (WuLiuConstant.LP_SUB_WULIU_TYPE_ARRY.contains(model.getWuType())) {
                                    // 良品订单派送的物流类型
                                    updateWuliuNo2(expressEnumService.getWuliuCompanyName(model.getCom()), model.getNu(), Long.valueOf(model.getDanHaoBind()), username);
                                }
                            }
                            WuliuUtil.sendWuliuProcessMessage(model.getWuliuid());
                        }

                    } else {
                        throw new CustomizeException((String)OptionalUtils.ifNotNull(result, R::getUserMsg));
                    }

                } catch (Exception e) {
                    emsg = e.getMessage();
                    if (e.getMessage() != null && !e.getMessage().startsWith(WuLiuConstant.NOT_AVAILABLE_FOR_DELIVERY)) {
                        // 消息推送
                        weixinAndOaMessageSend("顺丰快递单生成失败：操作人：" + userId + e.getMessage() + ";物流ID:" + model.getWuliuid() + "", 3, "", WuLiuConstant.ZLX_CH999_ID, null, null);
                        wuliudanRunErrmsg = e.getMessage();
                    }
                }
            }
            // 顺丰及顺丰 LaaS
            else if (WuLiuConstant.SHUNFENG.equals(model.getCom())) {

                if (isEnableShunfengLaas()) {
                    LogisticsBase base = areainfoService.getAreaName(model.getSareaid(), model.getRareaid());
                    // 请求参数
                    ShunFengCardVO card = shunfengLaas(m, model.getSareaid());
                    CreateShunfengLaasOrderReq req = new CreateShunfengLaasOrderReq();
                    req.setExpressType(LogisticsTypeEnum.SHUN_FENG_LAAS.getCode());
                    req.setXTenantId(0L);
                    req.setOrderNo(wlId.toString());
                    req.setSenderName(m.getJContact());
                    //缺少省份信息
                    String senderAddress = m.getJAddress();
                    if (StringUtils.isNotBlank(senderAddress) && !senderAddress.startsWith(m.getJProvince()) && !senderAddress.startsWith(Optional.ofNullable(m.getJProvince()).orElse("").replace(WuLiuConstant.PROVINCE, ""))) {
                        //缺少市级信息
                        if (!senderAddress.startsWith(m.getJCity())) {
                            //缺少区县级信息
                            if (!senderAddress.startsWith(m.getJCounty())) {
                                senderAddress = m.getJProvince() + m.getJCity() + m.getJCounty() + senderAddress;
                            } else {
                                senderAddress = m.getJProvince() + m.getJCity() + senderAddress;
                            }
                        }
                        //缺省信息
                        else {
                            senderAddress = m.getJProvince() + senderAddress;
                        }
                    }
                    req.setSenderAddress(senderAddress);
                    req.setSenderMobile(m.getJMobile());
                    req.setReceiverAddress(Optional.ofNullable(m.getDProvince()).orElse("")
                            + Optional.ofNullable(m.getDCity()).orElse("")
                            + Optional.ofNullable(m.getDCounty()).orElse("") + m.getDAddress());
                    req.setReceiverMobile(m.getDMobile());
                    req.setReceiverName(m.getDContact());
                    req.setPackageCount(NumUtil.ONE);
                    req.setSendShopId(base.getSendShopId());
                    req.setSendShopName(base.getSendShopName());
                    req.setReceiveShopId(base.getReceiveShopId());
                    req.setReceiveShopName(base.getReceiveShopName());
                    req.setDropMenuExpressType(Integer.valueOf(m.getExpressType()));
                    req.setMonthlyCard(m.getCustId());
                    req.setIsDocall(m.getDoCall());

                    // 添加签名
                    String secretByCode = secretCodeConfigService.getSecretByCode(SECRET_BY_CODE);
                    long currentTimeMillis = System.currentTimeMillis();
                    String sign = OaAuthUtil.signBySecret(secretByCode, currentTimeMillis, req);
                    OaSignReq<CreateOrderReq> param = new OaSignReq<>(sign, currentTimeMillis, req);
                    log.warn("顺丰Laas请参数:{},未签名参数:{}", JSONUtil.toJsonStr(param), JSONUtil.toJsonStr(req));
                    String result = "";
                    R<LogisticsBaseResVO> res = null;
                    try {
                        result = LogisticsHttpClient.post(JiuJiApi.CREATE_ORDER, param);
                        res = JacksonJsonUtils.toClass(result, new com.fasterxml.jackson.core.type.TypeReference<R<LogisticsBaseResVO>>() {
                        });
                    } catch (Exception e) {
                        // ignore
                        wuliudanRunErrmsg = "顺丰快递生成失败，物流中台接口调用异常：" + e.getMessage();
                        log.error("顺丰Laas快递生成失败！操作人={}，入参={}，{}", userId, JSONUtil.toJsonStr(param), Exceptions.getStackTraceAsString(e), e);
                    }
                    if (res != null) {

                        if (Objects.equals(res.getCode(), ResultCode.SUCCESS)) {

                            //记录顺风电子面单信息
                            LogisticsBaseResVO resVo = res.getData();
                            if (resVo != null) {
                                try {
                                    LogisticsBaseResVO.Extension extension = Optional.ofNullable(resVo.getExtension()).orElseGet(LogisticsBaseResVO.Extension::new);
                                    String destRouteLabel = extension.getDestRouteLabel();
                                    String twoDimensionCode = extension.getTwoDimensionCode();
                                    model.setOrgcode(extension.getOriginCode()); //原寄地编码
                                    model.setDestcode(extension.getDestCode()); //目的地编码

                                    List<WuLiuShunfengNoInfoEntity> cmdList = new ArrayList<>();
                                    cmdList.add(new WuLiuShunfengNoInfoEntity()
                                            .setMailNo(resVo.getWayBill())
                                            .setWuLiuId(OptionalUtils.ifNotNull(model.getWuliuid(), String::valueOf))
                                            .setDestRouteLabel(destRouteLabel)
                                            .setTwoDimensionCode(twoDimensionCode)
                                            .setSareaid(model.getSareaid())
                                            .setCustId(card.getCustId())
                                            .setAddDate(LocalDateTime.now())
                                            .setJMobile(m.getJMobile())
                                            .setDMobile(m.getDMobile())
                                            .setCkind(m.getCkind()));

                                    if (CollectionUtils.isNotEmpty(cmdList)) {
                                        wuLiuShunfengNoInfoService.addAll(cmdList);
                                    }

                                } catch (Exception e) {
                                    log.error("顺丰快递生成失败: 操作人={},异常={},入参={},出参={}", userId, Exceptions.getStackTraceAsString(e), JacksonJsonUtils.toJson(model), JacksonJsonUtils.toJson(resVo), e);
                                    sendTextMessage("顺丰快递生成失败，操作人=" + userId + ",异常=" + Exceptions.getStackTraceAsString(e) + ",入参=" + JacksonJsonUtils.toJson(model) + ",出参=" + JacksonJsonUtils.toJson(resVo));
                                }
                                model.setNu(resVo.getWayBill());
                                model.setMsg("顺丰快递运单号生成成功：" + model.getNu());
                                model.setYuejiekahao(m.getCustId());
                                if (!needSaveWuliu) {
                                    // 更新顺丰快递单号
                                    lambdaUpdate().set(WuLiuEntity::getNu, model.getNu()).eq(WuLiuEntity::getId, model.getWuliuid()).update();
                                }
                                isCreateWuliu = true;
                                if ( StringUtils.isBlank(model.getLinkType()) || "null".equals(model.getLinkType()) || WuLiuConstant.ZERO_STR.equals(Optional.ofNullable(model.getLinkType()).orElse("0"))) {
                                    if (WuLiuConstant.SUB_WULIU_TYPE_ARRY.contains(model.getWuType())) {
                                        // 订单派送的物流类型
                                        updteWuliuNo(expressEnumService.getWuliuCompanyName(model.getCom()), model.getNu(), Long.valueOf(model.getDanHaoBind()), username);
                                    } else if (WuLiuConstant.LP_SUB_WULIU_TYPE_ARRY.contains(model.getWuType())) {
                                        // 良品订单派送的物流类型
                                        updateWuliuNo2(expressEnumService.getWuliuCompanyName(model.getCom()), model.getNu(), Long.valueOf(model.getDanHaoBind()), username);
                                    }
                                }

                            } else {
                                log.error("顺丰快递生成失败！操作人={},入参={},中台入参={},中台出参={}", userId, JacksonJsonUtils.toJson(model), JacksonJsonUtils.toJson(param), JacksonJsonUtils.toJson(res));
                                sendTextMessage("顺丰快递生成失败，操作人=" + userId + "，入参=" + JacksonJsonUtils.toJson(model) + "，出参=" + JacksonJsonUtils.toJson(resVo));
                                wuliudanRunErrmsg = "顺丰快递生成失败";
                            }
                            WuliuUtil.sendWuliuProcessMessage(model.getWuliuid());

                        } else {
                            log.error("顺丰快递生成失败！操作人={},入参={},中台入参={},中台出参={}", userId, JacksonJsonUtils.toJson(model),JacksonJsonUtils.toJson(param), JacksonJsonUtils.toJson(res));
                            sendTextMessage("顺丰快递生成失败，操作人=" + userId + ",DATA=" + JacksonJsonUtils.toJson(res));
                            wuliudanRunErrmsg = res.getUserMsg();
                        }
                    } else {
                        log.error("顺快递生成失败！");
                        sendTextMessage("顺丰快递生成失败:操作人："+ userId + ",DATA=null");
                        wuliudanRunErrmsg = "顺丰快递生成失败";

                    }
                }
                // 顺丰快递 xw 结束

                else {
                    //企业号发送失败再使用微信号发送
                    try {
                        // hasCreateOrderAuth 为测试代码,上线时需要删除
                        String xml = "";
                        if (hasCreateOrderAuth()) {
                            xml = sendToCreateOrder(m, model.getSareaid());
                        }
                        if (StringUtils.isNotBlank(xml) && !xml.contains("远程服务器返回错误")) {

                            Document document = XmlUtils.toDocument(xml);
                            Element root = document.getRootElement();
                            Element head = root.element("Head");
                            if (("OK").equals(head.getTextTrim())) {
                                Element body = root.element("Body");
                                Element xn = body.element("OrderResponse");
                                model.setNu(xn.attributeValue("mailno"));
                                model.setOrgcode(xn.attributeValue("origincode")); //原寄地编码
                                model.setDestcode(xn.attributeValue("destcode")); //目的地编码
                                model.setYuejiekahao(m.getCustId());
                                if (StringUtils.isBlank(model.getLinkType()) || "null".equals(model.getLinkType()) || WuLiuConstant.ZERO_STR.equals(Optional.ofNullable(model.getLinkType()).orElse("0"))) {
                                    if (WuLiuConstant.SUB_WULIU_TYPE_ARRY.contains(model.getWuType())) {
                                        // 订单派送的物流类型
                                        updteWuliuNo(expressEnumService.getWuliuCompanyName(model.getCom()), model.getNu(), Long.valueOf(model.getDanHaoBind()), username);
                                    } else if (WuLiuConstant.LP_SUB_WULIU_TYPE_ARRY.contains(model.getWuType())) {
                                        // 良品订单派送的物流类型
                                        updateWuliuNo2(expressEnumService.getWuliuCompanyName(model.getCom()), model.getNu(), Long.valueOf(model.getDanHaoBind()), username);
                                    }
                                }
                            } else if ("ERR".equals(head.getTextTrim())) {
                                wuliudanRunErrmsg = root.element("ERROR").getTextTrim();
                            }

                        }
                    } catch (Exception e) {
                        emsg = e.getMessage();
                        if (emsg != null && !emsg.startsWith(WuLiuConstant.NOT_AVAILABLE_FOR_DELIVERY)) {
                            weixinAndOaMessageSend("顺丰快递单生成失败：" + e.getMessage() + ";物流ID:" + model.getWuliuid() + "", 3, "", WuLiuConstant.ZLX_CH999_ID, null, null);
                        }
                        wuliudanRunErrmsg = e.getMessage();
                    }

                    if (!needSaveWuliu) {
                        // 更新顺丰快递单号
                        lambdaUpdate().set(WuLiuEntity::getNu, model.getNu()).eq(WuLiuEntity::getId, model.getWuliuid()).update();
                    }
                    isCreateWuliu = true;
                }
            }
        }
        // 顺丰逻辑 结束

        // 中通逻辑 开始
        else if (isCreateOrder && WuLiuConstant.ZHONGTONG.equals(model.getCom()) && StringUtils.isBlank(model.getNu())) {
            if (Objects.equals(model.getSareaid(), WuLiuConstant.AREA_DC)) {
                model.setSName(areaSubject.getPrintName())
                        .setSMobile(WuLiuConstant.DC_S_MOBLIE);
            }
            if (StringUtils.isBlank(model.getSMobile()) && Objects.equals(model.getWuType(), 4)) {
                model.setSName(areaSubject.getPrintName())
                        .setSMobile(areaInfo.getCompanyTel1());
            }
            OrderGroupDTO conmodel = new OrderGroupDTO();
            // 订单号
            conmodel.setId(OptionalUtils.ifNotNull(model.getDanHaoBind(), String::valueOf));
            if (Objects.equals(model.getWuType(), 1)) {
                conmodel.setId(OptionalUtils.ifNotNull(model.getWuliuid(), String::valueOf));
            }

            // 其他物流，单号使用物流单号，先生成物流单，才能生成快运单号
            if (Objects.equals(model.getWuType(), 8) && Objects.equals(model.getDanHaoBind(), 0)) {
                conmodel.setId("o" + model.getWuliuid());
            }

            // 订单备注
            conmodel.setRemark("请勿摔打,保持整洁。");
            // 寄件人信息
            OrderSenderDTO sender = new OrderSenderDTO();
            conmodel.setSender(sender);
            CityIdListDTO jinfo;
            // 上门取件  其他派送
            if (Arrays.asList(NumUtil.SEVEN, NumUtil.EIGHT).contains(model.getWuType())) {
                jinfo = getAreaIdByCityId(model.getSDid(), null);
            } else {
                jinfo = getAreaInfoByArea(model.getSareaid());
            }
            // 发货人所在公司
            conmodel.getSender().setCompany(areaSubject.getPrintName());
            // 发货人姓名
            conmodel.getSender().setName(model.getSName() + getWuliuStatsArea(model.getWuType(), model.getSareaid(), 0));
            // 发货人手机号
            conmodel.getSender().setMobile(model.getSMobile());
            // 发货人所在城市
            conmodel.getSender().setCity(jinfo.getPname() + "," + jinfo.getZname() + "," + jinfo.getDname());
            // 发货人地址
            conmodel.getSender().setAddress(jinfo.getAddress());

            // 寄方地址为空，则为填写地址
            if (StringUtils.isBlank(m.getJAddress()) && !StringUtils.isBlank(model.getSAddress())) {
                conmodel.getSender().setAddress(model.getSAddress());
            }

            //收件人信息
            CityIdListDTO cittInfo;
            if (Objects.equals(model.getWuType(), 1)) {
                cittInfo = getAreaDetailInfoByArea(model.getRareaid());
            } else {
                cittInfo = getAreaDetailIdByCityId(Objects.equals(model.getRCityId(), 0) ? model.getRDid() : model.getRCityId());
            }
            // 收件人
            OrderReceiverDTO receiver = new OrderReceiverDTO();
            conmodel.setReceiver(receiver);
            conmodel.getReceiver().setName(model.getRName() + getWuliuStatsArea(model.getWuType(), model.getRareaid(), 1));
            conmodel.getReceiver().setMobile(model.getRMobile());
            // 收件人所在地
            conmodel.getReceiver().setCity(cittInfo.getPname() + "," + cittInfo.getZname() + "," + cittInfo.getDname());
            // 详细地址
            conmodel.getReceiver().setAddress(model.getRAddress());
            if (Objects.equals(model.getWuType(), 1) && StringUtils.isBlank(model.getRAddress())) {
                conmodel.getReceiver().setAddress(cittInfo.getAddress());
            }
            // 商品信息
            // 根据订单获取 bakset
            conmodel.setOrderSum(BigDecimal.ZERO);
            if (!Objects.equals(model.getWuType(), 9) && !Objects.equals(model.getWuType(), 10)) {
                List<WuLiuBasket2Entity> dr = wuLiuBasketService.getSubBasket(model.getDanHaoBind(), null, null);

                BigDecimal total = BigDecimal.ZERO;
                for (WuLiuBasket2Entity entity : dr) {
                    total = total.add(entity.getPrice().multiply(BigDecimal.valueOf(entity.getBasketCount())));
                }
                // 订单总金额
                conmodel.setOrderSum(total);
            } else if (!Objects.equals(model.getWuType(), 10)) {
                // 获取二手良品商品列表
                List<WuLiuRecoverMarketInfo2Entity> dr = wuLiuRecoverMarketInfoService.getSubBasket(model.getDanHaoBind(), null);
                BigDecimal total = BigDecimal.ZERO;
                for (WuLiuRecoverMarketInfo2Entity entity : dr) {
                    total = total.add(entity.getPrice().multiply(BigDecimal.valueOf(entity.getBasketCount())));
                }
                // 订单总金额
                conmodel.setOrderSum(total);
            }
            //创建物流单
            if (Objects.equals(model.getWuliuid(), 0)) {
                wlId = wuLiuService.newTransSaveWuLiu(model, areaId, username);
                needSaveWuliu = false;
                if (wlId > 0) {
                    model.setWuliuid(wlId);
                }
            }

            String jsonStr = "";
            try {
                OrderGroupResultDTO data = new OrderGroupResultDTO();


                // hasCreateOrderAuth 为测试代码,上线时需要删除
                if (hasCreateOrderAuth()) {
                    // 是否启用新接口
                    if (WuLiuConstant.ZHONGTONG_IS_NEW_PORT) {
                        data = createOrderGroupNew(conmodel, model.getSareaid());
                    } else {
                        jsonStr = createOrderGroup(conmodel, model.getSareaid());
                        log.info("中通快递单创建返回数据,{}", jsonStr);
                        data = JSONUtil.toBean(jsonStr, OrderGroupResultDTO.class);
                    }
                }

                if (data != null && data.getResult() != null && data.getData() != null) {
                    if (StringUtils.isBlank(data.getData().getBillCode())) {
                        throw new CustomizeException(data.getData().getMessage());
                    }
                    // 快递单号
                    model.setNu(data.getData().getBillCode())
                            // 网点名称
                            .setOrgcode(data.getData().getSiteName());
                    model.setMsg(expressEnumService.getWuliuCompanyName(model.getCom()) + "运单号生成成功：" + model.getNu());
                    if (WuLiuConstant.ZHONGTONG_IS_NEW_PORT) {
                        model.setDestcode(data.getData().getBigMark());
                    } else {
                        model.setDestcode(markGetmark(conmodel.getSender().getCity(), conmodel.getSender().getAddress(), conmodel.getReceiver().getCity(), conmodel.getReceiver().getAddress(), data.getData().getBillCode(), false)); //大头笔
                    }
                    model.setYuejiekahao("");
                    //同步快递单到中台
                    WuliuUtil.saveExpressOrder(model, conmodel);
                    WuliuUtil.sendWuliuProcessMessage(model.getWuliuid());
                } else {
                    String errmessage = data.getMessage();
                    if (data.getData() != null && StringUtils.isNotBlank(data.getData().getMessage())) {
                        errmessage += ("," + data.getData().getMessage());
                    }
                    throw new CustomizeException(errmessage);
                }
            } catch (Exception e) {
                log.error("中通类型物流单调用失败:{}", Exceptions.getStackTraceAsString(e), e);
                if (jsonStr.indexOf(':') > -1 && (jsonStr.length() > (jsonStr.lastIndexOf(':') + 2))) {
                    String tmsg = jsonStr.substring(jsonStr.lastIndexOf(':') + 2);
                    emsg = tmsg.substring(0, tmsg.length() - 2);
                } else {
                    emsg = e.getMessage();
                }
            }

            // 修改订单收货地址物流单号
            if (StringUtils.isBlank(model.getLinkType()) || "null".equals(model.getLinkType()) || WuLiuConstant.ZERO_STR.equals(Optional.ofNullable(model.getLinkType()).orElse("0"))) {
                if (WuLiuConstant.SUB_WULIU_TYPE_ARRY.contains(model.getWuType())) {
                    // 订单派送的物流类型
                    updteWuliuNo(expressEnumService.getWuliuCompanyName(model.getCom()), model.getNu(), OptionalUtils.ifNotNull(model.getDanHaoBind(), Long::valueOf), username);
                } else if (WuLiuConstant.LP_SUB_WULIU_TYPE_ARRY.contains(model.getWuType())) {
                    // 良品订单派送的物流类型
                    updateWuliuNo2(expressEnumService.getWuliuCompanyName(model.getCom()), model.getNu(), OptionalUtils.ifNotNull(model.getDanHaoBind(), Long::valueOf), username);
                }
            }
            isCreateWuliu = true;
        }
        // 中通逻辑 结束

        // 物流中台中通逻辑 开始
        else if (isCreateOrder && WuLiuConstant.ZHONGTONG_NEW.equals(model.getCom()) && StringUtils.isBlank(model.getNu())) {
            //校验ztkd权限
            //https://jiuji.yuque.com/docs/share/edd37a11-0ad8-4217-80dc-777387e93725?#
            if (SysUtils.hasNotRank(WuLiuConstant.RANK_ZTKD)) {
                throw new CustomizeException("您无权限使用中通快递新下单，权值ztkd");
            }
            if (Objects.equals(model.getSareaid(), WuLiuConstant.AREA_DC)) {
                model.setSName(areaSubject.getPrintName())
                        .setSMobile(WuLiuConstant.DC_S_MOBLIE);
            }
            if (StringUtils.isBlank(model.getSMobile()) && Objects.equals(model.getWuType(), 4)) {
                model.setSName(areaSubject.getPrintName())
                        .setSMobile(areaInfo.getCompanyTel1());
            }
            CreateOrderReq createOrderReq = new CreateOrderReq();
            // 订单备注
            createOrderReq.setRemark("请勿摔打,保持整洁。");
            CityIdListDTO jinfo;
            // 上门取件  其他派送
            if (Arrays.asList(NumUtil.SEVEN, NumUtil.EIGHT).contains(model.getWuType())) {
                jinfo = getAreaIdByCityId(model.getSDid(), null);
            } else {
                jinfo = getAreaInfoByArea(model.getSareaid());
            }
            // 发货人姓名
            createOrderReq.setSenderName(model.getSName() + getWuliuStatsArea(model.getWuType(), model.getSareaid(), 0));
            // 发货人手机号
            createOrderReq.setSenderMobile(model.getSMobile());
            createOrderReq.setSenderProvinceName(jinfo.getPname());
            createOrderReq.setSenderCityName(jinfo.getZname());
            createOrderReq.setSenderCountyName(jinfo.getDname());
            // 发货人地址
            createOrderReq.setSenderAddress(jinfo.getAddress());
            String saddress = WuliuAddressUtil.getAddress(model.getSAddress(), jinfo);
            createOrderReq.setSenderAddress(saddress);
            //获取中通面单账号
            String zhongTongAccount = WuliuUtil.getZhongTongAccount(jinfo.getPname());
            createOrderReq.setMonthlyCard(zhongTongAccount);

            //收件人信息
            CityIdListDTO cittInfo;
            if (Objects.equals(model.getWuType(), 1)) {
                cittInfo = getAreaDetailInfoByArea(model.getRareaid());
            } else {
                cittInfo = getAreaDetailIdByCityId(Objects.isNull(model.getRCityId()) || Objects.equals(model.getRCityId(), 0) ? model.getRDid() : model.getRCityId());
            }
            // 收件人
            createOrderReq.setReceiverName(model.getRName() + getWuliuStatsArea(model.getWuType(), model.getRareaid(), 1));
            createOrderReq.setReceiverMobile(model.getRMobile());
            // 收件人所在地
            createOrderReq.setReceiverProvinceName(cittInfo.getPname());
            createOrderReq.setReceiverCityName(cittInfo.getZname());
            createOrderReq.setReceiverCountyName(cittInfo.getDname());
            // 详细地址
            String raddress = WuliuAddressUtil.getAddress(model.getRAddress(), cittInfo);
            createOrderReq.setReceiverAddress(raddress);
            if (Objects.equals(model.getWuType(), 1) && StringUtils.isBlank(model.getRAddress())) {
                createOrderReq.setReceiverAddress(cittInfo.getAddress());
            }
            LogisticsBase base = areainfoService.getAreaName(model.getSareaid(), model.getRareaid());
            createOrderReq.setSendShopId(base.getSendShopId());
            createOrderReq.setReceiveShopId(base.getReceiveShopId());
            createOrderReq.setSendShopName(base.getSendShopName());
            createOrderReq.setReceiveShopName(base.getReceiveShopName());
            // 商品信息
            // 根据订单获取 bakset
            createOrderReq.setAmount(BigDecimal.ZERO);
            if (!Objects.equals(model.getWuType(), 9) && !Objects.equals(model.getWuType(), 10)) {
                List<WuLiuBasket2Entity> dr = wuLiuBasketService.getSubBasket(model.getDanHaoBind(), null, null);

                BigDecimal total = BigDecimal.ZERO;
                for (WuLiuBasket2Entity entity : dr) {
                    total = total.add(entity.getPrice().multiply(BigDecimal.valueOf(entity.getBasketCount())));
                }
                // 订单总金额
                createOrderReq.setAmount(total);
            } else if (!Objects.equals(model.getWuType(), 10)) {
                // 获取二手良品商品列表
                List<WuLiuRecoverMarketInfo2Entity> dr = wuLiuRecoverMarketInfoService.getSubBasket(model.getDanHaoBind(), null);
                BigDecimal total = BigDecimal.ZERO;
                for (WuLiuRecoverMarketInfo2Entity entity : dr) {
                    total = total.add(entity.getPrice().multiply(BigDecimal.valueOf(entity.getBasketCount())));
                }
                // 订单总金额
                createOrderReq.setAmount(total);
            }
            //创建物流单
            if (Objects.equals(model.getWuliuid(), 0)) {
                wlId = wuLiuService.newTransSaveWuLiu(model, areaId, username);
                needSaveWuliu = false;
                if (wlId > 0) {
                    model.setWuliuid(wlId);
                }
            }

            try {
                createOrderReq.setOrderNo(model.getWuliuid() + "");
                R<CreateOrderResV2> wlres = logisticsExpressService.createOrder(SysUtils.getUser(), createOrderReq, WuLiuConstant.ZHONGTONG_NEW_EXPRESS_TYPE);
                if (Objects.equals(wlres.getCode(), 0) && Objects.nonNull(wlres.getData())) {
                    // 快递单号
                    model.setNu(wlres.getData().getWaybillNo());
                    // 网点名称
                    BigMarkInfoRes bigMarkInfo = Objects.nonNull(wlres.getData().getBigMarkInfo()) ? wlres.getData().getBigMarkInfo() : new BigMarkInfoRes();
                    String orgCode = StringUtils.isNotBlank(wlres.getData().getSiteName()) ? wlres.getData().getSiteName() : wlres.getData().getSiteCode();
                    model.setOrgcode(orgCode);
                    model.setDestcode(bigMarkInfo.getMark());
                    model.setMsg(expressEnumService.getWuliuCompanyName(model.getCom()) + "运单号生成成功：" + model.getNu());
                    model.setYuejiekahao(createOrderReq.getMonthlyCard());
                    //中通面单
                    ztoBillInfoService.saveBillInfo(model,wlres.getData());
                    WuliuUtil.sendWuliuProcessMessage(model.getWuliuid());
                } else {
                    String errmessage = wlres.getUserMsg();
                    throw new CustomizeException(errmessage);
                }
            } catch (Exception e) {
                log.error("调用中台创建中通订单异常", e);
                emsg = e.getMessage();
            }

            // 修改订单收货地址物流单号
            if (StringUtils.isBlank(model.getLinkType()) || "null".equals(model.getLinkType()) || WuLiuConstant.ZERO_STR.equals(Optional.ofNullable(model.getLinkType()).orElse("0"))) {
                if (WuLiuConstant.SUB_WULIU_TYPE_ARRY.contains(model.getWuType())) {
                    // 订单派送的物流类型
                    updteWuliuNo(expressEnumService.getWuliuCompanyName(model.getCom()), model.getNu(), OptionalUtils.ifNotNull(model.getDanHaoBind(), Long::valueOf), username);
                } else if (WuLiuConstant.LP_SUB_WULIU_TYPE_ARRY.contains(model.getWuType())) {
                    // 良品订单派送的物流类型
                    updateWuliuNo2(expressEnumService.getWuliuCompanyName(model.getCom()), model.getNu(), OptionalUtils.ifNotNull(model.getDanHaoBind(), Long::valueOf), username);
                }
            }
            isCreateWuliu = true;
        }
        // 物流中台中通逻辑 结束

        // 美团 开始
        else if (isCreateOrder
                && Arrays.asList(WuLiuConstant.MEITUAN, WuLiuConstant.MEITUAN_FASTEST, WuLiuConstant.MEITUAN_JIUJI).contains(model.getCom())
                && StringUtils.isBlank(model.getNu())) {
            if (Objects.equals(model.getSareaid(), WuLiuConstant.AREA_DC)) {
                model.setSName(areaSubject.getPrintName())
                        .setSMobile(WuLiuConstant.DC_S_MOBLIE);
            }

            if (StringUtils.isBlank(model.getSMobile()) && Objects.equals(model.getWuType(), 4)) {
                model.setSName(areaSubject.getPrintName())
                        .setSMobile(areaInfo.getCompanyTel1());
            }

            Map<Integer, String> meituanArea = getMeituanAreaConfig();

            if (meituanArea.containsKey(model.getSareaid()) || WuLiuConstant.MEITUAN_JIUJI.equals(model.getCom())) {
                MeituanItemDTO item = new MeituanItemDTO();
                // 订单号
                item.setOrderId(String.valueOf(model.getDanHaoBind()));
                if (Objects.equals(model.getWuliuid(), 0)) {
                    wlId = wuLiuService.newTransSaveWuLiu(model, areaId, username);
                    item.setOrderId(String.valueOf(wlId));
                    needSaveWuliu = false;
                } else {
                    item.setOrderId(String.valueOf(model.getWuliuid()));
                }

                // 其他物流，单号使用物流单号，先生成物流单，才能生成快运单号
                if (Objects.equals(model.getWuType(), 8) && Objects.equals(model.getDanHaoBind(), 0)) {
                    item.setOrderId(String.valueOf(model.getDanHaoBind()));
                }
                item.setDeliveryId(OptionalUtils.ifNotNull(item.getOrderId(), Long::valueOf));
                if (Objects.equals(model.getDanHaoBind(), 0)) {
                    item.setOrderId(String.valueOf(model.getWuliuid()));
                    item.setDeliveryId(OptionalUtils.ifNotNull(model.getWuliuid(), Long::valueOf));
                }
                // 订单备注
                item.setNote("请勿摔打,保持整洁。");
                if (!WuLiuConstant.MEITUAN_JIUJI.equals(model.getCom())) {
                    item.setShopId(meituanArea.get(model.getSareaid()));
                }
                item.setDeliveryServiceCode(4011);
                if (WuLiuConstant.MEITUAN_FASTEST.equals(model.getCom())) {
                    item.setDeliveryServiceCode(4001);
                }

                CityIdListDTO cittInfo;
                Areainfo rAreaObj = null;
                if (Objects.equals(model.getWuType(), 1)) {
                    rAreaObj = Optional.ofNullable(areaInfoService.getAreaInfoByAreaId2(model.getRareaid())).orElseGet(Areainfo::new);
                    cittInfo = getAreaDetailInfoByArea(model.getRareaid());
                } else {
                    cittInfo = getAreaDetailIdByCityId(Objects.equals(model.getRCityId(), 0) ? model.getRDid() : model.getRCityId());
                }
                item.setReceiverName(model.getRName());
                item.setReceiverAddress(cittInfo.getPname() + cittInfo.getZname() + cittInfo.getDname() + model.getRAddress());
                item.setReceiverPhone(model.getRMobile());

                if (Objects.equals(model.getWuType(), 1) && StringUtils.isBlank(model.getRAddress())) {
                    item.setReceiverAddress(cittInfo.getPname() + cittInfo.getZname() + cittInfo.getDname() + cittInfo.getAddress());
                }
                Coordinate coordinate = null;
                if (rAreaObj == null) {
                    coordinate = AtlasUtil.translateAddressByThreeMap(item.getReceiverAddress(), "");
                } else {
                    coordinate = new Coordinate(rAreaObj.getPosition());
                }
                if (Objects.isNull(coordinate)) {
                    throw new CustomizeException("获取收件位置失败，请检查收件地址（"+item.getReceiverAddress()+"）是否正确");
                }
                coordinate = CoordinateUtil.wgs2gcj(coordinate);
                item.setReceiverLng(BigDecimal.valueOf(coordinate.getLongitude() * Math.pow(10, 6)).intValue());
                item.setReceiverLat(BigDecimal.valueOf(coordinate.getLatitude() * Math.pow(10, 6)).intValue());

                item.setCoordinateType(0);
                // 商品信息 根据订单获取bakset
                item.setGoodsValue(BigDecimal.ZERO);
                if (Objects.equals(model.getWuType(), 9) || Objects.equals(model.getSubKinds(), 2)) {
                    // 获取二手良品商品列表
                    List<WuLiuRecoverMarketInfo2Entity> list = wuLiuRecoverMarketInfoService.getSubBasket(model.getDanHaoBind(), null);
                    BigDecimal total = BigDecimal.ZERO;
                    if (CollectionUtils.isNotEmpty(list)) {
                        for (WuLiuRecoverMarketInfo2Entity entity : list) {
                            total = total.add(Optional.ofNullable(entity.getPrice()).orElse(BigDecimal.ZERO)
                                    .multiply(BigDecimal.valueOf(Optional.ofNullable(entity.getBasketCount()).orElse(0))));
                        }
                        // 订单总金额
                        item.setGoodsValue(total);
                    }
                } else if (!Arrays.asList(5, 10).contains(model.getWuType())) {
                    List<WuLiuBasket2Entity> list = wuLiuBasketService.getSubBasket(model.getDanHaoBind(), null, null);
                    BigDecimal total = BigDecimal.ZERO;
                    if (CollectionUtils.isNotEmpty(list)) {
                        for (WuLiuBasket2Entity entity : list) {
                            total = total.add(entity.getPrice().multiply(BigDecimal.valueOf(entity.getBasketCount())));
                        }
                        // 订单总金额
                        item.setGoodsValue(total);
                    }
                }

                if (item.getGoodsValue().compareTo(BigDecimal.valueOf(5000)) > 0) {
                    item.setGoodsValue(BigDecimal.valueOf(5000));
                }

                if (model.getWeight() != null) {
                    item.setGoodsWeight(model.getWeight());
                } else {
                    item.setGoodsWeight(BigDecimal.valueOf(5));
                }
                try {
                    SaasPlatformDTO saasPlatform = null;
                    if (WuLiuConstant.MEITUAN_JIUJI.equals(model.getCom())) {
                        saasPlatform = new SaasPlatformDTO()
                                .setSaasAreaid(model.getSareaid())
                                .setSaasTenant(areaSubject.getXtenant())
                                .setWuliuId(wlId);

                    }
                    LogisticsBase base = areainfoService.getAreaName(model.getSareaid(), model.getRareaid());
                    item.setXTenantId(xTenant.longValue());
                    item.setExpressType(LogisticsTypeEnum.MEI_TUAN.getCode());
                    item.setReceiveShopId(base.getReceiveShopId());
                    item.setReceiveShopName(base.getReceiveShopName());
                    item.setSendShopId(base.getSendShopId());
                    item.setSendShopName(base.getSendShopName());

                    CreateReturnResultDTO result = new CreateReturnResultDTO();
                    // hasCreateOrderAuth 为测试代码,上线时需要删除
                    if (hasCreateOrderAuth()) {
                        result = createMeiTuanOrder(item, saasPlatform, model);
                    }

                    if (Objects.equals(result.getCode(), 0)) {
                        // 快递单号
                        model.setNu(result.getMtPeisongId());
                        WuliuUtil.sendWuliuProcessMessage(model.getWuliuid());
                    } else {
                        emsg = result.getMessage();
                    }
                } catch (Exception e) {
                    emsg = e.getMessage();
                }

                if ((StringUtils.isBlank(model.getLinkType()) || "null".equals(model.getLinkType()) || WuLiuConstant.ZERO_STR.equals(Optional.ofNullable(model.getLinkType()).orElse("0")))
                        && !Objects.equals(model.getWuType(), 1) && model.getDanHaoBind() > 0) {

                    if (WuLiuConstant.SUB_WULIU_TYPE_ARRY.contains(model.getWuType())) {
                        // 订单派送的物流类型
                        updteWuliuNo(expressEnumService.getWuliuCompanyName(model.getCom()), model.getNu(), OptionalUtils.ifNotNull(model.getDanHaoBind(), Long::valueOf), username);
                    } else if (WuLiuConstant.LP_SUB_WULIU_TYPE_ARRY.contains(model.getWuType())) {
                        // 良品订单派送的物流类型
                        updateWuliuNo2(expressEnumService.getWuliuCompanyName(model.getCom()), model.getNu(), OptionalUtils.ifNotNull(model.getDanHaoBind(), Long::valueOf), username);
                    }
                }

                if (!needSaveWuliu) {
                    // 更新美团的单号
                    lambdaUpdate().set(WuLiuEntity::getNu, model.getNu()).eq(WuLiuEntity::getId, model.getWuliuid()).update();
                }
                isCreateWuliu = true;
            } else {
                if(!meituanArea.containsKey(model.getSareaid())) {
                    emsg = "未配置门店";
                }
            }
        }
        // 美团 结束

        // EMS 快递单生产接入 开始
        else if (isCreateOrder && WuLiuConstant.EMS.equals(model.getCom()) && StringUtils.isBlank(model.getNu())) {
            if (Objects.equals(model.getSareaid(), WuLiuConstant.AREA_DC)) {
                model.setSName(areaSubject.getPrintName());
                model.setSMobile(WuLiuConstant.DC_S_MOBLIE);
            }

            if (StringUtils.isBlank(model.getSMobile()) && Objects.equals(model.getWuType(), 4)) {
                model.setSName(areaSubject.getPrintName());
                model.setSMobile(areaInfo.getCompanyTel1());
            }

            //寄件人信息
            CityIdListDTO jinfo;
            //上门取件  其他派送
            if (Arrays.asList(NumUtil.SEVEN, NumUtil.EIGHT).contains(model.getWuType())) {
                jinfo = getAreaIdByCityId(model.getSDid(), null);
            } else {
                jinfo = getAreaInfoByArea(model.getSareaid());
            }
            AddressDTO sender = new AddressDTO();
            sender.setName(model.getSName());
            sender.setMobile(model.getSMobile());
            sender.setProv(jinfo.getPname());
            sender.setCity(jinfo.getZname());
            sender.setCounty(jinfo.getDname());
            sender.setAddress(jinfo.getAddress());

            if (StringUtils.isBlank(sender.getAddress())) {
                sender.setAddress(model.getSAddress());
            }

            //收件人信息
            CityIdListDTO cittInfo;
            if (Objects.equals(model.getWuType(), 1)) {
                cittInfo = getAreaDetailInfoByArea(model.getRareaid());
            } else {
                cittInfo = getAreaDetailIdByCityId(Objects.equals(Optional.ofNullable(model.getRCityId()).orElse(0), 0) ? model.getRDid() : model.getRCityId());
            }

            if (Objects.equals(model.getWuType(), 1)) {
                model.setRAddress(cittInfo.getAddress());
            }

            AddressDTO receiver = new AddressDTO();
            receiver.setName(model.getRName());
            receiver.setMobile(model.getRMobile());
            receiver.setProv(cittInfo.getPname());
            receiver.setCity(cittInfo.getZname());
            receiver.setCounty(cittInfo.getDname());
            receiver.setAddress(model.getRAddress());

            // 订单号
            Integer orderId = model.getDanHaoBind();
            if (Objects.equals(model.getWuliuid(), 0)) {
                wlId = wuLiuService.newTransSaveWuLiu(model, areaId, username);
                orderId = wlId;
                needSaveWuliu = false;
            } else {
                orderId = model.getWuliuid();
            }

            if (Objects.equals(model.getWuType(), 8) && Objects.equals(model.getDanHaoBind(), 0)) {
                orderId = model.getWuliuid();
            }

            OrderInfoDTO orderInfo = new OrderInfoDTO();
            orderInfo.setOrderId(String.valueOf(orderId));
            orderInfo.setSender(sender);
            orderInfo.setReceiver(receiver);

            //查询寄件地址与dc的距离,距离dc（dc的定位）300米内的寄件地址可以下单
            String dcPosition = Optional.ofNullable(areainfoService.getAreaInfoByAreaId2(WuLiuConstant.AREA_DC)).map(Areainfo::getPosition).orElse("0,0");
            Coordinate sendCoordinate = WuLiuTypeEnum.OTHERS.getCode().equals(model.getWuType()) ? new Coordinate(model.getSendPosition()) : SpringUtil.getBean(IWuliuAddressService.class).getAreaCoordinateV2(model.getSareaid(),jinfo.getAddress(),model.getSCityId(),1);
            double distance = AtlasUtil.getDistance(new Coordinate(dcPosition), sendCoordinate);
            // 地区
            if (distance <= NumberConstant.THREE * NumberConstant.ONE_HUNDRED) {
                OrderResultDTO emsResultDTO = emsCreateOrder(orderInfo);
                if (Objects.equals(emsResultDTO.getCode(), 0)) {
                    model.setNu(emsResultDTO.getLastOrderid());
                    if (StringUtils.isBlank(model.getLinkType()) || "null".equals(model.getLinkType()) || WuLiuConstant.ZERO_STR.equals(Optional.ofNullable(model.getLinkType()).orElse("0"))) {
                        if (WuLiuConstant.SUB_WULIU_TYPE_ARRY.contains(model.getWuType())) {
                            // 订单派送的物流类型
                            updteWuliuNo(expressEnumService.getWuliuCompanyName(model.getCom()), model.getNu(), OptionalUtils.ifNotNull(model.getDanHaoBind(), Long::valueOf), username);
                        } else if (WuLiuConstant.LP_SUB_WULIU_TYPE_ARRY.contains(model.getWuType())) {
                            // 良品订单派送的物流类型
                            updateWuliuNo2(expressEnumService.getWuliuCompanyName(model.getCom()), model.getNu(), OptionalUtils.ifNotNull(model.getDanHaoBind(), Long::valueOf), username);
                        }
                    }

                    if (!needSaveWuliu) {
                        lambdaUpdate().set(WuLiuEntity::getNu, model.getNu()).eq(WuLiuEntity::getId, model.getWuliuid()).update();
                    }
                    isCreateWuliu = true;
                    //同步快递单到中台
                    WuliuUtil.saveExpressOrder(model, orderInfo);
                    WuliuUtil.sendWuliuProcessMessage(model.getWuliuid());
                } else {
                    writewuliulogs(model.getWuliuid(), username, expressEnumService.getWuliuCompanyName(model.getCom()) + "运单号生成接口错误：" + emsResultDTO.getMessage(), null);
                }
            } else {
                throw new CustomizeException("与dc距离超过300m，不可使用EMS");
            }
        }
        // EMS 快递单生产接入 结束

        // 京东物流 开始
        else if (isCreateOrder && Arrays.asList(WuLiuConstant.JINGDONG, WuLiuConstant.JINGDONG_JIUJI).contains(model.getCom()) && StringUtils.isBlank(model.getNu())) {
            if (Objects.equals(model.getSareaid(), WuLiuConstant.AREA_DC)) {
                model.setSName(areaSubject.getPrintName());
                model.setSMobile(WuLiuConstant.DC_S_MOBLIE);
            }

            if (StringUtils.isBlank(model.getSMobile()) && Objects.equals(model.getWuType(), 4)) {
                model.setSName(areaSubject.getPrintName());
                model.setSMobile(areaInfo.getCompanyTel1());
            }

            //寄件人信息
            CityIdListDTO jinfo;
            //上门取件  其他派送
            if (Arrays.asList(NumUtil.SEVEN, NumUtil.EIGHT).contains(model.getWuType())) {
                jinfo = getAreaIdByCityId(model.getSDid(), null);
            } else {
                jinfo = getAreaInfoByArea(model.getSareaid());
            }

            AddressDTO sender = new AddressDTO();
            sender.setName(model.getSName());
            sender.setMobile(model.getSMobile());
            sender.setProv(jinfo.getPname());
            sender.setCity(jinfo.getZname());
            sender.setCounty(jinfo.getDname());
            sender.setAddress(jinfo.getAddress());

            if (StringUtils.isBlank(sender.getAddress())) {
                sender.setAddress(model.getSAddress());
            }

            // 收件人信息
            CityIdListDTO cittInfo;
            if (Objects.equals(model.getWuType(), 1)) {
                cittInfo = getAreaDetailInfoByArea(model.getRareaid());
            } else {
                cittInfo = getAreaDetailIdByCityId(Optional.ofNullable(model.getRCityId()).orElse(0) == 0 ? model.getRDid() : model.getRCityId());
            }

            if (Objects.equals(model.getWuType(), 1)) {
                model.setRAddress(cittInfo.getAddress());
            }

            AddressDTO receiver = new AddressDTO();

            receiver.setName(model.getRName());
            receiver.setMobile(model.getRMobile());
            receiver.setProv(cittInfo.getPname());
            receiver.setCity(cittInfo.getZname());
            receiver.setCounty(cittInfo.getDname());
            receiver.setAddress(model.getRAddress());

            if (Objects.equals(model.getWuliuid(), 0)) {
                wlId = wuLiuService.newTransSaveWuLiu(model, areaId, username);
                needSaveWuliu = false;
                if (wlId > 0) {
                    model.setWuliuid(wlId);
                }
            }

            try {

                if (WuLiuConstant.JINGDONG_JIUJI.equals(model.getCom())) {
                    if (Objects.isNull(model.getVloumn())) {
                        model.setVloumn(BigDecimal.ONE);
                    }
                    model.setExpressType(model.getJiujiJdExpressType());
                    JdOrderParamDTO param = new JdOrderParamDTO();
                    param.setSendAreaId(model.getSareaid());
                    param.setReceiveAreaId(model.getRareaid());
                    param.setReceiverName(model.getRName());
                    param.setReceiverMobile(model.getRMobile());
                    param.setReceiverAddress(cittInfo.getPname() + cittInfo.getZname() + cittInfo.getDname() + model.getRAddress());
                    param.setSendName(model.getSName());
                    param.setSendMobile(model.getSMobile());
                    String saddress = WuliuAddressUtil.getAddress(model.getSAddress(), jinfo);
                    param.setSendAddress(saddress);
                    param.setWuliuId(model.getWuliuid());
                    param.setGoodsWeight(String.valueOf(model.getWeight()));
                    param.setPackageCount(model.getPackageCount());
                    param.setVloumn(model.getVloumn());
                    param.setChildExpressType(OptionalUtils.ifNotNull(model.getExpressType(), Integer::valueOf));
                    param.setReceiverProvinceName(receiver.getProv());
                    param.setReceiverCityName(receiver.getCity());
                    param.setReceiverCountryName(receiver.getCounty());
                    param.setSenderProvinceName(sender.getProv());
                    param.setSenderCityName(sender.getCity());
                    param.setSenderCountryName(sender.getCounty());

                    // hasCreateOrderAuth 为测试代码,上线时需要删除
                    R<JdCreateOrderResultDTO> result = new R<>();
                    if (hasCreateOrderAuth()) {
                        result = jdCreateOrder(param);
                    }
                    if (Objects.equals(result.getCode(), 0) && result.getData() != null && result.getData().getDataResult() != null) {
                        model.setNu(result.getData().getDataResult().getExpressNumber());
                        if (StringUtils.isBlank(model.getNu()) && StringUtils.isNotBlank(result.getData().getExpressNumber())) {
                            model.setNu(StringUtils.isNotBlank(result.getData().getExpressNumber()) ? result.getData().getExpressNumber() : result.getData().getDataResult().getExpressNumber2());

                        }
                        if (StringUtils.isBlank(model.getLinkType()) || "null".equals(model.getLinkType()) || WuLiuConstant.ZERO_STR.equals(Optional.ofNullable(model.getLinkType()).orElse("0"))) {

                            if (WuLiuConstant.SUB_WULIU_TYPE_ARRY.contains(model.getWuType())) {
                                // 订单派送的物流类型
                                updteWuliuNo(expressEnumService.getWuliuCompanyName(model.getCom()), model.getNu(), OptionalUtils.ifNotNull(model.getDanHaoBind(), Long::valueOf), username);
                            } else if (WuLiuConstant.LP_SUB_WULIU_TYPE_ARRY.contains(model.getWuType())) {
                                // 良品订单派送的物流类型
                                updateWuliuNo2(expressEnumService.getWuliuCompanyName(model.getCom()), model.getNu(), OptionalUtils.ifNotNull(model.getDanHaoBind(), Long::valueOf), username);
                            }

                        }
                        lambdaUpdate().set(WuLiuEntity::getNu, model.getNu()).eq(WuLiuEntity::getId, model.getWuliuid()).update();
                        wuLiuExpressExtendService.save(new WuLiuExpressExtendEntity().setWuliuId(model.getWuliuid()).setExpressCompany(WuLiuConstant.JINGDONG_JIUJI).setExpressType(model.getExpressType()));
                        writewuliulogs(model.getWuliuid(), username, expressEnumService.getWuliuCompanyName(model.getCom()) + "运单号生成成功：" + model.getNu(), null);
                        WuliuUtil.sendWuliuProcessMessage(model.getWuliuid());

                    } else {
                        emsg = result.getUserMsg();
                    }
                } else {
                    Ch99ResultDataDTO<JdOrderResponseDTO> jdres = jdCreateNo(sender, receiver, "", null, null, null);
                    if (Objects.equals(jdres.getStats(), 1)) {
                        model.setNu(jdres.getData().getWaybillCode());
                        if (StringUtils.isBlank(model.getLinkType()) || "null".equals(model.getLinkType()) || WuLiuConstant.ZERO_STR.equals(Optional.ofNullable(model.getLinkType()).orElse("0"))) {
                            if (WuLiuConstant.SUB_WULIU_TYPE_ARRY.contains(model.getWuType())) {
                                // 订单派送的物流类型
                                updteWuliuNo(expressEnumService.getWuliuCompanyName(model.getCom()), model.getNu(), OptionalUtils.ifNotNull(model.getDanHaoBind(), Long::valueOf), username);
                            } else if (WuLiuConstant.LP_SUB_WULIU_TYPE_ARRY.contains(model.getWuType())) {
                                // 良品订单派送的物流类型
                                updateWuliuNo2(expressEnumService.getWuliuCompanyName(model.getCom()), model.getNu(), OptionalUtils.ifNotNull(model.getDanHaoBind(), Long::valueOf), username);
                            }
                        }

                        lambdaUpdate().set(WuLiuEntity::getNu, model.getNu()).eq(WuLiuEntity::getId, model.getWuliuid()).update();
                        writewuliulogs(model.getWuliuid(), username, expressEnumService.getWuliuCompanyName(model.getCom()) + "运单号生成成功：" + model.getNu(), null);
                    } else {
                        throw new CustomizeException(StringUtils.isNotBlank(jdres.getMsg()) ? jdres.getMsg() : "快递单号生成失败");
                    }
                }

            } catch (Exception e) {
                writewuliulogs(model.getWuliuid(), username, expressEnumService.getWuliuCompanyName(model.getCom()) + "运单号生成错误：" + e.getMessage(), null);
            }
        }
        // 京东物流 结束

        // 达达快递 开始
        else if (isCreateOrder && WuLiuConstant.DADA.equals(model.getCom()) && StringUtils.isBlank(model.getNu())) {
            //寄件人信息
            CityIdListDTO jinfo;
            //上门取件  其他派送
            if (Arrays.asList(NumUtil.SEVEN, NumUtil.EIGHT).contains(model.getWuType())) {
                jinfo = getAreaIdByCityId(model.getSDid(), null);
            } else {
                jinfo = getAreaInfoByArea(model.getSareaid());
            }

            Areainfo rAreaObj = null;
            CityIdListDTO cittInfo;
            if (Objects.equals(model.getWuType(), 1)) {
                rAreaObj = Optional.ofNullable(areaInfoService.getAreaInfoByAreaId2(model.getRareaid())).orElseGet(Areainfo::new);
                cittInfo = getAreaDetailInfoByArea(model.getRareaid());
            } else {
                cittInfo = getAreaDetailIdByCityId(Objects.isNull(model.getRCityId()) || Objects.equals(model.getRCityId(), 0) ? model.getRDid() : model.getRCityId());
            }

            //校验收货地址省市区信息
            checkReceiverAddrInfo(cittInfo);
            if (Objects.equals(model.getWuliuid(), 0)) {
                wlId = wuLiuService.newTransSaveWuLiu(model, areaId, username);
                needSaveWuliu = false;
                if (wlId > 0) {
                    model.setWuliuid(wlId);
                }
            }
            if (model.getWuliuid() > 0) {
                DadaReceiverDTO recover = new DadaReceiverDTO();
                String toUsernote = WuliuAddressUtil.getUsernote(rAreaObj);
                recover.setName(StringUtils.isNotBlank(toUsernote) ? toUsernote : model.getRName());
                //todo 虚拟号码
                String rMobile = model.getRMobile();
                if (Arrays.asList(4, 6).contains(model.getWuType())) {
                    String tempRMobile = "";
                    Integer danHaoBind = model.getDanHaoBind();
                    Sub sub = subService.getSub(danHaoBind);
                    if (Objects.nonNull(sub)) {
                        if (OrderSubTypeEnum.JD.getCode().equals(sub.getSubtype())) {
                            tempRMobile = subService.getJdBuyerMobile(danHaoBind);
                        } else if (WuLiuConstant.HOURS_ORDER_SUB_TYPE.contains(Optional.ofNullable(sub.getSubtype()).orElse(0))) {
                            tempRMobile = subService.getMtBuyerMobile(danHaoBind);
                        }
                    }
                    if (StringUtils.isNotEmpty(tempRMobile)) {
                        //处理抖音订单虚拟号码
                        tempRMobile = tempRMobile.replace("-",",").replace("_",",");
                        rMobile = tempRMobile;
                    }
                }
                recover.setMobile(rMobile);
                recover.setProvince(cittInfo.getPname());
                recover.setCityName(cittInfo.getZname());
                recover.setCountryName(cittInfo.getDname());
                if (Objects.nonNull(rAreaObj)) {
                    recover.setPosition(rAreaObj.getPosition());
                }
                String raddressPosition = WuliuAddressUtil.getSubPosition(Builder.of(SubPositionReq::new)
                        .with(SubPositionReq::setSubId, model.getDanHaoBind())
                        .with(SubPositionReq::setWuliuId, model.getWuliuid())
                        .with(SubPositionReq::setAddress, model.getRAddress())
                        .with(SubPositionReq::setWuType, model.getWuType())
                        .build());
                if (StringUtils.isNotBlank(raddressPosition)) {
                    recover.setPosition(raddressPosition);
                }
                recover.setAddress(model.getRAddress());
                recover.setOrderId(String.valueOf(model.getWuliuid()));
                recover.setShopCode(Optional.ofNullable(areaInfoService.getAreaInfoByAreaId2(model.getSareaid())).orElseGet(Areainfo::new).getArea());
                // hasCreateOrderAuth 为测试代码,上线时需要删除
                Ch99ResultDataDTO<?> wlres = new Ch99ResultDataDTO<>();
                if (hasCreateOrderAuth()) {
                    wlres = dadaCreateOrder(recover, model);
                }
                if (Objects.equals(wlres.getStats(), 1)) {
                    BigDecimal fee = BigDecimal.valueOf(Double.parseDouble(String.valueOf(wlres.getData())));
                    model.setNu(String.valueOf(model.getWuliuid()));
                    model.setPrice(fee);
                    lambdaUpdate().set(WuLiuEntity::getNu, model.getNu())
                            .set(WuLiuEntity::getPrice, fee)
                            .set(WuLiuEntity::getDanHaoBind, model.getDanHaoBind())
                            .eq(WuLiuEntity::getId, model.getWuliuid()).update();
                    writewuliulogs(model.getWuliuid(), username, expressEnumService.getWuliuCompanyName(model.getCom())+"下单成功~", null);
                    WuliuUtil.sendWuliuProcessMessage(model.getWuliuid());


                    if ((StringUtils.isBlank(model.getLinkType()) || "null".equals(model.getLinkType()) || WuLiuConstant.ZERO_STR.equals(Optional.ofNullable(model.getLinkType()).orElse("0")))
                            && !Objects.equals(model.getWuType(), 1) && model.getDanHaoBind() > 0) {

                        if (WuLiuConstant.SUB_WULIU_TYPE_ARRY.contains(model.getWuType())) {
                            // 订单派送的物流类型
                            updteWuliuNo(expressEnumService.getWuliuCompanyName(model.getCom()), model.getNu(), OptionalUtils.ifNotNull(model.getDanHaoBind(), Integer::longValue), username);
                        } else if (WuLiuConstant.LP_SUB_WULIU_TYPE_ARRY.contains(model.getWuType())) {
                            // 良品订单派送的物流类型
                            updateWuliuNo2(expressEnumService.getWuliuCompanyName(model.getCom()), model.getNu(), OptionalUtils.ifNotNull(model.getDanHaoBind(), Integer::longValue), username);
                        }
                    }
                } else {
                    emsg = wlres.getMsg();
                    writewuliulogs(model.getWuliuid(), username, wlres.getMsg(), null);
                }
            }
        }
        // 达达快递 结束

        // uu跑腿 开始
        else if (isCreateOrder && WuLiuConstant.UU_PAOTUI.equals(model.getCom()) && StringUtils.isBlank(model.getNu())) {
            //寄件人信息
            CityIdListDTO jinfo;
            //上门取件  其他派送
            if (Arrays.asList(NumUtil.SEVEN, NumUtil.EIGHT).contains(model.getWuType())) {
                jinfo = getAreaIdByCityId(model.getSDid(), null);
            } else {
                jinfo = getAreaInfoByArea(model.getSareaid());
            }

            CityIdListDTO cityInfo;
            Areainfo rArea = new Areainfo();
            if (Objects.equals(model.getWuType(), 1)) {
                rArea = Optional.ofNullable(areaInfoService.getAreaInfoByAreaId2(model.getRareaid())).orElseGet(Areainfo::new);
                cityInfo = getAreaDetailInfoByArea(model.getRareaid());
            } else {
                cityInfo = getAreaDetailIdByCityId(Objects.isNull(model.getRCityId()) || Objects.equals(model.getRCityId(), 0) ? model.getRDid() : model.getRCityId());
            }
            //校验收货地址省市区信息
            String checkAddress = WuliuAddressUtil.checkAddress(cityInfo);
            if (StringUtils.isNotEmpty(checkAddress)) {
                throw new CustomizeException(checkAddress);
            }

            if (Objects.equals(model.getWuliuid(), 0)) {
                wlId = wuLiuService.newTransSaveWuLiu(model, areaId, username);
                needSaveWuliu = false;
                if (wlId > 0) {
                    model.setWuliuid(wlId);
                }
            }
            if (model.getWuliuid() > 0) {
                LogisticsBase base = areainfoService.getAreaName(model.getSareaid(), model.getRareaid());
                CreateOrderByShop req = new CreateOrderByShop();
                req.setOrderId("" + model.getWuliuid());
                req.setSendName(model.getSName());
                req.setSendPhone(model.getSMobile());
                req.setSendShopId(model.getSareaid());
                req.setSendShopName(base.getSendShopName());
                Areainfo sArea = Optional.ofNullable(areaInfoService.getAreaInfoByAreaId2(model.getSareaid())).orElseGet(Areainfo::new);
                if (StringUtils.isNotEmpty(sArea.getPosition())) {
                    String[] split = sArea.getPosition().split(StrPool.COMMA);
                    if (NumUtil.TWO == split.length) {
                        req.setSendLng(split[0]);
                        req.setSendLat(split[1]);
                    }
                }
                String sendAddress = model.getSAddress();
                sendAddress = WuliuAddressUtil.getAddress(sendAddress, jinfo);
                req.setSendAddress(sendAddress);
                req.setReceiverName(model.getRName());
                //todo 虚拟号码
                String rMobile = model.getRMobile();
                if (Arrays.asList(4, 6).contains(model.getWuType())) {
                    String tempRMobile = "";
                    Integer danHaoBind = model.getDanHaoBind();
                    Sub sub = subService.getSub(danHaoBind);
                    if (Objects.nonNull(sub)) {
                        if (OrderSubTypeEnum.JD.getCode().equals(sub.getSubtype())) {
                            tempRMobile = subService.getJdBuyerMobile(danHaoBind);
                        } else if (WuLiuConstant.HOURS_ORDER_SUB_TYPE.contains(Optional.ofNullable(sub.getSubtype()).orElse(0))) {
                            tempRMobile = subService.getMtBuyerMobile(danHaoBind);
                        }
                    }
                    if (StringUtils.isNotEmpty(tempRMobile)) {
                        //处理抖音订单虚拟号码
                        tempRMobile = tempRMobile.replace("-","_");
                        rMobile = tempRMobile;
                    }
                }
                req.setReceiverPhone(rMobile);
                req.setReceiveShopId(model.getRareaid());
                req.setReceiveShopName(base.getReceiveShopName());
                if (StringUtils.isNotEmpty(rArea.getPosition())) {
                    String[] split = rArea.getPosition().split(StrPool.COMMA);
                    if (NumUtil.TWO == split.length) {
                        req.setReceiverLng(Double.valueOf(split[0]));
                        req.setReceiverLat(Double.valueOf(split[1]));
                    }
                }
                String raddressPosition = WuliuAddressUtil.getSubPosition(Builder.of(SubPositionReq::new)
                        .with(SubPositionReq::setSubId, model.getDanHaoBind())
                        .with(SubPositionReq::setWuliuId, model.getWuliuid())
                        .with(SubPositionReq::setAddress, model.getRAddress())
                        .with(SubPositionReq::setWuType, model.getWuType())
                        .build());
                if (StringUtils.isNotBlank(raddressPosition)) {
                    Coordinate rcoordinate = new Coordinate(raddressPosition);
                    req.setReceiverLng(rcoordinate.getLongitude());
                    req.setReceiverLat(rcoordinate.getLatitude());
                }
                String receiverAddress = model.getRAddress();
                receiverAddress = WuliuAddressUtil.getAddress(receiverAddress, cityInfo);
                req.setReceiverAddress(receiverAddress);
                if (Arrays.asList(NumUtil.SEVEN, NumUtil.EIGHT).contains(model.getWuType())) {
                    req.setReceiverCity(jinfo.getZname());
                    req.setReceiverCountry(jinfo.getDname());
                } else {
                    List<AreaListRes> areaList = wuLiuService.getAreaList();
                    AreaListRes areaListRes = areaList.stream().filter(v -> sArea.getZid().equals(v.getCode())).findFirst().orElseThrow(() -> new CustomizeException("获取城市信息失败，请在店面管理维护所属地区信息"));
                    AreaListRes countryArea = areaList.stream().filter(v -> sArea.getCityid().equals(v.getCode())).findFirst().orElse(new AreaListRes());
                    req.setReceiverCity(areaListRes.getName1());
                    req.setReceiverCountry(countryArea.getName1());
                }
                req.setFromUsernote(WuliuAddressUtil.getUsernote(sArea));
                req.setToUsernote(WuliuAddressUtil.getUsernote(rArea));
                //备注 寄件人备用电话
                String mobile = Optional.ofNullable(SysUtils.getUser()).map(v -> ch999UserService.getUserByCh999Id(v.getUserId()))
                        .map(Ch999User::getMobile).orElse(model.getSMobile());
                if (StringUtils.isNotBlank(mobile)) {
                    req.setComment(StrUtil.format("取件码：{} 寄件人备用电话：{}", model.getWuliuid(), mobile));
                }

                R<CreateOrderShopResDTO> wlres = logisticsExpressService.createOrderByShop(SysUtils.getUser(), req, WuLiuConstant.UU_PAOTUI_EXPRESS_TYPE);
                if (Objects.equals(wlres.getCode(), 0) && Objects.nonNull(wlres.getData())) {
                    BigDecimal fee = wlres.getData().getFee();
                    model.setNu(wlres.getData().getWaybillNo());
                    model.setPrice(fee);
                    lambdaUpdate().set(WuLiuEntity::getNu, model.getNu())
                            .set(WuLiuEntity::getPrice, fee)
                            .set(WuLiuEntity::getDanHaoBind, model.getDanHaoBind())
                            .eq(WuLiuEntity::getId, model.getWuliuid()).update();
                    writewuliulogs(model.getWuliuid(), username, expressEnumService.getWuliuCompanyName(model.getCom()) + "下单成功~", null);
                    if ((StringUtils.isBlank(model.getLinkType()) || "null".equals(model.getLinkType()) || WuLiuConstant.ZERO_STR.equals(Optional.ofNullable(model.getLinkType()).orElse("0")))
                            && !Objects.equals(model.getWuType(), 1) && model.getDanHaoBind() > 0) {
                        if (WuLiuConstant.SUB_WULIU_TYPE_ARRY.contains(model.getWuType())) {
                            // 订单派送的物流类型
                            updteWuliuNo(expressEnumService.getWuliuCompanyName(model.getCom()), model.getNu(), OptionalUtils.ifNotNull(model.getDanHaoBind(), Integer::longValue), username);
                        } else if (WuLiuConstant.LP_SUB_WULIU_TYPE_ARRY.contains(model.getWuType())) {
                            // 良品订单派送的物流类型
                            updateWuliuNo2(expressEnumService.getWuliuCompanyName(model.getCom()), model.getNu(), OptionalUtils.ifNotNull(model.getDanHaoBind(), Integer::longValue), username);
                        }
                    }
                    WuliuUtil.sendWuliuProcessMessage(model.getWuliuid());
                } else {
                    emsg = wlres.getUserMsg();
                    writewuliulogs(model.getWuliuid(), username, wlres.getUserMsg(), null);
                }
            }
        }
        // uu跑腿 结束

        // 闪送快递 开始
        else if (isCreateOrder && WuLiuConstant.SHANSONG.equals(model.getCom()) && StringUtils.isBlank(model.getNu())) {
            // hasCreateOrderAuth 为测试代码,上线时需要删除
            Map<String, String> needSaveWuliuMap = new HashMap<>();
            if (hasCreateOrderAuth()) {
                needSaveWuliuMap = createShansongOrder(model, username, areaId, needSaveWuliu);
            } else {
                // 测试流程是否能走通用
                needSaveWuliuMap.put("needSaveWuliu", "true");
                needSaveWuliuMap.put("wl_id", "0");
            }
            needSaveWuliu = Boolean.parseBoolean(needSaveWuliuMap.get("needSaveWuliu"));
            wlId = Integer.valueOf(needSaveWuliuMap.get("wl_id"));
        }
        // 闪送快递 结束

        if (needSaveWuliu) {
            wlId = wuLiuService.newTransSaveWuLiu(model, areaId, username);
        }

        if (WuLiuConstant.SHUNFENG.equals(model.getCom())
                && model.getOrgcode() != null
                && (StringUtils.isBlank(expressShunfeng) || !expressShunfeng.contains("9386"))) {
            // 保存运单的一些信息
            if (WuLiuConstant.JIUJI_MONTH_CUST_ID.equals(m.getCustId())) {
                saveWuliuWandDian(wlId, model.getOrgcode(), model.getDestcode(), model.getExpressType(), m.getPayMethod(), WuLiuConstant.JIUJI_MONTH_CUST_ID);
            } else {
                saveWuliuWandDian(wlId, model.getOrgcode(), model.getDestcode(), model.getExpressType(), m.getPayMethod(), model.getYuejiekahao());
            }
        }
        if (WuLiuConstant.SHUNFENG.equals(model.getCom()) && StringUtils.isNotBlank(expressShunfeng) && expressShunfeng.contains("9386")) {
            saveWuliuWandDian(wlId, model.getOrgcode(), model.getDestcode(), expressShunfeng, m.getPayMethod(), "**********"); //中台全国顺丰账号
        }
        if (WuLiuConstant.ZHONGTONG.equals(model.getCom()) && StringUtils.isNotBlank(model.getOrgcode())) {
            // 保存运单的一些信息
            saveWuliuWandDian(wlId, model.getOrgcode(), model.getDestcode(), model.getExpressType(), m.getPayMethod(), model.getYuejiekahao());
        }
        if (WuLiuConstant.ZHONGTONG_NEW.equals(model.getCom()) && StringUtils.isNotBlank(model.getOrgcode())) {
            // 保存运单的一些信息
            saveWuliuWandDian(wlId, model.getOrgcode(), model.getDestcode(), model.getExpressType(), m.getPayMethod(), model.getYuejiekahao());
        }

        if (!Objects.equals(wlId, -1)) {

            if (StringUtils.isNotBlank(wuliudanRunErrmsg)) {
                writewuliulogs(wlId, username, "运单号生成错误：" + wuliudanRunErrmsg, null);
            }

            model.setWuliuid(wlId);
            if (StringUtils.isNotBlank(model.getResult1())) {
                writeLogs(model, username, model.getResult1());
            }
            if (StringUtils.isNotEmpty(model.getMsg())) {
                writeLogs(model, username, model.getMsg());
            }

            if (!Objects.equals(Optional.ofNullable(old.getNu()).orElse("").trim(), Optional.ofNullable(model.getNu()).orElse("").trim())) {
                subWuliuTransferLogPush(wlId, "系统");
            }

            //计算物流单骑行距离
            WuliuUtil.sendWuliuExpressMessage(new WuliuExpressMqBO<WuliuDistributtionCostBO>().setAct(WuliuExpressConstant.ACT_CALCULATE_DISTRIBUTION_COST).
                    setData(LambdaBuild.create(new WuliuDistributtionCostBO())
                            .set(WuliuDistributtionCostBO::setSendPosition, model.getSendPosition())
                            .set(WuliuDistributtionCostBO::setReceivePosition, model.getReceivePosition())
                            .set(WuliuDistributtionCostBO::setSdetailedAddress, model.getSdetailedAddress())
                            .set(WuliuDistributtionCostBO::setRdetailedAddress, model.getRdetailedAddress())
                            .set(WuliuDistributtionCostBO::setWuliuId, wlId).build()));
            //新生成的物流单或者内部物流物流单

            boolean isEnabled = ModuleVersionKeys.isEnabled(RedisKeys.PAOTUI_AUTO_CALL_ENABLE,
                    CommonUtils.defaultIfNullOrZero(model.getSareaid(), model.getAreaId()), false);

            if(!isEnabled && WuLiuConstant.PAO_TUI.equals(model.getCom())){
                throw new CustomizeException("发货门店不支持呼叫跑腿");
            }

            if(isEnabled && (Boolean.TRUE.equals(model.getIsSuccessInsertWuLiu()) || isChangeComOrPaiJianRen)){
                //是否自动匹配派送人
                boolean isAutoMathPaisongRen = isCreateWuliu || WuLiuTypeEnum.INNER.getCode().equals(model.getWuType());
                //处理物流单自动叫跑腿
                handlePaotui(model, username, isAutoMathPaisongRen, old);
            }
        }

        if (isCreateNu && StringUtils.isNotBlank(model.getNu())) {
            lambdaUpdate().set(WuLiuEntity::getNu, model.getNu()).set(WuLiuEntity::getCom, model.getCom())
                    .set(WuLiuEntity::getShouJianRen, username)
                    .eq(WuLiuEntity::getId, model.getWuliuid()).update();
        }
        //销售单和良品单：物流单内生成快递单号货跑腿单号，状态变为等待派送
        boolean changeWuliuStats = isCreateOrder && StringUtils.isNotBlank(model.getNu()) && WuliuUtil.isOrderWutype(model.getWuType());
        if (changeWuliuStats) {
            ChangWuliuStatsBO wuliuStats = LambdaBuild.create(ChangWuliuStatsBO.class).set(ChangWuliuStatsBO::setWuliuId, model.getWuliuid()).set(ChangWuliuStatsBO::setStats, WuLiuStatusEnum.WAITING_DELIVERY.getCode()).build();
            WuliuUtil.sendWuliuExpressMessage(new WuliuExpressMqBO<ChangWuliuStatsBO>().setAct(WuliuExpressConstant.ACT_WULIU_CHANGE_STATS)
                    .setData(wuliuStats));
        }
        //调拨单发货，物流叫跑腿，推送OA消息给调拨单发货操作人
        List<String> paotuComList = Arrays.asList(LogisticsExpressTypeEnum.DA_DA.getCode(), LogisticsExpressTypeEnum.MEI_TUAN.getCode(), LogisticsExpressTypeEnum.UU_PAO_TUI.getCode(), LogisticsExpressTypeEnum.SFTC.getCode());
        boolean isPushWuliuMsg = isCreateOrder && paotuComList.contains(Optional.ofNullable(model.getCom()).orElse("")) && StringUtils.isNotBlank(model.getNu()) && WuLiuTypeEnum.INNER.getCode().equals(model.getWuType());
        if (isPushWuliuMsg) {
            WuliuUtil.sendWuliuExpressMessage(new WuliuExpressMqBO<DiaoboPaotuiWuliuBO>().setAct(WuliuExpressConstant.ACT_DIAOBO_PAOTUI_MESSAGE_PUSH)
                    .setData(DiaoboPaotuiWuliuBO.builder().wuliuId(model.getWuliuid()).build()));
        }

        try {

            // 设置物流单异常信息
            AbnomalWuliuRequestDTO abnomalRequest = new AbnomalWuliuRequestDTO()
                    .setCh999Id(Optional.ofNullable(Optional.ofNullable(userInfoClient.getCh999UserByUserName(username).getData()).orElseGet(Ch999UserVo::new).getCh999Id()).orElse(0))
                    .setIsCancel(!Boolean.TRUE.equals(model.getIsExceptionSub()))
                    .setRemark(model.getExceptionRemark())
                    .setWuliuId(String.valueOf(model.getWuliuid()));

            if (setAbnomalWuliu(abnomalRequest)) {
                writewuliulogs(wlId, username, Boolean.TRUE.equals(model.getIsExceptionSub()) ? ("设置物流异常：" + model.getExceptionRemark()) : "取消物流异常设置", null);
            }

        } catch (Exception e) {
            log.error("取消物流异常设置：{}", Exceptions.getStackTraceAsString(e), e);
        }

        Map<String, Object> data = new HashMap<>(7);
        data.put("wl_id", wlId);
        data.put("wuType", model.getWuType());
        data.put("com", model.getCom());
        data.put("nu", model.getNu());
        data.put("isCreateWuliu", isCreateWuliu);
        data.put("danhaobind", model.getDanHaoBind());
        data.put("userName", username);

        // 良品订单派送 开始
        setRabbitMqMessageForCsharp(JacksonJsonUtils.toJson(new RabbitMqActDTO().setAct("liangPingPaiSong").setData(data)));
        // 良品订单派送 结束

        map.put("wl_id", wlId);
        map.put("emsg", emsg);

        return map;
    }

    /**
     * 处理自动叫跑腿
     *
     * @param model
     * @param username
     * @param isAutoMathPaisongRen
     * @param old
     */
    private void handlePaotui(WuLiuAddOrUpdateReqVO model, String username, boolean isAutoMathPaisongRen, WuLiu2Entity old) {
        WuLiuPaoTuiMqReq wuLiuPaoTuiMqReq = new WuLiuPaoTuiMqReq();
        wuLiuPaoTuiMqReq.setWuliuId(model.getWuliuid());
        wuLiuPaoTuiMqReq.setNodeTime(LocalDateTime.now());
        if (WuLiuConstant.JIUJI_KUAI_SONG.equals(model.getCom())) {
            if(isAutoMathPaisongRen){
                //查询派送人
                String courierName = model.getPaiJianRen();
                //内部物流，九机快送，需要重新匹配派件人信息
                if(StringUtils.isBlank(courierName) || WuLiuTypeEnum.INNER.getCode().equals(model.getWuType())){
                    AreaCourierReq areaCourierReq = AreaCourierReq.builder().areaId(model.getSareaid()).delivery(2).build();
                    courierName = Optional.ofNullable(SpringUtil.getBean(CsharpInWcfCloud.class).getAreaCourier(areaCourierReq))
                            .filter(R::isSuccess).map(R::getData).map(AreaCourierRes::getStaffName).orElse(null);
                }

                if (StringUtils.isNotBlank(courierName)) {
                    model.setPaiJianRen(courierName);
                    //更新物流单派件人
                    lambdaUpdate().set(WuLiuEntity::getPaiJianRen, courierName)
                            .eq(WuLiuEntity::getId, model.getWuliuid())
                            .update();

                    WuliuPaijianInfoEntry wuliuPaijianInfo = new WuliuPaijianInfoEntry();
                    wuliuPaijianInfo.setFkWuliuId(model.getWuliuid());
                    wuliuPaijianInfo.setStatus(0);
                    wuliuPaijianInfo.setPaijianren(courierName);
                    wuliuPaijianInfo.setCreateTime(LocalDateTime.now());
                    wuliuPaijianInfo.setCreateUser(username);
                    SpringUtil.getBean(IWuliuPaijianInfoService.class).savePaiJianInfo(wuliuPaijianInfo);

                    wuLiuPaoTuiMqReq.setPaiJianId(wuliuPaijianInfo.getId());
                    wuLiuPaoTuiMqReq.setNodeType(wuliuPaijianInfo.getStatus());
                }
            }

            rabbitTemplate.convertAndSend(RabbitMqConfig.WULIU_PAOTUI_AUTO_CALL_QUEUE, JSON.toJSONString(wuLiuPaoTuiMqReq));
        } else if (WuLiuConstant.PAO_TUI.equals(model.getCom())) {
            wuLiuPaoTuiMqReq.setCallByUser(username);
            model.setIsCallIngPaoTui(Boolean.TRUE);
            //原来的快递方式
            wuLiuPaoTuiMqReq.setPrevCom(old.getCom());
            rabbitTemplate.convertAndSend(RabbitMqConfig.WULIU_PAOTUI_AUTO_CALL_QUEUE, JSON.toJSONString(wuLiuPaoTuiMqReq));
        }else if(StrUtil.isBlank(model.getCom())){
            // 第三方派送也进行推送
            rabbitTemplate.convertAndSend(RabbitMqConfig.WULIU_PAOTUI_AUTO_CALL_QUEUE, JSON.toJSONString(wuLiuPaoTuiMqReq));
        }
    }

    /**
     * 校验收货地址省市区信息
     * @param cittInfo
     */
    private void checkReceiverAddrInfo(CityIdListDTO cittInfo) {
        if (Objects.isNull(cittInfo)) {
            throw new CustomizeException("请检查收件地址,省市区信息是否正确");
        }
        if (StringUtils.isEmpty(cittInfo.getPname())) {
            throw new CustomizeException("请检查收件地址,省信息是否正确");
        }
        if (StringUtils.isEmpty(cittInfo.getZname())) {
            throw new CustomizeException("请检查收件地址,市信息是否正确");
        }
        if (StringUtils.isEmpty(cittInfo.getDname())) {
            throw new CustomizeException("请检查收件地址,区信息是否正确");
        }
    }

    /**
     * 临时方法,主要限定某些工号可以创建快递,避免在生产的快递单账号上创建快递，谨慎创建快递单
     *
     * @return boolean
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-04
     */
    public boolean hasCreateOrderAuth() {
        // 暂时只有
        // 李江华 10952、 吴遥遥 13774 、张琳翔 13140、李东泽13835 可以创建，谨慎创建快递单
        return Arrays.asList(10952, 13774, WuLiuConstant.ZLX_CH999_ID, 13835, 13682,15771,10771,10770,13596).contains(SysUtils.getUserId()) || SysUtils.isJiuJiProd();
    }

    /**
     * shunfengApiServices.sendToCreateOrder
     * 生成顺风快递单
     *
     * @param item
     * @param sareaid
     * @return String
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-01
     */
    public String sendToCreateOrder(SfOrderInfoVO item, Integer sareaid) {

        Areainfo areainfo = Optional.ofNullable(areainfoService.getAreaInfoByAreaId2(sareaid)).orElseGet(Areainfo::new);
        //顺丰运单全部无需手持终端确认
        // 总部:HQ/dc/d1/h1,dc1,sz,h2,D
        List<Integer> areaids = Arrays.asList(113, 14, 246, 96);

        if (Boolean.TRUE.equals(item.getDoCallFlag())) {
            item.setDoCall(1);
        } else {
            if (isCurAreaHqDcH1D1(sareaid) || areaids.contains(sareaid)) {
                item.setDoCall(0);
            } else {
                boolean isTestMobile = ("15368020004".equals(item.getJMobile()) || "15368020004".equals(item.getDMobile()) && LocalDateTime.now().isBefore(LocalDateTime.of(2020, 1, 1, 0, 0)));
                if (!isTestMobile) {
                    item.setDoCall(1);
                }
            }
        }
        if (Objects.equals(areainfo.getPid(), 52) && !Arrays.asList(113, 246, 96).contains(sareaid)) {
            //寄件门店排除dc1,h2,D（113,246,96）
            //20211008 修改贵州门店上门揽件，门店账号  247  类型 不支持上门揽件，顺丰IT客服反馈 2021年7月修改的政策，又改回原来逻辑
            item.setDoCall(1);
            item.setExpressType("247");
        } else {
            if (StringUtils.isNotBlank(item.getExpressType()) && item.getExpressType().contains(("_"))) {
                item.setExpressType(item.getExpressType().split("_")[0]);
            } else {
                //默认为九机特惠
                item.setExpressType("2");
            }
        }

        if (!Objects.equals(item.getSubmitType(), 1) && StringUtils.isNotBlank(item.getSendStartTime())) {
            item.setSendStartTime(LocalDateTime.now().plusMinutes(10).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        ShunFengCardVO card = getYueJieKaHao(sareaid, item.getExpressType());

        // ********** 这个账号 10点后 epxress_type 改成 247
        if ("**********".equals(card.getCustId())) {
            item.setDoCall(0);
            item.setExpressType("247");
        }

        item.setCustId(card.getCustId());

        String xml = shunfengGetWebXml(item, card.getClientCode());
        String verifyCode1 = xml + card.getCheckPwd();
        String verifyCode = shunfengMd5ToBase64String(verifyCode1);//生成verifyCode

        Map<String, Object> postValues = new HashMap<>(2);
        postValues.put("xml", xml);//报文
        postValues.put("verifyCode", verifyCode); //签名验证码

        //POST地址
        String postUrl = "http://bsp-oisp.sf-express.com/bsp-oisp/sfexpressService";

        String message = String.format("请求地址：%s 入参：%s", postUrl, JacksonJsonUtils.toJson(postValues));
        String xmlResult = "";
        try {
            xmlResult = HttpRequest.post(postUrl).form(postValues).execute().body(); //注：返回的数据是XML格式的噢
            message += xmlResult;
        } catch (Exception e) {
            log.error("顺丰快递请求创建快递单号报错，请求内容：{}，{}", message, Exceptions.getStackTraceAsString(e),e);
            throw new CustomizeException(String.format("顺丰快递请求创建快递单号报错，请求链接:%s", postUrl), e);
        }
        log.warn("顺丰快递创建日志：{}", message);
        if (StringUtils.isNotBlank(xml) && !xml.contains("远程服务器返回错误")) {
            try {

                Document document = XmlUtils.toDocument(xmlResult);
                Element root = document.getRootElement();

                Element head = root.element("Head");
                if ("OK".equals(head.getTextTrim())) {
                    Element body = root.element("Body");
                    Element orderResponse = body.element("OrderResponse");
                    Element xn = orderResponse;
                    if ("3".equals(xn.attributeValue("filter_result"))) {
                        throw new CustomizeException("不可收派，顺丰快递单生成失败！");
                    }

                    //记录顺风电子面单信息
                    Element dn = orderResponse.element("rls_info").element("rls_detail");
                    if (dn != null) {

                        try {

                            String orderid = xn.attributeValue("orderid");
                            String[] mailnoList = xn.attributeValue("mailno").split(",");
                            String destRouteLabel = dn.attributeValue("destRouteLabel");
                            String twoDimensionCode = dn.attributeValue("twoDimensionCode");

                            List<WuLiuShunfengNoInfoEntity> cmdList = new ArrayList<>();
                            for (String mailno : mailnoList) {
                                cmdList.add(new WuLiuShunfengNoInfoEntity()
                                        .setMailNo(mailno)
                                        .setWuLiuId(orderid)
                                        .setDestRouteLabel(destRouteLabel)
                                        .setTwoDimensionCode(twoDimensionCode)
                                        .setSareaid(sareaid)
                                        .setCustId(card.getCustId())
                                        .setAddDate(LocalDateTime.now())
                                        .setJMobile(item.getJMobile())
                                        .setDMobile(item.getDMobile())
                                        .setCkind(item.getCkind()));
                            }
                            if (CollectionUtils.isNotEmpty(cmdList)) {
                                wuLiuShunfengNoInfoService.addAll(cmdList);
                            }

                        } catch (Exception e) {
                            log.error("顺丰物流订单生成失败: {}, {}", Exceptions.getStackTraceAsString(e), ";areaid:" + sareaid + ";orderid:" + item.getOrderId() + ";xml:" + xmlResult, e);
                            sendTextMessage("顺丰物流订单生成失败:" + e.getMessage() + ";areaid:" + sareaid + ";orderid:" + item.getOrderId() + ";xml:" + xmlResult);
                            sendTextMessage("顺丰物流订单生成失败:DATA=" + JacksonJsonUtils.toJson(item));
                        }

                    }


                } else if (Objects.equals(head.getTextTrim(), "ERR")) {
                    Element error = root.element("ERROR");
                    if (Objects.equals(error.getTextTrim(), "重复下单") && areaids.contains(sareaid)) {
                        item.setOrderId(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + "-re");
                        return sendToCreateOrder(item, sareaid);
                    }
                }

            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return xmlResult;
    }

    /**
     * 顺丰 Laas 前置逻辑
     *
     * @param item
     * @param sareaid
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-16
     */
    public ShunFengCardVO shunfengLaas(SfOrderInfoVO item, Integer sareaid) {
        Areainfo areainfo = Optional.ofNullable(areainfoService.getAreaInfoByAreaId2(sareaid)).orElseGet(Areainfo::new);
        //顺丰运单全部无需手持终端确认
        // 总部:HQ/dc/d1/h1,dc1,sz,h2,D
        List<Integer> areaids = Arrays.asList(113, 14, 246, 96,738,826,878,879,880);

        // 默认通知快递小哥上门揽件
        item.setDoCall(1);

        if (Boolean.TRUE.equals(item.getDoCallFlag())) {
            item.setDoCall(1);
        } else {
            if (isCurAreaHqDcH1D1(sareaid) || areaids.contains(sareaid)) {
                item.setDoCall(0);
            } else {
                boolean isTestMobile = ("15368020004".equals(item.getJMobile()) || "15368020004".equals(item.getDMobile()) && LocalDateTime.now().isBefore(LocalDateTime.of(2020, 1, 1, 0, 0)));
                if (!isTestMobile) {
                    item.setDoCall(1);
                }
            }
        }
        if (Objects.equals(areainfo.getPid(), 52) && !Arrays.asList(113, 246, 96).contains(sareaid)) {
            //寄件门店排除dc1,h2,D（113,246,96）
            //20211008 修改贵州门店上门揽件，门店账号  247  类型 不支持上门揽件，顺丰IT客服反馈 2021年7月修改的政策，又改回原来逻辑
            item.setDoCall(0);
            item.setExpressType("247");
        } else {
            if (StringUtils.isNotBlank(item.getExpressType()) && item.getExpressType().contains(("_"))) {
                item.setExpressType(item.getExpressType().split("_")[0]);
            } else {
                //默认为九机特惠
                item.setExpressType("2");
            }
        }

        if (!Objects.equals(item.getSubmitType(), 1) && StringUtils.isNotBlank(item.getSendStartTime())) {
            item.setSendStartTime(LocalDateTime.now().plusMinutes(10).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        ShunFengCardVO card = getYueJieKaHao(sareaid, item.getExpressType());
        if (StringUtils.isNotEmpty(item.getCustId())) {
            card.setCustId(item.getCustId());
            return card;
        }
        item.setCustId(card.getCustId());
        item.setExpressType(String.valueOf(shunfengExpressTypeMapping(OptionalUtils.ifNotNull(item.getExpressType(), Integer::valueOf))));
        return card;
    }

    /**
     * QYWeiXinService.SendTextMessage
     *
     * @param content
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-04
     */
    private static void sendTextMessage(String content) {
        MessagePushUtils.pushOaMessage(WuLiuConstant.ZLX_CH999_ID, content, "", MessagePushUtils.MessagePushOaMessageTypeConstant.ABNORMAL);
    }

    /**
     * meituanServices.CreateMeiTuanOrder
     * 创建美团订单
     *
     * @param conn         meituanItemDTO
     * @param saasPlatform SaasPlatformDTO
     * @return createReturnResult
     * <AUTHOR> [<EMAIL>]
     * @date 2021-10-27
     */
    private CreateReturnResultDTO createMeiTuanOrder(MeituanItemDTO conn, SaasPlatformDTO saasPlatform, WuLiuAddOrUpdateReqVO model) {
        String logMsg = "";
        CreateReturnResultDTO info = new CreateReturnResultDTO();
        if (saasPlatform != null) {
            //标识是九机自己调用
            saasPlatform.setIsJiuJi(true);
            JSONObject reqjson = new JSONObject();
            reqjson.set("data", conn);
            reqjson.set("saasPlatform", saasPlatform);

            // 请求参数
            CreateOrderByShopVO req = new CreateOrderByShopVO();
            req.setDeliveryId(conn.getDeliveryId());
            req.setComment("");
            req.setDeliveryServiceCode(conn.getDeliveryServiceCode());
            req.setExpressType(1);
            req.setGoodsDetailList(conn.getGoodsDetail() == null ? "" : conn.getGoodsDetail());
            req.setGoodsValue(DecimalFormatUtils.decimalFormat(Optional.ofNullable(conn.getGoodsValue()).orElse(BigDecimal.ZERO)));
            BigDecimal weight = Optional.ofNullable(conn.getGoodsWeight()).orElse(BigDecimal.ONE);
            if (weight.compareTo(BigDecimal.ZERO) <= 0) {
                weight = BigDecimal.ONE;
            }
            req.setGoodsWeight(DecimalFormatUtils.decimalFormat(weight));
            req.setOrderId(String.valueOf(saasPlatform.getWuliuId()));
            // 订单类型，默认为0 0: 即时单(尽快送达，限当日订单) 1: 预约单
            req.setOrderType(Optional.ofNullable(conn.getOrderType()).orElse(0));
            req.setOuterOrderSourceDesc("201");
            req.setOuterOrderSourceNo(String.valueOf(conn.getDeliveryId()));
            req.setReceiverAddress(conn.getReceiverAddress());
            req.setReceiverLat(Double.valueOf(Optional.ofNullable(conn.getReceiverLat()).orElse(0)));
            req.setReceiverLng(Double.valueOf(Optional.ofNullable(conn.getReceiverLng()).orElse(0)));
            req.setReceiverName(conn.getReceiverName());
            req.setReceiverPhone(conn.getReceiverPhone());
            req.setXTenantId(Long.valueOf(saasPlatform.getSaasTenant()));
            req.setExpressType(LogisticsTypeEnum.MEI_TUAN.getCode());
            req.setReceiveShopId(conn.getReceiveShopId());
            req.setReceiveShopName(conn.getReceiveShopName());
            req.setSendShopId(conn.getSendShopId());
            req.setSendShopName(conn.getSendShopName());
            req.setNote(conn.getNote());

            if(Optional.ofNullable(model.getDanHaoBind()).orElse(0) > 0 && isMeituanFastSub(model.getDanHaoBind())) {
                String buyerMobile = thirdPlatformOrderService.getBuyerMobile(Long.valueOf(model.getDanHaoBind()));
                req.setNote(StringUtils.isNotBlank(buyerMobile) ? Optional.ofNullable(conn.getNote()).orElse("") + "收件人电话号码：" + buyerMobile : Optional.ofNullable(conn.getNote()).orElse(""));
            }

            // 添加签名
            String secretByCode = secretCodeConfigService.getSecretByCode(SECRET_BY_CODE);
            long currentTimeMillis = System.currentTimeMillis();
            String sign = OaAuthUtil.signBySecret(secretByCode, currentTimeMillis, req);
            OaSignReq<CreateOrderByShop> param = new OaSignReq<>(sign, currentTimeMillis, req);
            log.info("美团中台创建订单参数:{},未签名参数:{}", JSONUtil.toJsonStr(param), JSONUtil.toJsonStr(req));
            // 相应结果
            String result = LogisticsHttpClient.post(JiuJiApi.CREATE_ORDER_BY_SHOP, param);
            R<CreateOrderRes> json = JSON.parseObject(result, new TypeReference<R<CreateOrderRes>>() {
            });
            if (!Objects.equals(json.getCode(), ResultCode.SUCCESS) || json.getData() == null) {
                log.error("美团中台创建订单异常!{}", json.getUserMsg());
                weixinAndOaMessageSend("美团中台创建订单异常：" + json.getUserMsg(), 3, "", WuLiuConstant.ZLX_CH999_ID, 7, null);
                throw new CustomizeException(json.getUserMsg());
            }
            info.setCode(json.getCode());
            info.setMessage(json.getUserMsg());
            info.setOrderId(String.valueOf(json.getData().getLogisticsId()));
            info.setDeliveryId(String.valueOf(json.getData().getDeliveryId()));
            info.setMtPeisongId(json.getData().getPlatformInsideId());
            return info;
        }
        if(Optional.ofNullable(model.getDanHaoBind()).orElse(0) > 0 && isMeituanFastSub(model.getDanHaoBind())) {
            String buyerMobile = thirdPlatformOrderService.getBuyerMobile(Long.valueOf(model.getDanHaoBind()));
            conn.setNote(StringUtils.isNotBlank(buyerMobile) ? Optional.ofNullable(conn.getNote()).orElse("") + "收件人电话号码：" + buyerMobile : Optional.ofNullable(conn.getNote()).orElse(""));
        }
        // 快递合约到期产品类型调整 https://jiuji.yuque.com/docs/share/3ac5455e-f593-47f1-8655-696ed2dc99b2
        conn.setDeliveryServiceCode(isEnableNewCombo() ? 100004 : 4011);
        conn.setOuterOrderSourceDesc("201");
        conn.setTimestamp(String.valueOf(System.currentTimeMillis() / 1000));
        conn.setSign(getMeiTuanSign(conn));
        String url = "https://peisongopen.meituan.com/api/";
        url += "order/createByShop";

        StringBuilder str = new StringBuilder();
        Map<String, Object> stringObjectMap = JacksonJsonUtils.toMap(conn, String.class, Object.class);
        stringObjectMap.forEach((key, value) -> str.append(String.format("&%s=%s", key, value)));
        String result = "";
        MeiTuanResult<CreateReturnResultDTO> json;
        try {
            result = HttpUtil.post(url, stringObjectMap);
            json = JSON.parseObject(result, new TypeReference<MeiTuanResult<CreateReturnResultDTO>>() {
            });

            if (Objects.equals(json.getCode(), 0)) {
                info.setCode(0);
                info.setDeliveryId(String.valueOf(json.getData().getDeliveryId()));
                info.setMtPeisongId(json.getData().getMtPeisongId());
                info.setOrderId(json.getData().getOrderId());
            } else {
                info.setCode(5000);
                info.setMessage("美团快递提示：" + json.getMessage());
            }

        } catch (Exception e) {
            log.error("美团快递创建报错: 入参={}，入参={}", JacksonJsonUtils.toJson(stringObjectMap), info, e);
            info.setCode(5000);
            info.setMessage(e.getMessage());
        }
        log.warn("美团快递快递创建，入参={}，出参={}", JacksonJsonUtils.toJson(stringObjectMap), JacksonJsonUtils.toJson(info));

        return info;
    }

    /**
     * 是否启用新的快递套餐
     *
     * @return boolean
     * <AUTHOR> [<EMAIL>]
     * @date 2022-02-23
     */
    public static boolean isEnableNewCombo() {
        boolean bool = false;
        if (RedisUtils.hasKey(CacheKey.Redis.WULIU_IS_ENABLE_NEW_COMBO)) {
            bool = Objects.equals("1", RedisUtils.get(CacheKey.Redis.WULIU_IS_ENABLE_NEW_COMBO));
        }
        return bool;
    }

    public boolean isMeituanFastSub(Integer subId) {
        if(Optional.ofNullable(subId).orElse(0) <= 0) {
            return false;
        }
       return Optional.ofNullable(baseMapper.countMeituanFastSub(subId)).orElse(0) > 0;
    }

    /**
     * meituanServices.GetMeituanAreaConfig
     * 从配置中获取美团门店配置
     *
     * @return Map<Integer, String>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-10-27
     */
    private Map<Integer, String> getMeituanAreaConfig() {

        Map<Integer, String> meituanArea2 = new HashMap<>();
        meituanArea2.put(71, "170");
        meituanArea2.put(37, "215");
        meituanArea2.put(664, "123");
        meituanArea2.put(279, "124");
        meituanArea2.put(60, "125");
        meituanArea2.put(130, "126");
        meituanArea2.put(11, "127");
        meituanArea2.put(206, "128");
        meituanArea2.put(20, "129");
        meituanArea2.put(61, "130");
        meituanArea2.put(283, "131");
        meituanArea2.put(164, "132");
        meituanArea2.put(274, "133");
        meituanArea2.put(176, "134");
        meituanArea2.put(235, "135");
        meituanArea2.put(518, "136");
        meituanArea2.put(535, "137");
        meituanArea2.put(53, "138");
        meituanArea2.put(259, "139");
        meituanArea2.put(260, "140");
        meituanArea2.put(237, "141");
        meituanArea2.put(255, "142");
        meituanArea2.put(267, "143");
        meituanArea2.put(277, "144");
        meituanArea2.put(312, "145");
        meituanArea2.put(166, "146");
        meituanArea2.put(507, "147");
        meituanArea2.put(8, "148");
        meituanArea2.put(285, "149");
        meituanArea2.put(174, "150");
        meituanArea2.put(186, "151");
        meituanArea2.put(92, "152");
        meituanArea2.put(162, "153");
        meituanArea2.put(250, "154");
        meituanArea2.put(348, "155");
        meituanArea2.put(9, "156");
        meituanArea2.put(24, "157");
        meituanArea2.put(80, "158");
        meituanArea2.put(182, "159");
        meituanArea2.put(154, "160");
        meituanArea2.put(57, "161");
        meituanArea2.put(202, "162");
        meituanArea2.put(244, "163");
        meituanArea2.put(122, "164");
        meituanArea2.put(116, "165");
        meituanArea2.put(132, "166");
        meituanArea2.put(33, "168");
        meituanArea2.put(127, "169");
        meituanArea2.put(109, "171");
        meituanArea2.put(242, "172");
        meituanArea2.put(4, "173");
        meituanArea2.put(133, "174");
        meituanArea2.put(240, "175");
        meituanArea2.put(125, "176");
        meituanArea2.put(83, "177");
        meituanArea2.put(169, "178");
        meituanArea2.put(201, "179");
        meituanArea2.put(616, "180");
        meituanArea2.put(626, "181");
        meituanArea2.put(90, "182");
        meituanArea2.put(451, "183");
        meituanArea2.put(105, "184");
        meituanArea2.put(38, "185");
        meituanArea2.put(151, "186");
        meituanArea2.put(227, "187");
        meituanArea2.put(43, "188");
        meituanArea2.put(1, "189");
        meituanArea2.put(35, "190");
        meituanArea2.put(204, "191");
        meituanArea2.put(73, "192");
        meituanArea2.put(265, "193");
        meituanArea2.put(295, "194");
        meituanArea2.put(281, "195");
        meituanArea2.put(103, "196");
        meituanArea2.put(2, "197");
        meituanArea2.put(251, "198");
        meituanArea2.put(42, "199");
        meituanArea2.put(241, "200");
        meituanArea2.put(62, "201");
        meituanArea2.put(449, "202");
        meituanArea2.put(532, "203");
        meituanArea2.put(301, "204");
        meituanArea2.put(300, "205");
        meituanArea2.put(177, "206");
        meituanArea2.put(81, "207");
        meituanArea2.put(3, "208");
        meituanArea2.put(27, "209");
        meituanArea2.put(294, "210");
        meituanArea2.put(395, "211");
        meituanArea2.put(504, "212");
        meituanArea2.put(282, "213");
        meituanArea2.put(414, "214");
        meituanArea2.put(110, "216");
        meituanArea2.put(269, "217");
        meituanArea2.put(393, "218");
        meituanArea2.put(346, "219");
        meituanArea2.put(516, "220");
        meituanArea2.put(67, "221");
        meituanArea2.put(15, "222");
        meituanArea2.put(391, "223");
        meituanArea2.put(21, "224");
        meituanArea2.put(55, "225");
        meituanArea2.put(135, "226");
        meituanArea2.put(436, "227");
        meituanArea2.put(508, "228");
        meituanArea2.put(199, "229");
        meituanArea2.put(292, "230");
        meituanArea2.put(131, "231");
        meituanArea2.put(314, "232");
        meituanArea2.put(345, "233");
        meituanArea2.put(86, "234");
        meituanArea2.put(118, "235");
        meituanArea2.put(480, "236");
        meituanArea2.put(17, "237");
        meituanArea2.put(52, "238");
        meituanArea2.put(179, "239");
        meituanArea2.put(347, "240");
        meituanArea2.put(129, "241");
        meituanArea2.put(40, "242");
        meituanArea2.put(253, "243");
        meituanArea2.put(111, "244");
        meituanArea2.put(51, "245");
        meituanArea2.put(82, "246");
        meituanArea2.put(85, "247");
        meituanArea2.put(141, "248");
        meituanArea2.put(77, "249");
        meituanArea2.put(220, "250");
        meituanArea2.put(26, "251");
        meituanArea2.put(184, "252");
        meituanArea2.put(307, "253");
        meituanArea2.put(56, "254");
        meituanArea2.put(128, "255");
        meituanArea2.put(173, "256");
        meituanArea2.put(229, "257");
        meituanArea2.put(58, "258");
        meituanArea2.put(185, "259");
        meituanArea2.put(397, "260");
        meituanArea2.put(349, "261");
        meituanArea2.put(144, "262");
        meituanArea2.put(10, "263");
        meituanArea2.put(192, "264");
        meituanArea2.put(197, "265");
        meituanArea2.put(25, "266");
        meituanArea2.put(6, "267");
        meituanArea2.put(188, "268");
        meituanArea2.put(296, "269");
        meituanArea2.put(104, "270");
        meituanArea2.put(243, "271");
        meituanArea2.put(302, "272");
        meituanArea2.put(526, "273");
        meituanArea2.put(303, "274");
        meituanArea2.put(273, "275");
        meituanArea2.put(66, "276");
        meituanArea2.put(505, "277");
        meituanArea2.put(47, "278");
        meituanArea2.put(70, "279");
        meituanArea2.put(124, "280");
        meituanArea2.put(44, "281");
        meituanArea2.put(350, "282");
        meituanArea2.put(342, "283");
        meituanArea2.put(256, "284");
        meituanArea2.put(137, "285");
        meituanArea2.put(69, "286");
        meituanArea2.put(45, "287");
        meituanArea2.put(39, "288");
        meituanArea2.put(149, "289");
        meituanArea2.put(78, "290");
        meituanArea2.put(84, "291");
        meituanArea2.put(7, "292");
        meituanArea2.put(161, "293");
        meituanArea2.put(196, "294");
        meituanArea2.put(304, "295");
        meituanArea2.put(152, "296");
        meituanArea2.put(153, "297");
        meituanArea2.put(143, "298");
        meituanArea2.put(32, "299");
        meituanArea2.put(74, "300");
        meituanArea2.put(134, "301");
        meituanArea2.put(59, "302");
        meituanArea2.put(16, "303");
        meituanArea2.put(596, "410");
        meituanArea2.put(595, "411");
        meituanArea2.put(597, "412");
        meituanArea2.put(239, "413");

        ISysConfigService sysConfigService = ApplicationContextUtil.getBean(ISysConfigService.class);
        SysConfig meituanConfig = sysConfigService.getSysConfig().stream()
                .filter((SysConfig x) -> ConfigEnum.ENTERPRISE_MEITUAN.getCode().toString().equals(x.getCode().toString()))
                .findFirst().orElse(null);
        Map<Integer, String> dic = meituanArea2;
        if (!Objects.isNull(meituanConfig) && StringUtils.isNotEmpty(meituanConfig.getValue())) {
            String value = meituanConfig.getValue();
            String[] areaids = value.split(StrPool.COMMA);
            IAreaInfoService areaInfoService = ApplicationContextUtil.getBean(IAreaInfoService.class);
            List<Areainfo> areainfos = areaInfoService.lambdaQuery().in(Areainfo::getId, Arrays.asList(areaids)).list();
            for (String item : areaids) {
                if (dic.containsKey(Integer.parseInt(item))) {
                    continue;
                }

                Areainfo areainfo = areainfos.stream().filter(x -> item.equals(x.getId().toString())).findFirst().orElse(null);
                if (Objects.isNull(areainfo) || StringUtils.isEmpty(areainfo.getArea())) {
                    continue;
                }
                dic.put(Integer.parseInt(item), areainfo.getArea());
            }
        }
        return dic;
    }

    /**
     * zhongtongApiServices.MarkGetmark
     * 大头笔
     *
     * @param sendmarkes
     * @param sendaddress
     * @param receivemarkes
     * @param receiveaddress
     * @param billCode
     * @return String
     * <AUTHOR> [<EMAIL>]
     * @date 2021-10-27
     */
    @Override
    public String markGetmark(String sendmarkes, String sendaddress, String receivemarkes, String receiveaddress, String billCode, Boolean b) {
        b = Optional.ofNullable(b).orElse(false);
        String value = "";
        String sendProvince = "";
        String sendCity = "";
        String sendDistrict = "";
        String receiveProvince = "";
        String receiveCity = "";
        String receiveDistrict = "";
        if (StringUtils.isNotEmpty(sendmarkes)) {
            String[] sendmaks = sendmarkes.split(",");
            if (sendmaks.length >= 3) {
                sendProvince = sendmaks[0];
                sendCity = sendmaks[1];
                sendDistrict = sendmaks[2];
            } else if (sendmaks.length == 2) {
                sendProvince = sendmaks[0];
                sendCity = sendmaks[1];
            } else {
                sendProvince = sendmaks[0];
            }
        }

        if (StringUtils.isNotEmpty(receivemarkes)) {

            String[] receivemaks = receivemarkes.split(",");
            if (receivemaks.length >= 3) {
                receiveProvince = receivemaks[0];
                receiveCity = receivemaks[1];
                receiveDistrict = receivemaks[2];
            } else if (receivemaks.length == 2) {
                receiveProvince = receivemaks[0];
                receiveCity = receivemaks[1];
            } else {
                receiveProvince = receivemaks[0];
            }
        }
        /********************** 九机云南中通配置***************************/
        String companyId = "02ce66d8979744528b75580509451a5d";
        String user = "1000231431";
        String key = "5f6299b18e45";
        String pwd = "14F4HYZD5H";

        /********************** 九机贵州中通配置***************************/
        String gzUser = "ZTO521540460538986";
        String gzCompanyId = "913f6c102aa14967ace49b929ddde50b";
        String gzKey = "d0b6345d8a72";
        String gzPwd = "ALNP9Q1H";

        String cCompanyId = companyId;
        String cKey = key;
        String cUser = user;
        String cPwd = pwd;
        if (sendmarkes.contains(WuLiuConstant.GUIZHOU)) {
            cCompanyId = gzCompanyId;
            cKey = gzKey;
            cUser = gzUser;
            cPwd = gzPwd;
        }

        ZtoCdataDTO cdata = new ZtoCdataDTO();
        cdata.setUnionCode(billCode);
        cdata.setSendProvince(sendProvince);
        cdata.setSendCity(sendCity);
        cdata.setSendDistrict(sendDistrict);
        cdata.setSendAddress(sendaddress);
        cdata.setReceiveProvince(receiveProvince);
        cdata.setReceiveCity(receiveCity);
        cdata.setReceiveDistrict(receiveDistrict);
        cdata.setReceiveAddress(receiveaddress);

        ZopProperties property = new ZopProperties(cCompanyId, cKey);
        ZopClient client = new ZopClient(property);
        ZopPublicRequest request = new ZopPublicRequest();
        request.setUrl("http://japi.zto.cn/bagAddrMarkGetmark");
        request.addParam("company_id", cCompanyId);
        request.addParam("msg_type", "GETMARK");
        request.addParam("data", JSONUtil.toJsonStr(cdata));
        try {
            String rValue = client.execute(request);
            try {
                OrderMarkResultDTO result = JsonParseUtil.toBean(rValue, OrderMarkResultDTO.class);
                if (result != null && Boolean.TRUE.equals(result.getStatus())) {
                    if (Boolean.TRUE.equals(b)) {
                        value = "{\"mark\":\"" + result.getResult().getMark() + "\",\"marke\":\"" + result.getResult().getBagAddr() + "\"}";
                    } else {
                        value = Optional.ofNullable(result.getResult()).orElseGet(OrderMarkItemDTO::new).getMark();
                    }
                }

            } catch (JSONException e) {
                log.error("中通大头笔返回数据：{}", rValue);
                log.error("解析中通大头笔数据异常：{}", Exceptions.getStackTraceAsString(e));
                return value;
            }
        } catch (IOException e) {
            log.error("查询，异常:{}", Exceptions.getStackTraceAsString(e));
        }
        return value;
    }

    /**
     * zhongtongApiServices.CreateOrderGroup
     * 中通运单生成接口
     *
     * @param conn
     * @param sareaid
     * <AUTHOR> [<EMAIL>]
     * @date 2021-10-27
     */
    @Override
    public String createOrderGroup(OrderGroupDTO conn, Integer sareaid) {
        sareaid = Optional.ofNullable(sareaid).orElse(0);
        /********************** 九机云南中通配置***************************/
        ///////////////////// 九机云南中通配置 /////////////////////
        String companyId = "02ce66d8979744528b75580509451a5d";
        String user = "1000231431";
        String key = "5f6299b18e45";
        String pwd = "14F4HYZD5H";

        ///////////////////// 九机贵州中通配置 /////////////////////
        String gzUser = "ZTO521540460538986";
        String gzCompanyId = "913f6c102aa14967ace49b929ddde50b";
        String gzKey = "d0b6345d8a72";
        String gzPwd = "ALNP9Q1H";

        String cCompanyId = companyId;
        String cKey = key;
        String cUser = user;
        String cPwd = pwd;
        if (conn.getSender().getCity().contains(WuLiuConstant.GUIZHOU)) {
            cCompanyId = gzCompanyId;
            cKey = gzKey;
            cUser = gzUser;
            cPwd = gzPwd;
        }

        //物流反馈中通运单会重复，考虑到可能是合作订单号重复,在这里加个随机数
        String randomData = RandomUtil.randomNumbers(8);
        byte[] bytes = randomData.getBytes();
        String orderNum = String.valueOf(Math.abs(BitConverterUtils.toInt(bytes, 0))).substring(0, 4);
        conn.setId(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + orderNum);

        ZtoDataDTO data = new ZtoDataDTO();
        data.setPartner(cUser);
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        data.setDatetime(LocalDateTime.now().format(dateTimeFormatter));
        data.setContent(conn);
        data.setVerify(cPwd);
        ZopProperties property = new ZopProperties(cCompanyId, cKey);
        ZopClient client = new ZopClient(property);
        ZopPublicRequest request = new ZopPublicRequest();
        request.setUrl("http://japi.zto.cn/submitOrderCode");
        request.addParam("data", JSONUtil.toJsonStr(data));
        try {
            String rValue = client.execute(request);
            try {
                OrderGroupResultDTO jdata = JSONUtil.toBean(rValue, OrderGroupResultDTO.class);
                if (!Objects.isNull(jdata) && jdata.getResult()) {
                    this.subOrderInfo(cCompanyId, cKey, jdata.getData().getBillCode()); //快递单号提交订阅
                    if (!conn.getSender().getCity().contains(WuLiuConstant.GUIZHOU)) {
                        openOrderCreate(cCompanyId, cKey, jdata.getData().getBillCode(), conn, sareaid);//预约寄件
                    }
                }
            } catch (JSONException e) {
                log.error("中通下发返回数据：{}, 解析中通下发数据异常：{}", rValue, Exceptions.getStackTraceAsString(e), e);
                return Exceptions.getStackTraceAsString(e);
            }

            return rValue;
        } catch (Exception e) {
            log.error("中通运单下发出错,{}", Exceptions.getStackTraceAsString(e), e);
            return Exceptions.getStackTraceAsString(e);
        }
    }

    /**
     * zhongtongApiServices.OpenOrderCreate
     * 中通网点预约创建入库
     *
     * @param companyid
     * @param key
     * @param mailNo
     * @param conn
     * @param sareaid
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-10
     */
    public void openOrderCreate(String companyid, String key, String mailNo, OrderGroupDTO conn, Integer sareaid) {
        if (Boolean.TRUE.equals(this.isCurAreaHqDcH1(sareaid)) || Optional.ofNullable(sareaid).orElse(0) == 0) {
            return;
        }
        String[] sendCity = conn.getSender().getCity().split(",");
        String[] receiveCity = conn.getReceiver().getCity().split(",");
        if (sendCity.length < 3) {
            String[] city = new String[3];
            city[0] = sendCity.length >= 1 ? sendCity[0] : StringUtils.EMPTY;
            city[1] = sendCity.length >= 2 ? sendCity[1] : StringUtils.EMPTY;
            city[2] = StringUtils.EMPTY;
            sendCity = city;
        }
        if (receiveCity.length < 3) {
            String[] city = new String[3];
            city[0] = receiveCity.length >= 1 ? receiveCity[0] : StringUtils.EMPTY;
            city[1] = receiveCity.length >= 2 ? receiveCity[1] : StringUtils.EMPTY;
            city[2] = StringUtils.EMPTY;
            receiveCity = city;
        }

        ZtoSiteConfigDTO ztoConfig = Optional.ofNullable(getZtoSiteConfig(sareaid)).orElseGet(ZtoSiteConfigDTO::new);
        if (Objects.equals(Optional.ofNullable(ztoConfig.getId()).orElse(0), 0)) {
            return;
        }

        // 生成 8 位随机数
        String randomData = RandomUtil.randomNumbers(8);
        byte[] bytes = randomData.getBytes();
        String orderNum = String.valueOf(Math.abs(BitConverterUtils.toInt(bytes, 0))).substring(0, 4);

        OrderGroupDTO orderGroup = new OrderGroupDTO();
        orderGroup.setPartnerCode(conn.getId());
        orderGroup.setType("1");
        orderGroup.setTradeId(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + orderNum);
        orderGroup.setMailNo(mailNo);
        orderGroup.setSiteCode(ztoConfig.getSiteCode());
        orderGroup.setSiteName(ztoConfig.getSiteName());
        OrderSenderDTO sender = new OrderSenderDTO();
        sender.setId(LocalDateTime.now().format(DateTimeFormatter.ofPattern("MMddHHmmss")) + orderNum);
        sender.setName(conn.getSender().getName());
        sender.setCompany(conn.getSender().getCompany());
        sender.setMobile(conn.getSender().getMobile());
        sender.setProv(sendCity[0]);
        sender.setCity(sendCity[1]);
        sender.setCounty(sendCity[2]);
        sender.setAddress(conn.getSender().getAddress());
        orderGroup.setSender(sender);

        OrderReceiverDTO receiver = new OrderReceiverDTO();
        receiver.setName(conn.getReceiver().getName());
        receiver.setMobile(conn.getReceiver().getMobile());
        receiver.setProv(receiveCity[0]);
        receiver.setCity(receiveCity[1]);
        receiver.setCounty(receiveCity[2]);
        receiver.setAddress(conn.getReceiver().getAddress());
        orderGroup.setReceiver(receiver);

        ZtoResultDTO redata = Optional.ofNullable(orderYuyue(companyid, key, orderGroup, "http://japi.zto.cn/OpenOrderCreate"))
                .orElseGet(ZtoResultDTO::new);
        if (Boolean.TRUE.equals(redata.getStatus())) {
            WuliuZtoYuyueService wuliuZtoYuyueService = ApplicationContextUtil.getBean(WuliuZtoYuyueService.class);
            WuliuZtoYuyue wuliuZtoYuyue = new WuliuZtoYuyue();
            wuliuZtoYuyue.setWuliucode(conn.getId());
            wuliuZtoYuyue.setMailno(mailNo);
            wuliuZtoYuyue.setOrdercode(redata.getResult().getOrderCode());
            wuliuZtoYuyue.setSareaid(sareaid);
            wuliuZtoYuyue.setCompanyid(companyid);
            wuliuZtoYuyue.setCancel(Boolean.FALSE);
            wuliuZtoYuyue.setCreatetime(new Date());
            boolean save = wuliuZtoYuyueService.save(wuliuZtoYuyue);
            if (!save) {
                IAreaInfoService areaInfoService = ApplicationContextUtil.getBean(IAreaInfoService.class);
                Areainfo areainfo = areaInfoService.lambdaQuery().eq(Areainfo::getId, sareaid).one();
                log.error("中通物流预约寄件成功，但记录失败：" + areainfo.getArea() + "(" + sareaid + "),物流运单号：" + mailNo, "DATA:" + JSONUtil.toJsonStr(redata), "13140");
                sendTextMessage("中通物流预约寄件成功，但记录失败：" + areainfo.getArea() + "(" + sareaid + "),物流运单号：" + mailNo + "DATA:" + JacksonJsonUtils.toJson(redata));
            }
        } else {
            IAreaInfoService areaInfoService = ApplicationContextUtil.getBean(IAreaInfoService.class);
            Areainfo areainfo = areaInfoService.lambdaQuery().eq(Areainfo::getId, sareaid).one();
            log.error("中通物流预约寄件失败：" + areainfo.getArea() + "(" + sareaid + "),ORDERID:" + conn.getId() + ",物流运单号：" + mailNo + " DATA:" + JSONUtil.toJsonStr(redata), "1324");
            sendTextMessage("中通物流预约寄件失败：" + areainfo.getArea() + "(" + sareaid + "),ORDERID:" + conn.getId() + ",物流运单号：" + mailNo + " DATA:" + JacksonJsonUtils.toJson(redata));
        }
    }

    /**
     * zhongtongApiServices.CreateOrderGroupNew
     * 中通运单生成新接口
     *
     * @param conn
     * @param sareaid
     * @return orderGroupResult
     * <AUTHOR> [<EMAIL>]
     * @date 2021-10-27
     */
    private OrderGroupResultDTO createOrderGroupNew(OrderGroupDTO conn, Integer sareaid) {
        OrderGroupResultDTO result = new OrderGroupResultDTO();
        ZtoResultDTO order = createOrder(conn, sareaid);
        if (Boolean.TRUE.equals(order.getStatus())) {
            OrderGroupResultItemDTO data = new OrderGroupResultItemDTO()
                    .setBillCode(order.getResult().getBillCode())
                    .setOrderId(order.getResult().getId())
                    .setSiteCode(order.getResult().getSiteCode())
                    .setSiteName(order.getResult().getSiteName());

            result.setResult(true);
            result.setData(data);
        }
        return result;
    }

    /**
     * zhongtongApiServices.CancelOrder
     * 中通取消订单接口
     *
     * @param ztoOrder
     * @param companyid
     * @param reason
     * @return
     */
    private ZtoCancelDTO ztoCancelOrder(String ztoOrder, String companyid, String reason) {
        /********************** 九机云南中通配置***************************/
        ///////////////////// 九机云南中通配置 /////////////////////
        String companyId = "02ce66d8979744528b75580509451a5d";
        String user = "1000231431";
        String key = "5f6299b18e45";
        String pwd = "14F4HYZD5H";

        ///////////////////// 九机贵州中通配置 /////////////////////
        String gzUser = "ZTO521540460538986";
        String gzCompanyId = "913f6c102aa14967ace49b929ddde50b";
        String gzKey = "d0b6345d8a72";
        String gzPwd = "ALNP9Q1H";

        List<ZtoCancelReqDTO> cancelReqList = new ArrayList<>();
        ZtoCancelReqDTO req = new ZtoCancelReqDTO()
                .setOrderCode(ztoOrder)
                .setFieldName("status")
                .setFieldValue("cancel")
                .setReason(reason);
        cancelReqList.add(req);

        if (StringUtils.isNotEmpty(companyid)) {
            companyid = companyId;
        }
        String tmpKey = key;
        if (companyId.equals(companyid)) {
            companyid = gzCompanyId;
            tmpKey = gzKey;
        }

        ZopClient client = new ZopClient(companyid, tmpKey);
        ZopPublicRequest request = new ZopPublicRequest();
        request.setUrl("http://japi.zto.cn/commonOrderUpdate");
        request.addParam("company_id", companyid);
        request.addParam("msg_type", "UPDATE");
        request.addParam("data", JacksonJsonUtils.toJson(cancelReqList));

        String result = null;
        try {
            result = client.execute(request);
            log.info(result);
        } catch (Exception e) {
            log.error("取消中通快递失败", e);
            return new ZtoCancelDTO()
                    .setData(new ArrayList<>())
                    .setMsg("运单取消接口失败：" + e.getMessage());
        }
        return JacksonJsonUtils.toClass(result, ZtoCancelDTO.class);
    }

    /**
     * QYWeiXinService.weixinAndOaMessageSend
     *
     * @param msg
     * @param type
     * @param url
     * @param slias
     * @param msgType
     * @param agentId
     * <AUTHOR> [<EMAIL>]
     * @date 2021-10-27
     */
    @Override
    public void weixinAndOaMessageSend(String msg, Integer type, String url, Integer slias, Integer
            msgType, Integer agentId) {
        MessagePushUtils.pushOaMessage(slias, msg, "", MessagePushUtils.MessagePushOaMessageTypeConstant.ABNORMAL);
    }

    /**
     * subWLService.CreateShansongOrder
     *
     * @param model
     * @param username
     * @param areaId
     * @param needSaveWuliu
     * @return Boolean
     * <AUTHOR> [<EMAIL>]
     * @date 2021-10-27
     */
    private Map<String, String> createShansongOrder(WuLiuAddOrUpdateReqVO model, String username, Integer
            areaId, Boolean needSaveWuliu) {
        Map<String, String> map = new HashMap<>(NumUtil.TWO);
        map.put("wl_id", "0");
        map.put("needSaveWuliu", "true");
        try {
            //寄件人信息
            CityIdListDTO jinfo;
            //上门取件  其他派送
            if (Arrays.asList(NumUtil.SEVEN, NumUtil.EIGHT).contains(model.getWuType())) {
                jinfo = getAreaIdByCityId(model.getSDid(), null);
            } else {
                jinfo = getAreaInfoByArea(model.getSareaid());
            }
            CityIdListDTO cityInfo;
            Areainfo rArea = new Areainfo();
            if (Objects.equals(model.getWuType(), 1)) {
                rArea = Optional.ofNullable(areaInfoService.getAreaInfoByAreaId2(model.getRareaid())).orElseGet(Areainfo::new);
                cityInfo = getAreaDetailInfoByArea(model.getRareaid());
            } else {
                cityInfo = getAreaDetailIdByCityId(Objects.isNull(model.getRCityId()) || Objects.equals(model.getRCityId(), 0) ? model.getRDid() : model.getRCityId());
            }

            Integer wlId;
            if (model.getWuliuid() == NumUtil.ZERO) {
                wlId = wuLiuService.newTransSaveWuLiu(model, areaId, username);
                if (wlId > NumUtil.ZERO) {
                    model.setWuliuid(wlId);
                    model.setNu("");
                }
            }

            if (model.getWuliuid() > NumUtil.ZERO) {
                //获取闪送配置
                ShansongStoreConfigsEntity senderStore = shansongStoreConfigsService.getStoreConfigs().stream().filter(v -> model.getSareaid().equals(v.getAreaId())).findFirst().orElse(null);
                if (ObjectUtils.isEmpty(senderStore)) {
                    Areainfo areaInfo = Optional.ofNullable(areainfoService.getAreaInfoByAreaId2(model.getSareaid())).orElseGet(Areainfo::new);
                    throw new CustomizeException("门店" + areaInfo.getArea() + "未在闪送后台配置");
                }

                LocationVO point = BaiduMapUtil.getLocation(model.getSAddress(), jinfo.getZname());
                LocationVO toPoint = BaiduMapUtil.getLocation(model.getRAddress(), cityInfo.getZname());

                ShansongOrderCalculateVO orderCalcRequest = new ShansongOrderCalculateVO();

                ShansongSenderVO sender = new ShansongSenderVO();
                sender.setFromSenderName(model.getSName());
                sender.setFromAddress(model.getSAddress());
                sender.setFromAddressDetail(model.getSAddress());
                sender.setFromLatitude(point.getLatitude() + "");
                sender.setFromLongitude(point.getLongitude() + "");
                sender.setFromMobile(model.getRMobile());

                orderCalcRequest.setSender(sender);
                orderCalcRequest.setAppointmentDate("");
                orderCalcRequest.setAppointType(0);
                orderCalcRequest.setCityName(jinfo.getZname());
                orderCalcRequest.setDeliveryType(1);
                orderCalcRequest.setStoreId(Long.valueOf(senderStore.getStoreId()));

                ShansongReceiverVO receiver = new ShansongReceiverVO();
                receiver.setAdditionFee(0);
                receiver.setGoodType(ShansongGoodsTypeEnum.ELECTRONIC_PRODUCT);
                receiver.setInsurance(0);
                receiver.setInsuranceProId("");
                receiver.setOrderingSourceNo("");
                receiver.setOrderingSourceType(0);
                receiver.setOrderNo(model.getWuliuid() + "");
                receiver.setRemarks("");
                receiver.setToAddress(model.getRAddress());
                receiver.setToAddressDetail(model.getRAddress());
                receiver.setToLatitude(toPoint.getLatitude() + "");
                receiver.setToLongitude(toPoint.getLongitude() + "");
                receiver.setToMobile(model.getRMobile());
                receiver.setToReceiverName(model.getRName());
                receiver.setWeight(BigDecimal.ZERO.equals(model.getWeight()) ? BigDecimal.ONE : model.getWeight());

                List<ShansongReceiverVO> receiverList = new ArrayList<>();
                receiverList.add(receiver);
                orderCalcRequest.setReceiverList(receiverList);
                orderCalcRequest.setTravelWay(0);

                ShansongOrderCalculateModelVO resp = shansongService.orderCalculate(orderCalcRequest);
                resp = shansongService.placeOrder(resp.getOrderNumber());

                BigDecimal fee = resp.getTotalFeeAfterSave();
                model.setNu(resp.getOrderNumber());
                model.setPrice(fee);

                this.lambdaUpdate()
                        .set(WuLiuEntity::getNu, model.getNu())
                        .set(WuLiuEntity::getPrice, fee.divide(new BigDecimal(100), RoundingMode.HALF_UP))
                        .eq(WuLiuEntity::getId, model.getWuliuid())
                        .update();

                SaveWuLiuLogReq wuLiuLogReq = new SaveWuLiuLogReq();
                wuLiuLogReq.setWuliuid(model.getWuliuid());
                wuLiuLogReq.setInuser(username);
                wuLiuLogReq.setMsg("闪送快递下单成功~");
                wuLiuLogService.writeLogs(wuLiuLogReq);
            }
        } catch (Exception e) {
            log.error("创建闪送订单异常，error={}", e.getMessage(), e);
            SaveWuLiuLogReq wuLiuLogReq = new SaveWuLiuLogReq();
            wuLiuLogReq.setWuliuid(model.getWuliuid());
            wuLiuLogReq.setInuser(username);
            wuLiuLogReq.setMsg(e.getMessage());
            wuLiuLogService.writeLogs(wuLiuLogReq);
        }
        return map;
    }

    /**
     * dadaWuliuService.CreateOrder
     * 创建订单
     *
     * @param recover
     * @return var
     * @date 2021-10-22
     * <AUTHOR> [<EMAIL>]
     */
    private Ch99ResultDataDTO<BigDecimal> dadaCreateOrder(DadaReceiverDTO recover, WuLiuAddOrUpdateReqVO model) {
        Ch99ResultDataDTO<BigDecimal> result = new Ch99ResultDataDTO<>();
        try {
            String cityName = "市辖区".equals(recover.getCityName()) || "县".equals(recover.getCityName())
                    ? recover.getProvince().replace("省", "").replace("市", "")
                    : recover.getCityName().replace("市", "");

            //获取城市code
            DadaCityVO dadaCity = getCityCode().stream().filter(v -> cityName.equals(v.getCityName())).findFirst().orElse(new DadaCityVO());
            if (StringUtils.isEmpty(dadaCity.getCityCode())) {
                throw new CustomizeException("无法匹配达达快递接口城市编码");
            }

            if (!recover.getAddress().startsWith(recover.getProvince())) {
                if (!recover.getAddress().startsWith(recover.getCityName())) {
                    if (!recover.getAddress().startsWith(recover.getCountryName())) {
                        recover.setAddress(recover.getProvince() + recover.getCityName() + recover.getCountryName() + recover.getAddress());
                    } else {
                        recover.setAddress(recover.getProvince() + recover.getCityName() + recover.getAddress());
                    }
                } else {
                    recover.setAddress(recover.getProvince() + recover.getAddress());
                }
            }

            Coordinate coordinate = null;
            boolean flag = StringUtils.isNotBlank(recover.getPosition());
            if (flag) {
                coordinate = new Coordinate(recover.getPosition());
                flag = !CoordinateUtil.outOfChina(coordinate.getLongitude(),coordinate.getLatitude());
            }
            //优先使用门店经纬度信息
            if (flag) {
                coordinate = CoordinateUtil.wgs2gcj(new Coordinate(recover.getPosition()));
                log.info("达达经纬度转换，position={}，res={}", recover.getPosition(), JacksonJsonUtils.toJson(coordinate));
            } else {
                AddressToCoordinateVO addressToCoordinate = GeoCoordinateUtils.addressToCoordinateV2(recover.getAddress(), recover.getCityName());
                Assert.notNull(addressToCoordinate, "该收件地址经纬度获取失败，请检查收件地址("+recover.getAddress()+")是否填写正确");
                coordinate = addressToCoordinate.getMinSegmentMiddleCoordinate();
                Assert.notNull(coordinate, "该收件地址经纬度获取失败，请检查收件地址("+recover.getAddress()+")是否填写正确");
                coordinate = CoordinateUtil.wgs2gcj(coordinate);
                log.info("达达地址转经纬度，address={}，city={}, res={}", recover.getAddress(), recover.getCityName(), coordinate);
            }
            Assert.notNull(coordinate, "请检查收件地址是否填写正确");

            //达达创建订单参数
            DadaOrderAddVO orderAdd = Builder.of(DadaOrderAddVO::new)
                    .with(DadaOrderAddVO::setCallback, DadaAppConstant.DADA_CALLBACK_URL)
                    .with(DadaOrderAddVO::setShopNo, recover.getShopCode())
                    .with(DadaOrderAddVO::setOriginId, recover.getOrderId())
                    .with(DadaOrderAddVO::setCityCode, dadaCity.getCityCode())
                    .with(DadaOrderAddVO::setCargoPrice, BigDecimal.ONE)
                    .with(DadaOrderAddVO::setIsPrepay, 0)
                    .with(DadaOrderAddVO::setReceiverName, recover.getName())
                    .with(DadaOrderAddVO::setReceiverAddress, recover.getAddress())
                    .with(DadaOrderAddVO::setReceiverLng, coordinate.getLongitude())
                    .with(DadaOrderAddVO::setReceiverLat, coordinate.getLatitude())
                    .with(DadaOrderAddVO::setCargoWeight, 1.0)
                    .with(DadaOrderAddVO::setReceiverPhone, recover.getMobile())
                    .build();

            if(Optional.ofNullable(model.getDanHaoBind()).orElse(0) > 0 && isMeituanFastSub(model.getDanHaoBind())) {
                String buyerMobile = thirdPlatformOrderService.getBuyerMobile(Long.valueOf(model.getDanHaoBind()));
                orderAdd.setInfo(StringUtils.isNotBlank(buyerMobile) ? "收件人电话号码：" + buyerMobile : "");
            }
            String mobile = Optional.ofNullable(SysUtils.getUser()).map(v -> ch999UserService.getUserByCh999Id(v.getUserId()))
                    .map(Ch999User::getMobile).orElse(model.getSMobile());
            if (StringUtils.isNotBlank(mobile)) {
                orderAdd.setInfo(StrUtil.format("取件码：{} {} 寄件人备用电话：{}", model.getWuliuid(), Optional.ofNullable(orderAdd.getInfo()).orElse(""), mobile));
            }

            //调用达达创建订单接口
            String paramJson = JsonParseUtil.toJson(orderAdd);
            dadaIsOnline = SysUtils.isJiuJiProd();

            DadaRequestClient dadaClient = new DadaRequestClient(DadaUrlConstant.ORDER_ADD_URL, paramJson, dadaIsOnline);
            DadaApiResponse apiResponse = dadaClient.callRpc();

            if (ObjectUtils.isEmpty(apiResponse)) {
                log.error("达达快递创建={}，调用达达快递下单接口失败", paramJson);
                throw new Exception("调用达达快递下单接口失败");
            }

            log.warn("达达快递创建={}，出参={}", paramJson, JacksonJsonUtils.toJson(apiResponse));

            //下单成功
            if (DadaAppConstant.DADA_SUCCESS.equals(apiResponse.getStatus())) {
                DadaAddOrderResVO dadaAddOrderRes = BeanUtil.toBean(apiResponse.getResult(), DadaAddOrderResVO.class);
                result.setStats(1);
                result.setData(dadaAddOrderRes.getFee());
            } else {
                result.setMsg("达达下单失败" + apiResponse.getMsg());
            }
        } catch (Exception ex) {
            log.error("达达下单异常", ex);
            result.setMsg("达达物流下单失败" + ex.getMessage());
            weixinAndOaMessageSend("达达物流下单异常，物流单" + recover.getOrderId() + ": " + ex.getMessage() + "," + "DATA:" + JsonParseUtil.toJson(recover), 3, "", WuLiuConstant.ZLX_CH999_ID, null, null);
        }
        return result;
    }

    /**
     * C#:dadaWuliuService.getCityCode
     * <p>
     * 达达查询城市code
     *
     * @return
     */
    @Override
    public List<DadaCityVO> getCityCode() {
        String cacheKey = WuLiuConstant.WU_LIU_DADA_CITYCODE + DadaAppConstant.SOURCE_ID;

        String cityListStr = stringRedisTemplate.opsForValue().get(cacheKey);
        if (StringUtils.isNotEmpty(cityListStr)) {
            return JSONUtil.toList(cityListStr, DadaCityVO.class);
        }

        dadaIsOnline = SysUtils.isJiuJiProd();

        //调用达达获取cityCode
        DadaRequestClient dadaClient = new DadaRequestClient(DadaUrlConstant.CITY_LIST_URL, "", dadaIsOnline);
        DadaApiResponse apiResponse = dadaClient.callRpc();
        if (ObjectUtils.isEmpty(apiResponse)) {
            return new ArrayList<>();
        }
        List<DadaCityVO> cityCodeRes = JSONUtil.toList(JSONUtil.toJsonStr(apiResponse.getResult()), DadaCityVO.class);
        if (CollectionUtils.isNotEmpty(cityCodeRes)) {
            stringRedisTemplate.opsForValue().set(cacheKey, JSONUtil.toJsonStr(cityCodeRes), 180, TimeUnit.MINUTES);
            return cityCodeRes;
        } else {
            return new ArrayList<>();
        }

    }

    /**
     * JDApiServices.CreateNo
     * 生成京东快递单
     *
     * @param sender
     * @param receiver
     * @param remark
     * @param saasPlatform SAAS平台共享
     * @param orderId
     * @param packageCount
     * @return CH99ResultData<JdOrderResponse>
     * @date 2021-10-22
     * <AUTHOR> [<EMAIL>]
     */
    public Ch99ResultDataDTO<JdOrderResponseDTO> jdCreateNo(AddressDTO sender, AddressDTO receiver, String remark, SaasPlatformDTO saasPlatform, String orderId, Integer packageCount) {
        IWuLiujdService wuLiujdService = ApplicationContextUtil.getBean(IWuLiujdService.class);
        return wuLiujdService.jdCreateNo(sender, receiver, remark, saasPlatform, orderId, packageCount);
    }

    /**
     * WuliuService.SetAbnomalWuliu
     * 设置异常物流单
     *
     * @param model
     * @return bool
     * @date 2021-10-21
     * <AUTHOR> [<EMAIL>]
     */
    public boolean setAbnomalWuliu(AbnomalWuliuRequestDTO model) {
        boolean hasUpdate;
        model = Optional.ofNullable(model).orElseThrow(() -> new CustomizeException("异常物流信息不能为空"));
        if (StringUtils.isBlank(model.getRemark())) {
            log.warn("备注不能为空, model={}", JacksonJsonUtils.toJson(model));
            return false;
        }
        if (Objects.isNull(model.getCh999Id()) || model.getCh999Id() <= 0) {
            throw new CustomizeException("操作人不能为空");
        }
        if (StringUtils.isBlank(model.getWuliuId())) {
            throw new CustomizeException("物流单号无效");
        }
        if (Boolean.TRUE.equals(model.getIsCancel())) {
            hasUpdate = markAbnomalWuLiuRecordService.lambdaUpdate()
                    .eq(MarkAbnomalWuLiuRecord::getWuLiuId, model.getWuliuId())
                    .set(MarkAbnomalWuLiuRecord::getDel, 1)
                    .update();
        } else {
            int count = markAbnomalWuLiuRecordService.lambdaQuery()
                    .eq(MarkAbnomalWuLiuRecord::getWuLiuId, model.getWuliuId())
                    .count();
            if (count <= 0) {
                MarkAbnomalWuLiuRecord markAbnomalWuLiuRecord = new MarkAbnomalWuLiuRecord()
                        .setWuLiuId(Long.valueOf(model.getWuliuId()))
                        .setRemark(model.getRemark())
                        .setCh999Id(Long.valueOf(model.getCh999Id()))
                        .setDTime(LocalDateTime.now());
                hasUpdate = markAbnomalWuLiuRecordService.save(markAbnomalWuLiuRecord);
            } else {
                hasUpdate = markAbnomalWuLiuRecordService.lambdaUpdate()
                        .eq(MarkAbnomalWuLiuRecord::getWuLiuId, model.getWuliuId())
                        .set(MarkAbnomalWuLiuRecord::getDel, 0)
                        .update();
            }
        }
        return hasUpdate;
    }

    /**
     * orderTransferServices.subWuliuTransferLog
     * 订单调拨第三方派送日志
     *
     * @param wuliuid
     * @param userName
     * @date 2021-10-21
     * <AUTHOR> [<EMAIL>]
     */
    public void subWuliuTransferLog(Integer wuliuid, String userName) {
        List<SubWuliuTransferLogDTO> wuliuTransferLogDTOList = this.baseMapper.getThirdPartyLog(wuliuid);
        String comment;
        String otherDsc;

        for (SubWuliuTransferLogDTO dto : wuliuTransferLogDTOList) {
            if (StringUtils.isBlank(dto.getCom()) || StringUtils.isBlank(dto.getNu())) {
                continue;
            }
            otherDsc = "";
            if (dto.getBasketCount().equals(dto.getLcount())) {
                otherDsc = "x【" + dto.getLcount() + "】";
            }

            String expressCompany = getWuliuCompanyName(dto.getCom());
            if (expressCompany.startsWith("顺丰")) {
                expressCompany = "顺丰快递";
            }
            comment = "您的【" + dto.getProductName() + " " + dto.getProductColor() + "】" + otherDsc + "已由" + expressCompany
                    + "发出。 <a href='javascript:void(0)' onclick='wuliutrackShow(this" + wuliuid + ",1)'>查看物流信息</a>";
            SubLogVO subLogVO = new SubLogVO()
                    .setComment(comment)
                    .setShowType(true)
                    .setSubId(dto.getSubId().intValue())
                    .setInUser(userName)
                    .setType(1);
            opLog(subLogVO);
        }

    }

    /**
     * orderTransferServices.subWuliuTransferLogPush
     * 订单调拨第三方派送日志 推送
     *
     * @param wuliuid
     * @param userName
     * @date 2021-10-21
     * <AUTHOR> [<EMAIL>]
     */
    @Override
    public void subWuliuTransferLogPush(Integer wuliuid, String userName) {
        String act = "subWuliuTransferLogPush";
        SubRabbitMqDTO.SubRabbitMqData data = new SubRabbitMqDTO.SubRabbitMqData();
        data.setWuliuid(OptionalUtils.ifNotNull(wuliuid, Long::valueOf));
        data.setUserName(userName);
        SubRabbitMqDTO subRabbitMqDTO = new SubRabbitMqDTO();
        subRabbitMqDTO.setAct(act);
        subRabbitMqDTO.setData(data);
        setRabbitMqMessageForCsharp(JacksonJsonUtils.toJson(subRabbitMqDTO));
    }

    /**
     * 写入一条日志
     *
     * @param model
     * @param username
     * @param msg
     * @return JsonNode
     * @date 2021-10-21
     * <AUTHOR> [<EMAIL>]
     */
    public JsonNode writeLogs(WuLiuAddOrUpdateReqVO model, String username, String msg) {
        writewuliulogs(model.getWuliuid(), username, msg, null);
        return null;
    }

    /**
     * 写入物流日志
     *
     * @param wuliuid
     * @param inuser
     * @param msg
     * @return boolean 返回写入成功与否
     * @date 2021-10-21
     * <AUTHOR> [<EMAIL>]
     */
    public boolean writewuliulogs(Integer wuliuid, String inuser, String msg, Integer wuliuStatistcsCategray) {
        wuliuStatistcsCategray = Optional.ofNullable(wuliuStatistcsCategray).orElse(0);
        WuLiuLogEntity entity = new WuLiuLogEntity();
        try {
            entity.setWuliuid(wuliuid)
                    .setInuser(inuser)
                    .setMsg(msg.replace("'", ""))
                    .setDtime(LocalDateTime.now())
                    .setStatistcsCategray(wuliuStatistcsCategray);
            return wuLiuLogService.save(entity);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * subWLService.saveWuliuWandDian
     * 保存物流网点信息(中通，顺风)
     *
     * @param wuliuid
     * @param orgcode
     * @param destcode
     * @param exepresstype
     * @param paytype
     * @param yuejiekahao
     * @return bool
     * @date 2021-10-21
     * <AUTHOR> [<EMAIL>]
     */
    public boolean saveWuliuWandDian(Integer wuliuid, String orgcode, String destcode, String exepresstype, Integer
            paytype, String yuejiekahao) {
        yuejiekahao = Optional.ofNullable(yuejiekahao).orElse("");

        if (Objects.isNull(wuliuid) || Objects.equals(wuliuid, 0)) {
            return false;
        }
        String exepresstypeStr = "";
        if (StringUtils.isNotBlank(exepresstype)) {
            exepresstypeStr = getExepressType(exepresstype);
        }
        String payTypeStr = "";
        if (Objects.equals(paytype, 1)) {
            payTypeStr = "寄付月结";
        } else if (Objects.equals(paytype, 2)) {
            payTypeStr = "寄送方付";
        } else if (Objects.equals(paytype, 3)) {
            payTypeStr = "第三方付";
        }

        WuLiuWuliuwangdianEntity obj = wuLiuWuliuwangdianService.getOne(
                Wrappers.<WuLiuWuliuwangdianEntity>lambdaQuery()
                        .select(WuLiuWuliuwangdianEntity::getId)
                        .eq(WuLiuWuliuwangdianEntity::getWuliuid, wuliuid).orderByDesc(WuLiuWuliuwangdianEntity::getId).last("offset 0 row fetch next 2 row only"), false);
        if (obj == null) {
            WuLiuWuliuwangdianEntity entity = new WuLiuWuliuwangdianEntity()
                    .setWuliuid(wuliuid)
                    .setOrgcode(orgcode)
                    .setDestcode(destcode)
                    .setExepresstype(exepresstypeStr)
                    .setPayType(payTypeStr)
                    .setYuejiekahao(yuejiekahao);
            return wuLiuWuliuwangdianService.save(entity);

        } else {
            if (StringUtils.isNotBlank(exepresstypeStr) || StringUtils.isNotBlank(destcode)) {
                wuLiuWuliuwangdianService.lambdaUpdate().set(WuLiuWuliuwangdianEntity::getOrgcode, orgcode)
                        .set(WuLiuWuliuwangdianEntity::getDestcode, destcode)
                        .set(WuLiuWuliuwangdianEntity::getExepresstype, exepresstypeStr)
                        .set(WuLiuWuliuwangdianEntity::getYuejiekahao, yuejiekahao)
                        .eq(WuLiuWuliuwangdianEntity::getWuliuid, wuliuid).update();
            }
            return false;
        }

    }



    /**
     * shunfengApiServices.GetExepressType
     *
     * @param type
     * @return string
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-04
     */
    @Override
    public String getExepressType(String type) {
        if (StringUtils.isBlank(type)) {
            return "";
        }
        if ("顺丰标快（全国套餐）".equals(type)) {
            return "2_9386_3";
        }
        if ("2_9386_3".equals(type)) {
            return "顺丰标快（全国套餐）";
        }
        if ("顺丰干配（大仓专用）".equals(type)) {
            return "2_2312_1";
        }
        if ("2_2312_1".equals(type)) {
            return "顺丰干配（大仓专用）";
        }
        Map<String, String> dict = new HashMap<>(13);

        dict.put("2_2312_1", "省内(0-3公斤)+省外全部");
        dict.put("1_2322_2", "省内(3-20公斤)");
        dict.put("1_2322_3", "省内(0-20公斤)");
        dict.put("154_9386_1", "顺丰重货（发全国20-100KG）");
        dict.put("155_9386_2", "顺丰零担（发全国100KG+）");
        dict.put("2_9386_3", "顺丰标快（发全国0-20KG）");
        dict.put("1", "顺丰标快(发省内3-20公斤)");
        dict.put("2", "顺丰特惠");
        dict.put("3", "电商特惠");
        dict.put("37", "云仓专配次日");
        dict.put("38", "云仓专配隔日");
        dict.put("154", "重货包裹");
        dict.put("155", "小票零担");
        dict.put("208", "特惠专配");

        if (dict.containsKey(type)) {
            return dict.get(type);
        } else if (dict.containsValue(type)) {
            return dict.entrySet().stream().filter(d -> Objects.equals(d.getValue(), type)).findFirst().orElseGet(() -> new AbstractMap.SimpleEntry<>("", "")).getKey();
        }
        return "";
    }

    /**
     * 根据门店获取门店地址信息(省市区为别名)
     *
     * @param areaid
     * @return CityIDList
     * @date 2021-10-20
     * <AUTHOR> [<EMAIL>]
     */
    public CityIdListDTO getAreaDetailInfoByArea(Integer areaid) {
        areaid = Optional.ofNullable(areaid).orElse(WuLiuConstant.AREA_DC);
        Areainfo areaInfo = areaInfoService.getAreaInfoByAreaId2(areaid);
        CityIdListDTO item = getAreaDetailIdByCityId(areaInfo.getCityid());
        item.setAddress(areaInfo.getCompanyAddress());
        return item;
    }

    /**
     * 根据cityid获取 pid zid did和地区别名
     *
     * @param cityid
     * @return CityIDList
     * @date 2021-10-20
     * <AUTHOR> [<EMAIL>]
     */
    public CityIdListDTO getAreaDetailIdByCityId(Integer cityid) {
        if (cityid == null) {
            return new CityIdListDTO();
        }
        CityIdListDTO area = new CityIdListDTO();
        String cityidStr = String.valueOf(cityid);
        List<Integer> ids = new ArrayList<>();
        ids.add(cityid);
        if (cityidStr.length() == 6) {
            ids.add(cityid / (int) Math.pow(10, 4));
            ids.add(cityid / (int) Math.pow(10, 2));
        } else if (cityidStr.length() == 4) {
            ids.add(cityid / (int) Math.pow(10, 2));
        }

        List<AreaListRes> areaListResList = wuLiuService.getAreaList();
        List<AreaListRes> areaListResList2 = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(areaListResList)) {
            Stream<AreaListRes> stream = areaListResList.stream();
            stream = stream.filter(item -> !Boolean.TRUE.equals(item.getIsReal()));
            areaListResList2 = stream.filter(item -> ids.contains(item.getCode())).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(areaListResList2)) {
            for (AreaListRes m : areaListResList2) {
                if (m.getLevel() == 1) {
                    area.setPid(m.getCode());
                    area.setPname(m.getName1());
                }
                if (m.getLevel() == 2) {
                    area.setZid(m.getCode());
                    area.setZname(m.getName1());
                }
                if (m.getLevel() == 3) {
                    area.setDid(m.getCode());
                    area.setDname(m.getName1());
                }
            }
        }
        return area;
    }

    /**
     * 通知oa做对应门店订单商品做库存检查
     *
     * @param model
     * @param inUser
     * @date 2021-10-18
     * <AUTHOR> [<EMAIL>]
     */
    public void createWuliuSubCheck(WuLiuAddOrUpdateReqVO model, String inUser) {
        if (Objects.nonNull(model.getWuliuid()) && Objects.nonNull(model.getDanHaoBind()) && Objects.equals(1, model.getSource())) {
            if (WuLiuType.INTERNAL_LOGISTICS.getCode().equals(model.getWuType())
                    && Arrays.asList(WuLiuConstant.MEITUAN, WuLiuConstant.DADA, WuLiuConstant.UU_PAOTUI).contains(model.getCom())) {
                setRabbitMqMessageForCsharp(JacksonJsonUtils.toJson(new RabbitMqActDTO().setAct("wuliuCreateReturn")
                        .setData(new CreateWuliuSubCheckBO()
                                .setSubId(model.getDanHaoBind())
                                .setSubType(model.getSubKinds())
                                .setWuliuId(model.getWuliuid())
                                .setInUser(inUser))
                ));
            }
        }
    }

    /**
     * LpSubService.UpdateWuliuNo
     * 更新物流单号,修改订单收货地址物流单号
     *
     * @param companyName
     * @param wuLiuNo
     * @param subId
     * @param user
     * @date 2021-10-18
     * <AUTHOR> [<EMAIL>]
     */
    public void updateWuliuNo2(String companyName, String wuLiuNo, Long subId, String user) {
        setRabbitMqMessageForCsharp(JacksonJsonUtils.toJson(new RabbitMqActDTO().setAct("UpdateWuliuNo")
                .setData(new UpdateWuLiuNoDTO()
                        .setCompanyName(companyName)
                        .setWuLiuNo(wuLiuNo)
                        .setSubId(subId).setUser(user))
        ));
    }

    /**
     * orderServices.UpdteWuliuNo
     * 更新物流单号,修改订单收货地址物流单号
     *
     * @param companyName
     * @param wuLiuNo
     * @param subId
     * @param user
     * @date 2021-10-18
     * <AUTHOR> [<EMAIL>]
     */
    public void updteWuliuNo(String companyName, String wuLiuNo, Long subId, String user) {
        setRabbitMqMessageForCsharp(JacksonJsonUtils.toJson(new RabbitMqActDTO().setAct("UpdteWuliuNo")
                .setData(new UpdateWuLiuNoDTO()
                        .setCompanyName(companyName)
                        .setWuLiuNo(wuLiuNo)
                        .setSubId(subId).setUser(user))
        ));
    }

    /**
     * apiServices.mkc_logs_add
     * mkc_logs(product_mkc的comment表) 添加操作 无事务 basket_id
     *
     * @param comment
     * @param basketId
     * @param userName
     * @param showType
     * @param otherSql where后面的子句 以" and "开头
     * @return Boolean
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-09
     */
    public Boolean mkcLogsAdd(String comment, String basketId, String userName, Integer showType, String otherSql) {
        Boolean re = true;
        ProductMkc productMkc = productMkcService.lambdaQuery().eq(ProductMkc::getBasketId, basketId).last(otherSql).one();
        MkcLogNew mkcLogNew = new MkcLogNew()
                .setComment(comment)
                .setShowType(showType >= 1)
                .setMkcId(Long.valueOf(productMkc.getId()))
                .setInUser(userName)
                .setDTime(LocalDateTime.now());

        re = mkcLogsAdd(mkcLogNew);
        return re;
    }

    /**
     * apiServices.mkc_logs_add
     *
     * @param mkcLogNew
     * @return
     */
    public Boolean mkcLogsAdd(MkcLogNew mkcLogNew) {
        mkcLogsAdd2(Lists.newArrayList(mkcLogNew));
        return true;
    }

    /**
     * apiServices.mkc_logs_add
     *
     * @param mkcLogNewList
     */
    public void mkcLogsAdd2(List<MkcLogNew> mkcLogNewList) {
        // mongodb 实现
        for (MkcLogNew mkcLogNew : mkcLogNewList) {
            Map<String, Object> map = new HashMap<>();
            map.put("Comment", mkcLogNew.getComment());
            map.put("DTime", mkcLogNew.getDTime());
            map.put("InUser", mkcLogNew.getInUser());
            map.put("showType", mkcLogNew.getShowType());

            Update update = new Update();
            update.push("conts", map);
            mongoTemplate.upsert(new Query(Criteria.where("_id").is(mkcLogNew.getMkcId())), update, "mkcLogsNew");
        }

        // 双写
        mkcLogNewService.saveBatch(mkcLogNewList);
    }

    /**
     * apiServices.isCurAreaHQ_DC_H1
     * 当前门店是否为 HQ_DC_H1
     *
     * @param areaId
     * @return Boolean
     * @date 2021-10-18
     * <AUTHOR> [<EMAIL>]
     */
    @Override
    public boolean isCurAreaHqDcH1(Integer areaId) {
        boolean result = false;
        if (areaId == null) {
            return false;
        }
        if (AreaInfoUtils.isCurrentHq(areaId) || AreaInfoUtils.isCurrentDc(areaId) || AreaInfoUtils.isCurrentH1(areaId)) {
            result = true;
        }
        return result;
    }

    /**
     * apiServices.isCurAreaHQ_DC_H1_D1
     * 当前门店是否为 HQ_DC_H1_D1
     *
     * @param areaId
     * @return Boolean
     * @date 2021-10-18
     * <AUTHOR> [<EMAIL>]
     */
    @Override
    public boolean isCurAreaHqDcH1D1(Integer areaId) {
        boolean result = false;
        if (areaId == null) {
            return false;
        }
        if (AreaInfoUtils.isCurrentHq(areaId) || AreaInfoUtils.isCurrentDc(areaId) || AreaInfoUtils.isCurrentH1(areaId) || AreaInfoUtils.isCurrentD1(areaId)) {
            result = true;
        }
        return result;
    }

    /**
     * shunfengApiServices.SfLogisticsCenterCreateOrder
     * 顺丰物流中台创建订单
     *
     * @param item
     * @param wuLiuId
     * @return R<List < SfCreateOrderResultDTO>>
     * @date 2021-10-16
     * <AUTHOR> [<EMAIL>]
     */
    public R<List<SfCreateOrderResultDTO>> sfLogisticsCenterCreateOrder(SfOrderInfoVO item, String wuLiuId) {
        try {
            Areainfo fromAreaInfo = Optional.ofNullable(areainfoService.getAreaInfoByAreaId2(item.getSendAreaId())).orElseGet(Areainfo::new);
            Areainfo receiveAreaInfo = Optional.ofNullable(areainfoService.getAreaInfoByAreaId2(item.getReceiveAreaId())).orElseGet(Areainfo::new);
            SfParamDTO sfParam = new SfParamDTO();
            sfParam.setSfCreateOrderParams(Collections.emptyList());
            sfParam.setXtenantId(0);
            sfParam.setExpressType(2);
            sfParam.setShopId(item.getSendAreaId());
            sfParam.setShopName(OptionalUtils.ifNotNull(fromAreaInfo.getArea(),
                    item1 -> (fromAreaInfo.getArea() + "(" + fromAreaInfo.getAreaName() + ")")));
            sfParam.setReceiveShopId(item.getReceiveAreaId());
            sfParam.setReceiveShopName(OptionalUtils.ifNotNull(receiveAreaInfo.getArea(),
                    item1 -> (receiveAreaInfo.getArea() + "(" + receiveAreaInfo.getAreaName() + ")")));

            List<SfCreateOrderParamDTO> sfCreateOrderParams = new ArrayList<>();
            String senderAddress = item.getJAddress();
            String receiveAddress = item.getDAddress();
            // 缺少省份信息
            if (StringUtils.isNotBlank(receiveAddress)
                    && !receiveAddress.startsWith(item.getDProvince())
            ) {
                //缺少市级信息
                if (!receiveAddress.startsWith(item.getDCity())) {
                    //缺少区县级信息
                    if (!receiveAddress.startsWith(item.getDCounty())) {
                        receiveAddress = item.getDProvince() + item.getDCity() + item.getDCounty() + receiveAddress;
                    } else {
                        receiveAddress = item.getDProvince() + item.getDCity() + receiveAddress;
                    }
                } else {
                    //缺省信息
                    receiveAddress = item.getDProvince() + receiveAddress;
                }
            }

            //缺少省份信息
            if (StringUtils.isNotBlank(senderAddress) && !senderAddress.startsWith(item.getJProvince()) && !senderAddress.startsWith(Optional.ofNullable(item.getJProvince()).orElse("").replace(WuLiuConstant.PROVINCE, ""))) {
                //缺少市级信息
                if (!senderAddress.startsWith(item.getJCity())) {
                    //缺少区县级信息
                    if (!senderAddress.startsWith(item.getJCounty())) {
                        senderAddress = item.getJProvince() + item.getJCity() + item.getJCounty() + senderAddress;
                    } else {
                        senderAddress = item.getJProvince() + item.getJCity() + senderAddress;
                    }
                }
                //缺省信息
                else {
                    senderAddress = item.getJProvince() + senderAddress;
                }
            }

            if (CollectionUtils.isNotEmpty(item.getList())) {
                SfCreateOrderParamDTO orderParam = new SfCreateOrderParamDTO()
                        .setCustomerOrderNo(wuLiuId)
                        .setBspType(1)
                        .setExpressType(item.getExpressType() != null ? Integer.valueOf(item.getExpressType()) : null)
                        .setProductName(item.getList().get(0).getCargoName())
                        .setPackageNumber(1)
                        .setReceiveName(item.getDContact())
                        .setReceiveTel(item.getDMobile())
                        .setReceiveAddress(receiveAddress)
                        .setSendName(item.getJContact())
                        .setSendTel(item.getJMobile())
                        .setSendAddress(senderAddress);
                sfCreateOrderParams.add(orderParam);
            } else {
                SfCreateOrderParamDTO orderParam = new SfCreateOrderParamDTO()
                        .setCustomerOrderNo(wuLiuId)
                        .setReceiveName(item.getDContact())
                        .setReceiveTel(item.getDMobile())
                        .setReceiveAddress(receiveAddress)
                        .setSendName(item.getJContact())
                        .setSendTel(item.getJMobile())
                        .setSendAddress(senderAddress)
                        .setExpressType(item.getExpressType() != null ? Integer.valueOf(item.getExpressType()) : null)
                        .setBspType(1)
                        .setProductName("")
                        .setPackageNumber(1);
                sfCreateOrderParams.add(orderParam);
            }

            sfParam.setSfCreateOrderParams(sfCreateOrderParams);
            String url = SysConfigUtils.getJiujiMoaUrl(true) + "/cloudapi_nc/logistics/api/logistics-center/create-order-batch/v1";
            // 顺丰中台创建订单数据
            String json = genarateSignRequestPackage(sfParam);
            Map<String, String> headers = new Hashtable<>(2);
            headers.put("xservicename", "logistics-service");
            headers.put("logistics", DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.now()));
            HttpResponse httpResponse = HttpUtil.createPost(url).body(json).headerMap(headers, true).timeout(5000).execute();
            // 顺丰中台创建订单返回结果数据
            String body = httpResponse.body();
            R<List<SfCreateOrderResultDTO>> result;
            try {
                result = JSON.parseObject(body, new TypeReference<R<List<SfCreateOrderResultDTO>>>() {
                });
            } catch (Exception e) {
                log.error("顺丰快递返回数据解析报错={}", Exceptions.getStackTraceAsString(e), e);
                R<SfCreateOrderResultDTO> result2 = new R<>();
                try {
                    result2 = JSON.parseObject(body, new TypeReference<R<SfCreateOrderResultDTO>>() {
                    });
                } catch (Exception e2) {
                    log.error("顺丰快递返回数据解析报错2={}", Exceptions.getStackTraceAsString(e2), e2);
                    result2.setUserMsg("顺丰中台返回结果异常，请稍后再试");
                    result2.setCode(500);
                }
                result = new R<>();
                result.setUserMsg(result2.getUserMsg() + "，请重新提交");
                result.setCode(result2.getCode());
            }

            if (result != null && result.getCode() == 0) {
                log.info("物流单:{}推送到物流中台成功", wuLiuId);
            } else {
                log.error("物流单:{}推送到物流中台失败", wuLiuId);
            }
            return result;
        } catch (Exception e) {
            log.error("sfLogisticsCenterCreateOrder异常信息:{}", Exceptions.getStackTraceAsString(e), e);
            return null;
        }
    }

    /**
     * shunfengApiServices.SfLogisticsCenterCancelOrder
     * 顺丰中台取消快递单
     *
     * @param sforderids
     * @return R<SfCancelOrderResultRes>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-08
     */
    public R<SfCancelOrderResultRes> sfLogisticsCenterCancelOrder(List<String> sforderids) {
        SfCancelOrderParamDTO cancelOrder = new SfCancelOrderParamDTO();
        cancelOrder.setPlatformDeliveryIdList(sforderids);
        cancelOrder.setXTenantId(NumUtil.L_ZERO);
        cancelOrder.setExpressType(LogisticsTypeEnum.SHUN_FENG.getCode());
        String result = LogisticsHttpClient.post(JiuJiApi.CANCLE_ORDER, cancelOrder);
        R<SfCancelOrderResultRes> response = JSONUtil.toBean(result, new cn.hutool.core.lang.TypeReference<R<SfCancelOrderResultRes>>() {
        }, true);
        if (ObjectUtil.isEmpty(response.getData())) {
            return R.error("顺丰中台返回结果异常，请稍后再试");
        }
        if (response.getCode() == ResultCode.SUCCESS) {
            log.info("快递单：{} 顺丰物流中台取消成功.", sforderids);
        } else {
            log.error("快递单：{} 顺丰物流中台失败.", sforderids);
        }
        return response;
    }

    /**
     * LogisticsCommonServices.GenarateSignRequestPackage
     * 构造签名请求
     *
     * @param data
     * @return String
     * @date 2021-10-18
     * <AUTHOR> [<EMAIL>]
     */
    public <T> String genarateSignRequestPackage(T data) {
        CommonApiModelDTO<T> order = new CommonApiModelDTO<>();
        order.setData(data);
        // commonApiModel.EsecretCode
        // 13 14  被人悄悄的用了（没定义枚举，直接数据库占用）
        int esecretCodeLogistics = 15;
        getSecretByEcode(order, esecretCodeLogistics);
        return JacksonJsonUtils.toJson(order);
    }

    /**
     * apiServices.getSecretByECode
     * 获取签名
     *
     * @param conn
     * @param esecretCode
     * @date 2021-10-18
     * <AUTHOR> [<EMAIL>]
     */
    public <T> void getSecretByEcode(CommonApiModelDTO<T> conn, Integer esecretCode) {
        Map<String, Object> ht = new Hashtable<>();
        WuLiuSecretCodeConfigEntity secretObj = getSecretCodeByEcode(esecretCode);
        secretObj = Optional.ofNullable(secretObj).orElseThrow(() -> new CustomizeException("获取签名参数失败！" + esecretCode));
        conn.setTimestamp(System.currentTimeMillis());
        ht.put("secret", secretObj.getSecret());
        ht.put("version", secretObj.getVersion());
        ht.put("timestamp", conn.getTimestamp());
        ht.put("jsonData", JacksonJsonUtils.toJson(conn));
        List<String> keys = new ArrayList<>(ht.keySet());
        StringBuilder str = new StringBuilder();
        for (String key : keys) {
            Object value = ht.get(key);
            String value1 = OptionalUtils.ifTrue(value != null, () -> String.valueOf(value));
            if (StringUtils.isNotBlank(key) && StringUtils.isNotBlank(value1)) {
                str.append(String.format("%s%s", key, value));
            }
        }

        // 盐值 固定值
        String saltValue = "9ji.com";
        str.insert(0, secretObj.getSecret() + saltValue);
        conn.setSign(DigestUtils.sha1Hex(String.valueOf(str)));
    }

    /**
     * apiServices.getSecretCodeByECode
     * 获取接口密钥
     *
     * @param esecretCode
     * @return WuLiuSecretCodeConfigEntity
     * @date 2021-10-18
     * <AUTHOR> [<EMAIL>]
     */
    public WuLiuSecretCodeConfigEntity getSecretCodeByEcode(Integer esecretCode) {
        return wuLiuSecretCodeConfigService.getOne(Wrappers.<WuLiuSecretCodeConfigEntity>lambdaQuery().eq(WuLiuSecretCodeConfigEntity::getCode, esecretCode), false);
    }

    /**
     * apiServices.isHistory
     *
     * 判断是否走历史库查询
     *
     * @return Boolean
     * @date 2021-10-15
     * <AUTHOR> [<EMAIL>]
     */
    @Override
    public boolean isHistory(Integer subId, Integer subType) {
        WuLiuSubSeparateConfigEntity one = wuLiuSubSeparateConfigService.subSeparateConfigCache().stream().filter(item -> Objects.equals(item.getSubType(), subType)).findFirst().orElseGet(WuLiuSubSeparateConfigEntity::new);
        boolean isHistory = Optional.ofNullable(subId).orElse(0) < Optional.ofNullable(one.getSubId()).orElse(0L);
        if (isHistory) {
            SubTypeEnum subTypeEnum = EnumUtil.getEnumByCode(SubTypeEnum.class, subType);
            int count = 0;
            switch (subTypeEnum) {
                case NEW_ORDER:
                    // 新机订单
                    count = wuLiuSubService.lambdaQuery().eq(WuLiuSubEntity::getSubId, subId).count();
                    break;
                case GOOD_PRODUCT_ORDER:
                    // 良品订单
                    count = wuLiuRecoverMarketInfoService.lambdaQuery().eq(WuLiuRecoverMarketInfoEntity::getSubId, subId).count();
                    break;
                case AFTER_BOOKING:
                    // 售后预约
                    count = shouHouYuYueService.lambdaQuery().eq(ShouHouYuYueEntity::getSubId, subId).count();
                    break;
                case AFTER_MAINTENANCE:
                    // 售后维修
                    count = wuLiuShouHouService.lambdaQuery().eq(WuLiuShouHouEntity::getId, subId).count();
                    break;
                case RECYCLE_ORDER:
                    // 回收订单
                    count = wuLiuRecoverSubService.lambdaQuery().eq(WuLiuRecoverSubEntity::getSubId, subId).count();
                    break;
                case SMALL_PICK_UP_ORDER:
                    // 小件接件单
                    count = wuLiuSmallProService.lambdaQuery().eq(WuLiuSmallProEntity::getId, subId).count();
                    break;
                default:
                    break;
            }
            isHistory = count == 0;
        }
        return isHistory;
    }

    /**
     * apiServices.getAreaInfoByArea
     * 根据门店获取门店地址信息
     *
     * @param areaId
     * @return CityIdListDTO
     * @date 2021-10-13
     * <AUTHOR> [<EMAIL>]
     */
    @Override
    public CityIdListDTO getAreaInfoByArea(Integer areaId) {
        areaId = Optional.ofNullable(areaId).orElse(WuLiuConstant.AREA_DC);
        AtomicReference<CityIdListDTO> item = new AtomicReference<>();
        Optional.ofNullable(areainfoService.getAreaInfoByAreaId2(areaId))
                .ifPresent(areaInfo -> {
                    item.set(getAreaIdByCityId(areaInfo.getCityid(), 1));
                    item.get().setAddress(areaInfo.getCompanyAddress());
                });
        return item.get();
    }

    /**
     * apiServices.getAreaIDByCityID
     * 根据 cityId 获取 pid zid did
     *
     * @param cityId
     * @param type
     * @return CityIdListDTO
     * @date 2021-10-13
     * <AUTHOR> [<EMAIL>]
     */
    @Override
    public CityIdListDTO getAreaIdByCityId(Integer cityId, Integer type) {
        type = Optional.ofNullable(type).orElse(1);
        CityIdListDTO area = new CityIdListDTO();
        area.setPname("").setDname("").setZname("").setStreetName("");
        String cityIdStr = OptionalUtils.ifNotNull(cityId, String::valueOf, item -> "");
        List<Integer> ids = new ArrayList<>();
        ids.add(cityId);
        if (cityIdStr.length() == 9) {
            ids.add(cityId / (int) Math.pow(10, 7));
            ids.add(cityId / (int) Math.pow(10, 5));
            ids.add(cityId / (int) Math.pow(10, 3));
        } else if (cityIdStr.length() == 6) {
            ids.add(cityId / (int) Math.pow(10, 4));
            ids.add(cityId / (int) Math.pow(10, 2));
        } else if (cityIdStr.length() == 4) {
            ids.add(cityId / (int) Math.pow(10, 2));
        }
        List<AreaListRes> areaListResList = getAreaList(type);
        if (CollectionUtils.isNotEmpty(areaListResList)) {
            List<AreaListRes> areaListResList2 = areaListResList.stream().filter(item -> ids.contains(item.getCode())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(areaListResList2)) {
                for (AreaListRes m : areaListResList2) {
                    if (Objects.equals(m.getLevel(), 1)) {
                        area.setPid(m.getCode())
                                .setPname(m.getName());
                    }
                    if (Objects.equals(m.getLevel(), 2)) {
                        area.setZid(m.getCode())
                                .setZname(m.getName());
                    }
                    if (Objects.equals(m.getLevel(), 3)) {
                        area.setDid(m.getCode())
                                .setDname(m.getName());
                    }
                    if (Objects.equals(m.getLevel(), 4)) {
                        area.setStreetId(m.getCode())
                                .setStreetName(m.getName());
                    }
                }
            }
        }
        return area;
    }

    /**
     * apiServices.getAreaList
     * 获取所有行政区域信息数据，本地缓存 23 * 3 小时，远程缓存 23 * 6 小时
     *
     * @param isreal Integer
     * @return List<AreaListRes>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-08
     */
    @Cached(name = CacheKey.JetCache.AREA_LIST_IS_REAL, localExpire = 1, expire = 1, timeUnit = TimeUnit.HOURS, cacheType = CacheType.LOCAL)
    public List<AreaListRes> getAreaList(Integer isreal) {
        isreal = Optional.ofNullable(isreal).orElse(1);
        List<AreaListRes> areaListResList = wuLiuService.getAreaList();
        if (CollectionUtils.isNotEmpty(areaListResList)) {
            Stream<AreaListRes> stream = areaListResList.stream();
            if (isreal == 1) {
                return stream.filter(item -> !Boolean.TRUE.equals(item.getIsReal())).collect(Collectors.toList());
            } else {
                return stream.collect(Collectors.toList());
            }
        }
        return Collections.emptyList();
    }

    /**
     * 从远程拉取行政区域信息数据，本地缓存 23 * 3 小时，远程缓存 23 * 7 小时
     *
     * @return List<AreaListRes>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-12
     */
    @Override
    @Cached(name = CacheKey.JetCache.AREA_LIST, localExpire = 1, expire = 1, timeUnit = TimeUnit.HOURS, cacheType = CacheType.LOCAL)
    public List<AreaListRes> getAreaList() {
        List<AreaListRes> data;
        try {
            data = areaListClient.listAll().getData();
        } catch (HttpMessageNotReadableException e) {
            log.error("从远程拉取行政区域信息数据报错: {}", Exceptions.getStackTraceAsString(e), e);
            return Collections.emptyList();
        }
        return Optional.ofNullable(data).orElseGet(ArrayList::new);
    }

    /**
     * apiServices.getWuliuStatsArea
     * 顺丰、中通物流接口，收货人添加区域门店代码
     *
     * @param wuType
     * @param sAreaId
     * @param type
     * @return String
     * @date 2021-10-13
     * <AUTHOR> [<EMAIL>]
     */
    @Override
    public String getWuliuStatsArea(Integer wuType, Integer sAreaId, Integer type) {
        type = Optional.ofNullable(type).orElse(0);
        String area = "";
        Integer areaId = 0;
        // 寄件方有地区, 11拍靓机
        List<Integer> wuTypeList = Arrays.asList(1, 11, 4, 5, 6, 9, 10);
        if (Objects.equals(type, 0)) {
            if (wuTypeList.contains(wuType)) {
                areaId = sAreaId;
            }
        } else {
            // 收件方有地区
            wuTypeList = Arrays.asList(1, 9, 10);
            if (wuTypeList.contains(wuType)) {
                areaId = sAreaId;
            }
        }
        if (Optional.ofNullable(areaId).orElse(0) > 0) {
            area = Optional.ofNullable(areainfoService.getCustomAreaNameById(sAreaId)).orElse("");
        }
        return area;
    }

    /**
     * shunfengApiServices.getYueJieKahao
     * 根据地区 ID 获取月结卡号
     *
     * @param sAreaId
     * @param expressType
     * @return ShunFengCardVO
     * @date 2021-10-13
     * <AUTHOR> [<EMAIL>]
     */
    @Override
    public ShunFengCardVO getYueJieKaHao(Integer sAreaId, String expressType) {
        String sfAccountType = "";
        expressType = Optional.ofNullable(expressType).orElse("");
        if (StringUtils.isNotBlank(expressType) && expressType.contains("_")) {
            String[] expressTypeArr = expressType.split("_");
            expressType = expressTypeArr[0];
            if (expressTypeArr.length > 1) {
                sfAccountType = expressTypeArr[1];
            }
        }
        Areainfo areaInfo = Optional.ofNullable(areainfoService.getAreaInfoByAreaId2(sAreaId)).orElseGet(Areainfo::new);
        if (!Objects.equals(sAreaId, 14)) {
            // 月结卡
            String custId = "";
            // 顾客编号
            String clientCode = "JJDZC_GYY";
            // 校验码
            String checkWord = "FKtViN4HX534bQemLCLRnCzqJF7271Y2";

            if (Arrays.asList("154", "155").contains(expressType) && StringUtils.isBlank(sfAccountType)) { // 重货包裹（采购专用）,小票零担

                custId = "**********";
                // 顾客编号
                clientCode = "**********";
                // 校验码
                checkWord = "vrBsLe9JDNBGNUJwteyHkvjftTC3YVgT";
            } else if ("208".equals(expressType)) { // 特惠专配（低价专用）
                custId = "**********";
                // 顾客编号
                clientCode = "LSKJE_GYY";
                // 校验码
                checkWord = "n1fU5cQLnThaJXUJMT6ft2oNGvUZqCxN";
            } else if ((Objects.equals(expressType, "1") || Objects.equals(sfAccountType, "2322"))
                    && Objects.equals(areaInfo.getPid(), 53)) {
                custId = "**********";
                // 顾客编号
                clientCode = "LSKJE_GYY";
                // 校验码
                checkWord = "n1fU5cQLnThaJXUJMT6ft2oNGvUZqCxN";
            } else if (Objects.equals(expressType, "2") && Objects.equals(sfAccountType, "2312")) {
                //省内（0-20公斤）+省外全部 去除云南地区限制
                custId = "**********";
            } else if ((isCurAreaHqDcH1D1(sAreaId) || Objects.equals(sAreaId, WuLiuConstant.AREA_GCB)) && Objects.equals(areaInfo.getPid(), 53)) {
                //云南总部大仓使用**********
                custId = "**********";
            } else if (Arrays.asList(WuLiuConstant.AREA_CH_D1, WuLiuConstant.AREA_H3, WuLiuConstant.AREA_FX_D1).contains(sAreaId)) {
                //2022-05-13王加礼要求发货门店CH_d1，h3，FX_d1使用**********
                custId = "**********";
            } else {
                if (Objects.equals(areaInfo.getPid(), 53)) {
                    // 云南
                    //顺丰**********账号替换为**********账号 https://jiuji.yuque.com/staff-fwzd6v/cvecc8/kayulplry160x9yp?singleDoc#
                    custId = "**********";
                } else if (Objects.equals(areaInfo.getPid(), 52)) {
                    //贵州顺丰下单逻辑调整 https://jiuji.yuque.com/docs/share/967887f3-db1c-44eb-b21a-3191d0907885?#
                    custId = "**********";
                    // 贵州 dc1,h2,D
                    /*if (Objects.equals(sAreaId, 113) || Objects.equals(sAreaId, 246)
                            || Objects.equals(sAreaId, 96)) {
                        custId = "8511379103";
                    } else {
                        custId = "8511379124";
                    }*/
                } else if (Objects.equals(areaInfo.getPid(), 43)) {
                    //湖南
                    custId = "7312009130";
                }
            }
            if (StringUtils.isBlank(custId)) {
                throw new CustomizeException("地区错误,无法匹配月结卡,请先检查地区是否配置了月结卡号");
            }
            return new ShunFengCardVO().setCustId(custId).setClientCode(clientCode).setCheckPwd(checkWord);
        } else {

            // 顾客编号
            String code = WuLiuConstant.SHUNFENG_CUSTOMER_CODE;
            // 校验码
            String verifyCode = WuLiuConstant.SHUNFENG_CUSTOMER_VERIFY_CODE;
            // 月结卡号
            String custId = WuLiuConstant.SHUNFENG_CUSTOMER_ID;

            if (AreaInfoUtils.isCurrentDc(sAreaId) || AreaInfoUtils.isCurrentH1(sAreaId)) {
                // dc/h1
                custId = "**********";
            }
            if (Objects.equals(sAreaId, 113)) {
                // https://jiuji.yuque.com/docs/share/8dd1b479-6f8f-4dfd-af24-4ebc1a0d08d8
                // 原贵阳顺丰月结账号相关功能下线功能迭代
                // dc1
                // custId = "8511375973";
            }
            if (Objects.equals(sAreaId, 142)) {
                // dc2
                custId = "0284916579";
            }
            if (Arrays.asList(63, 120, 189, 68).contains(sAreaId)) {
                // cq
                custId = "0233797677";
            }
            if (Objects.equals(sAreaId, 50)) {
                // an
                custId = "8711465069";
            }
            if (Objects.equals(sAreaId, 17)) {
                // lj
                custId = "0880064486";
            }
            if (Objects.equals(sAreaId, 61)) {
                // bj
                custId = "8570040645";
            }
            if (Objects.equals(sAreaId, 40)) {
                // lp
                custId = "8580052719";
            }
            if (Objects.equals(sAreaId, 60)) {
                // as
                custId = "0530036164";
            }
            if (Objects.equals(sAreaId, 59)) {
                // zy
                custId = "0520147943";
            }
            if (Objects.equals(sAreaId, 139)) {
                // ls
                custId = "8339166211";
            }
            if (Objects.equals(sAreaId, 14)) {
                custId = "7550047585";
            }
            return new ShunFengCardVO().setCustId(custId).setClientCode(code).setCheckPwd(verifyCode);
        }
    }

    /**
     * apiServices.GetAreaSubject
     * 获取门店主体信息
     *
     * @param id Integer
     * @return AreaSubjectVO
     * @date 2021-10-13
     * <AUTHOR> [<EMAIL>]
     */
    @Override
    public AreaSubjectVO getAreaSubject(Integer id) {
        String webName = "九机网";
        AreaSubjectVO one = new AreaSubjectVO();
        Areainfo areaObj = areainfoService.getAreaInfoByAreaId2(id);
        if (areaObj != null) {
            one.setId(areaObj.getId());
            one.setIsSend(areaObj.getIsSend());
            one.setPrintName(areaObj.getPrintName());
            one.setLogo(Optional.ofNullable(areaObj.getLogo()).orElse("https://moa.9ji.com/static/24,37835b86a5e75b"));
            one.setIs9JiSubject(webName.equals(areaObj.getPrintName()));
            one.setAuthorizeId(areaObj.getAuthorizeid());
            one.setAreakind(areaObj.getKind1());
            one.setXtenant(areaObj.getXtenant());
            // 短信渠道枚举
            one.setSmsChannel(String.valueOf(getSmsChannelByXtenant(SysUtils.getIntXtenantId(), 1)));
            one.setSmsChannelMarketing(String.valueOf(getSmsChannelByXtenant(SysUtils.getIntXtenantId(), 2)));
            one.setMUrl(SysConfigUtils.getValue(SysConfigConstant.M_URL));
            one.setWebUrl(SysConfigUtils.getValue(SysConfigConstant.WEB_URL));
            one.setHsUrl(SysConfigUtils.getValue(SysConfigConstant.HS_URL));
            one.setArea(areaObj.getArea());
            one.setCityId(areaObj.getCityid());
        } else {
            one.setId(id);
            one.setIsSend(true);
            one.setPrintName("九机网");
            one.setIs9JiSubject(true);
            one.setAuthorizeId(NumUtil.ONE);
            one.setLogo("https://moa.9ji.com/static/24,37835b86a5e75b");
            one.setXtenant(NumUtil.ZERO);
            // 短信渠道枚举
            one.setSmsChannel(EsmsChannelEnum.JIUJI.getCode().toString());
            one.setSmsChannelMarketing(EsmsChannelEnum.JIUJI_YX.getCode().toString());
            one.setMUrl(SysConfigUtils.getMoaUrl());
            one.setWebUrl(SysConfigUtils.getValue(SysConfigConstant.WEB_URL));
            one.setHsUrl(SysConfigUtils.getValue(SysConfigConstant.HS_URL));
            one.setArea("");
            one.setCityId(NumUtil.ZERO);
        }
        if ("华为授权".equals(one.getPrintName())) {
            one.setSmsChannel("27");
        }
        return one;
    }

    /**
     * apiServices.getAreaSubjectBySubId
     * 根据订单编号获取主体
     *
     * @param subId   Integer
     * @param payType Integer
     * @return AreaSubjectVO
     * @date 2021-10-13
     * <AUTHOR> [<EMAIL>]
     */
    public AreaSubjectVO getAreaSubjectBySubId(Integer subId, Integer payType) {
        List<Integer> payTypeList = Stream.of(1, 2, 3, 4, 5, 6, 10).collect(Collectors.toList());
        int areaid = 0;
        if (subId > 0 && payTypeList.contains(payType)) {
            areaid = this.baseMapper.getSubAreaidBySubAndPaytype(subId, payType);
        }
        AreaSubjectVO areaSubject = getAreaSubject(areaid);
        //yhw 支付走丫丫平台
        if (areaSubject.getId() == 654) {
            areaSubject.setXtenant(1);
        }
        return areaSubject;
    }

    /**
     * subWLService.getWuliuInfoById
     * 根据物流 ID 获取物流信息
     *
     * @param subId
     * @param wuliuid
     * @return WuLiu2Entity
     * @date 2021-10-13
     * <AUTHOR> [<EMAIL>]
     */
    public WuLiu2Entity getWuLiuInfoById(Integer subId, Integer wuliuid) {
        wuliuid = Optional.ofNullable(wuliuid).orElse(0);

        if ((subId == null || subId == 0) && wuliuid == 0) {
            // 这里一定要判断，否则会阻塞系统，因为满足条件的记录太多
            return null;
        }
        WuLiu2Entity entity;
        // 优先判断物流单，物流单更准确，绑定单可能会重复
        if (wuliuid != 0) {
            entity = baseMapper.getWuLiuInfoById(wuliuid);
        } else {
            entity = baseMapper.getWuLiuInfoBySubId(subId);
        }
        Optional.ofNullable(entity).ifPresent(wuLiu2 -> {
            if (wuLiu2.getWuType() == 1) {
                Areainfo item2 = areainfoService.getAreaInfoByAreaId2(wuLiu2.getRAreaId());
                wuLiu2.setRAddress(item2.getCompanyAddress());
                wuLiu2.setRCityId(item2.getCityid());
            }

            WuLiuSubEntity sub = wuLiuSubService.lambdaQuery()
                    .select(WuLiuSubEntity.class, item2 -> Arrays.asList("delivery", "zitidianID").contains(item2.getColumn()))
                    .eq(WuLiuSubEntity::getSubId, wuLiu2.getDanHaoBind())
                    .one();

            Optional.ofNullable(sub).ifPresent(item2 -> {
                if (item2.getDelivery() == 3 && item2.getZiTiDianId() > 0) {
                    Optional.ofNullable(wuLiuZiTiDianService.lambdaQuery().
                            select(WuLiuZiTiDianEntity.class, item -> Arrays.asList("Contact", "tel1", "cityid", "address", "tel2").contains(item.getColumn()))
                            .eq(WuLiuZiTiDianEntity::getId, sub.getZiTiDianId())
                            .one())
                            .ifPresent(ziTiDian ->
                                    // 收件人
                                    entity.setRName(ziTiDian.getContact())
                                            // 收件联系电话
                                            .setRMobile(ziTiDian.getTel1())
                                            // 收件人城市 ID
                                            .setRCityId(ziTiDian.getCityId())
                                            // 收件人联系地址
                                            .setRAddress(ziTiDian.getAddress()));
                }
            });
        });
        return entity;
    }

    /**
     * subWLService.saveWuLiu
     * 保存物流单(actionName="save")
     *
     * @param model
     * @param sessionArea
     * @param userName
     * @return Integer 返回wuliuID
     * @date 2021-10-21
     * <AUTHOR> [<EMAIL>]
     */
    @Override
    @DSTransactional
    public Integer newTransSaveWuLiu(WuLiuAddOrUpdateReqVO model, Integer sessionArea, String userName) {
        String xid = TransactionContext.getXID();
        boolean xState = true;
        String newXid = UUID.randomUUID().toString();
        TransactionContext.bind(newXid);
        try {

            model.setSCityId(model.getSDid());
            model.setRCityId(model.getRDid());

            Integer wutype = model.getWuType();

            if (Objects.equals(wutype, 0) && model.getWuliuid() > 0) {
                wutype = Optional.ofNullable(Optional.ofNullable(lambdaQuery().select(WuLiuEntity::getWuType).eq(WuLiuEntity::getId, model.getWuliuid()).one()).orElseGet(WuLiuEntity::new).getWuType()).orElse(0);
            }

            int stats = 0;
            if (StringUtils.isNotBlank(model.getNu())) {
                stats = Optional.ofNullable(Optional.ofNullable(lambdaQuery().select(WuLiuEntity::getStats).eq(WuLiuEntity::getId, model.getWuliuid()).one()).orElseGet(WuLiuEntity::new).getStats()).orElse(0);
            }

            //处理cityid，用于计算价格
            if (Arrays.asList(WuLiuConstant.SHUNFENG, WuLiuConstant.ZHONGTONG).contains(model.getCom())) {
                if (Objects.equals(model.getWuType(), 1)) {
                    model.setSCityId(Optional.ofNullable(areainfoService.getAreaInfoByAreaId2(model.getSareaid())).orElseGet(Areainfo::new).getDid());
                    model.setRCityId(Optional.ofNullable(areainfoService.getAreaInfoByAreaId2(model.getRareaid())).orElseGet(Areainfo::new).getDid());
                } else if (Arrays.asList(4, 5, 6).contains(model.getWuType())) {
                    model.setSCityId(Optional.ofNullable(areainfoService.getAreaInfoByAreaId2(model.getSareaid())).orElseGet(Areainfo::new).getDid());
                } else if (Objects.equals(model.getWuType(), 7)) {
                    model.setRareaid(Optional.ofNullable(areainfoService.getAreaInfoByAreaId2(model.getRareaid())).orElseGet(Areainfo::new).getDid());
                }
            }

            //计算顺丰 中通 快递运费
            if (Arrays.asList(WuLiuConstant.SHUNFENG, WuLiuConstant.ZHONGTONG).contains(Optional.ofNullable(model.getCom()).orElse("")) && Optional.ofNullable(model.getWeight()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) > 0 && Optional.ofNullable(model.getSCityId()).orElse(0) > 0 && Optional.ofNullable(model.getRCityId()).orElse(0) > 0) {
                BigDecimal price = calculatePrice(model.getSCityId(), model.getRCityId(), model.getWeight(), model.getCom());
                if (price.compareTo(BigDecimal.ZERO) > 0) {
                    model.setPrice(price);
                }
            }

            if (model.getPrice() == null) {
                model.setPrice(BigDecimal.ZERO);
            }
            if (model.getInPrice() == null) {
                model.setInPrice(model.getPrice());
            }

            //update
            if (!Objects.equals(Optional.ofNullable(model.getWuliuid()).orElse(0), 0)) {
                WuLiuEntity dt = this.getById(model.getWuliuid());

                boolean updateIsSuccess = lambdaUpdate()
                        .set(!Objects.equals(model.getSCityId(), 0), WuLiuEntity::getSCityId, model.getSCityId())
                        .set(!Objects.equals(model.getRCityId(), 0), WuLiuEntity::getRCityId, model.getRCityId())
                        .set((Objects.equals(model.getWuType(), 1) && "meituan".equals(model.getCom()) && model.getDanHaoBind() > 0), WuLiuEntity::getDanHaoBind, model.getDanHaoBind())
                        .set(WuLiuEntity::getSName, model.getSName())
                        .set(WuLiuEntity::getSMobile, model.getSMobile())
                        .set(WuLiuEntity::getSAddress, model.getSAddress())
                        .set(WuLiuEntity::getSAreaId, model.getSareaid())
                        .set(WuLiuEntity::getRName, model.getRName())
                        .set(WuLiuEntity::getRMobile, model.getRMobile())
                        .set(WuLiuEntity::getRAddress, model.getRAddress())
                        .set(WuLiuEntity::getRAreaId, model.getRareaid())
                        .set(WuLiuEntity::getInPrice, model.getInPrice())
                        .set(WuLiuEntity::getPrice, model.getPrice())
                        .set(WuLiuEntity::getWeight, model.getWeight())
                        //.set(WuLiuEntity::getShouJianRen, model.getShouJianRen())
                        .set(WuLiuEntity::getPaiJianRen, model.getPaiJianRen())
                        .set(WuLiuEntity::getComment, model.getComment())
                        .set(WuLiuEntity::getCom, model.getCom())
                        .set(StringUtils.isNotBlank(model.getNu()),WuLiuEntity::getNu, model.getNu())
                        .set(WuLiuEntity::getSubKinds, model.getSubKinds())
                        .set(WuLiuEntity::getWCateId, model.getWCateId())
                        .eq(WuLiuEntity::getId, model.getWuliuid())
                        .update();

                if (updateIsSuccess) {
                    if (!Objects.equals(stats, 0)) {
                        // 更新状态
                        lambdaUpdate().set(WuLiuEntity::getStats, model.getStats()).eq(WuLiuEntity::getId, model.getWuliuid()).update();
                    }

                    if (CollectionUtils.isNotEmpty(model.getLogs())) {
                        model.getLogs().forEach(item -> writewuliulogs(model.getWuliuid(), item.getInuser(), item.getMsg(), null));
                    }

                    if (dt != null) {
                        getwuliulog(model, userName, dt);

                        String com = dt.getCom();
                        String nu = dt.getNu();
                        if (WuLiuConstant.SHUNFENG.equals(com) && !Objects.equals(com, model.getCom())
                                || (WuLiuConstant.SHUNFENG.equals(com) && !Objects.equals(nu, model.getNu()))) {
                            if (Boolean.TRUE.equals(cancelSfOrder(nu, null))) {
                                writewuliulogs(model.getWuliuid(), userName, "取消顺丰运单" + nu, null);
                            }
                        } else if (WuLiuConstant.DADA.equals(com) && !Objects.equals(com, model.getCom()) && StringUtils.isNotBlank(nu)) {
                            writewuliulogs(model.getWuliuid(), userName, "取消达达订单" + nu, null);
                            dadaCancelOrder(nu);
                        }

                        Integer payMethod = dt.getPayMethod();

                        if (StringUtils.isBlank(model.getNu()) && WuLiuConstant.SHUNFENG.equals(model.getCom()) && model.getPayMethod() != null && (payMethod == 0 || !Objects.equals(nu, model.getNu()))) {
                            lambdaUpdate().set(WuLiuEntity::getPayMethod, model.getPayMethod()).eq(WuLiuEntity::getId, model.getWuliuid()).update();
                        }
                    }


                    // 在订单中备注物流信息
                    // 订单派送的物流类型
                    if (dt != null && model.getDanHaoBind() > 0 && StringUtils.isNotBlank(model.getCom()) && WuLiuConstant.SUB_WULIU_TYPE_ARRY.contains(model.getWuType())) {
                        String com = dt.getCom();
                        String nu = dt.getNu();
                        if (!Objects.equals(com, model.getCom()) || !Objects.equals(nu, model.getNu())) {
                            String wuliuCom = getWuliuCompanyName(model.getCom());
                            String oldWuliuCom = getWuliuCompanyName(com);
                            String wuliuNo = StringUtils.isBlank(model.getNu()) ? "空" : model.getNu();
                            String subComment = String.format("物流信息：%s，物流单号：%s", wuliuCom, wuliuNo);
                            if (StringUtils.isNotBlank(com) || StringUtils.isNotBlank(nu)) {
                                String oldWuliuNo = StringUtils.isBlank(nu) ? "空" : nu;
                                subComment = String.format("物流信息由 %s 单号:%s 改为 %s 单号:%s", oldWuliuCom, oldWuliuNo, wuliuCom, wuliuNo);
                            }
                            opLog(new SubLogVO().setComment(subComment).setSubId(model.getDanHaoBind()).setShowType(false).setType(1).setInUser(userName));

                            if (!Objects.equals(wuliuCom, "") && !StringUtils.isNotBlank(model.getNu())) {
                                wuLiuSubAddressService.lambdaUpdate().set(WuLiuSubAddressEntity::getWuLiuCompany, wuliuCom).set(WuLiuSubAddressEntity::getWuLiuNo, model.getNu()).eq(WuLiuSubAddressEntity::getSubId, model.getDanHaoBind()).update();
                            }
                        }
                    }

                    //添加附件
                    // eAttachmentsType 物流派送图片 = 13,
                    OaAttachmentsAddOrUpdateReqVO files = new OaAttachmentsAddOrUpdateReqVO();
                    files.setFileList(model.getFiles()).setType(13).setLinkId(model.getWuliuid());
                    attachmentsService.addOrUpdate(files);
                    //计算物流单距离
                    WuliuUtil.sendWuliuExpressMessage(new WuliuExpressMqBO<WuliuDistributtionCostBO>().setAct(WuliuExpressConstant.ACT_CALCULATE_DISTRIBUTION_COST).
                            setData(LambdaBuild.create(new WuliuDistributtionCostBO())
                                    .set(WuliuDistributtionCostBO::setSendPosition, model.getSendPosition())
                                    .set(WuliuDistributtionCostBO::setReceivePosition, model.getReceivePosition())
                                    .set(WuliuDistributtionCostBO::setSdetailedAddress, model.getSdetailedAddress())
                                    .set(WuliuDistributtionCostBO::setRdetailedAddress, model.getRdetailedAddress())
                                    .set(WuliuDistributtionCostBO::setWuliuId, model.getWuliuid()).build()));
                    return model.getWuliuid();
                } else {
                    return -1;
                }
            } else {
                // insert

                WuLiuEntity entity = new WuLiuEntity();
                if (!Objects.equals(model.getSCityId(), 0)) {
                    entity.setSCityId(model.getSCityId());
                }
                if (!Objects.equals(model.getRCityId(), 0)) {
                    entity.setRCityId(model.getRCityId());
                }
                if (StringUtils.isNotBlank(model.getLinkType())) {
                    entity.setLinkType(Integer.valueOf(model.getLinkType()));
                }

                entity.setDTime(LocalDateTime.now());
                entity.setAreaId(sessionArea);
                entity.setDanHaoBind(model.getDanHaoBind());
                entity.setWuType(model.getWuType());
                entity.setStats(stats == 0 ? 1 : (stats == 1 || stats == 2 ? 3 : 0));
                entity.setInUser(userName);
                entity.setSName(model.getSName());
                entity.setSMobile(model.getSMobile());
                entity.setSAddress(model.getSAddress());
                entity.setSAreaId(model.getSareaid());
                entity.setRName(model.getRName());
                entity.setRMobile(model.getRMobile());
                entity.setRAddress(model.getRAddress());
                entity.setRAreaId(model.getRareaid());
                entity.setInPrice(model.getInPrice());
                entity.setPrice(model.getPrice());
                entity.setWeight(model.getWeight());
                //entity.setShouJianRen(model.getShouJianRen());
                entity.setPaiJianRen(model.getPaiJianRen());
                // 内容长度 1500
                entity.setComment(Optional.ofNullable(model.getComment()).orElse(""));
                entity.setCom(model.getCom());
                entity.setNu(model.getNu());
                entity.setPayMethod(model.getPayMethod());
                entity.setNotifyType(model.getNotifyType());
                entity.setSubKinds(model.getSubKinds());
                entity.setWCateId(model.getWCateId());
                entity.setIsCreateManually(model.getIsCreateManually());
                //物流单来源
                entity.setSource(model.getSource());

                // 新增物流单
                boolean addIsSuccess = save(entity);
                if (addIsSuccess) {
                    wuLiuLogService.addOne(entity.getId(), userName, WuLiuConstant.CREATE_WULIU_MSG);
                    model.setWuliuid(entity.getId());

                    if (CollectionUtils.isNotEmpty(model.getLogs())) {
                        model.getLogs().forEach(item -> writewuliulogs(model.getWuliuid(), item.getInuser(), item.getMsg(), null));
                    }


                    //添加附件
                    // eAttachmentsType 物流派送图片 = 13,
                    OaAttachmentsAddOrUpdateReqVO files = new OaAttachmentsAddOrUpdateReqVO();
                    files.setFileList(model.getFiles()).setType(13).setLinkId(model.getWuliuid());
                    attachmentsService.addOrUpdate(files);

                    model.setSareaid(Optional.ofNullable(model.getSareaid()).orElse(NumberConstant.ZERO));
                    model.setRareaid(Optional.ofNullable(model.getRareaid()).orElse(NumberConstant.ZERO));
                    if (model.getSareaid() > 0 && model.getRareaid() > 0) {
                        // 内部物流时效计算
                        setWuliuEstimatedArrivalTime(model.getSareaid(), model.getRareaid(), model.getWuliuid());
                    }

                    createWuliuSubCheck(model, userName);
                    //维修单创建，物流时效计算
                    if (WuLiuTypeEnum.AFTER_SERVICE.getCode().equals(entity.getWuType())) {
                        WuliuProcessBO wuliuProcess = LambdaBuild.create(WuliuProcessBO.class)
                                .set(WuliuProcessBO::setBusinessId, Convert.toStr(entity.getDanHaoBind()))
                                .set(WuliuProcessBO::setBusinessNode, SubDynamicsBusinessNodeEnum.REPAIR_ORDER_CREATE.getCode())
                                .set(WuliuProcessBO::setBusinessType, ProcessBusinessTypeEnum.REPAIR_DELIVERY.getCode())
                                .set(WuliuProcessBO::setOperationTime, LocalDateTime.now())
                                .set(WuliuProcessBO::setXtentant, XtenantEnum.getXtenant().longValue())
                                .set(WuliuProcessBO::setEmployeeName, userName)
                                .set(WuliuProcessBO::setSource, "saveWuLiu")
                                .set(WuliuProcessBO::setStatus, 0)
                                .set(WuliuProcessBO::setDelayMillisecond, 0)
                                .set(WuliuProcessBO::setAreaId, 0)
                                .set(WuliuProcessBO::setNextAreaId, 0)
                                .set(WuliuProcessBO::setRelatedId, entity.getId()).build();
                        WuliuUtil.sendWuliuProcessMessage(wuliuProcess);
                    }
                    model.setIsSuccessInsertWuLiu(Boolean.TRUE);
                    return model.getWuliuid();
                } else {
                    throw new CustomizeException("保存失败");
                }
            }

        } catch (Exception e) {
            xState = false;
            AtomicReference<String> noticeMsg = new AtomicReference<>();
            RRExceptionHandler.logError(StrUtil.format("物流单[{}]新增或更新", model.getWuliuid()), Dict.create()
                    .set("model", model).set("sessionArea", sessionArea).set("userName", userName), e,noticeMsg::set);
            SpringUtil.getBean(ISmsService.class).sendOaMsgTo9JiMan(noticeMsg.get());
            throw new CustomizeException(noticeMsg.get());
        }finally {
            com.baomidou.dynamic.datasource.tx.ConnectionFactory.notify(xState);
            TransactionContext.remove();
            //恢复之前的事务id
            TransactionContext.bind(xid);
        }

    }

    /**
     * 物流单日志
     * @param model
     * @param userName
     * @param dt
     * @throws Exception
     */
    private void getwuliulog(WuLiuAddOrUpdateReqVO model, String userName, WuLiuEntity dt) throws Exception {
        if (!StringUtils.isNumeric(model.getLinkType())) {
            model.setLinkType(null);
        }
        WuLiuEntity newWuliu = WuLiuMapStruct.INSTANCE.addReqVoToEntity(model);
        newWuliu.setSArea(Optional.ofNullable(areainfoService.getAreaInfoByAreaId2(model.getSareaid())).orElseGet(Areainfo::new).getArea());
        dt.setSArea(Optional.ofNullable(areainfoService.getAreaInfoByAreaId2(dt.getSAreaId())).orElseGet(Areainfo::new).getArea());
        newWuliu.setRArea(Optional.ofNullable(areainfoService.getAreaInfoByAreaId2(model.getRareaid())).orElseGet(Areainfo::new).getArea());
        dt.setRArea(Optional.ofNullable(areainfoService.getAreaInfoByAreaId2(dt.getRAreaId())).orElseGet(Areainfo::new).getArea());
        String msg = FieldCompareUtils.compare(dt, newWuliu).getDifference();
        if (!Objects.equals(dt.getCom(), model.getCom())) {
            String wuliuCom = Optional.ofNullable(expressEnumService.getWuliuCompanyName(model.getCom())).orElse("");
            String oldWuliuCom = Optional.ofNullable(expressEnumService.getWuliuCompanyName(dt.getCom())).orElse("");
            msg += "快递公司由【"+oldWuliuCom+"】修改为【"+wuliuCom+"】";
        }

        if ((Arrays.asList(WuLiuTypeEnum.VISIT.getCode(),WuLiuTypeEnum.OTHERS.getCode(),WuLiuTypeEnum.FOURTEEN_DAY.getCode(),WuLiuTypeEnum.ACCESSORY.getCode()).contains(model.getWuType()))
                && !Objects.equals(Optional.ofNullable(model.getRCityId()).orElse(0), 0)
                && !Objects.equals(dt.getRCityId(), model.getRCityId())) {
            CityIdListDTO oldCityId = getAreaIdByCityId(dt.getRCityId(), 1);
            CityIdListDTO newCityId = getAreaIdByCityId(model.getRCityId(), 1);
            msg += "寄件省市区由【"+oldCityId.getPname()+oldCityId.getDname()+oldCityId.getZname()+"】修改为【"+newCityId.getPname()+newCityId.getDname()+newCityId.getZname()+"】";
        }

        if (!Objects.equals(WuLiuTypeEnum.INNER.getCode(), model.getWuType()) && !Objects.equals(Optional.ofNullable(model.getRCityId()).orElse(0), 0) && !Objects.equals(dt.getRCityId(), model.getRCityId())) {
            CityIdListDTO oldCityId = getAreaIdByCityId(dt.getRCityId(), 1);
            CityIdListDTO newCityId = getAreaIdByCityId(model.getRCityId(), 1);
            msg += "收件省市区由【"+oldCityId.getPname()+oldCityId.getDname()+oldCityId.getZname()+"】修改为【"+newCityId.getPname()+newCityId.getDname()+newCityId.getZname()+"】";
        }

        if(!Objects.equals(dt.getWuType(), model.getWuType())) {
            String oldWuType = WuLiuTypeEnum.getMessage(dt.getWuType());
            String newWuType = WuLiuTypeEnum.getMessage(model.getWuType());
            msg += "类别由【"+oldWuType+"】修改为【"+newWuType+"】";
        }

        if(!Objects.equals(dt.getWCateId(), model.getWCateId())) {
            List<WuLiuCategoryResVO> wuLiuCategory = wuLiuCategoryService.queryWuLiuCategoryList();
            String oldCategory = wuLiuCategory.stream().filter(v -> v.getCateId().equals(dt.getWCateId())).findFirst().orElse(new WuLiuCategoryResVO()).getCateName();
            String newCategory = wuLiuCategory.stream().filter(v -> v.getCateId().equals(model.getWCateId())).findFirst().orElse(new WuLiuCategoryResVO()).getCateName();
            msg += "分类由【"+oldCategory+"】修改为【"+newCategory+"】";
        }

        String oldExpressType = "";
        String newExpressType = "";
        if (WuLiuConstant.SHUNFENG.equals(model.getCom())) {
            oldExpressType = wuLiuWuliuwangdianService.getExpressType(dt.getId());
            newExpressType = getExepressType(model.getExpressType());
        } else if (WuLiuConstant.JINGDONG_JIUJI.equals(model.getCom())) {
            oldExpressType = wuLiuExpressExtendService.getJdExpressType(dt.getId());
            newExpressType = JiujiJdxpressTypeEnum.getMessage(model.getExpressType());
        }
        if (!oldExpressType.equals(newExpressType)) {
            msg += "快递类型由【"+oldExpressType+"】修改为【"+newExpressType+"】";
        }
        if (StringUtils.isNotBlank(msg)) {
            writewuliulogs(model.getWuliuid(), userName, msg, null);
        }
    }

    /**
     * 自动创建物流单，非手动创建物流单调用此接口
     *
     * @param vo WuLiuAddOrUpdateReqV2VO
     * @param sessionAreaId Integer
     * @param username String
     * @return R<Integer>
     * <AUTHOR> [<EMAIL>]
     * @date 2022-01-12
     */
    @Override
    public R<Integer> saveWuliuV1(WuLiuAddOrUpdateReqV2VO vo, Integer sessionAreaId, String username) {
        WuLiuAddOrUpdateReqVO wuLiuAddOrUpdateReqVO = WuLiuMapStruct.INSTANCE.toWuLiuAddOrUpdateReqVo(vo);
        Integer wuliuId = wuLiuService.newTransSaveWuLiu(wuLiuAddOrUpdateReqVO, sessionAreaId, username);
        log.warn("wuliuId={},入参1={},入参2={}", wuliuId, JacksonJsonUtils.toJson(vo), JacksonJsonUtils.toJson(wuLiuAddOrUpdateReqVO));
        return wuliuId != null ? R.success("物流单创建成功", wuliuId) : R.error("物流单创建失败");
    }

    /**
     * subWLService.SetWuliuEstimatedArrivalTime
     * 设置物流单预计到达时间
     *
     * @param fromAreaId Integer
     * @param toAreaId   Integer
     * @param wuliuId    Integer
     * <AUTHOR> [<EMAIL>]
     * @date 2021-10-29
     */
    private void setWuliuEstimatedArrivalTime(Integer fromAreaId, Integer toAreaId, Integer wuliuId) {
        String result = "";
        try {
            String url = SysConfigUtils.getValue(SysConfigConstant.WEB_URL) + "/api/3_0/AreaHandler.ashx?act=PlanTime&from=" + fromAreaId + "&to=" +
                    toAreaId + "&t=" + DateTimeFormatter.ofPattern("yyyyMMddHHmmss").format(LocalDateTime.now());
            result = HttpUtil.get(url);
            if (!result.startsWith("error")) {
                R r = JSONUtil.toBean(result, R.class);
                if (r.getCode() == 0) {
                    if (r.getData() != null) {
                        String arrivalTime = r.getData().toString();
                        String regex = "(?<Arrival>\\d{4}(\\-\\d{1,2}){2}\\s+\\d{1,2}(\\:\\d{1,2}){1,2})";
                        boolean matches = Pattern.matches(regex, arrivalTime);
                        if (matches) {
                            Pattern compile = Pattern.compile(regex);
                            Matcher matcher = compile.matcher(regex);
                            String arrival = matcher.group("Arrival");
                            LocalDateTime localDateTime = LocalDateTime.parse(arrival);
                            this.lambdaUpdate()
                                    .eq(WuLiuEntity::getId, wuliuId)
                                    .set(WuLiuEntity::getEstimatedArrivalTime, arrival)
                                    .update();
                        }
                    }
                } else {
                    log.warn("获取物流单{}（{}-{}-{}）预计时间遇到异常； {} ", wuliuId, fromAreaId, toAreaId, result, result);
                }
            } else {
                log.warn("获取物流单{}（{}-{}-{}）预计时间遇到异常；", wuliuId, fromAreaId, toAreaId, result);
            }
        } catch (Exception e) {
            log.error("获取物流单{}（{}-{}-{}）预计时间遇到异常；{}", wuliuId, fromAreaId, toAreaId, result, e);
        }
    }

    /**
     * dadaWuliuService.CancelOrder
     * 达达取消订单
     *
     * @param orderid
     * <AUTHOR> [<EMAIL>]
     * @date 2021-10-29
     */
    private boolean dadaCancelOrder(String orderid) {
        try {
            DadaCancelReqVo vo = new DadaCancelReqVo();
            vo.setOrderId(orderid);
            vo.setCancelReasonId(10000);
            vo.setCancelReason("取消订单");

            dadaIsOnline = SysUtils.isJiuJiProd();

            //调用达达取消订单接口
            String paramsJson = JsonParseUtil.toJson(vo);
            DadaRequestClient dadaClient = new DadaRequestClient(DadaUrlConstant.ORDER_CANCEL_URL, paramsJson, dadaIsOnline);
            DadaApiResponse apiResponse = dadaClient.callRpc();
            if (ObjectUtils.isEmpty(apiResponse)) {
                throw new CustomizeException("调用达达取消接口失败");
            }
            if (!Objects.equals(0, apiResponse.getCode())) {
                throw new CustomizeException("达达取消失败" + apiResponse.getMsg());
            }
            log.info("达达取消成功，orderid={}", orderid);
            return true;
        } catch (Exception e) {
            log.error("达达取消快递失败，orderid={}，error={}", orderid, e);
            weixinAndOaMessageSend("达达物流取消订单异常，物流单" + orderid + ": " + e.getMessage(), 3, "", WuLiuConstant.ZLX_CH999_ID, null, null);
        }
        return false;
    }

    /**
     * uu取消订单
     *
     * @param deliveryId 物流单id
     * @param waybillNo 快递单
     * <AUTHOR> [<EMAIL>]
     * @date 2021-10-29
     */
    private boolean uuCancelOrder(Integer deliveryId,String waybillNo) {
        try {
            CancelOrderReq req = new CancelOrderReq();
            req.setCancelReason("取消订单");
            req.setPlatformInsideId(waybillNo);
            JSONObject result = cancelOrderService.cancelOrder(SysUtils.getUser(), req, WuLiuConstant.UU_PAOTUI_EXPRESS_TYPE);
            if (ObjectUtils.isEmpty(result)) {
                throw new CustomizeException("调用uu取消接口失败");
            }
            if (!Objects.equals(0, result.get("code"))) {
                throw new CustomizeException(""+result.get("userMsg"));
            }

            //wuLiuLogService.save(new WuLiuLogEntity().setWuliuid(Convert.toInt(req.getDeliveryId())).setInuser(SysUtils.getUser().getUserName()).setMsg(message).setDtime(LocalDateTime.now()));
            return true;
        } catch (Exception e) {
            log.error("uu取消快递失败，orderid={}，error={}", waybillNo, e);
            weixinAndOaMessageSend("uu物流取消订单异常，物流单" + deliveryId + ": " + e.getMessage(), 3, "", WuLiuConstant.ZLX_CH999_ID, null, null);
        }
        return false;
    }

    /**
     * 新中通快递取消订单
     *
     * @param deliveryId 物流单id
     * @param waybillNo 快递单
     * <AUTHOR> [<EMAIL>]
     * @date 2021-10-29
     */
    private boolean zhongTongCancelOrder(Integer deliveryId,String waybillNo) {
        try {
            CancelOrderReq req = new CancelOrderReq();
            req.setWaybillCode(waybillNo);
            JSONObject result = cancelOrderService.cancelOrder(SysUtils.getUser(), req, WuLiuConstant.ZHONGTONG_NEW_EXPRESS_TYPE);
            if (ObjectUtils.isEmpty(result)) {
                throw new CustomizeException("调用中通快递取消接口失败");
            }
            if (!Objects.equals(0, result.get("code"))) {
                throw new CustomizeException(""+result.get("userMsg"));
            }
            return true;
        } catch (Exception e) {
            log.error("中通快递取消失败，orderid={}，error={}", waybillNo, e);
            weixinAndOaMessageSend("中通快递取消订单异常，物流单" + deliveryId + ": " + e.getMessage(), 3, "", WuLiuConstant.ZYK_CH999_ID, null, null);
        }
        return false;
    }

    /**
     * shunfengApiServices.CancelSfOrder
     * 顺丰快递单取消接口
     *
     * @param sforderid
     * @param expressType2
     * @return Boolean
     * <AUTHOR> [<EMAIL>]
     * @date 2021-10-29
     */
    public Boolean cancelSfOrder(String sforderid, String expressType2) {
        expressType2 = Optional.ofNullable(expressType2).orElse("");

        WuLiuShunfengNoInfoEntity dt = wuLiuShunfengNoInfoService.queryByMailNoAndWuliuId(sforderid, null);
        if (dt != null) {
            String orderid = dt.getWuLiuId();
            Integer sareaid = dt.getSareaid();
            ShunFengCardVO card = getYueJieKaHao(sareaid, expressType2);
            StringBuilder bf = new StringBuilder();
            bf.append("<Request service='OrderConfirmService' lang='zh-CN'>");
            bf.append("<Head>").append(card.getClientCode()).append("</Head>");
            bf.append("<Body>");
            bf.append("<OrderConfirm dealtype='2' mailno='").append(sforderid).append("' orderid='").append(orderid).append("'/>");
            bf.append("</Body>");
            bf.append("</Request>");
            String xml = bf.toString();

            String verifyCode1 = xml + card.getCheckPwd();
            String verifyCode = shunfengMd5ToBase64String(verifyCode1);
            Map<String, Object> postValues = new HashMap<>();
            postValues.put("xml", xml);
            postValues.put("verifyCode", verifyCode);

            //POST地址
            String postUrl = "http://bsp-oisp.sf-express.com/bsp-oisp/sfexpressService";

            String xmlResult = postDate2(postUrl, postValues); //注：返回的数据是XML格式的噢
            try {
                Document document = XmlUtils.toDocument(xmlResult);
                Element root = document.getRootElement();
                Element head = root.element("Head");
                if ("OK".equals(head.getTextTrim())) {
                    return true;
                }
            } catch (Exception e) {
                return false;
            }
        }
        return false;
    }

    /**
     * zitidianServices.calculatePrice
     * 计算物流单快递价格
     *
     * @param scityid 寄件地
     * @param rcityid 收件地
     * @param weight  重量
     * @param com     快递公司
     * @return BigDecimal
     * @date 2021-09-30
     * <AUTHOR> [<EMAIL>]
     */
    public BigDecimal calculatePrice(Integer scityid, Integer rcityid, BigDecimal weight, String
            com) {
        AtomicReference<BigDecimal> price = new AtomicReference<>(BigDecimal.ZERO);

        Optional.ofNullable(scityid).flatMap(item -> Optional.ofNullable(rcityid)).ifPresent(item1 -> {
            String sCityIdString = StringUtils.substring(scityid.toString(), 0, 4);
            String rCityIdString = StringUtils.substring(rcityid.toString(), 0, 4);
            if (WuLiuConstant.SHUNFENG.equals(com)) {

                WuLiuPriceEntity priceEntity = wuLiuPriceService.getOne(sCityIdString, rCityIdString, null);
                // 同城
                if (sCityIdString.equals(rCityIdString)) {
                    priceEntity = Optional.ofNullable(priceEntity)
                            .orElseGet(() -> wuLiuPriceService.getOne(StringUtils.substring(sCityIdString, 0, 2), StringUtils.substring(rCityIdString, 0, 2), true));
                } else {
                    priceEntity = Optional.ofNullable(priceEntity)
                            .orElseGet(() -> wuLiuPriceService.getOne(sCityIdString, StringUtils.substring(rCityIdString, 0, 2), false));

                    priceEntity = Optional.ofNullable(priceEntity)
                            .orElseGet(() -> wuLiuPriceService.getOne(StringUtils.substring(sCityIdString, 0, 2), rCityIdString, false));

                    priceEntity = Optional.ofNullable(priceEntity)
                            .orElseGet(() -> wuLiuPriceService.getOne(StringUtils.substring(sCityIdString, 0, 2), StringUtils.substring(rCityIdString, 0, 2), false));
                }

                if (priceEntity != null) {
                    if (weight.compareTo(BigDecimal.ONE) <= 0) {
                        price.set(priceEntity.getUnitPrice());
                    } else {
                        BigDecimal weight1 = BigDecimal.valueOf((int) Math.ceil((weight.subtract(BigDecimal.ONE)).doubleValue()));
                        price.set(weight1.add(weight1.multiply(priceEntity.getAdvancePrice())));
                    }
                }

            } else if (WuLiuConstant.ZHONGTONG.equals(com)) {

                if (sCityIdString.startsWith("53") && rCityIdString.startsWith("53")) {
                    if (sCityIdString.startsWith("5301") && !rCityIdString.startsWith("5301")) {
                        if (weight.compareTo(BigDecimal.ONE) <= 0) {
                            price.set(BigDecimal.valueOf(8));

                        } else if ((weight.compareTo(BigDecimal.valueOf(6)) < 0)) {
                            BigDecimal weight1 = BigDecimal.valueOf((int) Math.ceil((weight.subtract(BigDecimal.ONE)).doubleValue()));

                            price.set(BigDecimal.valueOf(8).add(weight1.multiply(BigDecimal.valueOf(2))));
                        } else {
                            BigDecimal weight1 = BigDecimal.valueOf((int) Math.ceil(weight.doubleValue()));
                            price.set(weight1.multiply(BigDecimal.valueOf(2)));
                        }
                    } else if (!sCityIdString.startsWith("5301") && rCityIdString.startsWith("5301")) {
                        if (weight.compareTo(BigDecimal.ONE) <= 0) {
                            price.set(BigDecimal.TEN);
                        } else {
                            BigDecimal weight1 = BigDecimal.valueOf((int) Math.ceil((weight.subtract(BigDecimal.ONE)).doubleValue()));
                            price.set(BigDecimal.TEN.add(weight1.multiply(BigDecimal.valueOf(2))));
                        }
                    }
                }


            }
        });
        return price.get();
    }

    /**
     * subWLService.GetWuliuCompanyName
     * 获取快递名
     *
     * @param expressCode
     * @return String
     * <AUTHOR> [<EMAIL>]
     * @date 2021-10-29
     */
    public String getWuliuCompanyName(String expressCode) {
        if (StringUtils.isBlank(expressCode)) {
            return "";
        }
        return expressTypeService.listExpressType().stream().filter(item -> expressCode.equalsIgnoreCase(item.getExpressCode())).findFirst().orElseGet(ExpressTypeVO::new).getExpressName();
    }

    @Override
    public R<WuLiuResVO> getOne(Integer id) {
        WuLiuEntity entity = getById(id);
        return entity == null ? R.error("获取失败，该物流单不存在") : R.success("获取成功", WuLiuMapStruct.INSTANCE.toResVo(entity));
    }

    /**
     * 第三方快递公司列表
     *
     * @return
     */
    @Override
    public List<EnumVO> listExpressEnum() {
        List<ExpressEnumEntity> list = this.baseMapper.listExpressEnum();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(WuLiuMapStruct.INSTANCE::toEnumVO).collect(Collectors.toList());
    }

    /**
     * 第三方快递公司列表
     *
     * @return
     */
    @Override
    public List<ExpressEnumVO> listExpressEnumV2(Integer wuLiuId) {
        List<ExpressEnumEntity> list = this.baseMapper.listExpressEnum();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        //物流单状态
        Optional<WuLiuEntity> wuliuOpt = Optional.ofNullable(wuLiuId).filter(id -> id>0).map(wuLiuService::getById);
        List<Integer> expressTypeList = Arrays.stream(LogisticsExpressTypeEnum.values()).map(LogisticsExpressTypeEnum::getExpressType).collect(Collectors.toList());
        Optional<OaUserBO> oaUserOpt = Optional.ofNullable(currentRequestComponent.getCurrentStaffId());
        boolean isEnabledPaoTui = ModuleVersionKeys.isEnabled(RedisKeys.PAOTUI_AUTO_CALL_ENABLE,
                wuliuOpt.map(wuliu -> CommonUtils.defaultIfNullOrZero(wuliu.getSAreaId(), wuliu.getAreaId()))
                        .orElseGet(() -> wuLiuId == null ? oaUserOpt.map(OaUserBO::getAreaId).orElse(null) : null), false);
        return list.stream()
                .filter(v -> {
                    if(wuliuOpt.filter(wuLiu -> v.getExpressCode().equals(wuLiu.getCom())).isPresent()){
                        // 选中项必然要返回
                        return true;
                    }
                    /*if(isEnabledPaoTui && Stream.of(WuLiuConstant.UU_PAOTUI, WuLiuConstant.DADA, WuLiuConstant.MEITUAN, LogisticsExpressTypeEnum.SFTC.getCode())
                            .anyMatch(jkpt -> jkpt.equals(v.getExpressCode()))){
                        // 合并跑腿后, 不显示, uu, dd, meituan
                        return false;
                    }*/
                    if(Stream.of(WuLiuConstant.JIUJI_KUAI_SONG, WuLiuConstant.PAO_TUI)
                            .anyMatch(jkpt -> jkpt.equals(v.getExpressCode()))
                            && !isEnabledPaoTui
                    ){
                        //未启用跑腿自动派单，不显示跑腿和九机快送
                        return false;
                    }
                    return true;
                })
                .map(v -> {
            ExpressEnumVO expressEnum = WuLiuMapStruct.INSTANCE.toExpressEnumVO(v);
            Optional.ofNullable(v.getExpressType()).ifPresent(expressType -> {
                if (expressTypeList.contains(expressType)) {
                    expressEnum.setType(1);
                } else {
                    expressEnum.setType(0);
                }
            });
            return expressEnum;
        }).collect(Collectors.toList());
    }

    /**
     * 第三方快递公司列表
     *
     * @return
     */
    @Override
    public List<ShowPrintingEnumVOV2> getShowPrintingEnumVOList() {
        List<ExpressEnumEntity> list = this.baseMapper.listExpressEnum();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        List<ShowPrintingEnumVOV2> arrayList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            for (ExpressEnumEntity t : list) {
                ShowPrintingEnumVOV2 ShowPrintingEnumVOV2 = new ShowPrintingEnumVOV2()
                        .setLabel(t.getExpressName())
                        .setValue(t.getExpressCode());
                arrayList.add(ShowPrintingEnumVOV2);
            }
        } else {
            return new ArrayList<>();
        }
        return arrayList;

    }

    /**
     * 获取物流信息
     *
     * @param wuLiuId
     * @param subId
     * @return
     */
    @Override
    public List<WuLiuDTO> getWuLiuList(String actionName, Integer wuLiuId, Integer subId) {
        return this.baseMapper.getWuLiuList(actionName, wuLiuId, subId);
    }

    /**
     * 获取单个物流信息
     *
     * @param wuLiuId
     * @param subId
     * @return
     */
    @Override
    public WuLiuDTO getWuLiu(String actionName, Integer wuLiuId, Integer subId) {
        return this.baseMapper.getWuLiu(actionName, wuLiuId, subId);
    }

    /**
     * 是否是美团快送
     *
     * @param subId
     * @return
     */
    @Override
    public Boolean isFastMeiTuan(Integer subId) {
        Integer basketId = this.baseMapper.getFastMeiTuanBySubId(subId);
        return Objects.nonNull(basketId);
    }

    /**
     * get expectTime
     *
     * @param subId
     * @return
     */
    @Override
    public LocalDateTime getExpectTime(Integer subId) {
        return this.baseMapper.getExpectTime(subId);
    }

    /**
     * get expectTime
     *
     * @param subId
     * @return
     */
    @Override
    @DS("ch999oanew")
    public LocalDateTime getLiangpinExpectTime(Integer subId) {
        return this.baseMapper.getLiangpinExpectTime(subId);
    }

    /**
     * 获取物流的订单
     *
     * @param subId
     * @return
     */
    @Override
    public WuLiuSubDTO getWuLiuSub(Integer subId) {
        return this.baseMapper.getWuLiuSub(subId);
    }

    /**
     * 自提点
     *
     * @param ziTiDianId
     * @return
     */
    @Override
    public ZiTiDianDTO getZiTiDian(Integer ziTiDianId) {
        return this.baseMapper.getZiTiDian(ziTiDianId);
    }

    /**
     * 通过wuType获取物流信息
     *
     * @param sAreaId
     * @param wuType
     * @return
     */
    @Override
    public WuLiuEntity getWuLiuByWuType(Integer sAreaId, Integer wuType) {
        return this.baseMapper.getWuLiuByWuType(sAreaId, wuType);
    }

    /**
     * 二手良品订单物流
     *
     * @param subId
     * @return
     */
    @Override
    public WuLiuSubDTO getWuLiuReSub(Integer subId) {
        return this.baseMapper.getWuLiuReSub(subId);
    }

    /**
     * 原先就有物流单
     *
     * @param vo
     * @return
     */
    @Override
    @DS("ch999oanew")
    public WuLiuDTO getMarkAbnormalWuLiu(WuLiuInfoReqVO vo) {
        return this.baseMapper.getMarkAbnormalWuLiu(vo);
    }

    /**
     * apiServices.opLog
     * 后台订单相关 操作日志 无事务
     *
     * @param vo SubLogVO
     * @date 2021-10-08
     * <AUTHOR> [<EMAIL>]
     */
    public void opLog(SubLogVO vo) {
        String logName = "SubLogsNew";
        if (vo.getType() == 2) {
            logName = "basketLogsNew";
        }
        vo.setDTime(Optional.ofNullable(vo.getDTime()).orElseGet(LocalDateTime::now));
        mongoTemplate
                .upsert(new Query(Criteria.where("_id").is(vo.getSubId())), new Update().push("conts", vo),
                        logName);
    }

    /**
     * orderServices.checkWuLiuSub
     * 校验物流单的订单信息
     *
     * @param subId
     * @param subKinds
     * @param areaId
     * @param wuLiuType
     * @return String
     * @date 2021-10-09
     * <AUTHOR> [<EMAIL>]
     */
    public String checkWuLiuSub(Integer subId, Integer subKinds, Integer areaId, Integer wuLiuType) {
        boolean isExists;
        if (Objects.equals(subKinds, 2)) {
            isExists = wuLiuRecoverMarketInfoService.getRecoverMarketInfo(subId, areaId) != null;
            return isExists(isExists, wuLiuType, areaId);
        } else if (Objects.equals(subKinds, 3)) {
            isExists = wuLiuShouHouService.getShouHou(subId, YesOrNoEnum.YES.getCode()) != null;
            String existsMessage = isExists(isExists, wuLiuType, areaId);
            if (StringUtils.isNotBlank(existsMessage)) {
                return existsMessage;
            }
        } else {
            isExists = wuLiuSubService.getSub(subId) != null;
            String existsMessage = isExists(isExists, wuLiuType, areaId);
            if (StringUtils.isNotBlank(existsMessage)) {
                return existsMessage;
            }
        }

        if (Optional.ofNullable(areaId).orElse(0) > 0) {
            if (Objects.equals(subKinds, 3)) {
                isExists = wuLiuShouHouService.getShouHou2(subId, YesOrNoEnum.YES.getCode(), areaId) != null;
                String existsMessage = isExists(isExists, wuLiuType, areaId);
                if (StringUtils.isNotBlank(existsMessage)) {
                    return existsMessage;
                }
            } else {
                isExists = wuLiuShouHouService.getShouHou3(subId, areaId) != null;
                String existsMessage = isExists(isExists, wuLiuType, areaId);
                if (StringUtils.isNotBlank(existsMessage)) {
                    return existsMessage;
                }
            }
        }
        return "";
    }

    /**
     * @param isExists
     * @param wuLiuType
     * @param areaId
     * @return String
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-26
     */
    private String isExists(boolean isExists, Integer wuLiuType, Integer areaId) {
        if (!isExists) {
            String msg = "输入订单号错误，请输入进行中订单号";
            String addressType = "";
            // 内部物流判断收货门店，其他类型判断发货地区
            if (wuLiuType == null || wuLiuType == 1) {
                addressType = "收货地区";
            } else {
                addressType = "发货地区";
            }
            if (Optional.ofNullable(areaId).orElse(0) > 0) {
                msg += ("且 " + addressType + "是否与单号地区一致");
            }
            return msg;
        }
        return "";
    }

    /**
     * 获取该 openId 是否关注主站公众号
     *
     * @param openId
     * @return
     */
    public Boolean getWeChatFocus(String openId) {
        Integer xTenant = currentRequestComponent.getCurrentStaffId().getXTenant();
        SysConfig sysConfig = sysConfigService.lambdaQuery().eq(SysConfig::getCode, 7)
                .eq(SysConfig::getXtenant, xTenant).one();
        String url = sysConfig.getValue() + "/oaapi/api.ashx?act=wxOnFocus&openId=" + openId;
        String response = HttpUtil.get(url);
        JSONObject jsonObject = new JSONObject(response);
        String stats = jsonObject.get("stats").toString();
        return NumberConstant.ONE.toString().equals(stats);
    }

    public void backendOptionRecoverLog(SubLog log, Boolean isUserAddLog) {
        isUserAddLog = Optional.ofNullable(isUserAddLog).orElse(false);

        String logName = "recover_saleLogNew";
        if (log.getType() == 2) {
            logName = "recoverBasket_saleLogNew";
        }
        if (log.getType() == 1 && !Boolean.TRUE.equals(isUserAddLog) && !log.getShowType()) {
            //良品订单默认全部网站显示，除手工添加没有勾选网站显示的除外
            log.setShowType(true);
        }

        Map<String, Object> map = new HashMap<>(5);
        map.put("DTime", log.getDTime());
        map.put("Comment", log.getDTime());
        map.put("InUser", log.getDTime());
        map.put("Type", log.getDTime());
        map.put("showType", log.getDTime());

        // 存到mongdb
        Update update = new Update();
        update.push("conts", map);
        mongoTemplate.upsert(new Query(Criteria.where("_id").is(log.getSubId())), update, logName);
    }

    /**
     * oa999DAL.zhongtongApiServices.CreateOrder
     *
     * @param conn
     * @param sareaid
     * @return
     */
    public ZtoResultDTO createOrder(OrderGroupDTO conn, int sareaid) {
        String url = "http://japi.zto.cn";

        String cCompanyId = ZTO_COMPANY_ID;
        String cKey = ZTO_KEY;
        // 获取网点信息
        ZtoSiteConfigDTO ztoSiteConfig = getZtoSiteConfig(sareaid);
        Assert.isTrue(ztoSiteConfig.getId() != 0, "无法获取中通合作渠道");

        String ztoAccount = cCompanyId;
        String[] sendCityArray = conn.getSender().getCity().split(",");
        String[] receiveCityArray = conn.getReceiver().getCity().split(",");
        if (sendCityArray.length < 3) {
            String[] cityArray = new String[3];
            cityArray[0] = sendCityArray.length >= 1 ? sendCityArray[0] : "";
            cityArray[1] = sendCityArray.length >= 2 ? sendCityArray[1] : "";
            cityArray[2] = "";
            sendCityArray = cityArray;
        }
        if (receiveCityArray.length < 3) {
            String[] cityArray = new String[3];
            cityArray[0] = receiveCityArray.length >= 1 ? receiveCityArray[0] : "";
            cityArray[1] = receiveCityArray.length >= 2 ? receiveCityArray[1] : "";
            cityArray[2] = "";
            receiveCityArray = cityArray;
        }
        // 生成 8 位随机数
        String randomData = RandomUtil.randomNumbers(8);
        byte[] bytes = randomData.getBytes();
        String orderNum = String.valueOf(Math.abs(BitConverterUtils.toInt(bytes, 0))).substring(0, 4);

        // 构造请求体
        ZtoCreateReq.ZopSdkProperty zopSdkProperty = new ZtoCreateReq.ZopSdkProperty()
                .setCompanyId(cCompanyId)
                .setKey(cKey)
                .setUrl(url);

        ZtoCreateReq.PreOrderWaybillAccountReqDto preOrderWaybillAccountReqDto = new ZtoCreateReq.PreOrderWaybillAccountReqDto()
                .setAccount(ztoAccount);

        ZtoCreateReq ztoCreateReq = new ZtoCreateReq()
                .setZopSdkProperty(zopSdkProperty)
                .setNeedBigMark(true)
                .setIsSaveVas(true)
                .setPrepareSiteCode(ztoSiteConfig.getSiteCode())
                .setPrepareSiteName(ztoSiteConfig.getSiteName())
                .setPartnerId(cCompanyId)
                .setPartnerOrderCode(sareaid + StrUtil.DASHED + DateTimeFormatter.ofPattern("yyMMddHHmmssfff").format(LocalDateTime.now()) + orderNum)
                .setPreOrderWaybillAccountReqDto(preOrderWaybillAccountReqDto)
                .setReceiveProvince(receiveCityArray[0])
                .setReceiveCity(receiveCityArray[1])
                .setReceiveDistrict(receiveCityArray[2])
                .setReceiveAddress(conn.getReceiver().getAddress())
                .setReceiveName(conn.getReceiver().getName())
                .setReceivePhone(conn.getReceiver().getMobile())
                .setSaveVas(true)
                .setSendProvince(sendCityArray[0])
                .setSendCity(sendCityArray[1])
                .setSendDistrict(sendCityArray[3])
                .setSendAddress(conn.getSender().getAddress())
                .setSendId(String.valueOf(sareaid))
                .setSendMobile(conn.getSender().getMobile())
                .setSendName(conn.getSender().getName())
                .setSendCompany(conn.getSender().getCompany());

        ZtoResultDTO ztoResultDTO = getRequest(JsonUtil.object2JsonNonNull(ztoCreateReq));
        if (Boolean.TRUE.equals(ztoResultDTO.getStatus())) {
            //快递单号提交订阅
            subOrderInfo(cCompanyId, cKey, ztoResultDTO.getResult().getBillCode());
        }
        return ztoResultDTO;
    }

    public ZtoSiteConfigDTO getZtoSiteConfig(Integer areaid) {
        return this.baseMapper.getZtoSiteConfig(areaid);
    }

    /**
     * 请求中通接口
     * oa999DAL.zhongtongApiServices.GetRequest
     *
     * @param reqdata
     * @return
     */
    public ZtoResultDTO getRequest(String reqdata) {
        String redata = "";
        String reqerr = "";

        String url = SysConfigUtils.getMoaUrl() + "/cloudapi_nc/ncSegments/api/zop/saveOrder/savePreOrderWithBillCode?xservicename=oa-ncSegments";
        try {
            redata = HttpUtil.createPost(url).header("Authorization", currentRequestComponent.getCurrentToken()).body(reqdata).execute().body();
        } catch (IllegalArgumentException e) {
            reqerr = "ERROR:接口请求失败（" + e.getMessage() + "）";
        }

        ZtoResultDTO result;

        if (StringUtils.isBlank(redata)) {
            result = new ZtoResultDTO()
                    .setMessage(reqerr)
                    .setStatusCode("");
        } else {
            try {
                R rzto = JSONUtil.toBean(redata, R.class);
                if (rzto.getCode() == 0 && rzto.getData() != null) {
                    result = (ZtoResultDTO) rzto.getData();
                } else {
                    result = new ZtoResultDTO()
                            .setMessage(StringUtils.isBlank(rzto.getUserMsg()) ? "接口错误" : rzto.getUserMsg())
                            .setStatusCode("");
                }
            } catch (JSONException e) {
                log.error("中通接口数据解析失败: {}", Exceptions.getStackTraceAsString(e));
                result = new ZtoResultDTO()
                        .setMessage("接口数据解析失败")
                        .setStatusCode("");
            }
        }
        return result;
    }

    /**
     * 中通物流单订阅
     * oa999DAL.zhongtongApiServices.SubOrderInfo
     *
     * @param compayId
     * @param cKey
     * @param billCode
     * @return
     */
    public String subOrderInfo(String compayId, String cKey, String billCode) {
        if (StringUtils.isBlank(billCode)) {
            return "";
        }
        List<String> billCodeList = new ArrayList<>();
        billCodeList.add(billCode);
        return subOrderInfo(compayId, cKey, billCodeList);
    }

    /**
     * WuliuController.wuliucaozuo
     * subWLService.getCZ
     * subWLService.getCZResult
     * 物流操作
     *
     * @param currentUser OaUserBO
     * @param model WuliucaozuoVO
     * @return R<String>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-04
     */
    @Override
    public R<String> operateV1(OaUserBO currentUser, WuliucaozuoVO model) {
        R<String> json = new R<>();

        String type = Optional.ofNullable(model.getType()).orElse("1");
        String isapp = Optional.ofNullable(model.getIsapp()).orElse("");

        String MSG = "";//写入日志中的

        WuLiuEntity dr = getOne(Wrappers.<WuLiuEntity>lambdaQuery()
                .select(WuLiuEntity::getStats, WuLiuEntity::getShouJianRen,
                        WuLiuEntity::getPaiJianRen, WuLiuEntity::getCTime,
                        WuLiuEntity::getLinkType, WuLiuEntity::getDanHaoBind,
                        WuLiuEntity::getWuType, WuLiuEntity::getRAreaId,
                        WuLiuEntity::getId, WuLiuEntity::getRName,
                        WuLiuEntity::getSAreaId, WuLiuEntity::getSName,
                        WuLiuEntity::getRMobile, WuLiuEntity::getCom,
                        WuLiuEntity::getNu).eq(WuLiuEntity::getId, model.getWuliuid()), false);
        if (dr != null) {
            // 更新派送单信息 开始
            if (Objects.equals(type, "1")) {
                int oldState = getPaiSongStateByWuliuState(dr.getStats());
                int newState = getPaiSongStateByWuliuState(model.getStats());
                if (oldState != newState && !Objects.equals(dr.getDanHaoBind(), 0)) {
                    List<PaisongDTO> paisong = new ArrayList<>();

                    //订单 验证 是否已经出库
                    if (Arrays.asList(4, 6).contains(dr.getWuType()) && Arrays.asList(1, 2).contains(newState)) {
                        int count = Optional.ofNullable(wuLiuSubService.lambdaQuery()
                                .in(WuLiuSubEntity::getSubCheck, Arrays.asList(0, 1))
                                .eq(WuLiuSubEntity::getSubId, dr.getDanHaoBind()).count()).orElse(0);
                        if (count > 0) {
                            json.setUserMsg("订单还未出库，必须出库方可进行配送状态变更操作！");
                            json.setCode(5000);
                            return json;
                        }
                    }

                    paisong.add(new PaisongDTO()
                            .setPeisongstats(newState)
                            .setPeisongstatsOld(String.valueOf(oldState))
                            .setSubId(Long.valueOf(dr.getDanHaoBind()))
                            .setTrade(model.getPaijianren())
                            .setTradeOld(dr.getPaiJianRen())
                            .setRaddress(model.getRaddress())
                            .setLktp(Objects.equals(dr.getWuType(), 9) ? 12 : 0)
                            .setCh999(String.valueOf(dr.getDanHaoBind())));

                    nahuoSaveSend(paisong, currentUser);
                }
            }
            // 更新派送单信息 结束

            //是否推送派送信息给派送人员
            boolean isPush = false;
            //用来显示物流单是否是已完成派送
            boolean WLcomplete = false;

            LambdaUpdateChainWrapper<WuLiuEntity> set = lambdaUpdate();
            boolean ignoreSetUpdate = true;
            if ((Objects.equals(model.getStats(), 2) && Objects.equals(dr.getStats(), 1)) || Objects.equals(type, "2")) {

                set = lambdaUpdate().set(WuLiuEntity::getStats, 2);
                ignoreSetUpdate = false;

                if (StringUtils.isNotBlank(model.getShoujianren())) {
                    set.set(WuLiuEntity::getShouJianRen, model.getShoujianren());
                }
                if (Objects.equals(isapp, "1") && StringUtils.isNotBlank(model.getPaijianren())) {
                    set.set(WuLiuEntity::getPaiJianRen, model.getPaijianren());
                }
                set.eq(WuLiuEntity::getId, model.getWuliuid());

                MSG = "已经取货";
                if (!Objects.equals(dr.getPaiJianRen(), model.getPaijianren()) && StringUtils.isNotBlank(model.getPaijianren())) {
                    isPush = true;
                }

            } else if ((Objects.equals(model.getStats(), 3) && (Objects.equals(dr.getStats(), 2)
                    || (Objects.equals(dr.getStats(), 1) && Objects.equals(isapp, "1"))))
                    || Objects.equals(type, "2")) {

                if (StringUtils.isNotBlank(model.getPaijianren())) {
                    set.set(WuLiuEntity::getPaiJianRen, model.getPaijianren());
                }
                set.set(WuLiuEntity::getStats, 3)
                        .set(WuLiuEntity::getSendTime, LocalDateTime.now())
                        .eq(WuLiuEntity::getId, model.getWuliuid());
                ignoreSetUpdate = false;

                MSG = "派送员已经出发，姓名：" + model.getPaijianren() + "   手机号：" + getch999_number(model.getPaijianren());
            } else if (Objects.equals(model.getStats(), 6) && !Arrays.asList(4, 5).contains(dr.getStats()) || Objects.equals(type, "2"))//派送中 --> 已签收
            {
                set.set(WuLiuEntity::getReceiveUser, currentUser.getUserName())
                        .set(WuLiuEntity::getReceiveTime, LocalDateTime.now())
                        .set(WuLiuEntity::getStats, 6)
                        .eq(WuLiuEntity::getId, model.getWuliuid());
                ignoreSetUpdate = false;
                MSG = "已签收";

                if (Objects.equals(dr.getWuType(), 1)) { //内部定时
                    List<Integer> hqAreaIds = AreaInfoUtils.listAllCurrentHq();
                    Integer receiveAreaId = dr.getRAreaId();
                    Integer senderAreaId = dr.getSAreaId();
                    if (hqAreaIds != null && hqAreaIds.contains(receiveAreaId)) {
                        String hqAreaCode = Optional.ofNullable(areainfoService.getAreaInfoByAreaId2(receiveAreaId))
                                .orElseGet(Areainfo::new).getArea();
                        String sAreaName = Optional.ofNullable(areainfoService.getAreaInfoByAreaId2(senderAreaId))
                                .orElseGet(Areainfo::new).getArea();

                        Integer departId = Optional.ofNullable(ch999UserService.getUserByCh999Id(currentUser.getUserId())).orElseGet(Ch999User::new).getDepartId();

                        String msg = MessageFormat.format("您有{0}门店{1}发送的包裹已到达总部，{2}{3}已签收，请收到信息当天18点前到{2}领取（如已领取请忽略此信息）逾期包裹丢失概不负责。", sAreaName, dr.getSName(), Optional.ofNullable(getBumenOrXiaoqu(departId, true).getName()).orElse(""), currentUser.getUserName());
                        Ch999User obj = ch999UserService.getOne(Wrappers.<Ch999User>lambdaQuery().select(Ch999User::getCh999Id).eq(Ch999User::getCh999Name, dr.getRName()), false);
                        if (obj != null) {
                            weixinAndOaMessageSend(msg, 3, "", obj.getCh999Id(), MessagePushUtils.MessagePushOaMessageTypeConstant.LOGISTICS, null);
                        }

                    }

                    //推送生成发票派送单消息
                    setRabbitMqMessageForCsharp(JacksonJsonUtils.toJson(new RabbitMqActDTO().setAct("createFpPaisong").setData(model.getWuliuid())));

                }

            } else if ((Objects.equals(model.getStats(), 4) && Arrays.asList(3, 6).contains(dr.getStats())) || "2".equals(type)) {
                set.set(WuLiuEntity::getStats, 4)
                        .set(WuLiuEntity::getCTime, LocalDateTime.now())
                        .eq(WuLiuEntity::getId, model.getWuliuid());
                ignoreSetUpdate = false;

                MSG = "已完成派送";
                WLcomplete = true;
                //内部定时
                if (Objects.equals(dr.getWuType(), 1)) {
                    //如果已完成派送，并且是内部定时的
                    String msg = "您有包裹已到达" + Optional.ofNullable(Optional.ofNullable(areainfoService.getAreaInfoByAreaId2(dr.getRAreaId())).orElseGet(Areainfo::new).getArea()).orElse("")
                            + "，物流单号：<a href='" + SysConfigUtils.getMoaUrl() + "/mWuLiu/wuliuInfo?wuliuid=" + model.getWuliuid() + "'>" + model.getWuliuid() + "</a>，派送人：【" + currentUser.getUserName() + "】";
                    Ch999User obj = ch999UserService.getOne(Wrappers.<Ch999User>lambdaQuery().select(Ch999User::getCh999Id).eq(Ch999User::getCh999Name, dr.getRName()), false);
                    if (obj != null) {
                        weixinAndOaMessageSend(msg, 3, "", obj.getCh999Id(), MessagePushUtils.MessagePushOaMessageTypeConstant.LOGISTICS, null);
                    }
                    //如果是常用物资物流，同时完成常用物资订单的收货确认
                    if (Objects.equals(dr.getLinkType(), 12)) {
                        //TODO 常用物资订单的收货确认,常用资产接收凭证生成
                        List<Areaassetsub> rows = areaassetsubService.updateAreaAssetSub(currentUser.getUserName(), AssetSubChenkType.RECEIVED.getCode(), dr.getDanHaoBind());
                        if (CollectionUtils.isNotEmpty(rows) && (rows.get(0).getPzid() == null || Objects.equals(rows.get(0).getPzid(), 0))) {
                            areaassetsubService.createAreaAssetSubPingzheng(dr.getDanHaoBind(), "系统");
                        }
                    }
                } else if (WuLiuType.REPAIR_ORDER_DISPATCH.getCode().equals(dr.getWuType())) {

                } else if (WuLiuType.RENT.getCode().equals(dr.getWuType())) {
                    setWuliuComplete(model.getWuliuid(), dr.getDanHaoBind());
                }

            } else if ((Objects.equals(model.getStats(),50) && !Arrays.asList(4, 6).contains(dr.getStats())) || "2".equals(type)) {
                set.set(WuLiuEntity::getStats, WuliuStatusEnum.INVALID.getCode())
                        .eq(WuLiuEntity::getId, model.getWuliuid());
                ignoreSetUpdate = false;
                MSG = "作废操作";
                if (Arrays.asList("meituan", "meituanFastest", "meituan_jiuji").contains(dr.getCom()) && StringUtils.isNotEmpty(dr.getNu())) {
                    SaasPlatformDTO saasPlatform = new SaasPlatformDTO();
                    if ("meituan_jiuji".equals(dr.getCom())) {
                        AreaSubjectVO areaSubject = getAreaSubject(dr.getSAreaId());
                        saasPlatform.setSaasAreaid(dr.getSAreaId());
                        saasPlatform.setSaasTenant(areaSubject.getXtenant());
                        saasPlatform.setWuliuId(model.getWuliuid());
                    }
                    CancelItemVO cancelItemVo = new CancelItemVO();
                    cancelItemVo.setMtPeisongId(dr.getNu());
                    cancelItemVo.setDeliveryId(Long.valueOf(dr.getId()));
                    //物流中台取消美团订单
                    meituanCancelOrder(cancelItemVo, saasPlatform);
                }
            } else if (WuLiuLinkTypeEnum.INVOICE_LOGISTICS.getCode().equals(dr.getLinkType()) && !Objects.equals(dr.getPaiJianRen(), model.getPaijianren())) {
                set.set(WuLiuEntity::getPaiJianRen, model.getPaijianren())
                        .eq(WuLiuEntity::getId, model.getWuliuid());
                ignoreSetUpdate = false;
                MSG = "派件人由【" + dr.getPaiJianRen() + "】修改为【" + model.getPaijianren() + "】";
                if (StringUtils.isNotBlank(model.getPaijianren())) {
                    isPush = true;
                }
            }

            if (!ignoreSetUpdate && set.update()) {
                boolean isAutoKc = false;
                if (isAutoKc && (WuliuStatusEnum.COMPLETE.getCode().equals(model.getStats())
                        && (WuliuStatusEnum.DELIVERING.getCode().equals(dr.getStats())
                        || WuliuStatusEnum.RECEIVED.getCode().equals(dr.getStats())))
                        && dr.getWuType() == 1 && "1".equals(type)) {//内部定时自动完成收货
                    //TODO 调拨收货自动入库
                    //手机跨地区备货操作

                    //TODO 配件调拨
                }

                if (WuliuStatusEnum.COMPLETE.getCode().equals(model.getStats())
                        && (WuliuStatusEnum.DELIVERING.getCode().equals(dr.getStats()) || WuliuStatusEnum.RECEIVED.getCode().equals(dr.getStats()))
                        && WuLiuType.SECOND_HAND_DISPATCH.getCode().equals(dr.getWuType())
                        && Optional.ofNullable(dr.getDanHaoBind()).orElse(0) > 0) {
                    //良品订单派送
                    AreaSubjectVO areaSubject = getAreaSubjectBySubId(dr.getDanHaoBind(), 3);
                    if (areaSubject.getIs9JiSubject()) {
                        String msg = MessageFormat.format("您在{2}的订单{0}已由配送员【{1}】送达，如有异常配送请及时联系客服************，我们将及时为您处理，感谢您对{2}的支持。", dr.getDanHaoBind(), currentUser.getUserName(), "九机网".equals(areaSubject.getPrintName()) ? "九机" : areaSubject.getPrintName());

                        //Fun.sendMessage(dr["rmobile"].ToString(), msg, DateTime.Now.AddMinutes(-2).ToString(), areaSubject.smsChannel);
                        MessagePushUtils.pushSmsMessage(dr.getRMobile(), msg, Integer.valueOf(areaSubject.getSmsChannel()), LocalDateTime.now().plusMinutes(-2L), "系统");
                    }
                }
                json.setCode(0);

                if (Objects.equals(model.getStats(), 50) && Optional.ofNullable(model.getWuliuid()).orElse(0) > 0) { //作废，取消顺丰运单
                    WuLiuEntity wudt = getById(model.getWuliuid());
                    if (!ObjectUtils.isEmpty(wudt)) {
                        String nu = wudt.getNu();
                        String com = wudt.getCom();
                        if ("shunfeng".equals(com)) {
                            boolean isSuccess;
                            boolean isShunfengLaas = isShunfengLaas(wudt.getDTime());
                            if(isShunfengLaas) {
                                isSuccess = cancelShunfengLaas(nu).getData();
                            } else {
                                isSuccess = cancelSfOrder(nu, "");
                            }
                            if (isSuccess) {
                                MSG += "，顺丰物流单" + nu + "已取消";
                            }
                        }
                        //顺丰中台取消快递
                        else if ("shunfeng_jiuji".equals(com)) {
                            List<String> nus = new ArrayList<>();
                            nus.add(String.valueOf(model.getWuliuid()));
                            R<SfCancelOrderResultRes> result = sfLogisticsCenterCancelOrder(nus);
                            if (result != null && Objects.equals(result.getCode(), 0) && "success".equals(result.getMsg())) {
                                MSG += "，顺丰(九机特惠)物流单" + nu + "已取消";
                            } else {
                                MSG += "，顺丰(九机特惠)物流单" + nu + "取消失败；失败原因：" + (result != null ? result.getUserMsg() : "");
                            }
                        } else if ("zhongtong".equals(com)) {
                            List<WuliuZtoYuyue> yuyue = wuliuZtoYuyueService
                                    .lambdaQuery()
                                    .eq(WuliuZtoYuyue::getMailno, nu).list();
                            if (CollectionUtils.isNotEmpty(yuyue)) {
                                for (WuliuZtoYuyue yyr : yuyue) {
                                    ZtoCancelDTO cres = ztoCancelOrder(yyr.getOrdercode(), yyr.getCompanyid(), "取消订单");
                                    if (Boolean.TRUE.equals(cres.getStatus()) && CollectionUtils.isNotEmpty(cres.getData())) {
                                        ZtoCancelItemDTO citem = cres.getData().get(0);
                                        if (citem.getResult()) {
                                            MSG += "，中通物流运单" + nu + "已取消";
                                            wuliuZtoYuyueService.lambdaUpdate()
                                                    .set(WuliuZtoYuyue::getCancel, 1)
                                                    .eq(WuliuZtoYuyue::getId, yyr.getId())
                                                    .update();
                                        } else {
                                            MSG += "，中通物流运单" + nu + "取消失败（原因：" + citem.getReason() + "）";
                                        }
                                    }
                                }
                            }
                        } else if (WuLiuConstant.ZHONGTONG_NEW.equals(com)) {
                            zhongTongCancelOrder(wudt.getId(),nu);
                        } else if ("dada".equals(com)) {
                            dadaCancelOrder(nu);
                        } else if (WuLiuConstant.UU_PAOTUI.equals(com)) {
                            uuCancelOrder(wudt.getId(), nu);
                        }
                    }
                }

                writewuliulogs(model.getWuliuid(), currentUser.getUserName(), MSG, null);

                if (WLcomplete) { //如果物流为已完成
                    if (Arrays.asList(WuLiuLinkTypeEnum.DOOR_TO_DOOR_PICK_UP.getCode(), WuLiuLinkTypeEnum.DELIVERY_DOOR_TO_DOOR.getCode()).contains(dr.getLinkType())) {//如果是售后预约的，要修改预约状态为已完成
                        // 售后预约的，要修改预约状态为已完成
                        yuyuecomplete(dr.getDanHaoBind());
                    } else if (WuLiuLinkTypeEnum.INVOICE_LOGISTICS.getCode().equals(dr.getLinkType())) {//发票物流单，修改发票为已完成
                        // 修改发票为已完成
                       changePiaoByWuLiu(model.getWuliuid(), currentUser.getUserName());
                    } else if (WuLiuLinkTypeEnum.FIXED_ASSET_ALLOCATION.getCode().equals(dr.getLinkType())) {
                        //固定资产调拨发货完成
                        // TODO 固定资产调拨发货完成 subWLService.getCZResult 3513-3519
                    }
                }

                if (isPush
                        && WuLiuLinkTypeEnum.INVOICE_LOGISTICS.getCode().equals(dr.getLinkType())
                        && WuLiuType.OTHER.getCode().equals(dr.getWuType())) {
                    List<Integer> userIdList = ch999UserService.getUserIdByUserName(model.getPaijianren());
                    Integer userId = null;
                    if (CollectionUtils.isNotEmpty(userIdList)) {
                        userId = userIdList.get(0);
                    }

                    if (userId != null) {
                        weixinAndOaMessageSend("你有一个发票派送单需要派送<a href='" + SysConfigUtils.getMoaUrl() + "/mWuLiu/wuliuInfo?wuliuid=" + model.getWuliuid() + "' >vo.getWuliuid()</a>", 3, "", userId, MsgTypeEnum.LOGISTICS_NOTICE.getCode(), 0);
                    }
                }
            }
        }

        json.setUserMsg("操作成功");
        return json;
    }

    /**
     * PiaoService.changePiaoByWuLiu
     *
     * @param wuliuid
     * @param userName
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-15
     */
    private void changePiaoByWuLiu(Integer wuliuid, String userName) {
        // TODO 此处需要调用财务发票相关接口
    }

    /**
     * ShouhouYuyue.yuyuecomplete
     * 设置为已完成
     *
     * @param id
     * @return boolean
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-15
     */
    private boolean yuyuecomplete(Integer id) {
        if(id == null) {
            return false;
        }
        return shouHouYuYueService.lambdaUpdate().set(ShouHouYuYueEntity::getStats, 3).in(ShouHouYuYueEntity::getStats, Arrays.asList(6, 7)).and(item -> item.isNull(ShouHouYuYueEntity::getZy).or().eq(ShouHouYuYueEntity::getZy, false)).eq(ShouHouYuYueEntity::getId, id).update();
    }

    /**
     * DepartServices.getBumenOrXiaoqu
     * 获取 部门 或  小区
     *
     * @param departId
     * @param isdefault
     * @return
     */
    public DepartInfo getBumenOrXiaoqu(Integer departId, Boolean isdefault) {
        isdefault = Optional.ofNullable(isdefault).orElse(false);
        DepartInfo one = getAllDepartData(null).stream().filter(item -> Objects.equals(item.getId(), departId)).findFirst().orElse(null);
        if (one == null || one.getDataLevel() < 3) {
            if (isdefault && one != null) {
                return one;
            }
            return new DepartInfo();
        }
        if (one.getDataLevel() < 3) {
            return one;
        }
        DepartInfo info = getAllDepartData(false).stream().filter(item -> Optional.ofNullable(one.getDepartPathList()).orElseGet(ArrayList::new).contains(String.valueOf(item.getId())) && (Objects.equals(item.getDataType(), DepartTypeEnum.SMALL_REGION.getCode()) || Objects.equals(item.getDataType(), DepartTypeEnum.DEPARTMENT.getCode()))).findFirst().orElseGet(DepartInfo::new);

        if ((info.getId() == null || Objects.equals(info.getId(), 0)) && isdefault) {
            info = one;
        }
        return info;
    }

    /**
     * DepartServices.getAllDepartData
     * 获取所有部门信息,包含已删除部门（缓存9小时）
     *
     * @param isCache 是否走缓存，默认 true=走缓存
     * @return List<departInfo>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-08
     */
    @Override
    public List<DepartInfo> getAllDepartData(Boolean isCache) {
        Boolean isCache2 = Optional.ofNullable(isCache).orElse(true);
        List<DepartInfo> list;
        String cacheKey = "stock:depart:list_all";

        if (RedisUtils.hasKey(cacheKey) && isCache2) {
            String json = RedisUtils.get(cacheKey);
            list = JSON.parseArray(json, DepartInfo.class);
        } else {
            list = departInfoService.list();
            RedisUtils.set(cacheKey, list, Duration.ofHours(9));
        }
        return list;
    }


    /**
     * subWLService.getch999_number
     * 获取电话号码
     *
     * @param user 姓名串，以","隔开
     * @return string 电话号码串，以","隔开
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-08
     */
    private String getch999_number(String user) {
        try {
            if (StringUtils.isBlank(user)) {
                return "";
            }
            Set<String> nameSet = Arrays.stream(user.split(",")).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
            List<Ch999User> list = Optional.ofNullable(ch999UserService.lambdaQuery()
                    .in(Ch999User::getCh999Name, nameSet).select(Ch999User::getMobile).list())
                    .orElseGet(ArrayList::new);
            return StringUtils.join(list.stream().map(Ch999User::getMobile).collect(Collectors.toList()), ",");
        } catch (Exception e) {
            log.error("WuLiuServiceImpl.getCh999Number 报错：{}", Exceptions.getStackTraceAsString(e), e);
            return "";
        }
    }

    /**
     * nahuoServices.SaveSend
     * 派送单状态
     *
     * @param paisong
     * @param currentUser
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-08
     */
    private void nahuoSaveSend(List<PaisongDTO> paisong, OaUserBO currentUser) {
        // TODO 涉及到派送单功能建议走队列
        Map<String, Object> data = new HashMap<>(NumUtil.FOUR);
        data.put("paisong", paisong);
        data.put("issms", "1");
        LogInfoVO loginfo = new LogInfoVO();
        loginfo.setAreaid(currentUser.getAreaId());
        loginfo.setUserName(currentUser.getUserName());
        loginfo.setRank(currentUser.getRank());
        loginfo.setXtenant(currentUser.getXTenant());
        data.put("loginfo", loginfo);
        data.put("iswuliu", "");
        setRabbitMqMessageForCsharp(JacksonJsonUtils.toJson(new RabbitMqActDTO().setAct("nahuoSaveSend").setData(data)));
    }

    /**
     * Fun.getPaiSongStateByWuliuState
     * 根据派送单的状态获取物流单的状态
     *
     * @param wuliuState int
     * @return int
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-08
     */
    public static int getPaiSongStateByWuliuState(int wuliuState) {
        int result = 0;
        switch (wuliuState) {
            case 1:
                //未安排 等待取货
                break;
            case 2:
                result = 1; //等待配送 等待派送
                break;
            case 3:
                result = 2; //已送出 派送中
                break;
            case 4:
                result = 4;//已送达 完成
                break;
            default:
                break;
        }
        return result;
    }

    @Override
    public Integer delWuliuByLinkId(Long linkId, String username) {
        List<WuLiuEntity> wuLiuEntityList = this.baseMapper.delWuliuByLinkId(linkId);
        if (!wuLiuEntityList.isEmpty()) {
            WuLiuEntity wuLiuEntity = wuLiuEntityList.get(0);
            Integer id = wuLiuEntity.getId();
            String com = wuLiuEntity.getCom();
            String nu = wuLiuEntity.getNu();
            if (Objects.equals(com, WuLiuConstant.SHUNFENG) && ObjectUtil.isNotEmpty(cancelSfOrder(nu, null))) {
                writewuliulogs(id, username, "取消顺丰运单" + nu, null);
            } else {
                writewuliulogs(id, username, "作废操作", null);
            }

            return NumUtil.ONE;
        }
        return NumUtil.ZERO;
    }

    /**
     * 作废物流单
     *
     * @param id      物流单 ID
     * @param request HttpServletRequest
     * @return R<Boolean>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-01
     */
    @Override
    public R<Boolean> invalidV1(Integer id, HttpServletRequest request) {
        WuLiuEntity entity = getById(id);
        if (entity == null) {
            return R.error("该物流单不存在");
        }

        boolean cancelIsSuccess = false;
        boolean isShunfengLaas = false;
        boolean alreadyCancled = false;

        String nu = Optional.ofNullable(entity.getNu()).orElse("");
        if (WuLiuConstant.SHUNFENG_LAAS.equals(entity.getCom()) && StringUtils.isNotBlank(nu)) {
            isShunfengLaas = true;
            String message = "";
            R<Boolean> r = cancelShunfengLaas(nu);
            if (r != null) {
                if (r.getData()) {
                    cancelIsSuccess = true;
                    message = "取消顺丰快递运单" + nu + "成功";
                } else if (Optional.ofNullable(r.getUserMsg()).orElse("").contains("已取消")) {
                    alreadyCancled = true;
                } else {
                    message = "取消顺丰快递运单" + nu + "失败，" + r.getUserMsg();
                }
            } else {
                message = "取消顺丰快递运单" + nu + "失败，物流中台接口调用失败";
            }
            if (!alreadyCancled) {
                wuLiuLogService.save(new WuLiuLogEntity().setWuliuid(id).setInuser(SysUtils.getUserName())
                        .setMsg(message).setDtime(LocalDateTime.now()));
            }
        } else if (WuLiuConstant.UU_PAOTUI.equals(entity.getCom()) && StringUtils.isNotBlank(nu)) {
            uuCancelOrder(entity.getId(), entity.getNu());
        } else if (WuLiuConstant.ZHONGTONG_NEW.equals(entity.getCom()) && StringUtils.isNotBlank(nu)) {
            CancelOrderReqV2 cancelOrderReqV2 = new CancelOrderReqV2()
                    .setDeliveryId(entity.getId())
                    .setExpressType(entity.getCom())
                    .setWaybillCode(entity.getNu());

            cancelOrderService.cancelOrder(SysUtils.getUser(), cancelOrderReqV2);
        } else if (WuLiuConstant.DADA.equals(entity.getCom()) && StringUtils.isNotBlank(nu)) {
            dadaCancelOrder(entity.getNu());
        } else if (StringUtils.isNotBlank(nu)) {
            boolean cancelSuccess = logisticsExpressService.cancelOrderV2(nu);
            if (cancelSuccess) {
                cancelOrder(SysUtils.getUser(), id);
            }
        }

        if (isShunfengLaas && !cancelIsSuccess && !alreadyCancled) {
            return R.error("作废失败，顺丰快递取消失败");
        }
        Map<String, Object> map = new HashMap<>(NumUtil.TWO);
        map.put("stats", 50);
        map.put("wuliuid", id);
        Map<String, String> header = new HashMap<>(NumUtil.TWO);
        header.put("authorization", request.getHeader("authorization"));
        header.put("cookie", request.getHeader("cookie"));
        HttpRequest httpRequest = HttpRequest.post(SysConfigUtils.getMoaUrl() + "/wuliu/wuliucaozuo?actionName=cz")
                .body(JacksonJsonUtils.toJson(map)).headerMap(header, false);

        try {
            String s = OptionalUtils.ifNotNull(httpRequest, item -> {
                HttpResponse response = item.execute();
                return OptionalUtils.ifNotNull(response, HttpResponse::body);
            });
            if (StringUtils.isBlank(s) || s.contains(WuLiuConstant.LOGIN_TEXT)) {
                return R.error("作废失败，请刷新当前页确认是否登录");
            }

            R<Object> objectR = JacksonJsonUtils.toClass(s, new com.fasterxml.jackson.core.type.TypeReference<R<Object>>() {
            });
            if (objectR != null && Objects.equals(objectR.getCode(), 0)) {
                return R.success("作废成功", true);
            }
        } catch (Exception e) {
            log.error("调用 C# 物流单作废接口报错: {}, 入参: {}", Exceptions.getStackTraceAsString(e), JacksonJsonUtils.toJson(map), e);
        }
        return R.error("作废失败");
    }

    /**
     * 顺丰Laas取消快递
     *
     * @param nu
     * @return R<Boolean>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-21
     */
    public R<Boolean> cancelShunfengLaas(String nu) {
        R<Boolean> result = new R<>();
        result.setData(false);
        CancelOrderReq req = new CancelOrderReq();
        req.setXTenantId(SysUtils.getXtenantId());
        req.setExpressType(LogisticsTypeEnum.SHUN_FENG_LAAS.getCode());
        req.setWaybillCode(nu);
        // 生成签名
        long current = DateUtil.current();
        String signature = OaAuthUtil.signByUri(JiuJiApi.CANCLE_ORDER, current, req);
        OaSignReq<CancelOrderReq> signReq = new OaSignReq<>(signature, current, req);
        JSONObject jsonObject = JSONUtil.parseObj(signReq);
        Object data = jsonObject.get("data");
        jsonObject.remove("data");
        jsonObject.set("Data", data);
        try {
            String response = LogisticsHttpClient.post(JiuJiApi.CANCLE_ORDER, jsonObject);
            R<LogisticsBase> logisticsBaseResponse = JacksonJsonUtils.toClass(response, new com.fasterxml.jackson.core.type.TypeReference<R<LogisticsBase>>() {
            });
            result.setCode(logisticsBaseResponse.getCode());
            result.setMsg(logisticsBaseResponse.getMsg());
            result.setUserMsg(logisticsBaseResponse.getUserMsg());
            if(Objects.equals(logisticsBaseResponse.getCode(), 0)) {
                result.setData(true);
            }
        } catch (Exception e) {
            log.error("顺丰快递取消报错: {}，入参: {}", Exceptions.getStackTraceAsString(e), jsonObject);
            throw new CustomizeException("顺丰快递取消失败", e);
        }
        return result;
    }

    /**
     * 顺丰（九机特惠）取消快递
     *
     * @param nu
     * @return R<Boolean>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-21
     */
    public R<Boolean> cancelShunfengJiuji(String nu) {
        R<Boolean> result = new R<>();
        result.setData(false);
        CancelOrderReq req = new CancelOrderReq();
        req.setPlatformDeliveryIdList(Collections.singletonList(nu));
        JSONObject response = cancelOrderService.cancelOrder(SysUtils.getUser(), req, LogisticsTypeEnum.SHUN_FENG.getCode());
        if(response == null) {
          return result;
        }
        Integer code = response.getInt("code");
        result.setCode(code);
        result.setMsg(response.getStr("msg"));
        result.setUserMsg(response.getStr("userMsg"));
        if(Objects.equals(code, 0)) {
            result.setData(true);
        }
        return result;
    }

    /**
     * subWLService.CancelExpress
     *
     * @param wuliuId      物流单号
     * @param expressType  快递类型
     * @param userName     员工姓名
     * @param expressType2 快递类型2
     * @return CH99Result
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-02
     */
    public R<Boolean> cancelExpress(Integer wuliuId, String expressType, String userName, String expressType2) {
        expressType2 = Optional.ofNullable(expressType2).orElse("");

        R<Boolean> result = new R<>();
        try {

            WuLiuEntity entity = getOne(Wrappers.<WuLiuEntity>lambdaQuery().select(WuLiuEntity::getNu, WuLiuEntity::getCom, WuLiuEntity::getSAreaId).eq(WuLiuEntity::getId, wuliuId), false);

            String outputMessage = "";

            // 查询到指定物流单号
            if (entity != null) {
                outputMessage += String.format("物流单数据:单号-%s快递公司-%s", entity.getNu(), entity.getCom());
                boolean cancelResult = false;
                String cancelDes = "";
                switch (expressType) {
                    case "shansong":
                        CancelResponseDataVO res = shansongCancelOrder(entity.getNu());
                        if (res != null) {
                            cancelResult = true;
                            cancelDes = "取消闪送运单" + entity.getNu() + "成功";
                        }
                        break;
                    case "shunfeng":
                        if (isShunfengLaas(entity.getDTime())) {
                            cancelResult = cancelShunfengLaas(entity.getNu()).getData();
                        } else {
                            cancelResult = cancelSfOrder(entity.getNu(), expressType2);
                        }
                        cancelDes = "取消顺丰运单" + entity.getNu() + "成功";
                        break;
                    case "shunfeng_jiuji":
                        cancelResult = cancelShunfengJiuji(entity.getNu()).getData();
                        cancelDes = "取消顺丰（九机特惠）运单" + entity.getNu() + "成功";
                        break;
                    case "meituan":
                        CancelItemVO cancelItemVo = new CancelItemVO();
                        cancelItemVo.setMtPeisongId(entity.getNu());
                        cancelItemVo.setDeliveryId(Long.valueOf(wuliuId));
                        SaasPlatformDTO saasPlatform = new SaasPlatformDTO();
                        saasPlatform.setIsJiuJi(true);
                        cancelResult = meituanCancelOrder(cancelItemVo, saasPlatform);
                        cancelDes = "取消美团运单" + entity.getNu() + "成功";
                        break;
                    case "jingdong":
                        cancelDes = "取消京东物流（九机特惠）运单" + entity.getNu() + "成功";
                        break;
                    case "dada":
                        cancelResult = dadaCancelOrder(entity.getNu());
                        cancelDes = "取消达达运单" + entity.getNu() + "成功";
                        break;
                    case "ems":
                        cancelDes = "取消EMS运单" + entity.getNu() + "成功";
                        break;
                    case "uupt":
                        cancelResult = uuCancelOrder(entity.getId(),entity.getNu());
                        cancelDes = "取消UU运单" + entity.getNu() + "成功";
                        break;
                    case WuLiuConstant.ZHONGTONG_NEW:
                        cancelResult = zhongTongCancelOrder(entity.getId(),entity.getNu());
                        cancelDes = "取消中通快递新运单" + entity.getNu() + "成功";
                    default:
                        cancelResult = true;
                        break;
                }

                if (cancelResult) {
                    cancelOrder(SysUtils.getUser(), entity.getId());
                    result.setCode(0);
                    result.setUserMsg(cancelDes);
                }

            } else {
                outputMessage += "未查询到相关物流信息";
            }
            log.info(outputMessage);
            return result;

        } catch (Exception e) {
            log.error("物流单快递取消失败: {}", Exceptions.getStackTraceAsString(e), e);
            result.setUserMsg("快递取消失败");
            result.setCode(5000);
            return result;
        }
    }

    /**
     * 根据物流单创建时间判断，是否是新的顺丰丰桥（顺丰Laas)
     *
     * @param dtime 物流单创建时间
     * @return boolean
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-21
     */
    public boolean isShunfengLaas(LocalDateTime dtime) {
        if (isEnableShunfengLaas()) {
            if(RedisUtils.hasKey(CacheKey.Redis.WULIU_SHUNFENG_LAAS_ENABLE_TIME)) {
                String s = RedisUtils.get(CacheKey.Redis.WULIU_SHUNFENG_LAAS_ENABLE_TIME);
                if(StringUtils.isNotBlank(s)) {
                    LocalDateTime shunfengLassOnlineTime = LocalDateTime.parse(s, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    return Optional.ofNullable(dtime).orElseGet(LocalDateTime::now).isAfter(shunfengLassOnlineTime);
                }
            }
        }
        return false;
    }

    /**
     * 是否启用顺丰 LaaS
     *
     * @return boolean
     * <AUTHOR> [<EMAIL>]
     * @date 2022-01-12
     */
    public boolean isEnableShunfengLaas() {
        // redis 控制顺丰 LaaS 是否启用，redis key=stock:wuliu:shunfeng_laas_is_enable，1=启用，0=不启用
        boolean isEnableShunfengLaas = false;
        if (RedisUtils.hasKey(CacheKey.Redis.WULIU_SHUNFENG_LAAS_IS_ENABLE)) {
            isEnableShunfengLaas = Objects.equals("1", RedisUtils.get(CacheKey.Redis.WULIU_SHUNFENG_LAAS_IS_ENABLE));
        }
        // 哪些地区启用顺丰 laas
        if (!isEnableShunfengLaas && RedisUtils.hasKey(CacheKey.Redis.WULIU_SHUNFENG_LAAS_ENABLE_AREA)) {
            String redisValue = RedisUtils.get(CacheKey.Redis.WULIU_SHUNFENG_LAAS_ENABLE_AREA);
            if (StringUtils.isNotBlank(redisValue)) {
                Integer currentAreaId = SysUtils.getUser().getAreaId();
                isEnableShunfengLaas = Arrays.stream(redisValue.split(",")).anyMatch(item -> Objects.equals(item, String.valueOf(currentAreaId)));
            }
        }
        return isEnableShunfengLaas;
    }

    /**
     * 是否启用高德地图新接口
     *
     * @return boolean
     * <AUTHOR> [<EMAIL>]
     * @date 2022-01-12
     */
    public boolean isEnableAmapNew() {
        // redis 控制是否启用高德地图新接口，redis key=stock:wuliu:amap_new_enable_switch，1=启用，0=不启用
        boolean isEnableAmapNew = false;
        if (RedisUtils.hasKey(CacheKey.Redis.WULIU_AMAP_NEW_ENABLE_SWITCH)) {
            isEnableAmapNew = Objects.equals("1", RedisUtils.get(CacheKey.Redis.WULIU_AMAP_NEW_ENABLE_SWITCH));
        }
        return isEnableAmapNew;
    }

    /**
     * ShansongApiService.CancelOrder
     * 闪送取消订单
     *
     * @param issOrderNo
     * @return CancelResponseData
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-03
     */
    public CancelResponseDataVO shansongCancelOrder(String issOrderNo) {
        ShansongAppConfigs shansongAppConfigs = shansongAppConfigsService.getShansongAppConfigs();
        ShansongServiceImpl.verifyConfig(shansongAppConfigs);
        if (StringUtils.isBlank(issOrderNo)) {
            throw new CustomizeException("订单号不能为空");
        }

        String url = shansongAppConfigs.getServeraddress() + "/openapi/merchants/v5/abortOrder";
        Map<String, String> data = new HashMap<>(2);
        data.put("issOrderNo", issOrderNo);

        String resposeObj = ShanSongUtil.post(shansongAppConfigs, url, data);

        Integer status = JsonParseUtil.getStatus(resposeObj);
        String msg = JsonParseUtil.getMsg(resposeObj);

        if (Objects.equals(status, 200)) {
            throw new CustomizeException(msg);
        }
        String resData = JsonParseUtil.getData(resposeObj);
        return JsonParseUtil.toBean(resData, CancelResponseDataVO.class);
    }

    /**
     * Meituan.CancelOrder
     * meituanServices.CancelOrder
     * 美团取消快递订单
     *
     * @param data         CancelItemVO
     * @param saasPlatform SaasPlatformDTO
     * @return boolean
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-03
     */
    private boolean meituanCancelOrder(CancelItemVO data, SaasPlatformDTO saasPlatform) {
        boolean isSuccess = false;
        try {
            log.info("收到SAAS取消快递单请求：" + JacksonJsonUtils.toJson(data) + JacksonJsonUtils.toJson(saasPlatform));
            if (data == null || saasPlatform == null) {
                throw new CustomizeException("请求数据不能为空。");
            }
            if (!saasPlatform.getIsJiuJi()) {
                if (!verifySaasPlatform(saasPlatform)) {
                    throw new CustomizeException("物流SAAS接口未配置");
                }
            }

            CancelOrderRequestVO logisticRequest = new CancelOrderRequestVO().setExpressType(1).setPlatformInsideId(data.getMtPeisongId())
                    .setDeliveryId(data.getDeliveryId()).setCancelOrderReasonId(199)
                    .setCancelReason(StringUtils.isNotBlank(data.getCancelReason()) ? data.getCancelReason() : "主动取消");

            CancelOrderReq req = new CancelOrderReq();
            req.setExpressType(1);
            req.setPlatformInsideId(data.getMtPeisongId());
            req.setDeliveryId(String.valueOf(data.getDeliveryId()));
            req.setCancelReason(StringUtils.isNotBlank(data.getCancelReason()) ? data.getCancelReason() : "主动取消");
            req.setCancelOrderReasonId(101);

            OaUserBO currentUser = SysUtils.getUser();
            JSONObject result = cancelOrderService.cancelOrder(currentUser, req, LogisticsTypeEnum.MEI_TUAN.getCode());
            Object code = result.get("code");
            String message = "";
            if (!Objects.equals(code, 0)) {
                isSuccess = true;
                message = "取消失败，物流中台返回：" + result.get("userMsg");
            } else {
                message = "取消成功";
            }
            wuLiuLogService.save(new WuLiuLogEntity().setWuliuid(Convert.toInt(req.getDeliveryId())).setInuser(currentUser.getUserName()).setMsg(message).setDtime(LocalDateTime.now()));
            return isSuccess;
        } catch (Exception e) {
            log.error("美团快递取消失败: {}", Exceptions.getStackTraceAsString(e), e);
            MessagePushUtils.pushOaMessage(WuLiuConstant.ZLX_CH999_ID, "美团快递取消失败：" + Exceptions.getStackTraceAsString(e), "", MessagePushUtils.MessagePushOaMessageTypeConstant.ABNORMAL);
        }
        return isSuccess;
    }

    /**
     * 物流单取消快递，暂时支持顺丰快递
     *
     * @param id 物流单号
     * @return R<Boolean>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-02
     */
    @Override
    public R<Boolean> cancelExpress(Integer id) {
        WuLiuEntity entity = getById(id);
        if (entity == null) {
            return R.error("快递取消失败，该物流单不存在");
        }
        if (Arrays.asList(WuLiuConstant.SHUNFENG, WuLiuConstant.SHUNFENG_LAAS).contains(entity.getCom())) {
            R<String> r = cancelOrderService.shunfengCancelOrder(entity.getNu(), String.valueOf(id));
            if (r != null && Objects.equals(r.getCode(), 0)) {
                //lambdaUpdate().set(WuLiuEntity::getCom, "").set(WuLiuEntity::getNu, "").eq(WuLiuEntity::getId, id).update()
                //wuLiuLogService.addOne(id, currentRequestComponent.getCurrentStaffId().getUserName(), "取消顺丰运单"+ entity.getNu() +"成功")
                cancelOrder(SysUtils.getUser(), entity.getId());
                return R.success("快递取消成功");
            }
        }
        WuLiuWuliuwangdianEntity entity2 = wuLiuWuliuwangdianService.getOne(Wrappers.<WuLiuWuliuwangdianEntity>lambdaQuery().select(WuLiuWuliuwangdianEntity::getExepresstype).eq(WuLiuWuliuwangdianEntity::getWuliuid, id), false);
        return cancelExpress(id, entity.getCom(), SysUtils.getUserName(), getExepressType(Optional.ofNullable(entity2).orElseGet(WuLiuWuliuwangdianEntity::new).getExepresstype()));
    }

    @Override
    public R<Boolean> sendDIYMsg(String ids, String msg, String userIds, String userName) {
        R<Boolean> result = new R<>();
        try {
            List<Integer> upids = Arrays.stream(ids.split(StrPool.COMMA)).map(Integer::parseInt).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(upids)) {
                List<String> userNames = Arrays.asList(userIds.split(StrPool.COMMA));
                List<Integer> ch999userId = ch999UserService.getUserIdByUserName(userIds);
                if (CollectionUtils.isEmpty(ch999userId)) {
                    log.error("推送接受人员选择错误,ch999userId长度为0");
                    throw new CustomizeException("推送接受人员选择错误");
                }
                // 物流单号分组
                List<List<Integer>> listGroup = new ArrayList<>();
                int j = 20;
                for (int i = 0; i < upids.size(); i += 20) {
                    List<Integer> cList = new ArrayList<>();
                    cList = upids.subList(i, j);
                    j += 20;
                    listGroup.add(cList);
                }

                for (List<Integer> danhaolist : listGroup) {
                    for (Integer id : ch999userId) {
                        StringJoiner stringJoiner = new StringJoiner("、");
                        danhaolist.forEach(x -> stringJoiner.add("<a href='" + SysConfigUtils.getMoaUrl() + "/mWuLiu/wuliuInfo?wuliuid=" + x + "'>"));
                        String newmsg = msg + "，物流单号：" + stringJoiner;
                        weixinAndOaMessageSend(newmsg, 3, "", id, MessagePushUtils.MessagePushOaMessageTypeConstant.LOGISTICS, null);
                    }
                }
            } else {
                R.error("传入物流单号错误。");
            }

            return result;
        } catch (Exception e) {
            log.error("自定义推送异常:{}", e.getMessage(), e);
            return R.error("自定义推送错误!");
        }
    }

    /**
     * subWLService.wuliuPLZuoFei
     * 物流单批量作废
     *
     * @param idList
     * @return R<String>
     * <AUTHOR>
     * @date 2021-12-17
     */
    @Override
    public R<String> invalidBatch(String idList) {
        if(StringUtils.isNotBlank(idList)) {
            return R.error("传入物流单号错误.");
        }
        List<Integer> ids = Arrays.stream(idList.split(",")).filter(StringUtils::isNotBlank).map(Integer::parseInt).collect(Collectors.toList());
        if (ids.size() <= 0) {
            return R.error("传入物流单号错误.");
        }

        List<WuLiuEntity> list = Optional.ofNullable(lambdaQuery()
                .select(WuLiuEntity::getId, WuLiuEntity::getCom, WuLiuEntity::getNu)
                .notIn(WuLiuEntity::getStats, Arrays.asList(4, 6, 50))
                .in(WuLiuEntity::getId, ids).list())
                .orElseGet(ArrayList::new);
        String userName = currentRequestComponent.getCurrentStaffId().getUserName();

        this.lambdaUpdate().set(WuLiuEntity::getStats, 5)
                .notIn(WuLiuEntity::getStats, Arrays.asList(4,6,50))
                .in(WuLiuEntity::getId, ids)
                .update();

        List<WuLiuLogEntity> wuliuLogList = new ArrayList<>();
        ids.forEach(p -> {
            WuLiuLogEntity wuliuLogs = new WuLiuLogEntity()
                    .setInuser(userName)
                    .setMsg("作废操作")
                    .setDtime(LocalDateTime.now())
                    .setWuliuid(p);
            wuliuLogList.add(wuliuLogs);
        });
        wuLiuLogService.saveBatch(wuliuLogList);

        list.forEach(p -> {
            if (WuLiuConstant.SHUNFENG.equals(p.getCom()) && StringUtils.isNotBlank(p.getNu())) {
                if (Boolean.TRUE.equals(cancelSfOrder(p.getNu(), null))) {
                    writewuliulogs(p.getId(), userName, "取消顺丰运单" + p.getNu(), null);
                }
            }

            if (WuLiuConstant.SHANSONG.equals(p.getCom()) && StringUtils.isNotBlank(p.getNu())) {
                CancelResponseDataVO res = shansongCancelOrder(p.getNu());
                if (res != null) {
                    writewuliulogs(p.getId(), userName, "取消闪送运单" + p.getNu(), null);
                } else {
                    writewuliulogs(p.getId(), userName, "取消闪送运单失败" + res.toString() + p.getNu(), null);
                }
            }
        });
        return R.success("操作成功");
    }

    /**
     * addOrderController.GenerateMoreWuliuNo
     * 批量生成中通快递单号(子母件)
     *
     * @param wuLiu
     * @param eCount
     * @return R<String>
     * <AUTHOR> @date 2021-12-16
     */
    @Override
    public R<String> generateMoreWuLiuNo(WuLiuDTO wuLiu, Integer eCount) {
        eCount = Optional.ofNullable(eCount).orElse(0);
        AreaSubjectVO areaSubject = getAreaSubject(wuLiu.getSAreaId());
        Areainfo areaInfo = areaInfoService.getAreaInfoByAreaId2(wuLiu.getSAreaId());

        if (WuLiuConstant.ZHONGTONG.equals(wuLiu.getCom()) && !StringUtils.isBlank(wuLiu.getNu())) {
            if (AreaInfoUtils.isCurrentDc(wuLiu.getSAreaId())) {
                wuLiu.setSName(areaSubject.getPrintName());
            }

            if (StringUtils.isBlank(wuLiu.getSMobile()) && NumberConstant.FOUR.equals(wuLiu.getWuType())) {
                wuLiu.setSName(areaSubject.getPrintName())
                        .setSMobile(areaInfo.getCompanyTel1());
            }

            OrderGroupDTO orderGroupDTO = new OrderGroupDTO();
            // 订单号
            orderGroupDTO.setId(wuLiu.getDanHaoBind() + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));

            if (NumberConstant.ONE.equals(wuLiu.getWuType())) {
                orderGroupDTO.setId(wuLiu.getId() + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
            }

            // 订单备注
            orderGroupDTO.setRemark("请勿摔打,保持整洁。");

            // 寄件人信息
            CityIdListDTO cityIdListDTO = new CityIdListDTO();

            // 上门取件 其他派送
            if (NumberConstant.SEVEN.equals(wuLiu.getWuType()) || NumberConstant.EIGHT.equals(wuLiu.getWuType())) {
                cityIdListDTO = getAreaIdByCityId(wuLiu.getSCityId(), null);
                cityIdListDTO.setAddress(wuLiu.getSAddress());

            } else {
                cityIdListDTO = getAreaInfoByArea(wuLiu.getSAreaId());
            }

            // 发货人姓名
            orderGroupDTO.getSender().setName(wuLiu.getSName());
            orderGroupDTO.getSender().setCompany(areaSubject.getPrintName());
            orderGroupDTO.getSender().setMobile(wuLiu.getSMobile());
            // 发货人所在城市
            orderGroupDTO.getSender().setCity(cityIdListDTO.getPname() + "," + cityIdListDTO.getZname() + "," + cityIdListDTO.getDname());
            orderGroupDTO.getSender().setAddress(cityIdListDTO.getAddress());

            // 收件人信息
            CityIdListDTO cityIdListDTO1 = new CityIdListDTO();

            if (NumberConstant.ONE.equals(wuLiu.getWuType())) {
                cityIdListDTO1 = getAreaDetailInfoByArea(wuLiu.getRAreaId());
            } else {
                cityIdListDTO1 = getAreaDetailIdByCityId(wuLiu.getRAreaId() == 0 ? wuLiu.getRCityId() : wuLiu.getRCityId());
            }

            // 收件人
            orderGroupDTO.getReceiver().setName(wuLiu.getRName());
            orderGroupDTO.getReceiver().setMobile(wuLiu.getRMobile());
            orderGroupDTO.getReceiver().setCity(cityIdListDTO1.getPname() + "," + cityIdListDTO1.getZname() + "," + cityIdListDTO1.getDname());
            orderGroupDTO.getReceiver().setAddress(wuLiu.getRAddress());

            if (NumberConstant.ONE.equals(wuLiu.getWuType()) && StringUtils.isBlank(wuLiu.getRAddress())) {
                orderGroupDTO.getReceiver().setAddress(cityIdListDTO1.getAddress());
            }

            // 商品信息
            // 根据订单获取 basket
            List<WuLiuRecoverMarketInfo2Entity> subBasket = wuLiuRecoverMarketInfoService.getSubBasket(wuLiu.getDanHaoBind(), null);
            BigDecimal totalCount = new BigDecimal(0);

            for (WuLiuRecoverMarketInfo2Entity item : subBasket) {
                totalCount = item.getPrice().multiply((new BigDecimal(item.getBasketCount()))).add(totalCount);
            }

            // 订单总金额
            orderGroupDTO.setOrderSum(totalCount);
        } else if (WuLiuConstant.SHUNFENG.equals(wuLiu.getCom()) && !StringUtils.isBlank(wuLiu.getNu())) {
            return generateMoreNuSf(wuLiu, eCount);
        } else if (WuLiuConstant.JINGDONG_JIUJI.equals(wuLiu.getCom()) && !StringUtils.isBlank(wuLiu.getNu())) {
            return generateMoreNuJD(wuLiu, eCount);
        }

        return R.success("生成成功");
    }

    /**
     * subWLService.doPrint
     *
     * @param printId
     * @param type
     * @param clientNo
     * @param printCount
     * @return R<String>
     * <AUTHOR> @date 2021-12-15
     */
    @Override
    public R<String> doPrint(String printId, Integer type, String clientNo, Integer printCount) {
        clientNo = Optional.ofNullable(clientNo).orElse(StringUtils.EMPTY);
        printCount = Optional.ofNullable(printCount).orElse(1);

        OaUserBO currentStaffId = currentRequestComponent.getCurrentStaffId();
        return doPrint(printId, type, currentStaffId.getArea(), currentStaffId.getUserId(), currentStaffId.getUserName(), clientNo, printCount);
    }

    /**
     * subWLService.doPrint
     *
     * @param printId
     * @param type
     * @param area
     * @param userid
     * @param userName
     * @param clientNo
     * @param printCount
     * @return R<String>
     * <AUTHOR> @date 2021-12-15
     */
    private R<String> doPrint(String printId, Integer type, String area, Integer userid, String userName, String clientNo, Integer printCount) {
        clientNo = Optional.ofNullable(clientNo).orElse(StringUtils.EMPTY);
        printCount = Optional.ofNullable(printCount).orElse(1);

        Boolean successPushToRabbitMq = true;

        try {
            String vHost = "oa";
            String rabbitMqAddress = sysConfigService.getValueByCode2(31);
            List<String> addressItems = Arrays.asList(rabbitMqAddress.split("/"));

            if (Objects.nonNull(addressItems) && addressItems.size() == 2) {
                vHost = addressItems.get(1);
            }

            String message = String.format("{%s},{%s},{%s},{%s},{%s},{%s},{%s},{%s}", area, type, printId, userid,
                    userName, clientNo, LocalDateTime.now().toString(), printCount);

            rabbitTemplate.convertAndSend("PrinterExchange", message, area + "-" + clientNo);

        } catch (Exception e) {
            successPushToRabbitMq = false;
            log.error("发送RabbitMq打印消息遇到异常{}{}", e.getMessage(), e.getStackTrace());
        }

        if (successPushToRabbitMq) {
            // 打印记录到订单日志
            String comment = StringUtils.EMPTY;
            switch (type) {
                case 1:
                    comment = "销售明细单打印";
                    break;
                case 2:
                    comment = "定金单打印";
                    break;
                case 3:
                    comment = "配件单打印";
                    break;
                case 4:
                    comment = "保修单打印";
                    break;
                case 14:
                    WuLiuEntity wuLiu = this.lambdaQuery().eq(WuLiuEntity::getId, printId).one();
                    if (!Objects.isNull(wuLiu)) {
                        comment = "物流单打印";
                    }
                    break;
                case 15: {
                    Integer printedCount = this.baseMapper.getPrintCount(Integer.valueOf(printId), 29);
                    comment = String.format("打印销售小票，第%d次打印", printedCount + 1);
                    break;
                }
                case 10: {
                    Integer printedCount = this.baseMapper.getPrintCount(Integer.valueOf(printId), 10);
                    comment = String.format("打印订金单，第%d次打印", printedCount + 1);
                    break;
                }
                case 22: {
                    //打印xx小票，第x次打印 【操作人】2020-2-20 12:10:56
                    String[] split = printId.split("|");
                    Integer printedCount = this.baseMapper.getPrintCount(Integer.valueOf(split[1]), 22);
                    comment = String.format("打印良品质检测单，第%d次打印", printedCount + 1);
                    break;
                }
                case 9: {
                    Integer printedCount = this.baseMapper.getPrintCount(Integer.valueOf(printId), 9);
                    comment = String.format("打印销售明细单，第%d次打印", printedCount + 1);
                    break;
                }
                case 33:
                case 20: {
                    Integer printedCount = this.baseMapper.getPrintCount(Integer.valueOf(printId), 33);
                    comment = String.format("打印物流小票，第%d次打印", printedCount + 1);
                    break;
                }
                case 34: {
                    Integer printedCount = this.baseMapper.getPrintCount(Integer.valueOf(printId), 34);
                    comment = String.format("打印售后接件小票，第%d次打印", printedCount + 1);
                    break;
                }
                case 7: {
                    Integer printedCount = this.baseMapper.getPrintCount(Integer.valueOf(printId), 7);
                    comment = String.format("打印售后回执小票，第%d次打印", printedCount + 1);
                    break;
                }
                case 26:
                    comment = "备用机标签";
                    break;
                case 28:
                    comment = "备用机押金";
                    break;
            }
            if (StringUtils.isNotBlank(comment)) {
                if (type == 15) {
                    //  销售验机小票
                    SubLogVO log = new SubLogVO()
                            .setComment(comment)
                            .setShowType(false)
                            .setSubId(Integer.parseInt(printId))
                            .setInUser(userName)
                            .setType(1)
                            .setDTime(LocalDateTime.now());

                    // 订单
                    opLog(log);
                } else if (type == 10 || type == 9 || type == 22) {
                    SubLog log = new SubLog()
                            .setComment(comment)
                            .setShowType(false)
                            .setSubId(Long.valueOf(printId))
                            .setInUser(userName)
                            .setType(1)
                            .setDTime(LocalDateTime.now());
                    //良品
                    backendOptionRecoverLog(log, true);
                } else if (type == 33 || type == 20) {
                    //物流单
                    writewuliulogs(Integer.parseInt(printId), userName, comment, null);
                } else if (type == 34 || type == 7) {
                    //34售后接件单，7 售后回执单
                    Map<String, Object> map = new HashMap<>(5);
                    map.put("shouhou_id", printId);
                    map.put("comment", comment);
                    map.put("inuser", userName);
                    map.put("isweb", false);
                    map.put("dtime", LocalDateTime.now());

                    // 存到mongdb
                    Update update = new Update();
                    update.push("conts", map);
                    mongoTemplate.upsert(new Query(Criteria.where("_id").is(printId)), update, "shouhouLogNew");
                }
            }
        }
        return R.success("通知成功");
    }

    /**
     * addOrderController.GenerateMoreNuSf
     *
     * @param model
     * @param eCount
     * @return R<String>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-15
     */
    public R<String> generateMoreNuSf(WuLiuDTO model, Integer eCount) {
        Integer eCount2 = Optional.ofNullable(eCount).orElse(0);

        SfOrderInfoVO sfOrderInfoVO = new SfOrderInfoVO();
        AreaSubjectVO areaSubject = getAreaSubject(model.getSAreaId());
        Areainfo areainfo = areaInfoService.getAreaInfoByAreaId2(model.getSAreaId());

        if (WuLiuConstant.SHUNFENG.equals(model.getCom()) && !StringUtils.isBlank(model.getNu())) {

           // 特殊业务处理，特殊地区，寄件人强制 开始

            //如果是DC，并且是订单派送
            if (Objects.equals(model.getSAreaId(), 16))
            {
                model.setSName(areaSubject.getPrintName());
                model.setSMobile(WuLiuConstant.DC_S_MOBLIE);
            }

            //如果是H1
            if (Objects.equals(model.getSAreaId(), 13)) {
                model.setSName(areaSubject.getPrintName());
                model.setSMobile("18860789139"); //易涛
            }

            //如果是dc1
            if (Objects.equals(model.getSAreaId(), 113)) {
                model.setSName(areaSubject.getPrintName());
                model.setSMobile("18508501195"); //陶彪
            }

            //如果是dc2
            if (Objects.equals(model.getSAreaId(), 142)) {
                model.setSName(areaSubject.getPrintName());
                model.setSMobile("18613223881"); //缪青
            }
            //sz
            if (Objects.equals(model.getSAreaId(), 14)) {
                model.setSName(areaSubject.getPrintName());
                model.setSMobile("13760162987");
            }

            if (StringUtils.isBlank(model.getSMobile()) && Objects.equals(model.getWuType(), 4))
            {
                model.setSName(areaSubject.getPrintName());
                model.setSMobile(areainfo.getCompanyTel1());
            }

            // 特殊业务处理，特殊地区，寄件人强制 结束

            // 订单基本信息
            sfOrderInfoVO.setOrderId(model.getDanHaoBind() + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));

            if (NumberConstant.ONE.equals(model.getWuType())) {
                sfOrderInfoVO.setOrderId(model.getId() + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
            }

            sfOrderInfoVO.setParcelQuantity(1)
                    .setPayMethod(3)
                    .setExpressType(model.getExpressType());

            if (AreaInfoUtils.isCurrentDc(model.getSAreaId())) {
                sfOrderInfoVO.setPayMethod(1);
            }

            sfOrderInfoVO.setRemark("请勿摔打,保持整洁");
            sfOrderInfoVO.setSendStartTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            sfOrderInfoVO.setCustId(getYueJieKaHao(model.getSAreaId(), null).getCustId());

            // 寄件人信息
            sfOrderInfoVO.setJCompany(areaSubject.getPrintName())
                    .setJContact(model.getSName())
                    .setJMobile(model.getSMobile());

            CityIdListDTO cityIdListDTO;

            // 上门取件 其他派送
            if (NumberConstant.SEVEN.equals(model.getWuType()) || NumberConstant.EIGHT.equals(model.getWuType())) {
                cityIdListDTO = getAreaIdByCityId(model.getSCityId(), null);
                cityIdListDTO.setAddress(model.getSAddress());

            } else {
                cityIdListDTO = getAreaInfoByArea(model.getSAreaId());
            }

            sfOrderInfoVO.setJCounty(cityIdListDTO.getDname())
                    .setJCity(cityIdListDTO.getZname())
                    .setJProvince(cityIdListDTO.getPname())
                    .setJAddress(cityIdListDTO.getAddress())
                    .setDContact(model.getRName());

            CityIdListDTO rCityIdList;
            if (NumberConstant.ONE.equals(model.getWuType())) {
                rCityIdList = getAreaInfoByArea(model.getRAreaId());
            } else {
                rCityIdList = getAreaIdByCityId(model.getRCityId(), 1);
            }

            sfOrderInfoVO.setDCounty(rCityIdList.getDname())
                    .setDCity(rCityIdList.getZname())
                    .setDProvince(rCityIdList.getPname())
                    .setDMobile(model.getRMobile())
                    .setDAddress(model.getRAddress());

            if (NumberConstant.ONE.equals(model.getWuType()) && StringUtils.isBlank(model.getRAddress())) {
                sfOrderInfoVO.setDAddress(rCityIdList.getAddress());
            }
            sfOrderInfoVO.setParcelQuantity(eCount2);

            // 货品信息
            sfOrderInfoVO.setList(new ArrayList<>());

            // 根据订单获取 basket
            List<WuLiuRecoverMarketInfo2Entity> subBasket = wuLiuRecoverMarketInfoService.getSubBasket(model.getDanHaoBind(), null);
            for (WuLiuRecoverMarketInfo2Entity item : subBasket) {
                SfCargoVO hp = new SfCargoVO();
                hp.setAmount(item.getPrice().toString())
                        .setCargoName(item.getProductName())
                        .setCount(item.getBasketCount().toString())
                        .setUnit(StringUtils.EMPTY);
                sfOrderInfoVO.getList().add(hp);
            }
            String xml = sendToCreateOrder(sfOrderInfoVO, model.getSAreaId());

            try {
                if (StringUtils.isNotBlank(xml) && !xml.contains("远程服务器返回错误")) {
                    Document document = XmlUtils.toDocument(xml);
                    Element root = document.getRootElement();
                    Element head = root.element("Head");
                    if (("OK").equals(head.getTextTrim())) {
                        Element body = root.element("Body");
                        Element xn = body.element("OrderResponse");
                        model.setNu(xn.attributeValue("mailno"));
                        if (StringUtils.isNotBlank(model.getNu())) {
                            List<String> nuList = Arrays.asList(model.getNu().split(","));
                            nuList.forEach(item -> generateMoreWuliuNo(item, model.getId().toString(), WuLiuConstant.SHUNFENG, 1));
                        }
                        return R.success("生成成功");
                    } else {
                        return R.error("生成失败");
                    }
                }
            } catch (Exception e) {
                log.error("生成失败，报错信息：{}" , Exceptions.getStackTraceAsString(e), e);
            }
        }
        return R.error("生成失败");
    }

    /**
     * subWLService.GenerateMoreWuliuNo
     * 中通快递单号生成，一个物流单里面包含多个快递单
     *
     * @param nu
     * @param wuLiuId
     * @param com
     * @param packageCount
     * @return R<String>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-15
     */
    private R<String> generateMoreWuliuNo(String nu, String wuLiuId, String com, Integer packageCount) {
        String com2 = Optional.ofNullable(com).orElse(WuLiuConstant.ZHONGTONG);
        Integer packageCount2 = Optional.ofNullable(packageCount).orElse(1);
        this.getBaseMapper().inserWuLiuNoEx(nu, wuLiuId, com2, packageCount2);
        return R.success("添加成功");
    }

    /**
     * addOrderController.GenerateMoreNuJD
     *
     * @param model
     * @param eCount
     * @return R<String>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-15
     */
    private R<String> generateMoreNuJD(WuLiuDTO model, Integer eCount) {
        Integer eCount2 = Optional.ofNullable(eCount).orElse(0);

        AreaSubjectVO areaSubject = getAreaSubject(model.getSAreaId());
        Areainfo areaInfo = areaInfoService.getAreaInfoByAreaId2(model.getSAreaId());

        if (AreaInfoUtils.isCurrentHq(model.getSAreaId())) {
            model.setSName(areaSubject.getPrintName())
                    .setSMobile(WuLiuConstant.DC_S_MOBLIE);
        }

        if (StringUtils.isBlank(model.getSMobile()) && NumberConstant.FOUR.equals(model.getWuType())) {
            model.setSName(areaSubject.getPrintName())
                    .setSMobile(areaInfo.getCompanyTel1());
        }

        // 寄件人信息
        CityIdListDTO cityIdListDTO;

        // 上门取件 其他派送
        if (NumberConstant.SEVEN.equals(model.getWuType()) || NumberConstant.EIGHT.equals(model.getWuType())) {
            cityIdListDTO = getAreaIdByCityId(model.getSCityId(), null);

        } else {
            cityIdListDTO = getAreaInfoByArea(model.getSAreaId());
        }

        AddressDTO sender = new AddressDTO()
                .setName(model.getSName())
                .setMobile(model.getSMobile())
                .setProv(cityIdListDTO.getPname())
                .setCity(cityIdListDTO.getZname())
                .setCounty(cityIdListDTO.getDname())
                .setAddress(cityIdListDTO.getAddress());

        if (StringUtils.isBlank(sender.getAddress())) {
            sender.setAddress(model.getSAddress());
        }

        // 收件人信息
        CityIdListDTO cityIdListDTO1;

        if (NumberConstant.ONE.equals(model.getWuType())) {
            cityIdListDTO1 = getAreaDetailInfoByArea(model.getRAreaId());
        } else {
            cityIdListDTO1 = getAreaDetailIdByCityId(model.getRAreaId() == 0 ? model.getRCityId() : model.getRCityId());
        }

        if (NumberConstant.ONE.equals(model.getWuType())) {
            model.setRAddress(cityIdListDTO1.getAddress());
        }

        AddressDTO receiver = new AddressDTO()
                .setName(model.getRName())
                .setMobile(model.getRMobile())
                .setProv(cityIdListDTO1.getPname())
                .setCity(cityIdListDTO1.getZname())
                .setCounty(cityIdListDTO1.getDname())
                .setAddress(model.getRAddress());

        String orderId;

        try {
            if (eCount2 > 0) {
                SaasPlatformDTO saasPlatformDTO = new SaasPlatformDTO();
                if (WuLiuConstant.JINGDONG_JIUJI.equals(model.getCom())) {
                    saasPlatformDTO.setSaasAreaid(model.getSAreaId())
                            .setSaasTenant(areaSubject.getXtenant())
                            .setPackageCount(eCount2);
                }

                orderId = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyMMddHHmmssfff-")) + getNumCode(4);

                String order = orderId;
                Ch99ResultDataDTO<JdOrderResponseDTO> result = jdCreateNo(sender, receiver, StringUtils.EMPTY, saasPlatformDTO, order, eCount2);
                if (NumberConstant.ONE.equals(result.getStats())) {
                    model.setNu(result.getData().getWaybillCode());
                    generateMoreWuliuNo(model.getNu(), model.getId().toString(), WuLiuConstant.JINGDONG_JIUJI, eCount2);
                    return R.success(String.format("京东运单号生成成功：%s 包裹数:%d", model.getNu(), eCount2));
                } else {
                    return R.error("京东生成数量为0");
                }
            }
        } catch (Exception e) {
            log.error("京东运单号生成错误：{}{}", Exceptions.getStackTraceAsString(e), e);
        }
        return R.success(StringUtils.EMPTY);
    }

    @Override
    public R<Boolean> sendMsg(String ids, String type, String userName) {
        R<Boolean> result = new R<>();
        try {
            List<String> upids = Arrays.asList(ids.split(StrPool.COMMA));
            if (CollectionUtils.isNotEmpty(upids)) {
                // 收件人推送
                if (Objects.equals(type, "rname")) {
                    List<WuLiuSendMsgRes> wuliu = this.baseMapper.getReceiveNameList(upids);
                    for (WuLiuSendMsgRes item : wuliu) {
                        // 物流单号分组
                        String wuIds = item.getWuIds();
                        List<String> danhao = Arrays.asList(wuIds.split(StrPool.COMMA));
                        List<List<String>> listGroup = new ArrayList<>();
                        // 每组20个
                        int j = 20;
                        for (int i = 0; i < danhao.size(); i += 20) {
                            List<String> cList = new ArrayList<>();
                            cList = cList.subList(i, j);
                            j += 20;
                            listGroup.add(cList);
                        }
                        for (List<String> danhaolist : listGroup) {
                            String msg = "你好，物流单(单号:%s)已送达，收货已超时，请及时签收入库！";
                            StringJoiner sj = new StringJoiner("、");
                            danhaolist.forEach(x -> sj.add("<a href='" + SysConfigUtils.getMoaUrl() + "/mWuLiu/wuliuInfo?wuliuid=" + x + "'>" + x + "</a>"));
                            msg = String.format(msg, sj);
                            weixinAndOaMessageSend(msg, 3, "", item.getUserId(), null, null);
                        }
                        List<String> wuliuid = Arrays.asList(wuIds.split(StrPool.COMMA));
                        for (String w : wuliuid) {
                            if (StringUtils.isBlank(w)) {
                                continue;
                            }
                            String msg = DateUtil.now() + "    已推送收货提醒";
                            writewuliulogs(Integer.parseInt(w), userName, msg, null);
                        }
                    }
                }
                // 提交人推送
                if (Objects.equals(type, "inuser")) {
                    List<WuLiuSendMsgRes> wuliu = this.baseMapper.getInuserNameList(upids);
                    for (WuLiuSendMsgRes item : wuliu) {
                        String wuIds = item.getWuIds();
                        //物流单号分组
                        List<String> danhao = Arrays.asList(wuIds.split(StrPool.COMMA));
                        List<List<String>> listGroup = new ArrayList<>();
                        int j = 20;
                        for (int i = 0; i < danhao.size(); i += 20) {
                            List<String> cList = new ArrayList<>();
                            cList = danhao.subList(i, j);
                            j += 20;
                            listGroup.add(cList);
                        }

                        for (List<String> danhaolist : listGroup) {
                            String msg = "你好，请核实物流单(单号:%s)是否已发货，当前发货已超时！";
                            StringJoiner sj = new StringJoiner("、");
                            danhaolist.forEach(x -> sj.add("<a href='" + SysConfigUtils.getMoaUrl() + "/mWuLiu/wuliuInfo?wuliuid=" + x + "'>" + x + "</a>"));
                            msg = String.format(msg, sj);
                            weixinAndOaMessageSend(msg, 3, "", item.getUserId(), null, null);
                        }
                        List<String> wuliuid = Arrays.asList(wuIds.split(StrPool.COMMA));
                        for (String w : wuliuid) {
                            if (StringUtils.isBlank(w)) {
                                continue;
                            }
                            String msg = DateUtil.now() + "    已推送收货提醒";
                            writewuliulogs(Integer.parseInt(w), userName, msg, null);
                        }
                    }
                } else {
                    return R.error("传入物流单号错误。");
                }
            }
        } catch (Exception e) {
            log.info("消息推送、发货提醒异常:{}", e.getMessage(), e);
            return R.error("消息推送、发货提醒异常");
        }
        return result;
    }

    /**
     * SaasWuliuService.VerifySaasPlatform
     * 验证SAAS物流配件信息
     *
     * @param saasPlatform SaasPlatformDTO
     * @return boolean
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-03
     */
    public boolean verifySaasPlatform(SaasPlatformDTO saasPlatform) {
        if (saasPlatform == null || saasPlatform.getSaasTenant() == 0) {
            return false;
        }
        return outPutWebService.getOutPutWebByXtenant(saasPlatform.getSaasTenant()) != null;
    }

    /**
     * rentServices.SetWuliuComplete
     * C# SetWuliuComplete
     * 设置租机物流单完成
     *
     * @param wuliuId
     * @param sub_id
     */
    public void setWuliuComplete(int wuliuId, int sub_id) {
        RentWuliuBO result = new RentWuliuBO(sub_id, wuliuId);
        sendRabbitMQExchangeMessage(JacksonJsonUtils.toJson(result), "oaTopic", "rentWuliuComplete", "topic", 2, true);
    }

    /**
     * 异步队列使用
     * 给 C# SendRabbitMQExchangeMessage
     *
     * @param message
     * @date 2021-10-20
     * <AUTHOR> [<EMAIL>]
     */
    public void sendRabbitMQExchangeMessage(String message, String exchange, String routingkey, String type, Integer mode, Boolean durable) {
        log.info("给 C# 发送 RabbitMQ 队列消息入参: {}", message);
        try {
            rabbitTemplate.convertAndSend(exchange, routingkey, message);
        } catch (AmqpException e) {
            log.error("给 C# 发送 RabbitMQ 队列消息报错: message: {}，报错：{}, , e: {}", message, Exceptions.getStackTraceAsString(e) , e);
        }
    }

    /**
     * subWLService.kuaidiapi
     *
     * @param id 物流单 ID
     * @return R<String>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-21
     */
    @Override
    public R<String> traceV1(Integer id) {
        WuLiuEntity entity = getById(id);
        if(entity == null) {
            return R.error("物流轨迹查询失败，该物流单不存在");
        }
        WuLiuAddOrUpdateReqVO model = new WuLiuAddOrUpdateReqVO();
         model.setWuliuid(id);
        model.setCom(entity.getCom());
        model.setNu(entity.getNu());
        model.setJiujiJdExpressType(Optional.ofNullable(wuLiuWuliuwangdianService.getExpressType(id)).orElse(""));

        return R.success(kuaidiapi(model));
    }

    /**
     * subWLService.kuaidiapi
     * 快递API(获取快递日志)
     *
     * @param model WuLiuAddOrUpdateReqVO
     * @return string
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-10
     */
    public String kuaidiapi(WuLiuAddOrUpdateReqVO model) {
        //CH99Result json = new CH99Result();
        String json = "{";//参数比较多，所以还是用拼接字符串的方式
        try {
            String SQL = "";
            if (Objects.equals(model.getNu(), "") || Objects.equals(model.getCom(), "")) {
                WuLiuEntity dr_rst = getById(model.getWuliuid());
                if (dr_rst != null) {

                    json += "\"stats\":\"1\",";
                    json += "\"statsname\":\"" + getwuliuStatsName(dr_rst.getStats()) + "\"";
                    model.setCom(dr_rst.getCom());
                    model.setNu(dr_rst.getNu());

                    List<WuLiuLogEntity> Rows = wuLiuLogService.lambdaQuery()
                            .select(WuLiuLogEntity::getDtime, WuLiuLogEntity::getMsg, WuLiuLogEntity::getInuser)
                            .eq(WuLiuLogEntity::getWuliuid, model.getWuliuid()).orderByAsc(WuLiuLogEntity::getId).list();

                    StringBuffer dataStr = new StringBuffer();
                    StringBuffer finalDataStr = dataStr;
                    Rows.forEach(dr1 -> {
                        finalDataStr.append("{\"dtime\":\"")
                                .append(dr1.getDtime())
                                .append("\",")
                                .append("\"msg\":\"")
                                .append(dr1.getMsg())
                                .append("\",")
                                .append("\"inuser\":\"")
                                .append(dr1.getInuser())
                                .append("\"},");
                    });
                    dataStr = finalDataStr;
                    dataStr = dataStr.deleteCharAt(dataStr.lastIndexOf(","));
                    dataStr.insert(0, "\"data\":[");
                    json += dataStr.append("]");
                } else {
                    json += "\"stats\":0,";
                    json += "\"msg\":\"无记录\"";
                }
            }
            if (!Objects.equals(model.getNu(), "") || !Objects.equals(model.getCom(), "")) {
                if (Objects.equals(model.getCom(), "zengyi")) {
                    Map<String, String> ht = new HashMap<>();
                    ht.put("postid", model.getNu());

                    String disanfang = HttpRequest.get("http://www.kuaidi100.com/query?type=zengyisudi").headerMap(ht, true).execute().body();

                    json += "\"stats\":1,";
                    json += "\"disanfang\":" + disanfang;
                } else if (StringUtils.isNotBlank(model.getNu()) && Arrays.asList("zhongtong", "shunfeng", "meituan",
                        "meituanFastest", "jingdong", "jingdong-jiuji", "meituan_jiuji", "shunfeng_jiuji").contains(model.getNu())) {
                    TraceListVO list = new TraceListVO();
                    list.setTraces(new ArrayList<>());
                    try {
                        if (Arrays.asList("zhongtong", "shunfeng", "jingdong", "jingdong-jiuji", "shunfeng_jiuji").contains(model.getCom())) {
                            switch (model.getCom()) {
                                case "zhongtong":
                                    list = zhongtongOrderTrace(model.getNu());
                                    break;
                                case "shunfeng":
                                    list = shunfengSearchRouteService(model.getNu(), null);
                                    break;
                                case "shunfeng_jiuji":
                                    if (Optional.ofNullable(model.getWuliuid()).orElse(0) > 0) {
                                        list = shunfengSearchRouteService(model.getNu(), String.valueOf(model.getWuliuid()));
                                    }
                                    break;
                                case "jingdong":
                                    list = jdQueryTrace(model.getNu(), model.getWuliuid(), null);
                                    break;
                                case "jingdong-jiuji":
                                    TraceListVO queryList = new TraceListVO();
                                    queryList.setTraces(new ArrayList<>());
                                    List<TraceModelVO> traces = new ArrayList<>();
                                    R<LogisticsQueryTraceResultVO> queryResult = JdLogisticQueryTrace(model.getWuliuid(), model.getNu(), model.getJiujiJdExpressType());
                                    if (queryResult != null && Objects.equals(queryResult.getCode(), 0) && queryResult.getData() != null && !queryResult.getData().getQueryTraceContents().isEmpty()) {
                                        List<LogisticsQueryTraceResultVO.QueryTraceContentVO> queryTraceContents = queryResult.getData().getQueryTraceContents();
                                        for (LogisticsQueryTraceResultVO.QueryTraceContentVO item : queryTraceContents) {
                                            TraceModelVO traceRoute = new TraceModelVO();
                                            traceRoute.setRemark(item.getContent());
                                            traceRoute.setAcceptTime(item.getTime());
                                            traces.add(traceRoute);
                                        }
                                        queryList.setTraces(traces);
                                    }
                                    list = queryList;
                                    break;
                            }

                            StringBuffer data = new StringBuffer("");
                            String updatetime = "";

                            List<TraceModelVO> traces = list.getTraces();
                            if (!traces.isEmpty()) {
                                for (int i = 0; i < traces.size(); i++) {
                                    data.append("{\"content\":\"")
                                            .append(traces.get(i).getRemark().replace("\"", "'"))
                                            .append("\",\"time\":\"")
                                            .append(traces.get(i).getAcceptTime())
                                            .append("\"},");
                                    if (i == (traces.size() - 1)) {
                                        updatetime = traces.get(i).getAcceptTime();
                                    }
                                }
                                data = data.deleteCharAt(data.lastIndexOf(","));
                            }

                            String name = "";
                            switch (model.getCom()) {
                                case "zhongtong":
                                    name = "中通快递";
                                    break;
                                case "shunfeng":
                                    name = "顺丰";
                                    break;
                                case "shunfeng_jiuji":
                                    name = "顺丰(九机特惠)";
                                    break;
                                case "jingdong":
                                    name = "京东物流";
                                    break;
                            }

                            json += "\"stats\":1,";
                            json += "\"disanfang\":{\"errCode\":\"1\",\"name\":\"" + name
                                    + "\",\"order\":\"" + model.getNu()
                                    + "\",\"updatetime\":\"" + updatetime + "\",\"data\":[" + data + "]}";
                        } else if (Arrays.asList("meituan", "meituanFastest", "meituan_jiuji").contains(model.getCom())) {
                            //wuliuCompan = "美团";
                            WuLiuEntity wldt = null;
                            if (StringUtils.isNotBlank(model.getNu())) {
                                wldt = getOne(Wrappers.<WuLiuEntity>lambdaQuery().select(WuLiuEntity::getId, WuLiuEntity::getSAreaId).eq(WuLiuEntity::getNu, model.getNu()).orderByDesc(WuLiuEntity::getId).last("offset 0 row fetch next 2 row only"), false);
                            }
                            if (wldt != null) {

                                QueryItemVO conn = new QueryItemVO();
                                conn.setMtPeisongId(model.getNu());
                                conn.setDeliveryId(Long.valueOf(model.getWuliuid()));
                                AreaSubjectVO areaSubject = getAreaSubject(wldt.getSAreaId());
                                SaasPlatformDTO saasPlatform = null;
                                if (Objects.equals(model.getCom(), "meituan_jiuji")) {
                                    saasPlatform = new SaasPlatformDTO();
                                    saasPlatform.setSaasAreaid(wldt.getSAreaId());
                                    saasPlatform.setSaasTenant(areaSubject.getXtenant());
                                    saasPlatform.setWuliuId(model.getWuliuid());
                                }

                                QueryReturnResultVO result = meituanQueryOrderStatus(conn, saasPlatform);
                                if (Objects.equals(result.getCode(), 0)) {
                                    String data = "";
                                    data += "{\"content\":\"状态：" + result.getStatusName() + "，配送员姓名：" + result.getCourierName() + "，配送员电话：" + result.getCourierPhone() + " \",\"time\":\"\"}";
                                    json += "\"stats\":1,";
                                    json += "\"disanfang\":{\"errCode\":\"1\",\"name\":\"美团\",\"order\":\"" + model.getNu() + "\",\"updatetime\":\"" + result.getOperateTime() + "\",\"data\":[" + data + "]}";
                                } else {
                                    throw new Exception(result.getMessage());
                                }
                            }
                        }
                    } catch (Exception ex) {
                        json += "\"stats\":0,";
                        json += "\"msg\":" + ex.getMessage() + "}";
                    }
                } else if (Objects.equals(model.getCom(), "dada")) {
                    DadaQueryResultVO dadaorder = dadaQueryOrder(model.getWuliuid());
                    if (!Objects.equals(dadaorder.getStatusCode(), 0)) {
                        String updatetime = dadaorder.getCreateTime();
                        switch (dadaorder.getStatusCode()) {
                            case 5:
                                updatetime = dadaorder.getCancelTime();
                                break;
                            case 4:
                                updatetime = dadaorder.getFinishTime();
                                break;
                            case 3:
                                updatetime = dadaorder.getFetchTime();
                                break;
                        }

                        Map<String, Object> disanfang = new HashMap<>();
                        disanfang.put("name", "达达");
                        disanfang.put("order", dadaorder.getOrderId());
                        disanfang.put("updatetime", updatetime);

                        List<Map<String, String>> list = new ArrayList<>();
                        Map<String, String> map1 = new HashMap<>();
                        map1.put("content", "状态：{dadaorder.statusMsg}，配送员姓名：{dadaorder.transporterName}，配送员电话：{dadaorder.transporterPhone}");
                        map1.put("time", "");
                        list.add(map1);
                        disanfang.put("data", list);

                        json += "\"stats\":1,";
                        json += "\"disanfang\":" + disanfang;

                    } else {
                        json += "\"stats\":1,";
                        json += "\"disanfang\":\"\"";
                    }
                } else if (Objects.equals(model.getCom(), "ems")) {
                    json += "\"stats\":1,";
                    json += "\"disanfang\":\"\"";
                } else {
                    Map<String, Object> ht = new HashMap<>();
                    ht.put("order", model.getNu());
                    ht.put("id", model.getCom());
                    ht.put("ord", "asc");
                    ht.put("show", "json");

                    String disanfang = HttpRequest.post("http://www.aikuaidi.cn/rest/?key=bcefe18b9721440eb9d9043b653494f4").form(ht).timeout(5000).execute().body();
                    json += "\"stats\":1,";
                    json += "\"disanfang\":" + disanfang;
                }
            }
        } catch (Exception e) {
            log.error("物流轨迹查询报错：{}", Exceptions.getStackTraceAsString(e), e);
        }
        return json.substring(0, json.length() - 1) + "}";
    }

    /**
     * JdLogisticServices.QueryTrace
     * 京东中台查询快递轨迹
     *
     * @param wuliuId
     * @param expressNumber
     * @param expressType
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-15
     */
    private R<LogisticsQueryTraceResultVO> JdLogisticQueryTrace(Integer wuliuId, String expressNumber, String expressType) {
        String expressType2 = Optional.ofNullable(expressType).orElse("1");

        String logMsg = "接收参数：物流单号：" + wuliuId + ",快递单号：" + expressNumber;
        try {
            JdQueryTraceParamVO queryTraceParam = new JdQueryTraceParamVO();
            queryTraceParam.setExpressType(3);
            queryTraceParam.setExpressNumber(expressNumber);
            queryTraceParam.setXtenantId(SysUtils.getIntXtenantId());
            queryTraceParam.setChildExpressType(Integer.valueOf(expressType2));

            String json = JacksonJsonUtils.toJson(queryTraceParam);
            logMsg += ("快递路由查询数据:" + json);

            String url = SysConfigUtils.getJiujiMoaUrl() + "/cloudapi_nc/logistics/api/logistics-center/query-route-csharp/v1";
            Map<String, String> map = new HashMap<>();
            map.put("xservicename", "logistics-service");
            map.put("logistics", LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            String response = HttpRequest.post(url).body(json).timeout(5000).headerMap(map, true).execute().body();
            logMsg += ("快递路由查询结果:" + response);
            R<LogisticsQueryTraceResultVO> result = new R<>();
            try {
                result = JacksonJsonUtils.toClass(response, new com.fasterxml.jackson.core.type.TypeReference<R<LogisticsQueryTraceResultVO>>() {
                });
            } catch (Exception ex) {
                result.setCode(5000);
                result.setUserMsg("快递官网网络异常");
                result.setMsg(response);
                log.error(logMsg, ex);
                return result;
            }
            if (result != null && Objects.equals(result.getCode(), 0)) {
                logMsg += ("物流单：" + wuliuId + ",快递单号：" + expressNumber + "路由轨查询成功");
            } else {
                logMsg += ("物流单：" + wuliuId + "，快递单号：" + expressNumber + "路由轨查询失败");
            }

            log.error(logMsg);
            return result;
        } catch (Exception ex) {
            logMsg += ("JdQueryTrace异常信息：" + Exceptions.getStackTraceAsString(ex));
            log.error(logMsg);
            return null;
        }
    }

    /**
     * JDApiServices.QueryTrace
     * 京东物流轨迹查询
     *
     * @param waybillId
     * @param wuliuid
     * @param saasPlatform
     * @return TraceListVO
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-15
     */
    private TraceListVO jdQueryTrace(String waybillId, Integer wuliuid, SaasPlatformDTO saasPlatform) {
        Integer wuliuid2 = Optional.ofNullable(wuliuid).orElse(0);
        TraceListVO trace = new TraceListVO();
        List<TraceModelVO> traces = new ArrayList<>();
        JingdongConfig config = saasPlatform == null ? defaultConfig() : saasConfig();
        JdAccessTokenVO token = getAccessToken(config);
        if (token == null) {
            throw new CustomizeException("accessToken获取失败");
        }

        LdopReceiveTraceGetRequest req = new LdopReceiveTraceGetRequest();
        req.setCustomerCode(config.getCustomerCode());
        req.setWaybillCode(waybillId);

        String _appKey = config.getAppKey();
        String _appSecret = config.getAppSecret();
        String access_token = token.getAccess_token();

        if (wuliuid2 > 0) {

            boolean isJdIsvOrder = wuLiuWuliuIsvstoreService.getWuLiuIsvstoreByWuliuId(wuliuid2) != null;
            if (isJdIsvOrder) {
                JdIsvConfigVO isvconfig = getJdIsvConfig();
                req.setCustomerCode(isvconfig.getBdOwnerNo());//京东仓快递单轨迹查询使用的客户编号
            }
        }

        DefaultJdClient client = new DefaultJdClient("https://api.jd.com/routerjson", access_token, _appKey, _appSecret);
        req.setTimestamp(String.valueOf(System.currentTimeMillis()));
        try {
            LdopReceiveTraceGetResponse response = client.execute(req);
            TraceQueryResultDTO traceRes = response.getQuerytraceResult();
            if (traceRes != null && Objects.equals(traceRes.getCode(), 100)) {
                List<TraceDTO> traceData = traceRes.getData();
                Stream<TraceDTO> sorted = traceData.stream().sorted(Comparator.comparing(TraceDTO::getOpeTime));
                sorted.forEach(item ->
                        traces.add(new TraceModelVO()
                                .setRemark(item.getOpeRemark())
                                .setAcceptTime(item.getOpeTime())
                                .setOpcode("妥投".equals(item.getOpeTitle()) ? "80" : "10")));

                trace.setTraces(traces);
            }
        } catch (Exception e) {
            log.error("京东物流轨迹接口请求报错：{}", Exceptions.getStackTraceAsString(e), e);
        }

        return trace;
    }

    /**
     * JDApiServices.getJdIsvConfig
     * 获取京东仓isv下单配置
     *
     * @return JdIsvConfigVO
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-15
     */
    public static JdIsvConfigVO getJdIsvConfig() {
        JdIsvConfigVO cfg = new JdIsvConfigVO();
        cfg.setIsvSource("ISV0020000000068");//ISV来源编号，ECLP参数，联系京东业务人员提供
        cfg.setShopNo("ESP0020000090699");//店铺编号(B2C订单必填，B2B（soType=2）订单非必填)
        cfg.setBdOwnerNo("028K905858"); //青龙业主号,长度不能超50
        cfg.setWarehouseNo("118070585");//库房编号，事业部开启寻源拆分服务可不填；否则必填；
        cfg.setDepartmentNo("EBU4418046661034");// "EBU4418046661034";//事业部编号，且与pin匹配
        cfg.setSalePlatformSource("6");//销售平台来源，参考销售平台来源查询接口的值
        cfg.setShipperNo("CYS0000010");//承运商编号，默认为京东快递，CYS0000010
        //订单标记位，不同标记位代表不同服务，首位为1代表货到付款，如有特殊服务以京东物流对接人提供的为准
        cfg.setOrderMark("00000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000300000000000000000000000000000000000000000000000000000000000000000");
        cfg.setSoType("2");//订单类型（1:B2C订单, 2:B2B订单，不指定默认1）


        cfg.setAppKey("A91AA89B6648E00D59948428CCAA5CDD");
        cfg.setAppSecret("5fc5c02033d649e08675e6bbdbf95e04");
        cfg.setCustomerCode("ECP0020000104642");

        return cfg;
    }

    /**
     * JDApiServices.GetAccessToken
     * 获取存储的Token
     *
     * @param config JingdongConfig
     * @return JdAccessTokenVO
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-15
     */
    public static JdAccessTokenVO getAccessToken(JingdongConfig config) {
        if (config == null) {
            throw new CustomizeException("未配置接口！");
        }

        //物流中台那边将access Token 保存在 redis

        String key = "jingdong:accessToken:" + config.getAppKey();

        //如果未取到值，默认为现在使用中台申请到的accesstoken
        return RedisUtils.get(key, JdAccessTokenVO.class);
    }

    /**
     * JingdongConfig.DefaultConfig
     * 默认九机使用的
     *
     * @return JingdongConfig
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-15
     */
    public static JingdongConfig defaultConfig() {
        return new JingdongConfig()
                .setAppKey("D4A375A39D3010ACD0D6BBCD7F5217B3")
                .setAppSecret("1d77a749ff2948ad85eb1b3bee3db31a")
                .setCustomerCode("028K826598");
    }

    /**
     * JingdongConfig.SaasConfig
     * SAAS共享京东物流配置（九讯技术）
     *
     * @return JingdongConfig
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-15
     */
    public static JingdongConfig saasConfig() {
        return new JingdongConfig().setAppKey("FE953A1030F3CCB5D97691204CB480EB")
                .setAppSecret("bdd7350ad75f4417b3769d9da5a75e65")
                .setCustomerCode("028K963622");
    }

    /**
     * shunfengApiServices.SearchRouteService
     *
     * @param orderStrs
     * @param wuliuId
     * @return TraceListVO
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-14
     */
    private TraceListVO shunfengSearchRouteService(String orderStrs, String wuliuId) {
        String wuliuId2 = Optional.ofNullable(wuliuId).orElse("");
        return shunfengSearchRouteServiceNew(orderStrs, wuliuId2);
    }

    /**
     * shunfengApiServices.SearchRouteServiceNew
     *
     * @param orderid
     * @param wuliuId
     * @return TraceListVO
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-15
     */
    private TraceListVO shunfengSearchRouteServiceNew(String orderid, String wuliuId) {
        TraceListVO list = new TraceListVO();

        if (StringUtils.isBlank(wuliuId)) {
            ShunfengOrderInfoVO orderinfo = GetOrderInfoBy(orderid);
            if (orderinfo != null && Optional.ofNullable(orderinfo.getSareaid()).orElse(0) > 0) {
                String exepressTypeName = baseMapper.getExpressTypeByNu(orderid);
                String exepresstype = getExepressType(exepressTypeName);
                try {
                    list = PostRequestRoute(orderinfo, orderid, orderinfo.getNum1(), exepresstype);
                    if (list.getTraces().size() == 0 && StringUtils.isNotBlank(orderinfo.getNum2())) {
                        list = PostRequestRoute(orderinfo, orderid, orderinfo.getNum2(), exepresstype);
                    }
                } catch (Exception ex) {
                    log.error("顺丰物流订路由查询异常：{}", Exceptions.getStackTraceAsString(ex));
                    sendTextMessage("顺丰物流订路由查询异常：" + ex.getMessage() + ";orderid:" + orderid);
                }
            }
        } else {
            R<LogisticsQueryTraceResultVO> queryResult = SfLogisticsCenterQueryTrace(wuliuId, orderid);
            if (queryResult == null || !Objects.equals(queryResult.getCode(), 0) || queryResult.getData() == null || CollectionUtils.isEmpty(queryResult.getData().getQueryTraceContents())) {
                return list;
            }
            List<LogisticsQueryTraceResultVO.QueryTraceContentVO> queryTraceContents = queryResult.getData().getQueryTraceContents();
            List<TraceModelVO> traces = new ArrayList<>();
            for (LogisticsQueryTraceResultVO.QueryTraceContentVO item : queryTraceContents) {
                TraceModelVO traceRoute = new TraceModelVO();
                traceRoute.setRemark(item.getContent());
                traceRoute.setAcceptTime(item.getTime());
                traces.add(traceRoute);
            }
            list.setTraces(traces);
        }

        return list;
    }

    /**
     * shunfengApiServices.SfLogisticsCenterQueryTrace
     * 顺丰中台查询快递轨迹
     *
     * @param wuliuId
     * @param expressNum
     * @return R<LogisticsQueryTraceResultVO>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-14
     */
    public static R<LogisticsQueryTraceResultVO> SfLogisticsCenterQueryTrace(String wuliuId, String expressNum) {
        String logMsg = "接收参数：物流单号：" + wuliuId + ",快递单号：" + expressNum;
        try {
            SfQueryTraceParamVO sfQueryTraceParamVo = new SfQueryTraceParamVO();

            sfQueryTraceParamVo.setExpressNumber(expressNum);
            sfQueryTraceParamVo.setExpressType("2");

            String url = SysConfigUtils.getJiujiMoaUrl(true) + "/cloudapi_nc/logistics/api/logistics-center/query-route-csharp/v1";
            String json = JacksonJsonUtils.toJson(sfQueryTraceParamVo);
            logMsg += ("顺丰中台快递路由查询数据:" + json);
            Map<String, String> map = new HashMap<>();
            map.put("xservicename", "logistics-service");
            map.put("logistics", LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            String response = HttpRequest.post(url).body(json).headerMap(map, true).timeout(5000).execute().body();
            logMsg += ("顺丰中台快递路由查询结果:" + response);

            R<LogisticsQueryTraceResultVO> result = null;
            try {
                result = JacksonJsonUtils.toClass(response, new com.fasterxml.jackson.core.type.TypeReference<R<LogisticsQueryTraceResultVO>>() {
                });
            } catch (Exception e) {
                result = new R<>();
                result.setCode(500);
                result.setUserMsg("顺丰中台返回结果异常，请稍后再试");
                logMsg += ("顺丰中台返回结果异常:" + response);
                log.error(logMsg, e);
                return result;
            }

            if (result != null && Objects.equals(result.getCode(), 0)) {
                logMsg += ("物流单：" + wuliuId + ",快递单号：" + expressNum + "顺丰物流中台路由轨查询成功");
            } else {
                logMsg += ("物流单：" + wuliuId + ",快递单号：" + expressNum + "顺丰物流中台路由轨查询失败");
            }

            log.info(logMsg);
            return result;
        } catch (Exception e) {
            logMsg += ("SfLogisticsCenterQueryTrace异常信息：" + Exceptions.getStackTraceAsString(e));
            log.error(logMsg, e);
            return null;
        }
    }

    /**
     * shunfengApiServices.PostRequestRoute
     *
     * @param orderinfo
     * @param orderid
     * @param num
     * @param exepresstype
     * @return tracelist
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-14
     */
    private TraceListVO PostRequestRoute(ShunfengOrderInfoVO orderinfo, String orderid, String num, String exepresstype) {
        String num2 = Optional.ofNullable(num).orElse("");
        String exepresstype2 = Optional.ofNullable(exepresstype).orElse("");
        if (StringUtils.isNotBlank(num2)) {
            num2 = " check_phoneNo='" + num2 + "'";
        }
        if (StringUtils.isNotBlank(orderid) && orderid.startsWith("sf"))//处理手填写顺丰单小写sf
        {
            orderid = orderid.replace("sf", "SF");
        }

        ShunFengCardVO card = getYueJieKaHao(orderinfo.getSareaid(), exepresstype2);
        String xml = "<Request service='RouteService' lang='zh-CN'>" +
                "<Head>" + card.getClientCode() + "</Head>" +
                "<Body>" +
                "<RouteRequest tracking_type='1' method_type='1' tracking_number='" + orderid + "'" + num2 + "/>" +//
                "</Body>" +
                "</Request>";

        String verifyCode1 = xml + card.getCheckPwd();
        String verifyCode = shunfengMd5ToBase64String(verifyCode1);//生成verifyCode
        Map<String, Object> postValues = new HashMap<>();
        postValues.put("xml", xml);//报文
        postValues.put("verifyCode", verifyCode); //签名验证码

        //POST地址
        String postUrl = "http://bsp-oisp.sf-express.com/bsp-oisp/sfexpressService";

        String xmlResult = postDate2(postUrl, postValues); //注：返回的数据是XML格式的噢

        return parseRoute(xmlResult);
    }

    /**
     * shunfengApiServices.parseRoute
     *
     * @param xmlResult
     * @return TraceListVO
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-14
     */
    private static TraceListVO parseRoute(String xmlResult) {
        TraceListVO list = new TraceListVO();
        String xmlResult2 = Optional.ofNullable(xmlResult).orElse("");
        if (!xmlResult2.contains("远程服务器返回错误")) {

            Document document = XmlUtils.toDocument(xmlResult2);
            Element root = document.getRootElement();
            Element head = root.element("Head");
            if (("OK").equals(head.getTextTrim())) {
                Element body = root.element("Body");
                Element RouteResponse = body.element("RouteResponse");
                if (RouteResponse != null) {
                    List Routes = Optional.ofNullable(RouteResponse.elements("Route")).orElseGet(ArrayList::new);

                    List<TraceModelVO> traces = new ArrayList<>();
                    TraceModelVO traceModelVo;
                    for (Object route : Routes) {
                        if (route instanceof Element) {
                            traceModelVo = new TraceModelVO();
                            Attribute accept_address = ((Element) route).attribute("accept_address");
                            Attribute remark = ((Element) route).attribute("remark");
                            Attribute accept_time = ((Element) route).attribute("accept_time");
                            Attribute opcode = ((Element) route).attribute("opcode");
                            traceModelVo.setAcceptAddress(accept_address != null ? accept_address.getValue() : "");
                            traceModelVo.setRemark(remark != null ? remark.getValue() : "");
                            traceModelVo.setAcceptTime(accept_time != null ? accept_time.getValue() : "");
                            traceModelVo.setOpcode(opcode != null ? opcode.getValue() : "");
                            traceModelVo.setCityName(accept_address != null ? accept_address.getValue() : "");
                            traces.add(traceModelVo);
                        }
                    }
                    list.setTraces(traces);
                }

            }
        }
        return list;
    }

    /**
     * shunfengApiServices.GetOrderInfoBy
     * 按顺丰单号获取物流表sareaid
     *
     * @param orderid
     * @return ShunfengOrderInfo
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-14
     */
    private ShunfengOrderInfoVO GetOrderInfoBy(String orderid) {
        WuLiuShunfengNoInfoEntity dt = wuLiuShunfengNoInfoService.getOne(Wrappers.<WuLiuShunfengNoInfoEntity>lambdaQuery().select(WuLiuShunfengNoInfoEntity::getId, WuLiuShunfengNoInfoEntity::getSareaid, WuLiuShunfengNoInfoEntity::getCustId, WuLiuShunfengNoInfoEntity::getAddDate, WuLiuShunfengNoInfoEntity::getJMobile, WuLiuShunfengNoInfoEntity::getDMobile).eq(WuLiuShunfengNoInfoEntity::getMailNo, orderid), false);

        if (dt != null) {
            ShunfengOrderInfoVO orderinfo = new ShunfengOrderInfoVO();
            orderinfo.setSareaid(dt.getSareaid());
            orderinfo.setCustid(dt.getCustId());

            if (dt.getAddDate() == null) {
                orderinfo.setAddDate(LocalDateTime.of(LocalDate.of(2019, 11, 1), LocalTime.MAX));
            } else {
                orderinfo.setAddDate(dt.getAddDate());
            }

            String smobile = dt.getJMobile();
            String rmobile = dt.getDMobile();
            if (StringUtils.isNotBlank(smobile) && smobile.length() > 4) {
                orderinfo.setNum1(smobile.substring(smobile.length() - 4));
            }
            if (StringUtils.isNotBlank(rmobile) && rmobile.length() > 4) {
                orderinfo.setNum2(rmobile.substring(rmobile.length() - 4));
            }
            if (orderinfo.getSareaid() == null || orderinfo.getSareaid() == 0) {
                WuLiuEntity wdt = getOne(Wrappers.<WuLiuEntity>lambdaQuery().select(WuLiuEntity::getSAreaId, WuLiuEntity::getDTime, WuLiuEntity::getSMobile, WuLiuEntity::getRMobile).eq(WuLiuEntity::getNu, orderid).eq(WuLiuEntity::getCom, "shunfeng").orderByDesc(WuLiuEntity::getId).last("offset 0 row fetch next 2 row only"), false);

                if (wdt != null) {
                    orderinfo.setSareaid(wdt.getSAreaId());
                    orderinfo.setAddDate(wdt.getDTime());
                    smobile = wdt.getSMobile();
                    rmobile = wdt.getRMobile();
                    if (StringUtils.isNotBlank(smobile) && smobile.length() > 4) {
                        orderinfo.setNum1(smobile.substring(smobile.length() - 4));
                    }
                    if (StringUtils.isNotBlank(rmobile) && rmobile.length() > 4) {
                        orderinfo.setNum2(rmobile.substring(rmobile.length() - 4));
                    }
                }
            }

            //如果没有月结卡号是以前的记录，这里指地区和时间匹配到以前测查询方式
            if (orderinfo.getSareaid() == null || orderinfo.getSareaid() == 0) {
                orderinfo.setSareaid(16);
                orderinfo.setAddDate(LocalDateTime.of(LocalDate.of(2019, 11, 1), LocalTime.MAX));
            }
            return orderinfo;
        } else {
            WuLiuEntity wdt = getOne(Wrappers.<WuLiuEntity>lambdaQuery().select(WuLiuEntity::getSAreaId, WuLiuEntity::getDTime, WuLiuEntity::getSMobile, WuLiuEntity::getRMobile).eq(WuLiuEntity::getNu, orderid).eq(WuLiuEntity::getCom, "shunfeng").orderByDesc(WuLiuEntity::getId).last("offset 0 row fetch next 2 row only"), false);
            if (wdt != null) {
                ShunfengOrderInfoVO orderinfo = new ShunfengOrderInfoVO();
                orderinfo.setSareaid(wdt.getSAreaId());
                orderinfo.setAddDate(wdt.getDTime());
                String smobile = wdt.getSMobile();
                String rmobile = wdt.getRMobile();
                if (StringUtils.isNotBlank(smobile) && smobile.length() > 4) {
                    orderinfo.setNum1(smobile.substring(smobile.length() - 4));
                }
                if (StringUtils.isNotBlank(rmobile) && rmobile.length() > 4) {
                    orderinfo.setNum2(rmobile.substring(rmobile.length() - 4));
                }
                return orderinfo;
            }
        }
        return new ShunfengOrderInfoVO().setSareaid(16).setAddDate(LocalDateTime.of(LocalDate.of(2019, 11, 10), LocalTime.MAX));
    }

    /**
     * zhongtongApiServices.OrderTrace
     *
     * @param orderNo
     * @return TraceListVO
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-15
     */
    private TraceListVO zhongtongOrderTrace(String orderNo) {
        TraceListVO traces = new TraceListVO();
        try {
            String data = "[\"" + orderNo + "\", \"" + orderNo + "\"]";
            //data = "633708279490";
            // -- 需要调用的接口类型 ---
            String msg_type = "NEW_TRACES";
            //得到请求的接口地址
            String url = "http://japi.zto.cn/traceInterfaceNewTraces";
            ZopProperties zopProperties = new ZopProperties();
            zopProperties.setCompanyId(ZTO_COMPANY_ID);
            zopProperties.setKey(ZTO_KEY);
            ZopClient zopClient = new ZopClient(zopProperties);
            ZopPublicRequest request = new ZopPublicRequest();
            request.setUrl(url);
            Map<String, String> map = new HashMap<>();
            map.put("data", data);
            map.put("msg_type", msg_type);
            map.put("company_id", ZTO_COMPANY_ID);
            request.setParams(map);

            String execute = zopClient.execute(request);

            TracesResultInfoVO result = JacksonJsonUtils.toClass(execute, TracesResultInfoVO.class);

            if (CollectionUtils.isNotEmpty(result.getData())) {

                List<TracesVO> traces1 = Optional.ofNullable(result.getData().get(0).getTraces()).orElseGet(ArrayList::new);
                List<TraceModelVO> traces2 = new ArrayList<>();
                TraceModelVO traceModelVo;
                for (TracesVO t : traces1) {
                    traceModelVo = new TraceModelVO();
                    traceModelVo.setAcceptAddress(t.getScanSite());
                    traceModelVo.setAcceptTime(t.getScanDate());
                    traceModelVo.setRemark(t.getDesc());
                    traceModelVo.setOpcode(t.getScanType());
                    traceModelVo.setCityName(t.getScanProv() + t.getScanCity());
                    traces2.add(traceModelVo);
                }
                traces.setTraces(traces2);
            }
        } catch (Exception e) {
            log.error("WuLiuServiceImpl.zhongtongOrderTrace 报错：{}", Exceptions.getStackTraceAsString(e), e);
            return traces;
        }
        return traces;
    }

    /**
     * dadaWuliuService.QueryOrder
     * 订单查询
     *
     * @param orderid
     * @return var
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-13
     */
    private DadaQueryResultVO dadaQueryOrder(Integer orderid) {

        DadaQueryResultVO result = new DadaQueryResultVO();
        Map<String, Object> body = new HashMap<>();
        body.put("order_id", orderid);
        String rejsondata = "";
        try {

            String paramsJson = JsonParseUtil.toJson(body);

            DadaRequestClient dadaClient = new DadaRequestClient(DadaUrlConstant.ORDER_QUERY_URL, paramsJson, dadaIsOnline);
            DadaApiResponse apiResponse = dadaClient.callRpc();

            if (ObjectUtils.isEmpty(apiResponse)) {
                throw new Exception("调用达达快递下单接口失败");
            }
            R<DadaQueryResultVO> data = JSONUtil.toBean(JSONUtil.toJsonStr(apiResponse.getResult()), new cn.hutool.core.lang.TypeReference<R<DadaQueryResultVO>>() {
            }, true);

            if (data != null) {
                if (Objects.equals(data.getCode(), 0)) {
                    result = data.getData();
                }
            }
        } catch (Exception ex) {
            weixinAndOaMessageSend(String.format("达达物流取消订单异常，物流单%s：%s,请求：%s;返回结果：%s", orderid, ex.getMessage(), body, rejsondata), 3, "", 13140, null, null);
        }

        return result;

    }

    /**
     * meituanServices.QueryOrderStatus
     * 订单状态查询
     *
     * @param conn
     * @param saasPlatform
     * @return queryReturnResult
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-13
     */
    public QueryReturnResultVO meituanQueryOrderStatus(QueryItemVO conn, SaasPlatformDTO saasPlatform) {
        String logMsg = "传入参数：" + JacksonJsonUtils.toJson(conn);
        QueryReturnResultVO info = new QueryReturnResultVO();
        if (saasPlatform != null) {
            saasPlatform.setIsJiuJi(true);
            Map<String, Object> request = new HashMap<>();
            request.put("data", conn);
            request.put("saasPlatform", saasPlatform);

            try {
                String reqjson = JacksonJsonUtils.toJson(request);
                logMsg += ("meituan-jiuji，物流中台查询订单请求参数：" + reqjson);
                R<QueryReturnResultVO> getResult = meituanQueryTrace(conn, saasPlatform);
                if (getResult != null) {
                    info = getResult.getData();
                }
                logMsg += ("meituan-jiuji，物流中台查询订单返回结果" + JacksonJsonUtils.toJson(getResult));

            } catch (Exception e) {
                logMsg += ("美团中台查询订单接口异常，参数：" + logMsg + ",异常信息：" + Exceptions.getStackTraceAsString(e));
                info.setMessage(e.getMessage());
                info.setCode(5000);
                sendTextMessage("SAAS接口调用失败（" + e.getMessage() + "）,DATA:" + JacksonJsonUtils.toJson(request));
                log.error(logMsg, e);
            }
            log.info(logMsg);
            return info;
        }

        conn.setTimestamp(String.valueOf(System.currentTimeMillis()));
        conn.setSign(getMeiTuanSign(conn));
        Map<String, Object> stringObjectMap = JacksonJsonUtils.toMap(conn, String.class, Object.class);

        String url = "https://peisongopen.meituan.com/api/order/status/query";
        logMsg += ("meituan物流查询订单请求参数：" + JacksonJsonUtils.toJson(conn));
        try {
            String result = HttpUtil.post(url, stringObjectMap);
            R<QuerySuccessDataVO> json = JacksonJsonUtils.toClass(result, new com.fasterxml.jackson.core.type.TypeReference<R<QuerySuccessDataVO>>() {
            });

            logMsg += ("meituan物流查询订单请求结果：" + json);
            if (Objects.equals(json.getCode(), 0)) {

                info.setCode(0);
                info.setDeliveryId(json.getData().getDeliveryId());
                info.setMtPeisongId(json.getData().getMtPeisongId());
                info.setStatus(json.getData().getStatus());
                info.setCourierName(json.getData().getCourierName());
                info.setCourierPhone(json.getData().getCourierPhone());
                info.setCancelReason(json.getData().getCancelReason());
                info.setOperateTime(String.valueOf(json.getData().getOperateTime()));
            } else {
                info.setCode(json.getCode());
                info.setMessage(json.getUserMsg());
            }
        } catch (Exception e) {
            logMsg += ("美团物流查询订单异常，参数：" + logMsg + ",异常信息：" + Exceptions.getStackTraceAsString(e));
            info.setCode(5000);
            info.setMessage(e.getMessage());
            log.error(logMsg, e);
        }
        log.info(logMsg);
        return info;
    }

    /**
     * Meituan.aspx.cs.QueryTrace
     * 快递单轨迹查询
     *
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-13
     */
    private R<QueryReturnResultVO> meituanQueryTrace(QueryItemVO data, SaasPlatformDTO Tenant) {
        R<QueryReturnResultVO> responseJson = new R<>();
        try {
            if (data == null || Tenant == null) {
                throw new CustomizeException("请求数据不能为空。");
            }
            if (!Boolean.TRUE.equals(Tenant.getIsJiuJi())) {
                if (!verifySaasPlatform(new SaasPlatformDTO().setSaasAreaid(Tenant.getSaasAreaid()).setSaasTenant(Tenant.getSaasTenant()).setPackageCount(Tenant.getPackageCount()))) {
                    throw new Exception("物流SAAS接口未配置");
                }
            }

            QueryOrderRequestDTO logisticRequest = new QueryOrderRequestDTO();
            logisticRequest.setExpressType(1);
            logisticRequest.setDeliveryId(data.getDeliveryId());
            logisticRequest.setPlatformInsideId(data.getMtPeisongId());
            try {
                String url = SysConfigUtils.getJiujiMoaUrl(true) + "/cloudapi_nc/logistics/api/logistics-center/query-order/v1";
                // 创建订单数据
                String sign = genarateSignRequestPackage(logisticRequest);
                Map<String, String> headers = new Hashtable<>(2);
                headers.put("xservicename", "logistics-service");
                headers.put("logistics", DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.now()));
                HttpResponse httpResponse = HttpUtil.createPost(url).body(sign).headerMap(headers, true).timeout(5000).execute();
                // 创建订单返回结果数据
                String body = httpResponse.body();
                R<QuerySuccessDataVO> json = JacksonJsonUtils.toClass(body, new com.fasterxml.jackson.core.type.TypeReference<R<QuerySuccessDataVO>>() {
                });
                if (Objects.equals(responseJson.getCode(), 0)) {
                    responseJson.setData(new QueryReturnResultVO()
                            .setCode(0)
                            .setDeliveryId(json.getData().getDeliveryId())
                            .setMtPeisongId(json.getData().getMtPeisongId())
                            .setStatus(json.getData().getStatus())
                            .setCourierName(json.getData().getCourierName())
                            .setCourierPhone(json.getData().getCourierPhone())
                            .setCancelReason(json.getData().getCancelReason())
                            .setOperateTime2(json.getData().getOperateTime()));
                } else {
                    responseJson.setData(new QueryReturnResultVO().setCode(json.getCode())
                            .setMessage(json.getMsg()));
                }

            } catch (Exception e) {
                responseJson.setCode(5000);
                responseJson.setUserMsg("操作失败");
                responseJson.setMsg(e.getMessage());
                responseJson.setData(new QueryReturnResultVO().setCode(5000).setMessage(e.getMessage()));
            }

        } catch (Exception ex) {
            responseJson.setCode(5000);
            responseJson.setUserMsg("操作失败");
            responseJson.setMsg(ex.getMessage());
        }

        return responseJson;
    }

    /**
     * Fun.getwuliu_statsname
     * 获取物流状态(字符)
     *
     * @param stats 物流状态，数字
     * @return string
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-10
     */
    public static String getwuliuStatsName(int stats) {
        String sname = "";
        switch (stats) {
            case 1:
                sname = "等待取货";
                break;
            case 2:
                sname = "等待派送";
                break;
            case 3:
                sname = "派送中";
                break;
            case 4:
                sname = "完成";
                break;
            case 5:
                sname = "作废";
                break;
            case 6:
                sname = "已签收";
                break;
        }
        return sname;
    }

    /**
     * 获取顺丰账号配置信息列表 v1
     *
     * @param authorization md5 日期鉴权值
     * @return R<List < ShunfengCustidConfigEntity>>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-28
     */
    @Override
    public R<List<ShunfengCustidConfigEntity>> shunfengCustidConfigListAllV1(String authorization) {
        // 调用约定了通过加密日期作为鉴权
        if (!MD5Util.GetMD5Code(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))).equalsIgnoreCase(authorization)) {
            return R.success("获取失败，鉴权未通过", Collections.emptyList());
        }
        if (RedisUtils.hasKey(CacheKey.Redis.WULIU_SHUNFENG_CUSTID_CONFIG_LIST_ALL)) {
            return R.success("获取成功", JSON.parseArray(RedisUtils.get(CacheKey.Redis.WULIU_SHUNFENG_CUSTID_CONFIG_LIST_ALL), ShunfengCustidConfigEntity.class));
        } else {
            List<ShunfengCustidConfigEntity> list = Optional.ofNullable(shunfengCustidConfigMapper.selectList(Wrappers.query())).orElseGet(ArrayList::new);
            RedisUtils.set(CacheKey.Redis.WULIU_SHUNFENG_CUSTID_CONFIG_LIST_ALL, list, Duration.of(NumUtil.SIX, ChronoUnit.HOURS));
            return R.success("获取成功", list);
        }
    }

    /**
     * https://jiuji.yuque.com/docs/share/ae8bed26-062f-4390-983c-b48ef7f47a18?#%20%E3%80%8A%E3%80%90%E7%8B%AC%E7%AB%8B%E3%80%91%E3%80%90%E4%B9%9D%E6%9C%BA%E3%80%91%E3%80%90%E7%89%A9%E6%B5%81%E5%BC%80%E5%8F%91%E7%BB%84%E3%80%91%EF%BC%88PC%E5%8F%8A%E7%A7%BB%E5%8A%A8%E7%AB%AF%EF%BC%89%E6%89%8B%E5%8A%A8%E6%B7%BB%E5%8A%A0%E7%89%A9%E6%B5%81%E5%8D%95%E7%94%9F%E6%88%90%E5%BF%AB%E9%80%92%E5%8D%95%E5%A2%9E%E5%8A%A0%E6%9D%83%E9%99%90%E3%80%8B
     * 物流单列表快递方式增加权限管控
     * @return
     */
    public boolean checkKdscRank(WuLiuAddOrUpdateReqVO model) {
        //不是物流单列表添加的物流单
        if (!Objects.equals(1, model.getSource())) {
            return false;
        }
        //手动输入运单号不用校验权限
        if (StringUtils.isNotEmpty(model.getNu())) {
            return false;
        }
        //不生成快递
        if (StringUtils.isBlank(model.getCom())) {
            return false;
        }
        //没有kdsc权限，快递类型不是美团、达达、uu
        if (SysUtils.hasNotRank(WuLiuConstant.RANK_KDSC) && !(Arrays.asList(WuLiuConstant.MEITUAN_JIUJI, WuLiuConstant.MEITUAN,
                WuLiuConstant.DADA, WuLiuConstant.UU_PAOTUI, WuLiuConstant.PAO_TUI, LogisticsExpressTypeEnum.SFTC.getCode()).contains(model.getCom()))) {
            return true;
        }
        return false;
    }

    @Override
    public R<Boolean> saveScanLog(WuLiuLogReqVO req, OaUserBO currentUser) {
        log.info("发货扫描req={},currentUser={}",JacksonJsonUtils.toJson(req),JacksonJsonUtils.toJson(currentUser));
        String wuliuidString = req.getWuliuid();
        if(StringUtils.isEmpty(wuliuidString)){
            throw new CustomizeException("物流单id不能为空:"+wuliuidString);
        }
        Long wuliuid;
        try {
            wuliuid = Long.valueOf(wuliuidString);
        } catch (NumberFormatException e) {
            throw new CustomizeException("请输入正确的物流单id:" + wuliuidString);
        }

        WuLiuEntity wuLiuEntity = this.lambdaQuery().eq(WuLiuEntity::getId, wuliuid)
                .one();
        if (Objects.isNull(wuLiuEntity)) {
            return R.success("系统中不存在该物流单");
        }
        String userName = currentUser.getUserName();
        LocalDateTime now = LocalDateTime.now();
        StringBuilder msg = new StringBuilder();
        msg.append("发货扫描了物流单");
        //发货扫描物流单来源增加标识 手动和扫码标识
        if (Objects.equals(0,req.getFrom())) {
            msg.append("(手动添加)");
        } else if (Objects.equals(1,req.getFrom())) {
            msg.append("(扫码)");
        }
        WuLiuLogEntity wuliuLogs = new WuLiuLogEntity()
                .setInuser(userName)
                .setMsg(msg.toString())
                .setDtime(now)
                .setKind(1)
                .setWuliuid(Integer.valueOf(String.valueOf(wuliuid)));
        wuLiuLogService.save(wuliuLogs);
        return R.success("记录发货扫描日志成功");
    }

    /**
     * 更新物流单的运单号
     * @param wuliuWayBillNoReq
     */
    @Override
    @DS("oanewWrite")
    @Transactional
    public void updateNuById(WuliuWayBillNoReq wuliuWayBillNoReq) {
        boolean updateFlag = this.lambdaUpdate()
                .set(WuLiuEntity::getNu, wuliuWayBillNoReq.getWaybillNo())
                .set(StrUtil.isNotBlank(wuliuWayBillNoReq.getCom()), WuLiuEntity::getCom, wuliuWayBillNoReq.getCom())
                .eq(WuLiuEntity::getId, wuliuWayBillNoReq.getWuliuId())
                .and(wapper -> wapper.isNull(WuLiuEntity::getNu).or().eq(WuLiuEntity::getNu, ""))
                .update();
        if (updateFlag && !Boolean.FALSE.equals(wuliuWayBillNoReq.getIsSaveLog())) {
            wuLiuLogService.addOne(wuliuWayBillNoReq.getWuliuId().intValue(), "系统", "物流中台返回创建成功快递运单号"+wuliuWayBillNoReq.getWaybillNo());
        }
        log.info("物流中台返回快递运单号处理成功param={}", JacksonJsonUtils.toJson(wuliuWayBillNoReq));
    }

    /**
     * 查询PayMethod
     * @param sessionAreaId
     * @return
     */
    @Override
    @DS("ch999oanew")
    public Integer queryPayMethodByAreaId(Integer sessionAreaId) {
        return Optional.ofNullable(this.baseMapper.queryPayMethodByAreaId(sessionAreaId)).orElse(0);
    }

    /**
     * 快递单号查询物流单号
     *
     * @param nu
     * @return
     */
    @Override
    @DS("ch999oanew")
    public List<WuLiuEntity> getWuliuByNu(String nu) {
        if (StringUtils.isBlank(nu)) {
            return null;
        }
        return this.baseMapper.getWuliuByNu(nu);
    }

    /**
     * id查询物流单
     *
     * @param id
     * @return
     */
    @Override
    @DS("ch999oanew")
    public WuLiuEntity getWuliuById(Integer id) {
        return this.getById(id);
    }

    /**
     * 物流类型和绑定单号查询正在进行中（除了作废状态）的物流单
     *
     * @param wuType
     * @param danhaobind
     * @return
     */
    @Override
    public WuLiuEntity getWuLiuByWuTypeAndDanhaobind(Integer wuType, Integer danhaobind) {
        return this.baseMapper.getWuLiuByWuTypeAndDanhaobind(wuType, danhaobind);
    }

    @Override
    public void updateStateById(Integer wuliuid, Integer stats) {
        this.baseMapper.updateStateById(wuliuid,stats);
    }

    /**
     * 查询超时未完结的物流单
     *
     * @param days
     * @return
     */
    @Override
    @DS("ch999oanew")
    public List<WuLiuEntity> getTimeoutWuliuList(Integer days) {
        return this.baseMapper.getTimeoutWuliuList(days);
    }

    @Override
    public Coordinate getSendPositionByWuliuId(Long wuliuId) {
        WuLiuEntity wuLiuEntity = wuLiuService.getById(wuliuId);
        if (wuLiuEntity == null) {
            return null;
        }
        return SpringUtil.getBean(IWuliuAddressService.class).getAreaCoordinate(wuLiuEntity.getSAreaId(), wuLiuEntity.getSAddress(), wuLiuEntity.getSCityId(), 1);
    }

    @Override
    @DS("ch999oanew")
    public List<InnerWuliuInfoRes> getInnerWuliuInfo(InnerWuliuInfoReq vo) {
        if (Objects.isNull(vo.getDanhaobind()) && Objects.isNull(vo.getWuliuId())) {
            return null;
        }
        return this.baseMapper.getInnerWuliuInfo(vo);
    }

    /**
     * 内部物流单查询是否关联订单，良品，维修单订货调拨单
     * @param wuliuId
     * @return
     */
    @DS("ch999oanew")
    public Integer getInnerWuliuCount(Integer wuliuId) {
        return this.baseMapper.getInnerWuliuCount(wuliuId);
    }

    /**
     * 查询调拨物流单
     * @param wuliuId
     * @return
     */
    @Override
    @DS("ch999oanew")
    public Integer getDiaobWuliuCount(Integer wuliuId) {
        return this.baseMapper.getDiaobWuliuCount(wuliuId);
    }

    /**
     * 查询收货人是员工的代签收物流单
     *
     * @return
     */
    @Override
    @DS("ch999oanew")
    public Page<SignatureWuliuBO> querySignatureWuliuList(Page<SignatureWuliuBO> page) {
        return this.baseMapper.querySignatureWuliuList(page);
    }

    /**
     * 查询物流单关联发货调拨单信息
     *
     * @param data
     * @return
     */
    @Override
    @DS("ch999oanew")
    public List<DiaoboPaotuiWuliuResBO> queryDiaoboPaotuiWuliu(DiaoboPaotuiWuliuBO data) {
        return this.baseMapper.queryDiaoboPaotuiWuliu(data);
    }

    /**
     * 物流单未投妥
     *
     * @param wuliu
     */
    @DS("oanewWrite")
    @Override
    public boolean updateWuliuNotDelivered(WuLiuEntity wuliu) {
        return wuLiuService.lambdaUpdate().set(WuLiuEntity::getNu, null)
                .set(WuLiuEntity::getCom, null)
                .set(WuLiuEntity::getStats, WuLiuStatusEnum.WAITING_DELIVERY.getCode())
                .eq(WuLiuEntity::getId, wuliu.getId())
                .update();
    }
}
