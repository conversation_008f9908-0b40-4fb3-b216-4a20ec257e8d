package com.jiuji.oa.wuliu.service.impl;

import cn.hutool.core.convert.Convert;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.apollo.ApolloKeys;
import com.jiuji.oa.nc.common.util.CommonUtil;
import com.jiuji.oa.stock.sub.entity.Basket;
import com.jiuji.oa.wuliu.bo.SellerInfoBO;
import com.jiuji.oa.wuliu.entity.RecoverMarketsubinfo;
import com.jiuji.oa.wuliu.mapper.RecoverMarketsubinfoMapper;
import com.jiuji.oa.wuliu.service.IRecoverMarketsubinfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 转售详情单（良品详情订单）[责任小组:回收] 服务实现类
 * </p>
 *
 * <AUTHOR> yao yao
 * @since 2022-06-01
 */
@Service
@RequiredArgsConstructor
@Slf4j
@DS("oanewWrite")
public class RecoverMarketsubinfoServiceImpl extends ServiceImpl<RecoverMarketsubinfoMapper, RecoverMarketsubinfo> implements IRecoverMarketsubinfoService {


    /**
     * 是否为付费加急送
     *
     * @param subId
     * @return
     */
    @Override
    public boolean isPayMoneyDelivery(Integer subId) {
        Integer ppid = Convert.toInt(ApolloKeys.getApolloWithMainTenant(ApolloKeys.PAY_MONEY_DELIVERY_PPID, null));
        if(ppid == null){
            return false;
        }
        return this.lambdaQuery().eq(RecoverMarketsubinfo::getSubId, subId).and(CommonUtil.isNullOrEq(RecoverMarketsubinfo::getIsdel, false))
                .eq(RecoverMarketsubinfo::getPpriceid, ppid).count() > 0;
    }

    /**
     * @param subId
     * @return
     */
    @Override
    public SellerInfoBO getSellerInfoBySubId(Integer subId) {
        return this.baseMapper.getSellerInfoBySubId(subId);
    }
}
