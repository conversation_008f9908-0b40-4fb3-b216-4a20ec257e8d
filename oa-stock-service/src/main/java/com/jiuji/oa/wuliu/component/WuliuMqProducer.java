package com.jiuji.oa.wuliu.component;

import cn.hutool.core.lang.Dict;
import com.jiuji.oa.stock.common.util.JacksonJsonUtils;
import com.jiuji.oa.wuliu.vo.RabbitMqActDTO;
import com.jiuji.oa.wuliu.vo.UpdateWuLiuNoDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.AmqpException;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/3/16 15:19
 */
@Component
@Slf4j
public class WuliuMqProducer {
    @Resource(name = "oaAsyncRabbitTemplate")
    private RabbitTemplate oaAsyncRabbitTemplate;


    /**
     *
     * @param wuLiuId
     * @param inUser
     */
    public void updateSubThirdPlatformDelivery(Integer wuLiuId, String inUser) {
        oaAsyncSendMsg(JacksonJsonUtils.toJson(new RabbitMqActDTO().setAct("updateSubThirdPlatformDelivery")
                .setData(Dict.create().set("wuliuId", wuLiuId).set("inUser", inUser))
        ));
    }

    /**
     * orderServices.UpdteWuliuNo
     * 新机更新物流单号,修改订单收货地址物流单号
     *
     * @param companyName
     * @param wuLiuNo
     * @param subId
     * @param user
     * @date 2021-10-18
     * <AUTHOR> [<EMAIL>]
     */
    public void updteWuliuNo(String companyName, String wuLiuNo, Long subId, String user) {
        oaAsyncSendMsg(JacksonJsonUtils.toJson(new RabbitMqActDTO().setAct("UpdteWuliuNo")
                .setData(new UpdateWuLiuNoDTO()
                        .setCompanyName(companyName)
                        .setWuLiuNo(wuLiuNo)
                        .setSubId(subId).setUser(user))
        ));
    }

    /**
     * LpSubService.UpdateWuliuNo
     * 良品更新物流单号,修改订单收货地址物流单号
     *
     * @param companyName
     * @param wuLiuNo
     * @param subId
     * @param user
     * @date 2021-10-18
     * <AUTHOR> [<EMAIL>]
     */
    public void updateWuliuNo2(String companyName, String wuLiuNo, Long subId, String user) {
        oaAsyncSendMsg(JacksonJsonUtils.toJson(new RabbitMqActDTO().setAct("UpdateWuliuNo")
                .setData(new UpdateWuLiuNoDTO()
                        .setCompanyName(companyName)
                        .setWuLiuNo(wuLiuNo)
                        .setSubId(subId).setUser(user))
        ));
    }

    /**
     * oaAsync发送消息
     * @param message 要发送的消息
     * @date 2021-10-20
     * <AUTHOR> [<EMAIL>]
     */
    public void oaAsyncSendMsg(String message) {
        log.warn("给 C# 发送 RabbitMQ 队列消息入参: {}", message);
        try {
            oaAsyncRabbitTemplate.convertAndSend("oaAsync", message);
        } catch (AmqpException e) {
            log.error("给 C# 发送 RabbitMQ 队列消息报错: {}, message: {}, e: {}", e.getMessage(), message, e);
        }
    }
}
