package com.jiuji.oa.wuliu.vo.res;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/6/25 15:13
 */
@Data
public class WuliuOrderInfoRes {
    private Integer aliPayId;

    private Integer payType;

    private Integer subId;

    private Integer subCheck;

    private Integer userid;

    private String productName;

    private Integer delivery;

    private String wuliucompany;

    private String wuliuNo;

    private String subMobile;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expressReceiveTime;
}
