package com.jiuji.oa.wuliu.service;

import com.jiuji.oa.wuliu.vo.*;

/**
 * IWuLiujdService
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-12-05
 */
public interface IWuLiujdService {

    /**
     * 京东快递单生成
     * @param param
     * @return
     */
    JdCreateOrderResultDTO jdCreateOrder(JdOrderParamDTO param);


    /**
     * 生成京东快递单
     * @param sender
     * @param receiver
     * @param remark
     * @param saasPlatform
     * @param orderId
     * @param packageCount
     * @return
     */
    Ch99ResultDataDTO<JdOrderResponseDTO> jdCreateNo(AddressDTO sender, AddressDTO receiver, String remark, SaasPlatformDTO saasPlatform, String orderId, Integer packageCount);
}
