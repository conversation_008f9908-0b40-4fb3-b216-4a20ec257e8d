package com.jiuji.oa.wuliu.service.impl;


import cn.hutool.core.lang.TypeReference;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.jd.open.api.sdk.DefaultJdClient;
import com.jd.open.api.sdk.domain.etms.WaybillJosService.response.receive.WaybillResultInfoDTO;
import com.jd.open.api.sdk.request.etms.LdopWaybillReceiveRequest;
import com.jd.open.api.sdk.response.etms.LdopWaybillReceiveResponse;
import com.jiuji.oa.baozun.common.exception.BaoZunApiException;
import com.jiuji.oa.nc.common.bo.OaUserBO;
import com.jiuji.oa.nc.common.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.nc.common.config.rabbitmq.RabbitMqConfig;
import com.jiuji.oa.nc.common.exception.CustomizeException;
import com.jiuji.oa.nc.common.util.OaAuthUtil;
import com.jiuji.oa.nc.user.po.Areainfo;
import com.jiuji.oa.nc.user.service.IAreaInfoService;
import com.jiuji.oa.stock.common.util.AreaInfoUtils;
import com.jiuji.oa.stock.common.util.JacksonJsonUtils;
import com.jiuji.oa.stock.common.util.OptionalUtils;
import com.jiuji.oa.stock.common.util.SysUtils;
import com.jiuji.oa.stock.logisticscenter.utils.JiuJiApi;
import com.jiuji.oa.stock.logisticscenter.utils.LogisticsHttpClient;
import com.jiuji.oa.stock.logisticscenter.vo.req.OaSignReq;
import com.jiuji.oa.stock.secretconfig.service.SecretCodeConfigService;
import com.jiuji.oa.wuliu.bo.WuliuExpressMqBO;
import com.jiuji.oa.wuliu.constant.WuLiuConstant;
import com.jiuji.oa.wuliu.constant.WuliuExpressConstant;
import com.jiuji.oa.wuliu.entity.JingdongPrintInfo;
import com.jiuji.oa.wuliu.entity.SaasJingdongPrintInfo;
import com.jiuji.oa.wuliu.enums.MsgTypeEnum;
import com.jiuji.oa.wuliu.service.IJingdongPrintInfoService;
import com.jiuji.oa.wuliu.service.ISaasJingdongPrintInfoService;
import com.jiuji.oa.wuliu.service.IWuLiuService;
import com.jiuji.oa.wuliu.service.IWuLiujdService;
import com.jiuji.oa.wuliu.vo.*;
import com.jiuji.oa.wuliu.vo.express.req.JdCreateOrderParamReq;
import com.jiuji.oa.wuliu.vo.res.JingdongConfig;
import com.jiuji.tc.common.vo.R;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.AmqpException;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Objects;
import java.util.Optional;
import java.util.Random;


/**
 * WuLiujdServiceImpl
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-12-05
 */
@Service
@Slf4j
@DS("oanewWrite")
@AllArgsConstructor
public class WuLiujdServiceImpl implements IWuLiujdService {

    private static final Integer BIG_NUMBER = 9999;
    private static final Integer EXPRESS_TYPE = 3;
    private static final Integer RESULT_CODE_ONE = 100;
    private static final Integer RESULT_CODE_TWO = 110;
    private static final String GET_TOKEN_URL = "/api/logistics-jongdong/getAccessToken/v2/";
    private static final Integer SECRET_BY_CODE = 15;
    private final AbstractCurrentRequestComponent currentRequestComponent;
    private final IAreaInfoService areaInfoService;
    private final IJingdongPrintInfoService jingdongPrintInfoService;
    private final ISaasJingdongPrintInfoService saasJingdongPrintInfoService;
    private final SecretCodeConfigService secretCodeConfigService;
    private final RabbitTemplate rabbitTemplate;

    /**
     * 获取ReceiverAddress
     *
     * @param param
     * @return
     */
    private static String getReceiverAddress(JdOrderParamDTO param) {
        //收货地址自动完善
        StringBuilder receiverAddressBuilder = new StringBuilder();
        String receiverAddress = param.getReceiverAddress();
        String receiverProvinceName = Optional.ofNullable(param.getReceiverProvinceName()).orElseThrow(() -> new CustomizeException("ReceiverProvinceName不能为空"));
        //缺少省份信息
        if (StringUtils.isNotEmpty(receiverAddress) && !receiverAddress.startsWith(receiverProvinceName) && !receiverAddress.startsWith(receiverProvinceName.replace(WuLiuConstant.PROVINCE, ""))) {
            String receiverCityName = Optional.ofNullable(param.getReceiverCityName()).orElseThrow(() -> new CustomizeException("ReceiverCityName不能为空"));
            //缺少市级信息
            if (!receiverAddress.startsWith(receiverCityName)) {
                String receiverCountryName = Optional.ofNullable(param.getReceiverCountryName()).orElseThrow(() -> new CustomizeException("ReceiverCountryName不能为空"));
                //缺少区县级信息
                if (!receiverAddress.startsWith(receiverCountryName)) {
                    receiverAddressBuilder.append(receiverProvinceName).append(receiverCityName).append(receiverCountryName).append(receiverAddress);

                } else {
                    receiverAddressBuilder.append(receiverProvinceName).append(receiverCityName).append(receiverAddress);
                }
                //缺省信息
            } else {
                receiverAddressBuilder.append(receiverProvinceName).append(receiverAddress);
            }
        } else {
            receiverAddressBuilder.append(receiverAddress);
        }

        return receiverAddressBuilder.toString();
    }


    /**
     * 获取发货地址
     *
     * @param param
     * @return
     */
    private static String getSendAddress(JdOrderParamDTO param) {
        //发货地址自动完善
        StringBuilder sendAddressBuilder = new StringBuilder();
        String sendAddress = param.getSendAddress();
        String senderProvinceName = Optional.ofNullable(param.getSenderProvinceName()).orElseThrow(() -> new CustomizeException("SenderProvinceName不能为空"));
        if (StringUtils.isNotEmpty(sendAddress) && !sendAddress.startsWith(senderProvinceName) && !sendAddress.startsWith(senderProvinceName.replace(WuLiuConstant.PROVINCE, ""))) {
            String senderCityName = Optional.ofNullable(param.getSenderCityName()).orElseThrow(() -> new CustomizeException("SenderCityName不能为空"));
            //缺少市级信息
            if (!sendAddress.startsWith(senderCityName)) {
                String senderCountryName = Optional.ofNullable(param.getSenderCountryName()).orElseThrow(() -> new CustomizeException("SenderCountryName不能为空"));
                //缺少区县级信息
                if (!sendAddress.startsWith(senderCountryName)) {
                    sendAddressBuilder.append(senderProvinceName).append(senderCityName).append(senderCountryName).append(sendAddress);
                } else {
                    sendAddressBuilder.append(senderProvinceName).append(senderCityName).append(sendAddress);
                }
                //缺省信息
            } else {
                sendAddressBuilder.append(senderProvinceName).append(sendAddress);
            }
        } else {
            sendAddressBuilder.append(sendAddress);
        }
        return sendAddressBuilder.toString();
    }

    private static String getAccessToken(JingdongConfig config) {
        String url = GET_TOKEN_URL + config.getAppKey();
        String accessToken = null;
        try {
            accessToken = LogisticsHttpClient.get(url, null);
        } catch (Exception e) {
            log.error("中台获取accessToken异常:{}", e.getMessage(), e);
            throw new CustomizeException("中台获取accessToken异常");
        }
        Result result = JSONUtil.toBean(accessToken, Result.class);
        if (result.getCode() == 0) {
            return result.getData();
        } else {
            throw new CustomizeException("中台服务提示:" + result.getUserMsg());
        }

    }

    /**
     * 获取订单号
     *
     * @param orderId
     * @return
     */
    private static String getOrderId(String orderId) {
        if (StringUtils.isEmpty(orderId)) {
            DateTimeFormatter dtf2 = DateTimeFormatter.ofPattern("yyyyMMddHHmmss-");
            String date = dtf2.format(LocalDateTime.now());
            String number = String.format("%04d", new Random().nextInt(BIG_NUMBER));
            return date + number;
        } else {
            return orderId;
        }
    }

    @Override
    public JdCreateOrderResultDTO jdCreateOrder(JdOrderParamDTO param) {

        log.info("接受参数：{}", JSONUtil.toJsonStr(param));
        //获取收货地址
        String receiverAddress = getReceiverAddress(param);
        //获取发货地址
        String sendAddress = getSendAddress(param);

        Areainfo fromAreaInfo = areaInfoService.getById(param.getSendAreaId());
        Areainfo recevieAreaInfo = areaInfoService.getById(param.getReceiveAreaId());
        OaUserBO oaUserBO = Optional.ofNullable(currentRequestComponent.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("当前登录信息为空"));
        String s = Optional.ofNullable(param.getGoodsWeight()).orElse("0");
        s = StringUtils.isBlank(s) || "null".equalsIgnoreCase(s) ? "0" : s;
        double goodsWeight = Double.parseDouble(s);
        s = Optional.ofNullable(param.getPackageCount()).orElse("1");
        s = StringUtils.isBlank(s) || "null".equalsIgnoreCase(s) ? "1" : s;
        Integer packageCount = Integer.parseInt(s);
        //构建调用参数
        JdCreateOrderParamReq createOrderParam = new JdCreateOrderParamReq();
        createOrderParam.setSendShopId(param.getSendAreaId())
                .setSendShopName(fromAreaInfo == null ? "" : (fromAreaInfo.getArea() + fromAreaInfo.getAreaName()))
                .setReceiveShopId(param.getReceiveAreaId())
                .setReceiveShopName(recevieAreaInfo == null ? "" : (recevieAreaInfo.getArea() + recevieAreaInfo.getAreaName()))
                .setExpressType(EXPRESS_TYPE)
                .setXTenantId(OptionalUtils.ifNotNull(getAreaXTenat(oaUserBO.getAreaId()), Long::valueOf));
        createOrderParam.setOrderNo(param.getWuliuId())
                .setReceiverName(param.getReceiverName())
                .setReceiverAddress(receiverAddress)
                .setReceiverMobile(param.getReceiverMobile())
                .setGrossWeight(goodsWeight <= 0 ? 1 : goodsWeight)
                .setSenderName(param.getSendName())
                .setSenderMobile(param.getSendMobile())
                .setSenderAddress(sendAddress)
                .setExpressItemQty(packageCount)
                .setDropMenuExpressType(param.getChildExpressType())
                .setGrossVolume(param.getVloumn().doubleValue());
        if (param.getChildExpressType() != 1 && param.getChildExpressType() > 0) {
            createOrderParam
                    .setReceiverProvinceName(param.getReceiverProvinceName())
                    .setReceiverCityName(param.getReceiverCityName())
                    .setSenderProvinceName(param.getSenderProvinceName())
                    .setSenderCityName(param.getSenderCityName())
                    .setExpressItemName("电子产品");

        }
        //物流中台调用
        StringBuilder logMsg = new StringBuilder();
        logMsg.append("创建订单数据{").append(JSONUtil.toJsonStr(createOrderParam)).append("}");
        String result = null;
        String secretByCode = secretCodeConfigService.getSecretByCode(SECRET_BY_CODE);
        long currentTimeMillis = System.currentTimeMillis();
        try {
            String sign = OaAuthUtil.signBySecret(secretByCode, currentTimeMillis, createOrderParam);
            OaSignReq<JdCreateOrderParamReq> jdCreateOrderParamReqOaSignReq = new OaSignReq<>(sign, currentTimeMillis, createOrderParam);
            result = LogisticsHttpClient.post(JiuJiApi.CREATE_ORDER, jdCreateOrderParamReqOaSignReq);
            logMsg.append("创建订单返回结果数据").append(result);
        } catch (IllegalArgumentException | BaoZunApiException e) {
            log.error("物流中台接口调用异常,传入参数：[{}],返回参数：[{}]", JSONUtil.toJsonStr(createOrderParam), result, e);
            throw new CustomizeException("物流中台接口调用异常");
        }
        if (StringUtils.isEmpty(result)) {
            log.error("物流中台接口调用返回数据为空,传入参数：[{}],返回参数：[{}]", JSONUtil.toJsonStr(createOrderParam), result);
            throw new CustomizeException("物流中台接口调用返回数据为空");
        }
        R r = JSONUtil.toBean(result, R.class);
        int code = r.getCode();
        if (code != 0) {
            logMsg.append("物流单{").append(param.getWuliuId()).append("}推送失败");
            log.error("物流中台接口调用异常,传入参数：[{}],返回参数：[{}]", JSONUtil.toJsonStr(createOrderParam), result, r.getCode());
            throw new CustomizeException("物流中台服务异常：" + r.getUserMsg());
        } else {
            logMsg.append("物流单{").append(param.getWuliuId()).append("}推送成功");
        }
        Object data = Optional.ofNullable(r.getData()).orElseThrow(() -> new CustomizeException("返回data为空"));
        HashMap<String, String> stringStringHashMap = JSONUtil.toBean(JSONUtil.toJsonStr(data), new TypeReference<HashMap<String, String>>() {
        }, true);
        String jingdong = stringStringHashMap.get("jingdong");
        String lwbNo = stringStringHashMap.get("lwbNo");
        JSONObject jsonObject = JSONUtil.parseObj(jingdong);
        Object deliveryId = jsonObject.get("deliveryId");
        JdCreateOrderResultDTO jdCreateOrderResultDTO = new JdCreateOrderResultDTO();
        DataResultDTO dataResultDTO = new DataResultDTO();
        dataResultDTO.setExpressNumber(Optional.ofNullable(deliveryId).orElse(lwbNo) + "");
        dataResultDTO.setExpressNumber2(lwbNo);
        jdCreateOrderResultDTO.setDataResult(dataResultDTO);

        try {
            R<JdCreateOrderResultDTO> jdResult = JacksonJsonUtils.toClass(result, new com.fasterxml.jackson.core.type.TypeReference<R<JdCreateOrderResultDTO>>() {
            });
            if (Objects.nonNull(jdResult) && Objects.equals(jdResult.getCode(), 0) && Objects.nonNull(jdResult.getData())) {
                WuliuExpressMqBO<JingdongPrintInfo> wuliuExpressMq = new WuliuExpressMqBO<JingdongPrintInfo>().setAct(WuliuExpressConstant.ACT_JINGDONGPRINTINFO)
                        .setData(new JingdongPrintInfo().setOrderId(param.getWuliuId().toString())
                                        .setAreaId(Optional.ofNullable(param.getSendAreaId()).orElse(0))
                                        .setDeliveryId(jdResult.getData().getExpressNumber())
                                        .setPromiseTimeType(jdResult.getData().getDataResult().getPromiseTimeType())
                                        .setPreSortResult(JSONUtil.toJsonStr(jdResult.getData().getDataResult().getPreSortResult()))
                                        .setTransType(jdResult.getData().getDataResult().getTransType())
                                        .setNeedRetry(jdResult.getData().getDataResult().getNeedRetry())
                                        .setExpressOperationMode(jdResult.getData().getDataResult().getExpressOperationMode()));

                String jsonMessage = JacksonJsonUtils.toJson(wuliuExpressMq);
                rabbitTemplate.convertAndSend(RabbitMqConfig.QUEUE_WULIU_EXPRESS_SYNC, jsonMessage);
            }
        } catch (Exception e) {
            log.error("保存京东面单信息异常", e);
        }
        return jdCreateOrderResultDTO;
    }
    
    /**
     * 根据地区所在 hq 或者 dc 获取 xtenant
     *
     * @param areaId
     * @return Integer
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-28
     */
    public Integer getAreaXTenat(Integer areaId) {
        Integer currentAreaId = AreaInfoUtils.getCurrentH1(areaId);
        if(currentAreaId == null) {
            currentAreaId = AreaInfoUtils.getCurrentDc(areaId);
        }
        return Optional.ofNullable(areaInfoService.getAreaInfoByAreaId2(currentAreaId))
                .orElseGet(Areainfo::new).getXtenant();
    }

    @Override
    public Ch99ResultDataDTO<JdOrderResponseDTO> jdCreateNo(AddressDTO sender, AddressDTO receiver, String remark, SaasPlatformDTO saasPlatform, String orderId, Integer packageCount) {
        IWuLiuService wuLiuService = SpringUtil.getBean(IWuLiuService.class);
        Ch99ResultDataDTO<JdOrderResponseDTO> result = new Ch99ResultDataDTO<>();
        try {
            if (saasPlatform != null && saasPlatform.getPackageCount() > 1) {
                packageCount = saasPlatform.getPackageCount();
            }
            JingdongConfig config = new JingdongConfig();
            if (saasPlatform == null) {
                config.setCustomerCode("028K826598")
                        .setAppSecret("1d77a749ff2948ad85eb1b3bee3db31a")
                        .setAppKey("D4A375A39D3010ACD0D6BBCD7F5217B3");

            } else {
                config.setCustomerCode("028K963622")
                        .setAppSecret("bdd7350ad75f4417b3769d9da5a75e65")
                        .setAppKey("FE953A1030F3CCB5D97691204CB480EB");
            }
            String token = getAccessToken(config);
            if (StringUtils.isEmpty(token)) {
                throw new CustomizeException("accessToken获取失败");
            }
            //参数封装
            LdopWaybillReceiveRequest req = new LdopWaybillReceiveRequest();
            req.setSalePlat("0030001");
            req.setCustomerCode(config.getCustomerCode());
            req.setOrderId(getOrderId(orderId));
            req.setSenderName(sender.getName());
            req.setSenderAddress(sender.getAddress());
            req.setSenderMobile(sender.getMobile());
            req.setReceiveName(receiver.getName());
            req.setReceiveAddress(receiver.getAddress());
            req.setProvince(receiver.getProv());
            req.setCity(receiver.getCity());
            req.setReceiveMobile(receiver.getMobile());
            req.setPackageCount(packageCount);
            req.setWeight(1.0);
            req.setVloumn(1.0);
            req.setRemark(remark);
            DefaultJdClient client = new DefaultJdClient("https://api.jd.com/routerjson", token, config.getAppKey(), config.getAppSecret());
            LdopWaybillReceiveResponse response = client.execute(req);
            WaybillResultInfoDTO res = response.getReceiveorderinfoResult();
            if (res != null) {
                if (RESULT_CODE_ONE.equals(res.getResultCode())) {
                    boolean saasShare = false;
                    try {
                        JingdongPrintInfo jingdongPrintInfo = new JingdongPrintInfo();
                        jingdongPrintInfo.setOrderId(res.getOrderId())
                                .setDeliveryId(res.getDeliveryId())
                                .setPromiseTimeType(res.getPromiseTimeType())
                                .setPreSortResult(JSONUtil.toJsonStr(res.getPreSortResult()))
                                .setTransType(res.getTransType())
                                .setNeedRetry(res.getNeedRetry())
                                .setExpressOperationMode(res.getExpressOperationMode());
                        jingdongPrintInfoService.save(jingdongPrintInfo);
                        if (saasPlatform != null) {
                            saasShare = true;
                            SaasJingdongPrintInfo saasJingdongPrintInfo = new SaasJingdongPrintInfo();
                            saasJingdongPrintInfo.setSaasTenant(saasPlatform.getSaasTenant())
                                    .setSaasAreaid(saasPlatform.getSaasAreaid())
                                    .setOrderId(res.getOrderId())
                                    .setDeliveryId(res.getDeliveryId())
                                    .setPromiseTimeType(res.getPromiseTimeType())
                                    .setPreSortResult(JSONUtil.toJsonStr(res.getPreSortResult()))
                                    .setTransType(res.getTransType())
                                    .setNeedRetry(res.getNeedRetry())
                                    .setExpressOperationMode(res.getExpressOperationMode());
                            saasJingdongPrintInfoService.save(saasJingdongPrintInfo);

                        }
                    } catch (Exception e) {
                        log.error("京东物流异常", e);
                        log.error((saasShare ? "SAAS" : "") + "京东物流单生成保存打印信息失败(" + e.getMessage() + ")：DATA:" + JSONUtil.toJsonStr(res) + JSONUtil.toJsonStr(saasPlatform) + ";REQDATA:" + JSONUtil.toJsonStr(req));

                        String comment = (saasShare ? "SAAS" : "") + "京东物流单生成保存打印信息失败(" + e.getMessage() + ")：DATA:" + JSONUtil.toJsonStr(res) + JSONUtil.toJsonStr(saasPlatform) + ";REQDATA:" + JSONUtil.toJsonStr(req);
                        wuLiuService.weixinAndOaMessageSend(comment, 3, "", WuLiuConstant.ZLX_CH999_ID, MsgTypeEnum.ABNORMAL_NOTICE.getCode(), null);

                    }
                    result.setStats(1)
                            .setData(new JdOrderResponseDTO().setWaybillCode(res.getDeliveryId()));
                    return result;

                } else {
                    log.info("jingdongwuliu" + "京东物流单生成失败（" + res.getResultCode() + ":" + res.getResultMessage() + "）REDATA:" + JSONUtil.toJsonStr(res) + ";REQDATA:" + JSONUtil.toJsonStr(req));

                    if (!RESULT_CODE_TWO.equals(res.getResultCode())) {
                        String comment = "京东物流单生成失败（" + res.getResultCode() + ":" + res.getResultMessage() + "）REDATA:" + JSONUtil.toJsonStr(res) + ";REQDATA:" + JSONUtil.toJsonStr(req);
                        wuLiuService.weixinAndOaMessageSend(comment, 3, "", WuLiuConstant.ZLX_CH999_ID, MsgTypeEnum.ABNORMAL_NOTICE.getCode(), null);
                    }
                    throw new CustomizeException(res.getResultMessage() + "(" + res.getResultCode() + ")");

                }
            }

        } catch (Exception e) {
            log.error("京东快递生成异常{}", e.getMessage(), e);
            result.setMsg(e.getMessage());
        }

        return result;
    }

}
