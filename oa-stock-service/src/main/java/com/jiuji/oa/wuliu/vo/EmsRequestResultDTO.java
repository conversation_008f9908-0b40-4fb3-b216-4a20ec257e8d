package com.jiuji.oa.wuliu.vo;


import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.experimental.Accessors;
import org.codehaus.jackson.annotate.JsonProperty;

import java.io.Serializable;

/**
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-16
 */
@Data
@Accessors(chain = true)
public class EmsRequestResultDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("routeCode")
    @JSONField(name = "routeCode")
    private String routeCode;

    @JsonProperty("markDestinationCode")
    @JSONField(name = "markDestinationCode")
    private String markDestinationCode;

    @JsonProperty("packageCode")
    @JSONField(name = "packageCode")
    private String packageCode;

    @JsonProperty("waybill_no")
    @JSONField(name = "waybill_no")
    private String waybillNo;

    @JsonProperty("packageCodeName")
    @JSONField(name = "packageCodeName")
    private String packageCodeName;

    @JsonProperty("markDestinationName")
    @JSONField(name = "markDestinationName")
    private String markDestinationName;

}
