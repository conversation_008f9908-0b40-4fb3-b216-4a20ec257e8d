/*
 *    Copyright © 2006 - 2020 九机网 All Rights Reserved
 *
 */

package com.jiuji.oa.wuliu.vo.res;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 物流单分类查询响应数据
 *
 * <AUTHOR>
 * @date 2021-03-11 10:09:22
 */
@Data
@ApiModel(value = "物流单分类查询响应数据")
@AllArgsConstructor
@NoArgsConstructor
public class WuLiuCategoryResVO {


    @JsonProperty(value = "cateId")
    @JSONField(name = "cateId")
    private Integer cateId;

    @JsonProperty(value = "code")
    @JSONField(name = "code")
    private String code;

    @JsonProperty(value = "pId")
    @JSONField(name = "pId")
    private String pId;

    @JsonProperty(value = "leve")
    @JSONField(name = "leve")
    private Integer leve;

    @JsonProperty(value = "name")
    @JSONField(name = "name")
    private String cateName;

    @JsonProperty(value = "catRank")
    @JSONField(name = "catRank")
    private Integer catRank;


}


