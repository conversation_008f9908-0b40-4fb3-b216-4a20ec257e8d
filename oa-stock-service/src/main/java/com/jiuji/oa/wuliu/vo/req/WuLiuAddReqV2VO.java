package com.jiuji.oa.wuliu.vo.req;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 物流单新增 req VO
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-08
 */
@ApiModel(description = "物流单新增 req VO")
@Data
@Accessors(chain = true)
public class WuLiuAddReqV2VO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * (发送方)姓名
     */
    @ApiModelProperty("(发送方)姓名")
    @JsonProperty("sname")
    @JSONField(name = "sname")
    private String sname;

    /**
     * (发送方)手机号
     */
    @ApiModelProperty("(发送方)手机号")
    @NotBlank(message = "寄件人手机不能为空")
    @JsonProperty("smobile")
    @JSONField(name = "smobile")
    private String smobile;

    /**
     * (发送方)地址
     */
    @ApiModelProperty("(发送方)地址")
    @JsonProperty("saddress")
    @JSONField(name = "saddress")
    @Length(max = 250, message = "寄件方地址不能超过 250 个字符")
    private String saddress;

    /**
     * (发送方)城市ID（did）
     */
    @ApiModelProperty("(发送方)城市ID（did）")
    @JsonProperty("scityid")
    @JSONField(name = "scityid")
    private Integer scityid;

    /**
     * (接收方)姓名
     */
    @ApiModelProperty("(接收方)姓名")
    @JsonProperty("rname")
    @JSONField(name = "rname")
    private String rname;

    /**
     * (接收方)手机号
     */
    @ApiModelProperty("(接收方)手机号")
    @NotBlank(message = "收件人手机不能为空")
    @JsonProperty("rmobile")
    @JSONField(name = "rmobile")
    private String rmobile;

    /**
     * (接收方)地址
     */
    @ApiModelProperty("(接收方)地址")
    @JsonProperty("raddress")
    @JSONField(name = "raddress")
    @Length(max = 250, message = "收件方地址不能超过 250 个字符")
    private String raddress;

    /**
     * (接收方)城市ID（did）
     */
    @ApiModelProperty("(接收方)城市ID（did）")
    @JsonProperty("rcityid")
    @JSONField(name = "rcityid")
    private Integer rcityid;

    /**
     * 价格
     */
    @ApiModelProperty("价格")
    @JsonProperty("price")
    @JSONField(name = "price")
    private BigDecimal price;

    /**
     * 成本价
     */
    @ApiModelProperty("成本价")
    @JsonProperty("inprice")
    @JSONField(name = "inprice")
    private BigDecimal inPrice;

    /**
     * 重量
     */
    @ApiModelProperty("重量")
    @JsonProperty("weight")
    @JSONField(name = "weight")
    private BigDecimal weight;

    /**
     * linkType=2 || linkType=3 预约单
     * wuType=4 || wuType=6 订单
     * shouhouid != 0 售后单 ?
     * wuType=9 良品订单
     * wuType=7 && linkType=7 上门回收
     */
    @ApiModelProperty("linkType=2 || linkType=3 预约单\n" +
            "wuType=4 || wuType=6 订单\n" +
            "shouhouid != 0 售后单 ?\n" +
            "wuType=9 良品订单\n" +
            "wuType=7 && linkType=7 上门回收")
    @JsonProperty("danhaobind")
    @JSONField(name = "danhaobind")
    private Integer danHaoBind;

    /**
     * 类别
     */
    @ApiModelProperty("类别")
    @JsonProperty("wutype")
    @JSONField(name = "wutype")
    private Integer wuType;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    @Length(max = 1500, message = "备注不能超过1500字！")
    private String comment;

    /**
     * 第三方快递
     */
    @ApiModelProperty("第三方快递")
    private String com;

    /**
     * 快递单号
     */
    @ApiModelProperty("快递单号")
    private String nu;

    /**
     * 快递细分类型
     */
    private String expressType;

    /**
     * 地区 ID
     */
    @ApiModelProperty("地区 ID")
    @JsonProperty("areaid")
    @JSONField(name = "areaid")
    private Integer areaid;

    /**
     * 寄的地区 ID
     */
    @ApiModelProperty("寄的地区 ID")
    @JsonProperty("sareaid")
    @JSONField(name = "sareaid")
    private Integer sareaid;

    /**
     * 收的地区 ID
     */
    @ApiModelProperty("收的地区 ID")
    @JsonProperty("rareaid")
    @JSONField(name = "rareaid")
    private Integer rareaid;

    /**
     * 物流分类 ID
     */
    @ApiModelProperty("物流分类 ID")
    @JsonProperty("wcateId")
    @JSONField(name = "wcateId")
    private Integer wcateId;

    /**
     * 包裹数
     */
    @ApiModelProperty("包裹数")
    @JsonProperty("packageCount")
    @JSONField(name = "packageCount")
    private Integer packageCount;

    /**
     * 体积
     */
    @ApiModelProperty("体积")
    @JsonProperty("vloumn")
    @JSONField(name = "vloumn")
    private BigDecimal vloumn;

    /**
     * 月结卡号
     */
    private String monthlyCard;

    /**
     * 是否上门揽件
     * 是否通过手持终端通知顺丰收派员上门收件，支持以下值：1：要求 0：不要求
     */
    private Integer isDocall;
    /**
     * 京东：保价金额(保留小数点后两位)
     **/
    private BigDecimal guaranteeValueAmount;
    /**
     * 开始上门揽件时间
     */
    private String sendStartTime;
}
