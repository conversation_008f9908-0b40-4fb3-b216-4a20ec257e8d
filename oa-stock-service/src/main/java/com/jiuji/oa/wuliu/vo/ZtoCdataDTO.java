package com.jiuji.oa.wuliu.vo;


import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * ZtoCdataDTO
 * 中通大头笔数据传输对象
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-16
 */
@Data
@Accessors(chain = true)
public class ZtoCdataDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String unionCode;

    @JsonProperty("send_province")
    @JSONField(name = "send_province")
    private String sendProvince;

    @JsonProperty("send_city")
    @JSONField(name = "send_city")
    private String sendCity;

    @JsonProperty("send_district")
    @JSONField(name = "send_district")
    private String sendDistrict;

    @JsonProperty("send_address")
    @JSONField(name = "send_address")
    private String sendAddress;

    @JsonProperty("receive_province")
    @JSONField(name = "receive_province")
    private String receiveProvince;

    @JsonProperty("receive_city")
    @JSONField(name = "receive_city")
    private String receiveCity;

    @JsonProperty("receive_district")
    @JSONField(name = "receive_district")
    private String receiveDistrict;

    @JsonProperty("receive_address")
    @JSONField(name = "receive_address")
    private String receiveAddress;

}
