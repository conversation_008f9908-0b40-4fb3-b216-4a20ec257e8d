package com.jiuji.oa.wuliu.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2021/11/5 14:46
 */
@Data
public class DadaAddOrderResVO {
    /**
     * 订单号
     */
    private String orderId;
    /**
     * 配送距离（米）
     */
    private BigDecimal distance;
    /**
     * 实际运费(单位：元)，运费减去优惠券费用
     */
    private BigDecimal fee;
    /**
     * 运费(单位：元)
     */
    private BigDecimal deliverFee;
    /**
     * 优惠券费用(单位：元)
     */
    private BigDecimal couponFee;
    /**
     * 小费(单位：元)
     */
    private BigDecimal tips;
    /**
     * 保价费(单位：元)
     */
    private BigDecimal insuranceFee;
}
