package com.jiuji.oa.wuliu.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * SfCreateOrderParam.SfParam
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-16
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class SfParamDTO extends LogisticBaseParamDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("list")
    @JSONField(name = "list")
    private List<SfCreateOrderParamDTO> sfCreateOrderParams;

    @JsonProperty("expressType")
    @JSONField(name = "expressType")
    private Integer expressType;

    @JsonProperty("xTenantId")
    @JSONField(name = "xTenantId")
    private Integer xtenantId;

}
