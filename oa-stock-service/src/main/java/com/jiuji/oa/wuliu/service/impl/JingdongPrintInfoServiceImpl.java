package com.jiuji.oa.wuliu.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.wuliu.bo.WuliuExpressMqBO;
import com.jiuji.oa.wuliu.entity.JingdongPrintInfo;
import com.jiuji.oa.wuliu.mapper.JingdongPrintInfoMapper;
import com.jiuji.oa.wuliu.service.IJingdongPrintInfoService;
import org.springframework.stereotype.Service;

import java.util.Objects;


/**
 * JingdongPrintInfoServiceImpl
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-12-05
 */
@Service
@DS("oanewWrite")
public class JingdongPrintInfoServiceImpl  extends ServiceImpl<JingdongPrintInfoMapper, JingdongPrintInfo> implements IJingdongPrintInfoService {
    /**
     * 京东面单
     *
     * @param jingdongPrintInfoMsg
     */
    @Override
    @DS("oanewWrite")
    public void saveJingdongPrintInfo(WuliuExpressMqBO<JingdongPrintInfo> jingdongPrintInfoMsg) {
        if (Objects.nonNull(jingdongPrintInfoMsg) && Objects.nonNull(jingdongPrintInfoMsg.getData())) {
            this.save(jingdongPrintInfoMsg.getData());
        }
    }
}
