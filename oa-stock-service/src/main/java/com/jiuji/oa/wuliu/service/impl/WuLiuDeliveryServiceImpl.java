package com.jiuji.oa.wuliu.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.stock.common.cache.RedisUtils;
import com.jiuji.oa.wuliu.entity.WuLiuDeliveryEntity;
import com.jiuji.oa.wuliu.mapper.WuLiuDeliveryMapper;
import com.jiuji.oa.wuliu.mapstruct.WuLiuDeliveryMapStruct;
import com.jiuji.oa.wuliu.service.IWuLiuDeliveryService;
import com.jiuji.tc.utils.enums.EnumVO;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * 物流单配送方式 ServiceImpl
 *
 * <AUTHOR> @date 2021-11-19
 */
@Service
@DS("ch999oanew")
public class WuLiuDeliveryServiceImpl extends ServiceImpl<WuLiuDeliveryMapper, WuLiuDeliveryEntity> implements IWuLiuDeliveryService {

    private static final String WULIU_DELIVERY_LIST_ALL_REDIS_KEY = "oa:stock:wuliu:delivery:listAll";

    /**
     * 获取所有物流单配送方式
     *
     * @return List<WuLiuDeliveryEntity>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-19
     */
    @Override
    public List<WuLiuDeliveryEntity> listAll() {
        List<WuLiuDeliveryEntity> list;
        if (RedisUtils.hasKey(WULIU_DELIVERY_LIST_ALL_REDIS_KEY)) {
            list = JSON.parseArray(JSON.parse(RedisUtils.get(WULIU_DELIVERY_LIST_ALL_REDIS_KEY)).toString(), WuLiuDeliveryEntity.class);
        } else {
            list = lambdaQuery().list();
            RedisUtils.set(WULIU_DELIVERY_LIST_ALL_REDIS_KEY, list);
        }
        return Optional.ofNullable(list).orElseGet(ArrayList::new);
    }

    /**
     * 获取所有物流单配送方式枚举
     *
     * @return List<EnumVO>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-19
     */
    @Override
    public List<EnumVO> listAllEnumVo() {
        return listAll().stream()
                .map(WuLiuDeliveryMapStruct.INSTANCE::toEnumVo)
                .collect(Collectors.toList());
    }

}
