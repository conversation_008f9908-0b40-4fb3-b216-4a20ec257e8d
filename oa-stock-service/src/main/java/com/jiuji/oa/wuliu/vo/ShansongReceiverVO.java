package com.jiuji.oa.wuliu.vo;

import com.jiuji.oa.wuliu.enums.ShansongGoodsTypeEnum;
import lombok.Data;

import java.math.BigDecimal;

/**
 * C# Receiver
 *
 * <AUTHOR>
 * @date 2021/11/8 15:28
 */
@Data
public class ShansongReceiverVO {
    /**
     * 第三方平台流水号
     */
    private String orderNo;

    /**
     * 收件地址
     */
    private String toAddress;

    /**
     * 收件详细地址
     */
    private String toAddressDetail;

    /**
     * 收件纬度
     */
    private String toLatitude;

    /**
     * 收件经度
     */
    private String toLongitude;

    /**
     * 收件联系人
     */
    private String toReceiverName;

    /**
     * 收件人手机号
     */
    private String toMobile;

    /**
     * 物品类型
     */
    private ShansongGoodsTypeEnum goodType;

    /**
     * 物品重量
     */
    private BigDecimal weight;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 小费
     */
    private Integer additionFee;

    /**
     * 保险费用
     */
    private Integer insurance;

    /**
     * 保险产品ID
     */
    private String insuranceProId;

    /**
     * 物品来源
     */
    private Integer orderingSourceType;

    /**
     * 物品来源流水号
     */
    private String orderingSourceNo;
}
