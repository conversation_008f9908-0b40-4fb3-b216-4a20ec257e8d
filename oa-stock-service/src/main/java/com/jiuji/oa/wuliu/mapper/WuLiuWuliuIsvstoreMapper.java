package com.jiuji.oa.wuliu.mapper;

import com.jiuji.oa.wuliu.entity.WuLiuWuliuIsvstoreEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 *  Mapper 接口
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-12-15
 */
@Mapper
public interface WuLiuWuliuIsvstoreMapper extends BaseMapper<WuLiuWuliuIsvstoreEntity> {

    /**
     * getWuLiuIsvstoreByWuliuId
     *
     * @param wuliuId
     * @return WuLiuWuliuIsvstoreEntity
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-15
     */
    WuLiuWuliuIsvstoreEntity getWuLiuIsvstoreByWuliuId(@Param("wuliuId") Integer wuliuId);

}
