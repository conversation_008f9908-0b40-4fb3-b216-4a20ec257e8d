package com.jiuji.oa.wuliu.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 转售单（良品订单）,责任小组：回收 Entity
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-08
 */
@Data
@Accessors(chain = true)
@TableName("recover_marketInfo")
public class WuLiuRecoverMarketInfoEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单号
     */
    private Long subId;

    /**
     *
     */
    private LocalDateTime subDate;

    /**
     *
     */
    private Integer subCheck;

    /**
     *
     */
    private BigDecimal price;

    /**
     * 收货人
     */
    private String subTo;

    /**
     * 收货人电话
     */
    private String subTel;

    /**
     * 支付方式
     */
    private Integer subPay;

    private Integer basketCount;

    /**
     * 备注信息
     */
    private String comment;

    /**
     * 添加人
     */
    @TableField("Inuser")
    private String inUser;

    /**
     * 收货人手机号
     */
    private String subMobile;

    /**
     * 销货单打印次数
     */
    @TableField("printxcount")
    private String printCount;

    /**
     * 地区
     */
    private String area;

    /**
     * 自提点 ID
     */
    @TableField("zitidianID")
    private Integer ziTiDianId;

    /**
     * 会员 ID
     */
    @TableField("userid")
    private Long userId;

    /**
     * 无用字段
     */
    private String onlinePay;

    /**
     * 营销活动 ID
     */
    @TableField("Marketingid")
    private Integer marketingId;

    /**
     * 是否开过发票
     */
    @TableField("ispiao")
    private Boolean piaoFlag;

    /**
     * 订单类型
     */
    @TableField("subtype")
    private Integer subType;

    /**
     * 配送方式
     */
    private Integer delivery;

    /**
     * 应付金额
     */
    @TableField("yingfuM")
    private BigDecimal yingFuMoney;

    /**
     * 已付金额
     */
    @TableField("yifuM")
    private BigDecimal yiFuMoney;

    /**
     * 运费
     */
    @TableField("feeM")
    private BigDecimal feeMoney;

    /**
     * 优惠码
     */
    @TableField("youhui1M")
    private BigDecimal youHui1Money;

    /**
     * 手续费
     */
    @TableField("shouxuM")
    private BigDecimal shouXuMoney;

    /**
     * 积点支付金额
     */
    @TableField("jidianM")
    private BigDecimal jiDianMoney;

    /**
     * 出库时间
     */
    @TableField("tradeDate")
    private LocalDateTime tradeDate;

    /**
     * 交易时间
     */
    @TableField("tradeDate1")
    private LocalDateTime tradeDate1;

    /**
     * 定金
     */
    @TableField("dingjing")
    private BigDecimal dingJing;

    /**
     * 主单号
     */
    @TableField("subPID")
    private Integer subPid;

    /**
     * 交易人员
     */
    private String trader;

    /**
     * 返款
     */
    @TableField("fankuan")
    private BigDecimal fanKuan;

    /**
     * 返款
     */
    @TableField("saleType")
    private Integer saleType;

    /**
     * 门店 ID
     */
    @TableField("areaid")
    private Integer areaId;

    /**
     * 快递单
     */
    @TableField("kuaididan")
    private String kuaiDiDan;

    /**
     * 九机币
     */
    @TableField("coinM")
    private BigDecimal coinMoney;

    /**
     * 订单是否锁定
     */
    @TableField("islock")
    private Integer lockFlag;

    /**
     * 渠道名称
     */
    @TableField("qudaoname")
    private String quDaoName;

    /**
     * 发货状态
     */
    @TableField("isfahuo")
    private Integer isfahuo;

    /**
     * 预计送达时间
     */
    @TableField("expectTime")
    private LocalDateTime expectTime;

    /**
     * 新订单号
     */
    @TableField("newSubId")
    private Integer newSubId;

    /**
     *
     */
    @TableField("subApartDate")
    private LocalDateTime subApartDate;

    /**
     *
     */
    @TableField("returnDate")
    private LocalDateTime returnDate;

    /**
     *
     */
    @TableField("voucherId")
    private Integer voucherId;

    /**
     *
     */
    @TableField("recover_marketInfo_rv")
    private Byte[] recoverMarketInfoRv;

}
