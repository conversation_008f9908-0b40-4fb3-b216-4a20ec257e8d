package com.jiuji.oa.wuliu.service.impl;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.wuliu.bo.WuliuExpressMqBO;
import com.jiuji.oa.wuliu.entity.JingdongPrintInfo;
import com.jiuji.oa.wuliu.entity.SaasJingdongPrintInfo;
import com.jiuji.oa.wuliu.mapper.SaasJingdongPrintInfoMapper;
import com.jiuji.oa.wuliu.service.ISaasJingdongPrintInfoService;
import org.springframework.stereotype.Service;

import java.util.Objects;


/**
 * SassJingdongPrintInfoServiceImpl
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-12-05
 */
@Service
@DS("oanewWrite")
public class SassJingdongPrintInfoServiceImpl extends ServiceImpl<SaasJingdongPrintInfoMapper, SaasJingdongPrintInfo> implements ISaasJingdongPrintInfoService {

    /**
     * 京东面单
     *
     * @param jingdongPrintInfoMsg
     */
    @Override
    @DS("oanewWrite")
    public void saveSaasJingdongPrintInfo(WuliuExpressMqBO<JingdongPrintInfo> jingdongPrintInfoMsg) {
        if (Objects.nonNull(jingdongPrintInfoMsg) && Objects.nonNull(jingdongPrintInfoMsg.getData())) {
            JingdongPrintInfo data = jingdongPrintInfoMsg.getData();
            SaasJingdongPrintInfo saasJingdongPrintInfo = new SaasJingdongPrintInfo();
            saasJingdongPrintInfo.setDeliveryId(data.getDeliveryId());
            saasJingdongPrintInfo.setSaasAreaid(data.getAreaId());
            saasJingdongPrintInfo.setSaasTenant(0);
            saasJingdongPrintInfo.setExpressOperationMode(data.getExpressOperationMode());
            saasJingdongPrintInfo.setPreSortResult(data.getPreSortResult());
            saasJingdongPrintInfo.setPromiseTimeType(data.getPromiseTimeType());
            saasJingdongPrintInfo.setTransType(data.getTransType());
            saasJingdongPrintInfo.setNeedRetry(data.getNeedRetry());
            saasJingdongPrintInfo.setOrderId(data.getOrderId());
            this.save(saasJingdongPrintInfo);
        }
    }
}
