package com.jiuji.oa.wuliu.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.nc.common.constant.RedisKeys;
import com.jiuji.oa.nc.common.db.MyDynamicRoutingDataSource;
import com.jiuji.oa.nc.dict.bo.WuliuDistributionCostConfigBo;
import com.jiuji.oa.nc.dict.enums.ConfigEnum;
import com.jiuji.oa.nc.dict.service.ISysConfigService;
import com.jiuji.oa.wuliu.entity.WuLiuEntity;
import com.jiuji.oa.wuliu.entity.WuliuExtendInfo;
import com.jiuji.oa.wuliu.mapper.WuliuExtendInfoMapper;
import com.jiuji.oa.wuliu.service.WuliuExtendInfoService;
import com.jiuji.tc.utils.constants.NumberConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 *
 */
@Service
@Slf4j
@DS("oanewWrite")
public class WuliuExtendInfoServiceImpl extends ServiceImpl<WuliuExtendInfoMapper, WuliuExtendInfo>
implements WuliuExtendInfoService{
    @Resource
    private ISysConfigService sysConfigService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 保存或者更新
     *
     * @param wuliuExtendInfo
     * @return
     */
    @Override
    public boolean saveOrUpdateExtendInfo(WuliuExtendInfo wuliuExtendInfo) {
        WuliuExtendInfo info = this.baseMapper.queryWuliuExtendInfoByWuliuId(wuliuExtendInfo.getWuliuId());
        if (Objects.isNull(info)) {
            return this.save(wuliuExtendInfo);
        } else {
            wuliuExtendInfo.setId(info.getId());
            wuliuExtendInfo.setUpdateTime(LocalDateTime.now());
            return this.updateById(wuliuExtendInfo);
        }
    }

    /**
     * 清除距离成本信息
     *
     * @param id
     * @return
     */
    @Override
    public boolean clearInfoByWuLiuId(Integer id) {
        return this.lambdaUpdate()
                .set(WuliuExtendInfo::getDistance, null)
                .set(WuliuExtendInfo::getSendPosition, null)
                .set(WuliuExtendInfo::getReceivePosition, null)
                .set(WuliuExtendInfo::getDistributionCost, null)
                .eq(WuliuExtendInfo::getWuliuId, id).update();
    }

    /**
     * 物流单查询扩展信息
     *
     * @param wuliuId
     * @return
     */
    @Override
    public WuliuExtendInfo queryWuliuExtendInfoByWuliuId(Integer wuliuId) {
        return this.baseMapper.queryWuliuExtendInfoByWuliuId(wuliuId);
    }

    /**
     * 查询物流单骑行成本配置
     *
     * @param distance
     */
    @Override
    public BigDecimal queryDistributionCost(Long distance) {
        if (distance == null || distance == 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal distributionCost = new BigDecimal(NumberConstant.NINETY_NINE);
        String cacheKey = StrUtil.format("{}_{}", RedisKeys.WULIU_DISTRIBUTION_COST,
                MyDynamicRoutingDataSource.isTaxModel());
        String cacheString = stringRedisTemplate.opsForValue().get(cacheKey);
        List<WuliuDistributionCostConfigBo> wuliuDistributionCostConfigBos = null;
        if (StringUtils.isNotBlank(cacheString)) {
            wuliuDistributionCostConfigBos = JSONUtil.toList(stringRedisTemplate.opsForValue().get(cacheKey), WuliuDistributionCostConfigBo.class);
        }
        if (CollectionUtils.isEmpty(wuliuDistributionCostConfigBos)) {
            List<String> configList = sysConfigService.getValueByCode(ConfigEnum.WULIU_DISTRIBUTION_COST.getCode());
            wuliuDistributionCostConfigBos = configList.stream().filter(StringUtils::isNotBlank).map(v -> JSONUtil.toBean(v, WuliuDistributionCostConfigBo.class)).collect(Collectors.toList());
            stringRedisTemplate.opsForValue().set(cacheKey, JSONUtil.toJsonStr(wuliuDistributionCostConfigBos), NumberConstant.ONE, TimeUnit.DAYS);
        }
        if (CollectionUtils.isNotEmpty(wuliuDistributionCostConfigBos)) {
            for (WuliuDistributionCostConfigBo configBo : wuliuDistributionCostConfigBos) {
                if (distance > configBo.getStartRange() * NumberConstant.ONE_THOUSAND && distance <= configBo.getEndRange() * NumberConstant.ONE_THOUSAND) {
                    distributionCost = configBo.getPrice();
                    break;
                }
            }
        }
        return distributionCost;
    }

    /**
     * 查询未计算骑行距离的物流单
     *
     * @param days
     * @return
     */
    @Override
    @DS("ch999oanew")
    public List<WuLiuEntity> queryWuliuByNotDistributionCost(Integer days) {
        return this.baseMapper.queryWuliuByNotDistributionCost(days);
    }

    /**
     * 查询物流单
     *
     * @param days
     * @return
     */
    @Override
    @DS("ch999oanew")
    @Cached(name = "stock:wuliuExtendInfoService:queryWuliuIdByDtime", expire = 1, timeUnit = TimeUnit.DAYS, cacheType = CacheType.REMOTE)
    public Integer queryWuliuIdByDtime(Integer days) {
        return this.baseMapper.queryWuliuIdByDtime(days);
    }
}




