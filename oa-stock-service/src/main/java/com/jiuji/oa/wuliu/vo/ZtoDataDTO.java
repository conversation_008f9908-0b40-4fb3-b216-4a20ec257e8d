package com.jiuji.oa.wuliu.vo;


import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * ZtoCdataDTO
 * 中通大头笔数据传输对象
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-16
 */
@Data
@Accessors(chain = true)
public class ZtoDataDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String partner;
    private String datetime;
    private OrderGroupDTO content;
    private String verify;

}
