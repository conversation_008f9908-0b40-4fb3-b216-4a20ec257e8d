package com.jiuji.oa.wuliu.component;

import cn.hutool.json.JSONUtil;
import com.ch999.common.util.utils.Exceptions;
import com.jiuji.oa.nc.common.config.rabbitmq.RabbitMqConfig;
import com.jiuji.oa.wuliu.bo.WuliuInvalidBO;
import com.jiuji.oa.wuliu.service.IWuLiuBusService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2023/2/2 15:50
 */
@Slf4j
@Component
public class WuliuListener {
    @Resource
    private IWuLiuBusService wuLiuBusService;

    @RabbitListener(queues = RabbitMqConfig.QUEUE_WULIU_INVALID, containerFactory = "oaAsyncLimitContainerFactory")
    public void handleWuliuInvalidMessage(Message message) {
        try {
            String msg = new String(message.getBody(), StandardCharsets.UTF_8);
            log.info("mq物流单作废消息：{}", msg);
            if (StringUtils.isEmpty(msg)) {
                log.error("从rabbitmq获取快递消息为空！");
                return;
            }
            WuliuInvalidBO wuliuInvalid = JSONUtil.toBean(msg, WuliuInvalidBO.class);
            wuLiuBusService.wuliuInvalid(wuliuInvalid);
        } catch (Exception e) {
            log.error("物流单作废mq异常：{}", Exceptions.getStackTraceAsString(e));
        }
    }
}
