package com.jiuji.oa.wuliu.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.jiuji.cloud.logistics.vo.request.CreateOrderReq;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;


/**
 * CreateShunfengLaasOrderReq
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2022-01-13
 */
@Setter
@Getter
@ToString
public class CreateShunfengLaasOrderReq extends CreateOrderReq {

    /**
     * 是否通过手持终端通知顺丰收派员上门收件，支持以下值：1：要求 0：不要求
     */
    @JsonProperty("isDocall")
    @JSONField(name = "isDocall")
    private Integer isDocall;

}
