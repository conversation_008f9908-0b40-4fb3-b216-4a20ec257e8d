package com.jiuji.oa.wuliu.mapstruct;

import com.jiuji.oa.wuliu.dto.res.WuLiuNoteRes;
import com.jiuji.oa.wuliu.entity.WuLiuNote;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * 物流管理界面结构映射
 *
 * <AUTHOR>
 * @date 2021/10/08
 */
@Mapper(componentModel = "spring",unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface WuLiuNoteMapStruct {
    /**
     * entity列表映射res列表
     *
     * @param wuLiuNoteList 物流管理页面列表
     * @return {@link List}<{@link WuLiuNoteRes}>
     */
    List<WuLiuNoteRes>wuLiuNoteListToWuLiuNoteResList(List<WuLiuNote> wuLiuNoteList);
}
