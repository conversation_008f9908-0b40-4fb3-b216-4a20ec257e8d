package com.jiuji.oa.wuliu.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 中通面单信息
 * @TableName wuliu_zto_bill_info
 */
@TableName(value ="wuliu_zto_bill_info")
@Accessors(chain = true)
@Data
public class ZtoBillInfo implements Serializable {
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 物流单id
     */
    private Long wuliuid;

    /**
     * 运单号
     */
    private String billCode;

    /**
     * 大头笔
     */
    private String bigMark;

    /**
     * 创建时间
     */
    private LocalDate createTime;

    /**
     * 更新时间
     */
    private LocalDate updateTime;

    /**
     * 逻辑删除标识
     */
    @TableField(value = "is_del")
    @TableLogic
    private Boolean delFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}