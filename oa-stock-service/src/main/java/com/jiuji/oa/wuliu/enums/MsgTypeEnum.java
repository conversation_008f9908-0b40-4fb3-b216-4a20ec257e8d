package com.jiuji.oa.wuliu.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * MsgTypeEnum
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-12-05
 */
@Getter
@AllArgsConstructor
public enum MsgTypeEnum {

    /**
     * 系统通知
     */
    SYSTEM_NOTICE(0, "系统通知"),

    /**
     * 订单通知
     */
    ORDER_NOTICE(1,"订单通知"),

    /**
     * 良品通知
     */
    SECONDHAND_NOTICE(2,"良品通知"),

    /**
     * 物流通知
     */
    LOGISTICS_NOTICE(5,"物流通知"),

    /**
     * 异常通知
     */
    ABNORMAL_NOTICE(7,"异常通知");

    private final Integer code;

    private final String message;

}
