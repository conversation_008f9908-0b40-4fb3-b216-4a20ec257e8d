package com.jiuji.oa.wuliu.vo;

import com.jiuji.cloud.logistics.vo.base.LogisticsBase;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 说明： 物流订单创建公共 Res （支持：美团）
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class LogisticsBaseResVO extends LogisticsBase {
    private Object jingdong;
    private Extension extension;
    private String lwbNo;
    private String sendAddress;
    private String receiveAddress;
    private String wayBill;
    private List<String> subWayBillList;

    @Data
    public static class Extension {
        /**
         * 顺丰Laas
         * 客户订单号
         */
        private String orderId;
        /**
         * 顺丰Laas
         * 原寄地区域代码，可用于顺丰
         */
        private String originCode;
        /**
         * 目的地区域代码，可用于顺丰电子运单标签打印
         */
        private String destCode;
        /**
         * 打单时的路由标签信息如果是大网的路由标签,这里的值是目的地网点代码,如果是同城配的路由标签,这里的值是根据同城配的设置映射出来的值,不同的配置结果会不一样,不能根据-符号切分(如:上海同城配,可能是:集散点-目的地网点-接驳点,也有可能是目的地网点代码-集散点-接驳点)
         */
        private String destRouteLabel;
        /**
         * 二维码
         * 根据规则生成字符串信息,格式为MMM={‘k1’:’(目的地中转场代码)’,‘k2’:’(目的地原始网点代码)’,‘k3’:’(目的地单元区域)’,‘k4’:’(附件通过三维码(express_type_code、 limit_type_code、 cargo_type_code)映射时效类型)’,‘k5’:’(运单号)’,‘k6’:’(AB标识)’,‘k7’:’(校验码)’}
         */
        private String twoDimensionCode;
    }

}
