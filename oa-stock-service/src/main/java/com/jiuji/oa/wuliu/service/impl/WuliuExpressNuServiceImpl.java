package com.jiuji.oa.wuliu.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.ch999.common.util.atlas.AtlasUtil;
import com.ch999.common.util.atlas.CoordinateUtil;
import com.ch999.common.util.utils.Exceptions;
import com.ch999.common.util.vo.atlas.Coordinate;
import com.fasterxml.jackson.core.type.TypeReference;
import com.jiuji.cloud.logistics.enums.DePonTransportTypeEnum;
import com.jiuji.cloud.logistics.vo.base.CreateOrderNoBase;
import com.jiuji.cloud.logistics.vo.request.CreateOrderBatchReq;
import com.jiuji.cloud.logistics.vo.request.CreateOrderByShop;
import com.jiuji.cloud.logistics.vo.request.CreateOrderReq;
import com.jiuji.cloud.logistics.vo.request.OrderProductDetailReq;
import com.jiuji.oa.nc.common.config.rabbitmq.RabbitMqConfig;
import com.jiuji.oa.nc.common.exception.CustomizeException;
import com.jiuji.oa.nc.common.util.CommonUtil;
import com.jiuji.oa.nc.common.util.NumUtil;
import com.jiuji.oa.nc.dict.service.ISysConfigService;
import com.jiuji.oa.nc.oaapp.po.SysConfig;
import com.jiuji.oa.nc.stock.enums.AreaAttributeEnum;
import com.jiuji.oa.nc.user.po.Areainfo;
import com.jiuji.oa.nc.user.po.Ch999User;
import com.jiuji.oa.nc.user.service.Ch999UserService;
import com.jiuji.oa.nc.user.service.IAreaInfoService;
import com.jiuji.oa.orginfo.areainfo.vo.res.AreaListRes;
import com.jiuji.oa.stock.common.util.*;
import com.jiuji.oa.stock.logisticscenter.enums.LogisticsExpressTypeEnum;
import com.jiuji.oa.stock.logisticscenter.serive.ILogisticsExpressService;
import com.jiuji.oa.stock.logisticscenter.utils.JsonParseUtil;
import com.jiuji.oa.stock.logisticscenter.vo.LogisticsBase;
import com.jiuji.oa.stock.logisticscenter.vo.res.BigMarkInfoRes;
import com.jiuji.oa.stock.logisticscenter.vo.res.CreateOrderResV2;
import com.jiuji.oa.stock.nationalSupplement.res.NationalSupplementKindRes;
import com.jiuji.oa.stock.nationalSupplement.service.NationalSupplementService;
import com.jiuji.oa.stock.shouhou.enums.OrderSubTypeEnum;
import com.jiuji.oa.stock.sub.entity.Sub;
import com.jiuji.oa.stock.sub.service.ISubService;
import com.jiuji.oa.wuliu.bo.DiaoboPaotuiWuliuBO;
import com.jiuji.oa.wuliu.bo.WuliuExpressBO;
import com.jiuji.oa.wuliu.bo.WuliuExpressMqBO;
import com.jiuji.oa.wuliu.component.WuliuMqProducer;
import com.jiuji.oa.wuliu.constant.DadaAppConstant;
import com.jiuji.oa.wuliu.constant.DadaUrlConstant;
import com.jiuji.oa.wuliu.constant.WuLiuConstant;
import com.jiuji.oa.wuliu.constant.WuliuExpressConstant;
import com.jiuji.oa.wuliu.dada.DadaApiResponse;
import com.jiuji.oa.wuliu.dada.DadaRequestClient;
import com.jiuji.oa.wuliu.dto.OnlineNationalSupplementStockDTO;
import com.jiuji.oa.wuliu.dto.ShunfengCustomizedServiceParam;
import com.jiuji.oa.wuliu.dto.req.SubPositionReq;
import com.jiuji.oa.wuliu.entity.*;
import com.jiuji.oa.wuliu.enums.WuLiuTypeEnum;
import com.jiuji.oa.wuliu.mapstruct.WuLiuExpressMapStruct;
import com.jiuji.oa.wuliu.service.*;
import com.jiuji.oa.wuliu.utils.WuliuAddressUtil;
import com.jiuji.oa.wuliu.utils.WuliuUtil;
import com.jiuji.oa.wuliu.vo.*;
import com.jiuji.oa.wuliu.vo.req.WuLiuAddOrUpdateReqVO;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.foundation.message.send.service.MessageSendService;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.common.build.LambdaBuild;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.AmqpException;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/1/9 10:34
 */
@Slf4j
@Service
public class WuliuExpressNuServiceImpl implements IWuliuExpressNuService {
    private static final String CH999USER_IDS = "13682";
    @Resource
    private IAreaInfoService areaInfoService;
    @Resource
    private IWuLiuBasketService wuLiuBasketService;
    @Resource
    private IWuLiuRecoverMarketInfoService wuLiuRecoverMarketInfoService;
    @Resource
    private ILogisticsExpressService logisticsExpressService;
    @Resource
    private RabbitTemplate rabbitTemplate;
    @Resource
    private IWuLiuWuliuwangdianService wuLiuWuliuwangdianService;
    @Resource
    private MessageSendService messageSendService;
    @Resource
    private IExpressEnumService expressEnumService;
    @Resource
    private WuliuMqProducer wuliuMqProducer;
    @Resource
    private ISubService subService;
    @Resource
    private Ch999UserService ch999UserService;
    @Resource
    private IWuliuAddressService wuliuAddressService;
    @Resource
    private ISysConfigService sysConfigService;
    @Resource
    private WuLiuExpressMapStruct wuliuExpressMapStruct;

    /**
     * 批量创建快递
     * @param req
     * @return
     */
    @Override
    public GenerateMoreWuliuNoRes generateMoreWuliuNo (WuliuExpressBO req) {
        GenerateMoreWuliuNoRes generateWuliuNoRes = new GenerateMoreWuliuNoRes();
        boolean isSendMsgType = !Objects.equals(0, req.getSendMsgType());
        WuLiuEntity wuliu = req.getWuliu();
        int count = Optional.ofNullable(req.getPackageCount()).orElse(1);

        IWuLiuService wuLiuService = SpringUtil.getBean(IWuLiuService.class);
        //特殊业务处理，特殊地区，寄件人强制 开始
        String nu = "";
        if (StringUtils.isNotBlank(wuliu.getNu())) {
            generateWuliuNoRes.setNu(wuliu.getNu());
            return generateWuliuNoRes;
        }
        // 特殊业务处理，特殊地区，寄件人强制 开始
        // 如果是DC，并且是订单派送
        AreaSubjectVO areaSubject = wuLiuService.getAreaSubject(wuliu.getSAreaId());
        Areainfo areaInfo = Optional.ofNullable(areaInfoService.getAreaInfoByAreaId2(wuliu.getSAreaId())).orElseGet(Areainfo::new);
        String sname = "";
        String smoblie = "";
        if (Objects.equals(wuliu.getSAreaId(), WuLiuConstant.AREA_DC)) {
            sname = areaSubject.getPrintName();
            smoblie = WuLiuConstant.DC_S_MOBLIE;
        }
        // 如果是 H1
        if (Objects.equals(wuliu.getSAreaId(), WuLiuConstant.AREA_H1)) {
            // 易涛的电话
            sname = areaSubject.getPrintName();
            smoblie = "18860789139";
        }
        // 如果是 dc1
        if (Objects.equals(wuliu.getSAreaId(), WuLiuConstant.AREA_DC1)) {
            // 陶彪的电话
            sname = areaSubject.getPrintName();
            smoblie = "18508501195";
        }
        // 如果是 dc2
        if (Objects.equals(wuliu.getSAreaId(), WuLiuConstant.AREA_DC2)) {
            // 缪青的电话
            sname = areaSubject.getPrintName();
            smoblie = "18613223881";
        }
        // sz
        if (Objects.equals(wuliu.getSAreaId(), WuLiuConstant.AREA_SZ)) {
            sname = areaSubject.getPrintName();
            smoblie = "13760162987";
        }
        if (StringUtils.isBlank(wuliu.getSMobile()) && Objects.equals(wuliu.getWuType(), 4)) {
            sname = areaSubject.getPrintName();
            smoblie = areaInfo.getCompanyTel1();
        }
        // 特殊业务处理，特殊地区，寄件人强制 结束
        CityIdListDTO jinfo;
        if (Arrays.asList(NumUtil.SEVEN, NumUtil.EIGHT).contains(wuliu.getWuType())) {
            jinfo = wuLiuService.getAreaIdByCityId(wuliu.getSCityId(), 1);
            jinfo.setAddress(wuliu.getSAddress());
        } else {
            jinfo = wuLiuService.getAreaInfoByArea(wuliu.getSAreaId());
        }
        CityIdListDTO cittInfo;
        if (Objects.equals(wuliu.getWuType(), 1)) {
            cittInfo = wuLiuService.getAreaInfoByArea(wuliu.getRAreaId());
        } else {
            cittInfo = wuLiuService.getAreaIdByCityId(wuliu.getRCityId(), 1);
        }

        String rName = wuliu.getRName();
        String rname = rName + wuLiuService.getWuliuStatsArea(wuliu.getWuType(), wuliu.getRAreaId(), 1);
        CreateOrderReq createOrderReq = new CreateOrderReq();
        LogisticsBase base = areaInfoService.getAreaName(wuliu.getSAreaId(), wuliu.getRAreaId());
        createOrderReq.setSendShopId(base.getSendShopId());
        createOrderReq.setSendShopName(base.getSendShopName());
        createOrderReq.setReceiveShopId(base.getReceiveShopId());
        createOrderReq.setReceiveShopName(base.getReceiveShopName());
        createOrderReq.setSendStartTime(req.getSendStartTime());
        // 订单备注
        createOrderReq.setRemark("请勿摔打,保持整洁。");
        // 发货人姓名
        createOrderReq.setSenderName(StringUtils.isNotBlank(sname) ? sname : wuliu.getSName());
        // 发货人手机号
        createOrderReq.setSenderMobile(StringUtils.isNotBlank(smoblie) ? smoblie : wuliu.getSMobile());
        createOrderReq.setSenderProvinceName(jinfo.getPname());
        createOrderReq.setSenderCityName(jinfo.getZname());
        createOrderReq.setSenderCountyName(jinfo.getDname());
        // 发货人地址
        String saddress = WuliuAddressUtil.getAddress(StringUtils.isNotBlank(jinfo.getAddress()) ? jinfo.getAddress() : wuliu.getSAddress(), jinfo);
        createOrderReq.setSenderAddress(saddress);
        // 收件人
        createOrderReq.setReceiverName(rname);
        createOrderReq.setReceiverMobile(wuliu.getRMobile());
        // 收件人所在地
        createOrderReq.setReceiverProvinceName(cittInfo.getPname());
        createOrderReq.setReceiverCityName(cittInfo.getZname());
        createOrderReq.setReceiverCountyName(cittInfo.getDname());
        String raddress = WuliuAddressUtil.getAddress(wuliu.getRAddress(), cittInfo);
        // 详细地址
        if (Objects.equals(wuliu.getWuType(), 1) && StringUtils.isBlank(wuliu.getRAddress())) {
            raddress = WuliuAddressUtil.getAddress(cittInfo.getAddress(), cittInfo);
        }
        createOrderReq.setReceiverAddress(raddress);
        //商品信息
        List<SfCargoVO> list = new ArrayList<>();
        if (!Arrays.asList(9, 10, 13).contains(wuliu.getWuType())) {
            // 根据订单获取 bakset
            List<WuLiuBasket2Entity> dr = wuLiuBasketService.getSubBasket(wuliu.getDanHaoBind(), null, null);
            if (CollectionUtils.isNotEmpty(dr)) {
                dr.forEach(item ->
                        //添加到货品列表
                        list.add(new SfCargoVO()
                                // 单价
                                .setAmount(String.valueOf(Optional.ofNullable(item.getPrice()).orElse(BigDecimal.ZERO)))
                                // 商品名称
                                .setCargoName(Optional.ofNullable(item.getProductName()).orElse("").replace("&", ""))
                                // 数量
                                .setCount(String.valueOf(Optional.ofNullable(item.getBasketCount()).orElse(0)))
                                // 单位
                                .setUnit("")));
            }
        } else if (Objects.equals(wuliu.getWuType(), 9)) {
            // 获取二手良品商品信息
            List<WuLiuRecoverMarketInfo2Entity> dr = wuLiuRecoverMarketInfoService.getSubBasket(wuliu.getDanHaoBind(), null);
            if (CollectionUtils.isNotEmpty(dr)) {
                dr.forEach(item ->
                    //添加到货品列表
                    list.add(new SfCargoVO()
                            // 单价
                            .setAmount(String.valueOf(Optional.ofNullable(item.getPrice()).orElse(BigDecimal.ZERO)))
                            // 商品名称
                            .setCargoName(item.getProductName())
                            // 数量
                            .setCount(String.valueOf(Optional.ofNullable(item.getBasketCount()).orElse(0)))    // 单位
                            .setUnit(""))
                );
            }
        }

        // 顺丰逻辑 开始
        if (WuLiuConstant.SHUNFENG_LAAS.equals(wuliu.getCom())) {
            if (StringUtils.isNotBlank(req.getMonthlyCard())) {
                createOrderReq.setMonthlyCard(req.getMonthlyCard());
            } else if (StringUtils.isNotBlank(req.getExpressType()) && req.getExpressType().contains("9386")) {
                createOrderReq.setMonthlyCard("8712269386");
            } else {
                ShunFengCardVO yueJieKaHao = wuLiuService.getYueJieKaHao(wuliu.getSAreaId(), req.getExpressType());
                createOrderReq.setMonthlyCard(yueJieKaHao.getCustId());
            }
            String expressType = "2";
            if (StringUtils.isNotBlank(req.getExpressType()) && req.getExpressType().contains("_")) {
                String[] expressTypeArr = req.getExpressType().split("_");
                expressType = expressTypeArr[0];
            }
            createOrderReq.setXTenantId(0L);
            createOrderReq.setExpressItemQty(1);
            createOrderReq.setDropMenuExpressType(Integer.valueOf(expressType));
            createOrderReq.setIsDocall(req.getIsDocall());
            createOrderReq.setExpressType(LogisticsExpressTypeEnum.SHUN_FENG_LAAS.getExpressType());

            for (int i = 0; i < count; i++) {
                createOrderReq.setOrderNo(wuliu.getId().toString());
                if (i > 0) {
                    createOrderReq.setOrderNo("0");
                }
                String result = logisticsExpressService.createOrder(createOrderReq);
                R<LogisticsBaseResVO> lassResult = JacksonJsonUtils.toClass(result, new TypeReference<R<LogisticsBaseResVO>>() {});
                if (Objects.nonNull(lassResult) && Objects.equals(lassResult.getCode(), 0) && Objects.nonNull(lassResult.getData())) {
                    LogisticsBaseResVO data = lassResult.getData();
                    LogisticsBaseResVO.Extension extension = Optional.ofNullable(data.getExtension()).orElseGet(LogisticsBaseResVO.Extension::new);
                    String destRouteLabel = extension.getDestRouteLabel();
                    String twoDimensionCode = extension.getTwoDimensionCode();
                    if (i == 0) {
                        sendMqMessage(new WuliuExpressMqBO<WuLiuShunfengNoInfoEntity>().setAct(WuliuExpressConstant.ACT_SHUNFENGNOINFO)
                                .setData(new WuLiuShunfengNoInfoEntity()
                                        .setMailNo(data.getWayBill())
                                        .setWuLiuId(wuliu.getId().toString())
                                        .setDestRouteLabel(destRouteLabel)
                                        .setTwoDimensionCode(twoDimensionCode)
                                        .setSareaid(createOrderReq.getSendShopId())
                                        .setCustId(createOrderReq.getMonthlyCard())
                                        .setAddDate(LocalDateTime.now())
                                        .setJMobile(createOrderReq.getSenderMobile())
                                        .setDMobile(createOrderReq.getReceiverMobile())));
                        sendMqMessage(new WuliuExpressMqBO<WuLiuWuliuwangdianEntity>().setAct(WuliuExpressConstant.ACT_WULIUWANGDIAN)
                                .setData(new WuLiuWuliuwangdianEntity()
                                        .setWuliuid(wuliu.getId())
                                        .setOrgcode(extension.getOriginCode())
                                        .setDestcode(extension.getDestCode())
                                        .setExepresstype(wuLiuWuliuwangdianService.getExpressType(req.getExpressType()))
                                        .setPayType("寄付月结")
                                        .setYuejiekahao(req.getMonthlyCard())));
                        nu = data.getWayBill();
                    } else {
                        sendMqMessage(new WuliuExpressMqBO<Wuliunoex>().setAct(WuliuExpressConstant.ACT_WULIUNOEX)
                                .setData(new Wuliunoex()
                                        .setWuliuid(wuliu.getId())
                                        .setNu(data.getWayBill())
                                        .setCom(wuliu.getCom())
                                        .setPackagecount(1)));
                    }
                    wuLiuService.subWuliuTransferLogPush(wuliu.getId(),"系统");
                } else {
                    log.error("生成顺丰快递失败:{}", result);
                    String profile = ProfileUtil.getActiveProfile();
                    messageSendService.sendOaMessage(0, "", CH999USER_IDS, profile + "顺丰批量运单号生成接口错误wuliuId:" + wuliu.getId());
                }
            }
        }
        if (WuLiuConstant.SHUNFENG.equals(wuliu.getCom())) {
            if (StringUtils.isNotBlank(req.getMonthlyCard())) {
                createOrderReq.setMonthlyCard(req.getMonthlyCard());
            } else if (StringUtils.isNotBlank(req.getExpressType()) && req.getExpressType().contains("9386")) {
                createOrderReq.setMonthlyCard("8712269386");
            } else {
                ShunFengCardVO yueJieKaHao = wuLiuService.getYueJieKaHao(wuliu.getSAreaId(), req.getExpressType());
                createOrderReq.setMonthlyCard(yueJieKaHao.getCustId());
                createOrderReq.setIsDocall(doCall(yueJieKaHao.getCustId(), Optional.ofNullable(wuliu.getSAreaId()).orElse(0)));
            }
            CreateOrderBatchReq createOrderBatchReq = new CreateOrderBatchReq();
            CreateOrderNoBase orderNoBase = new CreateOrderNoBase();
            orderNoBase.setReceiveTel(createOrderReq.getReceiverMobile());
            orderNoBase.setSendTel(createOrderReq.getSenderMobile());
            orderNoBase.setReceiveName(createOrderReq.getReceiverName());
            orderNoBase.setSendName(createOrderReq.getSenderName());
            orderNoBase.setReceiveAddress(createOrderReq.getReceiverAddress());
            orderNoBase.setExpressType(2);
            orderNoBase.setSendAddress(createOrderReq.getSenderAddress());
            String productName = list.stream().findFirst().map(SfCargoVO::getCargoName).orElse("电子产品");
            orderNoBase.setProductName(productName);
            orderNoBase.setCustomerOrderNo(wuliu.getId().toString());
            orderNoBase.setPackageNumber(count);
            //线上顺丰国补
            createOrderBatchNationalSupplement(wuliu,orderNoBase);

            if (count > 1) {
                //子母件
                orderNoBase.setBspType(2);
            } else {
                orderNoBase.setBspType(1);
            }
            createOrderBatchReq.setList(Collections.singletonList(orderNoBase));
            createOrderBatchReq.setXTenantId(0L);
            createOrderBatchReq.setExpressType(LogisticsExpressTypeEnum.SHUN_FENG.getExpressType());
            createOrderBatchReq.setSendShopId(createOrderReq.getSendShopId());
            createOrderBatchReq.setReceiveShopId(createOrderReq.getReceiveShopId());
            createOrderBatchReq.setSendShopName(createOrderReq.getSendShopName());
            createOrderBatchReq.setReceiveShopName(createOrderReq.getReceiveShopName());
            createOrderBatchReq.setMonthlyCard(createOrderReq.getMonthlyCard());
            String shunfengResulltStr = logisticsExpressService.createOrderShunfeng9ji(createOrderBatchReq);
            R<Object> objResullt = JacksonJsonUtils.toClass(shunfengResulltStr, new TypeReference<R<Object>>() {});
            if (Objects.isNull(objResullt)) {
                log.warn("生成顺丰快递失败，参数：{}", createOrderBatchReq);
                throw new CustomizeException("顺丰快递订单创建失败，请稍后再试");
            }
            if (!Objects.equals(0, objResullt.getCode())) {
                log.warn("生成顺丰快递失败:参数：{},结果：{}", createOrderBatchReq,shunfengResulltStr);
                throw new CustomizeException(objResullt.getUserMsg());
            }
            R<List<SfCreateOrderResultDTO>> shunfengResullt = JacksonJsonUtils.toClass(shunfengResulltStr, new TypeReference<R<List<SfCreateOrderResultDTO>>>() {});
            if (Objects.isNull(shunfengResullt)) {
                log.warn("生成顺丰快递失败:参数：{},结果：{}", createOrderBatchReq,shunfengResulltStr);
                throw new CustomizeException("顺丰快递订单创建失败，请稍后再试");
            }
            if (Objects.equals(shunfengResullt.getCode(), 0) && CollectionUtils.isNotEmpty(shunfengResullt.getData())) {
                SfCreateOrderResultDTO sfCreateOrderResult = shunfengResullt.getData().get(0);
                WuliuExpressMqBO<WuLiuShunfengNoInfoEntity> wuliuExpressMq = new WuliuExpressMqBO<WuLiuShunfengNoInfoEntity>().setAct(WuliuExpressConstant.ACT_SHUNFENGNOINFO)
                        .setData(new WuLiuShunfengNoInfoEntity()
                                .setMailNo(sfCreateOrderResult.getMainMailNo())
                                .setWuLiuId(wuliu.getId().toString())
                                .setDestRouteLabel(sfCreateOrderResult.getDestRouteLabel())
                                .setTwoDimensionCode(sfCreateOrderResult.getTwoDimensionCode())
                                .setSareaid(wuliu.getSAreaId())
                                .setCustId(createOrderBatchReq.getMonthlyCard())
                                .setAddDate(LocalDateTime.now())
                                .setJMobile(createOrderReq.getSenderMobile())
                                .setCustomizedService(orderNoBase.getCustomizedService())
                                .setDMobile(createOrderReq.getReceiverMobile()));
                sendMqMessage(wuliuExpressMq);

                WuliuExpressMqBO<WuLiuWuliuwangdianEntity> wangdianMq = new WuliuExpressMqBO<WuLiuWuliuwangdianEntity>().setAct(WuliuExpressConstant.ACT_WULIUWANGDIAN)
                        .setData(new WuLiuWuliuwangdianEntity()
                                .setWuliuid(wuliu.getId())
                                .setOrgcode(sfCreateOrderResult.getOriginCode())
                                .setDestcode(sfCreateOrderResult.getDestCode())
                                .setExepresstype(wuLiuWuliuwangdianService.getExpressType(req.getExpressType()))
                                .setPayType("寄付月结")
                                .setYuejiekahao(createOrderBatchReq.getMonthlyCard()));
                sendMqMessage(wangdianMq);
                if (CollUtil.isNotEmpty(sfCreateOrderResult.getSubWaybillNoList())) {
                    for (SubWaybillNoDataRes data : sfCreateOrderResult.getSubWaybillNoList()) {
                        sendMqMessage(new WuliuExpressMqBO<Wuliunoex>().setAct(WuliuExpressConstant.ACT_WULIUNOEX)
                                .setData(new Wuliunoex()
                                        .setWuliuid(wuliu.getId())
                                        .setNu(data.getSubWaybillNo())
                                        .setCom(wuliu.getCom())
                                        .setPackagecount(1)));
                    }
                }
                nu = sfCreateOrderResult.getMainMailNo();
            } else {
                throw new CustomizeException(shunfengResullt.getUserMsg());
            }
        }
        // 顺丰逻辑 结束

        // 中通逻辑 开始
        else if (WuLiuConstant.ZHONGTONG.equals(wuliu.getCom())) {
            for (int i = 0; i < count; i++) {
                if (i > 0) {
                    createOrderReq.setOrderNo("0");
                }
                OrderGroupDTO conmodel = new OrderGroupDTO();
                // 订单号
                conmodel.setId(OptionalUtils.ifNotNull(wuliu.getDanHaoBind(), String::valueOf));
                if (Objects.equals(wuliu.getWuType(), 1)) {
                    conmodel.setId(OptionalUtils.ifNotNull(wuliu.getId(), String::valueOf));
                }
                // 其他物流，单号使用物流单号，先生成物流单，才能生成快运单号
                if (Objects.equals(wuliu.getWuType(), 8) && Objects.equals(wuliu.getDanHaoBind(), 0)) {
                    conmodel.setId("o" + wuliu.getId());
                }
                // 订单备注
                conmodel.setRemark("请勿摔打,保持整洁。");
                // 寄件人信息
                OrderSenderDTO sender = new OrderSenderDTO();
                conmodel.setSender(sender);
                // 发货人所在公司
                conmodel.getSender().setCompany(areaSubject.getPrintName());
                // 发货人姓名
                conmodel.getSender().setName(wuliu.getSName() + wuLiuService.getWuliuStatsArea(wuliu.getWuType(), wuliu.getSAreaId(), 0));
                // 发货人手机号
                conmodel.getSender().setMobile(wuliu.getSMobile());
                // 发货人所在城市
                conmodel.getSender().setCity(jinfo.getPname() + "," + jinfo.getZname() + "," + jinfo.getDname());
                // 发货人地址
                conmodel.getSender().setAddress(jinfo.getAddress());
                // 寄方地址为空，则为填写地址
                if (!StringUtils.isBlank(jinfo.getAddress())) {
                    conmodel.getSender().setAddress(wuliu.getSAddress());
                }

                // 收件人
                OrderReceiverDTO receiver = new OrderReceiverDTO();
                conmodel.setReceiver(receiver);
                conmodel.getReceiver().setName(rname);
                conmodel.getReceiver().setMobile(wuliu.getRMobile());
                // 收件人所在地
                conmodel.getReceiver().setCity(cittInfo.getPname() + "," + cittInfo.getZname() + "," + cittInfo.getDname());
                // 详细地址
                conmodel.getReceiver().setAddress(wuliu.getRAddress());
                if (Objects.equals(wuliu.getWuType(), 1) && StringUtils.isBlank(wuliu.getRAddress())) {
                    conmodel.getReceiver().setAddress(cittInfo.getAddress());
                }
                // 商品信息
                // 根据订单获取 bakset
                conmodel.setOrderSum(BigDecimal.ZERO);
                if (CollectionUtils.isNotEmpty(list)) {
                    BigDecimal total = list.stream().map(v -> new BigDecimal(v.getAmount())).reduce(BigDecimal.ZERO, BigDecimal::add);
                    // 订单总金额
                    conmodel.setOrderSum(total);
                }
                String jsonStr = "";
                try {
                    OrderGroupResultDTO data = null;
                    jsonStr = wuLiuService.createOrderGroup(conmodel, wuliu.getSAreaId());
                    data = JSON.parseObject(jsonStr, OrderGroupResultDTO.class);
                    if (data != null && data.getResult() != null && data.getData() != null) {
                        if (StringUtils.isBlank(data.getData().getBillCode())) {
                            sendMqMessage(new WuliuExpressMqBO<WuLiuLogEntity>().setAct(WuliuExpressConstant.ACT_WULIULOG)
                                    .setData(new WuLiuLogEntity().setWuliuid(wuliu.getId())
                                            .setMsg(expressEnumService.getWuliuCompanyName(wuliu.getCom()) + "运单号生成失败:" + data.getData().getMessage())
                                            .setDtime(LocalDateTime.now())
                                            .setInuser(req.getInUser())));
                            return generateWuliuNoRes;
                        }
                        // 快递单号
                        if (StringUtils.isBlank(nu)) {
                            nu = data.getData().getBillCode();
                            String destcode = wuLiuService.markGetmark(conmodel.getSender().getCity(), conmodel.getSender().getAddress(), conmodel.getReceiver().getCity(), conmodel.getReceiver().getAddress(), data.getData().getBillCode(), false);
                            sendMqMessage(new WuliuExpressMqBO<WuLiuWuliuwangdianEntity>().setAct(WuliuExpressConstant.ACT_WULIUWANGDIAN)
                                    .setData(new WuLiuWuliuwangdianEntity()
                                            .setWuliuid(wuliu.getId())
                                            .setOrgcode(data.getData().getSiteName())
                                            .setDestcode(destcode)
                                            .setExepresstype("")
                                            .setPayType("寄付月结")
                                            .setYuejiekahao("")));
                            wuLiuService.subWuliuTransferLogPush(wuliu.getId(),"系统");
                        } else {
                            sendMqMessage(new WuliuExpressMqBO<Wuliunoex>().setAct(WuliuExpressConstant.ACT_WULIUNOEX)
                                    .setData(new Wuliunoex()
                                            .setWuliuid(wuliu.getId())
                                            .setNu(data.getData().getBillCode())
                                            .setCom(wuliu.getCom())
                                            .setPackagecount(1)));
                        }
                        //同步快递单到中台
                        WuLiuAddOrUpdateReqVO model = LambdaBuild.create(WuLiuAddOrUpdateReqVO.class)
                                .set(WuLiuAddOrUpdateReqVO::setWuliuid, wuliu.getId())
                                .set(WuLiuAddOrUpdateReqVO::setNu, data.getData().getBillCode())
                                .set(WuLiuAddOrUpdateReqVO::setCom, wuliu.getCom())
                                .set(WuLiuAddOrUpdateReqVO::setSareaid, wuliu.getSAreaId())
                                .set(WuLiuAddOrUpdateReqVO::setRareaid, wuliu.getRAreaId())
                                .build();
                        WuliuUtil.saveExpressOrder(model, conmodel);
                    } else {
                        log.error("中通批量运单号生成接口错误jsonStr={}",jsonStr);
                        String profile = ProfileUtil.getActiveProfile();
                        messageSendService.sendOaMessage(0, "", CH999USER_IDS, profile + "中通批量运单号生成接口错误wuliuId:" + wuliu.getId());
                    }
                } catch (Exception e) {
                    log.error("中通批量运单号生成接口错误wuliuId:{}", Exceptions.getStackTraceAsString(e), e);
                    String profile = ProfileUtil.getActiveProfile();
                    messageSendService.sendOaMessage(0, "", CH999USER_IDS, profile + "中通批量运单号生成接口错误wuliuId:" + wuliu.getId());
                }
            }
        }
        // 中通逻辑 结束

        // 物流中台中通逻辑 开始
        else if (WuLiuConstant.ZHONGTONG_NEW.equals(wuliu.getCom())) {
            //获取中通面单账号
            String zhongTongAccount = WuliuUtil.getZhongTongAccount(jinfo.getPname());
            createOrderReq.setMonthlyCard(zhongTongAccount);
            createOrderReq.setExpressType(LogisticsExpressTypeEnum.ZTO.getExpressType());
            try {
                createOrderReq.setOrderNo(wuliu.getId() + "");
                for (int i = 0; i < count; i++) {
                    if (i > 0) {
                        createOrderReq.setOrderNo("0");
                    }
                    R<CreateOrderResV2> wlres = logisticsExpressService.createOrder(SysUtils.getUser(), createOrderReq, WuLiuConstant.ZHONGTONG_NEW_EXPRESS_TYPE);
                    if (Objects.nonNull(wlres) && Objects.equals(wlres.getCode(), 0) && Objects.nonNull(wlres.getData())) {
                        if (i == 0) {
                            // 快递单号
                            nu = wlres.getData().getWaybillNo();
                            // 网点名称
                            BigMarkInfoRes bigMarkInfo = Objects.nonNull(wlres.getData().getBigMarkInfo()) ? wlres.getData().getBigMarkInfo() : new BigMarkInfoRes();
                            String orgCode = StringUtils.isNotBlank(wlres.getData().getSiteName()) ? wlres.getData().getSiteName() : wlres.getData().getSiteCode();
                            //中通面单
                            sendMqMessage(new WuliuExpressMqBO<ZtoBillInfo>().setAct(WuliuExpressConstant.ACT_ZTOBILLINFO)
                                    .setData(new ZtoBillInfo()
                                            .setWuliuid(wuliu.getId().longValue())
                                            .setBillCode(nu)
                                            .setBigMark(JacksonJsonUtils.toJson(bigMarkInfo))));

                            sendMqMessage(new WuliuExpressMqBO<WuLiuWuliuwangdianEntity>().setAct(WuliuExpressConstant.ACT_WULIUWANGDIAN)
                                    .setData(new WuLiuWuliuwangdianEntity()
                                            .setWuliuid(wuliu.getId())
                                            .setOrgcode(orgCode)
                                            .setDestcode(bigMarkInfo.getMark())
                                            .setExepresstype("")
                                            .setPayType("寄付月结")
                                            .setYuejiekahao("")));
                            wuLiuService.subWuliuTransferLogPush(wuliu.getId(),"系统");
                        } else {
                            sendMqMessage(new WuliuExpressMqBO<Wuliunoex>().setAct(WuliuExpressConstant.ACT_WULIUNOEX)
                                    .setData(new Wuliunoex()
                                            .setWuliuid(wuliu.getId())
                                            .setNu(wlres.getData().getWaybillNo())
                                            .setCom(wuliu.getCom())
                                            .setPackagecount(1)));
                        }
                    } else {
                        String errmessage = wlres.getUserMsg();
                        log.error("中通新批量运单号生成接口错误" + errmessage);
                        String profile = ProfileUtil.getActiveProfile();
                        messageSendService.sendOaMessage(0, "", CH999USER_IDS, profile + "中通新批量运单号生成接口错误wuliuId:" + wuliu.getId());
                    }
                }

            } catch (Exception e) {
                log.error("中通新批量运单号生成接口错误", e);
                String profile = ProfileUtil.getActiveProfile();
                messageSendService.sendOaMessage(0, "", CH999USER_IDS, profile + "中通新批量运单号生成接口错误wuliuId:" + wuliu.getId());
                //QYWeiXinService.weixinAndOaMessageSend("PDA 中通批量运单号生成接口错误(wlid:" + model.wuliuid + ")：" + ex.Message + ">" + ex.StackTrace, 3, "", "1324,8942", msgType.异常通知);
            }
        }
        // 物流中台中通逻辑 结束

        // EMS 快递单生产接入 开始
        else if (WuLiuConstant.EMS.equals(wuliu.getCom())) {
            //查询寄件地址与dc的距离,距离dc（dc的定位）300米内的寄件地址可以下单
            String dcPosition = Optional.ofNullable(areaInfoService.getAreaInfoByAreaId2(WuLiuConstant.AREA_DC)).map(Areainfo::getPosition).orElse("0,0");
            Coordinate sendCoordinate = WuLiuTypeEnum.OTHERS.getCode().equals(wuliu.getWuType()) ? CoordinateUtil.gcj2wgs(new Coordinate(req.getSendPosition())) : SpringUtil.getBean(IWuliuAddressService.class).getAreaCoordinateV2(wuliu.getSAreaId(),wuliu.getSAddress(),wuliu.getSCityId(),1);
            double distance = AtlasUtil.getDistance(new Coordinate(dcPosition), sendCoordinate);
            if (distance > NumberConstant.THREE * NumberConstant.ONE_HUNDRED) {
                throw new CustomizeException("与dc距离超过300m，不可使用EMS");
            }
            AddressDTO sender = new AddressDTO();
            sender.setName(sname);
            sender.setMobile(smoblie);
            sender.setProv(jinfo.getPname());
            sender.setCity(jinfo.getZname());
            sender.setCounty(jinfo.getDname());
            sender.setAddress(jinfo.getAddress());
            if (StringUtils.isBlank(sender.getAddress())) {
                sender.setAddress(saddress);
            }
            AddressDTO receiver = new AddressDTO();
            receiver.setName(rname);
            receiver.setMobile(wuliu.getRMobile());
            receiver.setProv(cittInfo.getPname());
            receiver.setCity(cittInfo.getZname());
            receiver.setCounty(cittInfo.getDname());
            receiver.setAddress(raddress);
            // 订单号
            Integer orderId = wuliu.getDanHaoBind();
            if (Objects.equals(wuliu.getWuType(), 8) && Objects.equals(wuliu.getDanHaoBind(), 0)) {
                orderId = wuliu.getId();
            }
            for (int i = 0; i < count; i++) {
                orderId = orderId + i;
                OrderInfoDTO orderInfo = new OrderInfoDTO();
                orderInfo.setOrderId(String.valueOf(orderId));
                orderInfo.setSender(sender);
                orderInfo.setReceiver(receiver);
                OrderResultDTO emsResultDTO = wuLiuService.emsCreateOrder(orderInfo);
                if (Objects.equals(emsResultDTO.getCode(), 0)) {
                    if (StringUtils.isBlank(nu)) {
                        nu = emsResultDTO.getLastOrderid();

                        wuLiuService.subWuliuTransferLogPush(wuliu.getId(),"系统");
                    } else {
                        sendMqMessage(new WuliuExpressMqBO<Wuliunoex>().setAct(WuliuExpressConstant.ACT_WULIUNOEX)
                                .setData(new Wuliunoex()
                                        .setWuliuid(wuliu.getId())
                                        .setNu(emsResultDTO.getLastOrderid())
                                        .setCom(wuliu.getCom())
                                        .setPackagecount(1)));
                    }

                    //同步快递单到中台
                    WuLiuAddOrUpdateReqVO model = LambdaBuild.create(WuLiuAddOrUpdateReqVO.class)
                            .set(WuLiuAddOrUpdateReqVO::setWuliuid, wuliu.getId())
                            .set(WuLiuAddOrUpdateReqVO::setNu, emsResultDTO.getLastOrderid())
                            .set(WuLiuAddOrUpdateReqVO::setCom, wuliu.getCom())
                            .set(WuLiuAddOrUpdateReqVO::setSareaid, wuliu.getSAreaId())
                            .set(WuLiuAddOrUpdateReqVO::setRareaid, wuliu.getRAreaId())
                            .build();
                    WuliuUtil.saveExpressOrder(model, orderInfo);
                } else {
                    log.error("EMS运单号生成接口错误：result={}", emsResultDTO);
                    String profile = ProfileUtil.getActiveProfile();
                    messageSendService.sendOaMessage(0, "", CH999USER_IDS, profile + "EMS运单号生成接口错误wuliuId:" + wuliu.getId());
                    //writewuliulogs(model.getWuliuid(), username, "EMS运单号生成接口错误：" + emsResultDTO.getMessage(), null);
                }
            }
        }
        // EMS 快递单生产接入 结束

        // 京东物流 开始
        else if (Arrays.asList(WuLiuConstant.JINGDONG, WuLiuConstant.JINGDONG_JIUJI).contains(wuliu.getCom())) {
            createOrderReq.setXTenantId(0L);
            createOrderReq.setExpressItemQty(count);
            createOrderReq.setGrossWeight(Objects.nonNull(req.getWeight()) ? req.getWeight() : BigDecimal.ONE);
            createOrderReq.setGrossVolume(Objects.nonNull(req.getVloumn()) ? req.getVloumn() : BigDecimal.ONE);
            createOrderReq.setOrderNo(wuliu.getId().toString());
            createOrderReq.setDropMenuExpressType(Integer.valueOf(req.getExpressType()));
            createOrderReq.setExpressType(LogisticsExpressTypeEnum.JING_DONG_9JI.getExpressType());
            if (Objects.nonNull(req.getGuaranteeValueAmount())) {
                createOrderReq.setGuaranteeValueAmount(req.getGuaranteeValueAmount().doubleValue());
            }
            createOrderReq.setExpressItemName("电子产品");
            String result = logisticsExpressService.createOrder(createOrderReq);
            R<JdCreateOrderResultDTO> jdResult = JacksonJsonUtils.toClass(result, new TypeReference<R<JdCreateOrderResultDTO>>() {});
            if (Objects.nonNull(jdResult) && Objects.equals(jdResult.getCode(), 0) && Objects.nonNull(jdResult.getData())) {
                nu = jdResult.getData().getExpressNumber();
                DataResultDTO dataResult = jdResult.getData().getDataResult();
                sendMqMessage(new WuliuExpressMqBO<JingdongPrintInfo>().setAct(WuliuExpressConstant.ACT_JINGDONGPRINTINFO)
                        .setData(new JingdongPrintInfo().setOrderId(wuliu.getId().toString())
                                .setAreaId(Optional.ofNullable(createOrderReq.getSendShopId()).orElse(0))
                                .setDeliveryId(nu)
                                .setPromiseTimeType(dataResult.getPromiseTimeType())
                                .setPreSortResult(JSONUtil.toJsonStr(dataResult.getPreSortResult()))
                                .setTransType(dataResult.getTransType())
                                .setNeedRetry(dataResult.getNeedRetry())
                                .setExpressOperationMode(dataResult.getExpressOperationMode())));
                sendMqMessage(new WuliuExpressMqBO<WuLiuExpressExtendEntity>().setAct(WuliuExpressConstant.ACT_WULIUEXPRESSEXTEND)
                        .setData(new WuLiuExpressExtendEntity().setWuliuId(wuliu.getId())
                                .setExpressCompany(wuliu.getCom())
                                .setExpressType(req.getExpressType())));
                if (count > 1) {
                    sendMqMessage(new WuliuExpressMqBO<Wuliunoex>().setAct(WuliuExpressConstant.ACT_WULIUNOEX)
                            .setData(new Wuliunoex()
                                    .setWuliuid(wuliu.getId())
                                    .setNu(nu)
                                    .setCom(wuliu.getCom())
                                    .setPackagecount(count)));
                }
                wuLiuService.subWuliuTransferLogPush(wuliu.getId(),"系统");
            } else {
                log.error("生成京东快递失败，wuliuId={},result={}", wuliu.getId(), result);
                String profile = ProfileUtil.getActiveProfile();
                messageSendService.sendOaMessage(0, "", CH999USER_IDS, profile + "生成京东快递失败wuliuId:" + wuliu.getId());
            }
        }
        // 京东物流 结束
        // 德邦物流（德邦九机特惠） 开始
        else if (LogisticsExpressTypeEnum.DEP_PON_9JI.getCode().equals(wuliu.getCom())) {
            checkDebangParam(req, count);

            createOrderReq.setXTenantId(0L);
            createOrderReq.setExpressItemQty(count);
            createOrderReq.setGrossWeight(Objects.nonNull(req.getWeight()) ? req.getWeight() : BigDecimal.ONE);
            createOrderReq.setGrossVolume(Objects.nonNull(req.getVloumn()) ? req.getVloumn() : BigDecimal.ONE);
            createOrderReq.setOrderNo(wuliu.getId().toString());
            createOrderReq.setDropMenuExpressType(Integer.valueOf(req.getExpressType()));
            createOrderReq.setExpressType(LogisticsExpressTypeEnum.DEP_PON_9JI.getExpressType());
            createOrderReq.setExpressItemName("电子产品");
            String result = logisticsExpressService.createOrder(createOrderReq);
            R<CreateOrderResV2> deResult = JacksonJsonUtils.toClass(result, new TypeReference<R<CreateOrderResV2>>() {});
            if (Objects.nonNull(deResult) && Objects.equals(deResult.getCode(), 0) && Objects.nonNull(deResult.getData())) {
                nu = deResult.getData().getWaybillNo();
                List<String> subWaybillNos = deResult.getData().getSubWaybillNos();
                sendMqMessage(new WuliuExpressMqBO<WuLiuExpressExtendEntity>().setAct(WuliuExpressConstant.ACT_WULIUEXPRESSEXTEND)
                        .setData(new WuLiuExpressExtendEntity().setWuliuId(wuliu.getId())
                                .setExpressCompany(wuliu.getCom())
                                .setExpressType(req.getExpressType())));
                sendMqMessage(new WuliuExpressMqBO<WuLiuLogEntity>().setAct(WuliuExpressConstant.ACT_WULIULOG)
                        .setData(new WuLiuLogEntity().setWuliuid(wuliu.getId())
                                .setMsg(expressEnumService.getWuliuCompanyName(wuliu.getCom()) + "运单号生成成功:" + nu)
                                .setDtime(LocalDateTime.now())
                                .setInuser(req.getInUser())));
                if (CollectionUtils.isNotEmpty(subWaybillNos)) {
                    for (String subWaybillNo : subWaybillNos) {
                        sendMqMessage(new WuliuExpressMqBO<Wuliunoex>().setAct(WuliuExpressConstant.ACT_WULIUNOEX)
                                .setData(new Wuliunoex()
                                        .setWuliuid(wuliu.getId())
                                        .setNu(subWaybillNo)
                                        .setCom(wuliu.getCom())
                                        .setPackagecount(1)));
                    }
                }
                //sendWuliuMsg(req, wuliu, wuLiuService, nu);
            } else {
                log.error("生成德邦快递失败，wuliuId={},result={}", wuliu.getId(), result);
                String profile = ProfileUtil.getActiveProfile();
                messageSendService.sendOaMessage(0, "", CH999USER_IDS, profile + "生成德邦快递失败wuliuId:" + wuliu.getId());
            }
        }
        // 德邦物流 结束
        // 美团（九机特惠） 开始
        else if (LogisticsExpressTypeEnum.MEI_TUAN.getCode().equals(wuliu.getCom())) {
            CreateOrderByShop createOrderByShop = new CreateOrderByShop();
            createOrderByShop.setXTenantId(0L);
            createOrderByShop.setOrderType(0);
            createOrderByShop.setExpressType(LogisticsExpressTypeEnum.MEI_TUAN.getExpressType());
            createOrderByShop.setOrderId(Objects.equals(0,Optional.ofNullable(wuliu.getDanHaoBind()).orElse(0)) ? wuliu.getId().toString() : wuliu.getDanHaoBind().toString());
            createOrderByShop.setDeliveryId(wuliu.getId().longValue());
            // 订单备注
            createOrderByShop.setComment("请勿摔打,保持整洁。");
            //备注 寄件人备用电话
            String mobile = Optional.ofNullable(SysUtils.getUser()).map(v -> ch999UserService.getUserByCh999Id(v.getUserId()))
                    .map(Ch999User::getMobile).orElse(wuliu.getSMobile());
            if (StringUtils.isNotBlank(mobile)) {
                createOrderByShop.setComment(StrUtil.format("取件码：{} {} 寄件人备用电话：{}", wuliu.getId(), createOrderByShop.getComment(), mobile));
            }
            createOrderByShop.setSendShopId(wuliu.getSAreaId());
            createOrderByShop.setSendShopName(base.getSendShopName());
            createOrderByShop.setReceiveShopId(wuliu.getRAreaId());
            createOrderByShop.setReceiveShopName(base.getReceiveShopName());
            //createOrderByShop.setDeliveryServiceCode(100029)
            Areainfo areainfo = areaInfoService.lambdaQuery().eq(Areainfo::getId, wuliu.getRAreaId()).list().stream().findFirst().orElse(null);
            if (areainfo != null && AreaAttributeEnum.E.getCode().equals(areaInfo.getAreaAttribute())) {
                createOrderByShop.setReceiverName("九机" + areainfo.getAreaName());
            } else if (areainfo != null) {
                createOrderByShop.setReceiverName(areainfo.getAreaName());
            } else {
                createOrderByShop.setReceiverName(rName);
            }
            createOrderByShop.setReceiverAddress(raddress);
            //todo 虚拟号码
            String rMobile = wuliu.getRMobile();
            if (Arrays.asList(4, 6).contains(wuliu.getWuType())) {
                String tempRMobile = "";
                Integer danHaoBind = wuliu.getDanHaoBind();
                Sub sub = subService.getSub(danHaoBind);
                if (Objects.nonNull(sub)) {
                    if (OrderSubTypeEnum.JD.getCode().equals(sub.getSubtype())) {
                        tempRMobile = subService.getJdBuyerMobile(danHaoBind);
                    } else if (WuLiuConstant.HOURS_ORDER_SUB_TYPE.contains(Optional.ofNullable(sub.getSubtype()).orElse(0))) {
                        tempRMobile = subService.getMtBuyerMobile(danHaoBind);
                    }
                }
                if (StringUtils.isNotEmpty(tempRMobile)) {
                    rMobile = tempRMobile;
                }
            }
            createOrderByShop.setReceiverPhone(rMobile);
            String raddressPosition = WuliuAddressUtil.getSubPosition(Builder.of(SubPositionReq::new)
                    .with(SubPositionReq::setSubId, wuliu.getDanHaoBind())
                    .with(SubPositionReq::setWuliuId, wuliu.getId())
                    .with(SubPositionReq::setAddress, wuliu.getRAddress())
                    .with(SubPositionReq::setWuType, wuliu.getWuType())
                    .build());
            if (StringUtils.isNotBlank(raddressPosition)) {
                Coordinate rcoordinate = new Coordinate(raddressPosition);
                createOrderByShop.setReceiverLng(rcoordinate.getLongitude());
                createOrderByShop.setReceiverLat(rcoordinate.getLatitude());
            }
            if (Objects.equals(WuLiuTypeEnum.INNER.getCode(), wuliu.getWuType())
                    && Objects.nonNull(areainfo)
                    && StringUtils.isNotBlank(areainfo.getPosition())) {
                Coordinate rcoordinate = new Coordinate(areainfo.getPosition());
                createOrderByShop.setReceiverLng(rcoordinate.getLongitude());
                createOrderByShop.setReceiverLat(rcoordinate.getLatitude());
            }
            // 商品信息 根据订单获取bakset
            BigDecimal sumAmount = list.stream().filter(v -> StringUtils.isNotBlank(v.getAmount()))
                    .map(v -> new BigDecimal(v.getAmount())).reduce(BigDecimal.ZERO, BigDecimal::add);
            createOrderByShop.setGoodsValue(sumAmount.toString());
            createOrderByShop.setGoodsWeight(Objects.nonNull(wuliu.getWeight()) ? wuliu.getWeight().toString() : NumUtil.FIVE+"");
            R<CreateOrderShopResDTO> orderByShop = logisticsExpressService.createOrderByShop(createOrderByShop);
            if (Objects.equals(orderByShop.getCode(), 0) && Objects.nonNull(orderByShop.getData())) {
                nu = orderByShop.getData().getWaybillNo();
                generateWuliuNoRes.setPrice(orderByShop.getData().getFee());
                if (isSendMsgType) {
                    sendMqMessage(new WuliuExpressMqBO<WuLiuLogEntity>().setAct(WuliuExpressConstant.ACT_WULIULOG)
                            .setData(new WuLiuLogEntity().setWuliuid(wuliu.getId())
                                    .setMsg(expressEnumService.getWuliuCompanyName(wuliu.getCom()) + "单号生成成功:" + nu)
                                    .setDtime(LocalDateTime.now())
                                    .setInuser(req.getInUser())));
                    //sendWuliuMsg(req, wuliu, wuLiuService, nu);
                    //物流单更新美团费用 https://jira.9ji.com/browse/XSWL-8456
                    wuLiuService.lambdaUpdate().set(WuLiuEntity::getPrice, orderByShop.getData().getFee())
                            .eq(WuLiuEntity::getId, wuliu.getId()).update();
                }
            } else {
                log.error("生成美团跑腿订单失败，wuliuId={},result={}", wuliu.getId(), JacksonJsonUtils.toJson(orderByShop));
                if (isSendMsgType) {
                    sendMqMessage(new WuliuExpressMqBO<WuLiuLogEntity>().setAct(WuliuExpressConstant.ACT_WULIULOG)
                            .setData(new WuLiuLogEntity().setWuliuid(wuliu.getId())
                                    .setMsg(orderByShop.getUserMsg())
                                    .setDtime(LocalDateTime.now())
                                    .setInuser(req.getInUser())));
                }
                throw new CustomizeException(orderByShop.getUserMsg());
            }
        }
        // 美团（九机特惠） 结束
        //达达 开始
        else if (LogisticsExpressTypeEnum.DA_DA.getCode().equals(wuliu.getCom())) {
            CreateOrderByShop createOrderByShop = new CreateOrderByShop();
            createOrderByShop.setXTenantId(0L);
            createOrderByShop.setOrderType(0);
            createOrderByShop.setOrderId(Objects.equals(0,Optional.ofNullable(wuliu.getDanHaoBind()).orElse(0)) ? wuliu.getId().toString() : wuliu.getDanHaoBind().toString());
            createOrderByShop.setDeliveryId(wuliu.getId().longValue());
            // 订单备注
            createOrderByShop.setComment("请勿摔打,保持整洁。");
            //备注 寄件人备用电话
            String mobile = Optional.ofNullable(SysUtils.getUser()).map(v -> ch999UserService.getUserByCh999Id(v.getUserId()))
                    .map(Ch999User::getMobile).orElse(wuliu.getSMobile());
            if (StringUtils.isNotBlank(mobile)) {
                createOrderByShop.setComment(StrUtil.format("取件码：{} {} 寄件人备用电话：{}", wuliu.getId(), createOrderByShop.getComment(), mobile));
            }
            createOrderByShop.setSendShopId(wuliu.getSAreaId());
            createOrderByShop.setSendShopName(base.getSendShopName());
            createOrderByShop.setReceiveShopId(wuliu.getRAreaId());
            createOrderByShop.setReceiveShopName(base.getReceiveShopName());
            Areainfo areainfo = areaInfoService.lambdaQuery().eq(Areainfo::getId, wuliu.getRAreaId()).list().stream().findFirst().orElse(null);
            if (areainfo != null && AreaAttributeEnum.E.getCode().equals(areaInfo.getAreaAttribute())) {
                createOrderByShop.setReceiverName("九机" + areainfo.getAreaName());
            } else if (areainfo != null) {
                createOrderByShop.setReceiverName(areainfo.getAreaName());
            } else {
                createOrderByShop.setReceiverName(rName);
            }
            createOrderByShop.setReceiverAddress(raddress);

            String rMobile = wuliu.getRMobile();
            if (Arrays.asList(4, 6).contains(wuliu.getWuType())) {
                String tempRMobile = "";
                Integer danHaoBind = wuliu.getDanHaoBind();
                Sub sub = subService.getSub(danHaoBind);
                if (Objects.nonNull(sub)) {
                    if (OrderSubTypeEnum.JD.getCode().equals(sub.getSubtype())) {
                        tempRMobile = subService.getJdBuyerMobile(danHaoBind);
                    } else if (WuLiuConstant.HOURS_ORDER_SUB_TYPE.contains(Optional.ofNullable(sub.getSubtype()).orElse(0))) {
                        tempRMobile = subService.getMtBuyerMobile(danHaoBind);
                    }
                }
                if (StringUtils.isNotEmpty(tempRMobile)) {
                    //处理抖音订单虚拟号码
                    tempRMobile = tempRMobile.replace("-",",").replace("_",",");
                    rMobile = tempRMobile;
                }
            }
            createOrderByShop.setReceiverPhone(rMobile);
            String raddressPosition = WuliuAddressUtil.getSubPosition(Builder.of(SubPositionReq::new)
                    .with(SubPositionReq::setSubId, wuliu.getDanHaoBind())
                    .with(SubPositionReq::setWuliuId, wuliu.getId())
                    .with(SubPositionReq::setAddress, wuliu.getRAddress())
                    .with(SubPositionReq::setWuType, wuliu.getWuType())
                    .build());
            if (StringUtils.isNotBlank(raddressPosition)) {
                Coordinate rcoordinate = new Coordinate(raddressPosition);
                createOrderByShop.setReceiverLng(rcoordinate.getLongitude());
                createOrderByShop.setReceiverLat(rcoordinate.getLatitude());
            }
            if (Objects.equals(WuLiuTypeEnum.INNER.getCode(), wuliu.getWuType())
                    && Objects.nonNull(areainfo)
                    && StringUtils.isNotBlank(areainfo.getPosition())) {
                Coordinate rcoordinate = new Coordinate(areainfo.getPosition());
                createOrderByShop.setReceiverLng(rcoordinate.getLongitude());
                createOrderByShop.setReceiverLat(rcoordinate.getLatitude());
            }
            if (createOrderByShop.getReceiverLng() == null || createOrderByShop.getReceiverLat() == null) {
                Coordinate coordinate = wuliuAddressService.getAreaCoordinate(wuliu.getSAreaId(), wuliu.getSAddress(), wuliu.getSCityId(), 2);
                if (coordinate == null) {
                    throw new CustomizeException("地址转经纬度错误，请检查地址是否正确");
                }
                coordinate = CoordinateUtil.wgs2gcj(coordinate);
                createOrderByShop.setReceiverLng(coordinate.getLongitude());
                createOrderByShop.setReceiverLat(coordinate.getLatitude());
            }

            // 商品信息 根据订单获取bakset
            BigDecimal sumAmount = list.stream().filter(v -> StringUtils.isNotBlank(v.getAmount()))
                    .map(v -> new BigDecimal(v.getAmount())).reduce(BigDecimal.ZERO, BigDecimal::add);
            createOrderByShop.setGoodsValue(sumAmount.toString());
            createOrderByShop.setGoodsWeight(Objects.nonNull(wuliu.getWeight()) ? wuliu.getWeight().toString() : NumUtil.FIVE+"");

            //达达创建订单参数
            String city = cittInfo.getZname();
            String province = cittInfo.getPname();
            String cityName = "市辖区".equals(city) || "县".equals(city)
                    ? province.replace("省", "").replace("市", "")
                    : city.replace("市", "");
            //获取城市code
            DadaCityVO dadaCity = wuLiuService.getCityCode().stream().filter(v -> cityName.equals(v.getCityName())).findFirst().orElse(new DadaCityVO());
            if (StringUtils.isEmpty(dadaCity.getCityCode())) {
                throw new CustomizeException("无法匹配达达快递接口城市编码");
            }
            //经纬度转换为高德地图
            Coordinate coordinate = CoordinateUtil.wgs2gcj(new Coordinate(createOrderByShop.getReceiverLng(),createOrderByShop.getReceiverLat()));
            DadaOrderAddVO orderAdd = Builder.of(DadaOrderAddVO::new)
                    .with(DadaOrderAddVO::setCallback, DadaAppConstant.DADA_CALLBACK_URL)
                    .with(DadaOrderAddVO::setShopNo, Optional.ofNullable(areaInfoService.getAreaInfoByAreaId2(wuliu.getSAreaId())).orElseGet(Areainfo::new).getArea())
                    .with(DadaOrderAddVO::setOriginId, wuliu.getId().toString())
                    .with(DadaOrderAddVO::setCityCode, dadaCity.getCityCode())
                    .with(DadaOrderAddVO::setCargoPrice, BigDecimal.ONE)
                    .with(DadaOrderAddVO::setIsPrepay, 0)
                    .with(DadaOrderAddVO::setReceiverName, createOrderByShop.getReceiverName())
                    .with(DadaOrderAddVO::setReceiverAddress, createOrderByShop.getReceiverAddress())
                    .with(DadaOrderAddVO::setReceiverLng, coordinate.getLongitude())
                    .with(DadaOrderAddVO::setReceiverLat, coordinate.getLatitude())
                    .with(DadaOrderAddVO::setCargoWeight, 1.0)
                    .with(DadaOrderAddVO::setReceiverPhone, createOrderByShop.getReceiverPhone())
                    .build();
            orderAdd.setInfo(createOrderByShop.getComment());
            //调用达达创建订单接口
            String paramJson = JsonParseUtil.toJson(orderAdd);
            Boolean dadaIsOnline = SysUtils.isJiuJiProd();
            DadaRequestClient dadaClient = new DadaRequestClient(DadaUrlConstant.ORDER_ADD_URL, paramJson, dadaIsOnline);
            DadaApiResponse apiResponse = dadaClient.callRpc();
            if (ObjectUtils.isEmpty(apiResponse)) {
                log.error("达达快递创建={}，调用达达快递下单接口失败", paramJson);
                throw new CustomizeException("调用达达快递下单接口失败");
            }
            log.warn("达达快递创建={}，出参={}", paramJson, JacksonJsonUtils.toJson(apiResponse));
            //下单成功
            if (DadaAppConstant.DADA_SUCCESS.equals(apiResponse.getStatus())) {
                DadaAddOrderResVO dadaAddOrderRes = BeanUtil.toBean(apiResponse.getResult(), DadaAddOrderResVO.class);
                nu = wuliu.getId().toString();
                generateWuliuNoRes.setPrice(dadaAddOrderRes.getFee());
                if (isSendMsgType) {
                    sendMqMessage(new WuliuExpressMqBO<WuLiuLogEntity>().setAct(WuliuExpressConstant.ACT_WULIULOG)
                            .setData(new WuLiuLogEntity().setWuliuid(wuliu.getId())
                                    .setMsg(expressEnumService.getWuliuCompanyName(wuliu.getCom()) + "单号生成成功:" + nu)
                                    .setDtime(LocalDateTime.now())
                                    .setInuser(req.getInUser())));
                }
            } else {
                if (isSendMsgType) {
                    sendMqMessage(new WuliuExpressMqBO<WuLiuLogEntity>().setAct(WuliuExpressConstant.ACT_WULIULOG)
                            .setData(new WuLiuLogEntity().setWuliuid(wuliu.getId())
                                    .setMsg(apiResponse.getMsg())
                                    .setDtime(LocalDateTime.now())
                                    .setInuser(req.getInUser())));
                }
                throw new CustomizeException(apiResponse.getMsg());
            }
        }
        //达达 结束
        //uu 开始
        else if (LogisticsExpressTypeEnum.UU_PAO_TUI.getCode().equals(wuliu.getCom())) {
            CreateOrderByShop createOrderByShop = new CreateOrderByShop();
            createOrderByShop.setXTenantId(0L);
            createOrderByShop.setOrderType(0);
            createOrderByShop.setExpressType(LogisticsExpressTypeEnum.UU_PAO_TUI.getExpressType());
            createOrderByShop.setOrderId(wuliu.getId().toString());
            createOrderByShop.setDeliveryId(wuliu.getId().longValue());
            // 订单备注
            createOrderByShop.setComment("请勿摔打,保持整洁。");
            //备注 寄件人备用电话
            String mobile = Optional.ofNullable(SysUtils.getUser()).map(v -> ch999UserService.getUserByCh999Id(v.getUserId()))
                    .map(Ch999User::getMobile).orElse(wuliu.getSMobile());
            if (StringUtils.isNotBlank(mobile)) {
                if (StringUtils.isNotBlank(mobile)) {
                    createOrderByShop.setComment(StrUtil.format("取件码：{} {} 寄件人备用电话：{}", wuliu.getId(), createOrderByShop.getComment(), mobile));
                }
            }
            createOrderByShop.setSendShopId(wuliu.getSAreaId());
            createOrderByShop.setSendShopName(base.getSendShopName());
            createOrderByShop.setSendName(createOrderReq.getSenderName());
            // 发货人手机号
            createOrderByShop.setSendPhone(StringUtils.isNotBlank(smoblie) ? smoblie : wuliu.getSMobile());
            // 发货人地址
            createOrderByShop.setSendAddress(saddress);
            if (StringUtils.isNotEmpty(areaInfo.getPosition())) {
                String[] split = areaInfo.getPosition().split(StrPool.COMMA);
                if (NumUtil.TWO == split.length) {
                    createOrderByShop.setSendLng(split[0]);
                    createOrderByShop.setSendLat(split[1]);
                }
            }

            createOrderByShop.setReceiveShopId(wuliu.getRAreaId());
            createOrderByShop.setReceiveShopName(base.getReceiveShopName());
            Areainfo rareaInfo = areaInfoService.getAreaInfoByAreaId2(wuliu.getRAreaId());
            if (rareaInfo != null && AreaAttributeEnum.E.getCode().equals(areaInfo.getAreaAttribute())) {
                createOrderByShop.setReceiverName("九机" + rareaInfo.getAreaName());
            } else if (rareaInfo != null) {
                createOrderByShop.setReceiverName(rareaInfo.getAreaName());
            } else {
                createOrderByShop.setReceiverName(rName);
            }
            createOrderByShop.setReceiverAddress(raddress);
            String rMobile = wuliu.getRMobile();
            if (Arrays.asList(4, 6).contains(wuliu.getWuType())) {
                String tempRMobile = "";
                Integer danHaoBind = wuliu.getDanHaoBind();
                Sub sub = subService.getSub(danHaoBind);
                if (Objects.nonNull(sub)) {
                    if (OrderSubTypeEnum.JD.getCode().equals(sub.getSubtype())) {
                        tempRMobile = subService.getJdBuyerMobile(danHaoBind);
                    } else if (WuLiuConstant.HOURS_ORDER_SUB_TYPE.contains(Optional.ofNullable(sub.getSubtype()).orElse(0))) {
                        tempRMobile = subService.getMtBuyerMobile(danHaoBind);
                    }
                }
                if (StringUtils.isNotEmpty(tempRMobile)) {
                    //处理抖音订单虚拟号码
                    tempRMobile = tempRMobile.replace("-","_");
                    rMobile = tempRMobile;
                }
            }
            createOrderByShop.setReceiverPhone(rMobile);
            String raddressPosition = WuliuAddressUtil.getSubPosition(Builder.of(SubPositionReq::new)
                    .with(SubPositionReq::setSubId, wuliu.getDanHaoBind())
                    .with(SubPositionReq::setWuliuId, wuliu.getId())
                    .with(SubPositionReq::setAddress, wuliu.getRAddress())
                    .with(SubPositionReq::setWuType, wuliu.getWuType())
                    .build());
            if (StringUtils.isNotBlank(raddressPosition)) {
                Coordinate rcoordinate = new Coordinate(raddressPosition);
                createOrderByShop.setReceiverLng(rcoordinate.getLongitude());
                createOrderByShop.setReceiverLat(rcoordinate.getLatitude());
            }
            if (Objects.equals(WuLiuTypeEnum.INNER.getCode(), wuliu.getWuType())
                    && Objects.nonNull(rareaInfo)
                    && StringUtils.isNotBlank(rareaInfo.getPosition())) {
                Coordinate rcoordinate = new Coordinate(rareaInfo.getPosition());
                createOrderByShop.setReceiverLng(rcoordinate.getLongitude());
                createOrderByShop.setReceiverLat(rcoordinate.getLatitude());
            }
            // 商品信息 根据订单获取bakset
            BigDecimal sumAmount = list.stream().filter(v -> StringUtils.isNotBlank(v.getAmount()))
                    .map(v -> new BigDecimal(v.getAmount())).reduce(BigDecimal.ZERO, BigDecimal::add);
            createOrderByShop.setGoodsValue(sumAmount.toString());
            createOrderByShop.setGoodsWeight(Objects.nonNull(wuliu.getWeight()) ? wuliu.getWeight().toString() : NumUtil.FIVE+"");
            if (Arrays.asList(NumUtil.SEVEN, NumUtil.EIGHT).contains(wuliu.getWuType())) {
                createOrderByShop.setReceiverCity(jinfo.getZname());
                createOrderByShop.setReceiverCountry(jinfo.getDname());
            } else {
                List<AreaListRes> areaList = wuLiuService.getAreaList();
                AreaListRes areaListRes = areaList.stream().filter(v -> areaInfo.getZid().equals(v.getCode())).findFirst().orElseThrow(() -> new CustomizeException("获取城市信息失败，请在店面管理维护所属地区信息"));
                AreaListRes countryArea = areaList.stream().filter(v -> areaInfo.getCityid().equals(v.getCode())).findFirst().orElse(new AreaListRes());
                createOrderByShop.setReceiverCity(areaListRes.getName1());
                createOrderByShop.setReceiverCountry(countryArea.getName1());
            }
            createOrderByShop.setFromUsernote(WuliuAddressUtil.getUsernote(areaInfo));
            createOrderByShop.setToUsernote(WuliuAddressUtil.getUsernote(rareaInfo));
            R<CreateOrderShopResDTO> orderByShop = logisticsExpressService.createOrderByShop(createOrderByShop);
            if (Objects.equals(orderByShop.getCode(), 0) && Objects.nonNull(orderByShop.getData())) {
                nu = orderByShop.getData().getWaybillNo();
                generateWuliuNoRes.setPrice(orderByShop.getData().getFee());
                if (isSendMsgType) {
                    sendMqMessage(new WuliuExpressMqBO<WuLiuLogEntity>().setAct(WuliuExpressConstant.ACT_WULIULOG)
                            .setData(new WuLiuLogEntity().setWuliuid(wuliu.getId())
                                    .setMsg(expressEnumService.getWuliuCompanyName(wuliu.getCom()) + "单号生成成功:" + nu)
                                    .setDtime(LocalDateTime.now())
                                    .setInuser(req.getInUser())));
                    wuLiuService.lambdaUpdate().set(WuLiuEntity::getPrice, orderByShop.getData().getFee())
                            .eq(WuLiuEntity::getId, wuliu.getId()).update();
                }
            } else {
                log.error("生成uu跑腿订单失败，wuliuId={},result={}", wuliu.getId(), JacksonJsonUtils.toJson(orderByShop));
                if (isSendMsgType) {
                    sendMqMessage(new WuliuExpressMqBO<WuLiuLogEntity>().setAct(WuliuExpressConstant.ACT_WULIULOG)
                            .setData(new WuLiuLogEntity().setWuliuid(wuliu.getId())
                                    .setMsg(orderByShop.getUserMsg())
                                    .setDtime(LocalDateTime.now())
                                    .setInuser(req.getInUser())));
                }
                throw new CustomizeException(orderByShop.getUserMsg());
            }
        }
        //uu 结束
        //顺丰同城开始
        else if (LogisticsExpressTypeEnum.SFTC.getCode().equals(wuliu.getCom())) {
            CreateOrderByShop createOrderByShop = new CreateOrderByShop();
            createOrderByShop.setXTenantId(0L);
            createOrderByShop.setExpressType(LogisticsExpressTypeEnum.SFTC.getExpressType());
            createOrderByShop.setOrderId(wuliu.getId().toString());
            createOrderByShop.setDeliveryId(wuliu.getId().longValue());
            // 订单备注
            createOrderByShop.setComment(StrUtil.format("取件码【{}】", wuliu.getId()));

            createOrderByShop.setSendShopId(wuliu.getSAreaId());
            createOrderByShop.setSendShopName(base.getSendShopName());
            createOrderByShop.setSendName(createOrderReq.getSenderName());
            // 发货人手机号
            createOrderByShop.setSendPhone(StringUtils.isNotBlank(smoblie) ? smoblie : wuliu.getSMobile());
            // 发货人地址
            createOrderByShop.setSendAddress(saddress);
            if (StringUtils.isNotEmpty(areaInfo.getPosition())) {
                String[] split = areaInfo.getPosition().split(StrPool.COMMA);
                if (NumUtil.TWO == split.length) {
                    createOrderByShop.setSendLng(split[0]);
                    createOrderByShop.setSendLat(split[1]);
                }
            }

            createOrderByShop.setReceiveShopId(wuliu.getRAreaId());
            createOrderByShop.setReceiveShopName(base.getReceiveShopName());
            Areainfo rareaInfo = areaInfoService.getAreaInfoByAreaId2(wuliu.getRAreaId());
            if (rareaInfo != null && AreaAttributeEnum.E.getCode().equals(areaInfo.getAreaAttribute())) {
                createOrderByShop.setReceiverName("九机" + rareaInfo.getAreaName());
            } else if (rareaInfo != null) {
                createOrderByShop.setReceiverName(rareaInfo.getAreaName());
            } else {
                createOrderByShop.setReceiverName(rName);
            }
            createOrderByShop.setReceiverAddress(raddress);
            String rMobile = wuliu.getRMobile();
            if (Arrays.asList(4, 6).contains(wuliu.getWuType())) {
                String tempRMobile = "";
                Integer danHaoBind = wuliu.getDanHaoBind();
                Sub sub = subService.getSub(danHaoBind);
                if (Objects.nonNull(sub)) {
                    if (OrderSubTypeEnum.JD.getCode().equals(sub.getSubtype())) {
                        tempRMobile = subService.getJdBuyerMobile(danHaoBind);
                    } else if (WuLiuConstant.HOURS_ORDER_SUB_TYPE.contains(Optional.ofNullable(sub.getSubtype()).orElse(0))) {
                        tempRMobile = subService.getMtBuyerMobile(danHaoBind);
                    }
                }
                if (StringUtils.isNotEmpty(tempRMobile)) {
                    //处理抖音订单虚拟号码
                    tempRMobile = tempRMobile.replace("-","_");
                    rMobile = tempRMobile;
                }
                if (StringUtils.isNotBlank(sub.getComment())) {
                    createOrderByShop.setComment(createOrderByShop.getComment() + sub.getComment());
                }
            }
            createOrderByShop.setReceiverPhone(rMobile);
            String raddressPosition = WuliuAddressUtil.getSubPosition(Builder.of(SubPositionReq::new)
                    .with(SubPositionReq::setSubId, wuliu.getDanHaoBind())
                    .with(SubPositionReq::setWuliuId, wuliu.getId())
                    .with(SubPositionReq::setAddress, wuliu.getRAddress())
                    .with(SubPositionReq::setWuType, wuliu.getWuType())
                    .build());
            if (StringUtils.isNotBlank(raddressPosition)) {
                Coordinate rcoordinate = new Coordinate(raddressPosition);
                createOrderByShop.setReceiverLng(rcoordinate.getLongitude());
                createOrderByShop.setReceiverLat(rcoordinate.getLatitude());
            }
            if (Objects.equals(WuLiuTypeEnum.INNER.getCode(), wuliu.getWuType())
                    && Objects.nonNull(rareaInfo)
                    && StringUtils.isNotBlank(rareaInfo.getPosition())) {
                Coordinate rcoordinate = new Coordinate(rareaInfo.getPosition());
                createOrderByShop.setReceiverLng(rcoordinate.getLongitude());
                createOrderByShop.setReceiverLat(rcoordinate.getLatitude());
            }
            // 商品信息 写死
            List<OrderProductDetailReq> orderProductDetailList = Collections.singletonList(LambdaBuild.create(OrderProductDetailReq.class)
                    .set(OrderProductDetailReq::setProductName, "电子产品")
                    .set(OrderProductDetailReq::setPrice, new BigDecimal(NumberConstant.FIVE_HUNDRED))
                    .set(OrderProductDetailReq::setProductNum, 1).build());
            createOrderByShop.setProductDetailList(orderProductDetailList);
            createOrderByShop.setGoodsWeight(NumberConstant.ONE.toString());
            //备注 寄件人备用电话
            String mobile = Optional.ofNullable(SysUtils.getUser()).map(v -> ch999UserService.getUserByCh999Id(v.getUserId()))
                    .map(Ch999User::getMobile).orElse(wuliu.getSMobile());
            if (StringUtils.isNotBlank(mobile)) {
                createOrderByShop.setComment(createOrderByShop.getComment() + "寄件人备用电话："+ mobile);
            }
            R<CreateOrderShopResDTO> orderByShop = logisticsExpressService.createOrderByShop(createOrderByShop);
            if (Objects.equals(orderByShop.getCode(), 0) && Objects.nonNull(orderByShop.getData())) {
                nu = orderByShop.getData().getWaybillNo();
                generateWuliuNoRes.setPrice(orderByShop.getData().getFee());
                if (isSendMsgType) {
                    sendMqMessage(new WuliuExpressMqBO<WuLiuLogEntity>().setAct(WuliuExpressConstant.ACT_WULIULOG)
                            .setData(new WuLiuLogEntity().setWuliuid(wuliu.getId())
                                    .setMsg(expressEnumService.getWuliuCompanyName(wuliu.getCom()) + "单号生成成功:" + nu)
                                    .setDtime(LocalDateTime.now())
                                    .setInuser(req.getInUser())));
                    wuLiuService.lambdaUpdate().set(WuLiuEntity::getPrice, orderByShop.getData().getFee())
                            .eq(WuLiuEntity::getId, wuliu.getId()).update();
                }
            } else {
                log.error("生成顺丰同城订单失败，wuliuId={},result={}", wuliu.getId(), JacksonJsonUtils.toJson(orderByShop));
                if (isSendMsgType) {
                    sendMqMessage(new WuliuExpressMqBO<WuLiuLogEntity>().setAct(WuliuExpressConstant.ACT_WULIULOG)
                            .setData(new WuLiuLogEntity().setWuliuid(wuliu.getId())
                                    .setMsg(orderByShop.getUserMsg())
                                    .setDtime(LocalDateTime.now())
                                    .setInuser(req.getInUser())));
                }
                throw new CustomizeException(orderByShop.getUserMsg());
            }
        }
        //顺丰同城结束

        if (StringUtils.isNotBlank(nu) && isSendMsgType) {
            //通知oa处理订单中的快递单号
            sendWuliuMsg(req, wuliu, wuLiuService, nu);
            //物流时效消息通知
            WuliuUtil.sendWuliuProcessMessage(wuliu.getId());
        }
        List<String> paotuComList = Arrays.asList(LogisticsExpressTypeEnum.DA_DA.getCode(), LogisticsExpressTypeEnum.MEI_TUAN.getCode(), LogisticsExpressTypeEnum.UU_PAO_TUI.getCode(), LogisticsExpressTypeEnum.SFTC.getCode());
        boolean isPushWuliuMsg = paotuComList.contains(Optional.ofNullable(wuliu.getCom()).orElse("")) && StringUtils.isNotBlank(nu) && WuLiuTypeEnum.INNER.getCode().equals(wuliu.getWuType());
        //物流单叫跑腿后，调拨单已发货状态，推送OA消息给调拨单发货操作人
        if (isPushWuliuMsg) {
            WuliuUtil.sendWuliuExpressMessage(new WuliuExpressMqBO<DiaoboPaotuiWuliuBO>().setAct(WuliuExpressConstant.ACT_DIAOBO_PAOTUI_MESSAGE_PUSH)
                    .setData(DiaoboPaotuiWuliuBO.builder().wuliuId(wuliu.getId()).build()));
        }
        generateWuliuNoRes.setNu(nu);
        return generateWuliuNoRes;
    }

    /**
     * 顺丰线上国补参数处理
     * @param wuliu
     */
    private void createOrderBatchNationalSupplement(WuLiuEntity wuliu,
                                                    CreateOrderNoBase createOrderNoBase) {
        if (CommonUtil.isNotNullZero(wuliu.getDanHaoBind())
                && (WuLiuTypeEnum.ORDER.getCode().equals(wuliu.getWuType())
                || WuLiuTypeEnum.ORDER_EXPRESS.getCode().equals(wuliu.getWuType()))) {
            //线上国补订单校验
            List<NationalSupplementKindRes> nationalSupplementKindList = SpringUtil.getBean(NationalSupplementService.class).getNationalSupplementKindList(Collections.singletonList(Convert.toStr(wuliu.getDanHaoBind())));
            if (CollUtil.isNotEmpty(nationalSupplementKindList)) {
                OnlineNationalSupplementStockDTO onlineStock = wuLiuBasketService.getOnlineNationalSupplementStockBySubId(wuliu.getDanHaoBind());
                //顺丰国补配置
                List<Integer> codeList = Collections.singletonList(SysConfigConstant.NATIONAL_MOBILE_PPIDS);
                Long xtenant = onlineStock != null && onlineStock.getXtenant() != null ? onlineStock.getXtenant() : 0L;
                List<SysConfig> sysConfigList = sysConfigService.getSysConfigListByCode(codeList, xtenant);
                if (CollUtil.isEmpty(sysConfigList)) {
                    throw new CustomizeException("订单使用了线上国补支付，但未配置顺丰产品编码，请联系采销处理");
                }
                String customizedService = "";
                Integer ppid = onlineStock != null && onlineStock.getPpid() != null ? onlineStock.getPpid() : 0;
                for (SysConfig sysConfig : sysConfigList) {
                    List<Integer> ppids = CommonUtils.covertIdStr(sysConfig.getValue());
                    if (ppids.contains(ppid)) {
                        customizedService = sysConfig.getName();
                        break;
                    }
                }
                if (StrUtil.isBlank(customizedService)) {
                    throw new CustomizeException("订单使用了线上国补支付，但未配置顺丰产品编码，请联系采销处理");
                }
                ShunfengCustomizedServiceParam shunfengCustomizedServiceParam = wuliuExpressMapStruct.toShunfengCustomizedServiceParam(onlineStock);
                createOrderNoBase.setCustomizedService(customizedService);
                createOrderNoBase.setCustomizedServiceParam(JSON.toJSONString(shunfengCustomizedServiceParam));
            }
        }
    }

    /**
     * 通知调拨单，订单,良品订单
     * @param req
     * @param wuliu
     * @param wuLiuService
     * @param nu
     */
    private void sendWuliuMsg(WuliuExpressBO req, WuLiuEntity wuliu, IWuLiuService wuLiuService, String nu) {
        wuLiuService.subWuliuTransferLogPush(wuliu.getId(),"系统");
        if (WuLiuConstant.SUB_WULIU_TYPE_ARRY.contains(wuliu.getWuType())) {
            // 订单派送的物流类型
            wuliuMqProducer.updteWuliuNo(expressEnumService.getWuliuCompanyName(wuliu.getCom()), nu, Long.valueOf(wuliu.getDanHaoBind()), req.getInUser());
        } else if (WuLiuConstant.LP_SUB_WULIU_TYPE_ARRY.contains(wuliu.getWuType())) {
            // 良品订单派送的物流类型
            wuliuMqProducer.updateWuliuNo2(expressEnumService.getWuliuCompanyName(wuliu.getCom()), nu, Long.valueOf(wuliu.getDanHaoBind()), req.getInUser());
        }
    }

    /**
     * 校验德邦快递参数
     * @param req
     * @param count
     * @return
     */
    private void checkDebangParam(WuliuExpressBO req, Integer count) {
        int rcpCount = 9;
        int wxjthCount = 1;
        int nzbrhCount = 30;
        String errMsg = "";
        if (StringUtils.isBlank(req.getExpressType()) || !StringUtils.isNumeric(req.getExpressType())) {
            errMsg = "请选择正确的快递细分类型";
        }
        Integer dropMenuExpressType = Integer.valueOf(req.getExpressType());
        if (DePonTransportTypeEnum.RCP.getCode().equals(dropMenuExpressType) && count > rcpCount) {
            errMsg = "德邦物流大件快递包裹数不能超过9";
        } else if (DePonTransportTypeEnum.WXJTH.getCode().equals(dropMenuExpressType) && count > wxjthCount) {
            errMsg = "德邦物流微小件包裹数不能超过9";
        } else if (DePonTransportTypeEnum.NZBRH.getCode().equals(dropMenuExpressType) && count > nzbrhCount) {
            errMsg = "德邦物流重包入户包裹数不能超过30";
        }
        if (StringUtils.isNotBlank(errMsg)) {
            throw new CustomizeException(errMsg);
        }
    }

    /**
     * 发送消息
     * @param messageObj
     */
    private <T> void sendMqMessage(WuliuExpressMqBO<T> messageObj) {
        try {
            String jsonMessage = JacksonJsonUtils.toJson(messageObj);
            log.warn("发送 RabbitMQ 队列消息入参: {}", jsonMessage);
            rabbitTemplate.convertAndSend(RabbitMqConfig.QUEUE_WULIU_EXPRESS_SYNC, jsonMessage);
        } catch (AmqpException e) {
            log.error("发送 RabbitMQ 队列消息报错: {}, message: {}, e: {}", e.getMessage(), messageObj, e);
        }
    }

    /**
     * 是否上门揽件
     * @date 2021-12-16
     */
    public Integer doCall(String monthlyCard, Integer sareaid) {
        //顺丰运单全部无需手持终端确认
        // 总部:HQ/dc/d1/h1,dc1,sz,h2,D
        List<Integer> areaids = Arrays.asList(113, 14, 246, 96,738,826,878,879,880);
        // 默认通知快递小哥上门揽件
        Integer doCall = 1;
        if ("8712262312".equals(monthlyCard) && (isCurAreaHqDcH1D1(sareaid) || areaids.contains(sareaid))) {
            doCall = 0;
        }
        return doCall;
    }

    public boolean isCurAreaHqDcH1D1(Integer areaId) {
        boolean result = false;
        if (areaId == null) {
            return false;
        }
        if (AreaInfoUtils.isCurrentHq(areaId) || AreaInfoUtils.isCurrentDc(areaId) || AreaInfoUtils.isCurrentH1(areaId) || AreaInfoUtils.isCurrentD1(areaId)) {
            result = true;
        }
        return result;
    }
}
