package com.jiuji.oa.wuliu.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单,责任小组：销售 Entity
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-08
 */
@Data
@Accessors(chain = true)
@TableName("sub")
public class WuLiuSubEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 短单号
     */
    private Integer subId;

    /**
     * 下单时间
     */
    private LocalDateTime subDate;

    /**
     * 是否确认
     */
    private Integer subCheck;

    /**
     * 收货人
     */
    private String subTo;

    /**
     * 电话
     */
    private String subTel;

    /**
     * 付款方式
     */
    private Integer subPay;

    /**
     * 备注
     */
    private String comment;

    /**
     * 录入人员
     */
    @TableField("Inuser")
    private String inUser;

    /**
     * 手机号码
     */
    private String subMobile;

    /**
     * 打印次数
     */
    @TableField("printxcount")
    private Integer printCount;

    /**
     * 订单门店
     */
    private String area;

    /**
     * 自提店 ID
     */
    @TableField("zitidianID")
    private Integer ziTiDianId;

    /**
     * 客户 ID
     */
    @TableField("userid")
    private Long userId;

    /**
     * 网上支付 所选择的 银行
     */
    private String onlinePay;

    /**
     * 营销活动 ID
     */
    @TableField("Marketingid")
    private Integer marketingId;

    /**
     * 出具发票
     */
    @TableField("ispiao")
    private Boolean piaoFlag;

    /**
     * 订单类型
     */
    @TableField("subtype")
    private Integer subType;

    /**
     * 配送方式
     */
    @TableField("delivery")
    private Integer delivery;

    /**
     * 应付金额
     */
    @TableField("yingfuM")
    private BigDecimal yingFuMoney;

    /**
     * 已付金额
     */
    @TableField("yifuM")
    private BigDecimal yiFuMoney;

    /**
     * 应收运费
     */
    @TableField("feeM")
    private BigDecimal feeMoney;

    /**
     * 优惠码支付
     */
    @TableField("youhui1M")
    private BigDecimal youHui1Money;

    /**
     * 手续费
     */
    @TableField("shouxuM")
    private BigDecimal shouXuMoney;

    /**
     * 积点支付金额
     */
    @TableField("jidianM")
    private BigDecimal jiDianMoney;

    /**
     * 出库时间
     */
    @TableField("tradeDate")
    private LocalDateTime tradeDate;

    /**
     * 交易日期
     */
    @TableField("tradeDate1")
    private LocalDateTime tradeDate1;

    /**
     * 定金
     */
    @TableField("dingjing")
    private BigDecimal dingJing;

    /**
     * 原单
     */
    @TableField("subPID")
    private Integer subPid;

    /**
     * 交易员
     */
    private String trader;

    /**
     *
     */
    @TableField("fankuan")
    private BigDecimal fanKuan;

    /**
     * 订单锁定状态
     */
    @TableField("islock")
    private Short isLock;

    /**
     * 地区 ID
     */
    @TableField("areaid")
    private Integer areaId;

    /**
     * 九机币
     */
    @TableField("coinM")
    private BigDecimal coinMoney;

    /**
     * 预计到达时间
     */
    @TableField("expectTime")
    private LocalDateTime expectTime;

    /**
     * 退单时间
     */
    @TableField("returnDate")
    private LocalDateTime returnDate;

    /**
     * 凭证 ID
     */
    @TableField("pzid")
    private Integer certId;

    /**
     *
     */
    @TableField("subApartDate")
    private LocalDateTime subApartDate;

    /**
     *
     */
    @TableField("kcAreaid")
    private Integer kcAreaId;

    /**
     *
     */
    @TableField("voucherId")
    private Integer voucherId;

    /**
     *
     */
    private Byte[] subRv;

}
