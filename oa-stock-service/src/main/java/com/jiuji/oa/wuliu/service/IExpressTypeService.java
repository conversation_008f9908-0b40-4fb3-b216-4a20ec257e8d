package com.jiuji.oa.wuliu.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.wuliu.entity.ExpressTypeEntity;
import com.jiuji.oa.wuliu.vo.req.ExpressTypeVO;
import java.util.List;

/**
 * <p>
 * 物流单快递类型 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-30
 */
public interface IExpressTypeService extends IService<ExpressTypeEntity> {

    /**
     * 列出所有快递类型
     *
     * @return
     */
    List<ExpressTypeVO> listExpressType();

}
