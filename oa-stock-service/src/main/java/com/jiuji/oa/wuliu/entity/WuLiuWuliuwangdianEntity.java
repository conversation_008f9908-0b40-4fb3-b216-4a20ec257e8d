package com.jiuji.oa.wuliu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 快递网点记录表[责任小组:物流组] 实体类
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-29
 */
@Data
@Accessors(chain = true)
@TableName("wuliuwangdian")
@ApiModel(value = "WuLiuWuliuwangdianEntity 实体类", description = "快递网点记录表[责任小组:物流组] 实体类")
public class WuLiuWuliuwangdianEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("exepresstype")
    private String exepresstype;

    @TableField("orgcode")
    private String orgcode;

    @TableField("payType")
    private String payType;

    @TableField("wuliuid")
    private Integer wuliuid;

    @TableField("yuejiekahao")
    private String yuejiekahao;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("destcode")
    private String destcode;

}