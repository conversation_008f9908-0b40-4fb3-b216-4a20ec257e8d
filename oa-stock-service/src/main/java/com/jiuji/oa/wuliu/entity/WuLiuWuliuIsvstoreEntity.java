package com.jiuji.oa.wuliu.entity;

  import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 *  实体类
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-12-15
 */
@Data
@Accessors(chain = true)
@TableName("wuliu_isvstore")
@ApiModel(value = "WuLiuWuliuIsvstoreEntity 实体类", description = " 实体类")
public class WuLiuWuliuIsvstoreEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("inuser")
    private String inuser;

    @TableField("linkid")
    private Integer linkid;

    @TableField("orderno")
    private String orderno;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("kind")
    private Integer kind;

    @TableField("createtime")
    private LocalDateTime createtime;

}