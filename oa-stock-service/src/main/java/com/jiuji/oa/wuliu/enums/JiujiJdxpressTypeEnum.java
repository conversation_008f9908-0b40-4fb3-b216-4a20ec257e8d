package com.jiuji.oa.wuliu.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import com.jiuji.tc.utils.enums.EnumVO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>

/**
 * 京东物流(九机特惠)
 *
 * <AUTHOR>
 * @date 2021-12-04
 */
@AllArgsConstructor
@Getter
public enum JiujiJdxpressTypeEnum implements CodeMessageEnumInterface {

    /**
     * 京东快递（发全国0-20KG）
     */
    STANDARD(1, "京东快递（发全国0-20KG）"),

    /**
     * 京东重货（发全国20-100KG）
     */
    HEAVY_CARGO(2, "京东重货（发全国20-100KG）"),

    /**
     * 京东零担（发全国100KG以上）
     */
    PARTNER(3, "京东零担（发全国100KG以上）");

    private final Integer code;
    private final String message;

    /**
     * 京东快递类型
     */
    public static String getMessage(Integer code) {
        for (JiujiJdxpressTypeEnum jdxpressTypeEnum : values()) {
            if (jdxpressTypeEnum.getCode().equals(code)) {
                return jdxpressTypeEnum.getMessage();
            }
        }
        return "";
    }
    /**
     * 京东快递类型
     */
    public static String getMessage(String code) {
        if (!StringUtils.isNumeric(code)) {
            return "";
        }
        for (JiujiJdxpressTypeEnum jdxpressTypeEnum : values()) {
            if (jdxpressTypeEnum.getCode().equals(Integer.valueOf(code))) {
                return jdxpressTypeEnum.getMessage();
            }
        }
        return "";
    }

    /**
     * 京东子快递类型
     * @return
     */
    public static List<Integer> getDropMenuExpressType() {
        return Arrays.stream(values()).map(JiujiJdxpressTypeEnum::getCode).collect(Collectors.toList());
    }

    /**
     * m版枚举
     */
    public static List<EnumVO> getEnumList() {
        List<EnumVO> list = new ArrayList<>();
        for (JiujiJdxpressTypeEnum enums : values()) {
            EnumVO vo = new EnumVO();
            vo.setLabel(enums.getMessage());
            vo.setValue(enums.getCode().toString());
            list.add(vo);
        }
        return list;
    }
}
