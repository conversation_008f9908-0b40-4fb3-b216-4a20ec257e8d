package com.jiuji.oa.wuliu.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 日期类型的枚举
 *
 * <AUTHOR>
 * @date 2021/10/09
 */
@Getter
@AllArgsConstructor
public enum DeclareStateEnum implements CodeMessageEnumInterface {
    /**
     * 待审核
     */
    NOT_DECLARE(0, "未提交"),
    /**
     * 已审核
     */
    DECLARE(1, "已提交"),
    /**
     * 提交失败
     */
    DECLARE_FAIL(2, "提交失败");

    /**
     * 代码
     */
    private Integer code;
    /**
     * 消息
     */
    private String message;

    /**
     * message
     * @param deliveryCode
     * @return
     */
    public static String getMessageByCode (Integer code) {
        for (DeclareStateEnum stateEnum : values()) {
            if (stateEnum.getCode().equals(code)) {
                return stateEnum.getMessage();
            }
        }
        return "";
    }
}
