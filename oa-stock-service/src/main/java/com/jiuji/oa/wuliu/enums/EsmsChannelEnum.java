package com.jiuji.oa.wuliu.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> liu ming
 * @date 2021-11-12 上午 10:22
 */
@AllArgsConstructor
@Getter
public enum EsmsChannelEnum {

    /**
     * 九机
     */
    JIUJI(9),

    /**
     * 丫丫网
     */
    YAYA(23),

    /**
     * 九机网营销
     */
    JIUJI_YX(18),

    /**
     * 丫丫网营销
     */
    YAYA_YX(24),

    /**
     * 华为授权店
     */
    HUAWEI(27),

    /**
     * 九讯苹果体验店
     */
    JIUJI_APPLE(37),

    /**
     * 九讯苹果体验店营销
     */
    JIUJI_APPLE_YX(38),

    /**
     * 九电移动验证码通道
     */
    JIUDIAN_YD(39),

    /**
     * 九电移动营销通道
     */
    JIUDIAN_YX(40),

    /**
     * 大疆授权店验证码通道
     */
    DAJIANG(47),

    /**
     * 大疆授权店营销通道
     */
    DAJIANG_YX(48),

    /**
     * 任天堂授权店验证码通道
     */
    RENTIANTANG(49),

    /**
     * 任天堂授权店营销通道
     */
    RENTIANTANG_YX(50),

    /**
     * 九讯OPPO验证码通道
     */
    JIUXUN_OPPO(51),

    /**
     * 九讯OPPO营销通道
     */
    JIUXUN_OPPO_YX(52),

    /**
     * 九讯vivo验证码通道
     */
    JIUXUN_VIVO(53),

    /**
     *
     */
    JIUXUN_VIVO_YX(54),

    /**
     * 九讯小米验证码通道
     */
    JIUXUN_XIAOMI(55),

    /**
     * 九讯小米营销通道
     */
    JIUXUN_XIAOMI_YX(56),

    /**
     * 九讯荣耀验证码通道
     */
    JIUXUN_HONOR(57),

    /**
     * 九讯荣耀营销通道
     */
    JIUXUN_HONOR_YX(58);

    private Integer code;

}
