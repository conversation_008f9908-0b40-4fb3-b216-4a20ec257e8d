package com.jiuji.oa.wuliu.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class LogisticsMessageVo {

    @NotBlank(message = "快递单号不能为空")
    private String logisticsOrderNo;

    @NotNull(message = "快递公司不能为空")
    private String express;

    @NotNull(message = "租户id不能为空")
    private Integer xtenant;

    private Boolean isPaiLiangJi;
}
