/*
 *     Copyright © 2006 - 2020 九机网 All Rights Reserved
 *
 */
package com.jiuji.oa.wuliu.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.wuliu.entity.WuLiuSubAddressEntity;
import com.jiuji.oa.wuliu.mapper.WuLiuSubAddressMapper;
import com.jiuji.oa.wuliu.service.IWuLiuSubAddressService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * SubAddress ServiceImpl
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-08
 */
@Service
@RequiredArgsConstructor
@Slf4j
@DS("oanewWrite")
public class WuLiuSubAddressServiceImpl extends ServiceImpl<WuLiuSubAddressMapper, WuLiuSubAddressEntity> implements IWuLiuSubAddressService {


    /**
     * 清除订单中的快递公司和快递单号
     *
     * @param subId
     * @param nu
     * @return
     */
    @DS("oanewWrite")
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean clearWuliuNo(Integer subId, String nu) {
        return this.lambdaUpdate()
                .set(WuLiuSubAddressEntity::getWuLiuNo, null)
                .set(WuLiuSubAddressEntity::getWuLiuCompany, null)
                .eq(WuLiuSubAddressEntity::getSubId, subId)
                .eq(WuLiuSubAddressEntity::getWuLiuNo, nu)
                .update();
    }
}