package com.jiuji.oa.wuliu.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * 物流单类别对应的分类id
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-12-04
 */
@AllArgsConstructor
@Getter
public enum WuLiuCateEnum implements CodeMessageEnumInterface {

    /**
     * 大件调拨
     */
    DAJIAN_DIAOBO(4, "大件调拨"),

    /**
     * 小件调拨
     */
    XIAOJIAN_DIAOBO(5, "小件调拨"),

    /**
     * 内部其他
     */
    NEIBU_DIAOBO(8, "内部其他"),

    /**
     * 大件派送
     */
    DAJIAN_PAISONG(9, "大件派送"),

    /**
     * 小件派送
     */
    XIAOJIAN_PAISONG(10, "小件派送"),

    /**
     * 良品派送
     */
    LIANGPIN_PAISONG(11, "良品派送"),

    /**
     * 租机派送
     */
    ZUJI_PAISONG(12, "租机派送"),

    /**
     * 拍靓机派送
     */
    PAILIANGJI_PAISONG(13, "拍靓机派送"),

    /**
     * 上门取件
     */
    SHANGMEN_QUJIAN(14, "上门取件"),

    /**
     * 发票派送
     */
    FAPIAO_PAISONG(15, "发票派送"),

    /**
     * 其他派送
     */
    QITA_PAISONG(16, "其他派送"),

    /**
     * 回收机调拨
     */
    HUISHOU_DIAOBO(17, "回收机调拨"),

    /**
     * 良品调拨
     */
    LIANGPIN_DIAOBO(18, "良品调拨"),

    /**
     * 维修配件调拨
     */
    WEIXIUPEIJIAN_DIAOBO(19, "维修配件调拨"),

    /**
     * 维修单调拨
     */
    WEIXIUDAN_DIAOBO(20, "维修单调拨"),

    /**
     * 售后小件调拨
     */
    SHOUHOUXIAOJIAN_DIAOBO(21, "售后小件调拨"),

    /**
     * 租机调拨
     */
    ZUJI_DIAOBO(22, "租机调拨"),

    /**
     * 备用机调拨
     */
    BEIYONGJI_DIAOBO(23, "备用机调拨"),

    /**
     * 营销物料调拨
     */
    YINGXIAOWULIAO_DIAOBO(24, "营销物料调拨"),

    /**
     * 常用资产调拨
     */
    CHANGYONGZICHANG_DIAOBO(25, "常用资产调拨"),

    /**
     * 固定资产调拨
     */
    GUDINGZICHANG_DIAOBO(26, "固定资产调拨"),

    /**
     * 维修换货
     */
    WEIXIU_HUANHUO(27, "维修换货"),

    /**
     * 维修旧件
     */
    WEIXIU_JIUJIAN(28, "维修旧件");

    private final Integer code;
    private final String message;
}