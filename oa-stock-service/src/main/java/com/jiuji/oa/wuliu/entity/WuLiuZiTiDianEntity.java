package com.jiuji.oa.wuliu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 小店表,责任小组：运营 实体类
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-12
 */
@Data
@Accessors(chain = true)
@TableName("zitidian")
@ApiModel(value = "WuLiuZiTiDianEntity 实体类", description = "小店表,责任小组：运营 实体类")
public class WuLiuZiTiDianEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 小店绑定仓库地区
     */
    @ApiModelProperty("小店绑定仓库地区")
    @TableField("bindDcAreaId")
    private Integer bindDcAreaId;

    @TableField("zhoubian")
    private String zhoubian;

    @TableField("Jial_tel1")
    private String jialTel1;

    @TableField("sex")
    private String sex;

    @TableField("xtenant")
    private Integer xtenant;

    @TableField("Jial_work2")
    private String jialWork2;

    @TableField("shopLevel")
    private Integer shopLevel;

    @TableField("tel2")
    private String tel2;

    @TableField("Jia_chenfu1")
    private String jiaChenfu1;

    @TableField("inuser")
    private String inuser;

    @TableField("did")
    private Integer did;

    @TableField("j_Address")
    private String jAddress;

    @TableField("pid")
    private Integer pid;

    @TableField("d_Tel")
    private String dTel;

    @TableField("address")
    private String address;

    @TableField("Workid_picId")
    private Integer workidPicid;

    @TableField("cardid_picId")
    private Integer cardidPicid;

    @TableField("j_Tel")
    private String jTel;

    @TableField("shopid")
    private String shopid;

    @TableField("zid")
    private Integer zid;

    @TableField("J_name")
    private String jName;

    @TableField("hours")
    private String hours;

    @TableField("Jial_name1")
    private String jialName1;

    @TableField("rank")
    private Integer rank;

    @TableField("photo_pic")
    private String photoPic;

    @TableField("national")
    private String national;

    @TableField("ispass")
    private Boolean ispass;

    @TableField("Origin")
    private String origin;

    @TableField("OutPutWebId")
    private Integer outPutWebId;

    @TableField("health")
    private String health;

    @TableField("Workid_pic")
    private String workidPic;

    @TableField("adddate")
    private LocalDateTime adddate;

    @TableField("Company")
    private String company;

    @TableField("erdu")
    private BigDecimal erdu;

    @TableField("cityid")
    private Integer cityId;

    @TableField("d_Address")
    private String dAddress;

    @TableField("zhengzhi")
    private String zhengzhi;

    @TableField("Jial_tel2")
    private String jialTel2;

    @TableField("Contact")
    private String contact;

    @TableField("payCardNum")
    private String payCardNum;

    @TableField("comment")
    private String comment;

    @TableField("bindAreaid")
    private Integer bindAreaid;

    @TableField("Jia_chenfu2")
    private String jiaChenfu2;

    @TableField("idnumber")
    private String idnumber;

    @TableField("cardid_pic")
    private String cardidPic;

    @TableField("comment2")
    private String comment2;

    @TableField("distributionKind")
    private Integer distributionKind;

    @TableField("Jial_name2")
    private String jialName2;

    @TableField("birthday")
    private String birthday;

    @TableField("payCardName")
    private String payCardName;

    @TableField("userid")
    private Integer userid;

    @TableField("name")
    private String name;

    @TableField("shopType")
    private Integer shopType;

    @TableField("J_mobile")
    private String jMobile;

    @TableField("nickContact")
    private String nickContact;

    @TableField("Jial_work1")
    private String jialWork1;

    @TableField("tel1")
    private String tel1;

    @TableField("applyId")
    private Integer applyId;

    @TableField("photo_picId")
    private Integer photoPicid;

    @TableField("customerKinds")
    private Integer customerKinds;

    private Integer authorizeid;

}