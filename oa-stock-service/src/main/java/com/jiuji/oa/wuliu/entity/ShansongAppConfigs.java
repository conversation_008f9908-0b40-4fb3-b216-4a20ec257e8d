package com.jiuji.oa.wuliu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * ShansongAppConfigs
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-12-05
 */
@TableName(value = "ShansongAppConfigs")
@Data
public class ShansongAppConfigs implements Serializable {
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 客户端应用id
     */
    private String clientid;
    /**
     * 商户id
     */
    private String shopid;
    /**
     * App密钥
     */
    private String appsecrty;
    /**
     * 创建时间
     */
    private Date createtime;
    /**
     * 服务器地址
     */
    private String serveraddress;
}