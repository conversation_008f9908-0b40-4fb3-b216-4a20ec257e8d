package com.jiuji.oa.wuliu.vo;


import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.experimental.Accessors;
import org.codehaus.jackson.annotate.JsonProperty;

import java.io.Serializable;

/**
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-16
 */
@Data
@Accessors(chain = true)
public class EmsRequestDataDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("url")
    @JSONField(name = "url")
    private String url;

    @JsonProperty("parentId")
    @JSONField(name = "parentId")
    private String parentId;

    @JsonProperty("OrderNormal")
    @JSONField(name = "OrderNormal")
    private EmsOrderNormalDTO orderNormal;

}
