package com.jiuji.oa.wuliu.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * zhongtongApiServices.orderGroup
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-16
 */
@Data
@Accessors(chain = true)
public class OrderGroupDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 合作商订单号，为调用方生成，可以传订单号、主键、UUID等
     */
    @JsonProperty("id")
    @JSONField(name = "id")
    private String id;

    /**
     * 1:为电子面单类 （其他类型具体可调用获取类型接口）
     */
    @JsonProperty("typeid")
    @JSONField(name = "typeid")
    private String typeid = "1";

    /**
     * 订单备注
     */
    @JsonProperty("remark")
    @JSONField(name = "remark")
    private String remark;

    /**
     * 订单总金额(单位:元)
     */
    @JsonProperty("orderSum")
    @JSONField(name = "orderSum")
    private BigDecimal orderSum;

    @JsonProperty("sender")
    @JSONField(name = "sender")
    private OrderSenderDTO sender;

    @JsonProperty("receiver")
    @JSONField(name = "receiver")
    private OrderReceiverDTO receiver;

    @JsonProperty("partnerCode")
    @JSONField(name = "partnerCode")
    private String partnerCode;

    @JsonProperty("type")
    @JSONField(name = "type")
    private String type;

    private String tradeId;

    private String mailNo;
    private String siteCode;
    private String siteName;


}
