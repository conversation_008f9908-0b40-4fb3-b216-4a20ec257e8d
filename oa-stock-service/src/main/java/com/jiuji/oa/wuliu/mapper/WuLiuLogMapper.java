/*
 *    Copyright © 2006 - 2020 九机网 All Rights Reserved
 *
 */

package com.jiuji.oa.wuliu.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.wuliu.entity.WuLiuLogEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 物流单日志Mapper
 *
 * <AUTHOR> liu ming
 * @date 2021-05-24 18:40:13
 */
@Mapper
public interface WuLiuLogMapper extends BaseMapper<WuLiuLogEntity> {

    /**
     * 批量增加物流日志
     * @param logs
     */
    Integer insertLogBatch(@Param("logs") List<WuLiuLogEntity> logs);
}
