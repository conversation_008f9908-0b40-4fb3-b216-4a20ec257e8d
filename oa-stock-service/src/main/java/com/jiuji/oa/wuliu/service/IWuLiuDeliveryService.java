package com.jiuji.oa.wuliu.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.wuliu.entity.WuLiuDeliveryEntity;
import com.jiuji.tc.utils.enums.EnumVO;

import java.util.List;

/**
 * 物流单配送方式 Service 接口
 *
 * <AUTHOR> @date 2021-11-19
 */
public interface IWuLiuDeliveryService extends IService<WuLiuDeliveryEntity> {

     /**
      * 获取所有物流单配送方式
      *
      * @return List<WuLiuDelivery>
      * <AUTHOR> [<EMAIL>]
      * @date 2021-11-19
      */
     List<WuLiuDeliveryEntity> listAll();

     /**
      * 获取所有物流单配送方式
      *
      * @return List<EnumVO>
      * <AUTHOR> [<EMAIL>]
      * @date 2021-11-19
      */
     List<EnumVO> listAllEnumVo();

}
