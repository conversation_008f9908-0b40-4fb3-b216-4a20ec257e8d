package com.jiuji.oa.wuliu.vo.req;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 批量生成中通快递单号(子母件) req VO
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-08
 */
@ApiModel(description = "批量生成中通快递单号(子母件) req VO")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class WuLiuGenerateMoreWuLiuNoReqVO extends WuLiuAddOrUpdateReqVO {

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @JsonProperty("ECount")
    @JSONField(name = "ECount")
    private Integer eCount;

}
