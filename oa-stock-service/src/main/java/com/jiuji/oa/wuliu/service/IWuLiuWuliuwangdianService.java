package com.jiuji.oa.wuliu.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.wuliu.bo.WuliuExpressMqBO;
import com.jiuji.oa.wuliu.entity.WuLiuWuliuwangdianEntity;

/**
 * 快递网点记录表[责任小组:物流组] 服务类
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-29
 */
public interface IWuLiuWuliuwangdianService extends IService<WuLiuWuliuwangdianEntity> {

    /**
     * getExpressType
     *
     * @param wuliuId
     * @return String
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-10
     */
    String getExpressType(Integer wuliuId);

    /**
     * shunfengApiServices.GetExepressType
     *
     * @param type
     * @return string
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-04
     */
    String getExpressType(String type);

    /**
     * subWLService.saveWuliuWandDian
     * 保存物流网点信息(中通，顺风)
     *
     * @param wuliuid
     * @param orgcode
     * @param destcode
     * @param exepresstype
     * @param paytype
     * @param yuejiekahao
     * @return bool
     * @date 2021-10-21
     * <AUTHOR> [<EMAIL>]
     */
    boolean saveWuliuWandDian(Integer wuliuid, String orgcode, String destcode, String exepresstype, Integer paytype, String yuejiekahao);

    /**
     * saveWuliuWandDian
     */
    void saveWuliuWandDian(WuliuExpressMqBO<WuLiuWuliuwangdianEntity> wuliuExpressMq);
}
