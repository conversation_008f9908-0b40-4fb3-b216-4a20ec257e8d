package com.jiuji.oa.wuliu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 售后预约产品信息[责任小组:销售] 实体类
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-11-04
 */
@Data
@Accessors(chain = true)
@TableName("shouhou_yuyueproductinfo")
@ApiModel(value = "WuLiuShouhouYuyueproductinfoEntity 实体类", description = "售后预约产品信息[责任小组:销售] 实体类")
public class WuLiuShouhouYuyueproductinfoEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 预约id
     */
    @ApiModelProperty("预约id")
    @TableField("yuyueid")
    private Integer yuyueid;

    /**
     * 商品规格id
     */
    @ApiModelProperty("商品规格id")
    @TableField("ppriceid")
    private Integer ppriceid;

    /**
     * 商品名称
     */
    @ApiModelProperty("商品名称")
    @TableField("productname")
    private String productname;

    /**
     * 商品颜色
     */
    @ApiModelProperty("商品颜色")
    @TableField("productcolor")
    private String productcolor;

    /**
     * 商品id
     */
    @ApiModelProperty("商品id")
    @TableField("productid")
    private Integer productid;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    @TableField("remark")
    private String remark;

    /**
     * 串号
     */
    @ApiModelProperty("串号")
    @TableField("imei")
    private String imei;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

}