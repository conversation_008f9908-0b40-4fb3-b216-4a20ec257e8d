package com.jiuji.oa.wuliu.vo.res;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 物流单新增 req VO
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-09-028
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class WuLiuResVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    private String sname;

    private String smobile;

    private String saddress;

    private String sarea;

    private Integer scityid;

    private String rname;

    private String rmobile;

    private String raddress;

    private String rarea;

    private Integer rcityid;

    private String area;

    private LocalDateTime dtime;

    private LocalDateTime ctime;

    private Double price;

    private Double inprice;

    private String shoujianren;

    private String paijianren;

    private Integer stats;

    private Integer danhaobind;

    private Integer wutype;

    private String comment;

    private String com;

    private String nu;

    private Double weight;

    private String inuser;

    private Integer linktype;

    private LocalDateTime sendtime;

    private Integer areaid;

    private Integer sareaid;

    private Integer rareaid;

    @TableField("receiveUser")
    private String receiveUser;

    @TableField("receiveTime")
    private LocalDateTime receiveTime;

    @TableField("notifyType")
    private Integer notifyType;

    @TableField("subKinds")
    private Integer subKinds;

    @TableField("wCateId")
    private Integer wCateId;

}
