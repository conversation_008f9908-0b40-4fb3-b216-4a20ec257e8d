package com.jiuji.oa.wuliu.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.wuliu.bo.WuliuExpressMqBO;
import com.jiuji.oa.wuliu.entity.WuLiuExpressExtendEntity;

/**
 * 服务类
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-22
 */
public interface IWuLiuExpressExtendService extends IService<WuLiuExpressExtendEntity> {

    /**
     * 查询京东快递类型
     * @param wuliuId
     * @return
     */
    String getJdExpressType(Integer wuliuId);

    /**
     * 查询京东快递类型
     * @param wuliuId
     * @return
     */
    Integer getJdDropMenuExpressType(Integer wuliuId);
    /**
     * saveWuliuExpressExtend
     * @param wuLiuExpressExtend
     */
    void saveWuliuExpressExtend(WuliuExpressMqBO<WuLiuExpressExtendEntity> wuLiuExpressExtend);
}
