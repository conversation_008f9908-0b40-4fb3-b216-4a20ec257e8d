package com.jiuji.oa.wuliu.vo;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * JdQueryTraceParamVO
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-12-15
 */
@Setter
@Getter
public class JdQueryTraceParamVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 物流类型
     */
    @JsonProperty("expressType")
    private Integer expressType;

    /**
     * 租户id
     */
    @JsonProperty("xTenantId")
    private Integer xtenantId;

    /**
     * 快递单号
     */
    @JsonProperty("routeNum")
    private String expressNumber;

    /**
     * 快递子类型
     */
    @JsonProperty("dropMenuExpressType")
    private Integer childExpressType;

}
