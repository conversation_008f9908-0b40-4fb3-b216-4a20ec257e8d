package com.jiuji.oa.wuliu.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.wuliu.bo.WuliuExpressMqBO;
import com.jiuji.oa.wuliu.entity.WuLiuShunfengNoInfoEntity;
import com.jiuji.oa.wuliu.mapper.WuLiuShunfengNoInfoMapper;
import com.jiuji.oa.wuliu.service.IWuLiuShunfengNoInfoService;
import org.apache.commons.collections4.ListUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * 顺丰电子面单信息[责任小组:物流] 服务实现类
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-11-03
 */
@Service
public class WuLiuShunfengNoInfoServiceImpl extends ServiceImpl<WuLiuShunfengNoInfoMapper, WuLiuShunfengNoInfoEntity> implements IWuLiuShunfengNoInfoService {

    /**
     * 批量新增
     *
     * @param list
     * @return boolean
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-29
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addAll(List<WuLiuShunfengNoInfoEntity> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            int size = list.size();
            if (list.size() > 500) {
                List<List<WuLiuShunfengNoInfoEntity>> partition = ListUtils.partition(list, 500);
                for (List<WuLiuShunfengNoInfoEntity> attachments : partition) {
                    Integer resultSize = baseMapper.addAll(attachments);
                    if (!Objects.equals(resultSize, size)) {
                        return false;
                    }
                }
            } else {
                Integer resultSize = baseMapper.addAll(list);
                return Objects.equals(resultSize, size);
            }
        }
        return false;
    }

    /**
     * saveShunfengNoInfo
     *
     * @param shunfengNoInfo
     */
    @Override
    @DS("oanewWrite")
    public void saveShunfengNoInfo(WuliuExpressMqBO<WuLiuShunfengNoInfoEntity> shunfengNoInfo) {
        if (Objects.nonNull(shunfengNoInfo) && Objects.nonNull(shunfengNoInfo.getData())) {
            this.save(shunfengNoInfo.getData());
        }
    }

    /**
     * 查询顺丰面单
     *
     * @param waybillNo
     * @param wuliuId
     * @return
     */
    @Override
    @DS("ch999oanew")
    public WuLiuShunfengNoInfoEntity queryByMailNoAndWuliuId(String waybillNo, Integer wuliuId) {
        if (StrUtil.isBlank(waybillNo) && Objects.isNull(wuliuId)) {
            return null;
        }
        return this.baseMapper.queryByMailNoAndWuliuId(waybillNo,wuliuId);
    }

}
