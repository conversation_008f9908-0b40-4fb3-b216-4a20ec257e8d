package com.jiuji.oa.wuliu.vo.res;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * WuLiuLogVO
 * </p>
 *
 * @description: WuLiuLogVO
 * @author: David
 * @create: 2021-10-11 10:22
 */
@Data
@Accessors(chain = true)
public class WuLiuLogResVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    @JsonProperty("inuser")
    @JSONField(name = "inuser")
    private String inuser;

    @JsonProperty("wuliuid")
    @JSONField(name = "wuliuid")
    private Integer wuliuid;

    private String msg;

    @JsonProperty("dtime")
    @JSONField(name = "dtime")
    private LocalDateTime dtime;

}