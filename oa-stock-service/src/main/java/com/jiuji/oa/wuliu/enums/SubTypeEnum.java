package com.jiuji.oa.wuliu.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * SubTypeEnum
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-12-05
 */
@Getter
@AllArgsConstructor
public enum SubTypeEnum implements CodeMessageEnumInterface {

    /**
     *
     */
    UNKNOWN(0, "未知"),

    /**
     *
     */
    NEW_ORDER(1, "新机订单"),

    /**
     *
     */
    GOOD_PRODUCT_ORDER(2, "良品订单"),

    /**
     *
     */
    AFTER_BOOKING(3, "售后预约"),

    /**
     *
     */
    AFTER_MAINTENANCE(4, "售后维修"),

    /**
     *
     */
    SMALL_PICK_UP_ORDER(5, "小件接件单"),

    /**
     *
     */
    RECYCLE_ORDER(6, "回收订单");

    /**
     * 枚举编码
     */
    private final Integer code;

    /**
     * 枚举信息
     */
    private final String message;

}
