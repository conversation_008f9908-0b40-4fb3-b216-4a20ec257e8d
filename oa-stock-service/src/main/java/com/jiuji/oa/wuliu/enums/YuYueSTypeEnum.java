package com.jiuji.oa.wuliu.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: gengjiaping
 * @date: 2020/3/17
 */
@Getter
@AllArgsConstructor
public enum YuYueSTypeEnum implements CodeMessageEnumInterface {
    YYDD(1,"预约到店"),
    SMQJ(2,"上门取件"),
    YJSX(3,"邮寄送修"),
    SMKX(4,"上门快修"),
    DMYY(5,"店面预约"),
    SMAZ(6,"上门安装");
    /**
     * 状态
     */
    private Integer code;
    /**
     * 名称
     */
    private String message;

    public static String getMessage(Integer code) {
        for (YuYueSTypeEnum yuYueSTypeEnum : values()) {
            if (yuYueSTypeEnum.getCode().equals(code)) {
                return yuYueSTypeEnum.getMessage();
            }
        }
        return "";
    }
}
