/*
 *    Copyright © 2006 - 2020 九机网 All Rights Reserved
 *
 */

package com.jiuji.oa.wuliu.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.oa.orginfo.areainfo.vo.res.AreaListRes;
import com.jiuji.oa.wuliu.bo.DiaoboPaotuiWuliuBO;
import com.jiuji.oa.wuliu.bo.DiaoboPaotuiWuliuResBO;
import com.jiuji.oa.wuliu.bo.SignatureWuliuBO;
import com.jiuji.oa.wuliu.entity.*;
import com.jiuji.oa.wuliu.vo.*;
import com.jiuji.oa.wuliu.vo.req.InnerWuliuInfoReq;
import com.jiuji.oa.wuliu.vo.req.WuLiuInfoReqVO;
import com.jiuji.oa.wuliu.vo.res.InnerWuliuInfoRes;
import com.jiuji.oa.wuliu.vo.res.WuLiuSendMsgRes;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 物流单Mapper
 *
 * <AUTHOR>
 * @date 2021-05-17 11:24:40
 */
@Mapper
public interface WuLiuMapper extends BaseMapper<WuLiuEntity> {

    /**
     * getWuLiuClaim
     *
     * @param wuliuid
     * @return WuLiuClaimEntity
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-05
     */
    WuLiuClaimEntity getWuLiuClaim(@Param("wuliuid") Integer wuliuid);

    /**
     * 第三方快递公司列表
     *
     * @return
     */
    @Select("SELECT expressName,expressCode,expressType FROM dbo.expressenum with(nolock) where isdel=0 and status=0 order by showRank")
    List<ExpressEnumEntity> listExpressEnum();

    /**
     * 获取物流信息
     *
     * @param actionName
     * @param wuLiuId
     * @param subId
     * @return
     */
    List<WuLiuDTO> getWuLiuList(@Param("actionName") String actionName, @Param("wuLiuId") Integer wuLiuId,
                                @Param("subId") Integer subId);

    /**
     * 获取单个物流信息
     *
     * @param actionName
     * @param wuLiuId
     * @param subId
     * @return
     */
    WuLiuDTO getWuLiu(@Param("actionName") String actionName, @Param("wuLiuId") Integer wuLiuId,
                      @Param("subId") Integer subId);

    /**
     * 查询basket
     *
     * @param subId
     * @return
     */
    @Select("select top 1 basket_id from dbo.basket with(nolock) where isnull(isdel,0)=0 and sub_id=#{subId} and ppriceid=77939")
    Integer getFastMeiTuanBySubId(@Param("subId") Integer subId);


    /**
     * get expectTime
     *
     * @param subId
     * @return
     */
    @Select("select top 1 expectTime from dbo.sub with(nolock) where sub_id = #{subId}")
    LocalDateTime getExpectTime(@Param("subId") Integer subId);

    /**
     * 良品预计送达时间
     * @param subId
     * @return
     */
    @Select("select top 1 expectTime from recover_marketInfo with(nolock) where sub_id = #{subId}")
    LocalDateTime getLiangpinExpectTime(@Param("subId") Integer subId);

    /**
     * 获取物流的订单
     *
     * @param subId
     * @return
     */
    WuLiuSubDTO getWuLiuSub(@Param("subId") Integer subId);

    /**
     * 自提点
     *
     * @param ziTiDianId
     * @return
     */
    @Select("select top 1 Contact receiver,tel1 mobile,cityid cityid1,address,tel2 tel,'' zipcode from dbo.zitidian with(nolock) where id=#{ziTiDianId}")
    ZiTiDianDTO getZiTiDian(@Param("ziTiDianId") Integer ziTiDianId);

    /**
     * getWuLiuInfoById
     *
     * @param id
     * @return WuLiu2Entity
     * @date 2021-10-12
     * <AUTHOR> [<EMAIL>]
     */
    WuLiu2Entity getWuLiuInfoById(Integer id);

    /**
     * getWuLiuInfoBySubId
     *
     * @param subId
     * @return WuLiu2Entity
     * @date 2021-10-12
     * <AUTHOR> [<EMAIL>]
     */
    WuLiu2Entity getWuLiuInfoBySubId(@Param("subId") Integer subId);

    /**
     * 二手良品订单物流
     *
     * @param subId
     * @return
     */
    WuLiuSubDTO getWuLiuReSub(@Param("subId") Integer subId);

    /**
     * 原先就有物流单
     *
     * @param vo
     * @return
     */
    WuLiuDTO getMarkAbnormalWuLiu(@Param("vo") WuLiuInfoReqVO vo);

    /**
     * updateRecoverAuction
     *
     * @param wuLiuId
     * @param wuLiuGongSi
     * @param subId
     * @return Integer
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-05
     */
    Integer updateRecoverAuction(@Param("wuLiuId") String wuLiuId, @Param("wuLiuGongSi") String wuLiuGongSi, @Param("subId") Integer subId);

    /**
     * updateRecoverAuctionLinkWuId
     *
     * @param wuId
     * @param inSourceId
     * @param wuLiuId
     * @param wuLiuGongSi
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-05
     */
    void updateRecoverAuctionLinkWuId(@Param("wuId") Integer wuId, @Param("inSourceId") Integer inSourceId,
                                      @Param("wuLiuId") String wuLiuId, @Param("wuLiuGongSi") String wuLiuGongSi);

    /**
     * selectUserByUserId
     *
     * @param userId
     * @return WuLiuWeixinUserEntity
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-05
     */
    WuLiuWeixinUserEntity selectUserByUserId(@Param("userId") Integer userId);

    /**
     * getsubConfirmStartTime
     *
     * @param subId
     * @return LocalDateTime
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-05
     */
    LocalDateTime getsubConfirmStartTime(Long subId);

    /**
     * getSecordHandsubConfirmStartTime
     *
     * @param subId
     * @return LocalDateTime
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-05
     */
    LocalDateTime getSecordHandsubConfirmStartTime(Long subId);

    /**
     * getZtoSiteConfig
     *
     * @param areaId
     * @return ZtoSiteConfigDTO
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-05
     */
    ZtoSiteConfigDTO getZtoSiteConfig(@Param("areaId") int areaId);

    /**
     * delWuliuByLinkId
     *
     * @param linkId
     * @return List<WuLiuEntity>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-05
     */
    List<WuLiuEntity> delWuliuByLinkId(@Param("linkId") Long linkId);

    /**
     * getJieXinByCmd
     *
     * @param subId
     * @return Integer
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-05
     */
    Integer getJieXinByCmd(@Param("subId") Long subId);

    /**
     * getAreaList
     *
     * @return List<AreaListRes>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-05
     */
    List<AreaListRes> getAreaList();


    /**
     * getAreaList
     *
     * @return List<AreaListRes>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-05
     */
    List<AreaWebModel> getAllAreaWeb();

    /**
     * getJieXinBySql
     *
     * @param subId
     * @return Integer
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-05
     */
    Integer getJieXinBySql(@Param("subId") Long subId);

    /**
     * getThirdPartyLog
     *
     * @param wuLiuId
     * @return List<SubWuliuTransferLogDTO>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-05
     */
    List<SubWuliuTransferLogDTO> getThirdPartyLog(@Param("wuliuid") int wuLiuId);

    void inserWuLiuNoEx(@Param("nuid") String nu, @Param("wuliuid") String wuLiuId, @Param("com") String com, @Param("packageCount") Integer packageCount);

    Integer getPrintCount(@Param("subId") Integer subId, @Param("type") Integer type);

    List<WuLiuSendMsgRes> getReceiveNameList(List<String> ids);

    List<WuLiuSendMsgRes> getInuserNameList(List<String> ids);

    /**
     * C# apiServices.getSubAreaidBySubAndPaytype
     * 根据单号和支付方式 查询地区关联 areaid (支付发起地方使用)
     * @param subId
     * @param payType
     * @return
     */
    Integer getSubAreaidBySubAndPaytype(@Param("subId") Integer subId, @Param("payType") Integer payType);

    String getExpressTypeByNu(String nu);

    /**
     * 计数美团闪购订单，用于判断指定订单是否伟美团闪购订单
     *
     * @param subId 订单 ID
     * @return Integer
     * <AUTHOR> [<EMAIL>]
     * @date 2022-01-24
     */
    Integer countMeituanFastSub(@Param("subId") Integer subId);

    /**
     * 发货门店和物流类型查询物流单
     * @param sAreaId
     * @param wuType
     * @return
     */
    WuLiuEntity getWuLiuByWuType(@Param("sAreaId") Integer sAreaId,
                                 @Param("wuType") Integer wuType);

    /**
     * PayMethod
     * @param areaId
     * @return
     */
    Integer queryPayMethodByAreaId(@Param("areaId") Integer areaId);

    /**
     * 快递单号查询物流单
     * @param nu
     * @return
     */
    List<WuLiuEntity> getWuliuByNu(@Param("nu") String nu);

    /**
     * 物流类型和绑定单号查询正在进行中（除了作废状态）的物流单
     * @param wuType
     * @param danhaobind
     * @return
     */
    WuLiuEntity getWuLiuByWuTypeAndDanhaobind(@Param("wuType") Integer wuType,
                                              @Param("danhaobind") Integer danhaobind);
    @DS("oanewWrite")
    void updateStateById(@Param("wuliuid")Integer wuliuid, @Param("stats")Integer stats);

    /**
     * 查询超时未完成物流单
     * @param days
     * @return
     */
    List<WuLiuEntity> getTimeoutWuliuList(@Param("days") Integer days);

    /**
     * 查询业务单据调拨单的物流单
     * @param req
     * @return
     */
    List<InnerWuliuInfoRes> getInnerWuliuInfo(@Param("req") InnerWuliuInfoReq req);

    /**
     * 内部物流单查询是否关联订单，良品，维修单订货调拨单
     * @param wuliuId
     * @return
     */
    Integer getInnerWuliuCount(@Param("wuliuId") Integer wuliuId);

    /**
     * 查询调拨物流单
     * @param wuliuId
     * @return
     */
    Integer getDiaobWuliuCount(@Param("wuliuId") Integer wuliuId);

    /**
     * 查询收货人是员工的代签收物流单
     * @return
     */
    Page<SignatureWuliuBO> querySignatureWuliuList(@Param("page") Page<SignatureWuliuBO> page);

    /**
     * 查询物流单关联发货调拨单信息
     *
     * @param data
     * @return
     */
    List<DiaoboPaotuiWuliuResBO> queryDiaoboPaotuiWuliu(@Param("req") DiaoboPaotuiWuliuBO data);
}
