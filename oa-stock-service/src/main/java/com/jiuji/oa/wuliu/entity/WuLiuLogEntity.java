/*
 *    Copyright © 2006 - 2020 九机网 All Rights Reserved
 *
 */

package com.jiuji.oa.wuliu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 物流单日志Entity
 *
 * <AUTHOR> liu ming
 * @date 2021-05-24 18:40:13
 */
@Data
@TableName("wuliu_logs")
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "物流单日志")
@RequiredArgsConstructor
public class WuLiuLogEntity extends Model<WuLiuLogEntity> implements Serializable {
    private static final long serialVersionUID = 1;

    /**
     * $column.comments
     */
    @ApiModelProperty(value = "$column.comments")
    private Integer statistcsCategray;
    /**
     * $column.comments
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "$column.comments")
    private Integer id;
    /**
     * $column.comments
     */
    @ApiModelProperty(value = "$column.comments")
    private String inuser;
    /**
     * $column.comments
     */
    @ApiModelProperty(value = "$column.comments")
    private Integer wuliuid;
    /**
     * $column.comments
     */
    @ApiModelProperty(value = "$column.comments")
    private String msg;
    /**
     * $column.comments
     */
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @ApiModelProperty(value = "$column.comments")
    private LocalDateTime dtime;
    /**
     * 日志类别 1、发货扫描日志
     */
    private Integer kind;
}
