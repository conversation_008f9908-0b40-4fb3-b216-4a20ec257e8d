package com.jiuji.oa.wuliu.service;

import com.ch999.common.util.vo.atlas.Coordinate;
import com.jiuji.oa.wuliu.entity.WuliuAddress;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 *
 */
public interface IWuliuAddressService extends IService<WuliuAddress> {

    /**
     * 快递单查询地址信息
     * @return
     */
    WuliuAddress queryByNu(String nu);

    /**
     * 地址获取经纬度
     * @param areaId 门店id
     * @param address 地址
     * @param cityId 城市id
     * @param type 0、优先地址，1、优先门店信息
     * @return
     */
    Coordinate getAreaCoordinate(Integer areaId,
                                 String address,
                                 Integer cityId,
                                 Integer type);

    /**
     * 地址获取经纬度
     * @param areaId 门店id
     * @param address 地址
     * @param cityId 城市id
     * @param type 0、优先地址，1、优先门店信息
     * @return
     */
    Coordinate getAreaCoordinateV2(Integer areaId,
                                 String address,
                                 Integer cityId,
                                 Integer type);
}
