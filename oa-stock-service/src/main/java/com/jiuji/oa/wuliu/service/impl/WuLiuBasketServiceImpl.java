package com.jiuji.oa.wuliu.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.stock.common.util.SysUtils;
import com.jiuji.oa.wuliu.bo.SellerInfoBO;
import com.jiuji.oa.wuliu.dto.OnlineNationalSupplementStockDTO;
import com.jiuji.oa.wuliu.entity.WuLiuBasket2Entity;
import com.jiuji.oa.wuliu.entity.WuLiuBasketEntity;
import com.jiuji.oa.wuliu.enums.SubTypeEnum;
import com.jiuji.oa.wuliu.mapper.WuLiuBasketMapper;
import com.jiuji.oa.wuliu.service.IWuLiuBasketService;
import com.jiuji.oa.wuliu.service.IWuLiuService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 订单商品表,责任小组：销售 服务实现类
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-15
 */
@Slf4j
@DS("oanewWrite")
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy, @Autowired})
public class WuLiuBasketServiceImpl extends ServiceImpl<WuLiuBasketMapper, WuLiuBasketEntity> implements IWuLiuBasketService {

    /**
     * 微信红包 ppriceid
     */
    private static final List<Integer> RED_PACKET_PPRICE_ID_LIST = Arrays.asList(42527, 42665);

    private final IWuLiuService wuLiuService;

    @Override
    public List<WuLiuBasket2Entity> getSubBasket(Integer subId, Integer showDel, Integer xcAreaId) {
        int showDel2 = Optional.ofNullable(showDel).orElse(0);
        int xcAreaId2 = Optional.ofNullable(xcAreaId).orElse(0);
        List<WuLiuBasket2Entity> list;
        //九机历史订单走历史订单逻辑  输出没有历史库不需要走历史订单逻辑
        if (Objects.equals(SysUtils.getIntXtenantId(), 0)) {
            if (wuLiuService.isHistory(subId, SubTypeEnum.NEW_ORDER.getCode())) {
                DynamicDataSourceContextHolder.push("ch999oahis");
                list = baseMapper.getSubBasket(subId, RED_PACKET_PPRICE_ID_LIST, xcAreaId2, showDel2);
            } else {
                list = baseMapper.getSubBasket(subId, RED_PACKET_PPRICE_ID_LIST, xcAreaId2, showDel2);
            }
        } else {
            list = baseMapper.getSubBasket(subId, RED_PACKET_PPRICE_ID_LIST, xcAreaId, showDel2);
        }
        return list;
    }

    /**
     * @param subId
     */
    @Override
    public SellerInfoBO getSellerInfoBySubId(Integer subId) {
        return baseMapper.getSellerInfoBySubId(subId);
    }

    /**
     * 线上国补订单查询库存信息
     * @return
     */
    @Override
    public OnlineNationalSupplementStockDTO getOnlineNationalSupplementStockBySubId(Integer subId) {
        return baseMapper.getOnlineNationalSupplementStockBySubId(subId);
    }

}
