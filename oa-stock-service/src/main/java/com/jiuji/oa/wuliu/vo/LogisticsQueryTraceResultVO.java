package com.jiuji.oa.wuliu.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.codehaus.jackson.annotate.JsonProperty;

import java.io.Serializable;
import java.util.List;

/**
 * LogisticsQueryTraceResultVO
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-12-13
 */
@Setter
@Getter
public class LogisticsQueryTraceResultVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("errCode")
    private String errCode;

    @JsonProperty("name")
    private String name;

    @JsonProperty("order")
    private String order;

    @JsonProperty("data")
    private List<QueryTraceContentVO> queryTraceContents;

    /**
     * 路由轨迹内容
     *
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-13
     */
    @Setter
    @Getter
    @ToString
    public static class QueryTraceContentVO implements Serializable {

        private static final long serialVersionUID = 1L;

        @JsonProperty("content")
        private String content;

        @JsonProperty("time")
        private String time;

    }

}


