package com.jiuji.oa.wuliu.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;


/**
 * 派送页面搜索实体
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-11-09
 */
@Data
@Accessors(chain = true)
public class SubSendListModelDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("sub_id")
    @JSONField(name = "sub_id")
    private Integer subId;

    @JsonProperty("area")
    @JSONField(name = "area")
    private String area;

    @JsonProperty("sub_mobile")
    @JSONField(name = "sub_mobile")
    private String subMobile;

    @JsonProperty("Address")
    @JSONField(name = "Address")
    private String address;

    @JsonProperty("zitidianID")
    @JSONField(name = "zitidianID")
    private Integer zitidianId;

    @JsonProperty("delivery")
    @JSONField(name = "delivery")
    private Integer delivery;

    @JsonProperty("subtype")
    @JSONField(name = "subtype")
    private Integer subtype;

    @JsonProperty("dsc")
    @JSONField(name = "dsc")
    private String dsc;

    @JsonProperty("sub_date")
    @JSONField(name = "sub_date")
    private LocalDateTime subDate;

    @JsonProperty("paisongmin")
    @JSONField(name = "paisongmin")
    private LocalDateTime paisongmin;

    @JsonProperty("waitTime")
    @JSONField(name = "waitTime")
    private LocalDateTime waitTime;

    @JsonProperty("sendTime")
    @JSONField(name = "sendTime")
    private LocalDateTime sendTime;

    /**
     * 配送用时,从下单到当前时间
     */
    @JsonProperty("paisongmin2")
    @JSONField(name = "paisongmin2")
    private Integer paisongmin2;

    @JsonProperty("trader")
    @JSONField(name = "trader")
    private String trader;

    @JsonProperty("paisongState")
    @JSONField(name = "paisongState")
    private Integer paisongState;

    @JsonProperty("product")
    @JSONField(name = "product")
    private List<SubSendProductListDTO> product;

    /**
     * 用户配送日期
     */
    @JsonProperty("userDate")
    @JSONField(name = "userDate")
    private String userDate;

    /**
     * 用户配送时段
     */
    @JsonProperty("userTime")
    @JSONField(name = "userTime")
    private Integer userTime;

    @JsonProperty("areaid")
    @JSONField(name = "areaid")
    private Integer areaid;

    @JsonProperty("insourceid")
    @JSONField(name = "insourceid")
    private Integer insourceid;

    @JsonProperty("isSpecial")
    @JSONField(name = "isSpecial")
    private Boolean isSpecial;

    @JsonProperty("isXianHuo")
    @JSONField(name = "isXianHuo")
    private Boolean isXianHuo;

    /**
     * 是否支付
     */
    @JsonProperty("isPayed")
    @JSONField(name = "isPayed")
    private Boolean isPayed;

    /**
     * 排序
     */
    @JsonProperty("ranks")
    @JSONField(name = "ranks")
    private Integer ranks;

    @JsonProperty("sub_pay")
    @JSONField(name = "sub_pay")
    private String subPay;

    @JsonProperty("tradeDate")
    @JSONField(name = "tradeDate")
    private LocalDateTime tradeDate;

    @JsonProperty("tradeDate1")
    @JSONField(name = "tradeDate1")
    private LocalDateTime tradeDate1;

    /**
     * 订单状态
     */
    @JsonProperty("sub_check")
    @JSONField(name = "sub_check")
    private Integer subCheck;

    /**
     * 考勤状态
     */
    @JsonProperty("kaoqinFlag")
    @JSONField(name = "kaoqinFlag")
    private Boolean kaoqinFlag;

    /**
     * 会员级别
     */
    @JsonProperty("userClass")
    @JSONField(name = "userClass")
    private Integer userClass;

    /**
     * 加入派送单时间
     */
    @JsonProperty("addTime")
    @JSONField(name = "addTime")
    private LocalDateTime addTime;

    /**
     * 订单备注
     */
    @JsonProperty("remark")
    @JSONField(name = "remark")
    private String remark;

}
