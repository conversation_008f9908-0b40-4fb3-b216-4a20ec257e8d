package com.jiuji.oa.wuliu.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * SubLog.SubLog
 * 订单物流信息 VO
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-08
 */
@Data
@Accessors(chain = true)
public class SubLogVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("_id")
    @JSONField(name = "_id")
    private Integer id;

    @JsonProperty("ID")
    @JSONField(name = "ID")
    private Integer id2;

    /**
     * 单号
     */
    @JsonProperty("SubID")
    @JSONField(name = "SubID")
    private Integer subId;

    /**
     * 操作 说明
     */
    @JsonProperty("Comment")
    @JSONField(name = "Comment")
    private String comment;

    /**
     * 操作人员
     */
    @JsonProperty("InUser")
    @JSONField(name = "InUser")
    private String inUser;

    /**
     * 操作时间
     */
    @JsonProperty("DTime")
    @JSONField(name = "DTime")
    private LocalDateTime dTime = LocalDateTime.now();

    /**
     * 操作类型 1、订单 sub 2、basket 3、派送单
     */
    @JsonProperty("Type")
    @JSONField(name = "Type")
    private Integer type;

    /**
     * 显示 类型  1 代表前台显示， 不限 代表后台OA显示
     */
    @JsonProperty("showType")
    @JSONField(name = "showType")
    private Boolean showType;

}
