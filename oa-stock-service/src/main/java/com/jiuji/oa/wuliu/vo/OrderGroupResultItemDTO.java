package com.jiuji.oa.wuliu.vo;


import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * zhongtongApiServices.orderGroupResultItem
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-16
 */
@Data
@Accessors(chain = true)
public class OrderGroupResultItemDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单 ID
     */
    @JsonProperty("orderId")
    @JSONField(name = "orderId")
    private String orderId;

    /**
     * 单号
     */
    @JsonProperty("billCode")
    @JSONField(name = "billCode")
    private String billCode;

    /**
     * 网点编号
     */
    @JsonProperty("siteCode")
    @JSONField(name = "siteCode")
    private String siteCode;

    /**
     * 网点名称
     */
    @JsonProperty("siteName")
    @JSONField(name = "siteName")
    private String siteName;

    /**
     * 大头笔
     */
    @JsonProperty("bigMark")
    @JSONField(name = "bigMark")
    private String bigMark;

    /**
     *
     */
    @JsonProperty("message")
    @JSONField(name = "message")
    private String message;

}
