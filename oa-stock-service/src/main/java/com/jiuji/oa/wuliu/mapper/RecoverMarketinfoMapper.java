package com.jiuji.oa.wuliu.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.orderdynamics.vo.response.QueryWuliuBySubResVO;
import com.jiuji.oa.stock.develivery.vo.req.OrderOutStockPageReqVO;
import com.jiuji.oa.stock.develivery.vo.res.OrderOutStockPageRes;
import com.jiuji.oa.wuliu.dto.SubExpectTimeDTO;
import com.jiuji.oa.wuliu.entity.RecoverMarketinfo;
import com.jiuji.oa.wuliu.entity.WuLiuEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 转售单（良品订单）[责任小组:回收] Mapper 接口
 * </p>
 *
 * <AUTHOR> yao yao
 * @since 2022-06-08
 */
public interface RecoverMarketinfoMapper extends BaseMapper<RecoverMarketinfo> {

    /**
     * 良品单查询物流单信息
     * @param subId
     * @return
     */
    QueryWuliuBySubResVO queryWuliuBySubId(@Param("subId") Integer subId);

    /**
     * 良品单查询物流单信息 包含调拨
     * @param subId
     * @return
     */
    List<WuLiuEntity> queryWuliuEntityBySubId(@Param("subId") Integer subId);
    /**
     * 查询良品订单规定送达时间
     * @param subId
     * @return
     */
    SubExpectTimeDTO getSubExpectTimeBySubId(Integer subId);

    List<OrderOutStockPageRes> getSubIdListByPpid(@Param("req")OrderOutStockPageReqVO req);

    List<OrderOutStockPageRes> getSubIdListByMkcId(@Param("req")OrderOutStockPageReqVO req);

    List<OrderOutStockPageRes> getSubIdListByImei(@Param("req")OrderOutStockPageReqVO req);

    List<OrderOutStockPageRes> getSubIdListByBarcode(@Param("req")OrderOutStockPageReqVO req);
}
