package com.jiuji.oa.wuliu.service;

import com.jiuji.oa.wuliu.entity.WuLiuWuliuIsvstoreEntity;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 *  服务类
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-12-15
 */
public interface IWuLiuWuliuIsvstoreService extends IService<WuLiuWuliuIsvstoreEntity> {

    /**
     * getWuLiuIsvstoreByWuliuId
     *
     * @param wuliuId
     * @return WuLiuWuliuIsvstoreEntity
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-15
     */
    WuLiuWuliuIsvstoreEntity getWuLiuIsvstoreByWuliuId(Integer wuliuId);

}
