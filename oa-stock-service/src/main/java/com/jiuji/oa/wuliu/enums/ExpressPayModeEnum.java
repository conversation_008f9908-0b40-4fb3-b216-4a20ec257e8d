package com.jiuji.oa.wuliu.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 快递支付方式
 *
 * <AUTHOR>
 * @date 2021-12-04
 */
@Getter
@RequiredArgsConstructor
public enum ExpressPayModeEnum implements CodeMessageEnumInterface {
    /**
     *
     */
    /**
     * 寄方付
     */
    MAILER(1, "寄方付"),

    /**
     * 收方付
     */
    CONSIGNEE(2, "收方付"),

    /**
     * 第三方付
     */
    THIRD_PARTY(3, "第三方付");

    private final Integer code;
    private final String message;
}
