package com.jiuji.oa.wuliu.vo;


/**
 * <AUTHOR> [<EMAIL>]
 * @date 2021-12-08
 */

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * C# LogInfo
 * LogInfoVO
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-12-08
 */
@Data
public class LogInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @JsonProperty("UserID")
    public Integer userId;

    /**
     * 用户名
     */
    @JsonProperty("UserName")
    public String userName;

    /**
     * 用户区域
     */
    @JsonProperty("Area")
    public String area;

    /**
     * 权限值
     */
    @JsonProperty("Rank")
    public List<String> rank;

    /**
     * 二次密码
     */
    @JsonProperty("SecondPWD")
    public String secondPwd;

    /**
     * 部门名称
     */
    @JsonProperty("Department")
    public String department;

    /**
     *
     */
    @JsonProperty("area_kind1")
    public Integer areaKind1;

    /**
     *
     */
    @JsonProperty("level")
    public Integer level;

    /**
     * 职务
     */
    @JsonProperty("zhiwu")
    public String zhiwu;

    /**
     * 分管
     */
    @JsonProperty("rank_fenguan")
    public String rankFenguan;

    /**
     * 拥有哪些地区的管理权
     */
    @JsonProperty("Areas")
    public String[] areas;

    /**
     * 预借款
     */
    @JsonProperty("yjkauto")
    public String yjkauto;

    /**
     * 模式
     */
    @JsonProperty("show_kind")
    public String showKind;

    /**
     * 特许授权
     */
    @JsonProperty("authorizeid")
    public Integer authorizeid;

    /**
     * 平台 租户
     */
    @JsonProperty("xtenant")
    public Integer xtenant;

    /**
     *
     */
    @JsonProperty("errorMsg")
    public String errorMsg;

    /**
     * 门店主键
     */
    @JsonProperty("areaid")
    public Integer areaid;

    /**
     * 主要角色
     */
    @JsonProperty("mainRole")
    public Integer mainRole;

    /**
     * 用户角色
     */
    @JsonProperty("Roles")
    public String roles;

    /**
     * ip
     */
    @JsonProperty("userip")
    public String userip;

    /**
     *
     */
    @JsonProperty("isshixi")
    public Integer isshixi;

    /**
     * 职务id
     */
    @JsonProperty("zhiwuid")
    public Integer zhiwuid;

    /**
     * 用户归属后台地区
     */
    @JsonProperty("area1id")
    public Integer area1id;

    /**
     * 组织架构归属id
     */
    @JsonProperty("depart_id")
    public Integer departId;

    /**
     * 是否税务模式
     */
    @JsonProperty("IsTaxModel")
    public Boolean isTaxModel;

    /**
     * 登录证书对应地区id
     */
    @JsonProperty("SslAreaId")
    public Integer sslAreaId;

    /**
     * 登录证书对应地区 纳税主体 值为(1：分公司、2：个体经营部)
     */
    @JsonProperty("AreaTaxPayer")
    public Integer areaTaxPayer;

    /**
     * 登录证书对应地区 总公司主体
     */
    @JsonProperty("AreaTaxCompanyId")
    public Integer areaTaxCompanyId;

    /**
     * 税务模式公司主体
     */
    @JsonProperty("TaxCompanyId")
    public List<Integer> taxCompanyId;

}
