package com.jiuji.oa.wuliu.vo.req;

import com.jiuji.oa.nc.common.req.OaAttachmentsAddOrUpdateReqVO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 物流单更新 req VO
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-08
 */
@ApiModel(description = "物流单更新 req VO")
@Data
@Accessors(chain = true)
public class WuLiuUpdateReqVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键 ID
     */
    private Integer id;

    /**
     * (发送方)姓名
     */
    private String senderName;

    /**
     * (发送方)手机号
     */
    private String senderMobile;

    /**
     * (发送方)地址
     */
    private String senderAddress;

    /**
     * (发送方)大区
     */
    private String senderArea;

    /**
     * (发送方)城市ID（did）
     */
    private Integer senderCityId;

    /**
     * (接收方)姓名
     */
    private String recipientName;

    /**
     * (接收方)手机号
     */
    private String recipientMobile;

    /**
     * (接收方)地址
     */
    private String recipientAddress;

    /**
     * 接收地区
     */
    private String recipientArea;

    /**
     * (接收方)城市ID（did）
     */
    private Integer recipientCityId;

    /**
     * 物流单生成地区
     */
    private String area;

    /**
     * 登记时间
     */
    private LocalDateTime dTime;

    /**
     * 完成时间
     */
    private LocalDateTime cTime;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 成本价
     */
    private BigDecimal inPrice;

    /**
     * 收件人
     */
    private String shouJianRen;

    /**
     * 派件人
     */
    private String paiJianRen;

    /**
     * 状态
     */
    private Integer stats;

    /**
     * linkType=2 || linkType=3 预约单
     * wuType=4 || wuType=6 订单
     * shouhouid != 0 售后单 ?
     * wuType=9 良品订单
     * wuType=7 && linkType=7 上门回收
     */
    private Integer danHaoBind;

    /**
     * 类别
     */
    private Integer wuType;

    /**
     * 备注
     */
    private String comment;

    /**
     * 第三方快递
     */
    private String com;

    /**
     * 快递单号
     */
    private String nu;

    /**
     * 重量
     */
    private BigDecimal weight;

    /**
     * 操作录入人
     */
    private String inUser;

    /**
     * 关联类型 关联yuyueid（2:上门取件，3:送件上门） 关联sub_id(其它) 5 好像是维修  6 发票物流单  7 回收上门取件,11九机集市, 13固定资产调拨 ，20 配件调拨，21大件调拨
     */
    private Integer linkType;

    private LocalDateTime sendTime;

    /**
     * 地区ID
     */
    private Integer areaId;

    /**
     * 寄的地区ID
     */
    private Integer senderAreaId;

    /**
     * 收的地区ID
     */
    private Integer recipientAreaId;

    /**
     * 签收人
     */
    private String receiveUser;

    /**
     * 签收时间
     */
    private LocalDateTime receiveTime;

    /**
     * 通知类型 0 不通知(手动填加的默认不通知) 1和null 系统生成的物流单 2 售后转地区生成的物流单
     */
    private Integer notifyType;

    /**
     * 订单类型 1 普通订单 2良品单 3售后单
     */
    private Integer subKinds;

    /**
     * 支付方式
     */
    private Integer payMethod;

    /**
     * 合并后父级保留物流单号
     */
    private Integer wuPid;

    /**
     * 物流分类id
     */
    private Integer wuCateId;

    /**
     *
     */
    private LocalDateTime lastRouteTime;

    /**
     *
     */
    private LocalDateTime estimatedArrivalTime;

    /**
     *
     */
    private BigDecimal productPriceTotal;

    /**
     * 是否手动创建物流单：true=是，false=否
     */
    private Boolean createManuallyFlag;

    private List<OaAttachmentsAddOrUpdateReqVO.FileBO> fileList;

}
