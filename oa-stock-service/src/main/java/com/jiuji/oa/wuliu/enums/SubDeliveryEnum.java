package com.jiuji.oa.wuliu.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 订单配送方式
 *
 * @date 2021/10/09
 */
@Getter
@AllArgsConstructor
public enum SubDeliveryEnum implements CodeMessageEnumInterface {
    /**
     * 登记时间
     */
    SELF (1,"到店自取"),
    JIUJI(2,"九机快送"),
    SELF_LIFTING(3,"自提点"),
    EXPRESS(4,"快递运输"),
    EXPEDITED_DELIVERY(5,"加急配送"),
    THIRD_PARTY(6,"第三方派送")
    ;



    /**
     * 代码
     */
    private Integer code;
    /**
     * 消息
     */
    private String message;

    /**
     * message
     * @param deliveryCode
     * @return
     */
    public static String getMessageByCode (Integer deliveryCode) {
        for (SubDeliveryEnum subDeliveryEnum : values()) {
            if (subDeliveryEnum.getCode().equals(deliveryCode)) {
                return subDeliveryEnum.getMessage();
            }
        }
        return "";
    }
}
