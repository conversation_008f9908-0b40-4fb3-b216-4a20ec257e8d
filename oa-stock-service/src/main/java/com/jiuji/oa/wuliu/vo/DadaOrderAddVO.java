package com.jiuji.oa.wuliu.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/5 14:31
 */
@Data
public class DadaOrderAddVO {
    /**
     * 门店编号
     */
    @JsonProperty("shop_no")
    @JSONField(name = "shop_no")
    @NotNull
    private String shopNo;
    /**
     * 第三方订单ID
     */
    @JsonProperty("origin_id")
    @JSONField(name = "origin_id")
    @NotNull
    private String originId;
    /**
     * 订单所在城市的code
     */
    @JsonProperty("city_code")
    @JSONField(name = "city_code")
    @NotNull
    private String cityCode;
    /**
     * 订单金额（单位：元）
     */
    @JsonProperty("cargo_price")
    @JSONField(name = "cargo_price")
    @NotNull
    private BigDecimal cargoPrice;
    /**
     * 是否需要垫付 1:是 0:否 (垫付订单金额，非运费)
     */
    @JsonProperty("is_prepay")
    @JSONField(name = "is_prepay")
    @NotNull
    private Integer isPrepay;
    /**
     * 收货人姓名
     */
    @JsonProperty("receiver_name")
    @JSONField(name = "receiver_name")
    @NotNull
    private String receiverName;
    /**
     * 收货人地址
     */
    @JsonProperty("receiver_address")
    @JSONField(name = "receiver_address")
    @NotNull
    private String receiverAddress;
    /**
     * 收货人地址纬度（高德坐标系，若是其他地图经纬度需要转化成高德地图经纬度）
     */
    @JsonProperty("receiver_lat")
    @JSONField(name = "receiver_lat")
    @NotNull
    private Double receiverLat;
    /**
     * 收货人地址经度（高德坐标系，若是其他地图经纬度需要转化成高德地图经纬度)
     */
    @JsonProperty("receiver_lng")
    @JSONField(name = "receiver_lng")
    @NotNull
    private Double receiverLng;
    /**
     * 回调URL
     */
    private String callback;
    /**
     * 订单重量（单位：Kg）
     */
    @JsonProperty("cargo_weight")
    @JSONField(name = "cargo_weight")
    @NotNull
    private Double cargoWeight;
    /**
     * 收货人手机号（手机号和座机号必填一项）
     */
    @JsonProperty("receiver_phone")
    @JSONField(name = "receiver_phone")
    private String receiverPhone;
    /**
     * 收货人座机号（手机号和座机号必填一项）
     */
    @JsonProperty("receiver_tel")
    @JSONField(name = "receiver_tel")
    private String receiverTel;
    /**
     * 小费（单位：元，精确小数点后一位）
     */
    private BigDecimal tips;
    /**
     * 订单备注
     */
    private String info;
    /**
     * 订单商品类型：食品小吃-1,饮料-2,鲜花绿植-3,文印票务-8,便利店-9,水果生鲜-13,
     * 同城电商-19, 医药-20,蛋糕-21,酒品-24,小商品市场-25,服装-26,汽修零配-27,
     * 数码家电-28,小龙虾-29,个人-50,火锅-51,个护美妆-53、母婴-55,家居家纺-57,
     * 手机-59,家装-61,其他-5
     */
    @JsonProperty("cargo_type")
    @JSONField(name = "cargo_type")
    private Integer cargoType;
    /**
     * 订单商品数量
     */
    @JsonProperty("cargo_num")
    @JSONField(name = "cargo_num")
    private Integer cargoNum;
    /**
     * 发票抬头
     */
    @JsonProperty("invoiceTitle")
    @JSONField(name = "invoiceTitle")
    private String invoiceTitle;
    /**
     * 订单来源标示（只支持字母，最大长度为10）
     */
    @JsonProperty("origin_mark")
    @JSONField(name = "origin_mark")
    private String originMark;
    /**
     * 订单来源编号，最大长度为30，该字段可以显示在骑士APP订单详情页面，示例：
     * origin_mark_no:"#京东到家#1"
     * 达达骑士APP看到的是：#京东到家#1
     */
    @JsonProperty("origin_mark_no")
    @JSONField(name = "origin_mark_no")
    private String originMarkNo;
    /**
     * 是否使用保价费（0：不使用保价，1：使用保价； 同时，请确保填写了订单金额（cargo_price））
     * 商品保价费(当商品出现损坏，可获取一定金额的赔付)
     * 保费=配送物品实际价值*费率（5‰），配送物品价值及最高赔付不超过10000元， 最高保费为50元（物品价格最小单位为100元，
     * 不足100元部分按100元认定，保价费向上取整数， 如：物品声明价值为201元，保价费为300元*5‰=1.5元，取整数为2元。）
     * 若您选择不保价，若物品出现丢失或损毁，最高可获得平台30元优惠券。 （优惠券直接存入用户账户中）。
     */
    @JsonProperty("is_use_insurance")
    @JSONField(name = "is_use_insurance")
    private Integer isUseInsurance;
    /**
     * 收货码（0：不需要；1：需要。收货码的作用是：骑手必须输入收货码才能完成订单妥投）
     */
    @JsonProperty("is_finish_code_needed")
    @JSONField(name = "is_finish_code_needed")
    private Integer isFinishCodeNeeded;
    /**
     * 预约发单时间（预约时间unix时间戳(10位),精确到分;整分钟为间隔，
     * 并且需要至少提前5分钟预约，可以支持未来3天内的订单发预约单。）
     */
    @JsonProperty("delay_publish_time")
    @JSONField(name = "delay_publish_time")
    private Integer delayPublishTime;
    /**
     * 是否选择直拿直送（0：不需要；1：需要。选择直拿直送后，同一时间骑士只能配送此订单至完成，同时，也会相应的增加配送费用）
     */
    @JsonProperty("is_direct_delivery")
    @JSONField(name = "is_direct_delivery")
    private Integer isDirectDelivery;
    /**
     * 订单商品明细
     */
    @JsonProperty("product_list")
    @JSONField(name = "product_list")
    private List<DadaProductModelVO> productList;
    /**
     * 货架信息,该字段可在骑士APP订单备注中展示
     */
    @JsonProperty("pick_up_pos")
    @JSONField(name = "pick_up_pos")
    private String pickUpPos;
}
