package com.jiuji.oa.wuliu.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * commonApiModel
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-16
 */
@Data
@Accessors(chain = true)
public class CommonApiModelDTO<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 签名串
     */
    @JsonProperty("sign")
    @JSONField(name = "sign")
    private String sign;

    /**
     * 时间戳
     */
    @JsonProperty("timestamp")
    @JSONField(name = "timestamp")
    private Long timestamp;

    /**
     * 模型
     */
    @JsonProperty("Data")
    @JSONField(name = "Data")
    private T data;

    @JsonProperty("istest")
    @JSONField(name = "istest")
    private Integer isTest;

}
