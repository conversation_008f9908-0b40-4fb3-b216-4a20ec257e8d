package com.jiuji.oa.wuliu.vo.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 物流单取消/作废 req VO
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-09-29
 */
@ApiModel(description = "物流单取消/作废 req VO")
@Data
@Accessors(chain = true)
public class WuLiuCancelReqVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 物流单 ID
     */
    @NotNull
    @ApiModelProperty(value = "物流单 ID", required = true)
    private Integer id;

    /**
     * 取消/作废原因
     */
    @Length(max = 100, message = "取消/作废因不能超过 100 个字符")
    @ApiModelProperty(value = "取消/作废原因")
    private String reason;

}
