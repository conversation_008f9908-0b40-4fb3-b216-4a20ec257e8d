package com.jiuji.oa.wuliu.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.stock.stockmanage.vo.req.WuliuInstockPendingCaigouVO;
import com.jiuji.oa.stock.stockmanage.vo.req.WuliuInstockPendingSumVO;
import com.jiuji.oa.stock.stockmanage.vo.req.WuliuInstockPendingUpdateVO;
import com.jiuji.oa.wuliu.entity.WuliuClaimform;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR> yao yao
 * @since 2023-10-19
 */
public interface WuliuInstockPendingMapper extends BaseMapper<WuliuClaimform> {

    List<WuliuInstockPendingSumVO> getPendingSum(@Param("req")WuliuInstockPendingUpdateVO req);

    List<WuliuInstockPendingCaigouVO> getPendingCaigouList(@Param("wuLiuId")Integer wuLiuId);

    String selectThirdProductConfigNotifyUserIds();

}
