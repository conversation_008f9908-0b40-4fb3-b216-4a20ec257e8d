package com.jiuji.oa.wuliu.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * shunfengApiServices.ShunfengCard
 * 顺丰月结卡 VO
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-08
 */
@Data
@Accessors(chain = true)
public class ShunFengCardVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 月结卡
     */
    @JsonProperty("custid")
    @JSONField(name = "custid")
    private String custId;

    /**
     * 顾客编码
     */
    @JsonProperty("clientCode")
    @JSONField(name = "clientCode")
    private String clientCode;

    /**
     * 校验码
     */
    @JsonProperty("checkPwd")
    @JSONField(name = "checkPwd")
    private String checkPwd;

}
