package com.jiuji.oa.wuliu.component;

import cn.hutool.extra.spring.SpringUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.jiuji.oa.nc.common.config.rabbitmq.RabbitMqConfig;
import com.jiuji.oa.nc.common.exception.CustomizeException;
import com.jiuji.oa.stock.common.util.JacksonJsonUtils;
import com.jiuji.oa.stock.common.util.SysUtils;
import com.jiuji.oa.stock.logistics.order.service.ILogisticsOrderService;
import com.jiuji.oa.stock.logisticscenter.serive.ILogisticsExpressService;
import com.jiuji.oa.wuliu.bo.*;
import com.jiuji.oa.wuliu.constant.WuliuExpressConstant;
import com.jiuji.oa.wuliu.entity.*;
import com.jiuji.oa.wuliu.service.*;
import com.jiuji.oa.wuliu.utils.WuliuUtil;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * 手机退货未完成推送消息
 *
 * <AUTHOR>
 * @date 2021/9/13 15:16
 */

@Slf4j
@Component
public class WuliuExpressListener {

    @Resource
    private IWuLiuShunfengNoInfoService wuLiuShunfengNoInfoService;
    @Resource
    private IWuLiuWuliuwangdianService wuLiuWuliuwangdianService;
    @Resource
    private IJingdongPrintInfoService jingdongPrintInfoService;
    @Resource
    private ISaasJingdongPrintInfoService saasJingdongPrintInfoService;
    @Resource
    private IWuLiuExpressExtendService wuliuExpressExtendService;
    @Resource
    private IWuLiuLogService wuLiuLogService;
    @Resource
    private IWuliunoexService wuliunoexService;
    @Resource
    private ZtoBillInfoService ztoBillInfoService;
    @Resource
    private IWuLiuBusService wuLiuBusService;
    @Resource
    private ILogisticsExpressService logisticsExpressService;

    @RabbitListener(queues = RabbitMqConfig.QUEUE_WULIU_EXPRESS_SYNC, containerFactory = "manualContainerFactory")
    public void handleWuliuMessage(Message message, Channel channel) {
        try {
            String msg = new String(message.getBody(), StandardCharsets.UTF_8);
            log.info("从rabbitmq获取快递消息：{}", msg);
            if (StringUtils.isEmpty(msg)) {
                log.warn("从rabbitmq获取快递消息为空！");
                return;
            }
            WuliuExpressMqBO<Object> mqMesage = JacksonJsonUtils.toClass(msg, new TypeReference<WuliuExpressMqBO<Object>>() {
            });
            String act = Objects.nonNull(mqMesage) ? mqMesage.getAct() : "";
            switch (act) {
                case WuliuExpressConstant.ACT_JINGDONGPRINTINFO:
                    WuliuExpressMqBO<JingdongPrintInfo> jingdongPrintInfoMsg = JacksonJsonUtils.toClass(msg, new TypeReference<WuliuExpressMqBO<JingdongPrintInfo>>() {});
                    if (SysUtils.isJiuJiProd()) {
                        saasJingdongPrintInfoService.saveSaasJingdongPrintInfo(jingdongPrintInfoMsg);
                    }
                    jingdongPrintInfoService.saveJingdongPrintInfo(jingdongPrintInfoMsg);
                    break;
                case WuliuExpressConstant.ACT_SHUNFENGNOINFO:
                    WuliuExpressMqBO<WuLiuShunfengNoInfoEntity> shunfengNoInfo = JacksonJsonUtils.toClass(msg, new TypeReference<WuliuExpressMqBO<WuLiuShunfengNoInfoEntity>>() {});
                    wuLiuShunfengNoInfoService.saveShunfengNoInfo(shunfengNoInfo);
                    break;
                case WuliuExpressConstant.ACT_WULIUWANGDIAN:
                    WuliuExpressMqBO<WuLiuWuliuwangdianEntity> wuliuWangDian = JacksonJsonUtils.toClass(msg, new TypeReference<WuliuExpressMqBO<WuLiuWuliuwangdianEntity>>() {});
                    wuLiuWuliuwangdianService.saveWuliuWandDian(wuliuWangDian);
                    break;
                case WuliuExpressConstant.ACT_WULIUEXPRESSEXTEND:
                    WuliuExpressMqBO<WuLiuExpressExtendEntity> wuLiuExpressExtend = JacksonJsonUtils.toClass(msg, new TypeReference<WuliuExpressMqBO<WuLiuExpressExtendEntity>>() {});
                    wuliuExpressExtendService.saveWuliuExpressExtend(wuLiuExpressExtend);
                    break;
                case WuliuExpressConstant.ACT_WULIULOG:
                    WuliuExpressMqBO<WuLiuLogEntity> wuliuLog = JacksonJsonUtils.toClass(msg, new TypeReference<WuliuExpressMqBO<WuLiuLogEntity>>() {});
                    if (Objects.nonNull(wuliuLog) && Objects.nonNull(wuliuLog.getData())) {
                        wuLiuLogService.save(wuliuLog.getData());
                    }
                    break;
                case WuliuExpressConstant.ACT_WULIUNOEX:
                    WuliuExpressMqBO<Wuliunoex> wuliunoex = JacksonJsonUtils.toClass(msg, new TypeReference<WuliuExpressMqBO<Wuliunoex>>() {});
                    if (Objects.nonNull(wuliunoex) && Objects.nonNull(wuliunoex.getData())) {
                        wuliunoexService.save(wuliunoex.getData());
                    }
                    break;
                case WuliuExpressConstant.ACT_ZTOBILLINFO:
                    WuliuExpressMqBO<ZtoBillInfo> ztoBillInfo = JacksonJsonUtils.toClass(msg, new TypeReference<WuliuExpressMqBO<ZtoBillInfo>>() {});
                    if (Objects.nonNull(ztoBillInfo) && Objects.nonNull(ztoBillInfo.getData())) {
                        ztoBillInfoService.save(ztoBillInfo.getData());
                    }
                    break;
                case WuliuExpressConstant.ACT_CALCULATE_DISTRIBUTION_COST:
                    WuliuExpressMqBO<WuliuDistributtionCostBO> wuliuDistributtionCost = JacksonJsonUtils.toClass(msg, new TypeReference<WuliuExpressMqBO<WuliuDistributtionCostBO>>() {});
                    if (Objects.nonNull(wuliuDistributtionCost) && Objects.nonNull(wuliuDistributtionCost.getData())) {
                        wuLiuBusService.calculateDistributionCost(wuliuDistributtionCost.getData());
                    }
                    break;
                case WuliuExpressConstant.ACT_SAVE_LOGISTICS_EXPRESS_ORDER:
                    WuliuExpressMqBO<ExpressOrderAddBO> expressOrderAdd = JacksonJsonUtils.toClass(msg, new TypeReference<WuliuExpressMqBO<ExpressOrderAddBO>>() {});
                    if (Objects.nonNull(expressOrderAdd) && Objects.nonNull(expressOrderAdd.getData())) {
                        logisticsExpressService.saveExpressOrder(expressOrderAdd.getData());
                    }
                    break;
                case WuliuExpressConstant.ACT_WULIU_CHANGE_STATS:
                    WuliuExpressMqBO<ChangWuliuStatsBO> changWuliuStats = JacksonJsonUtils.toClass(msg, new TypeReference<WuliuExpressMqBO<ChangWuliuStatsBO>>() {});
                    if (Objects.nonNull(changWuliuStats) && Objects.nonNull(changWuliuStats.getData())) {
                        wuLiuBusService.changeWuliuStats(changWuliuStats.getData());
                    }
                    break;
                case WuliuExpressConstant.ACT_RECEIVE_WULIU:
                    WuliuExpressMqBO<WuliuTransferStationBO> wuliuTransferStation = JacksonJsonUtils.toClass(msg, new TypeReference<WuliuExpressMqBO<WuliuTransferStationBO>>() {});
                    if (Objects.nonNull(wuliuTransferStation) && Objects.nonNull(wuliuTransferStation.getData())) {
                        SpringUtil.getBean(ILogisticsOrderService.class).wuliuAssetSigned(wuliuTransferStation.getData());
                    }
                    break;
                case WuliuExpressConstant.ACT_WULIUEXPRESS_SIGNED:
                    WuliuExpressMqBO<WuliuExpressSignedBO> wuliuExpressSigned = JacksonJsonUtils.toClass(msg, new TypeReference<WuliuExpressMqBO<WuliuExpressSignedBO>>() {});
                    if (Objects.nonNull(wuliuExpressSigned) && Objects.nonNull(wuliuExpressSigned.getData())) {
                        wuLiuBusService.wuliuExpressSigned(wuliuExpressSigned.getData());
                    }
                    break;
                case WuliuExpressConstant.ACT_APPLE_PURCHASE_WULIU:
                    WuliuExpressMqBO<ApplePurchaseWuliuBO> applePurchaseWuliu = JacksonJsonUtils.toClass(msg, new TypeReference<WuliuExpressMqBO<ApplePurchaseWuliuBO>>() {});
                    if (Objects.nonNull(applePurchaseWuliu) && Objects.nonNull(applePurchaseWuliu.getData())) {
                        wuLiuBusService.applePurchaseWuliu(applePurchaseWuliu.getData());
                    }
                    break;
                case WuliuExpressConstant.ACT_DIAOBO_PAOTUI_MESSAGE_PUSH:
                    WuliuExpressMqBO<DiaoboPaotuiWuliuBO> diaoboPaotuiWuliu = JacksonJsonUtils.toClass(msg, new TypeReference<WuliuExpressMqBO<DiaoboPaotuiWuliuBO>>() {});
                    if (Objects.nonNull(diaoboPaotuiWuliu) && Objects.nonNull(diaoboPaotuiWuliu.getData())) {
                        wuLiuBusService.diaoboPaotuiWuliu(diaoboPaotuiWuliu.getData());
                    }
                    break;
                default:
                    log.warn("从rabbitmq获取快递信息msg={}",  message);
            }
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (CustomizeException ce) {
            try {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            } catch (IOException e) {
                log.error("手动ack MQ异常", e);
            }
            log.warn("处理快递信息异常", ce);
        } catch (Exception e) {
            log.error("从rabbitmq获取快递信息异常", e);
            WuliuUtil.manulRetry(message, channel, NumberConstant.TWO);
        }
    }

}
