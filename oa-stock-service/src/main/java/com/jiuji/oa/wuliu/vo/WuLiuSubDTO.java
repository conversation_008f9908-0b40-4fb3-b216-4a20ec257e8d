package com.jiuji.oa.wuliu.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jiuji.tc.utils.constants.TimeFormatConstant;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * @description: WuLiuSubDTO
 * </p>
 * @author: David
 * @create: -10-12 19:30
 */
@Data
public class WuLiuSubDTO {

    private Integer subId;
    /**
     * 订单类型
     */
    private Integer subtype;
    private String subTo;
    private String subAdds;
    private String subMobile;
    private Integer cityid;
    private Integer delivery;
    private Integer areaid;
    private Integer zitidianId;
    private Integer saleType;
    private BigDecimal yingfum;
    /**
     * 已付金额
     */
    private BigDecimal yifuM;
    /**
     * 规定送达时间
     */
    private LocalDateTime expectTime;
    /**
     * 订单状态
     */
    private Integer subCheck;
    /**
     * 用户申请取消
     */
    private Integer cancelApplication;
    // 增加两个 整形字段 userDate,userTime
    @JSONField(format = TimeFormatConstant.YYYY_MM_DD)
    @JsonFormat(pattern = TimeFormatConstant.YYYY_MM_DD, timezone = "GMT+8")
    private LocalDate userDate;
    private Integer userTime;
    // 增加 position 字符串字段
    private String position;
}
