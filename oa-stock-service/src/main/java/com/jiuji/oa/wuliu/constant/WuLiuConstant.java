package com.jiuji.oa.wuliu.constant;

import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 物流单常量类
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-09-29
 */
public final class WuLiuConstant {

    /**
     * 物流价格 Redis key
     */
    public static final String WU_LIU_PRICE_REDIS_KEY = "stock:wuliu:price_list_all";
    /**
     * 物流价格 Redis 缓存过期时间
     */
    public static final Duration WU_LIU_PRICE_REDIS_KEY_EXPIRE = Duration.of(12, ChronoUnit.HOURS);

    public static final String RANK_WLMT = "wlmt";

    /**
     * https://jiuji.yuque.com/docs/share/ae8bed26-062f-4390-983c-b48ef7f47a18?#%20%E3%80%8A%E3%80%90%E7%8B%AC%E7%AB%8B%E3%80%91%E3%80%90%E4%B9%9D%E6%9C%BA%E3%80%91%E3%80%90%E7%89%A9%E6%B5%81%E5%BC%80%E5%8F%91%E7%BB%84%E3%80%91%EF%BC%88PC%E5%8F%8A%E7%A7%BB%E5%8A%A8%E7%AB%AF%EF%BC%89%E6%89%8B%E5%8A%A8%E6%B7%BB%E5%8A%A0%E7%89%A9%E6%B5%81%E5%8D%95%E7%94%9F%E6%88%90%E5%BF%AB%E9%80%92%E5%8D%95%E5%A2%9E%E5%8A%A0%E6%9D%83%E9%99%90%E3%80%8B
     * 手动添加物流单快递方式增加权限管控
     */
    public static final String RANK_KDSC = "kdsc";

    /**
     * 中通快递新 权限
     */
    public static final String RANK_ZTKD = "ztkd";

    public static final String ACTION_SUB = "sub";

    public static final String ACTION_RE_SUB = "ReSub";

    /**
     * 达达城市code Redis key
     */
    public static final String WU_LIU_DADA_CITYCODE = "stock:wuliu:cache_dada_citycode_";

    /**
     * 闪送配置 redis key
     */
    public static final String SHANSONG_STORE_CONFIG_KEY = "stock:wuliu:shansong_store_config_key";

    /**
     * 顺丰快递
     */
    public static final String SHUNFENG = "shunfeng";

    /**
     * 顺丰快递（九机特惠）
     */
    public static final String SHUNFENG_JIUJI = "shunfeng_jiuji";

    /**
     * 顺丰快递 Laas
     */
    public static final String SHUNFENG_LAAS = "shunfeng_laas";

    /**
     * 达达快递
     */
    public static final String DADA = "dada";

    /**
     * UU跑腿
     */
    public static final String UU_PAOTUI = "uupt";

    /**
     * UU跑腿
     */
    public static final String UU_PAOTUI_NAME = "uu跑腿";

    /**
     * UU跑腿EXPRESS_TYPE
     */
    public static final Integer UU_PAOTUI_EXPRESS_TYPE = 10;

    /**
     * 中通快递
     */
    public static final String ZHONGTONG = "zhongtong";

    public static final String ZHONGTONG_NEW = "zhongtong-new";

    public static final Integer ZHONGTONG_NEW_EXPRESS_TYPE = 11;

    /**
     * EMS 快递
     */
    public static final String EMS = "ems";

    /**
     * 京东物流
     */
    public static final String JINGDONG = "jingdong";

    /**
     * 京东物流（九机特惠）
     */
    public static final String JINGDONG_JIUJI = "jingdong-jiuji";

    /**
     * 美团快递
     */
    public static final String MEITUAN = "meituan";

    /**
     * 美团快递（九机特惠）
     */
    public static final String MEITUAN_JIUJI = "meituan_jiuji";

    /**
     * 美团光速达
     */
    public static final String MEITUAN_FASTEST = "meituanFastest";

    /**
     * 闪送
     */
    public static final String SHANSONG = "shansong";

    /**
     * 美团短名
     */
    public static final String MEITUAN_SHORT_NAME = "美团";

    /**
     * EMS 短名
     */
    public static final String EMS_SHORT_NAME = "EMS";

    /**
     * JD 短名
     */
    public static final String JD_SHORT_NAME = "JD";

    /**
     * 达达短名
     */
    public static final String DADA_SHORT_NAME = "达达";

    /**
     * 顺丰快递（九机特惠）短名
     */
    public static final String SHUNFENG_JIUJI_SHORT_NAME = "顺丰快递";

    /**
     * 贵州
     */
    public static final String GUIZHOU = "贵州";

    /**
     * 顺丰顾客编号
     */
    public static final String SHUNFENG_CUSTOMER_CODE = "8711353857";
    /**
     * 顺丰校验码
     */
    public static final String SHUNFENG_CUSTOMER_VERIFY_CODE ="vrBsLe9JDNBGNUJwteyHkvjftTC3YVgT";
    /**
     * 顺丰月结卡号 customer id
     */
    public static final String SHUNFENG_CUSTOMER_ID ="8711353857";

    /**
     * 门店地区工程部 271
     */
    public static final int AREA_GCB = 271;

    /**
     * 中通 是否启用新接口 zhongtongApiServices.IsNewPort 新接口，暂时不启用
     */
    public static final boolean ZHONGTONG_IS_NEW_PORT = false;

    /**
     * 省
     */
    public static final String PROVINCE = "省";

    /**
     * img url
     */
    public static final String IMG_URL = "https://img.9xun.com/newstatic/%s";

    /**
     * dot
     */
    public static final char C_DOT = ',';

    /**
     * slash
     */
    public static final char C_SLASH = '/';

    /**
     * 208
     */
    public static final String TWO_HUNDRED_AND_EIGHT_STR = "208";

    /**
     * 1
     */
    public static final String ONE_STR = "1";

    /**
     * 接收异常信息的工号
     */
    public static final int ZLX_CH999_ID = 13682;
    public static final int ZYK_CH999_ID = 13682;

    /**
     * 大仓
     */
    public static final int AREA_DC = 16;

    /**
     * 昆明售后
     */
    public static final int AREA_H1 = 13;

    /**
     * Y 大仓
     */
    public static final int AREA_YDC = 476;

    /**
     * dc1
     */
    public static final int AREA_DC1 = 113;

    /**
     * dc2
     */
    public static final int AREA_DC2 = 142;
    public static final int DC2 = 203;
    /**
     * 门店深圳 sz
     */
    public static final int AREA_SZ = 14;

    /**
     * h2
     */
    public static final Integer AREA_H2 = 246;

    /**
     * CH_d1
     */
    public static final Integer AREA_CH_D1 = 826;
    /**
     * h3
     */
    public static final Integer AREA_H3 = 360;
    /**
     * FX_d1
     */
    public static final Integer AREA_FX_D1 = 738;

    /**
     * 九机网
     */
    public static final String JIUJI_NAME = "九机网";

    /**
     * 丫丫网
     */
    public static final String YAYA_NAME = "丫丫网";

    /**
     * 0
     */
    public static final String ZERO_STR = "0";

    /**
     * /login
     */
    public static final String LOGIN_TEXT = "/login";

    /**
     * EMS 异常信息
     */
    public static final String EMS_EXCEPTION_MESSAGE = "邮政获取运单接口处理异常: %s";

    /**
     * 订单派送的物流类型
     */
    public static final List<Integer> SUB_WULIU_TYPE_ARRY = Collections.unmodifiableList(Arrays.asList(4, 6));

    /**
     * 良品订单派送的物流类型
     */
    public static final List<Integer> LP_SUB_WULIU_TYPE_ARRY = Collections.singletonList(9);

    /**
     * 不可派收
     */
    public static final String NOT_AVAILABLE_FOR_DELIVERY = "不可收派";

    /**
     * 顺丰九机特惠月结卡号
     */
    public static final String JIUJI_MONTH_CUST_ID = "8712269386";

    /**
     * 顺丰九机特惠月结卡号
     */
    public static final String CREATE_WULIU_MSG = "创建物流单";

    /**
     * 销售单、良品单提前配送提示功能
     */
    public static final String WULIU_ADVANCE_DELIVERY_MSG = "%s:%s规定送达时间距离现在还有%s小时%s分钟，是否继续配送?";

    /**
     * 跑腿快递类型
     */
    public static final List<String> WULIU_DELIVERY_COM_ARRY = Arrays.asList("meituan","uupt","dada","sftc");

    /**
     * 快递dc默认寄件号码
     */
    public static final String DC_S_MOBLIE = "17088630339";

    /**
     * 新增两个 常量 九机快送 跑腿 值为拼音
     */
    public static final String JIUJI_KUAI_SONG = "jiujikuaisong";
    /**
     * 新增 跑腿 快递方式
     */
    public static final String PAO_TUI = "paotui";

    /**
     * 三方订单类型
     * 19 美团闪购
     * 32 抖音小时达
     * 38 淘宝小时达
     */
    public static final List<Integer> HOURS_ORDER_SUB_TYPE = Arrays.asList(19, 32, 38);

    /**
     * 未投妥原因
     */
    public static final String NOT_DELIVERED_REASON = "无法联系到客户";

    public static final String NOT_DELIVERED_MSG1 = "订单由【已出库】改为【已确认】";
    public static final String NOT_DELIVERED_MSG2 = "快递公司由【{}】更改为【】，快递单号【{}】更改为【】";
    public static final String NOT_DELIVERED_MSG3 = "预计送达时间由【{}】更改为【{}】";
    public static final String NOT_DELIVERED_SUB_SMS_MSG = "尊敬的九机网客户，您好，您的送货上门订单（订单号：{}），因骑手配送过程中，无法与您取得联系，商品已返回商家，若您需要二次发起配送，可以客服热线400-008-3939";
    public static final String NOT_DELIVERED_SUB_SMS_MSG1 = "尊敬的九机网客户，您好，您的送货上门订单（订单号：{}），因骑手配送过程中，无法与您取得联系，商品已返回商家，若您需要二次发起配送，可以联系销售（名字：{}，工号：{}，电话：{}）或客服热线400-008-3939";


    private WuLiuConstant() {
    }

}
