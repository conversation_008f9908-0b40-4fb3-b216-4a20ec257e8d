package com.jiuji.oa.wuliu.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单商品表,责任小组：销售 实体类
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-15
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("basket")
@ApiModel(value = "WuLiuBasket3Entity 实体类", description = "订单商品表,责任小组：销售 实体类")
public class WuLiuBasket3Entity extends WuLiuBasketEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("sub_date")
    private LocalDateTime subDate;

    @TableField("type")
    private Integer type;

    @TableField("yifuM")
    private BigDecimal yifuM;

    @TableField("yingfuM")
    private BigDecimal yingfuM;

}