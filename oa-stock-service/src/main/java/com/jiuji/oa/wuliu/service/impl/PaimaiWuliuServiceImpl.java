package com.jiuji.oa.wuliu.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.fasterxml.jackson.core.type.TypeReference;
import com.jiuji.cloud.logistics.vo.base.CreateOrderNoBase;
import com.jiuji.cloud.logistics.vo.request.CreateOrderBatchReq;
import com.jiuji.cloud.logistics.vo.request.CreateOrderReq;
import com.jiuji.oa.nc.common.config.rabbitmq.RabbitMqConfig;
import com.jiuji.oa.nc.common.exception.CustomizeException;
import com.jiuji.oa.nc.dict.service.ISysConfigService;
import com.jiuji.oa.nc.user.po.Areainfo;
import com.jiuji.oa.nc.user.service.IAreaInfoService;
import com.jiuji.oa.stock.common.util.JacksonJsonUtils;
import com.jiuji.oa.stock.common.util.SysUtils;
import com.jiuji.oa.stock.logisticscenter.enums.LogisticsExpressTypeEnum;
import com.jiuji.oa.stock.logisticscenter.serive.ILogisticsExpressService;
import com.jiuji.oa.wuliu.bo.WuliuExpressMqBO;
import com.jiuji.oa.wuliu.constant.WuLiuConstant;
import com.jiuji.oa.wuliu.constant.WuliuExpressConstant;
import com.jiuji.oa.wuliu.entity.*;
import com.jiuji.oa.wuliu.enums.JiuJiSfExpressTypeEnum;
import com.jiuji.oa.wuliu.enums.JiujiJdxpressTypeEnum;
import com.jiuji.oa.wuliu.enums.WuLiuCateEnum;
import com.jiuji.oa.wuliu.enums.WuLiuTypeEnum;
import com.jiuji.oa.wuliu.mapstruct.PaiMaiWuliuMapStruct;
import com.jiuji.oa.wuliu.service.*;
import com.jiuji.oa.wuliu.utils.WuliuAddressUtil;
import com.jiuji.oa.wuliu.vo.*;
import com.jiuji.oa.wuliu.vo.paimai.req.AddPaimaiWuliuReqVO;
import com.jiuji.oa.wuliu.vo.paimai.res.AddPaimaiWuliuResVO;
import com.jiuji.tc.common.vo.R;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.AmqpException;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/7/8 11:29
 */
@Service
@Slf4j
@DS("oanewWrite")
public class PaimaiWuliuServiceImpl implements IPaimaiWuliuService {
    private static final List<Integer> PAIMAI_EXPRESS_TYPE_LIST = Arrays.asList(2,3,8,12);
    @Resource
    private IAreaInfoService areaInfoService;
    @Resource
    private IWuLiuService wuLiuService;
    @Resource
    private PaiMaiWuliuMapStruct paiMaiWuliuMapStruct;
    @Resource
    private ILogisticsExpressService logisticsExpressService;
    @Resource
    private IWuLiuWuliuwangdianService wuLiuWuliuwangdianService;
    @Resource
    private ISysConfigService sysConfigService;
    @Resource
    private RabbitTemplate rabbitTemplate;

    /**
     * 拍靓机发送快递
     *
     * @param req
     * @return
     */
    @Override
    @DS("oanewWrite")
    @Transactional(rollbackFor = Exception.class)
    public AddPaimaiWuliuResVO addPaimaiWuliu(AddPaimaiWuliuReqVO req) {
        log.info("拍靓机生成物流单，param={}", req);
        Long xtenantId = 0L;
        if (!SysUtils.isJiuJiProd()) {
            xtenantId = SysUtils.getXtenantId() == null || SysUtils.getXtenantId() == 0L ? sysConfigService.getXtenantId() : SysUtils.getXtenantId();
        }
        req.setXtenantId(xtenantId);

        if (!PAIMAI_EXPRESS_TYPE_LIST.contains(req.getExpressType())) {
            //暂时只有顺丰/京东
            throw new CustomizeException("无效的运输方式");
        }
        String senderAdderss = req.getSenderAddress();
        CityIdListDTO jcity = wuLiuService.getAreaIdByCityId(req.getSenderCityId(), 1);
        if (Boolean.FALSE.equals(req.getNewJadress())) {
            Areainfo senderArea = areaInfoService.getAreaInfoByAreaId2(req.getSenderAreaId());
            if (senderArea == null) {
                throw new CustomizeException("无效的门店");
            }
            senderAdderss = senderArea.getCompanyAddress();
            jcity = wuLiuService.getAreaIdByCityId(senderArea.getCityid(), 1);
        } else {
            if (Objects.isNull(req.getSenderCityId()) || req.getSenderCityId() <= 0) {
                throw new CustomizeException("请输入寄件cityId");
            }
            if (StringUtils.isBlank(req.getSenderAddress())) {
                throw new CustomizeException("无效的寄件地址");
            }
            jcity = wuLiuService.getAreaIdByCityId(req.getSenderCityId(), 1);
            senderAdderss = req.getSenderAddress();
        }
        CityIdListDTO dcity = wuLiuService.getAreaIdByCityId(req.getReceiverCityId(), 1);
        req.setRemark(StringUtils.isBlank(req.getRemark()) ? "请勿摔打,保持整洁。" : req.getRemark());

        String wayBillNo = "";
        if (LogisticsExpressTypeEnum.JING_DONG_9JI.getExpressType().equals(req.getExpressType())) {
            if (!JiujiJdxpressTypeEnum.getDropMenuExpressType().contains(req.getDropMenuExpressType())) {
                //京东
                throw new CustomizeException("无效的业务类型");
            }
        } else {
            if (!JiuJiSfExpressTypeEnum.getDropMenuExpressType().contains(req.getDropMenuExpressType())) {
                //顺丰
                throw new CustomizeException("无效的业务类型");
            }
        }
        WuLiuEntity wuLiuEntity = new WuLiuEntity()
                .setWuType(WuLiuTypeEnum.SECOND_HAND.getCode())
                .setSAddress(senderAdderss)
                .setSAreaId(req.getSenderAreaId())
                .setSCityId(req.getSenderCityId())
                .setSName(req.getSenderName())
                .setSMobile(req.getSenderMobile())
                .setRAddress(req.getReceiverAddress())
                .setRCityId(dcity.getDid())
                .setRName(req.getReceiverName())
                .setRMobile(req.getReceiverMobile())
                .setAreaId(req.getSenderAreaId())
                .setStats(1)
                .setWeight(req.getWeight())
                .setRAreaId(0)
                .setDanHaoBind(0)
                .setComment(req.getComment())
                .setShouJianRen(StringUtils.isBlank(req.getInuser()) ? "系统" : req.getInuser())
                .setDTime(LocalDateTime.now())
                .setWCateId(WuLiuCateEnum.PAILIANGJI_PAISONG.getCode())
                .setInPrice(BigDecimal.ZERO);
        boolean save = wuLiuService.saveOrUpdate(wuLiuEntity);
        if (!save) {
            throw new CustomizeException("创建物流单失败，请稍后重试");
        }
        Integer wuliuId = wuLiuEntity.getId();

        String senderAddress = WuliuAddressUtil.getAddress(req.getSenderAddress(), jcity);
        String receiverAddress = WuliuAddressUtil.getAddress(req.getReceiverAddress(), dcity);
        req.setSenderAddress(senderAddress);
        req.setReceiverAddress(receiverAddress);

        String com = LogisticsExpressTypeEnum.SHUN_FENG_LAAS.getCode();
        //京东九机特惠
        if (Objects.equals(LogisticsExpressTypeEnum.JING_DONG_9JI_NEW.getExpressType(), req.getExpressType()) || Objects.equals(LogisticsExpressTypeEnum.JING_DONG_WULIU.getExpressType(), req.getExpressType())) {
            if (Objects.equals(LogisticsExpressTypeEnum.JING_DONG_9JI_NEW.getExpressType(), req.getExpressType())) {
                com = SysUtils.isJiuJiProd() || SysUtils.isDev() ? LogisticsExpressTypeEnum.JING_DONG_9JI.getCode() : LogisticsExpressTypeEnum.JING_DONG_9JI_NEW.getCode();
            } else {
                com = LogisticsExpressTypeEnum.JING_DONG_WULIU.getCode();
            }
            CreateOrderReq createOrderReq = paiMaiWuliuMapStruct.toCreateOrderReq(req, jcity, dcity);
            createOrderReq.setXTenantId(req.getXtenantId());
            createOrderReq.setExpressItemQty(req.getParcelQuantity());
            createOrderReq.setGrossWeight(req.getWeight());
            createOrderReq.setGrossVolume(req.getVloumn());
            createOrderReq.setOrderNo(wuliuId.toString());
            String result = logisticsExpressService.createOrder(createOrderReq);
            R<JdCreateOrderResultDTO> jdResult = JacksonJsonUtils.toClass(result, new TypeReference<R<JdCreateOrderResultDTO>>() {});
            if (Objects.isNull(jdResult)) {
                throw new CustomizeException("京东快递订单创建失败，请稍后再试");
            }
            if (Objects.equals(jdResult.getCode(), 0) && Objects.nonNull(jdResult.getData())) {
                wayBillNo = jdResult.getData().getExpressNumber();
                DataResultDTO dataResult = jdResult.getData().getDataResult();
                WuliuExpressMqBO<JingdongPrintInfo> wuliuExpressMq = new WuliuExpressMqBO<JingdongPrintInfo>().setAct(WuliuExpressConstant.ACT_JINGDONGPRINTINFO)
                        .setData(new JingdongPrintInfo().setOrderId(wuliuId.toString())
                                .setAreaId(Optional.ofNullable(createOrderReq.getSendShopId()).orElse(0))
                                .setDeliveryId(wayBillNo)
                                .setPromiseTimeType(dataResult.getPromiseTimeType())
                                .setPreSortResult(JSONUtil.toJsonStr(dataResult.getPreSortResult()))
                                .setTransType(dataResult.getTransType())
                                .setNeedRetry(dataResult.getNeedRetry())
                                .setExpressOperationMode(dataResult.getExpressOperationMode()));
                sendMqMessage(wuliuExpressMq);
            } else {
                throw new CustomizeException(jdResult.getUserMsg());
            }
        } else if (Objects.equals(LogisticsExpressTypeEnum.SHUN_FENG.getExpressType(), req.getExpressType())) {
            com = SysUtils.isJiuJiProd() || SysUtils.isDev() ? LogisticsExpressTypeEnum.SHUN_FENG_LAAS.getCode() : LogisticsExpressTypeEnum.SHUN_FENG.getCode();
            CreateOrderBatchReq createOrderBatchReq = new CreateOrderBatchReq();
            CreateOrderNoBase orderNoBase = paiMaiWuliuMapStruct.toCreateOrderNoBase(req);
            if (CollectionUtils.isNotEmpty(req.getList())) {
                orderNoBase.setProductName(req.getList().get(0).getCargoName());
            } else {
                orderNoBase.setProductName("电子产品");
            }
            orderNoBase.setCustomerOrderNo(wuliuId.toString());
            orderNoBase.setPackageNumber(req.getParcelQuantity());
            orderNoBase.setBspType(1);
            createOrderBatchReq.setList(Collections.singletonList(orderNoBase));
            createOrderBatchReq.setXTenantId(req.getXtenantId());
            createOrderBatchReq.setExpressType(req.getExpressType());
            createOrderBatchReq.setSendShopId(req.getSenderAreaId());
            createOrderBatchReq.setSendShopId(req.getSenderAreaId());
            String shunfengResulltStr = logisticsExpressService.createOrderShunfeng9ji(createOrderBatchReq);
            R<Object> objResullt = JacksonJsonUtils.toClass(shunfengResulltStr, new TypeReference<R<Object>>() {});
            if (Objects.isNull(objResullt)) {
                throw new CustomizeException("顺丰快递订单创建失败，请稍后再试");
            }
            if (!Objects.equals(0, objResullt.getCode())) {
                throw new CustomizeException(objResullt.getUserMsg());
            }
            R<List<SfCreateOrderResultDTO>> shunfengResullt = JacksonJsonUtils.toClass(shunfengResulltStr, new TypeReference<R<List<SfCreateOrderResultDTO>>>() {});
            if (Objects.isNull(shunfengResullt)) {
                throw new CustomizeException("顺丰快递订单创建失败，请稍后再试");
            }
            if (Objects.equals(shunfengResullt.getCode(), 0) && CollectionUtils.isNotEmpty(shunfengResullt.getData())) {
                SfCreateOrderResultDTO sfCreateOrderResult = shunfengResullt.getData().get(0);
                WuliuExpressMqBO<WuLiuShunfengNoInfoEntity> wuliuExpressMq = new WuliuExpressMqBO<WuLiuShunfengNoInfoEntity>().setAct(WuliuExpressConstant.ACT_SHUNFENGNOINFO)
                        .setData(new WuLiuShunfengNoInfoEntity()
                                .setMailNo(sfCreateOrderResult.getMainMailNo())
                                .setWuLiuId(wuliuId.toString())
                                .setDestRouteLabel(sfCreateOrderResult.getDestRouteLabel())
                                .setTwoDimensionCode(sfCreateOrderResult.getTwoDimensionCode())
                                .setSareaid(req.getSenderAreaId())
                                .setCustId(WuLiuConstant.JIUJI_MONTH_CUST_ID)
                                .setAddDate(LocalDateTime.now())
                                .setJMobile(req.getSenderMobile())
                                .setDMobile(req.getReceiverMobile()));
                sendMqMessage(wuliuExpressMq);

                WuliuExpressMqBO<WuLiuWuliuwangdianEntity> wangdianMq = new WuliuExpressMqBO<WuLiuWuliuwangdianEntity>().setAct(WuliuExpressConstant.ACT_WULIUWANGDIAN)
                        .setData(new WuLiuWuliuwangdianEntity()
                                .setWuliuid(wuliuId)
                                .setOrgcode(sfCreateOrderResult.getOriginCode())
                                .setDestcode(sfCreateOrderResult.getDestCode())
                                .setExepresstype(wuLiuWuliuwangdianService.getExpressType(req.getDropMenuExpressType()+""))
                                .setPayType("寄付月结")
                                .setYuejiekahao(WuLiuConstant.JIUJI_MONTH_CUST_ID));
                sendMqMessage(wangdianMq);

                wayBillNo = sfCreateOrderResult.getMainMailNo();
            } else {
                throw new CustomizeException(shunfengResullt.getUserMsg());
            }
        } else if (Objects.equals(LogisticsExpressTypeEnum.SHUN_FENG_LAAS.getExpressType(), req.getExpressType())) {
            com = LogisticsExpressTypeEnum.SHUN_FENG_LAAS.getCode();
            CreateOrderReq createOrderReq = paiMaiWuliuMapStruct.toCreateOrderReq(req, jcity, dcity);
            createOrderReq.setMonthlyCard(req.getMonthlyCard());
            createOrderReq.setXTenantId(req.getXtenantId());
            createOrderReq.setExpressItemQty(req.getParcelQuantity());
            createOrderReq.setOrderNo(wuliuId.toString());
            String result = logisticsExpressService.createOrder(createOrderReq);
            R<LogisticsBaseResVO> lassResult = JacksonJsonUtils.toClass(result, new TypeReference<R<LogisticsBaseResVO>>() {});
            if (Objects.isNull(lassResult)) {
                throw new CustomizeException("顺丰快递订单创建失败，请稍后再试");
            }
            if (Objects.equals(lassResult.getCode(), 0) && Objects.nonNull(lassResult.getData())) {
                LogisticsBaseResVO data = lassResult.getData();
                LogisticsBaseResVO.Extension extension = Optional.ofNullable(data.getExtension()).orElseGet(LogisticsBaseResVO.Extension::new);
                String destRouteLabel = extension.getDestRouteLabel();
                String twoDimensionCode = extension.getTwoDimensionCode();

                WuliuExpressMqBO<WuLiuShunfengNoInfoEntity> wuliuExpressMq = new WuliuExpressMqBO<WuLiuShunfengNoInfoEntity>().setAct(WuliuExpressConstant.ACT_SHUNFENGNOINFO)
                        .setData(new WuLiuShunfengNoInfoEntity()
                                .setMailNo(data.getWayBill())
                                .setWuLiuId(wuliuId.toString())
                                .setDestRouteLabel(destRouteLabel)
                                .setTwoDimensionCode(twoDimensionCode)
                                .setSareaid(req.getSenderAreaId())
                                .setCustId(req.getMonthlyCard())
                                .setAddDate(LocalDateTime.now())
                                .setJMobile(req.getSenderMobile())
                                .setDMobile(req.getReceiverMobile()));
                sendMqMessage(wuliuExpressMq);

                WuliuExpressMqBO<WuLiuWuliuwangdianEntity> wangdianMq = new WuliuExpressMqBO<WuLiuWuliuwangdianEntity>().setAct(WuliuExpressConstant.ACT_WULIUWANGDIAN)
                        .setData(new WuLiuWuliuwangdianEntity()
                                .setWuliuid(wuliuId)
                                .setOrgcode(extension.getOriginCode())
                                .setDestcode(extension.getDestCode())
                                .setExepresstype(wuLiuWuliuwangdianService.getExpressType(req.getDropMenuExpressType()+""))
                                .setPayType("寄付月结")
                                .setYuejiekahao(req.getMonthlyCard()));
                sendMqMessage(wangdianMq);
                wayBillNo = data.getWayBill();
            } else {
                throw new CustomizeException(lassResult.getUserMsg());
            }
        }

        wuLiuService.lambdaUpdate().set(WuLiuEntity::getCom, com)
                .set(WuLiuEntity::getNu, wayBillNo)
                .eq(WuLiuEntity::getId, wuliuId)
                .isNull(WuLiuEntity::getCom)
                .isNull(WuLiuEntity::getNu).update();
        saveWuliuLog(wuliuId, req.getInuser(), "创建物流单成功");
        saveWuliuLog(wuliuId, req.getInuser(), LogisticsExpressTypeEnum.getExpressMessage(com) + "运单号生成成功:" + wayBillNo);
        AddPaimaiWuliuResVO res = new AddPaimaiWuliuResVO();
        res.setWuliId(wuliuId.longValue());
        res.setWaybillNo(wayBillNo);
        return res;
    }

    /**
     * 发送消息
     * @param messageObj
     */
    private <T> void sendMqMessage(WuliuExpressMqBO<T> messageObj) {
        try {
            String jsonMessage = JacksonJsonUtils.toJson(messageObj);
            log.warn("发送 RabbitMQ 队列消息入参: {}", jsonMessage);
            rabbitTemplate.convertAndSend(RabbitMqConfig.QUEUE_WULIU_EXPRESS_SYNC, jsonMessage);
        } catch (AmqpException e) {
            log.error("发送 RabbitMQ 队列消息报错: {}, message: {}, e: {}", e.getMessage(), messageObj, e);
        }
    }

    /**
     * 发送消息
     */
    private void saveWuliuLog(Integer wuliuId, String inUser, String msg) {
        WuliuExpressMqBO<WuLiuLogEntity> wuliuLog = new WuliuExpressMqBO<WuLiuLogEntity>().setAct(WuliuExpressConstant.ACT_WULIULOG)
                .setData(new WuLiuLogEntity()
                        .setWuliuid(wuliuId)
                        .setInuser(StringUtils.isBlank(inUser) ? "系统" : inUser)
                        .setMsg(msg)
                        .setDtime(LocalDateTime.now()));
        sendMqMessage(wuliuLog);
    }
}
