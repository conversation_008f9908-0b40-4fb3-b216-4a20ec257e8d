package com.jiuji.oa.wuliu.vo;


import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * shunfengApiServices.SfOrderInfo
 * 顺丰，中通下单实体
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-11
 */
@ApiModel(description = "顺丰，中通下单实体")
@Data
@Accessors(chain = true)
public class SfOrderInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 寄件人信息-寄件公司
     */
    @JsonProperty("j_company")
    @JSONField(name = "j_company")
    private String jCompany;

    /**
     * 寄件人信息-寄件联系人
     */
    @JsonProperty("j_contact")
    @JSONField(name = "j_contact")
    private String jContact;

    /**
     * 寄件人信息-寄件人联系电话
     */
    @JsonProperty("j_tel")
    @JSONField(name = "j_tel")
    private String jTel;

    /**
     * 寄件人信息-寄件人手机
     */
    @JsonProperty("j_mobile")
    @JSONField(name = "j_mobile")
    private String jMobile;

    /**
     * 寄件人信息-寄件人所在省份
     */
    @JsonProperty("j_province")
    @JSONField(name = "j_province")
    private String jProvince;

    /**
     * 寄件人信息-寄件人所在市
     */
    @JsonProperty("j_city")
    @JSONField(name = "j_city")
    private String jCity;

    /**
     * 寄件人信息-寄件人所在区/县
     */
    @JsonProperty("j_county")
    @JSONField(name = "j_county")
    private String jCounty;

    /**
     * 寄件人信息-寄件人详细地址
     */
    @JsonProperty("j_address")
    @JSONField(name = "j_address")
    private String jAddress;

    /**
     * 寄件人信息-发货门店编号
     */
    @JsonProperty("SendAreaId")
    @JSONField(name = "SendAreaId")
    private Integer sendAreaId;

    /**
     * 收件人信息-收货门店编号
     */
    @JsonProperty("ReceiveAreaId")
    @JSONField(name = "ReceiveAreaId")
    private Integer receiveAreaId;

    /**
     * 收件人信息-收件人公司
     */
    @JsonProperty("d_company")
    @JSONField(name = "d_company")
    private String dCompany;

    /**
     * 收件人信息-收件联系人
     */
    @JsonProperty("d_contact")
    @JSONField(name = "d_contact")
    private String dContact;

    /**
     * 收件人信息-收件联系电话
     */
    @JsonProperty("d_tel")
    @JSONField(name = "d_tel")
    private String dTel;

    /**
     * 收件人信息-收件方手机号码
     */
    @JsonProperty("d_mobile")
    @JSONField(name = "d_mobile")
    private String dMobile;

    /**
     * 收件人信息-收件人所在省份
     */
    @JsonProperty("d_province")
    @JSONField(name = "d_province")
    private String dProvince;

    /**
     * 收件人信息-收件人所在市
     */
    @JsonProperty("d_city")
    @JSONField(name = "d_city")
    private String dCity;

    /**
     * 收件人信息-收件人所在区/县
     */
    @JsonProperty("d_county")
    @JSONField(name = "d_county")
    private String dCounty;

    /**
     * 收件人信息-收件人详细地址
     */
    @JsonProperty("d_address")
    @JSONField(name = "d_address")
    private String dAddress;

    /**
     * 订单信息-订单号(sub_id)
     */
    @JsonProperty("orderid")
    @JSONField(name = "orderid")
    private String orderId;

    /**
     * 订单信息-数量
     */
    @JsonProperty("BasketCount")
    @JSONField(name = "BasketCount")
    private Integer basketCount;

    /**
     * 订单信息-业务类型
     */
    @JsonProperty("express_type")
    @JSONField(name = "express_type")
    private String expressType;

    /**
     * 订单信息-支付类型
     */
    @JsonProperty("pay_method")
    @JSONField(name = "pay_method")
    private Integer payMethod;

    /**
     * 订单信息-包裹数
     */
    @JsonProperty("parcel_quantity")
    @JSONField(name = "parcel_quantity")
    private Integer parcelQuantity;

    /**
     * 订单信息-月结卡号
     */
    @JsonProperty("custid")
    @JSONField(name = "custid")
    private String custId;

    /**
     * 订单信息-快件总重量
     */
    @JsonProperty("cargo_total_weight")
    @JSONField(name = "cargo_total_weight")
    private String cargoTotalWeight;

    /**
     * 订单信息-发货时间(取件时间)
     */
    @JsonProperty("sendstarttime")
    @JSONField(name = "sendstarttime")
    private String sendStartTime;

    /**
     * 订单信息-提交类型：1=回收单寄回
     */
    @JsonProperty("submitType")
    @JSONField(name = "submitType")
    private Integer submitType;

    /**
     * 订单信息-订单来源
     */
    @JsonProperty("order_source")
    @JSONField(name = "order_source")
    private String orderSource;

    /**
     * 订单信息-订单备注
     */
    @JsonProperty("remark")
    @JSONField(name = "remark")
    private String remark;

    /**
     * 订单信息-快递公司原寄地编码
     */
    @JsonProperty("orgcode")
    @JSONField(name = "orgcode")
    private String orgCode;

    /**
     * 订单信息-快递公司目的地编码
     */
    @JsonProperty("destcode")
    @JSONField(name = "destcode")
    private String destCode;

    /**
     * 订单信息-第三方支付方式 1:寄付月结 2:收件方付 3:第三方付
     */
    @JsonProperty("paytype")
    @JSONField(name = "paytype")
    private String payType;

    /**
     * 订单信息-第三方付 月结卡号
     */
    @JsonProperty("yuejiekahao")
    @JSONField(name = "yuejiekahao")
    private String yueJieKaHao;

    /**
     * 订单信息-快递单号
     */
    @JsonProperty("nu")
    @JSONField(name = "nu")
    private String nu;

    /**
     * 订单信息-在线下单的，docall  为1 ，巴枪就能收到提醒 (巴枪是顺风小哥收件时候拿着的那个东西)  一般HHTwaydil未1，这个参数也给1
     */
    @JsonProperty("docall")
    @JSONField(name = "docall")
    private Integer doCall;

    /**
     * 订单信息-强制设置顺风小哥收件通知
     */
    @JsonProperty("docallFlag")
    @JSONField(name = "docallFlag")
    private Boolean doCallFlag;

    /**
     * 订单信息-HHTwaydil是小哥上门收件了，能打单的功能
     */
    @JsonProperty("HHTwaydil")
    @JSONField(name = "HHTwaydil")
    private Integer hhtWayDil;

    /**
     * 订单信息-
     */
    @JsonProperty("destRouteLabel")
    @JSONField(name = "destRouteLabel")
    private String destRouteLabel;

    /**
     * 订单信息-
     */
    @JsonProperty("twoDimensionCode")
    @JSONField(name = "twoDimensionCode")
    private String twoDimensionCode;

    /**
     * 货品信息-货品信息 list
     */
    @JsonProperty("list")
    @JSONField(name = "list")
    private List<SfCargoVO> list;

    /**
     * 物流信息-物流单类型
     */
    @JsonProperty("WuliuType")
    @JSONField(name = "WuliuType")
    private String wuLiuType;

    /**
     * 物流信息-订单备注
     */
    @JsonProperty("Remark")
    @JSONField(name = "Remark")
    private String remark2;

    /**
     * 物流信息-物流单号
     */
    @JsonProperty("WuliuId")
    @JSONField(name = "WuliuId")
    private String wuLiuId;

    /**
     * 物流信息-用来表示网站获取接口类别（1：九信）
     */
    @JsonProperty("ckind")
    @JSONField(name = "ckind")
    private Integer ckind;

}
