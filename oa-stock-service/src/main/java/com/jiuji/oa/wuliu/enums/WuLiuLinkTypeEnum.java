package com.jiuji.oa.wuliu.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */

@Getter
@RequiredArgsConstructor
public enum WuLiuLinkTypeEnum implements CodeMessageEnumInterface {
    /**
     * 2:上门取件，3:送件上门） 关联sub_id(其它) 5 好像是维修  6 发票物流单  7 回收上门取件,11九机集市, 13固定资产调拨 ，20 配件调拨，21大件调拨
     */

    DOOR_TO_DOOR_PICK_UP(2, "上门取件"),
    DELIVERY_DOOR_TO_DOOR(3, "送件上门"),
    REPAIR_PICK_UP(5, "维修取件"),
    INVOICE_LOGISTICS(6, "发票物流单"),
    RECYCLING(7, "回收上门取件"),
    JIU_JI(11, "九机集市"),
    FIXED_ASSET_ALLOCATION(13, "固定资产调拨"),
    ACCESSORIES_ALLOCATION(20, "配件调拨"),
    MOBILE_ALLOCATION(21, "大件调拨");

    private final Integer code;
    private final String message;
}
