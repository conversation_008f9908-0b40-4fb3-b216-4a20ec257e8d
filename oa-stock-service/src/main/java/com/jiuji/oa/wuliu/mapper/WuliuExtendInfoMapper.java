package com.jiuji.oa.wuliu.mapper;

import com.jiuji.oa.wuliu.entity.WuLiuEntity;
import com.jiuji.oa.wuliu.entity.WuliuExtendInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Entity com.jiuji.oa.wuliu.entity.WuliuExtendInfo
 */
public interface WuliuExtendInfoMapper extends BaseMapper<WuliuExtendInfo> {

    WuliuExtendInfo queryWuliuExtendInfoByWuliuId(@Param("wuliuId") Integer wuliuId);

    List<WuLiuEntity> queryWuliuByNotDistributionCost(@Param("wuliuId") Integer wuliuId);

    Integer queryWuliuIdByDtime(@Param("days") Integer days);
}




