package com.jiuji.oa.wuliu.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * zto信息dto
 *
 * <AUTHOR>
 * @date 2021/11/04
 */
@Data
public class ZtoInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private String id;
    /**
     * 指令码
     */
    private String orderCode;
    /**
     * 网点代码
     */
    private String siteCode;
    /**
     * 网点名称
     */
    private String siteName;
    /**
     * 账号
     */
    private String billCode;
    /**
     * 大客户信息
     */
    private ZtoBigMarkInfo bigMarkInfoResDto;


    /**
     * zto大标记信息
     *
     * <AUTHOR>
     * @date 2021/11/04
     */
    @Data
    static class ZtoBigMarkInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        private String bagAddr;
        private String mark;
    }
}
