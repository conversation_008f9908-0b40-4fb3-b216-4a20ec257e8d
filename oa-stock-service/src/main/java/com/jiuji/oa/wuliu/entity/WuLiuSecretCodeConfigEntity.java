package com.jiuji.oa.wuliu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 实体类
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-18
 */
@Data
@Accessors(chain = true)
@TableName("secretCodeConfig")
@ApiModel(value = "WuLiuSecretCodeConfigEntity 实体类", description = " 实体类")
public class WuLiuSecretCodeConfigEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("version")
    private String version;

    @TableField("secret")
    private String secret;

    @TableField("code")
    private Integer code;

}