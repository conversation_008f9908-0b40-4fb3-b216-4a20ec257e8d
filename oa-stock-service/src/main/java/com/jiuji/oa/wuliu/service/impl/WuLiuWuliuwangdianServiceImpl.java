package com.jiuji.oa.wuliu.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.wuliu.bo.WuliuExpressMqBO;
import com.jiuji.oa.wuliu.entity.WuLiuWuliuwangdianEntity;
import com.jiuji.oa.wuliu.mapper.WuLiuWuliuwangdianMapper;
import com.jiuji.oa.wuliu.service.IWuLiuWuliuwangdianService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 快递网点记录表[责任小组:物流组] 服务实现类
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-29
 */
@Service
public class WuLiuWuliuwangdianServiceImpl extends ServiceImpl<WuLiuWuliuwangdianMapper, WuLiuWuliuwangdianEntity> implements IWuLiuWuliuwangdianService {

    @DS("ch999oanew")
    @Override
    public String getExpressType(Integer wuliuId) {
        List<WuLiuWuliuwangdianEntity> list = lambdaQuery().select(WuLiuWuliuwangdianEntity::getExepresstype).eq(WuLiuWuliuwangdianEntity::getWuliuid, wuliuId)
                .orderByDesc(WuLiuWuliuwangdianEntity::getId)
                .list();
        if(CollectionUtils.isNotEmpty(list)) {
            return Optional.ofNullable(list.get(0).getExepresstype()).orElse("");
        }
        return "";
    }

    /**
     * shunfengApiServices.GetExepressType
     *
     * @param type
     * @return string
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-04
     */
    @Override
    public String getExpressType(String type) {
        if (StringUtils.isBlank(type)) {
            return "";
        }
        if ("顺丰标快（全国套餐）".equals(type)) {
            return "2_9386_3";
        }
        if ("2_9386_3".equals(type)) {
            return "顺丰标快（全国套餐）";
        }
        if ("顺丰干配（大仓专用）".equals(type)) {
            return "2_2312_1";
        }
        if ("2_2312_1".equals(type)) {
            return "顺丰干配（大仓专用）";
        }
        Map<String, String> dict = new HashMap<>(13);

        dict.put("2_2312_1", "省内(0-3公斤)+省外全部");
        dict.put("1_2322_2", "省内(3-20公斤)");
        dict.put("1_2322_3", "省内(0-20公斤)");
        dict.put("154_9386_1", "顺丰重货（发全国20-100KG）");
        dict.put("155_9386_2", "顺丰零担（发全国100KG+）");
        dict.put("2_9386_3", "顺丰标快（发全国0-20KG）");
        dict.put("1", "顺丰标快(发省内3-20公斤)");
        dict.put("2", "顺丰特惠");
        dict.put("3", "电商特惠");
        dict.put("37", "云仓专配次日");
        dict.put("38", "云仓专配隔日");
        dict.put("154", "重货包裹");
        dict.put("155", "小票零担");
        dict.put("208", "特惠专配");

        if (dict.containsKey(type)) {
            return dict.get(type);
        } else if (dict.containsValue(type)) {
            return dict.entrySet().stream().filter(d -> Objects.equals(d.getValue(), type)).findFirst().orElseGet(() -> new AbstractMap.SimpleEntry<>("", "")).getKey();
        }
        return "";
    }

    /**
     * subWLService.saveWuliuWandDian
     * 保存物流网点信息(中通，顺风)
     *
     * @param wuliuid
     * @param orgcode
     * @param destcode
     * @param exepresstype
     * @param paytype
     * @param yuejiekahao
     * @return bool
     * @date 2021-10-21
     * <AUTHOR> [<EMAIL>]
     */
    @Override
    @DS("oanewWrite")
    @Transactional(rollbackFor = Exception.class)
    public boolean saveWuliuWandDian(Integer wuliuid, String orgcode, String destcode, String exepresstype, Integer
            paytype, String yuejiekahao) {
        yuejiekahao = Optional.ofNullable(yuejiekahao).orElse("");

        if (Objects.isNull(wuliuid) || Objects.equals(wuliuid, 0)) {
            return false;
        }
        String exepresstypeStr = "";
        if (StringUtils.isNotBlank(exepresstype)) {
            exepresstypeStr = getExpressType(exepresstype);
        }
        String payTypeStr = "";
        if (Objects.equals(paytype, 1)) {
            payTypeStr = "寄付月结";
        } else if (Objects.equals(paytype, 2)) {
            payTypeStr = "寄送方付";
        } else if (Objects.equals(paytype, 3)) {
            payTypeStr = "第三方付";
        }

        WuLiuWuliuwangdianEntity obj = this.getOne(
                Wrappers.<WuLiuWuliuwangdianEntity>lambdaQuery()
                        .select(WuLiuWuliuwangdianEntity::getId)
                        .eq(WuLiuWuliuwangdianEntity::getWuliuid, wuliuid), false);
        if (obj == null) {
            WuLiuWuliuwangdianEntity entity = new WuLiuWuliuwangdianEntity()
                    .setWuliuid(wuliuid)
                    .setOrgcode(orgcode)
                    .setDestcode(destcode)
                    .setExepresstype(exepresstypeStr)
                    .setPayType(payTypeStr)
                    .setYuejiekahao(yuejiekahao);
            return this.save(entity);

        } else {
            if (StringUtils.isNotBlank(exepresstypeStr) || StringUtils.isNotBlank(destcode)) {
                this.lambdaUpdate().set(WuLiuWuliuwangdianEntity::getOrgcode, orgcode)
                        .set(WuLiuWuliuwangdianEntity::getDestcode, destcode)
                        .set(WuLiuWuliuwangdianEntity::getExepresstype, exepresstypeStr)
                        .set(WuLiuWuliuwangdianEntity::getYuejiekahao, yuejiekahao)
                        .eq(WuLiuWuliuwangdianEntity::getWuliuid, wuliuid).update();
            }
            return false;
        }
    }

    /**
     * saveWuliuWandDian
     *
     * @param wuliuWangDian
     */
    @Override
    @DS("oanewWrite")
    public void saveWuliuWandDian(WuliuExpressMqBO<WuLiuWuliuwangdianEntity> wuliuWangDian) {
        if (Objects.nonNull(wuliuWangDian) && Objects.nonNull(wuliuWangDian.getData())) {
            WuLiuWuliuwangdianEntity obj = this.getOne(
                    Wrappers.<WuLiuWuliuwangdianEntity>lambdaQuery()
                            .select(WuLiuWuliuwangdianEntity::getId)
                            .eq(WuLiuWuliuwangdianEntity::getWuliuid, wuliuWangDian.getData().getWuliuid()), false);
            if (obj == null) {
                this.save(wuliuWangDian.getData());
            } else {
                this.lambdaUpdate().set(WuLiuWuliuwangdianEntity::getOrgcode, wuliuWangDian.getData().getOrgcode())
                        .set(WuLiuWuliuwangdianEntity::getDestcode, wuliuWangDian.getData().getDestcode())
                        .set(WuLiuWuliuwangdianEntity::getExepresstype, wuliuWangDian.getData().getExepresstype())
                        .set(WuLiuWuliuwangdianEntity::getYuejiekahao, wuliuWangDian.getData().getYuejiekahao())
                        .eq(WuLiuWuliuwangdianEntity::getWuliuid, wuliuWangDian.getData().getWuliuid()).update();
            }
        }
    }
}
