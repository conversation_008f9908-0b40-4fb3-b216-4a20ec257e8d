package com.jiuji.oa.wuliu.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 吴刘注
 *
 * <AUTHOR>
 * @date 2021/10/08
 */
@TableName("wuliu")
@Data
public class WuLiuNote {
    /**
     * id
     */
    private Long id;

    /**
     * 发送方姓名
     */
    @TableField("sname")
    private String sendName;

    /**
     * 发送方手机号
     */
    @TableField("smobile")
    private String sendMobile;

    /**
     * 发送方地址
     */
    @TableField("saddress")
    private String sendAddress;

    /**
     * 发送方大区
     */
    @TableField("sarea")
    private String sendArea;

    /**
     * 发送方城市id
     */
    @TableField("scityid")
    private Integer sendCityId;

    /**
     * 接收方名字
     */
    @TableField("rname")
    private String receiveName;

    /**
     * 接收方手机
     */
    @TableField("rmobile")
    private String receiveMobile;

    /**
     * 接收方地址
     */
    @TableField("raddress")
    private String receiveAddress;

    /**
     * 接收方大区
     */
    @TableField("rarea")
    private String receiveArea;

    /**
     * 接收方城市id
     */
    @TableField("rcityid")
    private Integer receiveCityId;

    /**
     * 物流单生成地区
     */
    private String area;

    /**
     * 登记时间
     */
    @TableField("dtime")
    private LocalDateTime registerTime;

    /**
     * 完成时间
     */
    @TableField("ctime")
    private LocalDateTime accomplishTime;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 成本价格
     */
    @TableField("inprice")
    private BigDecimal costPrice;

    /**
     * 收件人
     */
    @TableField("shoujianren")
    private String receiver;

    /**
     * 派件人
     */
    @TableField("paijianren")
    private String courier;

    /**
     * 状态
     */
    @TableField("stats")
    private String status;

    /**
     * 跟踪单号绑定
     */
    @TableField("danhaobind")
    private Integer trackNumBind;

    /**
     * 物流类型
     */
    @TableField("wutype")
    private Integer wuLiuType;

    /**
     * 评论
     */
    private String comment;

    /**
     * 快递公司
     */
    @TableField("com")
    private String expressCompany;

    /**
     * 单号
     */
    @TableField("nu")
    private String trackNum;

    /**
     * 重量
     */
    private BigDecimal weight;

    /**
     * 操作人
     */
    @TableField("inuser")
    private String operator;

    /**
     * 链接类型
     */
    @TableField("linktype")
    private Integer linkType;

    /**
     * 发送时间
     */
    @TableField("sendtime")
    private LocalDateTime sendTime;

    /**
     * 生成物流单区域id
     */
    @TableField("areaid")
    private Integer areaId;

    /**
     * 发送方区域id
     */
    @TableField("sareaid")
    private Integer sendAreaId;

    /**
     * 接收方区域id
     */
    @TableField("rareaid")
    private Integer receiveAreaId;

    /**
     * 签收人
     */
    @TableField("receiveUser")
    private String recipient;

    /**
     * 签收时间
     */
    @TableField("receiveTime")
    private LocalDateTime signTime;

    /**
     * 通知类型
     */
    @TableField("notifyType")
    private Integer notifyType;

    /**
     * 订单类型
     */
    @TableField("subKinds")
    private Integer orderType;

    /**
     * 支付方法
     */
    private Integer payMethod;

    /**
     * 合并后父级保留物流单号
     */
    @TableField("wpid")
    private Integer wPid;

    /**
     * 物流分类id
     */
    @TableField("wCateId")
    private Integer wCateId;

    /**
     * 最后物流轨迹时间
     */
    @TableField("LastRouteTime")
    private LocalDateTime lastRouteTime;

    /**
     * 估计到达时间
     */
    @TableField("EstimatedArrivalTime")
    private LocalDateTime estimatedArrivalTime;

    /**
     * 产品总价
     */
    @TableField("ProductPriceTotal")
    private BigDecimal productPriceTotal;

    /**
     * 是否是手动创建
     */
    @TableField("isCreateManually")
    private Boolean manuallyCreate;
}
