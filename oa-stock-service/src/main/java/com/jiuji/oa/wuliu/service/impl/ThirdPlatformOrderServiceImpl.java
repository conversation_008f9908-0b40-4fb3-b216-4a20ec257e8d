package com.jiuji.oa.wuliu.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.wuliu.entity.ThirdPlatformOrderEntity;
import com.jiuji.oa.wuliu.mapper.ThirdPlatformOrderMapper;
import com.jiuji.oa.wuliu.service.ThirdPlatformOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * ThirdPlatformOrderServiceImpl 服务实现类
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2022-01-19
 */
@Slf4j
@Service
public class ThirdPlatformOrderServiceImpl extends ServiceImpl<ThirdPlatformOrderMapper, ThirdPlatformOrderEntity> implements ThirdPlatformOrderService {

    /**
     * buyer_mobile 是电话号码包含虚拟和真实的 cancel_check是平台取消订单(1为取消)
     *
     * @param subId 订单号
     * @return String
     * <AUTHOR> [<EMAIL>]
     * @date 2022-01-19
     */
    @Override
    public String getBuyerMobile(Long subId) {
        if(subId == null) {
            log.error("获取虚拟号码报错，subId={}", subId);
            return "";
        }
        ThirdPlatformOrderEntity one = getOne(Wrappers.<ThirdPlatformOrderEntity>lambdaQuery().select(ThirdPlatformOrderEntity::getBuyerMobile).eq(ThirdPlatformOrderEntity::getSubId, subId).and(item -> item.isNull(ThirdPlatformOrderEntity::getCancelCheck).or().eq(ThirdPlatformOrderEntity::getCancelCheck, 0)), false);
        if(one == null) {
            log.error("未找到符合条件的订单，subId={}", subId);
            return "";
        }
        return Optional.ofNullable(one.getBuyerMobile()).orElse("");
    }

}
