package com.jiuji.oa.wuliu.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.wuliu.entity.WuliuClaimform;
import com.jiuji.oa.wuliu.vo.WuliuClaimformCheckerRes;
import com.jiuji.oa.wuliu.vo.WuliuClaimformReq;
import com.jiuji.oa.wuliu.vo.WuliuClaimformRes;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR> yao yao
 * @since 2023-10-19
 */
public interface IWuliuClaimformService extends IService<WuliuClaimform> {

    WuliuClaimformRes getDetail(WuliuClaimformReq req);

    WuliuClaimformCheckerRes getChecker(WuliuClaimformReq req);

    Boolean deleteTodoList(WuliuClaimformReq req);

    void checkWuliuClaimform24();

    void checkWuliuClaimform48();

     void approveClaimFormSave(Integer id);

}
