package com.jiuji.oa.wuliu.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.infra.lmstfy.anotation.LmstfyConsume;
import com.jiuji.oa.apollo.WuliuApolloConfig;
import com.jiuji.oa.nc.bbsxpusers.po.BbsxpUsers;
import com.jiuji.oa.nc.bbsxpusers.service.BbsxpUsersService;
import com.jiuji.oa.nc.common.bo.OaUserBO;
import com.jiuji.oa.nc.common.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.nc.common.exception.CustomizeException;
import com.jiuji.oa.nc.common.exception.RRExceptionHandler;
import com.jiuji.oa.nc.stock.service.ISmsService;
import com.jiuji.oa.nc.user.bo.Ch999UserInfoBO;
import com.jiuji.oa.nc.user.po.Areainfo;
import com.jiuji.oa.nc.user.po.Ch999Fen;
import com.jiuji.oa.nc.user.po.Ch999User;
import com.jiuji.oa.nc.user.po.RoleInfo;
import com.jiuji.oa.nc.user.service.*;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.oa.orginfo.departinfo.client.DepartInfoClient;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.oa.stock.common.lmstfy.LmstfyConfig;
import com.jiuji.oa.stock.common.util.SysUtils;
import com.jiuji.oa.stock.logistics.order.entity.Attachments;
import com.jiuji.oa.stock.logistics.order.entity.WuliuLogs;
import com.jiuji.oa.stock.logistics.order.service.AttachmentsService;
import com.jiuji.oa.stock.logistics.order.service.WuliuLogsService;
import com.jiuji.oa.stock.logistics.order.vo.req.FileReq;
import com.jiuji.oa.stock.logistics.order.vo.req.SaveWuLiuLogReq;
import com.jiuji.oa.wuliu.entity.WuliuClaimform;
import com.jiuji.oa.wuliu.enums.WuliuClaimformStatusEnum;
import com.jiuji.oa.wuliu.enums.WuliuClaimformSubTypeEnum;
import com.jiuji.oa.wuliu.mapper.WuliuClaimformMapper;
import com.jiuji.oa.wuliu.service.IWuliuClaimformService;
import com.jiuji.oa.wuliu.vo.*;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.common.TraceIdUtil;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.meitu.platform.lmstfy.Job;
import com.meitu.platform.lmstfy.client.LmstfyClient;
import com.meitu.platform.lmstfy.exception.LmstfyException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR> yao yao
 * @since 2023-10-19
 */
@Service
@RequiredArgsConstructor
@Slf4j
@DS("oanewWrite")
public class WuliuClaimformServiceImpl extends ServiceImpl<WuliuClaimformMapper, WuliuClaimform> implements IWuliuClaimformService {

    private static final Integer[] VIP_USERS = {2, 500};

    @Resource
    private final AbstractCurrentRequestComponent currentRequestComponent;
    @Resource
    private final WuliuLogsService wuliuLogsService;

    @Resource(name = "firstLmstfyClient")
    private LmstfyClient firstLmstfyClient;
    @Resource
    private final AttachmentsService attachmentsService;
    @Resource
    private final BbsxpUsersService bbsxpUsersService;
    @Resource
    private final RoleInfoService roleService;
    @Resource
    private final IAreaInfoService areaInfoService;
    @Resource
    private final ICh999RankService ch999RankService;
    @Resource
    private final Ch999UserService ch999UserService;
    @Resource
    private final DepartInfoService departInfoService;
    @Resource(name = "oaAsyncRabbitTemplate")
    private final RabbitTemplate rabbitTemplate;
    @Resource
    private final ICh999FenService ch999FenService;
    @Resource
    private DepartInfoClient departInfoClient;
    @Resource
    private AreaInfoClient areaInfoClient;
    @Resource
    private WuliuApolloConfig wuliuApolloConfig;
    @Resource
    private SysConfigClient sysConfigClient;


    public static final String DEFAULT_CHECKERCH999_NAME="系统";

    @Override
    public WuliuClaimformRes getDetail(WuliuClaimformReq req) {

        WuliuClaimformRes res = new WuliuClaimformRes();
        Integer id = req.getId();
        WuliuClaimform wuliuClaimform = this.lambdaQuery().eq(WuliuClaimform::getId, id).one();
        if (Objects.isNull(wuliuClaimform)) {
            return res;
        }
        res.setOrderTime(wuliuClaimform.getOrdertime());
        res.setId(wuliuClaimform.getId());
        res.setAmount(wuliuClaimform.getAmount());
        res.setCourier(wuliuClaimform.getCourier());
        res.setRemark(wuliuClaimform.getRemark());
        res.setTrackingNo(wuliuClaimform.getTrackingno());
        res.setWuliuId(wuliuClaimform.getWuliuid());
        res.setMileage(wuliuClaimform.getMileage());
        res.setSubId(wuliuClaimform.getSubId());
        res.setSubType(wuliuClaimform.getSubType());
        res.setSubTypeName(WuliuClaimformSubTypeEnum.getValue(wuliuClaimform.getSubType()));
        res.setStatus(wuliuClaimform.getStatus());
        res.setStatusName(WuliuClaimformStatusEnum.getValue(wuliuClaimform.getStatus()));
        res.setCheckerId(wuliuClaimform.getCheckerid());
        res.setCheckTime(wuliuClaimform.getChecktime());
        WuliuClaimformCurrentRes vo = this.baseMapper.getCurrent(wuliuClaimform.getCreatorid());
        res.setCurrentAmount(vo.getCurrentAmount());
        res.setCurrentCount(vo.getCurrentCount());

        Integer type = 75;
        List<Attachments> attachments = attachmentsService.newList(new LambdaQueryWrapper<Attachments>().orderByAsc(Attachments::getId)
                .eq(Attachments::getLinkedID, wuliuClaimform.getWuliuid())
                .eq(Attachments::getType, type),wuliuClaimform.getWuliuid(),type);
        if (!org.springframework.util.CollectionUtils.isEmpty(attachments)) {
            List<FileReq> fileReqs = attachmentsService.convertList(attachments);
            res.setFile(fileReqs);
        }

        Ch999User userByCh999Id = ch999UserService.getUserByCh999Id(wuliuClaimform.getCreatorid());
        if (Objects.nonNull(userByCh999Id) && Objects.nonNull(userByCh999Id.getMobile())) {
            BbsxpUsers bbsxpUsers = bbsxpUsersService.getWeixinUserName(userByCh999Id.getMobile());
            if (Objects.nonNull(bbsxpUsers)) {
                res.setWxHeadImg(bbsxpUsers.getWxHeadImg());
                res.setWxName(bbsxpUsers.getHezuoName());
            }
        }
        return res;
    }

    @Override
    public WuliuClaimformCheckerRes getChecker(WuliuClaimformReq req) {
        WuliuClaimformCheckerRes res = new WuliuClaimformCheckerRes();
        Integer checkerCh999Id = 0;
        String checkerCh999Name = DEFAULT_CHECKERCH999_NAME;
        res.setId(req.getId());
        res.setCheckerCh999Id(checkerCh999Id);
        res.setCheckerCh999Name(checkerCh999Name);
        Integer id = req.getId();
        WuliuClaimform wuliuClaimform = this.lambdaQuery().eq(WuliuClaimform::getId, id).one();
        BigDecimal amount = wuliuClaimform.getAmount();
        Integer creatorid = wuliuClaimform.getCreatorid();
        Ch999User creatoridUserByCh999Id = ch999UserService.getUserByCh999Id(creatorid);
        String ch999Name = creatoridUserByCh999Id.getCh999Name();
        Integer wuliuid = wuliuClaimform.getWuliuid();
        if (amount.compareTo(BigDecimal.valueOf(15)) < 0) {
            //更改审核人
            updateCheckId(res);
            //报销金额为15元以下审核人匹配系统，且自动审批完成；
            return res;
        } else if (amount.compareTo(BigDecimal.valueOf(25)) <= 0) {
            //报销金额在15元（含15元）~25元（含25元）审核人匹配申请人所在大区有权值bx1的在职人员（大区内多人有权值获取工号大的人为审核人），

            //获取提单人所在大区
            List<Integer> areaIdList = new ArrayList<>();
            R<Integer> reDepart = departInfoClient.getDepartTypeId(creatoridUserByCh999Id.getDepartId(), 3);
            if(reDepart.isSuccess() && ObjectUtil.isNotNull(reDepart.getData())){
                //获取提单人所在大区所有的所有部门
                R<List<Integer>> reDepartList = departInfoClient.listAllLowNodeId(reDepart.getData());
                if(reDepartList.isSuccess() && ObjectUtil.isNotNull(reDepartList.getData())){
                    //获取提单人所在大区所有的所有门店
                    R<List<AreaInfo>> reAreaInfoList = areaInfoClient.listAreaInfoByDepartIds(reDepartList.getData());
                    if(reAreaInfoList.isSuccess() && CollectionUtils.isNotEmpty(reAreaInfoList.getData())){
                        List<AreaInfo> data = reAreaInfoList.getData();
                        if(CollectionUtils.isNotEmpty(data)){
                            areaIdList = data.stream().map(AreaInfo::getId).collect(Collectors.toList());
                        }
                    } else {
                        String msg = "获取提单人所在大区所有的门店异常："+Optional.of(reAreaInfoList.getMsg()).orElse(reDepart.getUserMsg())+"或者查询为空";
                        res.setRemark(msg);
                    }
                } else {
                    String msg = "获取提单人所在大区所有的所有部门："+Optional.of(reDepartList.getMsg()).orElse(reDepart.getUserMsg())+"或者查询为空";
                    res.setRemark(msg);
                }
            } else {
                String msg = "获取提单人所在大区异常："+Optional.of(reDepart.getMsg()).orElse(reDepart.getUserMsg())+"或者查询为空";
                res.setRemark(msg);
            }
            if(CollectionUtils.isEmpty(areaIdList)){
                //更改审核人
                updateCheckId(res);
                return res;
            }
            List<Integer> bx1 = ch999RankService.getCh999IdsByRanksAndBigRegion(Collections.singletonList("bx1"), areaIdList);
            Integer max = bx1.stream().max(Comparator.comparing(x -> x)).orElse(null);

            if (Objects.nonNull(max)) {
                Ch999User maxUserByCh999Id = ch999UserService.getUserByCh999Id(max);
                checkerCh999Id = maxUserByCh999Id.getCh999Id();
                checkerCh999Name = maxUserByCh999Id.getCh999Name();
                res.setCheckerCh999Id(checkerCh999Id);
                res.setCheckerCh999Name(checkerCh999Name);
                String msg = ch999Name + "发起报销申请，物流单号:" + wuliuid;
                //更改审核人
                updateCheckId(res);
                pushTodoList(checkerCh999Id, id, null, 1, msg);
                pushWuliuClaimFormDelayedMsg(id, 1);
                return res;
            } else {
                Integer area1id = creatoridUserByCh999Id.getArea1id();
                Areainfo areainfo = areaInfoService.getById(area1id);
                //贵州
                if (areainfo.getPid() == 52) {
                    //所在大区找不到审核人且申请人归属地区为贵州省，则找dc1角色为分公司物流负责人的在职人员，找不到dc1角色为分公司物流负责人则系统自动审核完成；
                    Integer roleId = roleService.lambdaQuery().eq(RoleInfo::getRoleName, "分公司物流负责人")
                            .list().stream().map(RoleInfo::getId).findFirst().orElse(null);
                    List<Ch999UserInfoBO> userInfoList = ch999UserService.getUserInfoByMainRolesWithoutAttendance(Collections.singletonList(roleId));
                    if (CollectionUtils.isNotEmpty(userInfoList)) {
                        Integer maxUserId = userInfoList.stream().filter(x -> x.getArea1Id() == 113)
                                .map(Ch999UserInfoBO::getCh999Id).max(Comparator.comparing(x -> x)).orElse(null);
                        if (Objects.nonNull(maxUserId)) {
                            Ch999User maxUserByCh999Id = ch999UserService.getUserByCh999Id(maxUserId);
                            checkerCh999Id = maxUserByCh999Id.getCh999Id();
                            checkerCh999Name = maxUserByCh999Id.getCh999Name();
                            res.setCheckerCh999Id(checkerCh999Id);
                            res.setCheckerCh999Name(checkerCh999Name);
                            //更改审核人
                            updateCheckId(res);
                            String msg = ch999Name + "发起报销申请，物流单号:" + wuliuid;
                            pushTodoList(checkerCh999Id, id, null, 1, msg);
                            pushWuliuClaimFormDelayedMsg(id, 1);
                            return res;
                        }
                    }
                } else {
                    //所在大区找不到审核人且申请人归属地区为云南省，则找dc角色为物流运营经理的人，找不到dc角色为物流运营经理的在职人员则系统自动审核完成，
                    Integer roleId = roleService.lambdaQuery().eq(RoleInfo::getRoleName, "物流运营经理")
                            .list().stream().map(RoleInfo::getId).findFirst().orElse(null);
                    List<Ch999UserInfoBO> userInfoList = ch999UserService.getUserInfoByMainRolesWithoutAttendance(Collections.singletonList(roleId));
                    if (CollectionUtils.isNotEmpty(userInfoList)) {
                        Integer maxUserId = userInfoList.stream().filter(x -> x.getArea1Id() == 16)
                                .map(Ch999UserInfoBO::getCh999Id).max(Comparator.comparing(x -> x)).orElse(null);
                        if (Objects.nonNull(maxUserId)) {
                            Ch999User maxUserByCh999Id = ch999UserService.getUserByCh999Id(maxUserId);
                            checkerCh999Id = maxUserByCh999Id.getCh999Id();
                            checkerCh999Name = maxUserByCh999Id.getCh999Name();
                            res.setCheckerCh999Id(checkerCh999Id);
                            res.setCheckerCh999Name(checkerCh999Name);
                            String msg = ch999Name + "发起报销申请，物流单号:" + wuliuid;
                            //更改审核人
                            updateCheckId(res);
                            pushTodoList(checkerCh999Id, id, null, 1, msg);
                            pushWuliuClaimFormDelayedMsg(id, 1);
                            return res;
                        }
                    }
                }
            }
            //更改审核人
            updateCheckId(res);
            return res;
        } else {
            //报销金额在25元以上审核人匹配有权值bx2的人（多人有权值获取工号大的人为审核人），找不到有权限bx2的在职人员则系统自动审核通过
            List<Integer> bx2 = ch999RankService.getCh999IdsByRanksAndBigRegion(Collections.singletonList("bx2"),Collections.singletonList(16));
            Integer max = bx2.stream().max(Comparator.comparing(x -> x)).orElse(null);
            if (Objects.nonNull(max)) {
                Ch999User maxUserByCh999Id = ch999UserService.getUserByCh999Id(max);
                checkerCh999Id = maxUserByCh999Id.getCh999Id();
                checkerCh999Name = maxUserByCh999Id.getCh999Name();
                res.setCheckerCh999Id(checkerCh999Id);
                res.setCheckerCh999Name(checkerCh999Name);
                String msg = ch999Name + "发起报销申请，物流单号:" + wuliuid;
                //更改审核人
                updateCheckId(res);
                pushTodoList(checkerCh999Id, id, null, 1, msg);
                pushWuliuClaimFormDelayedMsg(id, 1);
                return res;
            }
        }
        //更改审核人
        updateCheckId(res);
        return res;
    }


    /**
     * 快递报销 修改审核人
     * @param res
     */
    private void updateCheckId(WuliuClaimformCheckerRes res){
        Integer id = res.getId();
        Integer userId = res.getCheckerCh999Id();
        OaUserBO currentStaffId = Optional.ofNullable(currentRequestComponent.getCurrentStaffId()).orElseThrow(()-> new CustomizeException("登录信息超时"));
        WuliuClaimform one = Optional.ofNullable(this.lambdaQuery().eq(WuliuClaimform::getId, id).one()).orElseThrow(()->new CustomizeException("快递报销单已删除或者不存在"));
        boolean update = this.lambdaUpdate().eq(WuliuClaimform::getId, id)
                .set(WuliuClaimform::getCheckerid, userId)
                .update();
        String comment = String.format("快递报销更新 id：%s，审核人由：%s 更新为：%s", id, one.getCheckerid(), userId);
        if(update){
            SaveWuLiuLogReq saveWuLiuLogReq = new SaveWuLiuLogReq();
            saveWuLiuLogReq.setWuliuid(one.getWuliuid());
            saveWuLiuLogReq.setInuser(currentStaffId.getUserName());
            saveWuLiuLogReq.setMsg(comment+Optional.ofNullable(res.getRemark()).orElse(""));
            try {
                wuliuLogsService.saveWuliuLog(saveWuLiuLogReq);
            }catch (Exception e){
                log.error(comment,e);
            }
        } else {
            RRExceptionHandler.logError("快递报销更新更新审核人失败", res, null, SpringUtil.getBean(ISmsService.class)::sendOaMsgTo9JiMan);
            throw new CustomizeException("快递报销更新更新审核人失败："+comment);
        }
    }

    /**
     * 调用OA 接口
     * @param id
     */
    @Override
    public void approveClaimFormSave(Integer id){
        if(ObjectUtil.isNull(id)){
            return;
        }
        String token = Optional.ofNullable(currentRequestComponent.getCurrentToken()).orElseThrow(()->new CustomizeException("登录信息失效"));
        //获取域名
        String host = Optional.of(sysConfigClient.getValueByCode(SysConfigConstant.MOA_URL))
                .filter(r -> ResultCode.SUCCESS == r.getCode())
                .map(R::getData)
                .orElseThrow(() -> new CustomizeException("获取域名出错"));
        HashMap<String, Integer> param = new HashMap<>();
        param.put("id",id);
        String url= host+"/oa/addorder/approveClaimFormSave";
        HttpResponse response = HttpUtil.createPost(url)
                .header("Authorization", token)
                .body(JSONUtil.toJsonStr(param))
                .execute();
        String str = String.format("快递报销调用OA打款接口失败，调用地址：%s，传入参数：%s", url, id);
        if(response.isOk()){
            String body = response.body();
            log.error(str+"返回结果："+body);
            if(StringUtils.isNotEmpty(body)){
                R result = JSONUtil.toBean(JSONUtil.toJsonStr(body), R.class);
                if(!result.isSuccess()){
                    throw new CustomizeException(Optional.ofNullable(result.getUserMsg()).orElse(result.getMsg()));
                }
            }
            throw new CustomizeException(str+"返回结果为空");
            } else {
            throw new CustomizeException(str);
        }
    }


    private void pushTodoList(Integer checkerCh999Id, Integer id, Integer updateStaffId, Integer mqKind, String msg) {
        TodoListMqVo vo = new TodoListMqVo()
                .setBusinessNo(Convert.toStr(id)) // 业务主键
                .setLink(SysUtils.getCurrentUrl() + "/new/#/logistics/reimbursement/" + id) // 跳转链接
                .setMqKind(mqKind) // mq消息处理类型 1 生成待办 2 删除待办 3更新
                .setMsg(msg) // 待办描述
                .setStaffId(checkerCh999Id) // 审核人工号
                .setUpdateStaffId(updateStaffId)
                .setType(41); // 待办类型，我们这边就写死41
        String msgMq = JSONObject.toJSONString(vo);
        rabbitTemplate.convertAndSend("office.direct.to_do_list", msgMq);
        log.error("移动端快递报销待办MQ消息推送：{}",msgMq);
    }

    @Override
    public Boolean deleteTodoList(WuliuClaimformReq req) {
        Integer id = req.getId();
        WuliuClaimform wuliuClaimform = this.lambdaQuery().eq(WuliuClaimform::getId, id).one();
        log.error("删除物流报账待办查询订单结果：{}",JSONUtil.toJsonStr(wuliuClaimform));
        Integer checkerid = wuliuClaimform.getCheckerid();
        Optional.ofNullable(ch999UserService.getUserByCh999Id(checkerid)).ifPresent(checker->{
            String msg = checker.getCh999Name() + "完成审批报销申请，物流单号:" + wuliuClaimform.getWuliuid();
            pushTodoList(checkerid, id, null, 2, msg);
        });
        return true;
    }

    /**
     * 发送延时队列
     *
     * @param wuliuClaimFormId
     */
    private void pushWuliuClaimFormDelayedMsg(Integer wuliuClaimFormId, Integer delayTimes) {
        WuliuClaimFormDelayedMsgVO msgVO = new WuliuClaimFormDelayedMsgVO();
        msgVO.setWuliuClaimFormId(wuliuClaimFormId);
        msgVO.setDelayTimes(delayTimes);
        try {
            //延时24小时
            String publish = firstLmstfyClient.publish(LmstfyConfig.resolve(LmstfyConfig.WULIU_CLAIM_FORM),
                    JSONUtil.toJsonStr(msgVO).getBytes(), 0, (short) 1, wuliuApolloConfig.getPushSecond());
            log.error("物流报账单审核延迟队列推送成功，队列名称{}，推送参数{}，返回结果{}", LmstfyConfig.resolve(LmstfyConfig.WULIU_CLAIM_FORM)
                    , JSONUtil.toJsonStr(msgVO), publish);
        } catch (LmstfyException e) {
            log.error("物流报账单审核延迟队列推送异常，队列名称{}，推送参数{}", LmstfyConfig.resolve(LmstfyConfig.WULIU_CLAIM_FORM), JSONUtil.toJsonStr(msgVO), e);
        }
    }


    @LmstfyConsume(queues = LmstfyConfig.WULIU_CLAIM_FORM, clientBeanName = "firstLmstfyClient")
    public void consumeMeituanPrintPickQueue(Job job) {
        if(job == null){
            return;
        }
        MDC.put(TraceIdUtil.TRACE_ID_KEY, TraceIdUtil.getTraceId());
        Optional<WuliuClaimFormDelayedMsgVO> reqOpt = Optional.empty();
        try {
            log.error("物流报账单审核延迟队列消费到数据：{}", job.getData());
            String data = Optional.ofNullable(job).orElse(new Job()).getData();
            if (StringUtils.isEmpty(data)) {
            log.error("物流报账单审核延迟队列消费到数据为空");
                return;
            }
            WuliuClaimFormDelayedMsgVO vo = JSONUtil.toBean(data, WuliuClaimFormDelayedMsgVO.class);
            reqOpt = Optional.ofNullable(vo);
            Integer delayTimes = vo.getDelayTimes();
            if (delayTimes == 1) {
                boolean checkFlag = this.checkWuliuClaimform24ById(vo);
                if (!checkFlag) {
                    this.pushWuliuClaimFormDelayedMsg(vo.getWuliuClaimFormId(), 2);
                }
                return;
            }
            if (delayTimes == 2) {
                boolean checkFlag = this.checkWuliuClaimform24ById(vo);
                if (!checkFlag) {
                    boolean checkFlag2 = this.checkWuliuClaimform48ById(vo);
                    if (!checkFlag2) {
                        this.pushWuliuClaimFormDelayedMsg(vo.getWuliuClaimFormId(), 1);
                    }
                }
            }
        } catch (Exception e) {
            RRExceptionHandler.logError(StrUtil.format("物流报账单[{}]审核延迟队列", reqOpt.map(WuliuClaimFormDelayedMsgVO::getWuliuClaimFormId)
                    .orElse(0)), job.getData(), e, eMsg -> {
                SpringUtil.getBean(ISmsService.class).sendOaMsgTo9JiMan(eMsg);
            });
        }finally {
            MDC.remove(TraceIdUtil.TRACE_ID_KEY);
        }
    }

    private boolean checkWuliuClaimform24ById(WuliuClaimFormDelayedMsgVO vo) {
        WuliuClaimform wuliuClaimform = this.lambdaQuery()
                .eq(WuliuClaimform::getId, vo.getWuliuClaimFormId())
                .ne(WuliuClaimform::getCheckerid, 0)
                .eq(WuliuClaimform::getStatus, WuliuClaimformStatusEnum.WAITCHECK.getCode()).one();
        if (Objects.nonNull(wuliuClaimform)) {
            //审核人为dc角色为物流运营经理的在职人员不再进行积分扣除
            Integer checkerid = wuliuClaimform.getCheckerid();
            Integer roleId = roleService.lambdaQuery().eq(RoleInfo::getRoleName, "物流运营经理")
                    .list().stream().map(RoleInfo::getId).findFirst().orElse(null);
            List<Ch999UserInfoBO> userInfoList = ch999UserService.getUserInfoByMainRolesWithoutAttendance(Collections.singletonList(roleId));
            List<Integer> dcMangerCh999IdList = userInfoList.stream()
                    .filter(x -> x.getArea1Id() == 16)
                    .map(Ch999UserInfoBO::getCh999Id).collect(Collectors.toList());
            if (!dcMangerCh999IdList.contains(checkerid)) {
                //扣除10积分
                Ch999Fen ch999Fen = new Ch999Fen();
                ch999Fen.setCh999_id(checkerid);
                ch999Fen.setYanyin("因物流报账待办事项审核延误，您被扣了10积分");
                ch999Fen.setFen(-10);
                ch999Fen.setInuser("系统");
                ch999Fen.setFendate(LocalDateTime.now());
                ch999FenService.save(ch999Fen);
                return false;
            }
        }
        return true;
    }

    @Override
    public void checkWuliuClaimform24() {
        List<WuliuClaimform> waitCheckList = this.lambdaQuery()
                .eq(WuliuClaimform::getStatus, WuliuClaimformStatusEnum.WAITCHECK.getCode()).list();
        LocalDateTime now = LocalDateTime.now();
        for (WuliuClaimform wuliuClaimform : waitCheckList) {
            LocalDateTime createtime = wuliuClaimform.getCreatetime();
            if (Duration.between(now, createtime).toHours() < 24) {
                continue;
            } else {
                //扣除10积分
                Ch999Fen ch999Fen = new Ch999Fen();
                ch999Fen.setCh999_id(wuliuClaimform.getCheckerid());
                ch999Fen.setYanyin("因物流报账待办事项审核延误，您被扣了10积分");
                ch999Fen.setFen(-10);
                ch999Fen.setInuser("系统");
                ch999Fen.setFendate(LocalDateTime.now());
                ch999FenService.save(ch999Fen);
            }
        }
    }

    private boolean checkWuliuClaimform48ById(WuliuClaimFormDelayedMsgVO vo) {
        WuliuClaimform wuliuClaimform = this.lambdaQuery()
                .eq(WuliuClaimform::getId, vo.getWuliuClaimFormId())
                .ne(WuliuClaimform::getCheckerid, 0)
                .eq(WuliuClaimform::getStatus, WuliuClaimformStatusEnum.WAITCHECK.getCode()).one();
        if (Objects.nonNull(wuliuClaimform)) {
            //换人
            //所在大区找不到审核人且申请人归属地区为云南省，则找dc角色为物流运营经理的人，找不到dc角色为物流运营经理的在职人员则系统自动审核完成，
            Integer roleId = roleService.lambdaQuery().eq(RoleInfo::getRoleName, "物流运营经理")
                    .list().stream().map(RoleInfo::getId).findFirst().orElse(null);
            List<Ch999UserInfoBO> userInfoList = ch999UserService.getUserInfoByMainRolesWithoutAttendance(Collections.singletonList(roleId));
            if (CollectionUtils.isNotEmpty(userInfoList)) {
                Integer newCheckid = userInfoList.stream().filter(x -> x.getArea1Id() == 16)
                        .map(Ch999UserInfoBO::getCh999Id).max(Comparator.comparing(x -> x)).orElse(null);
                if (Objects.nonNull(newCheckid)) {
                    Integer oldCheckerid = wuliuClaimform.getCheckerid();
                    if (!oldCheckerid.equals(newCheckid)) {
                        Integer id = wuliuClaimform.getId();
                        //更新业务表
                        boolean update = this.lambdaUpdate().eq(WuliuClaimform::getId, id)
                                .set(WuliuClaimform::getCheckerid, newCheckid)
                                .update();
                        if (update) {
                            Ch999User creatoridUserByCh999Id = ch999UserService.getUserByCh999Id(wuliuClaimform.getCreatorid());
                            String msg = creatoridUserByCh999Id.getCh999Name() + "发起报销申请，物流单号:" + wuliuClaimform.getWuliuid();
                            //更新待办
                            pushTodoList(oldCheckerid, id, newCheckid, 3, msg);
                            Ch999User oldChecker = ch999UserService.getUserByCh999Id(oldCheckerid);
                            Ch999User newChecker = ch999UserService.getUserByCh999Id(newCheckid);
                            String wuliuLog = "超过48小时未审核，审核人由【" + oldChecker.getCh999Name() + "】改为【" + newChecker.getCh999Name() + "】";
                            //记录物流单日志
                            wuliuLogsService.save(new WuliuLogs().setWuliuid(wuliuClaimform.getWuliuid())
                                    .setInuser("系统").setMsg(wuliuLog).setDtime(LocalDateTime.now()));
                            return false;
                        }
                    }
                }
            }
        }
        return true;
    }

    @Override
    public void checkWuliuClaimform48() {
        List<WuliuClaimform> waitCheckList = this.lambdaQuery()
                .eq(WuliuClaimform::getStatus, WuliuClaimformStatusEnum.WAITCHECK.getCode()).list();
        LocalDateTime now = LocalDateTime.now();
        for (WuliuClaimform wuliuClaimform : waitCheckList) {
            LocalDateTime createtime = wuliuClaimform.getCreatetime();
            if (Duration.between(now, createtime).toHours() < 48) {
                continue;
            } else {
                //换人
                //所在大区找不到审核人且申请人归属地区为云南省，则找dc角色为物流运营经理的人，找不到dc角色为物流运营经理的在职人员则系统自动审核完成，
                Integer roleId = roleService.lambdaQuery().eq(RoleInfo::getRoleName, "物流运营经理")
                        .list().stream().map(RoleInfo::getId).findFirst().orElse(null);
                List<Ch999UserInfoBO> userInfoList = ch999UserService.getUserInfoByMainRolesWithoutAttendance(Collections.singletonList(roleId));
                if (CollectionUtils.isNotEmpty(userInfoList)) {
                    Integer maxUserId = userInfoList.stream().filter(x -> x.getArea1Id() == 16)
                            .map(Ch999UserInfoBO::getCh999Id).max(Comparator.comparing(x -> x)).orElse(null);
                    if (Objects.nonNull(maxUserId)) {
                        Ch999User maxUserByCh999Id = ch999UserService.getUserByCh999Id(maxUserId);
                        Integer id = wuliuClaimform.getId();
                        boolean update = this.lambdaUpdate().eq(WuliuClaimform::getId, id)
                                .set(WuliuClaimform::getCheckerid, maxUserByCh999Id)
                                .update();
                        if (update) {
                            Integer checkerid = wuliuClaimform.getCheckerid();
                            Ch999User creatoridUserByCh999Id = ch999UserService.getUserByCh999Id(wuliuClaimform.getCreatorid());
                            String msg = creatoridUserByCh999Id.getCh999Name() + "发起报销申请，物流单号:" + wuliuClaimform.getWuliuid();
                            pushTodoList(checkerid, id, maxUserByCh999Id.getCh999Id(), 3, msg);

                        }
                    }
                }
            }
        }
    }
}
