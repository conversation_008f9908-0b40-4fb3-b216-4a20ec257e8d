package com.jiuji.oa.wuliu.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ch999.common.util.atlas.AtlasUtil;
import com.ch999.common.util.atlas.CoordinateUtil;
import com.ch999.common.util.utils.Exceptions;
import com.ch999.common.util.vo.atlas.Coordinate;
import com.google.common.collect.Lists;
import com.jiuji.cloud.org.vo.request.RoleSimpleReq;
import com.jiuji.cloud.org.vo.request.RolesSimpleVo;
import com.jiuji.oa.apollo.WuliuApolloConfig;
import com.jiuji.oa.logapi.service.ISubLogService;
import com.jiuji.oa.loginfo.order.vo.req.SubLogsNewReq;
import com.jiuji.oa.nc.MaterialApplyCloud;
import com.jiuji.oa.nc.channel.service.QudaocontactssService;
import com.jiuji.oa.nc.channel.vo.dto.QuDaoShippingDTO;
import com.jiuji.oa.nc.common.bo.OaUserBO;
import com.jiuji.oa.nc.common.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.nc.common.constant.DataSourceConstants;
import com.jiuji.oa.nc.common.constant.RedisKeys;
import com.jiuji.oa.nc.common.enums.XtenantEnum;
import com.jiuji.oa.nc.common.exception.CustomizeException;
import com.jiuji.oa.nc.common.exception.RRExceptionHandler;
import com.jiuji.oa.nc.common.req.AttachmentsVO;
import com.jiuji.oa.nc.common.req.OaAttachmentsAddOrUpdateReqVO;
import com.jiuji.oa.nc.common.util.CommonUtil;
import com.jiuji.oa.nc.common.util.NumUtil;
import com.jiuji.oa.nc.stock.service.ISmsService;
import com.jiuji.oa.nc.stock.vo.req.ImMessageReq;
import com.jiuji.oa.nc.stock.vo.req.LogisticsMessageBody;
import com.jiuji.oa.nc.user.po.Areainfo;
import com.jiuji.oa.nc.user.po.Ch999User;
import com.jiuji.oa.nc.user.service.Ch999UserService;
import com.jiuji.oa.nc.user.service.IAreaInfoService;
import com.jiuji.oa.nc.user.service.RoleInfoService;
import com.jiuji.oa.oacore.csharp.cloud.CsharpInWcfCloud;
import com.jiuji.oa.oacore.csharp.vo.req.SubCheckOpReq;
import com.jiuji.oa.oacore.weborder.WebOrderCloud;
import com.jiuji.oa.oacore.weborder.req.SubAppReq;
import com.jiuji.oa.oacore.weborder.res.ImOrderObjVo;
import com.jiuji.oa.oacore.weborder.res.ImOrderVO;
import com.jiuji.oa.oacore.weborder.res.LogisticsTransferInfoVO;
import com.jiuji.oa.orderdynamics.util.TencentMapUtil;
import com.jiuji.oa.stock.area.service.AreaOpeningHoursService;
import com.jiuji.oa.stock.area.vo.AreaBusinessStatus;
import com.jiuji.oa.stock.common.aspect.RepeatSubmitCheck;
import com.jiuji.oa.stock.common.cache.RedisUtils;
import com.jiuji.oa.stock.common.util.Builder;
import com.jiuji.oa.stock.common.util.JacksonJsonUtils;
import com.jiuji.oa.stock.common.util.SysConfigUtils;
import com.jiuji.oa.stock.common.util.SysUtils;
import com.jiuji.oa.stock.logistics.order.context.WuliuOrderContext;
import com.jiuji.oa.stock.logistics.order.entity.Wuliu;
import com.jiuji.oa.stock.logistics.order.enums.WuliuStatusEnum;
import com.jiuji.oa.stock.logistics.order.enums.WuliuTypeEnum;
import com.jiuji.oa.stock.logistics.order.mapstruct.WuliuMapStruct;
import com.jiuji.oa.stock.logistics.order.service.AttachmentsService;
import com.jiuji.oa.stock.logistics.order.service.WuliuService;
import com.jiuji.oa.stock.logistics.order.vo.ExpressPushVO;
import com.jiuji.oa.stock.logistics.order.vo.req.JdDeliveryPreFeedbackReq;
import com.jiuji.oa.stock.logistics.order.vo.req.WuLiuWebReq;
import com.jiuji.oa.stock.logistics.order.vo.req.WuliuWayBillNoReq;
import com.jiuji.oa.stock.logistics.paotui.entity.WuliuAutoPaotuiInfoEntry;
import com.jiuji.oa.stock.logistics.paotui.entity.WuliuPaijianInfoEntry;
import com.jiuji.oa.stock.logistics.paotui.service.IWuliuAutoPaotuiInfoService;
import com.jiuji.oa.stock.logistics.paotui.service.IWuliuPaijianInfoService;
import com.jiuji.oa.stock.logisticscenter.enums.LogisticsExpressTypeEnum;
import com.jiuji.oa.stock.logisticscenter.serive.ICancelOrderService;
import com.jiuji.oa.stock.logisticscenter.serive.ILogisticsExpressService;
import com.jiuji.oa.stock.logisticscenter.vo.req.CancelOrderReqV2;
import com.jiuji.oa.stock.logisticscenter.vo.req.QueryRouteReqV2;
import com.jiuji.oa.stock.logisticscenter.vo.req.WuliuCancelExpressReq;
import com.jiuji.oa.stock.logisticscenter.vo.res.QueryRouteResV2;
import com.jiuji.oa.stock.logisticscenter.vo.res.RouteData;
import com.jiuji.oa.stock.nationalSupplement.res.NationalSupplementKindRes;
import com.jiuji.oa.stock.nationalSupplement.service.NationalSupplementService;
import com.jiuji.oa.stock.stockmanage.vo.req.WuliuInstockPendingUpdateVO;
import com.jiuji.oa.wuliu.bo.*;
import com.jiuji.oa.wuliu.component.WuliuMqProducer;
import com.jiuji.oa.wuliu.constant.WuLiuConstant;
import com.jiuji.oa.wuliu.constant.WuLiuTypeConstant;
import com.jiuji.oa.wuliu.constant.WuliuExpressConstant;
import com.jiuji.oa.wuliu.dto.ShouHouAddInfoDTO;
import com.jiuji.oa.wuliu.dto.SubExpectTimeDTO;
import com.jiuji.oa.wuliu.dto.YuYueAddInfoDTO;
import com.jiuji.oa.wuliu.dto.req.SubPositionReq;
import com.jiuji.oa.wuliu.dto.req.WuliuInvalidReqV2;
import com.jiuji.oa.wuliu.entity.*;
import com.jiuji.oa.wuliu.enums.*;
import com.jiuji.oa.wuliu.mapstruct.WuLiuMapStruct;
import com.jiuji.oa.wuliu.service.*;
import com.jiuji.oa.wuliu.utils.WuliuAddressUtil;
import com.jiuji.oa.wuliu.utils.WuliuUtil;
import com.jiuji.oa.wuliu.vo.*;
import com.jiuji.oa.wuliu.vo.req.*;
import com.jiuji.oa.wuliu.vo.res.*;
import com.jiuji.oa.wuliu.vo.tencentmap.TencentMapDrivingResVO;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.foundation.message.send.constants.OaMesTypeEnum;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.common.build.LambdaBuild;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.enums.EnumUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * @description: IWuLiuBusService
 * </p>
 * @author: David
 * @create: 2021-06-01 15:03
 */
@Slf4j
@Service("wuLiuBusServiceImpl2")
@RequiredArgsConstructor
public class WuLiuBusServiceImpl implements IWuLiuBusService {

    private static final int IMG_TYPE = 13;
    @Autowired
    private IWuliuPaijianInfoService wuliuPaijianInfoService;
    private final IWuLiuService wuLiuService;
    private final WuliuService wuLiuService2;
    private final WuliuMapStruct wuliuMapStruct;
    private final IWuLiuLogService wuLiuLogsService;
    private final IPrintCountService printCountService;
    private final IAreaInfoService areaInfoService;
    private final QudaocontactssService qudaoContactssService;
    private final IShouHouYuYueBusService shouHouYuYueBusService;
    private final IWuLiuWuliuwangdianService wuLiuWuliuwangdianService;
    private final Ch999UserService ch999UserService;
    private final AttachmentsService attachmentsService;
    private final IShouHouYuYueService shouHouYuYueService;
    private final ThirdPlatformOrderService thirdPlatformOrderService;
    private final MaterialApplyCloud materialApplyCloud;
    private final IWuLiuExpressExtendService wuLiuExpressExtendService;
    private final IWuLiuSubService wuLiuSubService;
    private final IWuLiuRecoverSubService wuLiuRecoverSubService;
    private final IRecoverMarketinfoService recoverMarketinfoService;
    private final WuLiuMapStruct wuLiuMapStruct;
    private final ILogisticsExpressService logisticsExpressService;
    private final ZtoBillInfoService ztoBillInfoService;
    private final IWuliuExpressNuService wuliuExpressNuService;
    private final WuliuOrderContext wuliuOrderContext;
    private final ICancelOrderService cancelOrderService;
    private final IWuliuAddressService wuliuAddressService;
    private final WuliuExtendInfoService wuliuExtendInfoService;
    private final IWuLiuRecoverMarketInfoService wuLiuRecoverMarketInfoService;
    private final ISmsService smsService;
    private final AbstractCurrentRequestComponent currentRequestComponent;
    private final WebOrderCloud webOrderCloud;
    @Resource
    private IExpressEnumService expressEnumService;


    @Resource
    private IWuliuInstockPendingService wuliuInstockPendingService;

    /**
     * 新版附件地址
     *
     * @param fid
     * @return String
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-05
     */
    public static String get9xunImgPath(String fid) {
        return String.format(WuLiuConstant.IMG_URL, StringUtils.isNotBlank(fid) ? fid.replace(WuLiuConstant.C_DOT, WuLiuConstant.C_SLASH) : StringUtils.EMPTY);
    }

    /**
     * url encode
     *
     * @param str
     * @return String
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-26
     */
    private static String urlEncode(String str) {
        try {
            String encode = URLEncoder.encode(str, StandardCharsets.UTF_8.name());
            return encode.replace("+", "%20");
        } catch (UnsupportedEncodingException e) {
            log.error("WuLiuBusServiceImpl.urlEncode 编码报错: {}", Exceptions.getStackTraceAsString(e), e);
            return "";
        }
    }

    @Override
    @DS("oanewWrite")
    @Transactional(rollbackFor = Exception.class)
    public Integer oaAdd(@Valid WuLiuWebReq req) {
        if (null != req.getDanhaobind() && !Objects.equals(0, req.getDanhaobind())) {
            //同一类别、分类、单号下，只允许创建一条记录
            WuLiuEntity wuliu = wuLiuService.getWuliuBywCateId(req.getWCateId(), req.getWutype(), req.getDanhaobind());
            if (null != wuliu) {
                throw new CustomizeException("该单号已创建过物流单:" + wuliu.getId() + "，请勿重复创建");
            }
        }
        Wuliu entity = wuliuMapStruct.toWuliu(req);
        if(entity.getDtime() == null){
            //创建时间为空,默认为当前
            entity.setDtime(LocalDateTime.now());
        }
        wuLiuService2.save(entity);
        log.debug("成功创建物流单:{}", entity.getId());
        WuLiuLogEntity wuliuLogs = Builder.of(WuLiuLogEntity::new)
                .with(WuLiuLogEntity::setWuliuid, entity.getId())
                .with(WuLiuLogEntity::setInuser, req.getInUser())
                .with(WuLiuLogEntity::setDtime, LocalDateTime.now())
                .with(WuLiuLogEntity::setMsg, "其他页面操作生成物流单")
                .build();
        wuLiuLogsService.save(wuliuLogs);
        log.debug("成功记录物流单日志");
        return entity.getId();
    }

    /**
     * 获取物流单信息
     *
     * @param model
     * @return
     */
    @DS("oanewWrite")
    @Override
    public WuLiuInfoReqVO getWuLiuInfo(WuLiuInfoReqVO model, OaUserBO currentUser) {
        WuLiuEntity entity = wuLiuService.getById(model.getWuliuid());
        //解决订单跳转物流单查询了订单的调拨物流单问题
        if(entity == null && Objects.nonNull(model.getSubId())) {
            if (WuLiuConstant.ACTION_SUB.equals(model.getActionName())) {
                entity = wuLiuService.getOne(Wrappers.<WuLiuEntity>lambdaQuery().eq(WuLiuEntity::getDanHaoBind, model.getSubId()).in(WuLiuEntity::getWuType, Arrays.asList(WuLiuTypeEnum.ORDER.getCode(),WuLiuTypeEnum.ORDER_EXPRESS.getCode())).orderByDesc(WuLiuEntity::getId).last("offset 0 row fetch next 2 row only"),false);
            } else if (WuLiuConstant.ACTION_RE_SUB.equals(model.getActionName())) {
                entity = wuLiuService.getOne(Wrappers.<WuLiuEntity>lambdaQuery().eq(WuLiuEntity::getDanHaoBind, model.getSubId()).eq(WuLiuEntity::getWuType, WuLiuTypeEnum.FOURTEEN_DAY.getCode()).orderByDesc(WuLiuEntity::getId).last("offset 0 row fetch next 2 row only"),false);
            } else {
                entity = wuLiuService.getOne(Wrappers.<WuLiuEntity>lambdaQuery().eq(WuLiuEntity::getDanHaoBind, model.getSubId()).orderByDesc(WuLiuEntity::getId).last("offset 0 row fetch next 2 row only"),false);
            }
        }
        //维修单
        if(Objects.isNull(entity) && !Objects.equals(0, Optional.ofNullable(model.getShouHouId()).orElse(0))) {
            entity = wuLiuService.getOne(Wrappers.<WuLiuEntity>lambdaQuery()
                    .eq(WuLiuEntity::getWuType, WuliuTypeEnum.AFTER_SERVICE.getCode())
                    .eq(WuLiuEntity::getDanHaoBind, model.getShouHouId())
                    .orderByDesc(WuLiuEntity::getId).last("offset 0 row fetch next 2 row only"),false);
        }
        //预约单
        if(Objects.isNull(entity) && !Objects.equals(0, Optional.ofNullable(model.getYuyueId()).orElse(0))) {
            entity = wuLiuService.getOne(Wrappers.<WuLiuEntity>lambdaQuery()
                    .eq(WuLiuEntity::getWuType, WuliuTypeEnum.VISIT.getCode())
                    .eq(WuLiuEntity::getDanHaoBind, model.getYuyueId())
                    .orderByDesc(WuLiuEntity::getId).last("offset 0 row fetch next 2 row only"),false);
        }
        Integer shouhouid = Optional.ofNullable(model.getShouHouId()).orElse(0);

        if (entity != null) {
            model.setWuliuid(Optional.ofNullable(entity.getId()).orElse(0));

            model.setShouHouId(entity.getDanHaoBind());
            model.setDanHaoBind(entity.getDanHaoBind());
            model.setWuType(Optional.ofNullable(entity.getWuType()).orElse(0));
            model.setNu(entity.getNu());
            model.setCom(entity.getCom());
            model.setSubKinds(entity.getSubKinds());
            model.setSareaid(entity.getSAreaId());
            model.setAreaId(entity.getAreaId());
            model.setSCityId(entity.getSCityId());
            model.setRCityId(entity.getRCityId());
            model.setLinkType(String.valueOf(entity.getLinkType()));
            model.setSName(String.valueOf(entity.getSName()));
            model.setWCateId(entity.getWCateId());
            model.setSMobile(String.valueOf(entity.getSMobile()));
            model.setStats(entity.getStats());
            model.setShouJianRen(entity.getShouJianRen());
            model.setPaiJianRen(entity.getPaiJianRen());

            // eAttachmentsType 物流派送图片 = 13,
            List<AttachmentsVO.FileBO> byLinkedIdAndType = attachmentsService.getByLinkedIdAndType(model.getWuliuid(), IMG_TYPE);
            List<OaAttachmentsAddOrUpdateReqVO.FileBO> files = wuliuMapStruct.toFileBO(byLinkedIdAndType);
            if (CollectionUtils.isNotEmpty(files)) {
                files.forEach((OaAttachmentsAddOrUpdateReqVO.FileBO x) -> {
                    if (StringUtils.isEmpty(x.getFilePath())) {
                        String filePath = get9xunImgPath(x.getFid());
                        x.setFilePath(filePath);
                    }
                });
            }
            model.setFiles(files);
            //物流单来源
            model.setSource(entity.getSource());
            model.setInUser(entity.getInUser());
            //跑腿, 设置呼叫的快递列表
            if(WuLiuConstant.PAO_TUI.equals(entity.getCom())){
                List<WuliuAutoPaotuiInfoEntry> paotuiList = SpringUtil.getBean(IWuliuAutoPaotuiInfoService.class)
                        .getPaotuiByWuliuId(entity.getId(), WuliuAutoPaotuiInfoEntry.AutoCallStatus.CALL_RUNNER);
                model.setNuList(paotuiList.stream().map(pt -> WuLiuInfoReqVO.NuVo.builder().nu(pt.getNu()).com(pt.getCom())
                                .comName(LogisticsExpressTypeEnum.getExpressMessage(pt.getCom())).build())
                        .collect(Collectors.toList()));
            }
        }

        model.setSpid(Optional.ofNullable(model.getSpid()).orElse(0));
        model.setRpid(Optional.ofNullable(model.getRpid()).orElse(0));

        model.setSubId(Optional.ofNullable(model.getSubId()).orElse(0));
        model.setYuyueId(Optional.ofNullable(model.getYuyueId()).orElse(0));


        //赋初值
        if (Optional.ofNullable(model.getWuType()).orElse(0) == 0) {
            model.setWuType(NumUtil.ONE);
        }

        boolean queryAddInfoFlag = entity == null;
        //订单派送 物流单
        if (Optional.ofNullable(model.getWuliuid()).orElse(0) != 0 || Optional.ofNullable(model.getSubId()).orElse(0) != 0) {
            model.setPrintCnt(printCountService.getPrintCountBySubId(model.getWuliuid()));
            WuLiuDTO dto = wuLiuService.getWuLiu(model.getActionName(), model.getWuliuid(), model.getSubId());

            if (dto != null) {
                this.setWuLiuInfoReqVO(model, dto);
                boolean bool = wuLiuService.isCurAreaHqDcH1D1(model.getSareaid()) || Objects.equals(model.getSareaid(), WuLiuConstant.AREA_GCB);
                if (Optional.ofNullable(model.getDanHaoBind()).orElse(0) > 0 && !WuliuStatusEnum.INVALID.getCode().equals(model.getStats()) && StringUtils.isBlank(model.getNu()) && StringUtils.isBlank(model.getExpressType()) && bool) {
                    //应付
                    BigDecimal jiaZhi = BigDecimal.ZERO;

                    if (Arrays.asList(WuLiuTypeConstant.ORDER, WuLiuTypeConstant.ORDER_EXPRESS ).contains(model.getWuType())) {
                        BigDecimal orderPayAble = printCountService.getOrderPayAble(model.getDanHaoBind());
                        jiaZhi = Optional.ofNullable(orderPayAble).orElse(jiaZhi);
                    } else if (Arrays.asList(NumUtil.ONE, NumUtil.TWO, NumUtil.THREE).contains(model.getWuType()) && Arrays.asList(NumUtil.ZERO, NumUtil.THREE).contains(model.getSubKinds())) {
                        BigDecimal transferPayAble = printCountService.getTransferPayAble(model.getSubId());
                        jiaZhi = Optional.ofNullable(transferPayAble).orElse(jiaZhi);
                    } else if (Objects.equals(model.getWuType(), NumUtil.NINE) || Objects.equals(NumUtil.TWO, model.getSubKinds())) {
                        BigDecimal returnPayAble = printCountService.getReturnPayAble(model.getSubId());
                        jiaZhi = Optional.ofNullable(returnPayAble).orElse(jiaZhi);
                    }
                    if (jiaZhi.compareTo(BigDecimal.ZERO) > 0 && jiaZhi.compareTo(new BigDecimal(NumUtil.FIVE_HUNDRED)) <= 0) {
                        //model.setExpressType(WuLiuConstant.TWO_HUNDRED_AND_EIGHT_STR)
                        //下线208对应的月结卡号8712265606
                        model.setExpressType("");
                    }
                }
            } else {
                model.setDTime(LocalDateTime.now());
            }

            //ACTION = sub 订单派送
            if (CommonUtil.isNullOrZero(model.getWuliuid()) && WuLiuConstant.ACTION_SUB.equals(model.getActionName())) {
                model.setDTime(LocalDateTime.now());
                model.setStats(Optional.ofNullable(model.getStats()).orElse(0));
                model.setCom(Optional.ofNullable(model.getCom()).orElse(""));
                model.setPrice(BigDecimal.ZERO);
                model.setInPrice(BigDecimal.ZERO);
                WuLiuSubDTO wuLiuSub = wuLiuService.getWuLiuSub(model.getSubId());
                if (Objects.nonNull(wuLiuSub)) {
                    model.setDelivery(wuLiuSub.getDelivery());
                    model.setDeliveryName(SubDeliveryEnum.getMessageByCode(wuLiuSub.getDelivery()));
                    model.setCancelApplication(wuLiuSub.getCancelApplication());
                    model.setIsLock(Boolean.TRUE.equals(RedisUtils.hashHasKey(RedisKeys.EXCEPTION_SUB_REDISKEY, Convert.toStr(model.getSubId()))) ? 1 : 0);

                    model.setAreaId(wuLiuSub.getAreaid());
                    model.setDanHaoBind(model.getSubId());
                    model.setSareaid(wuLiuSub.getAreaid());
                    model.setRName(wuLiuSub.getSubTo());
                    model.setRMobile(wuLiuSub.getSubMobile());
                    model.setRAddress(wuLiuSub.getSubAdds());
                    model.setRCityId(wuLiuSub.getCityid());
                    //美团光速
                    model.setMeiTuanFastFlag(wuLiuService.isFastMeiTuan(model.getSubId()));
                    if (Objects.equals(NumUtil.FIVE, wuLiuSub.getDelivery())) {
                        model.setWuType(WuLiuTypeConstant.ORDER_EXPRESS);
                    } else {
                        model.setWuType(WuLiuTypeConstant.ORDER);
                    }
                    if (Objects.equals(NumUtil.THREE, wuLiuSub.getDelivery()) && Objects.nonNull(wuLiuSub.getZitidianId())) {
                        ZiTiDianDTO ziTiDian = wuLiuService.getZiTiDian(wuLiuSub.getZitidianId());
                        if (Objects.nonNull(ziTiDian)) {
                            model.setRName(ziTiDian.getReceiver());
                            model.setRMobile(ziTiDian.getMobile());
                            model.setRAddress(ziTiDian.getAddress());
                            model.setRCityId(ziTiDian.getCityid1());
                        }
                    }
                    boolean bool = wuLiuService.isCurAreaHqDcH1D1(model.getSareaid()) || Objects.equals(model.getSareaid(), WuLiuConstant.AREA_GCB);
                    if (bool && Optional.ofNullable(wuLiuSub.getYingfum()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.valueOf(NumUtil.FIVE_HUNDRED)) <= 0) {
                        //model.setExpressType(WuLiuConstant.TWO_HUNDRED_AND_EIGHT_STR)
                        //下线208对应的月结卡号8712265606
                        model.setExpressType("");
                    }
                    WuLiuEntity wuLiuEntity = wuLiuService.getWuLiuByWuType(model.getSareaid(), model.getWuType());
                    if (Objects.nonNull(wuLiuEntity)) {
                        model.setShouJianRen(Optional.ofNullable(model.getShouJianRen()).orElse(wuLiuEntity.getShouJianRen()));
                        model.setPaiJianRen(Optional.ofNullable(model.getPaiJianRen()).orElse(wuLiuEntity.getPaiJianRen()));
                        model.setSName(Optional.ofNullable(model.getSName()).orElse(wuLiuEntity.getSName()));
                        model.setSMobile(Optional.ofNullable(model.getSMobile()).orElse(wuLiuEntity.getSMobile()));
                    }
                    //邮寄订单固定发货人和电话：批签：20191118015，
                    Areainfo sareainfo = areaInfoService.getById(model.getSareaid());
                    // EXtenant 九机网 = 0,
                    // EXtenant  丫丫网 = 1,
                    if (Objects.nonNull(sareainfo) && Arrays.asList(0, 1).contains(sareainfo.getXtenant())) {
                        if (Objects.equals(NumUtil.ZERO, sareainfo.getXtenant())) {
                            model.setSName(WuLiuConstant.JIUJI_NAME);
                            //深圳
                            if (Objects.equals(sareainfo.getId(), WuLiuConstant.AREA_SZ)) {
                                model.setSMobile("13760162987");
                            }

                            // #region 地区 Consts  public const int Area_dc = 16; Consts  public const int Area_Dc1 = 113;
                            if (Objects.equals(sareainfo.getId(), WuLiuConstant.AREA_DC)) {
                                model.setSMobile(WuLiuConstant.DC_S_MOBLIE);
                            } else if (Objects.equals(sareainfo.getId(), WuLiuConstant.AREA_DC1)) {
                                model.setSMobile("18508501195");
                            } else {
                                model.setSMobile(sareainfo.getCompanyTel1());
                            }
                        } else {
                            model.setSName("丫丫网");
                            //ydc
                            if (Objects.equals(model.getSareaid(), WuLiuConstant.AREA_YDC)) {
                                model.setSMobile(WuLiuConstant.DC_S_MOBLIE);
                            } else {
                                model.setSMobile(sareainfo.getCompanyTel1());
                            }
                        }
                    } else if (Objects.nonNull(sareainfo)) {
                        model.setSName(sareainfo.getPrintName());
                        model.setSMobile(sareainfo.getCompanyTel1());
                    }
                    Integer count = printCountService.countBasket(model.getSubId());
                    if (Optional.ofNullable(count).orElse(0) > 0) {
                        model.setWCateId(WuLiuCateEnum.DAJIAN_PAISONG.getCode());
                    } else {
                        model.setWCateId(WuLiuCateEnum.XIAOJIAN_PAISONG.getCode());
                    }
                }
            }
            //ACTION = ReSub 二手良品订单派送
            if (CommonUtil.isNullOrZero(model.getWuliuid()) && WuLiuConstant.ACTION_RE_SUB.equals(model.getActionName())) {
                //表示为二手良品订单
                model.setWuType(9);

                model.setDTime(LocalDateTime.now());
                model.setStats(Optional.ofNullable(model.getStats()).orElse(0));
                model.setCom(Optional.ofNullable(model.getCom()).orElse(""));
                model.setPrice(BigDecimal.ZERO);
                model.setInPrice(BigDecimal.ZERO);
                //获取良品订单信息
                WuLiuSubDTO wuLiuReSub = wuLiuService.getWuLiuReSub(model.getSubId());
                if (Objects.nonNull(wuLiuReSub)) {
                    model.setDelivery(wuLiuReSub.getDelivery());
                    model.setDeliveryName(SubDeliveryEnum.getMessageByCode(wuLiuReSub.getDelivery()));
                    model.setCancelApplication(wuLiuReSub.getCancelApplication());
                    model.setIsLock(Boolean.TRUE.equals(RedisUtils.hashHasKey(RedisKeys.EXCEPTION_LPRECOVER_SUB_REDISKEY, Convert.toStr(model.getSubId()))) ? 1 : 0);

                    model.setAreaId(wuLiuReSub.getAreaid());
                    model.setDanHaoBind(model.getSubId());
                    model.setSareaid(wuLiuReSub.getAreaid());
                    model.setRName(wuLiuReSub.getSubTo());
                    model.setRMobile(wuLiuReSub.getSubMobile());
                    model.setRAddress(wuLiuReSub.getSubAdds());
                    model.setRCityId(wuLiuReSub.getCityid());
                    model.setExpectTime(wuLiuReSub.getExpectTime());
                    //良品派送
                    model.setWCateId(WuLiuCateEnum.LIANGPIN_PAISONG.getCode());
                    if (Objects.equals(NumUtil.ONE, wuLiuReSub.getSaleType()) && WuLiuConstant.AREA_H2.equals(model.getSareaid())) {
                        model.setSAddress("贵州省贵阳市南明区花果园国际金融街6号25楼(出电梯左边)");
                        model.setSCityId(520102);
                    } else {
                        model.setSAddress("云南省昆明市五华区学府路690号金鼎科技园三号楼");
                        model.setSCityId(530102);
                    }
                    boolean bool = wuLiuService.isCurAreaHqDcH1D1(model.getSareaid()) || Objects.equals(model.getSareaid(), WuLiuConstant.AREA_GCB);
                    if (bool && Optional.ofNullable(wuLiuReSub.getYingfum()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.valueOf(NumUtil.FIVE_HUNDRED)) <= 0) {
                        model.setExpressType(WuLiuConstant.TWO_HUNDRED_AND_EIGHT_STR);
                    }
                    if (Objects.equals(NumUtil.ONE, wuLiuReSub.getSaleType())) {
                        QuDaoShippingDTO daoShipping = qudaoContactssService.getQuDaoShipping(model.getSubId());
                        if (Objects.nonNull(daoShipping)) {
                            model.setRName(daoShipping.getCwFzr());
                            model.setRMobile(daoShipping.getCwLxfs());
                            model.setRAddress(daoShipping.getShippingAddress());
                            model.setRCityId(daoShipping.getCityId());
                        }
                        if (Objects.equals(Optional.ofNullable(model.getWuliuid()).orElse(0), 0)) {
                            model.setComment(
                                    "单号：" + model.getSubId() + " 编号：" + printCountService.getShelvesNumBySubId(model.getSubId()));
                        }
                    } else if (Objects.equals(NumUtil.THREE, wuLiuReSub.getDelivery()) && Objects.nonNull(wuLiuReSub.getZitidianId())) {
                        ZiTiDianDTO ziTiDian = wuLiuService.getZiTiDian(wuLiuReSub.getZitidianId());
                        if (Objects.nonNull(ziTiDian)) {
                            model.setRName(ziTiDian.getReceiver());
                            model.setRMobile(ziTiDian.getMobile());
                            model.setRAddress(ziTiDian.getAddress());
                            model.setRCityId(ziTiDian.getCityid1());
                        }
                    }
                    WuLiuEntity wuLiuEntity = wuLiuService.getWuLiuByWuType(model.getSareaid(), model.getWuType());
                    if (Objects.nonNull(wuLiuEntity)) {
                        model.setShouJianRen(wuLiuEntity.getShouJianRen());
                        model.setPaiJianRen(wuLiuEntity.getPaiJianRen());
                        model.setSName(wuLiuEntity.getSName());
                        model.setSMobile(wuLiuEntity.getSMobile());
                    }
                    //邮寄订单固定发货人和电话：批签：20191118015，
                    Areainfo sareainfo = areaInfoService.getById(model.getSareaid());

                    //    平台 租户枚举 public enum EXtenant   九机网 = 0, 丫丫网 = 1
                    if (sareainfo != null && Arrays.asList(0, 1).contains(sareainfo.getXtenant())) {
                        if (Objects.equals(sareainfo.getXtenant(), 0)) {
                            model.setSName(WuLiuConstant.JIUJI_NAME);

                            // #region 地区 Consts  public const int Area_dc = 16; Consts  public const int Area_Dc1 = 113;
                            if (Objects.equals(sareainfo.getId(), WuLiuConstant.AREA_DC)) {
                                model.setSMobile(WuLiuConstant.DC_S_MOBLIE);
                            } else if (Objects.equals(sareainfo.getId(), WuLiuConstant.AREA_DC1)) {
                                model.setSMobile("18508501195");
                            } else {
                                model.setSMobile(sareainfo.getCompanyTel1());
                            }
                        } else {
                            model.setSName(WuLiuConstant.YAYA_NAME);
                            if (Objects.equals(model.getSareaid(), WuLiuConstant.AREA_YDC)) {
                                //ydc
                                model.setSMobile(WuLiuConstant.DC_S_MOBLIE);
                            } else {
                                model.setSMobile(sareainfo.getCompanyTel1());
                            }
                        }
                    } else if (Objects.nonNull(sareainfo)) {
                        model.setSName(sareainfo.getPrintName());
                        model.setSMobile(sareainfo.getCompanyTel1());
                    }
                }
            }
            //预约单 物流信息填充
        } else if (queryAddInfoFlag && !Objects.equals(0, Optional.ofNullable(model.getShouHouId()).orElse(0))) {
            ShouHouAddInfoDTO shouhouAddInfo = shouHouYuYueService.getShouhouAddInfo(model.getShouHouId());
            if (Objects.nonNull(shouhouAddInfo)) {
                model.setDanHaoBind(model.getShouHouId());
                model.setDTime(LocalDateTime.now());
                model.setStats(0);
                model.setCom("");
                model.setPrice(BigDecimal.ZERO);
                model.setInPrice(BigDecimal.ZERO);

                model.setWuType(WuliuTypeEnum.AFTER_SERVICE.getCode());
                model.setRName(shouhouAddInfo.getRname());
                model.setRMobile(shouhouAddInfo.getRmobile());
                model.setRCityId(shouhouAddInfo.getRcityid());
                model.setRAddress(shouhouAddInfo.getRaddress());
                model.setSareaid(shouhouAddInfo.getSareaid());
                model.setSName(shouhouAddInfo.getSname());
                model.setSMobile(shouhouAddInfo.getSmobile());
                model.setSAddress(shouhouAddInfo.getSaddress());
            }
        } else if (queryAddInfoFlag && !Objects.equals(0, Optional.ofNullable(model.getYuyueId()).orElse(0))) {
            YuYueAddInfoDTO yuYueAddInfo = shouHouYuYueService.getYuYueAddInfo(model.getYuyueId());
            if (Objects.nonNull(yuYueAddInfo)) {
                model.setDanHaoBind(model.getYuyueId());
                model.setDTime(LocalDateTime.now());
                model.setStats(0);
                model.setCom("");
                model.setPrice(BigDecimal.ZERO);
                model.setInPrice(BigDecimal.ZERO);

                model.setWuType(WuliuTypeEnum.VISIT.getCode());
                model.setSareaid(yuYueAddInfo.getSareaid());
                model.setSName(yuYueAddInfo.getSname());
                model.setSMobile(yuYueAddInfo.getSmobile());
                model.setSAddress(yuYueAddInfo.getSaddress());
                model.setRName(yuYueAddInfo.getRname());
                model.setRMobile(yuYueAddInfo.getRmobile());
                model.setRCityId(yuYueAddInfo.getRcityid());
                model.setRAddress(yuYueAddInfo.getRaddress());
            }
        } else if (!Objects.equals(0, Optional.ofNullable(model.getYuyueId()).orElse(0)) || !Objects.equals(0, shouhouid)) {
            //预约单信息
            ShouHouYuYueDTO adds;
            // //预约单信息
            if (Objects.equals(shouhouid, 0)) {
                adds = shouHouYuYueBusService.getYuYue(model.getYuyueId());
            } else {
                adds = shouHouYuYueBusService.getYuYueAddInfOps(new ShouHouYuYueDTO().setShouhouId(shouhouid));
            }
            WuLiuDTO dto = wuLiuService.getMarkAbnormalWuLiu(model);
            //原先就有物流单
            if (Objects.nonNull(dto)) {

                model.setSName(dto.getSName());
                model.setSMobile(dto.getSMobile());
                model.setSAddress(dto.getSAddress());
                model.setSareaid(dto.getSAreaId());
                model.setSCityId(dto.getSCityId());
                model.setRName(dto.getRName());
                model.setRMobile(dto.getRMobile());
                model.setRAddress(dto.getRAddress());
                model.setRareaid(dto.getRAreaId());
                model.setRCityId(dto.getRCityId());
                model.setAreaId(dto.getAreaId());
                model.setDTime(dto.getDTime());
                model.setCTime(dto.getDTime());
                model.setWeight(dto.getWeight());
                model.setPrice(dto.getPrice());
                model.setInPrice(dto.getInPrice());
                model.setShouJianRen(dto.getShouJianRen());
                model.setPaiJianRen(dto.getPaiJianRen());
                model.setStats(dto.getStats());
                model.setDanHaoBind(dto.getDanHaoBind());
                model.setWuType(dto.getWuType());
                model.setComment(dto.getComment());
                model.setCom(dto.getCom());
                model.setNu(dto.getNu());
                model.setPayMethod(dto.getPayMethod());
                model.setWuliuid(dto.getId());
                model.setLinkType(String.valueOf(dto.getLinkType()));
                model.setWCateId(dto.getWCateId());
                model.setIsExceptionSub(dto.getIsAbnomal());
                model.setExceptionRemark(dto.getRemark());

            } else {
                //没有物流单，要生成显示

                model.setDanHaoBind(model.getYuyueId() > 0 ? model.getYuyueId() : shouhouid);

                model.setDTime(LocalDateTime.now());
                model.setStats(0);
                model.setCom("");
                model.setPrice(BigDecimal.ZERO);
                model.setInPrice(BigDecimal.ZERO);
                //上门取件
                if (Arrays.asList(WuLiuLinkTypeEnum.DOOR_TO_DOOR_PICK_UP.getCode(), NumUtil.FOUR)
                        .contains(Integer.valueOf(Optional.ofNullable(model.getLinkType()).orElse("0")))) {
                    model.setWuType(NumUtil.SEVEN);
                    model.setSName(adds.getReciver1());
                    model.setSMobile(adds.getMobile());
                    model.setSCityId(adds.getCityid1());
                    model.setSAddress(adds.getAddress1());
                    model.setRareaid(model.getSessionAreaId());
                    model.setShouJianRen(adds.getConsignee1());
                    //维修机派送
                } else if (Arrays.asList(WuLiuLinkTypeEnum.DELIVERY_DOOR_TO_DOOR.getCode(), WuLiuLinkTypeEnum.REPAIR_PICK_UP.getCode())
                        .contains(Integer.valueOf(Optional.ofNullable(model.getLinkType()).orElse("0")))) {
                    model.setWuType(NumUtil.FIVE);
                    model.setRName(adds.getReciver2());
                    model.setRMobile(adds.getMobile());
                    model.setRCityId(adds.getCityid2());
                    model.setRAddress(adds.getAddress2());
                    model.setSareaid(model.getSessionAreaId());
                    model.setSName(adds.getConsignee2());
                }
            }
            //(普通)新增物流单
        } else {
            model.setDTime(LocalDateTime.now());
            model.setAreaId(model.getSessionAreaId());
            model.setSareaid(model.getAreaId());
            model.setDanHaoBind(0);
            model.setStats(0);
            model.setCom("");
            model.setPrice(BigDecimal.ZERO);
            model.setInPrice(BigDecimal.ZERO);
            Areainfo areaInfo = areaInfoService.getAreaInfoByAreaId2(model.getAreaId());
            if (areaInfo != null) {
                model.setSpid(areaInfo.getPid());
                model.setRpid(areaInfo.getPid());
                model.setSzid(areaInfo.getZid());
                model.setRzid(areaInfo.getZid());
                model.setSDid(areaInfo.getDid());
                model.setRDid(areaInfo.getDid());
            }

            Integer payMethod = wuLiuService.queryPayMethodByAreaId(model.getSessionAreaId());
            if (payMethod > 0) {
                model.setPayMethod(payMethod);
            }

        }

        if (!Objects.equals(model.getSCityId(), 0) && Objects.equals(model.getSpid(), 0)) {
            CityIdListDTO saddrInfo = wuLiuService.getAreaIdByCityId(model.getSCityId(), null);
            model.setSpid(saddrInfo.getPid());
            model.setSzid(saddrInfo.getZid());
            model.setSDid(saddrInfo.getDid());
        }
        if (!Objects.equals(model.getRCityId(), 0) && Objects.equals(model.getRpid(), 0)) {
            CityIdListDTO raddrInfo = wuLiuService.getAreaIdByCityId(model.getRCityId(), null);
            model.setRpid(raddrInfo.getPid());
            model.setRzid(raddrInfo.getZid());
            model.setRDid(raddrInfo.getDid());
        }

        //内部物流 查询收货地区 店面地址
        if (Optional.ofNullable(model.getWuliuid()).orElse(0) > 0 && Objects.equals(NumUtil.ONE, model.getWuType())
                && StringUtils.isBlank(model.getRAddress())) {
            Areainfo areaOne = Optional.ofNullable(areaInfoService.getAreaInfoByAreaId2(model.getRareaid())).orElseGet(Areainfo::new);
            model.setRAddress(areaOne.getCompanyAddress());
        }

        if (!Objects.equals(Optional.ofNullable(model.getWuliuid()).orElse(0), 0)) {
            getLogs(model);
            model.setIsShowWuliuClaim(isShowWuliuClaim(model.getWuliuid(), currentUser));
        } else {
            if (Objects.equals(Optional.ofNullable(model.getSubId()).orElse(0), 0) && StringUtils.isBlank(model.getActionName())) {
                model.setIsCreateManually(true);
            }
        }

        if (Objects.equals(shouhouid, NumUtil.ZERO)) {
            if (!Objects.equals(model.getYuyueId(), NumUtil.ZERO)) {
                //预约单没有传过售后id来
                shouhouid = getShIdbyYyId(model.getYuyueId());
            } else if (Arrays.asList("2", "3").contains(model.getLinkType()) && !Objects.equals(model.getDanHaoBind(), NumUtil.ZERO)) {  //预约ID已绑定到wuliu表
                shouhouid = getShIdbyYyId(model.getDanHaoBind());
            } else if (Arrays.asList("4", "5").contains(model.getLinkType()) && !Objects.equals(model.getDanHaoBind(), NumUtil.ZERO)) {
                shouhouid = model.getDanHaoBind();
            }
        }
        boolean bool = StringUtils.isBlank(model.getSName()) || StringUtils.isBlank(model.getSMobile());
        if (StringUtils.isBlank(model.getNu())
                && bool
                && (Arrays.asList(WuLiuConstant.SHUNFENG, WuLiuConstant.ZHONGTONG).contains(model.getCom()))) {
            Map<String, String> sender = getDefaultSenderInfo(model.getSareaid());
            if (MapUtils.isNotEmpty(sender)) {
                model.setSName(sender.get("snane"));
                model.setSMobile(sender.get("smobile"));
            }
        }

        if (model.getWCateId() == null) {
            model.setWCateId(8); //默认其他
        }

        model.setShouHouId(Optional.ofNullable(shouhouid).orElse(0));

        if (Arrays.asList(WuLiuConstant.JINGDONG, WuLiuConstant.JINGDONG_JIUJI).contains(model.getCom())) {
            Integer jdDropMenuExpressType = wuLiuExpressExtendService.getJdDropMenuExpressType(model.getWuliuid());
            model.setJiujiJdExpressType(jdDropMenuExpressType.toString());
            model.setExpressType(jdDropMenuExpressType.toString());
        }
        if (Arrays.asList(LogisticsExpressTypeEnum.DEP_PON.getCode(),LogisticsExpressTypeEnum.DEP_PON_9JI.getCode()).contains(model.getCom())) {
            Integer dropMenuExpressType = wuLiuExpressExtendService.getJdDropMenuExpressType(model.getWuliuid());
            model.setExpressType(dropMenuExpressType.toString());
        }

        boolean deliveryFlag = Objects.isNull(model.getDelivery()) && !Objects.equals(0,Optional.ofNullable(model.getDanHaoBind()).orElse(0));
        WuLiuSubDTO wuLiuSub = null;
        if (deliveryFlag && (WuLiuTypeEnum.ORDER.getCode().equals(model.getWuType()) || WuLiuTypeEnum.ORDER_EXPRESS.getCode().equals(model.getWuType()))) {
            wuLiuSub = wuLiuService.getWuLiuSub(model.getDanHaoBind());
            //订单异常锁定标记
            model.setIsLock(Boolean.TRUE.equals(RedisUtils.hashHasKey(RedisKeys.EXCEPTION_SUB_REDISKEY, Convert.toStr(model.getDanHaoBind()))) ? 1 : 0);

        } else if (deliveryFlag && WuLiuTypeEnum.FOURTEEN_DAY.getCode().equals(model.getWuType())) {
            wuLiuSub = wuLiuService.getWuLiuReSub(model.getDanHaoBind());
            //良品单异常锁定标记
            model.setIsLock(Boolean.TRUE.equals(RedisUtils.hashHasKey(RedisKeys.EXCEPTION_LPRECOVER_SUB_REDISKEY, Convert.toStr(model.getDanHaoBind()))) ? 1 : 0);

        }
        if (Objects.nonNull(wuLiuSub)) {
            model.setDelivery(wuLiuSub.getDelivery());
            model.setDeliveryName(SubDeliveryEnum.getMessageByCode(wuLiuSub.getDelivery()));
            //订单取消标记
            model.setCancelApplication(wuLiuSub.getCancelApplication());
            //第三方派送 设置默认快递方式
            if (DeliveryEnum.THIRD_TRANSFER.getCode().equals(wuLiuSub.getDelivery())) {
                setWuliuComInfo(model);
            }
        }
        //快递单号为空增加线上国补订单标识
        if (StrUtil.isBlank(model.getNu())
                && CommonUtil.isNotNullZero(model.getDanHaoBind())
                && (WuLiuTypeEnum.ORDER.getCode().equals(model.getWuType()) || WuLiuTypeEnum.ORDER_EXPRESS.getCode().equals(model.getWuType()))) {
            //线上国补订单校验
            List<NationalSupplementKindRes> nationalSupplementKindList = SpringUtil.getBean(NationalSupplementService.class).getNationalSupplementKindList(Collections.singletonList(Convert.toStr(model.getDanHaoBind())));
            if (CollUtil.isNotEmpty(nationalSupplementKindList)) {
                model.setOnlineNationalSupplement(1);
            }
        }

        if (!Objects.equals(0, Optional.ofNullable(model.getWuliuid()).orElse(0))) {
            WuliuExtendInfo wuliuExtendInfo = wuliuExtendInfoService.queryWuliuExtendInfoByWuliuId(model.getWuliuid());
            if (Objects.nonNull(wuliuExtendInfo)) {
                model.setDistance(wuliuExtendInfo.getDistance());
                model.setDistributionCost(wuliuExtendInfo.getDistributionCost());
                String sendPosition = Optional.ofNullable(wuliuExtendInfo.getSendPosition())
                        .map(v -> CoordinateUtil.wgs2gcj(new Coordinate(v))).map(Coordinate::toString).orElse("");
                String receivePosition = Optional.ofNullable(wuliuExtendInfo.getReceivePosition())
                        .map(v -> CoordinateUtil.wgs2gcj(new Coordinate(v))).map(Coordinate::toString).orElse("");
                model.setSendPosition(sendPosition);
                model.setReceivePosition(receivePosition);
                model.setSdetailedAddress(wuliuExtendInfo.getSdetailedAddress());
                model.setRdetailedAddress(wuliuExtendInfo.getRdetailedAddress());
            }
        }
        model.setNoCheckSub(false);
        if (CommonUtil.isNotNullZero(model.getWuliuid())
                && WuliuTypeEnum.INNER.getCode().equals(model.getWuType())) {
            model.setNoCheckSub(wuLiuService.getInnerWuliuCount(model.getWuliuid()) > 0);
        }
        model.setSendAreaName(areaInfoService.getAreaNameById(model.getSareaid()));
        model.setReceiverAreaName(areaInfoService.getAreaNameById(model.getRareaid()));

        WuliuPaijianInfoEntry paiJian = wuliuPaijianInfoService.getPaiJianInfoByWuliuId(model.getWuliuid(),model.getPaiJianRen());
        model.setPaiJain(paiJian);
        return model;
    }
    /**
     * 物料申请完成
     *
     * @param wuliuId
     * @param userName
     * @return
     */
    @Override
    @DS("ch999oanew")
    public boolean completeMaterialApply(Integer wuliuId, String userName, Integer type) {
        try {
            WuLiuEntity wuLiuEntity = wuLiuService.getById(wuliuId);
            if(Objects.isNull(wuLiuEntity)) {
                throw new CustomizeException("物流单不存在");
            }
            //物流单签收
            if (Objects.equals(1, type) || Objects.equals(WuliuStatusEnum.RECEIVED.getCode(), wuLiuEntity.getStats()) || Objects.equals(WuliuStatusEnum.COMPLETE.getCode(), wuLiuEntity.getStats())) {
                //完成物料制作申请订单
                if (Objects.equals(WuLiuTypeEnum.ACCESSORY.getCode(), wuLiuEntity.getWuType())
                        && Objects.equals(WuLiuCateEnum.YINGXIAOWULIAO_DIAOBO.getCode(), wuLiuEntity.getWCateId())) {
                    R<Boolean> booleanR = materialApplyCloud.updateMaterialApplyToFinished(wuLiuEntity.getDanHaoBind(), userName);
                    log.info("远程调用物料申请完成操作，applyId={},userName={},result={}", wuLiuEntity.getDanHaoBind(), userName, JSONUtil.toJsonStr(booleanR));
                    return Objects.nonNull(booleanR) && Objects.equals(0, booleanR.getCode());
                }
            }

        } catch (Exception e) {
            log.error("物料申请完成操作失败param={}", wuliuId, e);
        }
        return false;
    }

    /**
     * 查询物流单
     *
     * @param req
     * @param currentUser
     * @return
     */
    @Override
    @DS("ch999oanew")
    public WuLiuInfoReqVO getWuLiuInfoV2(WuLiuInfoReqVO req, OaUserBO currentUser) {
        WuLiuInfoReqVO wuLiuInfo = this.getWuLiuInfo(req, currentUser);
        CityIdListDTO sCityDto = wuLiuService.getAreaIdByCityId(wuLiuInfo.getSCityId(), 1);
        CityIdListDTO rCityDto = wuLiuService.getAreaIdByCityId(wuLiuInfo.getRCityId(), 1);
        wuLiuInfo.setSendCityName(Objects.nonNull(sCityDto) ? Optional.ofNullable(sCityDto.getPname()).orElse("")
                + Optional.ofNullable(sCityDto.getZname()).orElse("")
                + Optional.ofNullable(sCityDto.getDname()).orElse("") : "");
        wuLiuInfo.setReceiverCityName(Objects.nonNull(rCityDto) ? Optional.ofNullable(rCityDto.getPname()).orElse("")
                + Optional.ofNullable(rCityDto.getZname()).orElse("")
                + Optional.ofNullable(rCityDto.getDname()).orElse("") : "");
        //wuLiuInfo.setSendAreaName(areaInfoService.getAreaNameById(wuLiuInfo.getSareaid()))
        //wuLiuInfo.setReceiverAreaName(areaInfoService.getAreaNameById(wuLiuInfo.getRareaid()))
        if (WuLiuTypeEnum.ORDER.getCode().equals(wuLiuInfo.getWuType())
                || WuLiuTypeEnum.ORDER_EXPRESS.getCode().equals(wuLiuInfo.getWuType())) {
            wuLiuInfo.setSubKinds(1);
        } else if (WuLiuTypeEnum.FOURTEEN_DAY.getCode().equals(wuLiuInfo.getWuType())) {
            wuLiuInfo.setSubKinds(2);
        }
        return wuLiuInfo;
    }

    /**
     * 创建物流单
     *
     * @param vo
     * @return
     */
    @Override
    @DS("oanewWrite")
    @Transactional(rollbackFor = Exception.class)
    public R<WuLiuAddResV2VO> add(WuLiuAddReqV2VO vo) {
        log.info("添加物流单vo={}", JacksonJsonUtils.toJson(vo));
        OaUserBO user = SysUtils.getUser();
        if (Objects.isNull(user)) {
            throw new CustomizeException("当前登录信息失效，请登录");
        }
        WuLiuEntity wuLiuEntity = wuLiuMapStruct.toWuLiuEntity(vo);
        wuLiuEntity.setAreaId(user.getAreaId());
        wuLiuEntity.setDTime(LocalDateTime.now());
        wuLiuEntity.setInUser(user.getUserName());
        boolean saveFlag = wuLiuService.save(wuLiuEntity);
        if (saveFlag) {
            wuLiuLogsService.addOne(wuLiuEntity.getId(), user.getUserName(), "创建物流单成功");
        }

        if (StringUtils.isNotBlank(vo.getCom()) && StringUtils.isBlank(vo.getNu())) {
            WuliuExpressBO wuliuExpress = new WuliuExpressBO();
            wuliuExpress.setExpressType(vo.getExpressType());
            wuliuExpress.setWuliu(wuLiuEntity);
            wuliuExpress.setMonthlyCard(vo.getMonthlyCard());
            wuliuExpress.setWeight(vo.getWeight());
            wuliuExpress.setVloumn(vo.getVloumn());
            wuliuExpress.setPackageCount(vo.getPackageCount());
            wuliuExpress.setIsDocall(vo.getIsDocall());
            wuliuExpress.setSendStartTime(vo.getSendStartTime());
            wuliuExpress.setInUser(user.getUserName());

            //京东九机特惠默认标快
            if (StringUtils.isBlank(vo.getExpressType()) && Arrays.asList(WuLiuConstant.JINGDONG, WuLiuConstant.JINGDONG_JIUJI).contains(vo.getCom())) {
                wuliuExpress.setExpressType("1");
            }
            if (SysUtils.isDev() || SysUtils.isJiuJiProd()) {
                try {
                    GenerateMoreWuliuNoRes generateMoreWuliuNoRes = wuliuExpressNuService.generateMoreWuliuNo(wuliuExpress);
                    String nu = Optional.ofNullable(generateMoreWuliuNoRes).map(GenerateMoreWuliuNoRes::getNu).orElse("");
                    WuLiuEntity wuliu = new WuLiuEntity();
                    wuliu.setId(wuLiuEntity.getId());
                    wuliu.setNu(nu);
                    wuLiuService.updateById(wuliu);
                } catch (Exception e) {
                    log.error("创建快递失败", e);
                }
            }
        }
        WuLiuAddResV2VO res = new WuLiuAddResV2VO();
        res.setWuliuId(wuLiuEntity.getId());
        return R.success(res);
    }

    /**
     * 更新物流单
     *
     * @param user
     * @param vo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("oanewWrite")
    public R<String> updateWuliuV2(OaUserBO user, WuLiuUpdateReqV2 vo) {
        log.info("修改物流单信息V2，参数：{}",JSONUtil.toJsonStr(vo));
        WuLiuEntity wuLiuEntity = wuLiuMapStruct.toWuLiuEntity(vo);
        WuLiuEntity oldWuliu = wuLiuService.getById(vo.getWuliuId());
        if (Objects.isNull(oldWuliu)) {
            return R.error("物流单号不存在");
        }
        /*if (!Arrays.asList(WuLiuTypeEnum.ORDER.getCode(),WuLiuTypeEnum.ORDER_EXPRESS.getCode(),
            WuLiuTypeEnum.FOURTEEN_DAY.getCode(),WuLiuTypeEnum.AFTER_SERVICE.getCode(),WuLiuTypeEnum.VISIT.getCode()).contains(oldWuliu.getWuType())) {
            return R.error("物流单修改失败，不支持修改" + WuLiuTypeEnum.getMessage(oldWuliu.getWuType()) +"物流单");
        }*/
        if (WuliuStatusEnum.COMPLETE.getCode().equals(oldWuliu.getStats())) {
            return R.error("物流单已经完成,不能修改");
        }
        if (WuliuStatusEnum.INVALID.getCode().equals(oldWuliu.getStats())) {
            wuLiuLogsService.addOne(oldWuliu.getId(), user.getUserName(), "业务单据调用物流单修改失败，物流单已作废");
            return R.success("操作成功");
        }
        if (StringUtils.isNotBlank(oldWuliu.getNu())) {
            return R.error("订单对应的物流单（"+vo.getWuliuId()+"）已生成快递单号，如需修改地址或门店请先取消快递单");
        }
        if (Arrays.asList(WuliuStatusEnum.DELIVERING.getCode(),WuliuStatusEnum.RECEIVED.getCode()).contains(oldWuliu.getStats())) {
            return R.error("配送员已发出，不允许修改，需对接送货员处理");
        }
        String comment = getUpdateWuliuLogComment(wuLiuEntity, oldWuliu);
        if (StringUtils.isBlank(comment)) {
            return R.success("操作成功");
        }
        boolean b = wuLiuService.updateById(wuLiuEntity);
        if (b) {
            wuLiuLogsService.addOne(oldWuliu.getId(), user.getUserName(), comment);
        }
        return R.success("操作成功");
    }


    /**
     * 更新物流单
     *
     * @param vo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("oanewWrite")
    public R<Boolean> updateWuliuV3(WuLiuUpdateReqV3 vo) {
        R<Boolean> success = R.success("操作成功");
        success.setData(true);
        WuLiuEntity wuLiuEntity = wuLiuMapStruct.toWuLiuEntity(vo);
        WuLiuEntity oldWuliu = wuLiuService.getById(vo.getWuliuId());
        if (Objects.isNull(oldWuliu)) {
            success.setData(false);
            success.setUserMsg("物流单号不存在");
            return success;
        }
        if (WuliuStatusEnum.COMPLETE.getCode().equals(oldWuliu.getStats())) {
            success.setData(false);
            success.setUserMsg("物流单已经完成,不能修改");
            return success;
        }
        if (WuliuStatusEnum.INVALID.getCode().equals(oldWuliu.getStats())) {
            wuLiuLogsService.addOne(oldWuliu.getId(), vo.getUserName(), "业务单据调用物流单修改失败，物流单已作废");
            return success;
        }
        if (StringUtils.isNotBlank(oldWuliu.getNu())) {
            success.setData(false);
            success.setUserMsg("订单对应的物流单（"+vo.getWuliuId()+"）已生成快递单号，如需修改地址或门店请先取消快递单");
            return success;

        }
        if (Arrays.asList(WuliuStatusEnum.DELIVERING.getCode(),WuliuStatusEnum.RECEIVED.getCode()).contains(oldWuliu.getStats())) {
            success.setData(false);
            success.setUserMsg("配送员已发出，不允许修改，需对接送货员处理");
            return success;
        }
        String comment = getUpdateWuliuLogComment(wuLiuEntity, oldWuliu);
        if (StringUtils.isBlank(comment)) {
            success.setData(true);
            return R.success("操作成功");
        }
        boolean b = wuLiuService.updateById(wuLiuEntity);
        if (b) {
            wuLiuLogsService.addOne(oldWuliu.getId(), vo.getUserName(), comment);
        }
        success.setData(true);
        return R.success("操作成功");
    }

    /**
     * 获取物流单关联单据寄件收件信息
     *
     * @param vo
     * @return
     */
    @Override
    public WuliuAddressInfoRes getWuliuAddressInfo(WuliuAddressInfoReq vo) {
        WuliuTypeEnum wuliuTypeEnum = WuliuTypeEnum.getWuliuTypeEnumByCode(vo.getWuType());
        if (Objects.isNull(wuliuTypeEnum)) {
            throw new CustomizeException("物流单类型错误");
        }
        WuLiuEntity wuliu = wuLiuService.getWuLiuByWuTypeAndDanhaobind(vo.getWuType(), vo.getDanhaobind());
        if (Objects.nonNull(wuliu)) {
            throw new CustomizeException("该订单已存在关联的物流单（"+wuliu.getId()+"），请核对");
        }
        WuliuAddressInfoRes result = new WuliuAddressInfoRes();
        switch (wuliuTypeEnum) {
            //订单
            case ORDER:
            case ORDER_EXPRESS:
                WuLiuSubDTO wuLiuSub = wuLiuService.getWuLiuSub(vo.getDanhaobind());
                if (Objects.isNull(wuLiuSub)) {
                    throw new CustomizeException("未查询到订单信息");
                }
                if (SubDeliveryEnum.SELF.getCode().equals(wuLiuSub.getDelivery())) {
                    throw new CustomizeException("订单为到店自取，请修改订单配送方式再添加物流单");
                }
                if (!SubCheckEnum.IN_PROGRESS_CHECK.contains(wuLiuSub.getSubCheck())) {
                    throw new CustomizeException("只有进行中的订单可以添加物流单，请核对");
                }
                //线上国补订单标识
                List<NationalSupplementKindRes> nationalSupplementKindList = SpringUtil.getBean(NationalSupplementService.class).getNationalSupplementKindList(Collections.singletonList(Convert.toStr(wuLiuSub.getSubId())));
                if (CollUtil.isNotEmpty(nationalSupplementKindList)) {
                    result.setOnlineNationalSupplement(1);
                }
                result.setDelivery(wuLiuSub.getDelivery());
                result.setDeliveryName(SubDeliveryEnum.getMessageByCode(wuLiuSub.getDelivery()));
                result.setSareaid(wuLiuSub.getAreaid());
                result.setRname(wuLiuSub.getSubTo());
                result.setRmobile(wuLiuSub.getSubMobile());
                result.setRaddress(wuLiuSub.getSubAdds());
                result.setRcityid(wuLiuSub.getCityid());
                if (Objects.equals(NumUtil.THREE, wuLiuSub.getDelivery()) && Objects.nonNull(wuLiuSub.getZitidianId())) {
                    ZiTiDianDTO ziTiDian = wuLiuService.getZiTiDian(wuLiuSub.getZitidianId());
                    if (Objects.nonNull(ziTiDian)) {
                        result.setRname(ziTiDian.getReceiver());
                        result.setRmobile(ziTiDian.getMobile());
                        result.setRaddress(ziTiDian.getAddress());
                        result.setRcityid(ziTiDian.getCityid1());
                    }
                }
                WuLiuEntity wuLiu = wuLiuService.getWuLiuByWuType(result.getSareaid(), vo.getWuType());
                if (Objects.nonNull(wuLiu)) {
                    result.setSname(wuLiu.getSName());
                    result.setSmobile(wuLiu.getSMobile());
                }
                setWuliuSendInfo(result);
                if (SubDeliveryEnum.THIRD_PARTY.getCode().equals(wuLiuSub.getDelivery())) {
                    setWuliuComInfo(result, vo);
                }
                break;
            //良品单
            case FOURTEEN_DAY:
                //获取良品订单信息
                WuLiuSubDTO wuLiuReSub = wuLiuService.getWuLiuReSub(vo.getDanhaobind());
                if (Objects.isNull(wuLiuReSub)) {
                    throw new CustomizeException("未查询到良品订单信息");
                }
                if (SubDeliveryEnum.SELF.getCode().equals(wuLiuReSub.getDelivery())) {
                    throw new CustomizeException("订单为到店自取，请修改订单配送方式再添加物流单");
                }
                if (!SubCheckEnum.IN_PROGRESS_CHECK.contains(wuLiuReSub.getSubCheck())) {
                    throw new CustomizeException("只有进行中的订单可以添加物流单，请核对");
                }
                result.setDelivery(wuLiuReSub.getDelivery());
                result.setDeliveryName(SubDeliveryEnum.getMessageByCode(wuLiuReSub.getDelivery()));
                result.setSareaid(wuLiuReSub.getAreaid());
                result.setRname(wuLiuReSub.getSubTo());
                result.setRmobile(wuLiuReSub.getSubMobile());
                result.setRaddress(wuLiuReSub.getSubAdds());
                result.setRcityid(wuLiuReSub.getCityid());
                if (Objects.equals(NumUtil.ONE, wuLiuReSub.getSaleType()) && WuLiuConstant.AREA_H2.equals(result.getSareaid())) {
                    result.setSaddress("贵州省贵阳市南明区花果园国际金融街6号25楼(出电梯左边)");
                    result.setScityid(520102);
                } else {
                    result.setSaddress("云南省昆明市五华区学府路690号金鼎科技园三号楼");
                    result.setScityid(530102);
                }
                if (Objects.equals(NumUtil.ONE, wuLiuReSub.getSaleType())) {
                    QuDaoShippingDTO daoShipping = qudaoContactssService.getQuDaoShipping(vo.getDanhaobind());
                    if (Objects.nonNull(daoShipping)) {
                        result.setRname(daoShipping.getCwFzr());
                        result.setRmobile(daoShipping.getCwLxfs());
                        result.setRaddress(daoShipping.getShippingAddress());
                        result.setRcityid(daoShipping.getCityId());
                    }
                } else if (Objects.equals(NumUtil.THREE, wuLiuReSub.getDelivery()) && Objects.nonNull(wuLiuReSub.getZitidianId())) {
                    ZiTiDianDTO ziTiDian = wuLiuService.getZiTiDian(wuLiuReSub.getZitidianId());
                    if (Objects.nonNull(ziTiDian)) {
                        result.setRname(ziTiDian.getReceiver());
                        result.setRmobile(ziTiDian.getMobile());
                        result.setRaddress(ziTiDian.getAddress());
                        result.setRcityid(ziTiDian.getCityid1());
                    }
                }
                WuLiuEntity wuLiuEntity = wuLiuService.getWuLiuByWuType(result.getSareaid(), vo.getWuType());
                if (Objects.nonNull(wuLiuEntity)) {
                    result.setSname(wuLiuEntity.getSName());
                    result.setSmobile(wuLiuEntity.getSMobile());
                }
                setWuliuSendInfo(result);
                if (SubDeliveryEnum.THIRD_PARTY.getCode().equals(wuLiuReSub.getDelivery())) {
                    setWuliuComInfo(result, vo);
                }
                break;
            case AFTER_SERVICE:
                ShouHouAddInfoDTO shouhouAddInfo = shouHouYuYueService.getShouhouAddInfo(vo.getDanhaobind());
                if (Objects.isNull(shouhouAddInfo)) {
                    throw new CustomizeException("未查询到维修订单");
                }
                if (StringUtils.isBlank(shouhouAddInfo.getRaddress())) {
                    throw new CustomizeException("维修单预约信息未选择送货上门，请选择后再添加物流单");
                }
                if (!Arrays.asList(WxStatusEnum.YXH.getCode(),WxStatusEnum.XBH.getCode()).contains(shouhouAddInfo.getStats())) {
                    throw new CustomizeException("只有已修好或修不好的维修单才可以添加物流单，请核对");
                }

                result = wuLiuMapStruct.toWuliuAddressInfo(shouhouAddInfo);
                if (Objects.nonNull(result.getScityid())) {
                    CityIdListDTO scity = wuLiuService.getAreaIdByCityId(result.getScityid(), 1);
                    result.setSpid(scity.getPid());
                    result.setSzid(scity.getZid());
                    result.setSdid(scity.getDid());
                }
                if (Objects.nonNull(result.getRcityid())) {
                    CityIdListDTO scity = wuLiuService.getAreaIdByCityId(result.getRcityid(), 1);
                    result.setRpid(scity.getPid());
                    result.setRzid(scity.getZid());
                    result.setRdid(scity.getDid());
                }
                break;
            case VISIT:
                YuYueAddInfoDTO yuYueAddInfo = shouHouYuYueService.getYuYueAddInfo(vo.getDanhaobind());
                if (Objects.isNull(yuYueAddInfo)) {
                    throw new CustomizeException("未查询到上门订单");
                }
                if (!Objects.equals(YuyueStatusEnum.YWQR.getCode(), yuYueAddInfo.getStats())) {
                    throw new CustomizeException("只有预约单状态为业务确认才可以添加物流单，请核对");
                }
                if (!Objects.equals(YuYueSTypeEnum.SMQJ.getCode(), yuYueAddInfo.getStype())) {
                    throw new CustomizeException("预约单为"+YuYueSTypeEnum.getMessage(yuYueAddInfo.getStype())+"，请修改为上门取件再添加物流单");
                }

                result = wuLiuMapStruct.toWuliuAddressInfo(yuYueAddInfo);
                if (Objects.nonNull(result.getScityid())) {
                    CityIdListDTO scity = wuLiuService.getAreaIdByCityId(result.getScityid(), 1);
                    result.setSpid(scity.getPid());
                    result.setSzid(scity.getZid());
                    result.setSdid(scity.getDid());
                }
                if (Objects.nonNull(result.getRcityid())) {
                    CityIdListDTO scity = wuLiuService.getAreaIdByCityId(result.getRcityid(), 1);
                    result.setRpid(scity.getPid());
                    result.setRzid(scity.getZid());
                    result.setRdid(scity.getDid());
                }
                break;
            default:
                break;
        }
        return result;
    }

    /**
     * 查询物流单关联订单、良品单规定送达时间
     *
     * @param wuliuId
     * @return
     */
    @Override
    public WuliuAdvanceDeliveryRes getWuliuAdvanceDelivery(Integer wuliuId,Integer wutype,Integer danhaobind) {
        WuliuAdvanceDeliveryRes res = new WuliuAdvanceDeliveryRes();
        res.setHasAdvanceDelivery(false);
        WuLiuEntity wuLiuEntity = new WuLiuEntity();
        wuLiuEntity.setWuType(wutype);
        wuLiuEntity.setDanHaoBind(danhaobind);
        if (Objects.nonNull(wuliuId) && !Objects.equals(0,wuliuId)) {
            wuLiuEntity = wuLiuService.getById(wuliuId);
            if (Objects.isNull(wuLiuEntity) || Objects.isNull(wuLiuEntity.getDanHaoBind())) {
                log.info("订单、良品单提前配送提示，物流单或绑定的单号为空，wuliuId={}", wuliuId);
                return res;
            }
        }
        SubExpectTimeDTO subExpectTimeDto = null;
        String msg = "";
        if (WuLiuTypeEnum.ORDER.getCode().equals(wuLiuEntity.getWuType()) || WuLiuTypeEnum.ORDER_EXPRESS.getCode().equals(wuLiuEntity.getWuType())) {
            subExpectTimeDto = wuLiuSubService.getSubExpectTimeBySubId(wuLiuEntity.getDanHaoBind());
            msg = "销售单";
        } else if (WuLiuTypeEnum.FOURTEEN_DAY.getCode().equals(wuLiuEntity.getWuType())) {
            subExpectTimeDto = recoverMarketinfoService.getSubExpectTimeBySubId(wuLiuEntity.getDanHaoBind());
            msg = "良品单";
        }
        log.info("订单、良品单提前配送提示,wuliuId={},subExpectTimeDto={}", wuliuId, subExpectTimeDto);
        if (Objects.isNull(subExpectTimeDto) || Objects.isNull(subExpectTimeDto.getExpectTime()) || Objects.isNull(subExpectTimeDto.getUserTime())) {
            return res;
        }
        //到店自取，快递运输
        List<Integer> deliveryList = Arrays.asList(DeliveryEnum.AREA_SELF_PICK.getCode(), DeliveryEnum.EXPRESS_TRANSFER.getCode());
        if (subExpectTimeDto.getUserTime() > 0 && Objects.nonNull(subExpectTimeDto.getDelivery()) && !deliveryList.contains(subExpectTimeDto.getDelivery())) {
            LocalDateTime now = LocalDateTime.now();
            long advanceDeliveryhours = Duration.between(now, subExpectTimeDto.getExpectTime()).toHours();
            if (advanceDeliveryhours >= NumUtil.TWO) {
                long advanceDeliveryMinutes = Duration.between(now, subExpectTimeDto.getExpectTime()).toMinutes();
                advanceDeliveryMinutes = advanceDeliveryMinutes - advanceDeliveryhours * NumUtil.SIXTY;
                res.setHasAdvanceDelivery(true);
                String advanceDeliveryMsg = String.format(WuLiuConstant.WULIU_ADVANCE_DELIVERY_MSG, msg, subExpectTimeDto.getSubId(), advanceDeliveryhours, advanceDeliveryMinutes);
                res.setAdvanceDeliveryMsg(advanceDeliveryMsg);
                res.setExpectTime(subExpectTimeDto.getExpectTime());
            }
        }
        return res;
    }

    /**
     * 作废物流单
     *
     * @param wuliuInvalid
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("oanewWrite")
    public void wuliuInvalid(WuliuInvalidBO wuliuInvalid) {
        WuLiuEntity wuliu = wuLiuService.getById(wuliuInvalid.getWuliuId());
        if (Objects.isNull(wuliu)) {
            log.warn("作废物流单失败，物流单不存在wuliuInvalid={}",JSONUtil.toJsonStr(wuliuInvalid));
            return;
        }
        //物流单状态为等待取货或者等待派送
        if (!Arrays.asList(WuliuStatusEnum.WAITING_GETTING_GOODS.getCode(),
                WuliuStatusEnum.WAITING_DELIVERY.getCode()).contains(wuliu.getStats())) {
            log.warn("作废物流单失败，物流单状态不是等待取货或者等待派送wuliuInvalid={}",JSONUtil.toJsonStr(wuliuInvalid));
            return;
        }

        OaUserBO bo = new OaUserBO();
        bo.setUserName(wuliuInvalid.getUserName());
        bo.setXTenant(0);
        if (StringUtils.isNotBlank(wuliu.getCom()) && StringUtils.isNotBlank(wuliu.getNu())) {
            String com = wuliu.getCom();
            R<String> r = null;
            //达达，中通取消
            if (WuLiuConstant.DADA.equals(com) || WuLiuConstant.ZHONGTONG.equals(com)) {
                com.jiuji.oa.stock.logistics.order.vo.CancelOrderReq cancelOrderReq = new com.jiuji.oa.stock.logistics.order.vo.CancelOrderReq();
                cancelOrderReq.setDeliveryId(wuliu.getId());
                cancelOrderReq.setExpressType(com);
                cancelOrderReq.setWaybillCode(wuliu.getNu());
                r = wuliuOrderContext.getStrategyType(com).cancelOrder(cancelOrderReq, bo);
            } else {
                CancelOrderReqV2 req = new CancelOrderReqV2();
                req.setDeliveryId(wuliu.getId());
                req.setExpressType(wuliu.getCom());
                req.setWaybillCode(wuliu.getNu());
                r = cancelOrderService.cancelOrder(bo, req);
            }
            //取消失败
            if (Objects.isNull(r) || !Objects.equals(0,r.getCode())) {
                log.warn("作废物流单失败，取消快递失败wuliuInvalid={}",JSONUtil.toJsonStr(wuliuInvalid));
                return;
            }
        }
        //作废物流单
        boolean updateFlag = wuLiuService.lambdaUpdate()
                .set(WuLiuEntity::getStats, WuliuStatusEnum.INVALID.getCode())
                .eq(WuLiuEntity::getId, wuliu.getId())
                .in(WuLiuEntity::getStats, Arrays.asList(WuliuStatusEnum.WAITING_GETTING_GOODS.getCode(),
                        WuliuStatusEnum.WAITING_DELIVERY.getCode()))
                .update();
        if (updateFlag) {
            wuLiuLogsService.addOne(wuliu.getId(), bo.getUserName(), "订单已取消，物流单自动作废");
        }
    }

    /**
     * 终止物流单
     *
     * @param req
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("oanewWrite")
    public boolean terminateWuliu(TerminateWuliuReq req) {
        boolean flag = wuLiuService.lambdaUpdate()
                .set(WuLiuEntity::getStats, WuliuStatusEnum.COMPLETE.getCode())
                .eq(WuLiuEntity::getId, req.getWuliuId())
                .eq(WuLiuEntity::getStats, WuliuStatusEnum.DELIVERING.getCode())
                .update();
        if (flag) {
            wuLiuLogsService.addOne(req.getWuliuId(), SysUtils.getUserName(), "派送单终止派送，物流单自动完成");
        }
        return flag;
    }

    /**
     * 计算物流单成本
     *
     * @param req
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("oanewWrite")
    @RepeatSubmitCheck(expression = "#{packageFullName}:#{methodSignName}:#{req.wuliuId}",message = "计算物流单骑行配送成本")
    public boolean calculateDistributionCost(WuliuDistributtionCostBO req) {
        WuLiuEntity wuLiuEntity = wuLiuService.getById(req.getWuliuId());
        if (Objects.isNull(wuLiuEntity)) {
            log.warn("计算物流单成本，物流单不存在req={}", req);
            return true;
        }
        //快递方式由第三方快递、美团、达达、uu改为其他快递，需清除骑行距离
        if (StringUtils.isNotBlank(wuLiuEntity.getCom()) && !Arrays.asList(LogisticsExpressTypeEnum.DA_DA.getCode(),LogisticsExpressTypeEnum.MEI_TUAN.getCode(),
                LogisticsExpressTypeEnum.UU_PAO_TUI.getCode(),LogisticsExpressTypeEnum.SFTC.getCode(),"paotui","jiujikuaisong").contains(wuLiuEntity.getCom())) {
            return wuliuExtendInfoService.clearInfoByWuLiuId(wuLiuEntity.getId());
        }

        //寄件经纬度
        Coordinate sendCoordinate = StringUtils.isNotBlank(req.getSendPosition()) ? CoordinateUtil.gcj2wgs(new Coordinate(req.getSendPosition())) : wuliuAddressService.getAreaCoordinateV2(wuLiuEntity.getSAreaId(), wuLiuEntity.getSAddress(), wuLiuEntity.getSCityId(), 1);
        //收件地址经纬度
        Coordinate receiveCoordinate = null;
        if (Arrays.asList(WuLiuTypeEnum.ORDER.getCode(),
                WuLiuTypeEnum.ORDER_EXPRESS.getCode(),
                WuLiuTypeEnum.FOURTEEN_DAY.getCode()).contains(wuLiuEntity.getWuType())) {
            String raddressPosition = WuliuAddressUtil.getSubPosition(Builder.of(SubPositionReq::new)
                    .with(SubPositionReq::setSubId, wuLiuEntity.getDanHaoBind())
                    .with(SubPositionReq::setWuliuId, wuLiuEntity.getId())
                    .with(SubPositionReq::setAddress, wuLiuEntity.getRAddress())
                    .with(SubPositionReq::setWuType, wuLiuEntity.getWuType())
                    .build());
            if (StringUtils.isNotBlank(raddressPosition)) {
                receiveCoordinate = new Coordinate(raddressPosition);
            }
            if (Objects.isNull(receiveCoordinate) || CoordinateUtil.outOfChina(receiveCoordinate.getLongitude(),receiveCoordinate.getLatitude())) {
                receiveCoordinate = wuliuAddressService.getAreaCoordinateV2(wuLiuEntity.getRAreaId(), wuLiuEntity.getRAddress(), wuLiuEntity.getRCityId(), 0);
            }
        } else {
            receiveCoordinate = StringUtils.isNotBlank(req.getReceivePosition()) ? CoordinateUtil.gcj2wgs(new Coordinate(req.getReceivePosition())) : wuliuAddressService.getAreaCoordinateV2(wuLiuEntity.getRAreaId(), wuLiuEntity.getRAddress(), wuLiuEntity.getRCityId(), 1);
        }
        if (Objects.isNull(sendCoordinate) || Objects.isNull(receiveCoordinate)) {
            WuliuExtendInfo wuliuExtendInfo = LambdaBuild.create(WuliuExtendInfo.class)
                    .set(WuliuExtendInfo::setWuliuId, wuLiuEntity.getId())
                    .set(WuliuExtendInfo::setDistance, 0L)
                    .set(WuliuExtendInfo::setDistributionCost, BigDecimal.ZERO)
                    .set(WuliuExtendInfo::setSdetailedAddress, req.getSdetailedAddress())
                    .set(WuliuExtendInfo::setRdetailedAddress, req.getRdetailedAddress())
                    .build();
            return wuliuExtendInfoService.saveOrUpdateExtendInfo(wuliuExtendInfo);
        }
        WuliuExtendInfo oldExtendInfo = wuliuExtendInfoService.queryWuliuExtendInfoByWuliuId(wuLiuEntity.getId());
        if (Objects.nonNull(oldExtendInfo)
                && Objects.equals(oldExtendInfo.getSendPosition(), sendCoordinate.toString())
                && Objects.equals(oldExtendInfo.getReceivePosition(), receiveCoordinate.toString())) {
            return wuliuExtendInfoService.lambdaUpdate()
                    .set(WuliuExtendInfo::getSdetailedAddress, req.getSdetailedAddress())
                    .set(WuliuExtendInfo::getRdetailedAddress, req.getRdetailedAddress())
                    .eq(WuliuExtendInfo::getId, oldExtendInfo.getId())
                    .update();
        }

        Coordinate sendCoordinateGcj = CoordinateUtil.wgs2gcj(sendCoordinate);
        Coordinate receiveCoordinateGcj = CoordinateUtil.wgs2gcj(receiveCoordinate);
        TencentMapDrivingResVO tencentMapDriving = TencentMapUtil.tencentMapBicycling(sendCoordinateGcj.getLatitude() + "," + sendCoordinateGcj.getLongitude(), receiveCoordinateGcj.getLatitude() + "," + receiveCoordinateGcj.getLongitude());
        long distance = 0;
        Assert.isFalse(tencentMapDriving == null, "调用腾讯地图骑行路径规划异常");
        if (Objects.equals(TencentMapUtil.BICYCLING_RATE_LIMIT, tencentMapDriving.getStatus())) {
            log.warn("调用腾讯地图骑行路径规划{}", JSONUtil.toJsonStr(tencentMapDriving));
            return true;
        }
        if (!Objects.equals(0, tencentMapDriving.getStatus())) {
            WuliuUtil.sendWuliuExpressMessage(new WuliuExpressMqBO<WuLiuLogEntity>().setAct(WuliuExpressConstant.ACT_WULIULOG)
                    .setData(new WuLiuLogEntity()
                            .setWuliuid(wuLiuEntity.getId())
                            .setInuser(StringUtils.isBlank(req.getInUser()) ? "系统" : req.getInUser())
                            .setMsg(tencentMapDriving.getMessage())
                            .setDtime(LocalDateTime.now())));
        } else if (Objects.nonNull(tencentMapDriving.getResult()) && CollectionUtils.isNotEmpty(tencentMapDriving.getResult().getRoutes())) {
            TencentMapDrivingResVO.ResultData.RouteData routeData = tencentMapDriving.getResult().getRoutes().get(0);
            distance = routeData.getDistance();
        }
        BigDecimal distributionCost = wuliuExtendInfoService.queryDistributionCost(distance);
        WuliuExtendInfo wuliuExtendInfo = LambdaBuild.create(WuliuExtendInfo.class)
                .set(WuliuExtendInfo::setWuliuId, wuLiuEntity.getId())
                .set(WuliuExtendInfo::setSendPosition, sendCoordinate.toString())
                .set(WuliuExtendInfo::setReceivePosition, receiveCoordinate.toString())
                .set(WuliuExtendInfo::setDistance, distance)
                .set(WuliuExtendInfo::setDistributionCost, distributionCost)
                .set(WuliuExtendInfo::setSdetailedAddress, req.getSdetailedAddress())
                .set(WuliuExtendInfo::setRdetailedAddress, req.getRdetailedAddress())
                .build();
        return wuliuExtendInfoService.saveOrUpdateExtendInfo(wuliuExtendInfo);
    }

    /**
     * 处理物流单骑行距离
     *
     * @param days
     */
    @Override
    public void handleDistributionCost(Integer days) {
        Integer wuliuId = wuliuExtendInfoService.queryWuliuIdByDtime(days);
        List<WuLiuEntity> wuliuList = wuliuExtendInfoService.queryWuliuByNotDistributionCost(wuliuId);
        if (CollectionUtils.isNotEmpty(wuliuList)) {
            wuliuList.forEach(v -> {
                WuliuUtil.sendWuliuExpressMessage(new WuliuExpressMqBO<WuliuDistributtionCostBO>().setAct(WuliuExpressConstant.ACT_CALCULATE_DISTRIBUTION_COST)
                        .setData(LambdaBuild.create(new WuliuDistributtionCostBO()).set(WuliuDistributtionCostBO::setWuliuId, v.getId()).build()));
            });
        }
    }

    /**
     * 作废物流单
     *
     * @param req
     * @return
     */
    @Override
    @DS("oanewWrite")
    public R<Boolean> invalidV2(WuliuInvalidReqV2 req) {
        WuLiuEntity wuliu = wuLiuService.getById(req.getWuliuId());
        if (Objects.isNull(wuliu)) {
            R<Boolean> r = R.error("物流单号不存在");
            r.setData(false);
            return r;
        }
        if (Arrays.asList(WuLiuStatusEnum.FINISH.getCode(),WuLiuStatusEnum.INVALID.getCode(),WuLiuStatusEnum.SIGHED.getCode()).contains(Optional.ofNullable(wuliu.getStats()).orElse(0))) {
            R<Boolean> r = R.error("已签收，已删除的物流单不能再删除");
            r.setData(false);
            return r;
        }
        WuliuCancelExpressReq cancelExpressReq = LambdaBuild.create(WuliuCancelExpressReq.class)
                .set(WuliuCancelExpressReq::setWuliuId, wuliu.getId())
                .build();
        OaUserBO user = SysUtils.getUser();
        if (Objects.isNull(user)) {
            user = new OaUserBO();
            user.setUserName("系统");
        }
        R<String> stringR = cancelOrderService.cancelOrderV2(user , cancelExpressReq);
        if (Objects.nonNull(stringR) && Objects.equals(0, stringR.getCode())) {
            boolean updateFlag = wuLiuService.lambdaUpdate()
                    .set(WuLiuEntity::getStats, WuLiuStatusEnum.INVALID.getCode())
                    .eq(WuLiuEntity::getId, wuliu.getId())
                    .eq(WuLiuEntity::getStats, wuliu.getStats())
                    .update();
            if (updateFlag && StringUtils.isNotBlank(req.getLogMsg())) {
                wuLiuLogsService.addOne(wuliu.getId(), user.getUserName(), req.getLogMsg());
            }
            log.info("取消物流单接口req={}, res={}", JSONUtil.toJsonStr(req), updateFlag);
            return R.success(updateFlag);
        }
        log.info("取消物流单失败req={}, cancelExpressReq={}, res={}", JSONUtil.toJsonStr(req), JSONUtil.toJsonStr(cancelExpressReq), JSONUtil.toJsonStr(stringR));
        R<Boolean> r = R.error(Optional.ofNullable(stringR).map(R::getUserMsg).orElse("取消快递失败"));
        r.setData(false);
        return r;
    }

    /**
     * 更新物流单状态
     *
     * @param wuliuStats
     * @return
     */
    @Override
    @DS("oanewWrite")
    public boolean changeWuliuStats(ChangWuliuStatsBO wuliuStats) {
        WuLiuEntity wuliu = wuLiuService.getById(wuliuStats.getWuliuId());
        if (Objects.isNull(wuliu)) {
            return false;
        }
        Integer oldStats = wuliuStats.getOldStats() != null ? wuliuStats.getOldStats() : Optional.ofNullable(wuliu.getStats()).orElse(0);
        if (Objects.equals(oldStats, wuliuStats.getStats())) {
            return true;
        }
        //已删除,签收的物流单不允许修改
        if (Arrays.asList(WuLiuStatusEnum.FINISH.getCode(),WuLiuStatusEnum.INVALID.getCode(),WuLiuStatusEnum.SIGHED.getCode()).contains(oldStats)) {
            return false;
        }
        boolean updateFlag = wuLiuService.lambdaUpdate()
                .set(WuLiuEntity::getStats, wuliuStats.getStats())
                .set(Objects.equals(WuLiuStatusEnum.SIGHED.getCode(),wuliuStats.getStats()), WuLiuEntity::getReceiveTime, LocalDateTime.now())
                .eq(WuLiuEntity::getId, wuliu.getId())
                .eq(WuLiuEntity::getStats, oldStats)
                .update();
        if (updateFlag && StringUtils.isNotBlank(wuliuStats.getLogMsg())) {
            wuLiuLogsService.addOne(wuliu.getId(), wuliuStats.getInUser(), wuliuStats.getLogMsg());
        }
        return updateFlag;
    }

    /**
     * 处理超时物流单
     *
     * @param days
     * @return
     */
    @Override
    public boolean handleTimeoutWuliu(Integer days) {
        List<WuLiuEntity> wuliuList = wuLiuService.getTimeoutWuliuList(days);
        if (CollectionUtils.isNotEmpty(wuliuList)) {
            wuliuList.forEach(v -> {
                ChangWuliuStatsBO wuliuStats = LambdaBuild.create(ChangWuliuStatsBO.class)
                        .set(ChangWuliuStatsBO::setWuliuId, v.getId())
                        .set(ChangWuliuStatsBO::setLogMsg, "物流单超过15天未完结，自动删除")
                        .set(ChangWuliuStatsBO::setInUser, "系统")
                        .set(ChangWuliuStatsBO::setStats, WuLiuStatusEnum.INVALID.getCode()).build();
                WuliuUtil.sendWuliuExpressMessage(new WuliuExpressMqBO<ChangWuliuStatsBO>().setAct(WuliuExpressConstant.ACT_WULIU_CHANGE_STATS)
                        .setData(wuliuStats));
            });
        }
        return false;
    }

    /**
     * 物流单，快递收货地址是否一致
     *
     * @param req
     * @return
     */
    @Override
    public SameWuliuExpressAddressRes sameReceiveAddress(SameWuliuExpressAddressReq req) {
        WuLiuEntity wuliu = wuLiuService.getById(req.getWuliuId());
        LcExpressOrder expressOrder = SpringUtil.getBean(LcExpressOrderService.class).findExpressOrderByWaybillNo(req.getNu());
        SameWuliuExpressAddressRes res = new SameWuliuExpressAddressRes();
        res.setSameAddress(false);
        if (Objects.nonNull(wuliu) && Objects.nonNull(expressOrder)) {
            res.setSameAddress(Optional.ofNullable(expressOrder.getReceiverDetailAddress()).orElse("").contains(wuliu.getRAddress()));
        }
        return res;
    }

    /**
     * 快递签收物流单处理
     */
    @Override
    public void handleWuliuExpressSigned(ExpressPushVO expressPush) {
        String receiverDetailAddress = Optional.ofNullable(SpringUtil.getBean(LcExpressOrderService.class).findExpressOrderByWaybillNo(expressPush.getWaybillNo()))
                .map(LcExpressOrder::getReceiverDetailAddress)
                .orElse("");
        List<WuLiuEntity> wuliuList = wuLiuService.getWuliuByNu(expressPush.getWaybillNo());
        if(CollectionUtils.isNotEmpty(wuliuList)) {
            wuliuList.forEach(v -> {
                WuliuExpressSignedBO wuliuExpressSigned = wuLiuMapStruct.toWuliuExpressSigned(v);
                wuliuExpressSigned.setExpressReceiverAddress(StringUtils.isNotBlank(receiverDetailAddress) ? receiverDetailAddress : wuliuExpressSigned.getRaddress());
                WuliuUtil.sendWuliuExpressMessage(new WuliuExpressMqBO<WuliuExpressSignedBO>().setAct(WuliuExpressConstant.ACT_WULIUEXPRESS_SIGNED)
                        .setData(wuliuExpressSigned));
            });
        }
    }

    /**
     * 快递签收物流单处理
     */
    @Override
    public void handleWuliuExpressSigned(String nu) {
        List<WuLiuEntity> wuliuList = wuLiuService.getWuliuByNu(nu);
        if(CollectionUtils.isNotEmpty(wuliuList)) {
            wuliuList.forEach(v -> {
                WuliuExpressSignedBO wuliuExpressSigned = wuLiuMapStruct.toWuliuExpressSigned(v);
                wuliuExpressSigned.setExpressReceiverAddress(v.getRAddress());
                WuliuUtil.sendWuliuExpressMessage(new WuliuExpressMqBO<WuliuExpressSignedBO>().setAct(WuliuExpressConstant.ACT_WULIUEXPRESS_SIGNED)
                        .setData(wuliuExpressSigned));
            });
        }
    }

    /**
     * 快递签收物流单处理
     */
    @Override
    @DS("oanewWrite")
    public void wuliuExpressSigned(WuliuExpressSignedBO wuliuExpress) {
        List<Integer> statsList = Arrays.asList(WuLiuStatusEnum.WAITING_PICK.getCode(), WuLiuStatusEnum.WAITING_DELIVERY.getCode(), WuLiuStatusEnum.DELIVERY.getCode());
        Integer stats = Optional.ofNullable(wuliuExpress.getStats()).orElse(0);
        if (StringUtils.isNotBlank(wuliuExpress.getExpressReceiverAddress()) && statsList.contains(stats)) {
            String raddress = StringUtils.isNotBlank(wuliuExpress.getRaddress()) ? wuliuExpress.getRaddress() : Optional.ofNullable(areaInfoService.getAreaInfoByAreaId2(wuliuExpress.getRareaId())).map(Areainfo::getCompanyAddress).orElse("");
            if (wuliuExpress.getExpressReceiverAddress().contains(raddress)) {
                boolean isUpdate = wuLiuService.lambdaUpdate().set(WuLiuEntity::getStats, WuLiuStatusEnum.SIGHED.getCode())
                        .set(WuLiuEntity::getReceiveTime, LocalDateTime.now())
                        .eq(WuLiuEntity::getId, wuliuExpress.getWuliuId())
                        .in(WuLiuEntity::getStats, statsList)
                        .update();
                if (isUpdate) {
                    wuLiuLogsService.addOne(wuliuExpress.getWuliuId(), "系统", "快递签收物流单自动完成");
                }
            }
        }
    }

    @Override
    public WuliuOrderInfoRes getOrderByPayId(Integer payId) {
        WuliuOrderInfoRes orderByPayId = wuLiuSubService.getOrderByPayId(payId);
        if (Objects.nonNull(orderByPayId) && Objects.equals(1, orderByPayId.getPayType())) {
            return orderByPayId;
        }
        return wuLiuRecoverMarketInfoService.getOrderByPayId(payId);
    }

    @Override
    public List<InnerWuliuInfoRes> getInnerWuliuInfo(InnerWuliuInfoReq vo) {
        List<InnerWuliuInfoRes> list = wuLiuService.getInnerWuliuInfo(vo);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<InnerWuliuInfoRes> resultList = new ArrayList<>();
        //相同的物流单合并商品信息
        Map<Integer, List<InnerWuliuInfoRes>> listMap = list.stream().collect(Collectors.groupingBy(InnerWuliuInfoRes::getWuliuId));
        listMap.forEach((k, v) -> {
            String productName = v.stream().map(InnerWuliuInfoRes::getProductName).collect(Collectors.joining(","));
            InnerWuliuInfoRes innerWuliuInfoRes = v.get(0);
            innerWuliuInfoRes.setProductName(productName);
            resultList.add(innerWuliuInfoRes);
        });
        return resultList;
    }

    @Override
    public WuliuOrderInfoRes getOrderByPayDes(String payDes) {
        WuliuOrderInfoRes orderByPayId = wuLiuSubService.getOrderByPayDes(payDes);
        if (Objects.nonNull(orderByPayId) && Objects.equals(1, orderByPayId.getPayType())) {
            return orderByPayId;
        }
        return wuLiuRecoverMarketInfoService.getOrderByPayDes(payDes);
    }

    @Override
    public Boolean sendWuliuOrder(SendWuliuOrderReq vo) {
        OaUserBO currentStaffId = currentRequestComponent.getCurrentStaffId();
        if (Objects.isNull(currentStaffId)) {
            throw new CustomizeException("请登录后再操作哦");
        }
        //查询物流单卡片信息
        String type = "logistics";
        SubAppReq req = new SubAppReq();
        req.setIds(Convert.toStr(vo.getWuliuId()));
        req.setType(type);
        List<ImOrderVO> imOrderList = Optional.ofNullable(webOrderCloud.getOrderByTypeAndUserIdV2(Collections.singletonList(req)))
                .map(R::getData).map(ImOrderObjVo::getList).orElse(Collections.emptyList());
        ImOrderVO imOrder = imOrderList.stream().findFirst().orElse(null);
        if (Objects.isNull(imOrder)) {
            throw new CustomizeException("发送消息失败，未查询到对应的物流单卡片信息");
        }
        LogisticsTransferInfoVO logisticsInfo = imOrder.getLogisticsInfo();
        LogisticsMessageBody messageBody = LogisticsMessageBody.builder()
                .type(type)
                .logisticsId(vo.getWuliuId())
                .transferId(logisticsInfo.getTransferId())
                .ppid(logisticsInfo.getOrderId())
                .sendArea(logisticsInfo.getSendArea())
                .recieveArea(logisticsInfo.getRecieveArea())
                .poster(imOrder.getImagePath())
                .link(imOrder.getLink())
                .plainText(vo.getPlainText())
                .build();
        ImMessageReq<Object> messageReq = ImMessageReq.builder()
                .fromOa(true)
                .sendUserId(currentStaffId.getUserId())
                .subTenant(XtenantEnum.getXtenant())
                .receiveUserIds(Collections.singletonList(vo.getReceiveUserId()))
                .messageBody(messageBody)
                .build();
        //发送im消息
        return smsService.sendImMsgTo9ji(messageReq);
    }

    /**
     * 查询物流单门店营业时间
     *
     * @param vo
     * @return
     */
    @Override
    public WuliuAreaOpeningTimeRes getWuliuAreaOpeningTime(WuliuAreaOpeningTimeReq vo) {
        WuliuAreaOpeningTimeRes res = new WuliuAreaOpeningTimeRes();
        AreaOpeningHoursService areaOpeningHoursService = SpringUtil.getBean(AreaOpeningHoursService.class);
        if (com.jiuji.common.util.CommonUtil.isNotNullZero(vo.getSendAreaId())) {
            AreaBusinessStatus sendOpeningTime = areaOpeningHoursService.getAreaOpenTime(vo.getSendAreaId());
            res.setSendBusinessStatus(sendOpeningTime.getStatus());
            res.setSendBusinessStatusText(sendOpeningTime.getStatusText());
            res.setSendBusinessTime(sendOpeningTime.getOpenTime());
        }
        if (com.jiuji.common.util.CommonUtil.isNotNullZero(vo.getReceiveAreaId())) {
            AreaBusinessStatus receiveOpeningTime = areaOpeningHoursService.getAreaOpenTime(vo.getReceiveAreaId());
            res.setReceiveBusinessStatus(receiveOpeningTime.getStatus());
            res.setReceiveBusinessStatusText(receiveOpeningTime.getStatusText());
            res.setReceiveBusinessTime(receiveOpeningTime.getOpenTime());
        }
        return res;
    }

    /**
     * 处理快递单取消
     *
     * @param expressPush
     */
    @Override
    @DS("oanewWrite")
    public void handleWuliuExpressCanceled(ExpressPushVO expressPush) {
        List<WuLiuEntity> wuliuList = wuLiuService.getWuliuByNu(expressPush.getWaybillNo());
        if (CollectionUtils.isNotEmpty(wuliuList)) {
            List<Integer> statsList = Arrays.asList(WuLiuStatusEnum.WAITING_PICK.getCode(), WuLiuStatusEnum.WAITING_DELIVERY.getCode(), WuLiuStatusEnum.DELIVERY.getCode());
            for (WuLiuEntity wuliu : wuliuList) {
                //清空物流单快递单信息
                boolean isUpdate = wuLiuService.lambdaUpdate()
                        .set(WuLiuEntity::getCom, null)
                        .set(WuLiuEntity::getNu, null)
                        .set(WuLiuEntity::getPtUserName, null)
                        .set(WuLiuEntity::getPtUserMobile, null)
                        .eq(WuLiuEntity::getId, wuliu.getId())
                        .eq(WuLiuEntity::getCom, wuliu.getCom())
                        .eq(WuLiuEntity::getNu, wuliu.getNu())
                        .in(WuLiuEntity::getStats, statsList)
                        .update();
                if (isUpdate) {
                    wuLiuLogsService.addOne(wuliu.getId(), "系统", StrUtil.format("系统推送快递单取消，清空物流单快递单号：{}", expressPush.getWaybillNo()));
                    //推送订单取消快递消息
                    if (WuLiuConstant.SUB_WULIU_TYPE_ARRY.contains(wuliu.getWuType()) || WuLiuConstant.LP_SUB_WULIU_TYPE_ARRY.contains(wuliu.getWuType())) {
                        wuLiuService.setRabbitMqMessageForCsharp(JacksonJsonUtils.toJson(new RabbitMqActDTO().setAct("ExpressWaybillCancelMqAct")
                                .setData(new CancelWuliuNoDTO()
                                        .setWuliuId(wuliu.getId().longValue())
                                        .setNu(wuliu.getNu())
                                        .setWuliuType(wuliu.getWuType())
                                        .setSubId(wuliu.getDanHaoBind().longValue())
                                        .setUser("系统"))
                        ));
                    }
                }
            }
        }
        SpringUtil.getBean(IWuliuAutoPaotuiInfoService.class).cancelCallingByNu(expressPush.getWaybillNo(), "平台取消跑腿单");
    }

    /**
     * 苹果采购物流单处理
     *
     * @param applePurchaseWuliu
     */
    @Override
    public void applePurchaseWuliu(ApplePurchaseWuliuBO applePurchaseWuliu) {
        QueryRouteReqV2 req = QueryRouteReqV2.builder()
                .expressCode(applePurchaseWuliu.getCom())
                .waybillNo(applePurchaseWuliu.getNu())
                .phone(applePurchaseWuliu.getReceiveMobile())
                .mark("苹果采购物流单查询快递轨迹信息")
                .xTenantId(Convert.toLong(XtenantEnum.getXtenant()))
                .build();
        List<RouteData> routeList = Optional.ofNullable(logisticsExpressService.queryRoute(req)).map(R::getData)
                .map(QueryRouteResV2::getRouteList).orElse(Collections.emptyList());
        if (CollectionUtils.isEmpty(routeList)) {
            return;
        }
        //快递签收
        if(routeList.stream().anyMatch(routeData -> "80".equals(routeData.getOpCode())
                || routeData.getContent().contains("已签收")
                || routeData.getContent().contains("代签收"))){
            //物流单签收
            WuliuExpressSignedBO wuliuExpressSigned = LambdaBuild.create(WuliuExpressSignedBO.class)
                    .set(WuliuExpressSignedBO::setWuliuId, applePurchaseWuliu.getWuliuId())
                    .set(WuliuExpressSignedBO::setCom, applePurchaseWuliu.getCom())
                    .set(WuliuExpressSignedBO::setNu, applePurchaseWuliu.getNu())
                    .set(WuliuExpressSignedBO::setStats, applePurchaseWuliu.getStats())
                    .set(WuliuExpressSignedBO::setExpressReceiverAddress, applePurchaseWuliu.getReceiveAddress())
                    .set(WuliuExpressSignedBO::setExpressReceiverAddress, applePurchaseWuliu.getReceiveAddress())
                    .build();
            WuliuUtil.sendWuliuExpressMessage(new WuliuExpressMqBO<WuliuExpressSignedBO>().setAct(WuliuExpressConstant.ACT_WULIUEXPRESS_SIGNED)
                    .setData(wuliuExpressSigned));
            WuliuInstockPendingUpdateVO vo = new WuliuInstockPendingUpdateVO();
            vo.setWuliuId(applePurchaseWuliu.getWuliuId());
            CompletableFuture.runAsync(() -> wuliuInstockPendingService.dealPending(vo));
        }
    }

    /**
     * 查询用户与门店的距离
     *
     * @param req
     * @return
     */
    @Override
    public DistanceAreaAndPositionRes getDistanceAreaAndPosition(DistanceAreaAndPositionReq req) {
        DistanceAreaAndPositionRes res = new DistanceAreaAndPositionRes();
        if (StrUtil.isBlank(req.getPosition())) {
            return res;
        }
        String[] split = StrUtil.split(req.getPosition(), ",");
        if (split.length != 2) {
            return res;
        }
        String lat = split[0];
        String lng = split[1];
        String position = lng + "," + lat;
        //经纬度错误
        if (WuliuAddressUtil.outOfChina(position)) {
            return res;
        }

        Areainfo areaInfo = null;
        if (StringUtils.isNumeric(req.getAreaId())) {
            areaInfo = areaInfoService.getAreaInfoByAreaId2(Convert.toInt(req.getAreaId(),0));
        } else {
            areaInfo = areaInfoService.getAreaInfoByArea(req.getAreaId());
        }
        if (Objects.nonNull(areaInfo) && StrUtil.isNotBlank(areaInfo.getPosition())) {
            double distance = AtlasUtil.getDistance(lng +","+lat, areaInfo.getPosition());
            res.setDistance(distance);
            if (distance > NumberConstant.FIVE_HUNDRED) {
                res.setMsg("当前位置与送达门店超过500米，是否确认送达？");
            }
        }
        return res;
    }

    /**
     * 更新物流单跑腿派送信息
     *
     * @return
     */
    @Override
    @DS(DataSourceConstants.OANEW_WRITE)
    public void updateWuliuPtInfo(String nu, String ptUserName, String ptUserMobile) {
        List<WuLiuEntity> wuliuList = wuLiuService.getWuliuByNu(nu);
        if (CollUtil.isNotEmpty(wuliuList)) {
            List<Integer> wuliuIdList = wuliuList.stream().map(WuLiuEntity::getId).collect(Collectors.toList());
            wuLiuService.lambdaUpdate()
                    .set(WuLiuEntity::getPtUserName, ptUserName)
                    .set(WuLiuEntity::getPtUserMobile, ptUserMobile)
                    .in(WuLiuEntity::getId, wuliuIdList)
                    .eq(WuLiuEntity::getNu, nu)
                    .isNull(WuLiuEntity::getPtUserName)
                    .isNull(WuLiuEntity::getPtUserMobile)
                    .update();
        }
    }

    /**
     * 保存物流单代签和揽收
     *
     * @param req
     * @return
     */
    @Override
    public R<String> signatureAndPickUpWuliu(WuliuSignatureAndPickUpReq req) {
        OaUserBO user = SysUtils.getUser();
        if (Objects.isNull(user)) {
            return R.error("请登录后再操作哦");
        }
        log.info("代签、揽收物理单={},currentUser={}",JacksonJsonUtils.toJson(req),user.getUserName());
        List<Integer> kinds = Arrays.asList(WuliuLogKindEnum.SIGNATURE_WULIU.getCode(), WuliuLogKindEnum.PICK_UP_WULIU.getCode());
        Integer kind = Optional.ofNullable(req.getKind()).orElse(0);
        if (!kinds.contains(kind)) {
            return R.error("操作类型错误，只支持代签扫描，揽收扫描");
        }
        List<WuLiuEntity> wuLiuList = CommonUtils.bigDataInQuery(req.getWuliuIdList(), wuLiuService::listByIds);
        if (CollectionUtils.isEmpty(wuLiuList)) {
            return R.error("没查询到对应的物流单");
        }
        String roleName = Optional.ofNullable(SpringUtil.getBean(RoleInfoService.class).getRoleIdsByUserIds(LambdaBuild.create(RoleSimpleReq.class)
                .set(RoleSimpleReq::setUserIdList, Collections.singletonList(user.getUserId())).build())).orElse(new ArrayList<>()).stream().findFirst()
                .map(RolesSimpleVo::getName).orElse("");
        Areainfo areainfo = Optional.ofNullable(areaInfoService.getById(user.getArea1id())).orElse(new Areainfo());

        List<WuLiuLogEntity> wuliuLogList = wuLiuList.stream().map(v -> {
            String logMsg = "";
            if (WuliuLogKindEnum.SIGNATURE_WULIU.getCode().equals(req.getKind())) {
                logMsg = StrUtil.format("{}{}{}（{}）已签收快件", areainfo.getAreaName(), areainfo.getArea(), roleName, user.getUserName());
            } else if (WuliuLogKindEnum.PICK_UP_WULIU.getCode().equals(req.getKind())) {
                logMsg = StrUtil.format("{}{}{}（{}）已揽收快件", areainfo.getAreaName(), areainfo.getArea(), roleName, user.getUserName());
            }
            return LambdaBuild.create(WuLiuLogEntity.class).build()
                    .setInuser(user.getUserName())
                    .setMsg(logMsg)
                    .setDtime(LocalDateTime.now())
                    .setKind(req.getKind())
                    .setWuliuid(v.getId());
        }).collect(Collectors.toList());

        String moaUrl = SysConfigUtils.getMoaUrl();
        if (WuliuLogKindEnum.SIGNATURE_WULIU.getCode().equals(req.getKind())) {
            Map<String, List<WuLiuEntity>> wuliuMap = wuLiuList.stream().filter(v -> StrUtil.isNotBlank(v.getRName())).collect(Collectors.groupingBy(WuLiuEntity::getRName));
            Set<String> userNames = wuliuMap.keySet();
            Map<String, Integer> ch999UserMap = ch999UserService.getCh999UserMap(userNames);
            for (String userName : wuliuMap.keySet()) {
                Integer userId = ch999UserMap.get(userName);
                List<WuLiuEntity> wuLius = wuliuMap.get(userName);
                if (CommonUtil.isNotNullZero(userId) && CollUtil.isNotEmpty(wuLius)) {
                    Lists.partition(wuLius, NumberConstant.TEN).forEach(v -> {
                        String wuliuLink = v.stream().map(x -> StrUtil.format("<a href='{}/mWuLiu/wuliuInfo?wuliuid={}'>{}</a>", moaUrl, x.getId(), x.getId())).collect(Collectors.joining("，"));
                        String sendMsg = StrUtil.format("您有快件（物流单{}），已到达三号平台一楼九机快送部代签点，请尽快到一楼后门取件签收",wuliuLink);
                        smsService.sendOaMsg(sendMsg, null, Convert.toStr(userId), OaMesTypeEnum.WLTZ);
                    });
                }
            }
        }
        if (WuliuLogKindEnum.PICK_UP_WULIU.getCode().equals(req.getKind())) {
            Map<String, List<WuLiuEntity>> wuliuMap = wuLiuList.stream().filter(v -> StrUtil.isNotBlank(v.getSName())).collect(Collectors.groupingBy(WuLiuEntity::getSName));
            Set<String> userNames = wuliuMap.keySet();
            Map<String, Integer> ch999UserMap = ch999UserService.getCh999UserMap(userNames);
            for (String userName : wuliuMap.keySet()) {
                Integer userId = ch999UserMap.get(userName);
                List<WuLiuEntity> wuLius = wuliuMap.get(userName);
                if (CommonUtil.isNotNullZero(userId) && CollUtil.isNotEmpty(wuLius)) {
                    Lists.partition(wuLius, NumberConstant.TEN).forEach(v -> {
                        String wuliuLink = v.stream().map(x -> StrUtil.format("<a href='{}/mWuLiu/wuliuInfo?wuliuid={}'>{}</a>", moaUrl, x.getId(), x.getId())).collect(Collectors.joining("，"));
                        String sendMsg = StrUtil.format("您有快件{},已由{}{}{}（{}）揽收，后续物流详情可通过物流单查看",wuliuLink,areainfo.getAreaName(), areainfo.getArea(), roleName, user.getUserName());
                        smsService.sendOaMsg(sendMsg, null, Convert.toStr(userId), OaMesTypeEnum.WLTZ);
                    });
                }
            }
        }
        if (CollUtil.isNotEmpty(wuliuLogList)) {
            CommonUtils.bigDataPage(NumberConstant.ONE_HUNDRED,wuliuLogList, wuLiuLogsService::saveLogBatch);
        }
        return R.success("操作成功");
    }

    /**
     * 获取物流单信息
     *
     * @param req
     * @return
     */
    @Override
    @DS(DataSourceConstants.CH999_OA_NEW)
    public GetWuliuInfoRes getWuLiuInfo(GetWuliuInfoReq req) {
        WuLiuEntity wuliuInfo = wuLiuService.getWuliuById(req.getWuliuId());
        if (Objects.isNull(wuliuInfo)) {
            throw new CustomizeException("未查询到物流单信息");
        }
        List<Integer> kinds = Arrays.asList(WuliuLogKindEnum.SIGNATURE_WULIU.getCode(), WuliuLogKindEnum.PICK_UP_WULIU.getCode());
        Integer kind = Optional.ofNullable(req.getKind()).orElse(0);
        if(kinds.contains(kind)) {
            Integer count = wuLiuLogsService.lambdaQuery().eq(WuLiuLogEntity::getWuliuid, req.getWuliuId()).eq(WuLiuLogEntity::getKind, kind).count();
            if (count > 0) {
                throw new CustomizeException(StrUtil.format("物流单：{}已{}，请勿重复操作", req.getWuliuId(), EnumUtil.getMessageByCode(WuliuLogKindEnum.class, kind, "")));
            }
        }
        GetWuliuInfoRes result = wuLiuMapStruct.toGetWuliuInfoRes(wuliuInfo);
        String fromArea = Optional.ofNullable(areaInfoService.getAreaInfoByAreaId2(wuliuInfo.getSAreaId())).map(Areainfo::getArea).orElse("");
        String toArea = Optional.ofNullable(areaInfoService.getAreaInfoByAreaId2(wuliuInfo.getRAreaId())).map(Areainfo::getArea).orElse("");
        result.setFromArea(fromArea);
        result.setToArea(toArea);
        result.setStateName(EnumUtil.getMessageByCode(WuLiuStatusEnum.class, wuliuInfo.getStats(),""));
        return result;
    }

    /**
     * 签收物流单发送OA消息
     *
     * @return
     */
    @Override
    public Boolean signatureWuliuSendOaMsg() {
        Page<SignatureWuliuBO> page = new Page<>(1,NumberConstant.ONE_THOUSAND);
        page.setOrders(OrderItem.ascs("u.ch999_id,w.id"));
        Page<SignatureWuliuBO> resultPages = wuLiuService.querySignatureWuliuList(page);
        sendSignatureWuliuMsg(resultPages);
        int i = 2;
        while (i < resultPages.getPages()) {
            page.setCurrent(i);
            resultPages = wuLiuService.querySignatureWuliuList(page);
            sendSignatureWuliuMsg(resultPages);
            i++;
        }
        return true;
    }

    /**
     * 调拨单已发货状态，物流单叫跑腿后，推送OA消息给调拨单发货操作人
     *
     * @param data
     */
    @Override
    public void diaoboPaotuiWuliu(DiaoboPaotuiWuliuBO data) {
        List<DiaoboPaotuiWuliuResBO> dataList = wuLiuService.queryDiaoboPaotuiWuliu(data);
        if (CollUtil.isNotEmpty(dataList)) {
            //String moaUrl = SysConfigUtils.getMoaUrl()
            for (DiaoboPaotuiWuliuResBO diaoboPaotuiWuliu : dataList) {
                if (Objects.nonNull(diaoboPaotuiWuliu.getSendUserId())) {
                    String sendMsg = "";
                    if (Objects.equals(1,diaoboPaotuiWuliu.getKind())) {
                        sendMsg = StrUtil.format("你发货的调拨单号：{}，收货门店已改成三方跑腿送货，请及时备好货，等待取货",diaoboPaotuiWuliu.getSubId());
                    } else {
                        sendMsg = StrUtil.format("你发货的机器编号：{}，收货门店已改成三方跑腿送货，请及时备好货，等待取货",diaoboPaotuiWuliu.getOrderid());
                    }
                    smsService.sendOaMsg(sendMsg, null, Convert.toStr(diaoboPaotuiWuliu.getSendUserId()), OaMesTypeEnum.WLTZ);
                }
            }
        }
    }

    /**
     * 物流单未投妥
     *
     * @param req
     * @return
     */
    @Override
    public R<String> wuliuNotDelivered(WuliuNotDeliveredReq req) {
        OaUserBO currentStaffId = currentRequestComponent.getCurrentStaffId();
        if (Objects.isNull(currentStaffId)) {
            throw new CustomizeException("请登录后再操作哦");
        }
        WuLiuEntity wuliu = wuLiuService.getWuliuById(req.getWuliuId());
        String checkMsg = checkWuliuNotDelivered(req,wuliu);
        if (StrUtil.isNotBlank(checkMsg)) {
            return R.error(checkMsg);
        }
        String subLogMsg = StrUtil.format("未妥投，原因：{}", req.getReason());
        CsharpInWcfCloud inWcfCloud = SpringUtil.getBean(CsharpInWcfCloud.class);
        String wuliuCompanyName = expressEnumService.getWuliuCompanyName(wuliu.getCom());
        if (WuLiuTypeEnum.ORDER.getCode().equals(wuliu.getWuType()) || WuLiuTypeEnum.ORDER_EXPRESS.getCode().equals(wuliu.getWuType())) {
            //订单
            WuLiuSubEntity sub = wuLiuSubService.getSub(wuliu.getDanHaoBind());
            if (sub != null && SubCheckEnum.ALREADY_OUT.getCode().equals(sub.getSubCheck())) {
                //订单状态由已出库改为已确认
                SubCheckOpReq subCheckOpReq = SubCheckOpReq.builder().subId(Convert.toStr(sub.getSubId())).subtype("1").subCheck(SubCheckEnum.ALREADY_CONFIRM.getCode()).areaid(sub.getAreaId()).userName(currentStaffId.getUserName()).build();
                R subCheckOpR = inWcfCloud.subCheckOp(subCheckOpReq);
                log.info("物流单未投妥，修改订单状态，参数：{}，结果：{}", JacksonJsonUtils.toJson(subCheckOpReq),JacksonJsonUtils.toJson(subCheckOpR));
                if (Objects.nonNull(subCheckOpR) && subCheckOpR.isSuccess()) {
                    subLogMsg = subLogMsg + "，" + WuLiuConstant.NOT_DELIVERED_MSG1;
                } else {
                    RRExceptionHandler.logError("物流单未投妥，修改订单状态失败", subCheckOpReq, null, smsService::sendOaMsgTo9JiMan);
                }
            }
            if (sub != null) {
                boolean isUpdateSubAddress = SpringUtil.getBean(IWuLiuSubAddressService.class).clearWuliuNo(sub.getSubId(), wuliu.getNu());
                if (isUpdateSubAddress) {
                    subLogMsg = subLogMsg + "，" + StrUtil.format(WuLiuConstant.NOT_DELIVERED_MSG2, wuliuCompanyName, wuliu.getNu());
                }
                //预计送达时间
                if(req.getExpectTime() != null) {
                    boolean isUpdateExpectTime = wuLiuSubService.updateSubExpectTime(sub.getSubId(), req.getExpectTime());
                    String oldExpectTimeStr = Optional.ofNullable(DateUtil.formatLocalDateTime(sub.getExpectTime())).orElse("");
                    String expectTimeStr = DateUtil.formatLocalDateTime(req.getExpectTime());
                    if (isUpdateExpectTime && !Objects.equals(oldExpectTimeStr, expectTimeStr)) {
                        subLogMsg = subLogMsg + "，" + StrUtil.format(WuLiuConstant.NOT_DELIVERED_MSG3, oldExpectTimeStr, expectTimeStr);
                    }
                }
                SpringUtil.getBean(ISubLogService.class).saveSubLog(sub.getSubId(), false, subLogMsg,currentStaffId.getUserName());
                //无法联系客户
                if (WuLiuConstant.NOT_DELIVERED_REASON.equals(req.getReason())) {
                    //查询销售信息
                    SellerInfoBO sellerInfo = SpringUtil.getBean(IWuLiuBasketService.class).getSellerInfoBySubId(sub.getSubId());
                    String msg = StrUtil.format(WuLiuConstant.NOT_DELIVERED_SUB_SMS_MSG, sub.getSubId());
                    if (sellerInfo != null) {
                        msg = StrUtil.format(WuLiuConstant.NOT_DELIVERED_SUB_SMS_MSG, sub.getSubId(), sellerInfo.getSeller(), sellerInfo.getCh999UserId(), sellerInfo.getMobile());
                    }
                    log.info("物流单未投妥，发送短信，电话：{},消息：{}",sub.getSubMobile(), msg);
                    if (SysUtils.isJiuJiProd()) {
                        smsService.sendSmsNew(sub.getSubMobile(), msg, SpringUtil.getBean(WuliuApolloConfig.class).getSmsWuliuChannelid());
                    }
                    SpringUtil.getBean(ISubLogService.class).saveSubLog(sub.getSubId(), false, StrUtil.format("未妥投通知，推送方式：短信；推送内容【{}】", msg),currentStaffId.getUserName());
                }
            }
        } else if (WuLiuTypeEnum.FOURTEEN_DAY.getCode().equals(wuliu.getWuType())) {
            //良品订单
            RecoverMarketinfo recoverSub = recoverMarketinfoService.getRecoverSub(wuliu.getDanHaoBind());
            if (recoverSub != null && SubCheckEnum.ALREADY_OUT.getCode().equals(recoverSub.getSubCheck())) {
                //良品订单状态由已出库改为已确认
                SubCheckOpReq subCheckOpReq = SubCheckOpReq.builder().subId(Convert.toStr(recoverSub.getSubId())).subtype("2").subCheck(SubCheckEnum.ALREADY_CONFIRM.getCode()).areaid(recoverSub.getAreaid()).userName(currentStaffId.getUserName()).build();
                R subCheckOpR = inWcfCloud.recoverSubCheckOp(subCheckOpReq);
                log.info("物流单未投妥，修改良品单状态，参数：{}，结果：{}", JacksonJsonUtils.toJson(subCheckOpReq),JacksonJsonUtils.toJson(subCheckOpR));
                if (Objects.nonNull(subCheckOpR) && subCheckOpR.isSuccess()) {
                    subLogMsg = subLogMsg + "，" + WuLiuConstant.NOT_DELIVERED_MSG1;
                } else {
                    RRExceptionHandler.logError("物流单未投妥，修改良品订单状态失败", subCheckOpReq, null, smsService::sendOaMsgTo9JiMan);
                }
            }
            if (recoverSub != null) {
                boolean isUpdateSubAddress = SpringUtil.getBean(IWuLiuRecoverSubAddressService.class).clearWuliuNo(Convert.toInt(recoverSub.getSubId()), wuliu.getNu());
                if (isUpdateSubAddress) {
                    subLogMsg = subLogMsg + "，" + StrUtil.format(WuLiuConstant.NOT_DELIVERED_MSG2, wuliuCompanyName, wuliu.getNu());
                }
                //预计送达时间
                if(req.getExpectTime() != null) {
                    boolean isUpdateExpectTime = recoverMarketinfoService.updateSubExpectTime(Convert.toInt(recoverSub.getSubId()), req.getExpectTime());
                    String oldExpectTimeStr = Optional.ofNullable(DateUtil.formatLocalDateTime(recoverSub.getExpecttime())).orElse("");
                    String expectTimeStr = DateUtil.formatLocalDateTime(req.getExpectTime());
                    if (isUpdateExpectTime && !Objects.equals(oldExpectTimeStr, expectTimeStr)) {
                        subLogMsg = subLogMsg + "，" + StrUtil.format(WuLiuConstant.NOT_DELIVERED_MSG3, oldExpectTimeStr, expectTimeStr);
                    }
                }
                SubLogsNewReq subLogsNewReq = new SubLogsNewReq();
                subLogsNewReq.setSubId(Convert.toInt(recoverSub.getSubId()));
                subLogsNewReq.setType(1);
                subLogsNewReq.setShowType(false);
                subLogsNewReq.setComment(subLogMsg);
                subLogsNewReq.setDTime(LocalDateTime.now());
                subLogsNewReq.setInUser(currentStaffId.getUserName());
                SpringUtil.getBean(ISubLogService.class).saveLpSubLog(subLogsNewReq);
                //无法联系客户
                if (WuLiuConstant.NOT_DELIVERED_REASON.equals(req.getReason())) {
                    //查询销售信息
                    SellerInfoBO sellerInfo = SpringUtil.getBean(IRecoverMarketsubinfoService.class).getSellerInfoBySubId(Convert.toInt(recoverSub.getSubId()));
                    String msg = StrUtil.format(WuLiuConstant.NOT_DELIVERED_SUB_SMS_MSG, recoverSub.getSubId());
                    if (sellerInfo != null) {
//                        msg = StrUtil.format(WuLiuConstant.NOT_DELIVERED_SUB_SMS_MSG1, recoverSub.getSubId(), sellerInfo.getSeller(), sellerInfo.getCh999UserId(), sellerInfo.getMobile());
                    }
                    log.info("物流单未投妥，发送短信，电话：{},消息：{}",recoverSub.getSubMobile(), msg);
                    if (SysUtils.isJiuJiProd()) {
                        smsService.sendSmsNew(recoverSub.getSubMobile(), msg, SpringUtil.getBean(WuliuApolloConfig.class).getSmsWuliuChannelid());
                    }
                    subLogsNewReq.setComment(StrUtil.format("未妥投通知，推送方式：短信；推送内容【{}】", msg));
                    subLogsNewReq.setDTime(LocalDateTime.now());
                    SpringUtil.getBean(ISubLogService.class).saveLpSubLog(subLogsNewReq);
                }
            }
        }
        boolean isWuliuUpdate = wuLiuService.updateWuliuNotDelivered(wuliu);
        if (isWuliuUpdate) {
            String msg = StrUtil.format("未妥投，原因：{}，物流单状态由【{}】更改为【{}】，快递方式由【{}】更改为【】，快递单号【{}】更改为【】", req.getReason(), WuliuStatusEnum.getWuliuStatusEnum(wuliu.getStats()), WuLiuStatusEnum.WAITING_DELIVERY.getMessage(), wuliuCompanyName, wuliu.getNu());
            wuLiuLogsService.saveLog(wuliu.getId(),currentStaffId.getUserName(),msg,WuliuLogKindEnum.WULIU_NOT_DELIVERED.getCode());
        }
        return R.success("操作成功","成功");
    }

    /**
     * 跑腿配送异常
     * @return
     */
    @Override
    public void abnormalDeliver(String waybillNo, String exceptionMsg) {
        List<WuLiuEntity> wuliuList = wuLiuService.getWuliuByNu(waybillNo);
        if (CollUtil.isEmpty(wuliuList)) {
            return;
        }
        for (WuLiuEntity wuLiu : wuliuList) {
            String logMsg = StrUtil.format("跑腿送货异常，异常原因：{}", exceptionMsg);
            //记录物流异常日志
            wuLiuLogsService.saveLog(wuLiu.getId(), null, logMsg, WuliuLogKindEnum.DELIVERING_EXCEPTION.getCode());
            //记录订单日志
            SubLogsNewReq subLogsNewReq = new SubLogsNewReq();
            subLogsNewReq.setType(1);
            subLogsNewReq.setShowType(false);
            subLogsNewReq.setComment(logMsg);
            subLogsNewReq.setDTime(LocalDateTime.now());
            subLogsNewReq.setInUser("系统");

            if (WuLiuTypeEnum.ORDER.getCode().equals(wuLiu.getWuType()) || WuLiuTypeEnum.ORDER_EXPRESS.getCode().equals(wuLiu.getWuType())) {
                WuLiuSubEntity sub = wuLiuSubService.getSub(wuLiu.getDanHaoBind());
                if (sub!= null) {
                    subLogsNewReq.setSubId(sub.getSubId());
                    SpringUtil.getBean(ISubLogService.class).saveSubLog(subLogsNewReq);
                }
            } else if (WuLiuTypeEnum.FOURTEEN_DAY.getCode().equals(wuLiu.getWuType())) {
                RecoverMarketinfo recoverSub = recoverMarketinfoService.getRecoverSub(wuLiu.getDanHaoBind());
                if (recoverSub != null) {
                    subLogsNewReq.setSubId(Convert.toInt(recoverSub.getSubId()));
                    SpringUtil.getBean(ISubLogService.class).saveLpSubLog(subLogsNewReq);
                }
            }
        }
    }

    /**
     * @param req
     */
    @Override
    public void jdDeliveryPreFeedback(JdDeliveryPreFeedbackReq req) {
        //查询物流单信息
        WuLiuEntity wuLiu = wuLiuService.getWuLiuByWuTypeAndDanhaobind(WuLiuTypeEnum.ORDER.getCode(), Convert.toInt(req.getErpDeliveryNo()));
        if (Objects.isNull(wuLiu)) {
            wuLiu = wuLiuService.getWuLiuByWuTypeAndDanhaobind(WuLiuTypeEnum.ORDER_EXPRESS.getCode(), Convert.toInt(req.getErpDeliveryNo()));
        }
        if (Objects.isNull(wuLiu)) {
            RRExceptionHandler.logError("京东仓销售订单预回传运单号，未查询到物流单信息", req, null, SpringUtil.getBean(ISmsService.class)::sendOaMsgTo9JiMan);
            return;
        }
        WuliuWayBillNoReq wuliuWayBillNoReq = new WuliuWayBillNoReq();
        wuliuWayBillNoReq.setWuliuId(Convert.toLong(wuLiu.getId()));
        wuliuWayBillNoReq.setCom(WuLiuConstant.JINGDONG_JIUJI);
        wuliuWayBillNoReq.setWaybillNo(req.getExpressCode());
        wuliuWayBillNoReq.setIsSaveLog(false);
        //更新物流单快递单号
        wuLiuService.updateNuById(wuliuWayBillNoReq);
        //推送单号到Oa
        SpringUtil.getBean(WuliuMqProducer.class).updteWuliuNo(expressEnumService.getWuliuCompanyName(wuliuWayBillNoReq.getCom()), wuliuWayBillNoReq.getWaybillNo(), Long.valueOf(wuLiu.getDanHaoBind()), "系统");
    }

    private String checkWuliuNotDelivered(WuliuNotDeliveredReq req,WuLiuEntity wuliu) {
        String msg = "";
        if (req.getWuliuId() == null) {
            return "物流单号不能为空";
        }
        if (StrUtil.isBlank(req.getReason())) {
            return "未投妥原因不能为空";
        }
        if (wuliu == null) {
            return "未查询到物流单信息";
        }
        if (!WuliuStatusEnum.DELIVERING.getCode().equals(wuliu.getStats())) {
            return "物流单状态不是运输中，不能操作未投妥";
        }
        if (StrUtil.isNotBlank(wuliu.getCom()) && StrUtil.isNotBlank(wuliu.getNu()) && !WuLiuConstant.WULIU_DELIVERY_COM_ARRY.contains(wuliu.getCom())) {
            msg = "物流单快递方式不是达达、uu、美团、顺丰同城，不能操作未投妥";
        }
        return msg;
    }

    private void sendSignatureWuliuMsg(Page<SignatureWuliuBO> resultPages) {
        if (resultPages== null || CollUtil.isEmpty(resultPages.getRecords())) {
            return;
        }
        List<SignatureWuliuBO> records = resultPages.getRecords();
        Map<Integer, List<SignatureWuliuBO>> signatureWuliuMap = records.stream().collect(Collectors.groupingBy(SignatureWuliuBO::getCh999UserId));
        String moaUrl = SysConfigUtils.getMoaUrl();
        for (Integer userId : signatureWuliuMap.keySet()) {
            if (CommonUtil.isNotNullZero(userId) && CollUtil.isNotEmpty(signatureWuliuMap.get(userId))) {
                Lists.partition(signatureWuliuMap.get(userId), NumberConstant.TEN).forEach(v -> {
                    String wuliuLink = v.stream().map(x -> StrUtil.format("<a href='{}/mWuLiu/wuliuInfo?wuliuid={}'>{}</a>", moaUrl, x.getWuliuId(), x.getWuliuId())).collect(Collectors.joining("，"));
                    String sendMsg = StrUtil.format("您有快件（物流单{}），请尽快到三号平台一楼后门（九机快送）取件签收",wuliuLink);
                    smsService.sendOaMsg(sendMsg, null, Convert.toStr(userId), OaMesTypeEnum.WLTZ);
                });
            }
        }
    }

    /**
     * 邮寄订单固定发货人和电话
     * @param result
     */
    private void setWuliuSendInfo(WuliuAddressInfoRes result) {
        //邮寄订单固定发货人和电话：批签：20191118015，
        Areainfo sareainfo = areaInfoService.getById(result.getSareaid());
        if (Objects.nonNull(sareainfo) && Arrays.asList(0, 1).contains(sareainfo.getXtenant())) {
            result.setSaddress(sareainfo.getCompanyAddress());
            result.setScityid(sareainfo.getCityid());
            if (Objects.equals(NumUtil.ZERO, sareainfo.getXtenant())) {
                result.setSname(WuLiuConstant.JIUJI_NAME);
                //深圳
                if (Objects.equals(sareainfo.getId(), WuLiuConstant.AREA_SZ)) {
                    result.setSmobile("13760162987");
                }

                if (Objects.equals(sareainfo.getId(), WuLiuConstant.AREA_DC)) {
                    result.setSmobile(WuLiuConstant.DC_S_MOBLIE);
                } else if (Objects.equals(sareainfo.getId(), WuLiuConstant.AREA_DC1)) {
                    result.setSmobile("18508501195");
                } else {
                    result.setSmobile(sareainfo.getCompanyTel1());
                }
            } else {
                result.setSname(WuLiuConstant.YAYA_NAME);
                //ydc
                if (Objects.equals(result.getSareaid(), WuLiuConstant.AREA_YDC)) {
                    result.setSmobile(WuLiuConstant.DC_S_MOBLIE);
                } else {
                    result.setSmobile(sareainfo.getCompanyTel1());
                }
            }
        } else if (Objects.nonNull(sareainfo)) {
            result.setSaddress(sareainfo.getCompanyAddress());
            result.setScityid(sareainfo.getCityid());
            result.setSname(sareainfo.getPrintName());
            result.setSmobile(sareainfo.getCompanyTel1());
        }

        if (Objects.nonNull(result.getScityid())) {
            CityIdListDTO scity = wuLiuService.getAreaIdByCityId(result.getScityid(), 1);
            result.setSpid(scity.getPid());
            result.setSzid(scity.getZid());
            result.setSdid(scity.getDid());
        }
        if (Objects.nonNull(result.getRcityid())) {
            CityIdListDTO scity = wuLiuService.getAreaIdByCityId(result.getRcityid(), 1);
            result.setRpid(scity.getPid());
            result.setRzid(scity.getZid());
            result.setRdid(scity.getDid());
        }
    }

    /**
     * 设置默认快递方式
     * @param result
     */
    private void setWuliuComInfo(WuliuAddressInfoRes result, WuliuAddressInfoReq vo) {
        result.setOpenMeituanFlag(WuliuUtil.openMeituanFlag(result.getSareaid()));
        //寄件经纬度
        Coordinate sendCoordinate = wuliuAddressService.getAreaCoordinateV2(result.getSareaid(), result.getSaddress(), result.getScityid(), 1);
        //收件地址经纬度
        Coordinate receiveCoordinate = null;
        String raddressPosition = WuliuAddressUtil.getSubPosition(Builder.of(SubPositionReq::new)
                .with(SubPositionReq::setSubId, vo.getDanhaobind())
                .with(SubPositionReq::setAddress, result.getRaddress())
                .with(SubPositionReq::setWuType, vo.getWuType())
                .build());
        if (StringUtils.isNotBlank(raddressPosition)) {
            receiveCoordinate = new Coordinate(raddressPosition);
        }
        if (Objects.isNull(receiveCoordinate) || CoordinateUtil.outOfChina(receiveCoordinate.getLongitude(),receiveCoordinate.getLatitude())) {
            receiveCoordinate = wuliuAddressService.getAreaCoordinateV2(result.getRareaid(), result.getRaddress(), result.getRcityid(), 0);
        }
        Coordinate sendCoordinateGcj = CoordinateUtil.wgs2gcj(Optional.ofNullable(sendCoordinate).orElse(new Coordinate()));
        Coordinate receiveCoordinateGcj = CoordinateUtil.wgs2gcj(Optional.ofNullable(receiveCoordinate).orElse(new Coordinate()));
        //获取骑行距离
        TencentMapDrivingResVO tencentMapDriving = TencentMapUtil.tencentMapBicycling(sendCoordinateGcj.getLatitude() + "," + sendCoordinateGcj.getLongitude(), receiveCoordinateGcj.getLatitude() + "," + receiveCoordinateGcj.getLongitude());
        Long distance = Optional.ofNullable(tencentMapDriving).map(TencentMapDrivingResVO::getResult)
                .map(v -> v.getRoutes().stream().findFirst().orElse(new TencentMapDrivingResVO.ResultData.RouteData()))
                .map(TencentMapDrivingResVO.ResultData.RouteData::getDistance).orElse(null);
        //新机单和良品订单配送方式为第三方派送时，订单关联的派送物流单，若骑行距离小于4km，物流单的快递方式默认为美团
        if (distance != null && distance < 4000D) {
            result.setDefaultCom(WuLiuExpressEnum.MEI_TUAN.getCode());
        }
    }

    /**
     * 设置默认快递方式
     * @param model
     */
    private void setWuliuComInfo(WuLiuInfoReqVO model) {
        model.setOpenMeituanFlag(WuliuUtil.openMeituanFlag(model.getSareaid()));
        //寄件经纬度
        Coordinate sendCoordinate = wuliuAddressService.getAreaCoordinateV2(model.getSareaid(), model.getSAddress(), model.getSCityId(), 1);
        //收件地址经纬度
        Coordinate receiveCoordinate = null;
        String raddressPosition = WuliuAddressUtil.getSubPosition(Builder.of(SubPositionReq::new)
                .with(SubPositionReq::setSubId, model.getDanHaoBind())
                .with(SubPositionReq::setAddress, model.getRAddress())
                .with(SubPositionReq::setWuType, model.getWuType())
                .build());
        if (StringUtils.isNotBlank(raddressPosition)) {
            receiveCoordinate = new Coordinate(raddressPosition);
        }
        if (Objects.isNull(receiveCoordinate) || CoordinateUtil.outOfChina(receiveCoordinate.getLongitude(),receiveCoordinate.getLatitude())) {
            receiveCoordinate = wuliuAddressService.getAreaCoordinateV2(model.getRareaid(), model.getRAddress(), model.getRCityId(), 0);
        }
        Coordinate sendCoordinateGcj = CoordinateUtil.wgs2gcj(sendCoordinate);
        Coordinate receiveCoordinateGcj = CoordinateUtil.wgs2gcj(receiveCoordinate);
        //获取骑行距离
        TencentMapDrivingResVO tencentMapDriving = TencentMapUtil.tencentMapBicycling(sendCoordinateGcj.getLatitude() + "," + sendCoordinateGcj.getLongitude(), receiveCoordinateGcj.getLatitude() + "," + receiveCoordinateGcj.getLongitude());
        Long distance = Optional.ofNullable(tencentMapDriving).map(TencentMapDrivingResVO::getResult)
                .map(v -> v.getRoutes().stream().findFirst().orElse(new TencentMapDrivingResVO.ResultData.RouteData()))
                .map(TencentMapDrivingResVO.ResultData.RouteData::getDistance).orElse(null);
        //新机单和良品订单配送方式为第三方派送时，订单关联的派送物流单，若骑行距离小于4km，物流单的快递方式默认为美团
        if (distance != null && distance < 4000D) {
            model.setDefaultCom(WuLiuExpressEnum.MEI_TUAN.getCode());
        }
    }

    private String getUpdateWuliuLogComment(WuLiuEntity wuLiuEntity, WuLiuEntity oldWuliu) {
        StringBuilder sb = new StringBuilder();
        if(Objects.nonNull(wuLiuEntity.getSAreaId()) && !wuLiuEntity.getSAreaId().equals(oldWuliu.getSAreaId())) {
            sb.append("寄件门店由【");
            sb.append(Optional.ofNullable(areaInfoService.getAreaInfoByAreaId2(oldWuliu.getSAreaId())).orElse(new Areainfo()).getArea());
            sb.append("】改为【");
            sb.append(Optional.ofNullable(areaInfoService.getAreaInfoByAreaId2(wuLiuEntity.getSAreaId())).orElse(new Areainfo()).getArea());
            sb.append("】");
        }
        if(Objects.nonNull(wuLiuEntity.getRAreaId()) && !wuLiuEntity.getRAreaId().equals(oldWuliu.getRAreaId())) {
            sb.append("收件门店由【");
            sb.append(Optional.ofNullable(areaInfoService.getAreaInfoByAreaId2(oldWuliu.getRAreaId())).orElse(new Areainfo()).getArea());
            sb.append("】改为【");
            sb.append(Optional.ofNullable(areaInfoService.getAreaInfoByAreaId2(wuLiuEntity.getRAreaId())).orElse(new Areainfo()).getArea());
            sb.append("】");
        }
        if(Objects.nonNull(wuLiuEntity.getSCityId()) && !wuLiuEntity.getSCityId().equals(oldWuliu.getSCityId())) {
            sb.append("寄件城市信息由【");
            sb.append(Optional.ofNullable(wuLiuService.getAreaIdByCityId(oldWuliu.getSCityId(), 1)).orElse(new CityIdListDTO()).getDname());
            sb.append("】改为【");
            sb.append(Optional.ofNullable(wuLiuService.getAreaIdByCityId(wuLiuEntity.getSCityId(), 1)).orElse(new CityIdListDTO()).getDname());
            sb.append("】");
        }
        if(Objects.nonNull(wuLiuEntity.getRCityId()) && !wuLiuEntity.getRCityId().equals(oldWuliu.getRCityId())) {
            sb.append("寄件城市信息由【");
            sb.append(Optional.ofNullable(wuLiuService.getAreaIdByCityId(oldWuliu.getRCityId(), 1)).orElse(new CityIdListDTO()).getDname());
            sb.append("】改为【");
            sb.append(Optional.ofNullable(wuLiuService.getAreaIdByCityId(wuLiuEntity.getRCityId(), 1)).orElse(new CityIdListDTO()).getDname());
            sb.append("】");
        }
        if(StringUtils.isNotBlank(wuLiuEntity.getSName()) && !wuLiuEntity.getSName().equals(oldWuliu.getSName())) {
            sb.append("寄件人由【");
            sb.append(oldWuliu.getSName());
            sb.append("】改为【");
            sb.append(wuLiuEntity.getSName());
            sb.append("】");
        }
        if(Objects.nonNull(wuLiuEntity.getSMobile()) && !wuLiuEntity.getSMobile().equals(oldWuliu.getSMobile())) {
            sb.append("寄件电话由【");
            sb.append(oldWuliu.getSMobile());
            sb.append("】改为【");
            sb.append(wuLiuEntity.getSMobile());
            sb.append("】");
        }
        if(Objects.nonNull(wuLiuEntity.getSAddress()) && !wuLiuEntity.getSAddress().equals(oldWuliu.getSAddress())) {
            sb.append("寄件详细地址由【");
            sb.append(oldWuliu.getSAddress());
            sb.append("】改为【");
            sb.append(wuLiuEntity.getSAddress());
            sb.append("】");
        }
        if(StringUtils.isNotBlank(wuLiuEntity.getRName()) && !wuLiuEntity.getRName().equals(oldWuliu.getRName())) {
            sb.append("收件人由【");
            sb.append(oldWuliu.getRName());
            sb.append("】改为【");
            sb.append(wuLiuEntity.getRName());
            sb.append("】");
        }
        if(Objects.nonNull(wuLiuEntity.getRMobile()) && !wuLiuEntity.getRMobile().equals(oldWuliu.getRMobile())) {
            sb.append("收件电话由【");
            sb.append(oldWuliu.getRMobile());
            sb.append("】改为【");
            sb.append(wuLiuEntity.getRMobile());
            sb.append("】");
        }
        if(Objects.nonNull(wuLiuEntity.getRAddress()) && !wuLiuEntity.getRAddress().equals(oldWuliu.getRAddress())) {
            sb.append("收件详细地址由【");
            sb.append(oldWuliu.getRAddress());
            sb.append("】改为【");
            sb.append(wuLiuEntity.getRAddress());
            sb.append("】");
        }
        if(Objects.nonNull(wuLiuEntity.getCom()) && !wuLiuEntity.getCom().equals(oldWuliu.getCom())) {
            sb.append("快递公司由【");
            sb.append(oldWuliu.getCom());
            sb.append("】改为【");
            sb.append(wuLiuEntity.getCom());
            sb.append("】");
        }
        if(Objects.nonNull(wuLiuEntity.getNu()) && !wuLiuEntity.getNu().equals(oldWuliu.getNu())) {
            sb.append("快递单号由【");
            sb.append(oldWuliu.getNu());
            sb.append("】改为【");
            sb.append(wuLiuEntity.getNu());
            sb.append("】");
        }
        if(Objects.nonNull(wuLiuEntity.getComment()) && !wuLiuEntity.getComment().equals(oldWuliu.getComment())) {
            sb.append("备注由【");
            sb.append(oldWuliu.getComment());
            sb.append("】改为【");
            sb.append(wuLiuEntity.getComment());
            sb.append("】");
        }
        return sb.toString();
    }

    /**
     * subWLService.GetDefaultSenderInfo
     * 根据地区获取默认寄件人信息
     *
     * @param areaid
     * @return Map<String, String>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-10
     */
    private Map<String, String> getDefaultSenderInfo(Integer areaid) {
        Map<String, String> sender = new HashMap<>(2);
        if (areaid != null) {
            if (Objects.equals(areaid, WuLiuConstant.AREA_DC)) {
                sender.put("sname", WuLiuConstant.JIUJI_NAME);
                sender.put("smobile", "18860789139");
            } else if (Objects.equals(areaid, WuLiuConstant.AREA_DC1)) {
                sender.put("sname", "陶彪");
                sender.put("smobile", "18508501195");
            } else {
                List<Ch999User> dt = ch999UserService.listAllUserByAreaIdAndZhiWuList(Arrays.asList("主管", "店长", "副店长", "职员", "专员"), areaid);
                if (CollectionUtils.isNotEmpty(dt)) {
                    sender.put("sname", dt.get(0).getCh999Name());
                    sender.put("smobile", dt.get(0).getMobile());
                }
            }
        }
        return sender;
    }

    /**
     * subWLService.getSHIDbyYYID
     * 根据预约ID 获取 售后ID
     *
     * @param yuyueId
     * @return Integer
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-10
     */
    private Integer getShIdbyYyId(Integer yuyueId) {
        ShouHouYuYueEntity res = shouHouYuYueService.lambdaQuery()
                .eq(ShouHouYuYueEntity::getId, yuyueId)
                .one();
        if (Objects.isNull(res)) {
            return 0;
        } else {
            return res.getShouhouId();
        }
    }

    /**
     * 是否显示物流单报销按钮
     *
     * @param wuliuid
     * @param currentUser
     * @return Boolean
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-10
     */
    private Boolean isShowWuliuClaim(Integer wuliuid, OaUserBO currentUser) {
        WuLiuClaimEntity data = wuLiuService.getWuLiuClaim(wuliuid);

        if (data == null) {
            return false;
        }

        //已完成的物流单 手动创建保存的物流单不显示快递报销按钮
        if (Arrays.asList(WuLiuStatusEnum.FINISH.getCode(), WuLiuStatusEnum.SIGHED.getCode()).contains(Optional.ofNullable(data.getStats()).orElse(0)) && StringUtils.isBlank(data.getCom()) && !Boolean.TRUE.equals(data.getIsCreateManually())) {
            data.setSareaid(Optional.ofNullable(data.getSareaid()).orElse(0));
            data.setRareaid(Optional.ofNullable(data.getRareaid()).orElse(0));

            Integer sareaId = data.getSareaid() > 0 ? data.getSareaid() : data.getAreaid();
            Integer rareaId = data.getRareaid();
            if (Optional.ofNullable(data.getId()).orElse(0) > 0) {
                return Objects.equals(sareaId, currentUser.getArea1id()) || Objects.equals(rareaId, currentUser.getArea1id()) || (data.getRareaid() > 0 && Objects.equals(data.getRareaid(), currentUser.getArea1id())) || currentUser.getRank().contains("wldl");
            } else {
                //物流单自主报销逻辑调整 https://jiuji.yuque.com/staff-fwzd6v/cvecc8/lkmf1biolkahci04?#
                if (Arrays.asList(WuLiuTypeEnum.ORDER.getCode(),WuLiuTypeEnum.ORDER_EXPRESS.getCode(),WuLiuTypeEnum.FOURTEEN_DAY.getCode()).contains(data.getWutype())) {
                    if (!Objects.equals(SubDeliveryEnum.THIRD_PARTY.getCode(), data.getDelivery())) {
                        return false;
                    }
                }

                //物流单完成时间（物流日志中记录为）距当前时间不超过48小时显示快递报销按钮
                if (data.getDtime() != null && LocalDateTime.now().isBefore(data.getDtime().plusDays(2))) {
                    return Objects.equals(sareaId, currentUser.getArea1id()) || Objects.equals(rareaId, currentUser.getArea1id());
                }
            }
        }
        return false;
    }

    /**
     * subWLService.getLogs
     * 对物流操作日志序列赋值
     *
     * @param model
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-10
     */
    private void getLogs(WuLiuInfoReqVO model) {
        model.setLogs(wuLiuLogsService.getWuLiuLog(model.getWuliuid())
                .stream().map(item ->
                        new WuLiuLogResVO()
                                .setWuliuid(item.getWuliuid())
                                .setDtime(item.getDtime())
                                .setInuser(item.getInuser())
                                .setMsg(urlEncode(Optional.ofNullable(item.getMsg())
                                        .orElse("")))
                                .setId(item.getId()))
                .collect(Collectors.toList()));
    }

    /**
     * setWuLiuInfoReqVO
     *
     * @param vo
     */
    private void setWuLiuInfoReqVO(WuLiuInfoReqVO vo, WuLiuDTO dto) {
        String expressType = "";
        if (StrUtil.isNotBlank(dto.getNu())) {
            if (StringUtils.isBlank(dto.getExpressType()) && WuLiuConstant.SHUNFENG.equals(dto.getCom())) {
                expressType = getExpressType(dto.getId());
            } else {
                expressType = dto.getExpressType();
            }
        }
        vo.setSName(dto.getSName());
        vo.setSMobile(dto.getSMobile());
        vo.setSAddress(dto.getSAddress());
        vo.setSareaid(dto.getSAreaId());
        vo.setSCityId(dto.getSCityId());
        vo.setRName(dto.getRName());
        vo.setRMobile(dto.getRMobile());
        vo.setRAddress(dto.getRAddress());
        vo.setRareaid(dto.getRAreaId());
        vo.setRCityId(dto.getRCityId());
        vo.setAreaId(dto.getAreaId());
        vo.setDTime(dto.getDTime());
        vo.setCTime(dto.getDTime());
        vo.setWeight(dto.getWeight());
        vo.setPrice(dto.getPrice());
        vo.setInPrice(dto.getInPrice());
        vo.setShouJianRen(dto.getShouJianRen());
        vo.setPaiJianRen(dto.getPaiJianRen());
        vo.setStats(dto.getStats());
        vo.setDanHaoBind(dto.getDanHaoBind());
        vo.setWuType(dto.getWuType());
        vo.setComment(dto.getComment());
        vo.setCom(dto.getCom());
        vo.setNu(dto.getNu());
        vo.setPayMethod(dto.getPayMethod());
        vo.setWuliuid(dto.getId());
        vo.setSubKinds(dto.getSubKinds());
        if (Objects.nonNull(dto.getLinkType())) {
            vo.setLinkType(dto.getLinkType() != null ? String.valueOf(dto.getLinkType()) : "");
        }
        if (Objects.nonNull(dto.getSAreaId())) {
            vo.setSAreaM(areaInfoService.getAreaShotName(vo.getSareaid()));
        }
        if (Objects.nonNull(dto.getRAreaId())) {
            vo.setRAreaM(areaInfoService.getAreaShotName(vo.getRareaid()));
        }
        vo.setExpressType(expressType);
        vo.setIsExceptionSub(dto.getIsAbnomal());
        vo.setExceptionRemark(dto.getRemark());
        vo.setIsCreateManually(dto.getIsCreateManually());
        boolean bool = Arrays.asList(WuLiuTypeConstant.ORDER, WuLiuTypeConstant.ORDER_EXPRESS).contains(dto.getWuType());
        if (bool && Optional.ofNullable(dto.getDanHaoBind()).orElse(0) > 0) {
            vo.setMeiTuanFastFlag(wuLiuService.isFastMeiTuan(vo.getDanHaoBind()));
            vo.setExpectTime(wuLiuService.getExpectTime(vo.getDanHaoBind()));
        }
        if (WuLiuTypeEnum.FOURTEEN_DAY.getCode().equals(dto.getWuType()) && Optional.ofNullable(dto.getDanHaoBind()).orElse(0) > 0) {
            vo.setExpectTime(wuLiuService.getLiangpinExpectTime(vo.getDanHaoBind()));
        }
        vo.setWuPid(dto.getWPid());
        vo.setWCateId(dto.getWCateId());
    }

    /**
     * 根据分类获取id
     *
     * @param wuliuid
     * @return string
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-04
     */
    public String getExpressType(int wuliuid) {
        String ename = wuLiuWuliuwangdianService.getExpressType(wuliuid);
        return wuLiuService.getExepressType(ename);
    }

}
