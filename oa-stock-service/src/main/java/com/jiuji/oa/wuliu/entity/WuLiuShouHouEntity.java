package com.jiuji.oa.wuliu.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 售后主表,责任小组：销售 Entity
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-08
 */
@Data
@Accessors(chain = true)
@TableName("shouhou")
public class WuLiuShouHouEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Integer id;

    /**
     * 机型
     */
    private String name;

    /**
     * 配置
     */
    @TableField("peizhi")
    private String peizhi;

    /**
     * 故障
     */
    private String problem;

    /**
     * 备注
     */
    private String comment;

    /**
     * 用户名
     */
    private String username;

    /**
     * 手机
     */
    private String mobile;

    /**
     * 电话
     */
    private String tel;

    /**
     * 状态
     */
    private Short stats;

    /**
     * 保修期 0不在 1在 4检测换机 3待检测 2外修
     */
    @TableField("baoxiu")
    private Short baoXiu;

    /**
     * 接件人
     */
    @TableField("inuser")
    private String inUser;

    /**
     * 串号
     */
    private String imei;

    /**
     * 是否删除 1正常 0删除
     */
    @TableField("xianshi")
    private Boolean xianShi;

    /**
     * 测试时间
     */
    @TableField("contentcsdate")
    private LocalDateTime contentTestDate;

    /**
     * 订单购买时间
     */
    @TableField("tradedate")
    private LocalDateTime tradeDate;

    /**
     * 接件时间
     */
    @TableField("modidate")
    private LocalDateTime modiDate;

    /**
     * 维修费
     */
    @TableField("feiyong")
    private BigDecimal feiYong;

    /**
     * 维修成本
     */
    @TableField("costprice")
    private BigDecimal costPrice;

    /**
     * 维修人
     */
    @TableField("weixiuren")
    private String weiXiuRen;

    /**
     * 代用机ID
     */
    @TableField("dyjid")
    private Integer standbyMachineId;

    /**
     * 取机时间
     */
    @TableField("offtime")
    private LocalDateTime offTime;

    /**
     * 接件地区
     */
    private String area;

    /**
     * 收银锁定
     */
    @TableField("shouyinglock")
    private Integer shouYingLock;

    /**
     * 收银时间
     */
    @TableField("shouyingdate")
    private LocalDateTime shouYingDate;

    /**
     * 收银人
     */
    @TableField("shouyinguser")
    private String shouYingUser;

    /**
     * 购买用户ID
     */
    @TableField("userid")
    private Long userId;

    /**
     * 地区分类 bd本地 dz地州
     */
    private String kinds;

    /**
     * 是否提成
     */
    @TableField("isticheng")
    private Boolean isTiCheng;

    /**
     * 外观描述
     */
    @TableField("waiguan")
    private String waiGuan;

    /**
     * 跟进时间
     */
    @TableField("result_dtime")
    private LocalDateTime resultTime;

    /**
     * 是否软件接件
     */
    @TableField("issoft")
    private Boolean isSoft;

    /**
     * 修好时间
     */
    @TableField("modidtime")
    private LocalDateTime modiTime;

    /**
     * 商品id
     */
    @TableField("product_id")
    private Long productId;

    /**
     * 商品规格
     */
    @TableField("product_color")
    private String productColor;

    /**
     * 购买地区
     */
    @TableField("buyarea")
    private String buyArea;

    /**
     * 是否盘点
     */
    @TableField("pandian")
    private Boolean panDian;

    /**
     * 盘点时间
     */
    @TableField("pandiandate")
    private LocalDateTime panDianDate;

    /**
     * 转到地区id
     */
    @TableField("toarea")
    private String toArea;

    /**
     * 是否退款
     */
    @TableField("istui")
    private Boolean isTui;

    /**
     * 盘点人
     */
    @TableField("pandianinuser")
    private String panDianInUser;

    /**
     * ppid
     */
    @TableField("ppriceid")
    private Integer ppriceId;

    /**
     * 库存id（大件or小件？）
     */
    @TableField("mkc_id")
    private Integer mkcId;

    /**
     * 是否快速
     */
    @TableField("isquick")
    private Boolean isQuick;

    /**
     * 维修次数
     */
    @TableField("wcount")
    private Integer weiXiuCount;

    /**
     * 维修组ID
     */
    @TableField("weixiuzuid")
    private Short weixiuzuid;

    /**
     * 维修组记录
     */
    @TableField("weixiuzuid_jl")
    private Short weiXiuZuIdJiLu;

    /**
     * 是否维修
     */
    @TableField("isweixiu")
    private Boolean isWeiXiu;

    /**
     * 维修组完成时间
     */
    @TableField("weixiudtime")
    private LocalDateTime weiXiuTime;

    /**
     * 维修组分配时间
     */
    @TableField("weixiu_startdtime")
    private LocalDateTime weiXiuStartTime;

    /**
     * 短单号
     */
    @TableField("orderid")
    private String orderId;

    /**
     * 是否取机
     */
    @TableField("isquji")
    private Boolean isQuJi;

    /**
     * 是否返修
     */
    @TableField("isfan")
    private Boolean isFan;

    /**
     * 评价类型
     */
    @TableField("pingjia")
    private Short pingJia;

    /**
     * 评价类型1
     */
    @TableField("pingjia1")
    private Short pingJia1;

    /**
     * 对应订单id
     */
    private Integer subId;

    /**
     * 1 修 2换 3 退
     */
    @TableField("webtype1")
    private Short webType1;

    /**
     * 1预约到店  2上门取机  3快递至三九
     */
    @TableField("webtype2")
    private Short webType2;

    /**
     * 网站状态 1 未审核 2 已审核 3 已收到 4 已发出 5已完成
     */
    @TableField("webstats")
    private Short webStats;

    /**
     * 服务方式
     */
    @TableField("ServiceType")
    private Short serviceType;

    /**
     * 对应basket_id
     */
    @TableField("basket_id")
    private Integer basketId;

    /**
     * 是否回购机 1是，0不是
     */
    @TableField("ishuishou")
    private Short isHuiShou;

    /**
     * 预约id
     */
    @TableField("yuyueid")
    private Integer yuYueId;

    /**
     * 回执单打印次数
     */
    @TableField("huiprint")
    private Integer huiPrint;

    /**
     * 维修人时间
     */
    @TableField("weixiurentime")
    private LocalDateTime weiXiuRenTime;

    /**
     * 维修人
     */
    @TableField("reweixiuren")
    private Boolean reWeiXiuRen;

    /**
     * 送修人姓名
     */
    @TableField("sxname")
    private String songXiuName;

    /**
     * 送修人手机
     */
    @TableField("sxmobile")
    private String songXiuMobile;

    /**
     * 送修人性别
     */
    @TableField("sxsex")
    private Short songXiuSex;

    /**
     * 送修人id
     */
    @TableField("sxuserid")
    private Integer songXiuUserId;

    /**
     * 锁屏密码
     */
    @TableField("lockpwd")
    private String lockPwd;

    /**
     * 测试人
     */
    @TableField("testuser")
    private String testUser;

    /**
     * 处理方式 1修 2换 3多 4送 5显示总成置换 6快
     */
    @TableField("wxkind")
    private Integer wxKind;

    /**
     * 维修配件
     */
    @TableField("wxConfig")
    private String wxConfig;

    /**
     * 通知时间
     */
    @TableField("noticetime")
    private LocalDateTime noticeTime;

    /**
     * 测试时间
     */
    @TableField("testtime")
    private LocalDateTime testTime;

    /**
     * 设备账号/ID
     */
    @TableField("deviceid")
    private String deviceId;

    /**
     * 设备密码
     */
    @TableField("devicepwd")
    private String devicePwd;

    /**
     * 优惠码
     */
    @TableField("youhuima")
    private String youHuiMa;

    /**
     * 接件确认
     */
    @TableField("yuyueCheck")
    private Boolean yuYueCheck;

    /**
     * 是否瑕疵机
     */
    @TableField("isXcMkc")
    private Boolean isXcMkc;

    /**
     * 瑕疵机来源
     */
    @TableField("isXcMkcInfo")
    private String isXcMkcInfo;

    /**
     * 维修测试时间
     */
    @TableField("wxTestTime")
    private LocalDateTime wxTestTime;

    /**
     * 维修测试信息
     */
    @TableField("wxTestInfo")
    private String wxTestInfo;

    /**
     * 维修等级
     */
    @TableField("RepairLevel")
    private Short repairLevel;

    /**
     * 门店id
     */
    @TableField("areaid")
    private Integer areaId;

    /**
     * 转地区id
     */
    @TableField("toareaid")
    private Integer toAreaId;

    /**
     * 购买地区id
     */
    @TableField("buyareaid")
    private Integer buyAreaId;

    /**
     * 1:测试通过 2;测试不通过
     */
    @TableField("wxTestStats")
    private Short wxTestStats;

    /**
     * 跟进人
     */
    @TableField("gjUser")
    private String gjUser;

    /**
     * 业务处理状态
     */
    @TableField("ProcessConfirmStats")
    private String processConfirmStats;

    /**
     * 旧id
     */
    @TableField("oldshouhouid")
    private Integer oldShouHouId;

    /**
     * 是否备份数据
     */
    @TableField("isBakData")
    private Short isBakData;

    /**
     * 是否解绑QQ/微信
     */
    @TableField("isjbanwxqq")
    private Boolean isJieBangWxQq;

    /**
     * 业务确认人
     */
    @TableField("yuyueCheckuser")
    private String yuYueCheckUser;

    /**
     * 取机通知时间
     */
    @TableField("qujitongzhitime")
    private LocalDateTime quJiTongZhiTime;

    /**
     * 倒计时
     */
    @TableField("daojishi")
    private Integer daoJiShi;

    /**
     * 手机验证码
     */
    @TableField("codeMsg")
    private String codeMsg;

    /**
     * 跟进人
     */
    private String resultUser;

    /**
     * 短信发送时间
     */
    @TableField("smstime")
    private LocalDateTime smsTime;

    /**
     * 电话跟进时间
     */
    @TableField("teltime")
    private LocalDateTime telTime;

    /**
     *
     */
    @TableField("EarnestMoneySubid")
    private Integer earnestMoneySubId;

    /**
     * 服务出险人
     */
    @TableField("serversOutUser")
    private String serversOutUser;

    /**
     * 服务出险时间
     */
    @TableField("serversOutDtime")
    private LocalDateTime serversOutTime;

    /**
     * 优惠费用
     */
    @TableField("youhuifeiyong")
    private BigDecimal youHuiFeiYong;

    /**
     * 姓名
     */
    @TableField("truename")
    private String trueName;

    /**
     * 是否中邮
     */
    @TableField("iszy")
    private Boolean isZy;

    /**
     * 维修地区id
     */
    @TableField("wxAreaid")
    private Integer wxAreaId;

    /**
     * 接件拍照图
     */
    @TableField("imeifid")
    private String imeiFid;

    /**
     * 已付金额
     */
    @TableField("yifum")
    private BigDecimal yiFuMoney;

    /**
     * 是否是快修
     */
    @TableField("kuaixiuFlag")
    private Short kuaiXiuFlag;

    /**
     * 快修提示消息推送时间
     */
    @TableField("kuaixiuSendTime")
    private LocalDateTime kuaiXiuSendTime;

    /**
     * 是否是赠品
     */
    @TableField("iszp")
    private Boolean isZp;

    /**
     * 物流
     */
    @TableField("wuliyou")
    private String wuLiu;

    /**
     * 额外服务类型， 1=手机清洁服务
     */
    @TableField("mobileServeiceType")
    private Short mobileServiceType;

    /**
     * 良品配置情况
     */
    @TableField("lppeizhi")
    private Short liangPingPeiZhi;

    /**
     * 原维修单
     */
    @TableField("fromshouhouid")
    private Integer fromShouHouId;

    /**
     * 服务成本
     */
    @TableField("serviceCostprice")
    private BigDecimal serviceCostPrice;

    /**
     * 故障类型
     */
    private String questionType;

    /**
     * 凭证号
     */
    @TableField("pzid")
    private Integer certId;

    /**
     *
     */
    @TableField("voucherId")
    private Integer voucherId;

    /**
     * 品牌ID
     */
    private Integer brandId;

    /**
     * 退货备注
     */
    private String refundRemark;

    /**
     * 接件来源
     */
    private Short orderSource;

    /**
     *
     */
    @TableField("shouhou_rv")
    private Byte[] shouHouRv;
}
