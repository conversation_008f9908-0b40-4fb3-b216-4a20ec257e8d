package com.jiuji.oa.wuliu.enums;

import cn.hutool.core.convert.Convert;
import com.jiuji.oa.nc.abnormal.vo.ShowPrintingEnumVOV2;
import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 销售单状态枚举
 *
 * <AUTHOR>
 * @date 2021/10/09
 */
@Getter
@AllArgsConstructor
public enum DeliveryEnum  implements CodeMessageEnumInterface {
    /**
     * 登记时间
     */
    AREA_SELF_PICK(1,"到店自取"),
    JIUJI_FAST(2,"九机快送"),
    SELF_PICK_POINT(3,"自提点"),
    EXPRESS_TRANSFER(4,"快递运输"),
    HURRY_DELIVERY(5,"加急配送"),
    THIRD_TRANSFER(6,"第三方派送")
    ;

    /**
     * 代码
     */
    private Integer code;
    /**
     * 消息
     */
    private String message;



    public static String getDeliveryEnum(String code) {
        for (DeliveryEnum enums : DeliveryEnum.values()) {
            if (enums.getCode().equals(Convert.toInt(code))) {
                return enums.getMessage();
            }
        }
        return "";
    }

    public static List<ShowPrintingEnumVOV2> getAllPrintingEnum() {
        DeliveryEnum[] array = DeliveryEnum.values();
        List<ShowPrintingEnumVOV2> arrayList = new ArrayList<>();
        for (DeliveryEnum t : array) {
            ShowPrintingEnumVOV2 ShowPrintingEnumVOV2 = new ShowPrintingEnumVOV2()
                    .setLabel(t.getMessage())
                    .setValue(Convert.toStr(t.getCode()));
            arrayList.add(ShowPrintingEnumVOV2);
        }
        return arrayList;
    }
}
