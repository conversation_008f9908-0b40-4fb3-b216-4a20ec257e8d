package com.jiuji.oa.wuliu.vo;

import com.jiuji.oa.stock.logistics.order.vo.req.FileReq;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/2 15:04
 */
@Data
@NoArgsConstructor
public class WuliuClaimformRes {

    private Integer id;
    /**
     * 物流单号
     */
    private Integer wuliuId;


    /**
     * 报销金额
     */
    private BigDecimal amount;


    /**
     * 跑腿单号
     */
    private String trackingNo;


    /**
     * 订单类型
     */
    private Integer subType;

    /**
     * 订单类型
     */
    private String subTypeName;

    /**
     * 关联单号
     */
    private Long subId;

    /**
     * 下单时间
     */
    private LocalDateTime orderTime;

    /**
     * 里程(公里)
     */
    private Double mileage;

    /**
     * 快递方式
     */
    private String courier;

    /**
     * 状态
     */
    private Integer status;
    /**
     * 状态
     */
    private String statusName;

    /**
     * 状态
     */
    private LocalDateTime checkTime;

    /**
     * 审批人
     */
    private Integer checkerId;

    /**
     * 当月已报金额
     */
    private BigDecimal currentAmount;

    /**
     * 当月已报次数
     */
    private Integer currentCount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 微信头像
     */
    private String wxHeadImg;

    /**
     * 微信名称
     */
    private String wxName;

    /**
     * 附件
     */
    private List<FileReq> file ;

}
