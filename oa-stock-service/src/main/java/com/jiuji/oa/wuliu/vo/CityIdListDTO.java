package com.jiuji.oa.wuliu.vo;


import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * CityIDList
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-13
 */
@Data
@Accessors(chain = true)
public class CityIdListDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 省份 ID
     */
    @JsonProperty("pid")
    @JSONField(name = "pid")
    private Integer pid;

    /**
     * 省份名
     */
    @JsonProperty("pname")
    @JSONField(name = "pname")
    private String pname;

    /**
     * 市 ID
     */
    @JsonProperty("zid")
    @JSONField(name = "zid")
    private Integer zid;

    /**
     * 市名
     */
    @JsonProperty("zname")
    @JSONField(name = "zname")
    private String zname;

    /**
     * 区域 ID
     */
    @JsonProperty("did")
    @JSONField(name = "did")
    private Integer did;

    /**
     * 区域名
     */
    @JsonProperty("dname")
    @JSONField(name = "dname")
    private String dname;

    /**
     * 街道 ID
     */
    @JsonProperty("streetId")
    @JSONField(name = "streetId")
    private Integer streetId;

    /**
     * 街道名
     */
    @JsonProperty("streetName")
    @JSONField(name = "streetName")
    private String streetName;

    /**
     * 详细地址
     */
    @JsonProperty("address")
    @JSONField(name = "address")
    private String address;

}
