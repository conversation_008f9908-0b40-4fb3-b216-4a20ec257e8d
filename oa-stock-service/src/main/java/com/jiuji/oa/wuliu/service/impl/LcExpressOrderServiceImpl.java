package com.jiuji.oa.wuliu.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.nc.common.enums.XtenantEnum;
import com.jiuji.oa.wuliu.entity.LcExpressOrder;
import com.jiuji.oa.wuliu.service.LcExpressOrderService;
import com.jiuji.oa.wuliu.mapper.LcExpressOrderMapper;
import org.springframework.stereotype.Service;

/**
 *
 */
@Service
@DS("oa_nc")
public class LcExpressOrderServiceImpl extends ServiceImpl<LcExpressOrderMapper, LcExpressOrder>
implements LcExpressOrderService{


    /**
     * 查询快递信息
     *
     * @param waybillNo
     * @return
     */
    @Override
    public LcExpressOrder findExpressOrderByWaybillNo(String waybillNo) {
        if (XtenantEnum.isSaasXtenant()) {
            return null;
        }
        return this.baseMapper.selectExpressOrderByWaybillNo(waybillNo);
    }
}




