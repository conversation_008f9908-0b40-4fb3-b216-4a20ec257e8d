package com.jiuji.oa.wuliu.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * authConfigServices.dcAreaModel
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-16
 */
@Data
@Accessors(chain = true)
public class DcAreaModelDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("areaid")
    @JSONField(name = "areaid")
    private Integer areaid;

    @JsonProperty("authId")
    @JSONField(name = "authId")
    private Integer authId;

}
