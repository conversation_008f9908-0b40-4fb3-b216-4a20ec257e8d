package com.jiuji.oa.wuliu.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.wuliu.entity.WuLiuSubSeparateConfigEntity;

import java.util.List;

/**
 * 服务类
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-15
 */
public interface IWuLiuSubSeparateConfigService extends IService<WuLiuSubSeparateConfigEntity> {

    /**
     * OA 生产库各类订单最小单号查询
     *
     * @return List<WuLiuSubSeparateConfigEntity>
     * @date 2021-10-15
     * <AUTHOR> [<EMAIL>]
     */
    List<WuLiuSubSeparateConfigEntity> subSeparateConfigCache();

}
