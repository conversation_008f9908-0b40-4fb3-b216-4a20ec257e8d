package com.jiuji.oa.wuliu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 
 * @TableName wuliu_express_time
 */
@TableName(value ="wuliu_express_time")
@Data
public class WuliuExpressTime implements Serializable {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 物流单id
     */
    @TableField(value = "wuliuid")
    private Integer wuliuid;

    /**
     * 快递单号
     */
    @TableField(value = "wuliuNo")
    private String wuliuNo;

    /**
     * 快递生成时间
     */
    @TableField(value = "express_generate_time")
    private LocalDateTime expressGenerateTime;

    /**
     * 快递签收时间
     */
    @TableField(value = "express_receive_time")
    private LocalDateTime expressReceiveTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}