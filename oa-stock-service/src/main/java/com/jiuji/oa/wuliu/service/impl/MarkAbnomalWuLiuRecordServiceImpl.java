package com.jiuji.oa.wuliu.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.wuliu.entity.MarkAbnomalWuLiuRecord;
import com.jiuji.oa.wuliu.mapper.MarkAbnomalWuLiuRecordMapper;
import com.jiuji.oa.wuliu.service.IMarkAbnomalWuLiuRecordService;
import org.springframework.stereotype.Service;

/**
 *
 *
 * <AUTHOR>
 * @since 2021-10-13
 */
@Service
@DS("oanewWrite")
public class MarkAbnomalWuLiuRecordServiceImpl extends ServiceImpl<MarkAbnomalWuLiuRecordMapper, MarkAbnomalWuLiuRecord> implements IMarkAbnomalWuLiuRecordService {
}
