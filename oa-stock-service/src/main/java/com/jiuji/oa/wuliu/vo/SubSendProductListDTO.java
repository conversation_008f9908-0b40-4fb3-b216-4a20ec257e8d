package com.jiuji.oa.wuliu.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR> [<EMAIL>]
 * @date 2021-11-09
 */
@Data
@Accessors(chain = true)
public class SubSendProductListDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 大件编号
     */
    @JsonProperty("orderid")
    @JSONField(name = "orderid")
    public String orderid;

    /**
     * imei,串号
     */
    @JsonProperty("imei")
    @JSONField(name = "imei")
    public String imei;

    /**
     * 库存状态
     */
    @JsonProperty("kc_check")
    @JSONField(name = "kc_check")
    public Integer kcCheck;

    @JsonProperty("basket_id")
    @JSONField(name = "basket_id")
    public Integer basketId;

    @JsonProperty("basket_count")
    @JSONField(name = "basket_count")
    public Integer basketCount;

    @JsonProperty("price")
    @JSONField(name = "price")
    public BigDecimal price;

    @JsonProperty("ismobile")
    @JSONField(name = "ismobile")
    public Boolean ismobile;

    @JsonProperty("beiCount")
    @JSONField(name = "beiCount")
    public Integer beiCount;

    @JsonProperty("isna")
    @JSONField(name = "isna")
    public Integer isna;

    @JsonProperty("product_peizhi")
    @JSONField(name = "product_peizhi")
    public String productPeizhi;

    @JsonProperty("productName")
    @JSONField(name = "productName")
    public String productName;

    @JsonProperty("basket_date")
    @JSONField(name = "basket_date")
    public LocalDateTime basketDate;

    /**
     * PPID
     */
    @JsonProperty("ppriceid")
    @JSONField(name = "ppriceid")
    public Long ppriceid;

    /**
     * 销售人
     */
    @JsonProperty("seller")
    @JSONField(name = "seller")
    public String seller;

    /**
     * 成本价
     */
    @JsonProperty("inprice")
    @JSONField(name = "inprice")
    public BigDecimal inprice;

    /**
     * 派送单类别 1:订单 2:售后单  3 回  4 集市  5   良品  6  发票派送 7 租机派送
     */
    @JsonProperty("pstype")
    @JSONField(name = "pstype")
    public Integer pstype;

    @JsonProperty("wuliuid")
    @JSONField(name = "wuliuid")
    public Integer wuliuid;

    /**
     * 12 良品  13 用于标识发票 14 租机
     */
    @JsonProperty("lktp")
    @JSONField(name = "lktp")
    public Integer lktp;

    @JsonProperty("basket_type")
    @JSONField(name = "basket_type")
    public Integer basketType;

}
