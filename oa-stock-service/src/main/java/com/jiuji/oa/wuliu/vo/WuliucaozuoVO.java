package com.jiuji.oa.wuliu.vo;


import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * WuliucaozuoVO
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-11-04
 */
@Data
@Accessors(chain = true)
@ApiModel("wuliucaozuo")
public class WuliucaozuoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 操作标识
     */
    @JsonProperty("actionName")
    private String actionName;

    /**
     * 物流ID
     */
    @JsonProperty("wuliuid")
    private Integer wuliuid;

    /**
     * 状态
     */
    @JsonProperty("stats")
    private Integer stats;

    /**
     * 收件人
     */
    @JsonProperty("shoujianren")
    private String shoujianren;

    /**
     * 派件人
     */
    @JsonProperty("paijianren")
    private String paijianren;

    @JsonProperty("raddress")
    private String raddress;

    /**
     * 默认 "1"
     */
    private String type;

    /**
     *
     */
    private String isapp;

}
