package com.jiuji.oa.wuliu.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("expressEnum")
public class ExpressEnumEntity extends Model<ExpressEnumEntity> {

    private static final long serialVersionUID = 6389601251065940532L;

    private Long id;
    private String expressName;
    private String expressCode;
    private String expressTel;

    @TableLogic
    @TableField("isdel")
    private Boolean del;
    private Integer showRank;
    private Integer expressType;
    private Integer status;

    @Override
    protected Serializable pkVal() {
        return null;
    }

}
