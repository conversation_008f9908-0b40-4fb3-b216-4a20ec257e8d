package com.jiuji.oa.wuliu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 订单团单状态记录表,责任小组：销售 实体类
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-18
 */
@Data
@Accessors(chain = true)
@TableName("subFlagRecord")
@ApiModel(value = "WuLiuSubFlagRecordEntity 实体类", description = "订单团单状态记录表,责任小组：销售 实体类")
public class WuLiuSubFlagRecordEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * @see DeclareStateEnum
     */
    @TableField("status")
    private Integer status;

    /**
     * 申报文案
     */
    @TableField("status_comment")
    private String statusComment;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("sub_id")
    private Long subId;

    @TableField("dtime")
    private LocalDateTime dtime;

    @TableField("flagType")
    private Integer flagType;

    /**
     * 审核人
     */
    @TableField("check_user")
    private String checkUser;

    /**
     * 审核时间
     */
    @TableField("check_time")
    private LocalDateTime checkTime;

    /**
     * 审核状态
     * @see SubFlagRecordStateEnum
     */
    @TableField("check_state")
    private Integer checkState;


    /**
     * 财务审核人
     */
    @TableField("finance_check_user")
    private String financeCheckUser;

    /**
     * 财务审核时间
     */
    @TableField("finance_check_time")
    private LocalDateTime financeCheckTime;

    /**
     * 财务 审核状态
     * @see FinanceCheckStateEnum
     */
    @TableField("finance_check_state")
    private Integer financeCheckState;



    /**
     * 运营审核人
     */
    @TableField("operation_check_user")
    private String operationCheckUser;

    /**
     * 运营审核时间
     */
    @TableField("operation_check_time")
    private LocalDateTime operationCheckTime;

    /**
     * 运营审核状态
     * @see com.jiuji.oa.wuliu.enums.OperationStateEnum
     */
    @TableField("operation_check_state")
    private Integer operationCheckState;



    /**
     * 审核备注
     */
    @TableField("check_comment")
    private String checkComment;




}