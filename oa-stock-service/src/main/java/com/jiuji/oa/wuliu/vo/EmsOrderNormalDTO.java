package com.jiuji.oa.wuliu.vo;


import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-16
 */
@Data
@Accessors(chain = true)
public class EmsOrderNormalDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private static final int TWELVE = 12;

    @JsonProperty("created_time")
    @JSONField(name = "created_time")
    private String createdTime;

    @JsonProperty("logistics_provider")
    @JSONField(name = "logistics_provider")
    private String logisticsProvider = "B";

    @JsonProperty("ecommerce_no")
    @JSONField(name = "ecommerce_no")
    private String ecommerceNo;

    @JsonProperty("ecommerce_user_id")
    @JSONField(name = "ecommerce_user_id")
    private Integer ecommerceUserId;

    @JsonProperty("sender_type")
    @JSONField(name = "sender_type")
    private String senderType;

    /**
     * 大客户编号
     */
    @JsonProperty("sender_no")
    @JSONField(name = "sender_no")
    private String senderNo;

    @JsonProperty("inner_channel")
    @JSONField(name = "inner_channel")
    private String innerChannel;

    @JsonProperty("logistics_order_no")
    @JSONField(name = "logistics_order_no")
    private String logisticsOrderNo;

    @JsonProperty("contents_attribute")
    @JSONField(name = "contents_attribute")
    private String contentsAttribute;

    @JsonProperty("base_product_no")
    @JSONField(name = "base_product_no")
    private String baseProductNo;

    @JsonProperty("biz_product_no")
    @JSONField(name = "biz_product_no")
    private String bizProductNo;

    @JsonProperty("product_type")
    @JSONField(name = "product_type")
    private String productType;

    /**
     * 保险保价标志  0：保险保价1:基本2:保价3:保险
     */
    @JsonProperty("insurance_flag")
    @JSONField(name = "insurance_flag")
    private String insuranceFlag;

    /**
     * 揽收方式 揽收方式：0 客户送货上门，1 机构上门揽收
     */
    @JsonProperty("pickup_type")
    @JSONField(name = "pickup_type")
    private String pickupType;

    /**
     * 付款方式  1:寄件人 2:收件人 3:第三方 4:收件人集中付费 5:免费 6:寄/收件人 7:预付卡
     */
    @JsonProperty("payment_mode")
    @JSONField(name = "payment_mode")
    private String paymentMode;

    /**
     * 代收款标志 1:代收货款2:代缴费9:无
     */
    @JsonProperty("cod_flag")
    @JSONField(name = "cod_flag")
    private String codFlag;

    /**
     * 回单标识1:基本2:回执 3:短信 5:电子返单 6:格式返单7: 自备返单8: 反向返单
     */
    @JsonProperty("receipt_flag")
    @JSONField(name = "receipt_flag")
    private String receiptFlag;

    /**
     * 贵品标识:0 无 1有
     */
    @JsonProperty("valuable_flag")
    @JSONField(name = "valuable_flag")
    private String valuableFlag;

    /**
     * 收件人安全码
     */
    @JsonProperty("sender_safety_code")
    @JSONField(name = "sender_safety_code")
    private String senderSafetyCode;

    @JsonProperty("sender")
    @JSONField(name = "sender")
    private OrderSenderDTO sender;

    @JsonProperty("receiver")
    @JSONField(name = "receiver")
    private OrderReceiverDTO receiver;

    public String getEcommerceNo() {
        return ecommerceNo == null ? "JIUJI" : ecommerceNo;
    }

    public Integer getEcommerceUserId() {
        return ecommerceUserId == null ? TWELVE : ecommerceUserId;
    }

    public String getSenderSafetyCode() {
        return senderSafetyCode == null ? "0" : senderSafetyCode;
    }

    public String getValuableFlag() {
        return valuableFlag == null ? "0" : valuableFlag;
    }

    public String getReceiptFlag() {
        return receiptFlag == null ? "1" : receiptFlag;
    }

    public String getPaymentMode() {
        return paymentMode == null ? "1" : paymentMode;
    }

    public String getPickupType() {
        return pickupType == null ? "1" : pickupType;
    }

    public String getCodFlag() {
        return codFlag == null ? "9" : codFlag;
    }

    public String getInsuranceFlag() {
        return insuranceFlag == null ? "1" : insuranceFlag;
    }

    public String getSenderType() {
        return senderType == null ? "1" : senderType;
    }

    public String getSenderNo() {
        return senderNo == null ? "1100043915699" : senderNo;
    }

    public String getInnerChannel() {
        return innerChannel == null ? "0" : innerChannel;
    }

    public String getContentsAttribute() {
        return contentsAttribute == null ? "3" : contentsAttribute;
    }

    public String getBaseProductNo() {
        return baseProductNo == null ? "21210" : baseProductNo;
    }

    public String getBizProductNo() {
        return bizProductNo == null ? "112104302300991" : bizProductNo;
    }

    public String getProductType() {
        return productType == null ? "112104302300991" : productType;
    }

}
