package com.jiuji.oa.wuliu.entity;

  import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * ThirdPlatformOrderEntity 实体类
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2022-01-19
 */
@Data
@Accessors(chain = true)
@TableName("third_platform_order")
@ApiModel(value = "ThirdPlatformOrderEntity 实体类", description = " 实体类")
public class ThirdPlatformOrderEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 平台取消订单(1:取消)
     */
    @ApiModelProperty("平台取消订单(1:取消)")
    @TableField("cancel_check")
    private Integer cancelCheck;

    @TableField("buyer_country_name")
    private String buyerCountryName;

    @TableField("sub_id")
    private Long subId;

    @TableField("order_time")
    private LocalDateTime orderTime;

    @TableField("buyer_name")
    private String buyerName;

    @TableField("total_money")
    private Double totalMoney;

    @TableField("buyer_city")
    private String buyerCity;

    @TableField("point_money")
    private Double pointMoney;

    @TableField("buyer_city_name")
    private String buyerCityName;

    @TableField("freight_money")
    private Double freightMoney;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("area_code")
    private String areaCode;

    @TableField("discount_money")
    private Double discountMoney;

    @TableField("vender_money")
    private Double venderMoney;

    @TableField("buyer_tel")
    private String buyerTel;

    @TableField("good_money")
    private Double goodMoney;

    @TableField("trade_time")
    private LocalDateTime tradeTime;

    @TableField("sub_message")
    private String subMessage;

    @TableField("org_code")
    private String orgCode;

    @TableField("plat_money")
    private Double platMoney;

    @TableField("buyer_address")
    private String buyerAddress;

    @TableField("store_code")
    private String storeCode;

    @TableField("buyer_remark")
    private String buyerRemark;

    @TableField("plat_code")
    private String platCode;

    /**
     * buyer_mobile 是电话号码包含虚拟和真实的 cancel_check是平台取消订单(1为取消)
     */
    @TableField("buyer_mobile")
    private String buyerMobile;

    @TableField("area_id")
    private Integer areaId;

    @TableField("pay_status")
    private Integer payStatus;

    @TableField("payable_money")
    private Double payableMoney;

    @TableField("buyer_country")
    private String buyerCountry;

    @TableField("order_status")
    private Integer orderStatus;

    @TableField("order_id")
    private String orderId;

    @TableField("buyer_pin")
    private String buyerPin;

}