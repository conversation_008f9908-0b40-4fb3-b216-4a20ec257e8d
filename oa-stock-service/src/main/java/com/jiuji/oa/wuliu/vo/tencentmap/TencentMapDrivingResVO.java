package com.jiuji.oa.wuliu.vo.tencentmap;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/9 20:28
 */
@Data
public class TencentMapDrivingResVO {
    /**
     * 状态码，0为正常，其它为异常
     */
    private Integer status;
    /**
     * 状态说明
     */
    private String message;
    /**
     * request_id
     */
    @JsonProperty(value = "request_id")
    private String requestId;
    /**
     * 搜索结果
     */
    private ResultData result;

    @Data
    public static class ResultData {
        /**
         * 路线方案
         */
        private List<RouteData> routes;

        @Data
        public static class RouteData {
            /**
             * 方案交通方式，固定值：“WALKING”
             */
            private String mode;
            /**
             * 方案整体距离，单位：米
             */
            private long distance;
            /**
             * 方案估算时间，单位：分钟
             */
            private long duration;
            /**
             * 方案整体方向
             */
            private Restriction restriction;
            /**
             * 方案途经红绿灯个数
             */
            @JsonProperty("traffic_light_count")
            private Integer trafficLightCount;
            /**
             *方案路线坐标点串
             */
            private List<Double> polyline;
            private List<Waypoint> waypoints;
            /**
             * 路线步骤
             */
            private List<Step> steps;
            /**
             * 方案标签
             */
            private List<String> tags;
            /**
             * 	预估打车费
             */
            @JsonProperty("taxi_fare")
            private TaxiFare taxiFare;
        }

        @Data
        public static class Restriction {
            /**
             * 限行状态码：
             * 0 途经没有限行城市，或路线方案未涉及限行区域
             * 1 途经包含有限行的城市
             * 3 [设置车牌] 已避让限行
             * 4 [设置车牌] 无法避开限行区域（本方案包含限行路段）
             */
            private Integer status;
        }

        @Data
        public static class Step {
            /**
             * 阶段路线描述
             */
            private String instruction;
            /**
             * 阶段路线坐标点串在方案路线坐标点串的位置
             */
            @JsonProperty("polyline_idx")
            private List<Integer> polylineIdx;
            /**
             * 阶段路线路名
             */
            @JsonProperty("road_name")
            private String roadName;
            /**
             * 阶段路线方向
             */
            @JsonProperty("dir_desc")
            private String dirDesc;
            /**
             * 阶段路线距离，单位：米
             */
            private Double distance;
            /**
             * 预估到达耗时，单位：分钟
             */
            private Double duration;
            /**
             * 阶段路线末尾动作：如：左转调头
             */
            @JsonProperty("act_desc")
            private String actDesc;
            /**
             * 末尾辅助动作：如：到达终点
             */
            @JsonProperty("accessorial_desc")
            private String accessorialDesc;
        }

        @Data
        public static class TaxiFare {
            /**
             * 预估打车费用，单位：元
             */
            private BigDecimal fare;
        }

        /**
         * 途径点
         */
        @Data
        public static class Waypoint {
            /**
             * 	途经点路名
             */
            private String title;
            /**
             * 途经点坐标
             */
            private Location location;
            /**
             * 该途经点在polyline中的索引位置（数组下标位置）
             */
            @JsonProperty("polyline_idx")
            private Integer polylineIdx;
            /**
             * 预估到达耗时，单位：分钟
             */
            private Double duration;
            /**
             * 起点到该途经点的距离，单位：米
             */
            private Double distance;
        }

        @Data
        public static class Location {
            /**
             * 纬度
             */
            private Double lat;
            /**
             * 经度
             */
            private Double lng;
        }
    }
}
