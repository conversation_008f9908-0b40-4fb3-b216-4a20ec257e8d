/*
 *     Copyright © 2006 - 2020 九机网 All Rights Reserved
 *
 */

package com.jiuji.oa.wuliu.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.wuliu.entity.WuLiuPriceEntity;

import java.util.List;

/**
 * 物流价格 Service
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-09-29
 */
public interface IWuLiuPriceService extends IService<WuLiuPriceEntity> {

    /**
     * 获取所有物流价格
     *
     * @return List<WuLiuPriceEntity>
     * @date 2021-09-30
     * <AUTHOR> [<EMAIL>]
     */
    List<WuLiuPriceEntity> listAll();

    /**
     * 获取出发点和目的地城市的物流价格
     *
     * @param sourceCityId 出发点城市 ID
     * @param targetCityId 目的地城市 ID
     * @param isSameCity 是否同城？
     * @return WuLiuPriceEntity
     * @date 2021-09-30
     * <AUTHOR> [<EMAIL>]
     */
    WuLiuPriceEntity getOne(Integer sourceCityId, Integer targetCityId, Boolean isSameCity);

    /**
     * 获取出发点和目的地城市的物流价格
     *
     * @param sourceCityId 出发点城市 ID
     * @param targetCityId 目的地城市 ID
     * @param isSameCity 是否同城？
     * @return WuLiuPriceEntity
     * @date 2021-09-30
     * <AUTHOR> [<EMAIL>]
     */
    WuLiuPriceEntity getOne(String sourceCityId, String targetCityId, Boolean isSameCity);

    /**
     * 重建物流价格缓存
     *
     * <AUTHOR> [<EMAIL>]
     * @date 2021-09-30
     */
    void rebuildCache();

}
