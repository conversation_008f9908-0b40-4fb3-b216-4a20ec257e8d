/*
 *    Copyright © 2006 - 2020 九机网 All Rights Reserved
 *
 */

package com.jiuji.oa.wuliu.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.wuliu.entity.WuLiuShunfengNoInfoEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 顺丰快递 Mapper
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-09-29
 */
@Mapper
public interface WuLiuShunfengNoInfoMapper extends BaseMapper<WuLiuShunfengNoInfoEntity> {

    /**
     * 批量插入
     *
     * @param list
     * @return Integer
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-29
     */
    Integer addAll(@Param("list") List<WuLiuShunfengNoInfoEntity> list);

    WuLiuShunfengNoInfoEntity queryByMailNoAndWuliuId(@Param("waybillNo") String waybillNo, @Param("wuliuId") Integer wuliuId);
}
