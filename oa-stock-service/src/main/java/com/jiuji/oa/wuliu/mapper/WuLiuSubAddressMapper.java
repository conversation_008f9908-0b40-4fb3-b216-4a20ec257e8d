/*
 *    Copyright © 2006 - 2020 九机网 All Rights Reserved
 *
 */

package com.jiuji.oa.wuliu.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.wuliu.entity.WuLiuSubAddressEntity;
import com.jiuji.oa.wuliu.vo.req.WuLiuSubAddressVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * SubAddress Mapper
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-09-29
 */
@Mapper
public interface WuLiuSubAddressMapper extends BaseMapper<WuLiuSubAddressEntity> {

    /**
     * getSubPaisongOutPush
     *
     * @param subId
     * @return List<WuLiuSubAddressVO>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-05
     */
    List<WuLiuSubAddressVO> getSubPaisongOutPush(@Param("subId") Long subId);

}
