package com.jiuji.oa.wuliu.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.wuliu.entity.WuLiuSubFlagRecordEntity;
import com.jiuji.oa.wuliu.mapper.WuLiuSubFlagRecordMapper;
import com.jiuji.oa.wuliu.service.IWuLiuSubFlagRecordService;
import org.springframework.stereotype.Service;

/**
 * 订单团单状态记录表,责任小组：销售 服务实现类
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-18
 */
@Service
public class WuLiuSubFlagRecordServiceImpl extends ServiceImpl<WuLiuSubFlagRecordMapper, WuLiuSubFlagRecordEntity> implements IWuLiuSubFlagRecordService {
}
