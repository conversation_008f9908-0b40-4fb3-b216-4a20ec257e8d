package com.jiuji.oa.wuliu.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.wuliu.entity.WuliuTransferStation;
import com.jiuji.oa.wuliu.mapper.WuliuTransferStationMapper;
import com.jiuji.oa.wuliu.service.IWuliuTransferStationService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *
 */
@Service
@DS("ch999oanew")
public class WuliuTransferStationServiceImpl extends ServiceImpl<WuliuTransferStationMapper, WuliuTransferStation>
implements IWuliuTransferStationService {

    /**
     * 物流单查询中转
     *
     * @param wuliuIds
     * @return
     */
    @Override
    @DS("ch999oanew")
    public List<WuliuTransferStation> queryByWuliuIds(List<Long> wuliuIds) {
        return this.lambdaQuery().in(WuliuTransferStation::getWuliuId, wuliuIds).orderByDesc(WuliuTransferStation::getId).list();
    }
}




