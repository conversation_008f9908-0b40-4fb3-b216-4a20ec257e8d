package com.jiuji.oa.wuliu.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * SfCreateOrderParam.SfCreateOrderParam
 * 顺丰中台入参
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-16
 */
@Data
@Accessors(chain = true)
public class SfCreateOrderParamDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 物流单号
     */
    @JsonProperty("customerOrderNo")
    @JSONField(name = "customerOrderNo")
    private String customerOrderNo;

    /**
     * 接收人名称
     */
    @JsonProperty("receiveName")
    @JSONField(name = "receiveName")
    private String receiveName;

    /**
     * 接收人电话
     */
    @JsonProperty("receiveTel")
    @JSONField(name = "receiveTel")
    private String receiveTel;

    /**
     * 接收人地址
     */
    @JsonProperty("receiveAddress")
    @JSONField(name = "receiveAddress")
    private String receiveAddress;

    /**
     * 寄件人姓名
     */
    @JsonProperty("sendName")
    @JSONField(name = "sendName")
    private String sendName;

    /**
     * 寄件人电话
     */
    @JsonProperty("sendTel")
    @JSONField(name = "sendTel")
    private String sendTel;

    /**
     * 寄件地址
     */
    @JsonProperty("sendAddress")
    @JSONField(name = "sendAddress")
    private String sendAddress;

    /**
     * 快递方式
     */
    @JsonProperty("expressType")
    @JSONField(name = "expressType")
    private Integer expressType;

    /**
     * 子母件类型(顺丰)
     */
    @JsonProperty("bspType")
    @JSONField(name = "bspType")
    private Integer bspType;

    /**
     * 商品名称
     */
    @JsonProperty("productName")
    @JSONField(name = "productName")
    private String productName;

    /**
     * 包裹数
     */
    @JsonProperty("packageNumber")
    @JSONField(name = "packageNumber")
    private Integer packageNumber;

    /**
     *
     */
    @JsonProperty("type")
    @JSONField(name = "type")
    private Integer type;

}
