package com.jiuji.oa.wuliu.vo;

import lombok.Getter;
import lombok.Setter;
import org.codehaus.jackson.annotate.JsonProperty;

import java.io.Serializable;


/**
 * SfQueryTraceParamVO
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-12-14
 */
@Setter
@Getter
public class SfQueryTraceParamVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 快递单号
     */
    @JsonProperty("routeNum")
    private String expressNumber;

    /**
     * 快递类型
     * 顺丰快递=2
     */
    @JsonProperty("expressType")
    private String expressType;

}
