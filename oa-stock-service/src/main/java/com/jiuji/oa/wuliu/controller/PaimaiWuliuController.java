package com.jiuji.oa.wuliu.controller;

import com.jiuji.oa.wuliu.service.IPaimaiWuliuService;
import com.jiuji.oa.wuliu.vo.paimai.req.AddPaimaiWuliuReqVO;
import com.jiuji.oa.wuliu.vo.paimai.res.AddPaimaiWuliuResVO;
import com.jiuji.tc.common.vo.R;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2022/7/11 16:31
 */
@RestController
@RequestMapping("/api/paimai-wuliu")
public class PaimaiWuliuController {
    @Resource
    private IPaimaiWuliuService paimaiWuliuService;

    /**
     * 拍靓机创建物流单、快递单
     * @return
     */
    @PostMapping("/add/v1")
    public R<AddPaimaiWuliuResVO> addPaimaiWuliu(@Valid @RequestBody AddPaimaiWuliuReqVO req) {
        return R.success(paimaiWuliuService.addPaimaiWuliu(req));
    }
}
