package com.jiuji.oa.wuliu.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum WuliuLogKindEnum implements CodeMessageEnumInterface {
    /**
     * 物流统计类别
     **/
    SEND_WULIU(1, "发货扫描"),
    SIGNATURE_WULIU(2, "代签"),
    PICK_UP_WULIU(3, "揽收"),
    DELIVERING_EXCEPTION(4, "跑腿送货异常"),
    WULIU_NOT_DELIVERED(5, "物流单未投妥"),
    ;


    private final Integer code;
    private final String message;
}
