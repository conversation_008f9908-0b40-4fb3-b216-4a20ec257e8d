/*
 *     Copyright © 2006 - 2020 九机网 All Rights Reserved
 *
 */

package com.jiuji.oa.wuliu.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.wuliu.entity.WuLiuSubAddressEntity;

/**
 * SubAddress Service
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-08
 */
public interface IWuLiuSubAddressService extends IService<WuLiuSubAddressEntity> {
    /**
     * 清除订单中的快递公司和快递单号
     * @param subId
     * @param nu
     * @return
     */
    boolean clearWuliuNo(Integer subId, String nu);
}
