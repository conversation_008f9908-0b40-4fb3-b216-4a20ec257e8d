package com.jiuji.oa.wuliu.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.wuliu.bo.SellerInfoBO;
import com.jiuji.oa.wuliu.entity.RecoverMarketsubinfo;

/**
 * <p>
 * 转售详情单（良品详情订单）[责任小组:回收] 服务类
 * </p>
 *
 * <AUTHOR> yao yao
 * @since 2022-06-01
 */
public interface IRecoverMarketsubinfoService extends IService<RecoverMarketsubinfo> {


    /**
     * 是否为付费加急送
     * @param subId
     * @return
     */
    boolean isPayMoneyDelivery(Integer subId);

    SellerInfoBO getSellerInfoBySubId(Integer subId);
}
