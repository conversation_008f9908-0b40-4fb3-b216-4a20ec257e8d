package com.jiuji.oa.wuliu.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR> @date 2021-11-25
 */
@Data
@Accessors(chain = true)
@TableName("MarkAbnomalWuliuRecords")
public class MarkAbnomalWuLiuRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId
    @TableField("Id")
    private Long id;
    @TableField("WuliuId")
    private Long wuLiuId;
    @TableField("Remark")
    private String remark;
    @TableField("Ch999Id")
    private Long ch999Id;
    @TableField("Dtime")
    private LocalDateTime dTime;
    @TableField("IsDel")
    private Boolean del;
}
