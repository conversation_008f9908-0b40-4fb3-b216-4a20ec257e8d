package com.jiuji.oa.wuliu.vo;


import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;


/**
 * 派送
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-11-04
 */
@Data
@Accessors(chain = true)
public class PaisongDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 派送人
     */
    private String trade;
    /**
     * 老的派送人
     */
    private String tradeOld;

    /**
     * 派送状态
     */
    private Integer peisongstats;

    /**
     * 原始派送状态
     */
    private String peisongstatsOld;

    /**
     * 订单号
     */
    @JsonProperty("sub_id")
    @JSONField(name = "sub_id")
    private Long subId;

    private String ch999;

    /**
     * 配送方式
     */
    private Integer delivery;

    // 售后添加字段
    /**
     * 是否是售后  是售后：该字段为售后id  不是售后：该字段为0
     */
    @JsonProperty("ShouhouId")
    @JSONField(name = "ShouhouId")
    public Integer shouhouId;

    /**
     * 是否是售后上门取件
     */
    @JsonProperty("isSShangmen")
    @JSONField( name = "isSShangmen")
    private Integer isSshangmen;

    /**
     * 物流id
     */
    private Integer wuliuid;

    /**
     * 配送地址
     */
    private String raddress;

    /**
     * 数据库 最大为 11 , 添加12 作为良品派送单标识 ,8 是售后预约上门取件 13 发票派送单标识 14 租机
     */
    private Integer lktp;

    private Integer isna;

}
