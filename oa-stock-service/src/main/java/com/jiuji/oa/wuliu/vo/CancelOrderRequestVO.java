package com.jiuji.oa.wuliu.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR> [<EMAIL>]
 * @date 2021-12-03
 */
@Data
@Accessors(chain = true)
public class CancelOrderRequestVO implements Serializable {

    /**
     *
     */
    @JsonProperty("deliveryId")
    @JSONField( name = "deliveryId")
    private Long deliveryId;

    /**
     *
     */
    @JsonProperty("platformInsideId")
    @JSONField( name = "platformInsideId")
    private String platformInsideId;

    /**
     * 客户取消订单原因
     */
    @JsonProperty("cancelOrderReasonId")
    @JSONField( name = "cancelOrderReasonId")
    private Integer cancelOrderReasonId;

    /**
     * 取消原因
     */
    @JsonProperty("cancelReason")
    @JSONField( name = "cancelReason")
    private String cancelReason;

    @JsonProperty("expressType")
    @JSONField( name = "expressType")
    private Integer expressType;

    @JsonProperty("xTenantId")
    @JSONField( name = "xTenantId")
    private Integer xTenantId;

    public Integer getExpressType() {
        return expressType == null ? 1 : expressType;
    }

}
