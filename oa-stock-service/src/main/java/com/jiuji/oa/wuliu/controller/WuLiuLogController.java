/*
 *    Copyright © 2006 - 2020 九机网 All Rights Reserved
 *
 */

package com.jiuji.oa.wuliu.controller;

import com.jiuji.oa.nc.common.bo.OaUserBO;
import com.jiuji.oa.nc.common.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.stock.logistics.order.vo.req.SaveWuLiuLogReq;
import com.jiuji.oa.wuliu.entity.WuLiuLogEntity;
import com.jiuji.oa.wuliu.service.IWuLiuLogService;
import com.jiuji.oa.wuliu.vo.res.WuLiuLogResVO;
import com.jiuji.tc.common.vo.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.format.DateTimeFormatter;
import java.util.List;


/**
 * 物流单Controller
 *
 * <AUTHOR>
 * @date 2021-05-17 11:24:40
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/api/wuliu/log")
@Api(value = "logistics-wuliu-log", tags = "物流单-日志")
public class WuLiuLogController {

    private final IWuLiuLogService wuLiuLogService;

    @Resource
    private AbstractCurrentRequestComponent component;

    /**
     * 查询单个物流单日志
     *
     * @return 物流单日志
     */
    @ApiOperation("查询单个物流单日志")
    @GetMapping("/list/v1")
    public R<List<WuLiuLogResVO>> listAllV1(@RequestParam String wuliuid) {
        // 查询查询物流单分类
        List<WuLiuLogResVO> wuLiuLogResVO = wuLiuLogService.getWuLiuLog(wuliuid);
        return R.success(wuLiuLogResVO);
    }

    /**
     * ory/query-wuliu-category-list/v1
     * 新增物流单日志
     *
     * @param saveWuLiuLogReq WuLiuAddOrUpdateReqVO
     * @return R<Boolean>
     * @date 2021-10-09
     * <AUTHOR>
     */
    @ApiOperation(value = "新增物流单日志")
    @PostMapping("/add/v1")
    public R<String> addOneV1(@RequestBody SaveWuLiuLogReq saveWuLiuLogReq) {
        OaUserBO currentStaffId = component.getCurrentStaffId();
        String inuser = currentStaffId.getUserName();
        saveWuLiuLogReq.setInuser(inuser);
        WuLiuLogEntity wuLiuLogEntity = wuLiuLogService.writeLogs(saveWuLiuLogReq);
        return R.success("操作成功", wuLiuLogEntity.getDtime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
    }

}
