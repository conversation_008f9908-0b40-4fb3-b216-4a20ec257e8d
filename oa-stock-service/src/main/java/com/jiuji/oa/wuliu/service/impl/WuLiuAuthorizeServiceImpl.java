package com.jiuji.oa.wuliu.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.wuliu.entity.WuLiuAuthorizeEntity;
import com.jiuji.oa.wuliu.mapper.WuLiuAuthorizeMapper;
import com.jiuji.oa.wuliu.service.IWuLiuAuthorizeService;
import org.springframework.stereotype.Service;

/**
 * 服务实现类
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-19
 */
@Service
public class WuLiuAuthorizeServiceImpl extends ServiceImpl<WuLiuAuthorizeMapper, WuLiuAuthorizeEntity> implements IWuLiuAuthorizeService {
}
