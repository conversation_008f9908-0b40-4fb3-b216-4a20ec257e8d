package com.jiuji.oa.wuliu.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.oa.wuliu.dto.req.WuLiuNoteReq;
import com.jiuji.oa.wuliu.dto.res.WuLiuNoteRes;
import com.jiuji.oa.wuliu.entity.WuLiuNote;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 物流管理界面持久层映射
 *
 * <AUTHOR>
 * @date 2021/10/08
 */
@Mapper
public interface WuLiuNoteMapper extends BaseMapper<WuLiuNote> {

    /**
     * 物流单分页
     *
     * @param req
     * @param begin                   分页参数，起始数
     * @param end                     分页参数，结束数
     * @return Page<WuLiuNoteRes>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-24
     */
    @InterceptorIgnore(tenantLine = "true")
    List<WuLiuNoteRes> pageList(@Param("param") WuLiuNoteReq req, @Param("begin") Integer begin, @Param("end") Integer end);

    /**
     * 总数
     *
     * @param req
     * @return Integer
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-24
     */
    @InterceptorIgnore(tenantLine = "true")
    Integer countAll(@Param("param") WuLiuNoteReq req);

    /**
     * getWuLiuData
     *
     * @param condition
     * @param req
     * @return Page<WuLiuNoteRes>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-05
     */
    Page<WuLiuNoteRes> getWuLiuData(@Param("page") IPage<WuLiuNoteReq> condition, @Param("param") WuLiuNoteReq req);

    /**
     * 查询物流单列表
     * @param condition
     * @param wuLiuNoteReq
     * @return
     */
    Page<WuLiuNoteRes> getWuLiuDataV2(@Param("page") Page<WuLiuNoteReq> condition, @Param("param") WuLiuNoteReq wuLiuNoteReq);
}
