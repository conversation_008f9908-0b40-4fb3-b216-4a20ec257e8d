package com.jiuji.oa.wuliu.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.wuliu.entity.WuLiuRecoverSubAddressEntity;
import com.jiuji.oa.wuliu.mapper.WuLiuRecoverSubAddressMapper;
import com.jiuji.oa.wuliu.service.IWuLiuRecoverSubAddressService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 良品订单地址[责任小组:回收] 服务实现类
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-27
 */
@DS("oanewWrite")
@Service
public class WuLiuRecoverSubAddressServiceImpl extends ServiceImpl<WuLiuRecoverSubAddressMapper, WuLiuRecoverSubAddressEntity> implements IWuLiuRecoverSubAddressService {
    /**
     * 清除良品订单中的快递公司和快递单号
     *
     * @param subId
     * @param nu
     * @return
     */
    @DS("oanewWrite")
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean clearWuliuNo(Integer subId, String nu) {
        return this.lambdaUpdate()
                .set(WuLiuRecoverSubAddressEntity::getWuliuNo, null)
                .set(WuLiuRecoverSubAddressEntity::getWuliucompany, null)
                .eq(WuLiuRecoverSubAddressEntity::getSubId, subId)
                .eq(WuLiuRecoverSubAddressEntity::getWuliuNo, nu)
                .update();
    }
}
