package com.jiuji.oa.wuliu.vo;

import com.jiuji.oa.stock.logisticscenter.vo.LogisticsBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> <PERSON>iu ming
 * @date 2021-09-27 下午 7:35
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Valid
public class WuLiuBase extends LogisticsBase {
    @NotNull(message = "物流类别不能为空")
    private Integer wuLiuCategory;

    @NotNull(message = "物流分类不能为空")
    private Integer wuLiuType;

}
