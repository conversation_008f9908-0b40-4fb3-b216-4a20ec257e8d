package com.jiuji.oa.wuliu.service.impl;

import com.jiuji.oa.wuliu.service.ISubWuLiuService;
import com.jiuji.oa.wuliu.vo.req.WuLiuInfoReqVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 *
 * @description: SubWuLiuService
 * </p>
 * @author: <PERSON>
 * @create: 2021-10-09 17:01
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SubWuLiuServiceImpl implements ISubWuLiuService {

    /**
     * 预约单 新增物流单
     *
     * @param vo
     * @param areaId
     */
    @Override
    public void appointmentAddWuLiu(WuLiuInfoReqVO vo, Integer areaId) {
        //赋初值
        if (vo.getWuType() == 0) {
            vo.setWuType(1);
        }
        //订单派送 物流单
    }

}