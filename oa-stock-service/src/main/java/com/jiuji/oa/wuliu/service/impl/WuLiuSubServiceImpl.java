/*
 *     Copyright © 2006 - 2020 九机网 All Rights Reserved
 *
 */
package com.jiuji.oa.wuliu.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.orderdynamics.dto.DiaoboWuliuSubDTO;
import com.jiuji.oa.orderdynamics.dto.DiySubDTO;
import com.jiuji.oa.orderdynamics.dto.WuliuSubDTO;
import com.jiuji.oa.orderdynamics.vo.response.QueryWuliuBySubResVO;
import com.jiuji.oa.wuliu.dto.SubExpectTimeDTO;
import com.jiuji.oa.wuliu.dto.SubPositionDTO;
import com.jiuji.oa.wuliu.dto.req.SubPositionReq;
import com.jiuji.oa.wuliu.entity.WuLiuEntity;
import com.jiuji.oa.wuliu.entity.WuLiuSubEntity;
import com.jiuji.oa.wuliu.mapper.WuLiuSubMapper;
import com.jiuji.oa.wuliu.service.IWuLiuSubService;
import com.jiuji.oa.wuliu.vo.res.WuliuOrderInfoRes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;


/**
 * 订单,责任小组：销售 ServiceImpl
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-09
 */
@Slf4j
@DS("oanewWrite")
@Service
public class WuLiuSubServiceImpl extends ServiceImpl<WuLiuSubMapper, WuLiuSubEntity> implements IWuLiuSubService {

    /**
     * @param subId
     * @return WuLiuSubEntity
     * @date 2021-10-11
     * <AUTHOR> [<EMAIL>]
     */
    @Override
    public WuLiuSubEntity getSub(Integer subId) {
        return baseMapper.getSub(subId);
    }

    /**
     * 物流单查询订单
     *
     * @param wuliuId
     * @return
     */
    @Override
    @DS("ch999oanew")
    public List<WuliuSubDTO> getWuliuSubByWuliuId(Integer wuliuId) {
        return baseMapper.getWuliuSubByWuliuIds(wuliuId);
    }

    /**
     * 快递单查询订单（不包含调拨）
     *
     * @param nu
     * @return
     */
    @Override
    @DS("ch999oanew")
    public List<WuliuSubDTO> getWuliuSubByNu(String nu) {
        return baseMapper.getWuliuSubByNu(nu);
    }

    /**
     * 快递单查询订单
     *
     * @param nu
     * @return
     */
    @Override
    @DS("ch999oanew")
    public List<DiySubDTO> getDiySubByNu(String nu) {
        if (StringUtils.isBlank(nu)) {
            return null;
        }
        return baseMapper.getDiySubByNu(nu);
    }

    /**
     * 快递单号调拨单订单
     * @param nu
     * @return
     */
    @Override
    @DS("ch999oanew")
    public List<DiaoboWuliuSubDTO> getTransferWuliuSubByNu(String nu) {
        if (StringUtils.isBlank(nu)) {
            return null;
        }
        return baseMapper.getTransferWuliuSubByNu(nu);
    }

    /**
     * 物流单查询调拨单订单
     *
     * @param wuliuId
     * @return
     */
    @Override
    @DS("ch999oanew")
    public List<DiaoboWuliuSubDTO> getTransferWuliuSubByWuliuId(Integer wuliuId) {
        if (Objects.isNull(wuliuId)) {
            return null;
        }
        return baseMapper.getTransferWuliuSubByWuliuId(wuliuId);
    }

    /**
     * 订单查询物流单信息
     *
     * @param subId
     * @return
     */
    @Override
    @DS("ch999oanew")
    public QueryWuliuBySubResVO queryWuliuBySubId(Integer subId) {
        return baseMapper.queryWuliuBySubId(subId);
    }

    /**
     * 包含调拨单
     * 物流单查询订单
     *
     * @param subId
     * @return
     */
    @Override
    @DS("ch999oanew")
    public List<WuLiuEntity> getWuliuEntityBySubId(Integer subId) {
        return baseMapper.getWuliuEntityBySubId(subId);
    }

    /**
     * 查询订单预计送达时间
     *
     * @param subId
     * @return
     */
    @Override
    @DS("ch999oanew")
    public SubExpectTimeDTO getSubExpectTimeBySubId(Integer subId) {
        return baseMapper.getSubExpectTimeBySubId(subId);
    }

    /**
     * 查询订单预计送达时间
     *
     * @param subId
     * @return
     */
    @Override
    @DS("oanewWrite")
    public boolean updateSubExpectTime(Integer subId, LocalDateTime expectTime) {
        return this.lambdaUpdate()
                .set(WuLiuSubEntity::getExpectTime, expectTime)
                .eq(WuLiuSubEntity::getSubId, subId)
                .update();
    }

    /**
     * 查询订单收货位置信息
     *
     * @param sub
     * @return
     */
    @Override
    @DS("ch999oanew")
    public SubPositionDTO getSubPositionBySub(SubPositionReq sub) {
        return baseMapper.getSubPositionBySub(sub);
    }

    /**
     * 支付商户订单号查询订单物流信息
     *
     * @param payId
     * @return
     */
    @Override
    @DS("ch999oanew")
    public WuliuOrderInfoRes getOrderByPayId(Integer payId) {
        return this.baseMapper.getOrderByPayId(payId);
    }
    /**
     * 查询订单信息
     *
     * @param payDes
     * @return
     */
    @Override
    @DS("ch999oanew")
    public WuliuOrderInfoRes getOrderByPayDes(String payDes) {
        return this.baseMapper.getOrderByPayDes(payDes);
    }

    @Override
    @DS("ch999oanew")
    public WuLiuSubEntity getBySubId(Long subId) {
        return this.baseMapper.getBySubId(subId);
    }

    /**
     * 查询订单信息
     *
     * @param subId
     * @return
     */
    @Override
    @DS("ch999oanew")
    public WuLiuSubEntity getSubBySubId(Integer subId) {
        return this.getOne(new LambdaQueryWrapper<WuLiuSubEntity>().eq(WuLiuSubEntity::getSubId, subId), false);
    }

    /**
     * 快递单查询订单门店电话号码
     *
     * @param nu
     * @return
     */
    @Override
    @DS("ch999oanew")
    public String getSubTelByNu(String nu) {
        return baseMapper.getSubTelByNu(nu);
    }

    @Override
    @DS("ch999oanew")
    public SubPositionDTO getSubAddressPositionBySubId(SubPositionReq req) {
        return baseMapper.getSubAddressPositionBySubId(req);
    }

}