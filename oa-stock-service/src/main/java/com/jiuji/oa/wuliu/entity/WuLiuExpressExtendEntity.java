package com.jiuji.oa.wuliu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 实体类
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-22
 */
@Data
@Accessors(chain = true)
@TableName("wuliu_express_extend")
@ApiModel(value = "WuLiuExpressExtendEntity 实体类", description = " 实体类")
public class WuLiuExpressExtendEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Id
     */
    @ApiModelProperty("Id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 物流ID
     */
    @ApiModelProperty("物流ID")
    @TableField("wuliu_id")
    private Integer wuliuId;

    /**
     * 快递公司标识
     */
    @ApiModelProperty("快递公司标识")
    @TableField("express_company")
    private String expressCompany;

    /**
     * 快递子类型
     */
    @ApiModelProperty("快递子类型")
    @TableField("express_type")
    private String expressType;

    /**
     * 删除标记
     */
    @ApiModelProperty("删除标记")
    @TableField("is_del")
    private Boolean isDel;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @TableField("update_time")
    private LocalDateTime updateTime;

}