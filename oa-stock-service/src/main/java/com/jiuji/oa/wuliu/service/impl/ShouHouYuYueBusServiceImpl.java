package com.jiuji.oa.wuliu.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jiuji.oa.nc.user.po.Areainfo;
import com.jiuji.oa.nc.user.service.IAreaInfoService;
import com.jiuji.oa.stock.common.util.OptionalUtils;
import com.jiuji.oa.stock.logistics.order.entity.Attachments;
import com.jiuji.oa.stock.logistics.order.service.AttachmentsService;
import com.jiuji.oa.wuliu.entity.AddInfoPsEntity;
import com.jiuji.oa.wuliu.entity.ShouHouYuYueEntity2;
import com.jiuji.oa.wuliu.entity.WuLiuShouhouYuyueproductinfoEntity;
import com.jiuji.oa.wuliu.service.*;
import com.jiuji.oa.wuliu.vo.ShouHouYuYueDTO;
import com.jiuji.wcf.wcfclient.csharp.gen.oa999model.shouhou.attachModel;
import com.jiuji.wcf.wcfclient.csharp.gen.oa999model.shouhou.shouhou_yuyueproductinfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <p>
 *
 * @description: ShouHouYuYueBusServiceImpl
 * </p>
 * @author: David
 * @create: 2021-10-13 14:49
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ShouHouYuYueBusServiceImpl implements IShouHouYuYueBusService {

    private final IShouHouYuYueService shouHouYuYueService;
    private final IAddInfoPsService addInfoPsService;
    private final IAreaInfoService areaInfoService;
    private final AttachmentsService attachmentsService;
    private final IWuLiuShouhouYuyueproductinfoService wuLiuShouhouYuyueproductinfoService;

    private final IWuLiuCardLogsService wuLiuCardLogsService;

    /**
     * 启用新会员级别标识
     *
     * @return Boolean
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-04
     */
    public static Boolean startNewUserClass() {
        return LocalDateTime.now().isBefore(LocalDateTime.of(2018, 10, 18, 16, 0, 0));
    }

    /**
     * @param userclass
     * @return String
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-04
     */
    public static String getusertype(int userclass) {
        String re = "";
        if (startNewUserClass()) {
            switch (userclass) {
                case 0:
                    re = "青铜会员";
                    break;
                case 1:
                    re = "白银会员";
                    break;
                case 2:
                    re = "黄金会员";
                    break;
                case 3:
                    re = "黑钻会员";
                    break;
                default:
                    re = "";
                    break;
            }
        } else {
            switch (userclass) {
                case 0:
                    re = "普通会员";
                    break;
                case 1:
                    re = "青铜会员";
                    break;
                case 2:
                    re = "白银会员";
                    break;
                case 3:
                    re = "黄金会员";
                    break;
                case 5:
                    re = "钻石会员";
                    break;
                case 6:
                    re = "双钻会员";
                    break;
                default:
                    re = "";
                    break;
            }
        }
        return re;
    }

    /**
     * 获得预约地址
     *
     * @param dto
     * @return
     */
    @Override
    @DS("ch999oanew")
    public ShouHouYuYueDTO getYuYueAddInfOps(ShouHouYuYueDTO dto) {
        return getShouHouYuYueDTO(dto);
    }

    /**
     * 获取一条预约信息
     *
     * @param shouHouId
     * @return
     */
    @Override
    public ShouHouYuYueDTO getYuYue(Integer shouHouId) {
        ShouHouYuYueEntity2 dt = shouHouYuYueService.getOne2(shouHouId);
        ShouHouYuYueDTO model = new ShouHouYuYueDTO();
        if (dt != null) {
            model.setId(dt.getId());
            model.setUserid(dt.getUserid());
            model.setSubId(dt.getSubId());
            model.setName(dt.getName());
            model.setColor(dt.getColor());
            model.setIsmobile(dt.getMobileFlag());
            model.setImei(dt.getImei());
            model.setUsername(dt.getUsername());
            model.setMobile(dt.getMobile());
            model.setProblem(dt.getProblem());
            model.setComment(dt.getComment());
            model.setWuliyou(dt.getWuliyou());
            model.setStime(dt.getStime());
            model.setEtime(dt.getEtime());
            model.setDtime(dt.getDtime());
            model.setStype(dt.getStype());
            model.setKind(dt.getKind());
            model.setAreaid(dt.getAreaid());
            model.setStats(dt.getStats());
            model.setShouhouId(dt.getShouhouId());
            model.setWuliuid(dt.getWuliuid());
            model.setProductid(dt.getProductId());
            model.setPpriceid(dt.getPpriceid());
            model.setTuiData(dt.getTuidata());
            model.setYMkcAreaId(dt.getYMkcareaid());
            model.setYPpriceid(dt.getYPpriceid());
            model.setSmallproid(dt.getSmallproid());
            model.setUserClass(OptionalUtils.ifNotNull(dt.getUserclass(), Integer::valueOf));
            model.setIsdel(dt.getDel());
            model.setIsBakData(dt.getIsBakData());
            model.setEnterUser(dt.getEnterUser());
            model.setIszy(dt.getZy());
            model.setZyptype(dt.getZyptype());
            model.setZypnum(dt.getZypnum());
            model.setKdtime(dt.getKdtime());
            model.setKuaididan(dt.getKuaididan());
            model.setKdtype(dt.getKdtype());
            model.setYuyuePpids(dt.getYuyuePpids());
            model.setKuaidigongsi(dt.getKuaidigongsi());
            model.setFuwuma(dt.getFuwuma());
            model.setInuser(dt.getInuser());
            model.setCheckUser(dt.getCheckUser());
            model.setGuideStaffId(dt.getGuideStaffId());
        }

        model.setYMkcArea(Optional.ofNullable(areaInfoService.getAreaInfoByAreaId2(model.getYMkcAreaId())).orElseGet(Areainfo::new).getArea());
        model = getYuYueAddInfOps(model);
        model.setUserClassName(getusertype(Optional.ofNullable(model.getUserClass()).orElse(-1)));
        //如果是中邮，获取这2部分数据
        if (Boolean.TRUE.equals(model.getIszy())) {
            model.setPics(getYuyuePics(model.getId()));
            model.setYyproducts(getYuyueProducts(model.getId()));
        }

        //获取预约单使用的优惠码
        model.setYouhuima(wuLiuCardLogsService.getYuyueYouHuiMa(shouHouId));

        return model;

    }

    /**
     * 获取中邮预约机型集合
     *
     * @param yuyueId
     * @return List<shouhou_yuyueproductinfo>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-04
     */
    private List<shouhou_yuyueproductinfo> getYuyueProducts(Integer yuyueId) {
        List<shouhou_yuyueproductinfo> list = new ArrayList<>();
        List<WuLiuShouhouYuyueproductinfoEntity> dt = wuLiuShouhouYuyueproductinfoService.lambdaQuery().eq(WuLiuShouhouYuyueproductinfoEntity::getYuyueid, yuyueId).list();
        if (CollectionUtils.isNotEmpty(dt)) {
            shouhou_yuyueproductinfo item;
            for (WuLiuShouhouYuyueproductinfoEntity entity : dt) {
                item = new shouhou_yuyueproductinfo();
                item.setPpriceid(entity.getPpriceid());
                item.setProductid(entity.getProductid());
                item.setProductname(entity.getProductname());
                item.setProductcolor(entity.getProductcolor());
                item.setRemark(entity.getRemark());
                item.setImei(entity.getImei());
                list.add(item);
            }
        }
        return list;
    }

    /**
     * 获取中邮预约图片
     *
     * @param yuyueId
     * @return List<attachModel>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-04
     */
    public List<attachModel> getYuyuePics(int yuyueId) {
        List<attachModel> list = new ArrayList<>();
        List<Attachments> dt = attachmentsService.newList(new LambdaQueryWrapper<Attachments>().eq(Attachments::getType, 49).eq(Attachments::getLinkedID, yuyueId), yuyueId, 49);
        if (CollectionUtils.isNotEmpty(dt)) {
            attachModel attachModel;
            for (Attachments attachments : dt) {
                attachModel = new attachModel();
                attachModel.setFid(attachments.getFid());
                attachModel.setExtension(attachments.getExtension());
                attachModel.setFilename(attachments.getFilename());
                list.add(attachModel);
            }
        }
        return list;

    }

    /**
     * getShouHouYuYueDTO
     *
     * @return
     */
    private ShouHouYuYueDTO getShouHouYuYueDTO(ShouHouYuYueDTO dto) {
        AddInfoPsEntity addInfoPsEntity = addInfoPsService
                .getByShouHouId(dto.getShouhouId(), dto.getId());
        addInfoPsEntity = Optional.ofNullable(addInfoPsEntity).orElse(new AddInfoPsEntity());
        dto.setReciver2(addInfoPsEntity.getReceiver());
        dto.setCityid2(addInfoPsEntity.getCityId());
        dto.setAddress2(addInfoPsEntity.getAddress());
        dto.setConsignee2(addInfoPsEntity.getConsignee());
        return dto;
    }
}