package com.jiuji.oa.wuliu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 转售详情单（良品详情订单）[责任小组:回收]
 * </p>
 *
 * <AUTHOR> yao yao
 * @since 2022-06-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("recover_marketSubInfo")
public class RecoverMarketsubinfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单id
     */
    @TableId(value = "basket_id", type = IdType.AUTO)
    private Integer basketId;

    /**
     * 可退数量
     */
    private Integer basketCount;

    /**
     * 加单时间
     */
    private LocalDateTime basketDate;

    /**
     * 销售人员
     */
    private String seller;

    /**
     * 是否大件
     */
    private Boolean ismobile;

    /**
     * 成本价
     */
    private Double price;

    /**
     * 订单id
     */
    private Long subId;

    /**
     * 原价
     */
    private Double price1;

    /**
     * 商品规格id
     */
    private Integer ppriceid;

    /**
     * 成本
     */
    private Double inprice;

    /**
     * 赠品id
     */
    private Integer giftid;

    /**
     * 销售方式，和新机单的一样
     */
    private Integer type;

    /**
     * 删除标识
     */
    private Boolean isdel;

    /**
     * 出库
     */
    private Boolean ischu;

    /**
     * 单价
     */
    private Double price2;

    /**
     * 是否豁免
     */
    private Boolean ishm;

    /**
     * 亏损备注
     */
    @TableField("ksRemark")
    private String ksremark;

    /**
     * 用于删除的单和退订的单显示串号
     */
    private String ksfid;

    /**
     * 原串号
     */
    private String imei2;

    /**
     * 是否到店购买
     */
    @TableField("isOnShop")
    private Boolean isonshop;

    /**
     * 原mkcid,用在删除后还能查询到mkcid
     */
    private Integer mkcId2;

    /**
     * 服务费
     */
    private Double auctionFee;

    /**
     * aa_voucher_record表主键id
     */
    private Long u8VoucherId;

    /**
     * 二手转售成本结算（九机）凭证id
     */
    private Long u8VoucherId2;



    @TableField("youhuiPrice")
    private BigDecimal youhuiprice;

    @TableField("jifenPrice")
    private BigDecimal jifenprice;

    @TableField("coinPrice")
    private BigDecimal coinprice;

    private Double returnPrice;


}
