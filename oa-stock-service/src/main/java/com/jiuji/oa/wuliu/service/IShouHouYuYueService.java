package com.jiuji.oa.wuliu.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.wuliu.dto.ShouHouAddInfoDTO;
import com.jiuji.oa.wuliu.dto.YuYueAddInfoDTO;
import com.jiuji.oa.wuliu.entity.ShouHouYuYueEntity;
import com.jiuji.oa.wuliu.entity.ShouHouYuYueEntity2;

/**
 * <p>
 * 售后预约表,责任小组：销售 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-09
 */
public interface IShouHouYuYueService extends IService<ShouHouYuYueEntity> {

    /**
     * 获取一条预约信息
     *
     * @param id
     * @return
     */
    ShouHouYuYueEntity getOne(Integer id);

    /**
     * 获取一条预约信息 2
     *
     * @param id
     * @return
     */
    ShouHouYuYueEntity2 getOne2(Integer id);

    /**
     * 查询预约物流收寄件信息
     */
    YuYueAddInfoDTO getYuYueAddInfo(Integer yuyueId);

    /**
     * 查询预约物流收寄件信息
     */
    ShouHouAddInfoDTO getShouhouAddInfo(Integer shouhouId);
}
