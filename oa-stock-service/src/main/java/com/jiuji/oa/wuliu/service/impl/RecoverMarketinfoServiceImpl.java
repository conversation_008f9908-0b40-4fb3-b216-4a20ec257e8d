package com.jiuji.oa.wuliu.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.orderdynamics.vo.response.QueryWuliuBySubResVO;
import com.jiuji.oa.stock.develivery.vo.req.OrderOutStockPageReqVO;
import com.jiuji.oa.stock.develivery.vo.res.OrderOutStockPageRes;
import com.jiuji.oa.wuliu.dto.SubExpectTimeDTO;
import com.jiuji.oa.wuliu.entity.RecoverMarketinfo;
import com.jiuji.oa.wuliu.entity.WuLiuEntity;
import com.jiuji.oa.wuliu.entity.WuLiuSubEntity;
import com.jiuji.oa.wuliu.mapper.RecoverMarketinfoMapper;
import com.jiuji.oa.wuliu.service.IRecoverMarketinfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 转售单（良品订单）[责任小组:回收] 服务实现类
 * </p>
 *
 * <AUTHOR> yao yao
 * @since 2022-06-08
 */
@Service
@RequiredArgsConstructor
@Slf4j
@DS("oanewWrite")
public class RecoverMarketinfoServiceImpl extends ServiceImpl<RecoverMarketinfoMapper, RecoverMarketinfo> implements IRecoverMarketinfoService {

    /**
     * 良品单查询物流单
     *
     * @param subId
     * @return
     */
    @Override
    @DS("ch999oanew")
    public QueryWuliuBySubResVO queryWuliuBySubId(Integer subId) {
        return baseMapper.queryWuliuBySubId(subId);
    }

    /**
     * 良品订单查询物流单
     *
     * @param subId
     * @return
     */
    @Override
    public List<WuLiuEntity> getWuliuEntityBySubId(Integer subId) {
        return baseMapper.queryWuliuEntityBySubId(subId);
    }

    /**
     * 查询订单预计送达时间
     *
     * @param subId
     * @return
     */
    @Override
    @DS("ch999oanew")
    public SubExpectTimeDTO getSubExpectTimeBySubId(Integer subId) {
        return baseMapper.getSubExpectTimeBySubId(subId);
    }

    @Override
    public List<OrderOutStockPageRes> getSubIdListByPpid(OrderOutStockPageReqVO req) {
        List<OrderOutStockPageRes> result = this.baseMapper.getSubIdListByPpid(req);
        return result;
    }

    @Override
    public List<OrderOutStockPageRes> getSubIdListByMkcId(OrderOutStockPageReqVO req) {
        List<OrderOutStockPageRes> result = this.baseMapper.getSubIdListByMkcId(req);
        return result;
    }
    @Override
    public List<OrderOutStockPageRes> getSubIdListByImei(OrderOutStockPageReqVO req) {
        List<OrderOutStockPageRes> result = this.baseMapper.getSubIdListByImei(req);
        return result;
    }

    @Override
    public RecoverMarketinfo getRecoverSub(Integer subId) {
        RecoverMarketinfo sub = this.lambdaQuery().eq(RecoverMarketinfo::getSubId, subId)
                .list().stream().findFirst().orElse(null);
        return sub;
    }

    @Override
    public List<OrderOutStockPageRes> getSubIdListByBarcode(OrderOutStockPageReqVO req) {
        List<OrderOutStockPageRes> result = this.baseMapper.getSubIdListByBarcode(req);
        return result;
    }

    /**
     * @param expectTime
     * @return
     */
    @DS("oanewWrite")
    @Override
    public boolean updateSubExpectTime(Integer subId, LocalDateTime expectTime) {
        return this.lambdaUpdate()
                .set(RecoverMarketinfo::getExpecttime, expectTime)
                .eq(RecoverMarketinfo::getSubId, subId)
                .update();
    }
}
