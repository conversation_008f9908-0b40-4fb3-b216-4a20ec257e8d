package com.jiuji.oa.wuliu.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.wuliu.bo.SellerInfoBO;
import com.jiuji.oa.wuliu.dto.OnlineNationalSupplementStockDTO;
import com.jiuji.oa.wuliu.entity.WuLiuBasket2Entity;
import com.jiuji.oa.wuliu.entity.WuLiuBasketEntity;

import java.util.List;

/**
 * 订单商品表,责任小组：销售 服务类
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-15
 */
public interface IWuLiuBasketService extends IService<WuLiuBasketEntity> {

    /**
     * orderServices.getSubBasket
     * 获取订单商品
     *
     * @param danHaoBind Integer
     * @param xcAreaId   瑕疵库存地区
     * @param showDel  Integer
     * @return List<WuLiuBasket2Entity>
     * @date 2021-10-15
     * <AUTHOR> [<EMAIL>]
     */
    List<WuLiuBasket2Entity> getSubBasket(Integer danHaoBind, Integer xcAreaId, Integer showDel);

    SellerInfoBO getSellerInfoBySubId(Integer subId);
    OnlineNationalSupplementStockDTO getOnlineNationalSupplementStockBySubId(Integer subId);
}
