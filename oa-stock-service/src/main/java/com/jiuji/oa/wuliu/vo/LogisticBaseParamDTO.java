package com.jiuji.oa.wuliu.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * LogisticBaseParam
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-16
 */
@Data
@Accessors(chain = true)
public class LogisticBaseParamDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 门店 ID
     */
    @JsonProperty("sendShopId")
    @JSONField(name = "sendShopId")
    private Integer shopId;

    /**
     * 门店名称
     */
    @JsonProperty("sendShopName")
    @JSONField(name = "sendShopName")
    private String shopName;

    /**
     * 门店 ID
     */
    @JsonProperty("receiveShopId")
    @JSONField(name = "receiveShopId")
    private Integer receiveShopId;

    /**
     * 门店名称
     */
    @JsonProperty("receiveShopName")
    @JSONField(name = "receiveShopName")
    private String receiveShopName;
}
