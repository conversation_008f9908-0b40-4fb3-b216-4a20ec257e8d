package com.jiuji.oa.wuliu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 门店常用资产申请表[责任小组:办公]
 * @TableName AreaAssetSub
 */
@TableName(value ="AreaAssetSub")
@Data
public class Areaassetsub implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 
     */
    private String title;

    /**
     * 
     */
    private Integer areaid;

    /**
     * 
     */
    private Integer subcheck;

    /**
     * 
     */
    private String inuser;

    /**
     * 
     */
    private String comment;

    /**
     * 
     */
    private Date dtime;

    /**
     * 
     */
    private String checkuser;

    /**
     * 
     */
    private Date checktime;

    /**
     * 
     */
    private String outuser;

    /**
     * 
     */
    private Date outtime;

    /**
     * 
     */
    private String result;

    /**
     * 
     */
    private Long wuliudan;

    /**
     * 
     */
    private Date wuliutime;

    /**
     * 
     */
    private String confirmuser;

    /**
     * 
     */
    private Date confirmtime;

    /**
     * 
     */
    private String departcode;

    /**
     * 
     */
    private Long piqianid;

    /**
     * 
     */
    private Long pzid;

    /**
     * 
     */
    private Integer outareaid;

    /**
     * 
     */
    private Integer emergencylevel;

    /**
     * 
     */
    private Integer departId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}