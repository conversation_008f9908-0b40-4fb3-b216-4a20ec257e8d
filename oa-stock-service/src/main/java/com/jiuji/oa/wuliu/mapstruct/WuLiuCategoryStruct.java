package com.jiuji.oa.wuliu.mapstruct;


import com.jiuji.oa.wuliu.entity.WuLiuCategoryEntity;
import com.jiuji.oa.wuliu.vo.res.WuLiuCategoryResVO;
import com.jiuji.tc.utils.enums.EnumVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * WuLiuCategoryStruct
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-12-05
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface WuLiuCategoryStruct {

    /**
     * toWuLiuCategoryResVO
     *
     * @param wuLiuCategoryEntity
     * @return WuLiuCategoryResVO
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-05
     */
    @Mapping(target = "code", source = "cateId")
    WuLiuCategoryResVO toWuLiuCategoryResVO(WuLiuCategoryEntity wuLiuCategoryEntity);

    /**
     * toWuLiuCategoryResVO
     *
     * @param wuLiuCategoryEntity
     * @return List<WuLiuCategoryResVO>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-05
     */
    List<WuLiuCategoryResVO> toWuLiuCategoryResVO(List<WuLiuCategoryEntity> wuLiuCategoryEntity);

    /**
     * toEnumVO
     * @param wuLiuCategoryRes
     * @return
     */
    @Mapping(target = "label", source = "cateName")
    @Mapping(target = "value", source = "cateId")
    EnumVO toEnumVO(WuLiuCategoryResVO wuLiuCategoryRes);
}
