/*
 *     Copyright © 2006 - 2020 九机网 All Rights Reserved
 *
 */
package com.jiuji.oa.wuliu.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.nc.common.exception.CustomizeException;
import com.jiuji.oa.wuliu.dto.SubPositionDTO;
import com.jiuji.oa.wuliu.dto.req.SubPositionReq;
import com.jiuji.oa.wuliu.entity.WuLiuRecoverMarketInfo2Entity;
import com.jiuji.oa.wuliu.entity.WuLiuRecoverMarketInfoEntity;
import com.jiuji.oa.wuliu.mapper.WuLiuRecoverMarketInfoMapper;
import com.jiuji.oa.wuliu.service.IWuLiuRecoverMarketInfoService;
import com.jiuji.oa.wuliu.vo.RouteVO;
import com.jiuji.oa.wuliu.vo.res.WuliuOrderInfoRes;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;


/**
 * 转售单（良品订单）,责任小组：回收 ServiceImpl
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-09
 */
@RequiredArgsConstructor
@Slf4j
@DS("oanewWrite")
@Service
public class WuLiuRecoverMarketInfoServiceImpl extends ServiceImpl<WuLiuRecoverMarketInfoMapper, WuLiuRecoverMarketInfoEntity> implements IWuLiuRecoverMarketInfoService {

    @Override
    public WuLiuRecoverMarketInfoEntity getRecoverMarketInfo(Integer subId, Integer areaId) {
        return baseMapper.getRecoverMarketInfo(subId, areaId);
    }

    @Override
    public List<WuLiuRecoverMarketInfo2Entity> getSubBasket(Integer subId, Integer showDel) {
        showDel = Optional.ofNullable(showDel).orElse(0);
        return baseMapper.getSubBasket(subId, showDel);
    }

    @Override
    public Integer getBasketId(Integer danHaoBind) {
        return baseMapper.getBasketId(danHaoBind);
    }

    @Override
    public List<LocalDateTime> selectSubdate(Long subId) {
        return this.baseMapper.selectSubdate(subId);

    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSellerBySubId(RouteVO routeVO) {
        boolean b = this.getBaseMapper().updateSellerBySubId(routeVO.getSubId(), routeVO.getUserName());
        if(!b){
            throw new CustomizeException("数据库更新异常");
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updatesubCheckBySubId(RouteVO routeVO) {
        return this.lambdaUpdate().eq(WuLiuRecoverMarketInfoEntity::getSubId, routeVO.getSubId())
                .eq(WuLiuRecoverMarketInfoEntity::getSubCheck, 0)
                .set(WuLiuRecoverMarketInfoEntity::getSubCheck, 1)
                .update();
    }

    /**
     * 查询良品订单收货位置信息
     *
     * @param sub
     * @return
     */
    @Override
    @DS("ch999oanew")
    public SubPositionDTO getSubPositionBySub(SubPositionReq sub) {
        return baseMapper.getSubPositionBySub(sub);
    }

    /**
     * 支付商户订单号查询订单物流信息
     *
     * @param payId
     * @return
     */
    @Override
    @DS("ch999oanew")
    public WuliuOrderInfoRes getOrderByPayId(Integer payId) {
        return this.baseMapper.getOrderByPayId(payId);
    }

    /**
     * 支付商户订单号查询订单物流信息
     *
     * @param payDes
     * @return
     */
    @Override
    @DS("ch999oanew")
    public WuliuOrderInfoRes getOrderByPayDes(String payDes) {
        return this.baseMapper.getOrderByPayDes(payDes);
    }

    /**
     * 查询良品订单
     *
     * @param subId
     * @return
     */
    @Override
    @DS("ch999oanew")
    public WuLiuRecoverMarketInfoEntity getSubBySubId(Integer subId) {
        return baseMapper.getSubBySubId(subId);
    }

    @Override
    @DS("ch999oanew")
    public SubPositionDTO getSubAddressPositionBySubId(SubPositionReq req) {
        return baseMapper.getSubAddressPositionBySubId(req);
    }

}