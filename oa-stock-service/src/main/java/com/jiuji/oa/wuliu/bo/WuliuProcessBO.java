package com.jiuji.oa.wuliu.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.codehaus.jackson.annotate.JsonProperty;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/8/22 15:37
 */
@Data
public class WuliuProcessBO {
    @JsonProperty("LogId")
    private String logId;
    @JsonProperty("BusinessId")
    private String businessId;
    @JsonProperty("BusinessType")
    private Integer businessType;
    @JsonProperty("BusinessNode")
    private Integer businessNode;
    @JsonProperty("OperationTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime operationTime;
    @JsonProperty("Xtentant")
    private Long xtentant;
    @JsonProperty("AreaId")
    private Integer areaId;
    @JsonProperty("EmployeeName")
    private String employeeName;
    @JsonProperty("Source")
    private String source;
    @JsonProperty("MkcIds")
    private String mkcIds;
    @JsonProperty("Status")
    private Integer status;
    @JsonProperty("NextAreaId")
    private Integer nextAreaId;
    @JsonProperty("DelayMillisecond")
    private Integer delayMillisecond;
    @JsonProperty("RelatedId")
    private Integer relatedId;
    @JsonProperty("json")
    private String json;
}