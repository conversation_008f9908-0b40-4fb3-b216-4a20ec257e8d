/*
 *    Copyright © 2006 - 2020 九机网 All Rights Reserved
 *
 */

package com.jiuji.oa.wuliu.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.orderdynamics.dto.DiaoboWuliuSubDTO;
import com.jiuji.oa.orderdynamics.dto.DiySubDTO;
import com.jiuji.oa.orderdynamics.dto.WuliuSubDTO;
import com.jiuji.oa.orderdynamics.vo.response.QueryWuliuBySubResVO;
import com.jiuji.oa.wuliu.dto.SubExpectTimeDTO;
import com.jiuji.oa.wuliu.dto.SubPositionDTO;
import com.jiuji.oa.wuliu.dto.req.SubPositionReq;
import com.jiuji.oa.wuliu.entity.WuLiuEntity;
import com.jiuji.oa.wuliu.entity.WuLiuSubEntity;
import com.jiuji.oa.wuliu.vo.res.WuliuOrderInfoRes;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 订单,责任小组：销售 Mapper
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-09-29
 */
@Mapper
public interface WuLiuSubMapper extends BaseMapper<WuLiuSubEntity> {

    /**
     * getSub
     *
     * @param subId
     * @return WuLiuSubEntity
     * @date 2021-10-11
     * <AUTHOR> [<EMAIL>]
     */
    WuLiuSubEntity getSub(Integer subId);

    /**
     * 物流单查询订单
     * @param wuliuId
     * @return
     */
    List<WuliuSubDTO> getWuliuSubByWuliuIds(@Param("wuliuId") Integer wuliuId);

    /**
     * 快递单查询订单
     * @param nu
     * @return
     */
    List<DiySubDTO> getDiySubByNu(@Param("nu") String nu);

    /**
     * 快递单号查询订单
     * @param nu
     * @return
     */
    List<WuliuSubDTO> getWuliuSubByNu(@Param("nu") String nu);

    /**
     * 快递单号查询调拨单订单
     * @param nu
     * @return
     */
    List<DiaoboWuliuSubDTO> getTransferWuliuSubByNu(@Param("nu") String nu);

    /**
     * 物流单号查询调拨单订单
     * @param wuliuId
     * @return
     */
    List<DiaoboWuliuSubDTO> getTransferWuliuSubByWuliuId(@Param("wuliuId") Integer wuliuId);

    /**
     * 订单查询物流单
     * @param subId
     * @return
     */
    QueryWuliuBySubResVO queryWuliuBySubId(Integer subId);

    /**
     * 包含调拨
     * 订单查询物流单
     * @param subId
     * @return
     */
    List<WuLiuEntity> getWuliuEntityBySubId(@Param("subId") Integer subId);

    /**
     * 查询订单规定送达时间
     * @param subId
     * @return
     */
    SubExpectTimeDTO getSubExpectTimeBySubId(Integer subId);

    /**
     * 查询订单收货位置信息
     *
     * @param req
     * @return
     */
    SubPositionDTO getSubPositionBySub(@Param("req") SubPositionReq req);

    /**
     * 支付商户订单号查询订单物流信息
     *
     * @param payId
     * @return
     */
    WuliuOrderInfoRes getOrderByPayId(@Param("payId") Integer payId);
    WuliuOrderInfoRes getOrderByPayDes(@Param("payDes") String payDes);

    WuLiuSubEntity getBySubId(Long subId);

    String getSubTelByNu(@Param("nu") String nu);

    /**
     * 查询订单地址经纬度
     * @param req
     * @return
     */
    SubPositionDTO getSubAddressPositionBySubId(@Param("req") SubPositionReq req);
}
