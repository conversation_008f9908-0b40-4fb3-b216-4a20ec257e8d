package com.jiuji.oa.wuliu.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum WuliuStatistcsCategrayEnum implements CodeMessageEnumInterface {
    /**
     * 物流统计类别
     **/
    UNKNOWN(0, "未知"),
    WAIT_SEND_WULIU(1, "待发扫描"),
    SEND_WULIU(2, "发货扫描"),
    RECEIVE_WULIU(3, "物流送达"),
    COMPLETE_WULIU(4, "签收完成");


    private final Integer code;
    private final String message;

}
