package com.jiuji.oa.wuliu.vo;


import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * Address
 * 地址信息
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-16
 */
@Data
@Accessors(chain = true)
public class CancelResponseDataVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("name")
    @JSONField(name = "name")
    private String name;

    @JsonProperty("mobile")
    @JSONField(name = "mobile")
    private String mobile;

    @JsonProperty("prov")
    @JSONField(name = "prov")
    private String prov;

    @JsonProperty("city")
    @JSONField(name = "city")
    private String city;

    @JsonProperty("county")
    @JSONField(name = "county")
    private String county;

    @JsonProperty("address")
    @JSONField(name = "address")
    private String address;

    @JsonProperty("areaid")
    @JSONField(name = "areaid")
    private Integer areaid;

    @JsonProperty("fullAddress")
    @JSONField(name = "fullAddress")
    private String fullAddress;

    public String getFullAddress() {
        if (StringUtils.isBlank(address)) {
            return "";
        }
        String addr = address;
        if (StringUtils.isNotBlank(prov) && addr.startsWith(prov)) {
            addr = address.replaceAll("^" + prov, "");
        }
        if (StringUtils.isNotBlank(city) && addr.startsWith(city)) {
            addr = address.replaceAll("^" + city, "");
        }
        if (StringUtils.isNotBlank(county) && addr.startsWith(county)) {
            addr = address.replaceAll("^" + county, "");
        }
        // 添加区域显示
        if (StringUtils.isNotBlank(county) && addr.startsWith(county)) {
            addr = String.format("%s%s%s%s", prov, city, county, addr);
        } else if (StringUtils.isNotBlank(city) && !addr.startsWith(city)) {
            addr = String.format("%s%s%s", prov, city, addr);
        } else if (!addr.startsWith(prov)) {
            addr = String.format("%s%s", prov, addr);
        }

        return addr;
    }

}
