package com.jiuji.oa.wuliu.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * sub.subFlagRecord
 * 订单 特殊类型状态记录表
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-16
 */
@Data
@Accessors(chain = true)
public class SubFlagRecordDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("id")
    @JSONField(name = "id")
    private Long id;

    @JsonProperty("sub_id")
    @JSONField(name = "sub_id")
    private Long subId;

    /**
     * 状态类型
     */
    @JsonProperty("flagType")
    @JSONField(name = "flagType")
    private Byte flagType;

    /**
     * 状态
     */
    @JsonProperty("status")
    @JSONField(name = "status")
    private Byte status;

    @JsonProperty("dtime")
    @JSONField(name = "dtime")
    private LocalDateTime dtime;

}
