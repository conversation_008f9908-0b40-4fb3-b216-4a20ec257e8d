package com.jiuji.oa.wuliu.mapstruct;


import com.jiuji.oa.wuliu.entity.WuLiuLogEntity;
import com.jiuji.oa.wuliu.vo.res.WuLiuLogResVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;


/**
 * WuLiuLogStruct
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-12-05
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface WuLiuLogStruct {

    /**
     * toWuLiuLogResVO
     *
     * @param wuLiuCategoryEntity
     * @return List<WuLiuLogResVO>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-05
     */
    List<WuLiuLogResVO> toWuLiuLogResVO(List<WuLiuLogEntity> wuLiuCategoryEntity);

}
