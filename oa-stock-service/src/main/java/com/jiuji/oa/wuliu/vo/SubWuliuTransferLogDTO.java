package com.jiuji.oa.wuliu.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * SubWuliuTransferLogDTO
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-12-05
 */
@Data
public class SubWuliuTransferLogDTO {

    @JsonProperty("sub_id")
    @JSONField(name = "sub_id")
    private Long subId;

    @JsonProperty("product_name")
    @JSONField(name = "product_name")
    private String productName;

    @JsonProperty("product_color")
    @JSONField(name = "product_color")
    private String productColor;
    private Long areaid;
    private Long toareaid;

    @JsonProperty("basket_count")
    @JSONField(name = "basket_count")
    private Integer basketCount;
    private Integer lcount;
    private String com;
    private String nu;
}
