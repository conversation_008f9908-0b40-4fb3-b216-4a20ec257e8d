package com.jiuji.oa.wuliu.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class RouteVO {
    private Long subId;
    private Integer subCheck;
    private String userName;
    private List<String> rank;
    private Integer areaId;
    private Integer oldSubCheck;
    private Integer delivery;
    private Integer saleType;
    private Boolean addPaiSongFlag;
    private List<SubLogVO> logList;
}
