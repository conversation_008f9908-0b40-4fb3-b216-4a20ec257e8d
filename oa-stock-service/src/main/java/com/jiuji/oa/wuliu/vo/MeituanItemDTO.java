package com.jiuji.oa.wuliu.vo;


import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.jiuji.oa.stock.common.util.SysUtils;
import com.jiuji.oa.stock.logisticscenter.vo.LogisticsBase;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * meituanServices.meituanItem
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-16
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class MeituanItemDTO extends LogisticsBase implements Serializable {

    private static final long serialVersionUID = 1L;

    private static String secret = SysUtils.isJiuJiProd() ? "zcY_w]T}%?0C5Yx$)k}R4gyKpI7d54!+/QXlF2w@)y+{Li-&piIt,>vL^dDxX170" : "u1SpL{_8.!PTYCXw@1moQ$JtR#7.;n{dzykcO*3+7rT;4[WLEw88N*Bd${33@|[h";

    private static String url = "https://peisongopen.meituan.com/api/";

    @JsonProperty("appkey")
    @JSONField(name = "appkey")
    private String appKey;

    @JsonProperty("timestamp")
    @JSONField(name = "timestamp")
    private String timestamp;

    @JsonProperty("version")
    @JSONField(name = "version")
    private String version = "1.0";

    @JsonProperty("sign")
    @JSONField(name = "sign")
    private String sign;

    /**
     * 即配送活动标识，由外部系统生成，不同order_id应对应不同的delivery_id，若因美团系统故障导致发单接口失败，可利用相同的delivery_id重新发单，系统视为同一次配送活动，若更换delivery_id，则系统视为两次独立配送活动。
     */
    @JsonProperty("delivery_id")
    @JSONField(name = "delivery_id")
    private Long deliveryId;

    /**
     * 订单id，即该订单在合作方系统中的id，最长不超过32个字符
     */
    @JsonProperty("order_id")
    @JSONField(name = "order_id")
    private String orderId;

    /**
     * 取货门店id，即合作方向美团提供的门店id
     */
    @JsonProperty("shop_id")
    @JSONField(name = "shop_id")
    private String shopId;

    /**
     * 配送服务代码，详情见合同 飞速达:4002 快速达:4011 及时达:4012 集中送:4013
     */
    @JsonProperty("delivery_service_code")
    @JSONField(name = "delivery_service_code")
    private Integer deliveryServiceCode;

    /**
     * 收件人名称，最长不超过256个字符
     */
    @JsonProperty("receiver_name")
    @JSONField(name = "receiver_name")
    private String receiverName;

    /**
     * 收件人地址，最长不超过512个字符
     */
    @JsonProperty("receiver_address")
    @JSONField(name = "receiver_address")
    private String receiverAddress;

    /**
     * 收件人电话，最长不超过64个字符
     */
    @JsonProperty("receiver_phone")
    @JSONField(name = "receiver_phone")
    private String receiverPhone;

    /**
     * 收件人经度（火星坐标或百度坐标，和 coordinate_type 字段配合使用），坐标 * （10的六次方），如 116398419
     */
    @JsonProperty("receiver_lng")
    @JSONField(name = "receiver_lng")
    private Integer receiverLng;

    /**
     * 收件人纬度（火星坐标或百度坐标，和 coordinate_type 字段配合使用），坐标 * （10的六次方），如 39985005
     */
    @JsonProperty("receiver_lat")
    @JSONField(name = "receiver_lat")
    private Integer receiverLat;

    /**
     * 坐标类型，0：火星坐标（高德，腾讯地图均采用火星坐标） 1：百度坐标 （默认值为0
     */
    @JsonProperty("coordinate_type")
    @JSONField(name = "coordinate_type")
    private Integer coordinateType;

    /**
     * 货物价格，单位为元，精确到小数点后两位（如果小数点后位数多于两位，则四舍五入保留两位小数），范围为0-5000
     */
    @JsonProperty("goods_value")
    @JSONField(name = "goods_value")
    private BigDecimal goodsValue;

    /**
     *
     */
    @JsonProperty("goods_height")
    @JSONField(name = "goods_height")
    private BigDecimal goodsHeight;

    /**
     * 货物宽度，单位为cm，精确到小数点后两位（如果小数点后位数多于两位，则四舍五入保留两位小数），范围为0-50
     */
    @JsonProperty("goods_width")
    @JSONField(name = "goods_width")
    private BigDecimal goodsWidth;

    /**
     * 货物长度，单位为cm，精确到小数点后两位（如果小数点后位数多于两位，则四舍五入保留两位小数），范围为0-65
     */
    @JsonProperty("goods_length")
    @JSONField(name = "goods_length")
    private BigDecimal goodsLength;

    /**
     * 货物重量，单位为kg，精确到小数点后两位（如果小数点后位数多于两位，则四舍五入保留两位小数），范围为0-50
     */
    @JsonProperty("goods_weight")
    @JSONField(name = "goods_weight")
    private BigDecimal goodsWeight;

    /**
     *
     */
    @JsonProperty("goods_detail")
    @JSONField(name = "goods_detail")
    private String goodsDetail;

    /**
     * 货物取货信息，用于骑手到店取货，最长不超过100个字符
     */
    @JsonProperty("goods_pickup_info")
    @JSONField(name = "goods_pickup_info")
    private String goodsPickupInfo;

    /**
     * 货物交付信息，最长不超过100个字符
     */
    @JsonProperty("goods_delivery_info")
    @JSONField(name = "goods_delivery_info")
    private String goodsDeliveryInfo;

    /**
     * 期望取货时间，时区为GMT+8，当前距离Epoch（1970年1月1日) 以秒计算的时间，即unix-timestamp。
     */
    @JsonProperty("expected_pickup_time")
    @JSONField(name = "expected_pickup_time")
    private Long expectedPickupTime;

    /**
     * 期望送达时间，时区为GMT+8，当前距离Epoch（1970年1月1日) 以秒计算的时间，即unix-timestamp
     */
    @JsonProperty("expected_delivery_time")
    @JSONField(name = "expected_delivery_time")
    private Long expectedDeliveryTime;

    /**
     * 订单类型，默认为0 0: 即时单(尽快送达，限当日订单) 1: 预约单
     */
    @JsonProperty("order_type")
    @JSONField(name = "order_type")
    private Integer orderType;

    /**
     * 门店订单流水号，建议提供，方便骑手门店取货，最长不超过32个字符
     */
    @JsonProperty("poi_seq")
    @JSONField(name = "poi_seq")
    private String poiSeq;

    /**
     * 备注，最长不超过200个字符。
     */
    @JsonProperty("note")
    @JSONField(name = "note")
    private String note;

    /**
     * 骑手应付金额，单位为元，精确到分【预留字段】
     */
    @JsonProperty("cash_on_delivery")
    @JSONField(name = "cash_on_delivery")
    private BigDecimal cashOnDelivery;

    /**
     * 骑手应收金额，单位为元，精确到分【预留字段】
     */
    @JsonProperty("cash_on_pickup")
    @JSONField(name = "cash_on_pickup")
    private BigDecimal cashOnPickup;

    /**
     * 发票抬头，最长不超过256个字符【预留字段】
     */
    @JsonProperty("invoice_title")
    @JSONField(name = "invoice_title")
    private String invoiceTitle;

    /**
     * 订单来源：101.美团（外卖&闪购）、102.饿了么、103.京东到家、201.商家web网站、202.商家小程序-微信、203.商家小程序-支付宝、204.商家APP、205.商家热线、其他，请直接填写中文字符串，最长不超过20个字符、非「其他」需传代码
     */
    @JsonProperty("outer_order_source_desc")
    @JSONField(name = "outer_order_source_desc")
    private String outerOrderSourceDesc;

    public String getAppKey() {
        String temp = SysUtils.isJiuJiProd() ? "2b04d994c1554f118b3621e804a1d153" : "6bc5c6b263a84352abea49819c425fac" ;
        return appKey == null ? temp : appKey;
    }

}
