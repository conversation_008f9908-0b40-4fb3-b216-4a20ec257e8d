package com.jiuji.oa.wuliu.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 打印次数,责任小组：运营
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("PrintCount")
public class PrintCountEntity extends Model<PrintCountEntity> {

    private static final long serialVersionUID = -1148233492496916812L;

    private Long id;
    private Integer subId;
    private Integer printCount;
    private Integer printType;


    @Override
    protected Serializable pkVal() {
        return null;
    }

}
