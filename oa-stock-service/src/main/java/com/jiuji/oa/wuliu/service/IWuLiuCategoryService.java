package com.jiuji.oa.wuliu.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.nc.abnormal.vo.ShowPrintingEnumVOV2;
import com.jiuji.oa.wuliu.entity.WuLiuCategoryEntity;
import com.jiuji.oa.wuliu.vo.res.WuLiuCategoryResVO;
import com.jiuji.tc.utils.enums.EnumVO;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR> liu ming
 * @since 2021-09-27
 */
public interface IWuLiuCategoryService extends IService<WuLiuCategoryEntity> {

    /**
     * 查询物流单分类
     *
     * @return 物流单分类
     */
    List<WuLiuCategoryResVO> queryWuLiuCategoryList();

    /**
     * getWuLiuCategoryList
     *
     * @return List<WuLiuCategoryEntity>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-05
     */
    List<WuLiuCategoryEntity> getWuLiuCategoryList();

    /**
     * getWuLiuCateName
     *
     * @param cateId
     * @return String
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-05
     */
    String getWuLiuCateName(Integer cateId);

    /**
     * getWuLiuCateNameList
     *
     * @return Map<Integer,String>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-05
     */
    Map<Integer, String> getWuLiuCateNameList();

    /**
     * 查询分类枚举
     */
    List<EnumVO> getEnumVoList();

    /**
     * 查询分类枚举
     */
    List<ShowPrintingEnumVOV2> getShowPrintingEnumVOList();

    /**
     * 查询分类map
     * @return
     */
    Map<Integer,String> getWuLiuCateNameMap();
}
