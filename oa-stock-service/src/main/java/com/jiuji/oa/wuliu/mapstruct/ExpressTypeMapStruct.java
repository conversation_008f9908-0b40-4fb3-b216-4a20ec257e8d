package com.jiuji.oa.wuliu.mapstruct;

import com.jiuji.oa.wuliu.entity.ExpressTypeEntity;
import com.jiuji.oa.wuliu.vo.req.ExpressTypeVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ExpressTypeMapStruct {

    /**
     * 转换为ExpressTypeVO
     *
     * @param entity
     * @return
     */
    ExpressTypeVO toExpressTypeVO(ExpressTypeEntity entity);

}
