package com.jiuji.oa.wuliu.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 日期类型的枚举
 *
 * <AUTHOR>
 * @date 2021/10/09
 */
@Getter
@AllArgsConstructor
public enum OperationStateEnum implements CodeMessageEnumInterface {
    /**
     * 待复核
     */
    PENDING(0, "待复核"),
    /**
     * 已复核
     */
    APPROVED(1, "一致"),
    /**
     * 复核不一致
     */
    REJECTED(2, "不一致");

    /**
     * 代码
     */
    private Integer code;
    /**
     * 消息
     */
    private String message;

    /**
     * message
     * @param deliveryCode
     * @return
     */
    public static String getMessageByCode (Integer code) {
        for (OperationStateEnum stateEnum : values()) {
            if (stateEnum.getCode().equals(code)) {
                return stateEnum.getMessage();
            }
        }
        return "";
    }
}
