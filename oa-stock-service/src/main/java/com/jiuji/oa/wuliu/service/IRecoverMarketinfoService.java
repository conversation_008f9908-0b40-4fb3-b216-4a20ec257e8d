package com.jiuji.oa.wuliu.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.orderdynamics.vo.response.QueryWuliuBySubResVO;
import com.jiuji.oa.stock.develivery.vo.req.OrderOutStockPageReqVO;
import com.jiuji.oa.stock.develivery.vo.res.OrderOutStockPageRes;
import com.jiuji.oa.wuliu.dto.SubExpectTimeDTO;
import com.jiuji.oa.wuliu.entity.RecoverMarketinfo;
import com.jiuji.oa.wuliu.entity.WuLiuEntity;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 转售单（良品订单）[责任小组:回收] 服务类
 * </p>
 *
 * <AUTHOR> yao yao
 * @since 2022-06-08
 */
public interface IRecoverMarketinfoService extends IService<RecoverMarketinfo> {

    /**
     * 良品单查询物流单
     * @param subId
     * @return
     */
    QueryWuliuBySubResVO queryWuliuBySubId(Integer subId);

    /**
     * 良品订单查询物流单
     * @param subId
     * @return
     */
    List<WuLiuEntity> getWuliuEntityBySubId(Integer subId);

    /**
     * 查询订单预计送达时间
     *
     * @param subId
     * @return
     */
    SubExpectTimeDTO getSubExpectTimeBySubId(Integer subId);


    List<OrderOutStockPageRes> getSubIdListByPpid(OrderOutStockPageReqVO req);

    List<OrderOutStockPageRes> getSubIdListByMkcId(OrderOutStockPageReqVO req);

    List<OrderOutStockPageRes> getSubIdListByImei(OrderOutStockPageReqVO req);

    RecoverMarketinfo getRecoverSub(Integer subId);

    List<OrderOutStockPageRes> getSubIdListByBarcode(OrderOutStockPageReqVO req);

    boolean updateSubExpectTime(Integer subId, LocalDateTime expectTime);
}
