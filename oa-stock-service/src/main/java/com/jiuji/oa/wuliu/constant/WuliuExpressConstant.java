package com.jiuji.oa.wuliu.constant;

/**
 * <AUTHOR>
 * @date 2022/7/26 11:29
 */
public class WuliuExpressConstant {

    /**
     * saveJingdongPrintInfo
     */
    public static final String ACT_JINGDONGPRINTINFO = "saveJingdongPrintInfo";

    /**
     * saveShunfengNoInfo
     */
    public static final String ACT_SHUNFENGNOINFO = "saveShunfengNoInfo";

    /**
     * saveWuliuWandDian
     */
    public static final String ACT_WULIUWANGDIAN = "saveWuliuWandDian";

    /**
     * saveWuLiuExpressExtend
     */
    public static final String ACT_WULIUEXPRESSEXTEND = "saveWuLiuExpressExtend";

    /**
     * saveWuliuLog
     */
    public static final String ACT_WULIULOG = "saveWuliuLog";

    /**
     * saveWuliunoex
     */
    public static final String ACT_WULIUNOEX = "saveWuliunoex";

    /**
     * saveZtoBillInfo
     */
    public static final String ACT_ZTOBILLINFO = "saveZtoBillInfo";

    /**
     * 计算物流单成本
     */
    public static final String ACT_CALCULATE_DISTRIBUTION_COST = "calculateDistributionCost";

    /**
     * 中台保存快递单
     */
    public static final String ACT_SAVE_LOGISTICS_EXPRESS_ORDER = "saveLogisticsExpressOrder";

    /**
     * 物流时效计算
     */
    public static final String ACT_WULIU_RELATED_RECORD = "wuliuRelatedRecord";

    /**
     * 物流单状态变更
     */
    public static final String ACT_WULIU_CHANGE_STATS = "wuliuChangeStats";

    /**
     * 物流业务节点
     */
    public static final String ACT_WULIU_PROCESS_EVENT = "wuliu_process_event";

    /**
     * 物流送达
     */
    public static final String ACT_RECEIVE_WULIU = "receiveWuliu";

    /**
     * 快递签收物流单处理
     */
    public static final String ACT_WULIUEXPRESS_SIGNED = "handleWuliuExpressSigned";

    /**
     * 快递签收物流单处理
     */
    public static final String ACT_APPLE_PURCHASE_WULIU = "applePurchaseWuliu";

    /**
     * 调拨单已发货状态，物流单叫跑腿后，推送OA消息给调拨单发货操作人
     */
    public static final String ACT_DIAOBO_PAOTUI_MESSAGE_PUSH = "diaoboPaotuiMessagePush";

    /**
     * oa消息推送 act
     */
    public static final String ACT_OAPUSH_MESSAGE_PUSH = "OAPushMessagePush";
}
