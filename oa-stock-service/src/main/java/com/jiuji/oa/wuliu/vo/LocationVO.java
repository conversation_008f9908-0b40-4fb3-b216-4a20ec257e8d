package com.jiuji.oa.wuliu.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/5/19 10:57
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
public class LocationVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 经度
     **/
    private Double longitude;

    /**
     * 纬度
     **/
    private Double latitude;

}
