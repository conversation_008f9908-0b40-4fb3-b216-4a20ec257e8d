package com.jiuji.oa.wuliu.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.wuliu.entity.WuLiuRecoverSubEntity;
import com.jiuji.oa.wuliu.mapper.WuLiuRecoverSubMapper;
import com.jiuji.oa.wuliu.service.IWuLiuRecoverSubService;
import org.springframework.stereotype.Service;

/**
 * 回收子订单,责任小组：回收 服务实现类
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-15
 */
@Service
public class WuLiuRecoverSubServiceImpl extends ServiceImpl<WuLiuRecoverSubMapper, WuLiuRecoverSubEntity> implements IWuLiuRecoverSubService {
}
