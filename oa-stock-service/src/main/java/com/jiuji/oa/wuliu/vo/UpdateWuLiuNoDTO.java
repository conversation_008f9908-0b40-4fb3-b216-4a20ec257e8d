package com.jiuji.oa.wuliu.vo;


import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;


/**
 * 通过队列调用 C# 方法数据传输实体
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-11-12
 */
@Data
@Accessors(chain = true)
public class UpdateWuLiuNoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 快递公司名
     */
    private String companyName;

    /**
     * 物流单号
     */
    private String wuLiuNo;

    /**
     * 订单号
     */
    private Long subId;

    /**
     * 员工名
     */
    private String user;

}
