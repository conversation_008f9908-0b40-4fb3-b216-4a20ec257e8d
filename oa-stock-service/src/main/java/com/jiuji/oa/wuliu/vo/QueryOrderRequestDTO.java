package com.jiuji.oa.wuliu.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * QueryOrderRequestDTO
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-12-13
 */
@Setter
@Getter
public class QueryOrderRequestDTO implements Serializable {

    @JsonProperty("deliveryId")
    public Long deliveryId;

    /**
     * 美团 1
     */
    @JsonProperty("expressType")
    public Integer expressType;

    /**
     *
     */
    @JsonProperty("platformInsideId")
    public String platformInsideId;

}
