package com.jiuji.oa.wuliu.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@TableName("SAAS_Jingdong_PrintInfo")
public class SaasJingdongPrintInfo {

    @TableId
    private Long id;
    @TableField("saas_tenant")
    private Integer saasTenant;
    @TableField("saas_areaid")
    private Integer saasAreaid;
    @TableField("orderId")
    private String orderId;
    @TableField("deliveryId")
    private String deliveryId;
    @TableField("promiseTimeType")
    private Integer promiseTimeType;
    @TableField("preSortResult")
    private String preSortResult;
    @TableField("transType")
    private Integer transType;
    @TableField("needRetry")
    private Boolean needRetry;
    @TableField("expressOperationMode")
    private Integer expressOperationMode;
}
