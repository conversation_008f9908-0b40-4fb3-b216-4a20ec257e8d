package com.jiuji.oa.wuliu.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 销售单状态枚举
 *
 * <AUTHOR>
 * @date 2021/10/09
 */
@Getter
@AllArgsConstructor
public enum WuliuClaimformSubTypeEnum implements CodeMessageEnumInterface {
    /**
     * 登记时间
     */
    ONE(1,"新机订单"),
    TWO(2,"良品订单"),
    THREE(3,"售后预约"),
    FOUR(4,"售后维修"),
    FIVE(5,"小件接件单"),
    SIX(6,"回收订单"),
    ;

    /**
     * 代码
     */
    private Integer code;
    /**
     * 消息
     */
    private String message;


    /**
     * 根据 key 获取 value
     *
     * @param key key
     * @return value
     */
    public static String getValue(Integer key) {
        for (WuliuClaimformSubTypeEnum item : values()) {
            if (item.getCode().equals(key)) {
                return item.getMessage();
            }
        }
        return null;
    }


    /**
     * 根据 value 获取 key
     * @param value
     * @return
     */
    public static Integer getKey(String value) {
        for (WuliuClaimformSubTypeEnum item : values()) {
            if (item.getMessage().equals(value)) {
                return item.getCode();
            }
        }
        return null;
    }
}
