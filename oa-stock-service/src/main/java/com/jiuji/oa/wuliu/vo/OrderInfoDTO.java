package com.jiuji.oa.wuliu.vo;


import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * EmsApiServices.OrderInfo
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-16
 */
@Data
@Accessors(chain = true)
public class OrderInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @JsonProperty("order_id")
    @JSONField(name = "order_id")
    private String orderId;

    /**
     * 寄件人信息
     */
    @JsonProperty("sender")
    @JSONField(name = "sender")
    private AddressDTO sender;

    /**
     * 收件人信息
     */
    @JsonProperty("receiver")
    @JSONField(name = "receiver")
    private AddressDTO receiver;

    /**
     * 包裹信息
     */
    @JsonProperty("cargos")
    @JSONField(name = "cargos")
    private List<CargosDTO> cargos;

}
