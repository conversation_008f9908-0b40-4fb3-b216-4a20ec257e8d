package com.jiuji.oa.wuliu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> liu ming
 * @since 2021-09-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("wuliuCategory")
public class WuLiuCategoryEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "cateId", type = IdType.AUTO)
    private Integer cateId;

    private Boolean isdel;

    private Long code;

    private Integer leve;

    @TableField("pId")
    private Integer pId;

    @TableField("cateName")
    private String cateName;

    @TableField("catRank")
    private Integer catRank;


}
