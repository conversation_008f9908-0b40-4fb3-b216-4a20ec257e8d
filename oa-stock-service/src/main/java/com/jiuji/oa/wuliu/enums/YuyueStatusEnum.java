package com.jiuji.oa.wuliu.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: gengjiaping
 * @date: 2020/3/13
 */
@Getter
@AllArgsConstructor
public enum YuyueStatusEnum implements CodeMessageEnumInterface {
    WQR(1,"未确认"),
    KFQR(2,"客服确认"),
    YWC(3,"已完成"),
    YWQR(4,"业务确认"),
    YSC(5,"已删除"),
    YSM(6,"已上门"),
    YDD(7,"已到店"),
    WQJ(8,"未取件"),
    YQX(10,"已取消");
    /**
     * 状态
     */
    private Integer code;
    /**
     * 名称
     */
    private String message;
}
