package com.jiuji.oa.wuliu.vo;


import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * SAAS平台信息
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-16
 */
@Data
@Accessors(chain = true)
public class SaasPlatformDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("saas_tenant")
    @JSONField(name = "saas_tenant")
    private Integer saasTenant;

    @JsonProperty("saas_areaid")
    @JSONField(name = "saas_areaid")
    private Integer saasAreaid;

    /**
     * 包裹数量
     */
    @JsonProperty("packageCount")
    @JSONField(name = "packageCount")
    private Integer packageCount;

    /**
     * 物流单 ID
     */
    @JsonProperty("WuliuId")
    @JSONField(name = "WuliuId")
    private Integer wuliuId;

    @JsonProperty("IsJiuJi")
    @JSONField(name = "<PERSON><PERSON><PERSON><PERSON><PERSON>")
    private Boolean isJiuJi;

}
