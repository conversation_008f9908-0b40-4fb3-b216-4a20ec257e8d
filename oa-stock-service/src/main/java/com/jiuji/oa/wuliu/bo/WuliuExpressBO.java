package com.jiuji.oa.wuliu.bo;

import com.jiuji.oa.wuliu.entity.WuLiuEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/1/9 11:16
 */
@Data
public class WuliuExpressBO {
    /**
     * 物流信息
     */
    private WuLiuEntity wuliu;
    /**
     * 包裹数
     */
    private Integer packageCount;
    /**
     * 月结卡号
     */
    private String monthlyCard;

    private String expressType;

    private BigDecimal weight;

    private BigDecimal vloumn;

    private Integer isDocall;
    /**
     * 京东：保价金额(保留小数点后两位)
     **/
    private BigDecimal guaranteeValueAmount;
    /**
     * 开始上门揽件时间
     */
    private String sendStartTime;
    /**
     * 操作人
     */
    private String inUser;

    /**
     * 寄件地址经纬度
     */
    private String sendPosition;

    /**
     * 收件地址经纬度
     */
    private String receivePosition;

    /**
     * 0 否， 1 是
     * 是否发送跑腿（达达，uu，美团）创建成功日志消息
     */
    private Integer sendMsgType;
}
