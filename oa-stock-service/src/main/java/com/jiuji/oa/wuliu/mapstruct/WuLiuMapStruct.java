package com.jiuji.oa.wuliu.mapstruct;

import com.jiuji.oa.stock.logisticscenter.vo.LogisticsBase;
import com.jiuji.oa.wuliu.bo.ExpressOrderAddBO;
import com.jiuji.oa.wuliu.bo.WuliuExpressSignedBO;
import com.jiuji.oa.wuliu.dto.ShouHouAddInfoDTO;
import com.jiuji.oa.wuliu.dto.YuYueAddInfoDTO;
import com.jiuji.oa.wuliu.entity.ExpressEnumEntity;
import com.jiuji.oa.wuliu.entity.WuLiuEntity;
import com.jiuji.oa.wuliu.vo.OrderGroupDTO;
import com.jiuji.oa.wuliu.vo.OrderInfoDTO;
import com.jiuji.oa.wuliu.vo.req.*;
import com.jiuji.oa.wuliu.vo.res.ExpressEnumVO;
import com.jiuji.oa.wuliu.vo.res.GetWuliuInfoRes;
import com.jiuji.oa.wuliu.vo.res.WuLiuResVO;
import com.jiuji.oa.wuliu.vo.res.WuliuAddressInfoRes;
import com.jiuji.tc.utils.enums.EnumVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * 物流单 MapStruct
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-09-028
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface WuLiuMapStruct {

    WuLiuMapStruct INSTANCE = Mappers.getMapper(WuLiuMapStruct.class);

    /**
     * 转 WuLiuEntity
     *
     * @param vo WuLiuAddOrUpdateReqVO
     * @return WuLiuEntity
     * @date 2021-09-28
     * <AUTHOR> [<EMAIL>]
     */
    WuLiuEntity addReqVoToEntity(WuLiuAddOrUpdateReqVO vo);

    /**
     * 转 WuLiuEntity
     *
     * @param vo WuLiuUpdateReqVO
     * @return WuLiuEntity
     * @date 2021-09-29
     * <AUTHOR> [<EMAIL>]
     */
    WuLiuEntity updateReqVoToEntity(WuLiuUpdateReqVO vo);

    /**
     * 转 WuLiuResVO
     *
     * @param entity WuLiuEntity
     * @return WuLiuResVO
     * @date 2021-09-28
     * <AUTHOR> [<EMAIL>]
     */
    WuLiuResVO toResVo(WuLiuEntity entity);


    /**
     * 转EnumVO
     *
     * @param entity
     * @return
     */
    @Mapping(source = "expressName", target = "label")
    @Mapping(source = "expressCode", target = "value")
    EnumVO toEnumVO(ExpressEnumEntity entity);

    /**
     * 转换为ExpressEnumVO
     *
     * @param entity
     * @return
     */
    @Mapping(target = "label", source = "expressName")
    @Mapping(target = "value", source = "expressCode")
    @Mapping(target = "selected", constant = "false")
    @Mapping(target = "type", constant = "0")
    ExpressEnumVO toExpressEnumVO(ExpressEnumEntity entity);

    /**
     * WuLiuAddOrUpdateReqV2VO 转 WuLiuAddOrUpdateReqVO
     *
     * @param vo WuLiuAddOrUpdateReqV2VO
     * @return WuLiuAddOrUpdateReqVO
     * <AUTHOR> [<EMAIL>]
     * @date 2022-01-14
     */
    @Mappings({
            @Mapping(source = "showWuliuClaimFlag", target = "isShowWuliuClaim"),
            @Mapping(source = "exceptionSubFlag", target = "isExceptionSub"),
            @Mapping(source = "createManuallyFlag", target = "isCreateManually"),
            @Mapping(source = "sname", target = "SName"),
            @Mapping(source = "smobile", target = "SMobile"),
            @Mapping(source = "saddress", target = "SAddress"),
            @Mapping(source = "sareaM", target = "SAreaM"),
            @Mapping(source = "scityId", target = "SCityId"),
            @Mapping(source = "rname", target = "RName"),
            @Mapping(source = "rmobile", target = "RMobile"),
            @Mapping(source = "raddress", target = "RAddress"),
            @Mapping(source = "rareaM", target = "RAreaM"),
            @Mapping(source = "rcityId", target = "RCityId"),
            @Mapping(source = "dtime", target = "DTime"),
            @Mapping(source = "sdid", target = "SDid"),
            @Mapping(source = "rdid", target = "RDid"),
            @Mapping(source = "wcateId", target = "WCateId"),
            @Mapping(source = "ctime", target = "CTime"),
    })
    WuLiuAddOrUpdateReqVO toWuLiuAddOrUpdateReqVo(WuLiuAddOrUpdateReqV2VO vo);

    @Mappings({
            @Mapping(source = "sname", target = "SName"),
            @Mapping(source = "smobile", target = "SMobile"),
            @Mapping(source = "saddress", target = "SAddress"),
            @Mapping(source = "scityid", target = "SCityId"),
            @Mapping(source = "rname", target = "RName"),
            @Mapping(source = "rmobile", target = "RMobile"),
            @Mapping(source = "raddress", target = "RAddress"),
            @Mapping(source = "rcityid", target = "RCityId"),
            @Mapping(source = "sareaid", target = "SAreaId"),
            @Mapping(source = "rareaid", target = "RAreaId"),
            @Mapping(source = "wcateId", target = "WCateId"),
            @Mapping(source = "wuType", target = "wuType"),
            @Mapping(source = "com", target = "com"),
            @Mapping(source = "nu", target = "nu"),
            @Mapping(source = "weight", target = "weight", defaultValue = "1"),
            @Mapping(source = "price", target = "price", defaultValue = "0"),
            @Mapping(source = "inPrice", target = "inPrice", defaultValue = "0"),
            @Mapping(source = "danHaoBind", target = "danHaoBind", defaultValue = "0"),
            @Mapping(source = "comment", target = "comment"),
            @Mapping(target = "stats", constant = "1"),
    })
    WuLiuEntity toWuLiuEntity(WuLiuAddReqV2VO reqV2);
    /**
     * 转 WuLiuEntity
     *
     * @param vo WuLiuUpdateReqVO
     * @return WuLiuEntity
     * @date 2021-09-29
     * <AUTHOR> [<EMAIL>]
     */
    @Mappings({
            @Mapping(source = "wuliuId", target = "id"),
            @Mapping(target = "SAreaId", expression = "java(com.jiuji.oa.wuliu.utils.WuliuUtil.convertNullValue(vo.getSareaId()))"),
            @Mapping(target = "SName", expression = "java(com.jiuji.oa.wuliu.utils.WuliuUtil.convertNullValue(vo.getSname()))"),
            @Mapping(target = "SMobile", expression = "java(com.jiuji.oa.wuliu.utils.WuliuUtil.convertNullValue(vo.getSmobile()))"),
            @Mapping(target = "SAddress", expression = "java(com.jiuji.oa.wuliu.utils.WuliuUtil.convertNullValue(vo.getSaddress()))"),
            @Mapping(target = "SCityId", expression = "java(com.jiuji.oa.wuliu.utils.WuliuUtil.convertNullValue(vo.getScityId()))"),
            @Mapping(target = "RAreaId", expression = "java(com.jiuji.oa.wuliu.utils.WuliuUtil.convertNullValue(vo.getRareaId()))"),
            @Mapping(target = "RName", expression = "java(com.jiuji.oa.wuliu.utils.WuliuUtil.convertNullValue(vo.getRname()))"),
            @Mapping(target = "RMobile", expression = "java(com.jiuji.oa.wuliu.utils.WuliuUtil.convertNullValue(vo.getRmobile()))"),
            @Mapping(target = "RAddress", expression = "java(com.jiuji.oa.wuliu.utils.WuliuUtil.convertNullValue(vo.getRaddress()))"),
            @Mapping(target = "RCityId", expression = "java(com.jiuji.oa.wuliu.utils.WuliuUtil.convertNullValue(vo.getRcityId()))"),
            @Mapping(source = "shoujianren", target = "shouJianRen"),
    })
    WuLiuEntity toWuLiuEntity(WuLiuUpdateReqV2 vo);

    /**
     * 转 WuLiuEntity
     *
     * @param vo WuLiuUpdateReqVO
     * @return WuLiuEntity
     * @date 2021-09-29
     * <AUTHOR> [<EMAIL>]
     */
    @Mappings({
            @Mapping(source = "wuliuId", target = "id"),
            @Mapping(source = "com", target = "com"),
            @Mapping(source = "nu", target = "nu"),
    })
    WuLiuEntity toWuLiuEntity(WuLiuUpdateReqV3 vo);

    /**
     * 转 WuliuAddressInfoRes
     */
    WuliuAddressInfoRes toWuliuAddressInfo(ShouHouAddInfoDTO dto);

    /**
     * 转 WuliuAddressInfoRes
     */
    WuliuAddressInfoRes toWuliuAddressInfo(YuYueAddInfoDTO vo);

    /**
     * to ExpressOrderAddBO
     */
    @Mappings({
            @Mapping(source = "orderGroup.receiver.address", target = "receiverDetailAddress"),
            @Mapping(source = "orderGroup.receiver.name", target = "receiverName"),
            @Mapping(source = "orderGroup.receiver.mobile", target = "receiverPhone"),
            @Mapping(source = "orderGroup.sender.address", target = "senderDetailAddress"),
            @Mapping(source = "orderGroup.sender.name", target = "senderName"),
            @Mapping(source = "orderGroup.sender.mobile", target = "senderPhone"),
            @Mapping(defaultValue = "0", target = "tenantScale"),
            @Mapping(source = "logisticsBase.receiveShopId", target = "receiveShopId"),
            @Mapping(source = "logisticsBase.receiveShopName", target = "receiveShopName"),
            @Mapping(source = "logisticsBase.sendShopId", target = "sendShopId"),
            @Mapping(source = "logisticsBase.sendShopName", target = "sendShopName"),
            @Mapping(source = "orderGroup.receiver.prov", target = "receiverProvinceName"),
            @Mapping(source = "orderGroup.receiver.city", target = "receiverCityName"),
            @Mapping(source = "orderGroup.receiver.county", target = "receiverCountyName"),
            @Mapping(source = "orderGroup.sender.prov", target = "senderProvinceName"),
            @Mapping(source = "orderGroup.sender.city", target = "senderCityName"),
            @Mapping(source = "orderGroup.sender.county", target = "senderCountyName"),
    })
    ExpressOrderAddBO toExpressOrderAdd(OrderGroupDTO orderGroup, LogisticsBase logisticsBase);
    /**
     * to ExpressOrderAddBO
     */
    @Mappings({
            @Mapping(source = "orderInfo.receiver.address", target = "receiverDetailAddress"),
            @Mapping(source = "orderInfo.receiver.name", target = "receiverName"),
            @Mapping(source = "orderInfo.receiver.mobile", target = "receiverPhone"),
            @Mapping(source = "orderInfo.sender.address", target = "senderDetailAddress"),
            @Mapping(source = "orderInfo.sender.name", target = "senderName"),
            @Mapping(source = "orderInfo.sender.mobile", target = "senderPhone"),
            @Mapping(defaultValue = "0", target = "tenantScale"),
            @Mapping(source = "logisticsBase.receiveShopId", target = "receiveShopId"),
            @Mapping(source = "logisticsBase.receiveShopName", target = "receiveShopName"),
            @Mapping(source = "logisticsBase.sendShopId", target = "sendShopId"),
            @Mapping(source = "logisticsBase.sendShopName", target = "sendShopName"),
            @Mapping(source = "orderInfo.receiver.prov", target = "receiverProvinceName"),
            @Mapping(source = "orderInfo.receiver.city", target = "receiverCityName"),
            @Mapping(source = "orderInfo.receiver.county", target = "receiverCountyName"),
            @Mapping(source = "orderInfo.sender.prov", target = "senderProvinceName"),
            @Mapping(source = "orderInfo.sender.city", target = "senderCityName"),
            @Mapping(source = "orderInfo.sender.county", target = "senderCountyName"),
    })
    ExpressOrderAddBO toExpressOrderAdd(OrderInfoDTO orderInfo, LogisticsBase logisticsBase);

    /**
     * 转 toWuliuExpressSignedBO
     */
    @Mappings({
            @Mapping(source = "id", target = "wuliuId"),
            @Mapping(source = "stats", target = "stats"),
            @Mapping(source = "nu", target = "nu"),
            @Mapping(source = "com", target = "com"),
            @Mapping(source = "RAreaId", target = "rareaId"),
            @Mapping(source = "RAddress", target = "raddress"),
    })
    WuliuExpressSignedBO toWuliuExpressSigned(WuLiuEntity vo);

    /**
     * 转 toGetWuliuInfoRes
     */
    @Mappings({
            @Mapping(source = "id", target = "id"),
            @Mapping(source = "SName", target = "fromName"),
            @Mapping(source = "RName", target = "toName"),
            @Mapping(source = "DTime", target = "createTime"),
    })
    GetWuliuInfoRes toGetWuliuInfoRes(WuLiuEntity vo);
}
