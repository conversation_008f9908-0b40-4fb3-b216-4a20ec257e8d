package com.jiuji.oa.wuliu.constant;

/**
 * <AUTHOR> liu ming
 * @date 2021-09-27 下午 7:48
 */

public final class WuLiuTypeConstant {

    public static final int INNER = 1;
    public static final int ORDER = 4;
    public static final int AFTER_SERVICE = 5;
    public static final int ORDER_EXPRESS = 6;
    public static final int VISIT = 7;
    public static final int OTHERS = 8;
    public static final int FOURTEEN_DAY = 9;
    public static final int ACCESSORY = 10;
    public static final int SECOND_HAND = 11;
    public static final int INVOICE = 12;
    public static final int RENT = 13;

    private WuLiuTypeConstant() {
        throw new IllegalStateException("WuLiuTypeConstant class");
    }
}
