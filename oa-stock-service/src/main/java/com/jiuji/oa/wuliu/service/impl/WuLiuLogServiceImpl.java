/*
 *     Copyright © 2006 - 2020 九机网 All Rights Reserved
 *
 */
package com.jiuji.oa.wuliu.service.impl;

import cn.hutool.core.lang.Assert;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.logapi.common.UrlUtils;
import com.jiuji.oa.stock.logistics.order.vo.req.SaveWuLiuLogReq;
import com.jiuji.oa.wuliu.entity.WuLiuLogEntity;
import com.jiuji.oa.wuliu.enums.WuliuStatistcsCategrayEnum;
import com.jiuji.oa.wuliu.mapper.WuLiuLogMapper;
import com.jiuji.oa.wuliu.mapstruct.WuLiuLogStruct;
import com.jiuji.oa.wuliu.service.IWuLiuLogService;
import com.jiuji.oa.wuliu.vo.res.WuLiuLogResVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 物流单日志ServiceImpl
 *
 * <AUTHOR> liu ming
 * @date 2021-05-24 18:40:13
 */
@Service
@DS("oanewWrite")
public class WuLiuLogServiceImpl extends ServiceImpl<WuLiuLogMapper, WuLiuLogEntity> implements IWuLiuLogService {

    private static final String INUSER = "系统";
    private static final String GET_METHOD = "getMsg";
    private static final String SET_METHOD = "setMsg";
    private static final Integer MAX_MSG_LENGTH = 650;

    @Resource
    private WuLiuLogStruct wuLiuLogStruct;

    @Override
    @DS("oanewWrite")
    public void saveWuliuLog(SaveWuLiuLogReq saveWuLiuLogReq) {
        WuLiuLogEntity wuliuLogs = new WuLiuLogEntity()
                .setWuliuid(saveWuLiuLogReq.getWuliuid())
                .setInuser(INUSER)
                .setMsg(saveWuLiuLogReq.getMsg())
                .setDtime(LocalDateTime.now());
        this.save(wuliuLogs);
    }

    @DS("oanewWrite")
    @Override
    public boolean addOne(Integer wuLiuId, String inUser, String msg) {
        return save(new WuLiuLogEntity()
                .setWuliuid(wuLiuId)
                .setInuser(inUser == null ? INUSER : inUser)
                .setMsg(msg)
                .setDtime(LocalDateTime.now()));
    }


    @Override
    @DS("oanewWrite")
    public WuLiuLogEntity writeLogs(SaveWuLiuLogReq saveWuLiuLogReq) {
        String msg = saveWuLiuLogReq.getMsg().replace("'", "");
        Assert.isTrue(MAX_MSG_LENGTH >= msg.length(),String.format( "日志长度超过最大长度 %d !", MAX_MSG_LENGTH));
        WuLiuLogEntity wuLiuLogEntity = new WuLiuLogEntity()
                .setWuliuid(saveWuLiuLogReq.getWuliuid())
                .setInuser(saveWuLiuLogReq.getInuser())
                .setMsg(msg)
                .setStatistcsCategray(WuliuStatistcsCategrayEnum.UNKNOWN.getCode())
                .setDtime(LocalDateTime.now());
        this.save(wuLiuLogEntity);
        return wuLiuLogEntity;
    }

    @Override
    public List<WuLiuLogResVO> getWuLiuLog(String wuliuid) {
        List<WuLiuLogResVO> wuLiuLogResVOList = new ArrayList<>();
        List<WuLiuLogEntity> wuLiuLogEntityList = this.lambdaQuery().in(WuLiuLogEntity::getWuliuid, wuliuid)
                .orderByAsc(WuLiuLogEntity::getId)
                .orderByAsc(WuLiuLogEntity::getDtime)
                .list();
        if (CollectionUtils.isEmpty(wuLiuLogEntityList)) {
            return wuLiuLogResVOList;
        }
        wuLiuLogResVOList = wuLiuLogStruct.toWuLiuLogResVO(wuLiuLogEntityList);
        UrlUtils.encodeComment(wuLiuLogResVOList, GET_METHOD, SET_METHOD);
        return wuLiuLogResVOList;
    }

    @Override
    public List<WuLiuLogEntity> getWuLiuLog(Integer wuliuid) {
        return Optional.ofNullable(lambdaQuery()
                .eq(WuLiuLogEntity::getWuliuid, wuliuid)
                .orderByAsc(WuLiuLogEntity::getId).list())
                .orElseGet(ArrayList::new);
    }

    @DS("oanewWrite")
    @Override
    public boolean saveLog(Integer wuLiuId, String inUser, String msg, Integer kind) {
        return save(new WuLiuLogEntity()
                .setWuliuid(wuLiuId)
                .setInuser(inUser == null ? INUSER : inUser)
                .setKind(kind)
                .setMsg(msg)
                .setDtime(LocalDateTime.now()));
    }

    /**
     * 批量保存日志
     *
     * @param logs
     */
    @Override
    @DS("oanewWrite")
    @DSTransactional()
    public Integer saveLogBatch(List<WuLiuLogEntity> logs) {
        return this.baseMapper.insertLogBatch(logs);
    }

}
