package com.jiuji.oa.wuliu.utils;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.jiuji.common.util.CommonUtil;
import com.jiuji.oa.nc.common.config.rabbitmq.RabbitMqConfig;
import com.jiuji.oa.nc.common.enums.XtenantEnum;
import com.jiuji.oa.nc.dict.enums.ConfigEnum;
import com.jiuji.oa.nc.dict.service.ISysConfigService;
import com.jiuji.oa.nc.user.service.IAreaInfoService;
import com.jiuji.oa.stock.common.util.JacksonJsonUtils;
import com.jiuji.oa.stock.common.util.SysUtils;
import com.jiuji.oa.stock.logisticscenter.enums.LogisticsExpressTypeEnum;
import com.jiuji.oa.wuliu.bo.ExpressOrderAddBO;
import com.jiuji.oa.wuliu.bo.WuliuExpressMqBO;
import com.jiuji.oa.wuliu.bo.WuliuProcessBO;
import com.jiuji.oa.wuliu.bo.WuliuRelatedRecordBO;
import com.jiuji.oa.wuliu.component.WuliuMqProducer;
import com.jiuji.oa.wuliu.constant.WuLiuConstant;
import com.jiuji.oa.wuliu.constant.WuliuExpressConstant;
import com.jiuji.oa.wuliu.enums.WuLiuTypeEnum;
import com.jiuji.oa.wuliu.mapstruct.WuLiuMapStruct;
import com.jiuji.oa.wuliu.vo.OrderGroupDTO;
import com.jiuji.oa.wuliu.vo.OrderInfoDTO;
import com.jiuji.oa.wuliu.vo.req.WuLiuAddOrUpdateReqVO;
import com.jiuji.tc.utils.common.build.LambdaBuild;
import com.jiuji.tc.utils.enums.order.ProcessBusinessTypeEnum;
import com.jiuji.tc.utils.enums.order.SubDynamicsBusinessNodeEnum;
import com.rabbitmq.client.AMQP;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/6/20 18:55
 */
@Slf4j
public class WuliuUtil {
    private static final String ZTO_ACCOUNT_ID = "**********";
    private static final String GZ_ZTO_ACCOUNT_ID = "ZTO521540460538986";

    private WuliuUtil() {
    }

    /**
     * 中通电子面单账号
     * @param provinceName
     * @return
     */
    public static String getZhongTongAccount(String provinceName) {
        if (SysUtils.isDev() || SysUtils.isSaasTest()) {
            return "test";
        }
        String accountId = ZTO_ACCOUNT_ID;
        if (StringUtils.isNotEmpty(provinceName) && provinceName.contains(WuLiuConstant.GUIZHOU)) {
            accountId = GZ_ZTO_ACCOUNT_ID;
        }
        return accountId;
    }

    /**
     * 空字符串处理成null
     * @return
     */
    public static String convertNullValue(String s) {
        return StringUtils.isBlank(s) ? null : s;
    }

    /**
     * 空字符串处理成null
     * @return
     */
    public static Integer convertNullValue(Integer areaId) {
        return Objects.equals(0, areaId) ? null : areaId;
    }

    /**
     * 发送消息
     * @param messageObj
     */
    public static <T> void sendWuliuExpressMessage(WuliuExpressMqBO<T> messageObj) {
        String msg = "";
        try {
            msg = JacksonJsonUtils.toJson(messageObj);
            RabbitTemplate rabbitTemplate = SpringUtil.getBean(RabbitTemplate.class);
            rabbitTemplate.convertAndSend(RabbitMqConfig.QUEUE_WULIU_EXPRESS_SYNC, msg);
            log.info("发送物流队列消息msg={}", msg);
        } catch (Exception e) {
            log.error("发送物流队列消息消息异常msg={}", msg, e);
        }
    }

    /**
     * 中通快递单号同步中台
     * @param model
     * @param conmodel
     */
    public static void saveExpressOrder(WuLiuAddOrUpdateReqVO model, OrderGroupDTO conmodel) {
        ExpressOrderAddBO expressOrderAdd = SpringUtil.getBean(WuLiuMapStruct.class).toExpressOrderAdd(conmodel, SpringUtil.getBean(IAreaInfoService.class).getAreaName(model.getSareaid(), model.getRareaid()));
        expressOrderAdd.setXtenant(Convert.toLong(XtenantEnum.getXtenant()));
        expressOrderAdd.setDeliveryId(Convert.toStr(model.getWuliuid()));
        expressOrderAdd.setWaybillNo(model.getNu());
        expressOrderAdd.setExpressType(LogisticsExpressTypeEnum.getExpressType(model.getCom()));
        saveExpressOrder(expressOrderAdd);
    }

    /**
     * 快递单号同步中台
     * @param model
     * @param orderInfo
     */
    public static void saveExpressOrder(WuLiuAddOrUpdateReqVO model, OrderInfoDTO orderInfo) {
        ExpressOrderAddBO expressOrderAdd = SpringUtil.getBean(WuLiuMapStruct.class).toExpressOrderAdd(orderInfo, SpringUtil.getBean(IAreaInfoService.class).getAreaName(model.getSareaid(), model.getRareaid()));
        expressOrderAdd.setXtenant(Convert.toLong(XtenantEnum.getXtenant()));
        expressOrderAdd.setDeliveryId(Convert.toStr(model.getWuliuid()));
        expressOrderAdd.setWaybillNo(model.getNu());
        expressOrderAdd.setExpressType(LogisticsExpressTypeEnum.getExpressType(model.getCom()));
        saveExpressOrder(expressOrderAdd);
    }

    /**
     * 快递单号同步中台
     * @param req
     */
    public static void saveExpressOrder(ExpressOrderAddBO req) {
        WuliuUtil.sendWuliuExpressMessage(new WuliuExpressMqBO<ExpressOrderAddBO>().setAct(WuliuExpressConstant.ACT_SAVE_LOGISTICS_EXPRESS_ORDER)
                .setData(req));
    }

    /**
     * 物流单时效计算消息
     * @param req
     */
    public static void sendWuliuRelatedRecordMessage(WuliuRelatedRecordBO req) {
        String msg = "";
        try {
            WuliuExpressMqBO<WuliuRelatedRecordBO> mq = new WuliuExpressMqBO<WuliuRelatedRecordBO>().setAct(WuliuExpressConstant.ACT_WULIU_RELATED_RECORD)
                    .setData(req);
            msg = JacksonJsonUtils.toJson(mq);
            WuliuMqProducer wuliuMqProducer = SpringUtil.getBean(WuliuMqProducer.class);
            wuliuMqProducer.oaAsyncSendMsg(msg);
            log.info("发送物流单时效计算消息msg={}", msg);
        } catch (Exception e) {
            log.error("发送物流单时效计算消息异常msg={}", msg, e);
        }
    }

    /**
     * 物流业务节点信息
     * @param req
     */
    public static void sendWuliuProcessMessage(WuliuProcessBO req) {
        String msg = "";
        try {
            if (XtenantEnum.isSaasXtenant()) {
                return;
            }
            WuliuExpressMqBO<WuliuProcessBO> mq = new WuliuExpressMqBO<WuliuProcessBO>().setAct(WuliuExpressConstant.ACT_WULIU_PROCESS_EVENT)
                    .setData(req);
            msg = JacksonJsonUtils.toJson(mq);
            RabbitTemplate oaAsyncRabbitTemplate = SpringUtil.getBean("oaAsyncRabbitTemplate",RabbitTemplate.class);
            oaAsyncRabbitTemplate.convertAndSend(RabbitMqConfig.WULIU_PROCESS, msg);
            log.info("物流业务节点信息={}", msg);
        } catch (Exception e) {
            log.error("物流业务节点信息异常msg={}", msg, e);
        }
    }

    /**
     * 物流业务节点信息
     */
    public static void sendWuliuProcessMessage(Integer wuliuId) {
        String msg = "";
        try {
            if (!XtenantEnum.isJiujiXtenant()) {
                return;
            }
            WuliuProcessBO wuliuProcess = LambdaBuild.create(WuliuProcessBO.class)
                    .set(WuliuProcessBO::setBusinessId, Convert.toStr(wuliuId))
                    .set(WuliuProcessBO::setBusinessNode, SubDynamicsBusinessNodeEnum.EXPRESS_CRATE.getCode())
                    .set(WuliuProcessBO::setBusinessType, ProcessBusinessTypeEnum.EXPRESS_DELIVERY_PROCESS.getCode())
                    .set(WuliuProcessBO::setOperationTime, LocalDateTime.now())
                    .set(WuliuProcessBO::setXtentant, XtenantEnum.getXtenant().longValue())
                    .set(WuliuProcessBO::setEmployeeName, "系统")
                    .set(WuliuProcessBO::setSource, "sendWuliuProcess")
                    .set(WuliuProcessBO::setStatus, 0)
                    .set(WuliuProcessBO::setDelayMillisecond, 0)
                    .set(WuliuProcessBO::setAreaId, 0)
                    .set(WuliuProcessBO::setNextAreaId, 0)
                    .set(WuliuProcessBO::setRelatedId, 0).build();
            WuliuExpressMqBO<WuliuProcessBO> mq = new WuliuExpressMqBO<WuliuProcessBO>().setAct(WuliuExpressConstant.ACT_WULIU_PROCESS_EVENT)
                    .setData(wuliuProcess);
            msg = JacksonJsonUtils.toJson(mq);
            RabbitTemplate oaAsyncRabbitTemplate = SpringUtil.getBean("oaAsyncRabbitTemplate",RabbitTemplate.class);
            oaAsyncRabbitTemplate.convertAndSend(RabbitMqConfig.WULIU_PROCESS, msg);
            log.info("物流业务节点信息={}", msg);
        } catch (Exception e) {
            log.error("物流业务节点信息异常msg={}", msg, e);
        }
    }

    /**
     * 查询是否开通美团
     * @param areaId
     * @return
     */
    public static boolean openMeituanFlag(Integer areaId) {
        if (CommonUtil.isNullOrZero(areaId)) {
            return false;
        }
        String configStr = SpringUtil.getBean(ISysConfigService.class).getValueByCode2(ConfigEnum.ENTERPRISE_MEITUAN.getCode());
        if (StringUtils.isNotBlank(configStr)) {
            return Arrays.stream(StrUtil.split(configStr, StrPool.COMMA)).map(v -> Convert.toInt(v, 0)).anyMatch(v -> Objects.equals(v, areaId));
        }
        return false;
    }

    /**
     * 物流单类型是订单或良品
     */
    public static boolean isOrderWutype(Integer wutype) {
        return Arrays.asList(WuLiuTypeEnum.ORDER.getCode(),WuLiuTypeEnum.ORDER_EXPRESS.getCode(),WuLiuTypeEnum.FOURTEEN_DAY.getCode()).contains(Optional.ofNullable(wutype).orElse(0));
    }

    /**
     * mq消息手动重发
     * @param message
     * @param channel
     * @param retryNum
     */
    public static void manulRetry(Message message, Channel channel, int retryNum) {
        try {
            if (message == null || channel == null) {
                return;
            }
            //先Ack确认当前消息
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);

            String exchange = message.getMessageProperties().getReceivedExchange();
            String receivedRoutingKey = message.getMessageProperties().getReceivedRoutingKey();
            //重试失败,手动应答之后，并放到消息队列最后面,先处理别的消息
            Map<String, Object> headers = message.getMessageProperties().getHeaders();
            Integer retryCount = (Integer) headers.get("retryCount");
            if (retryCount == null) {
                retryCount = 1;
            } else {
                if (retryCount >= retryNum) {
                    //达到重试次数，确认
                    channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                    return;
                }
                retryCount = retryCount + 1;
            }
            headers.put("retryCount", retryCount);
            AMQP.BasicProperties basicProperties = new AMQP.BasicProperties().builder()
                    .contentType("text/plain")
                    .headers(headers).build();
            //重新发送消息到队尾
            channel.basicPublish(exchange,
                    receivedRoutingKey, basicProperties,
                    message.getBody());

        } catch (IOException e) {
            log.info("手动重试MQ异常", e);
        }
    }
}
