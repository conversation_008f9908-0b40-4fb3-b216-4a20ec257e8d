package com.jiuji.oa.wuliu.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: gengjiaping
 * @date: 2020/3/27
 */
@Getter
@AllArgsConstructor
public enum WxStatusEnum implements CodeMessageEnumInterface {
    CLZ(0,"处理中"),
    YXH(1,"已修好"),
    XBH(3,"修不好");
//    HSZ(2,"回收站");
    /**
     * 状态
     */
    private Integer code;
    /**
     * 名称
     */
    private String message;
}
