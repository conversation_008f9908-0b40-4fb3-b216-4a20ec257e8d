package com.jiuji.oa.wuliu.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 物流单快递类型
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_wuliu_express_type")
public class ExpressTypeEntity extends Model<ExpressTypeEntity> {

    private static final long serialVersionUID = 3044792091547373945L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 快递名称
     */
    private String expressName;

    /**
     * 快递编码(兼容C#)
     */
    private String expressCode;

    /**
     * 快递类型
     */
    private Integer expressType;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 是否删除
     */
    @TableLogic
    @TableField("is_delete")
    private Boolean delFlag;

    /**
     * 是否套餐
     */
    @TableField("is_combo")
    private Boolean combo;

    /**
     * 顺序
     */
    private Integer rankOrder;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
