package com.jiuji.oa.wuliu.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;


/**
 * 快递单取消模型
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-12-03
 */
@Data
public class CancelItemVO extends QueryItemVO {

    private static final long serialVersionUID = 1L;

    /**
     * 客户取消订单原因
     */
    @JsonProperty("cancel_reason_id")
    private Integer cancelReasonId;

    /**
     * 详细取消原因，最长不超过256个字符
     */
    @JsonProperty("cancel_reason")
    private String cancelReason;

}
