package com.jiuji.oa.wuliu.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.stock.common.util.SysUtils;
import com.jiuji.oa.wuliu.entity.WuliuExpressTime;
import com.jiuji.oa.wuliu.mapper.WuliuExpressTimeMapper;
import com.jiuji.oa.wuliu.service.IWuliuExpressTimeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 *
 */
@Service
@Slf4j
@DS("oanewWrite")
public class WuliuExpressTimeServiceImpl extends ServiceImpl<WuliuExpressTimeMapper, WuliuExpressTime>
implements IWuliuExpressTimeService {

    /**
     * 快递创建时间
     *
     * @param wuliuExpressTime
     */
    @Override
    public void saveOrUpdateExpressTime(WuliuExpressTime wuliuExpressTime) {
        if (SysUtils.isJiuJiProd() || SysUtils.isDev()) {
            this.baseMapper.saveOrUpdateExpressCreateTime(wuliuExpressTime);
        }
    }
}




