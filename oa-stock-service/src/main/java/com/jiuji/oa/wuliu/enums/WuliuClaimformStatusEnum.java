package com.jiuji.oa.wuliu.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 销售单状态枚举
 *
 * <AUTHOR>
 * @date 2021/10/09
 */
@Getter
@AllArgsConstructor
public enum WuliuClaimformStatusEnum implements CodeMessageEnumInterface {
    /**
     * 登记时间
     */
    WAITCHECK(1,"待审核"),
    APPROVE(2,"已同意"),
    REFUSE(3,"已驳回"),
    ACCOUNTING(4,"结算中"),
    ACCOUNTED(5,"已结算");

    /**
     * 代码
     */
    private Integer code;
    /**
     * 消息
     */
    private String message;


    /**
     * 根据 key 获取 value
     *
     * @param key key
     * @return value
     */
    public static String getValue(Integer key) {
        for (WuliuClaimformStatusEnum item : values()) {
            if (item.getCode().equals(key)) {
                return item.getMessage();
            }
        }
        return null;
    }


    /**
     * 根据 value 获取 key
     * @param value
     * @return
     */
    public static Integer getKey(String value) {
        for (WuliuClaimformStatusEnum item : values()) {
            if (item.getMessage().equals(value)) {
                return item.getCode();
            }
        }
        return null;
    }
}
