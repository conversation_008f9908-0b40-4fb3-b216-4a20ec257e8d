package com.jiuji.oa.wuliu.dto.res;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * WuLiuNoteRes
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-12-05
 */
@Data
public class WuLiuNoteRes {
    private Long id;
    private String wuLiuType;
    private String trackNum;
    private String receiveName;
    private String receiveAreaId;
    private String receiveArea;
    private String sendAreaId;
    private String sendArea;
    private String sendName;
    private LocalDateTime registerTime;
    private String comment;
    private String trackNumBind;
    private String overtime;

    /**
     * fee 是 String 给前端用，fee1 是后端从数据库取
     */
    @JsonIgnore
    private BigDecimal fee1;
    private String fee;
    private String operator;
    private String linkType;
    private String status;
    @JsonIgnore
    private Integer wCateId;
    private String wuLiuCate;
    private String sendSmallArea;
    private String sendBigArea;
    private String sendAreaName;
    private String receiveAreaName;
    private String receiveSmallArea;
    private String receiveBigArea;
    private String sendCityName;
    private String receiveCityName;
    private String claimFormAmount;
    private String receiveAddress;
    private String sendAddress;
    /**
     * 物流状态描述
     */
    private String statusName;
    /**
     * 骑行距离
     */
    private Long distance;
    /**
     * 骑行配送成本
     */
    private BigDecimal distributionCost;
}
