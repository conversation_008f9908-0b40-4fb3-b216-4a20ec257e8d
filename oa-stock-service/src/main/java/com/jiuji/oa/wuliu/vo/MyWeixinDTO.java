package com.jiuji.oa.wuliu.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * MyWeixin
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-16
 */
@Data
@Accessors(chain = true)
public class MyWeixinDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("userid")
    @JSONField(name = "userid")
    private String userid;

    @JsonProperty("openid")
    @JSONField(name = "openid")
    private String openid;

    @JsonProperty("wxid")
    @JSONField(name = "wxid")
    private Integer wxid;

    @JsonProperty("kinds")
    @JSONField(name = "kinds")
    private Integer kinds;

    @JsonProperty("unionid")
    @JSONField(name = "unionid")
    private String unionid;

}
