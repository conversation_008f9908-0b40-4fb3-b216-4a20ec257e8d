package com.jiuji.oa.wuliu.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2021/11/5 14:33
 */
@Data
public class DadaProductModelVO {
    /**
     * 商品名称，限制长度128
     */
    @JsonProperty("sku_name")
    @JSONField(name = "sku_name")
    @NotNull
    private String skuName;
    /**
     * 商品名称，限制长度128
     */
    @JsonProperty("src_product_no")
    @JSONField(name = "src_product_no")
    @NotNull
    private String srcProductNo;
    /**
     * 商品数量，精确到小数点后两位
     */
    @NotNull
    private BigDecimal count;
    /**
     * 商品单位，默认：件
     */
    private String unit;

}
