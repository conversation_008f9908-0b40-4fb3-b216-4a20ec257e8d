package com.jiuji.oa.wuliu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 
 * @TableName wuliunoex
 */
@TableName(value ="wuliunoex")
@Accessors(chain = true)
@Data
public class Wuliunoex implements Serializable {
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 子运单
     */
    private String nu;

    /**
     * 物流Id
     */
    private Integer wuliuid;

    /**
     * 快递公司
     */
    private String com;

    /**
     * 数量
     */
    private Integer packagecount;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}