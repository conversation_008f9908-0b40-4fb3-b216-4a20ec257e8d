/*
 *     Copyright © 2006 - 2020 九机网 All Rights Reserved
 *
 */

package com.jiuji.oa.wuliu.service;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.orderdynamics.dto.DiaoboWuliuSubDTO;
import com.jiuji.oa.orderdynamics.dto.DiySubDTO;
import com.jiuji.oa.orderdynamics.dto.WuliuSubDTO;
import com.jiuji.oa.orderdynamics.vo.response.QueryWuliuBySubResVO;
import com.jiuji.oa.wuliu.dto.SubExpectTimeDTO;
import com.jiuji.oa.wuliu.dto.SubPositionDTO;
import com.jiuji.oa.wuliu.dto.req.SubPositionReq;
import com.jiuji.oa.wuliu.entity.WuLiuEntity;
import com.jiuji.oa.wuliu.entity.WuLiuSubEntity;
import com.jiuji.oa.wuliu.vo.res.WuliuOrderInfoRes;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 订单,责任小组：销售 Service
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-09
 */
public interface IWuLiuSubService extends IService<WuLiuSubEntity> {

    /**
     * getSub
     *
     * @param subId
     * @return WuLiuSubEntity
     * @date 2021-10-11
     * <AUTHOR> [<EMAIL>]
     */
    WuLiuSubEntity getSub(Integer subId);

    /**
     * 物流单查询订单
     *
     * @param wuliuId
     * @return
     */
    @Cached(name = "stock:IWuLiuSubService:getWuliuSubByWuliuId", key="#wuliuId", expire = 5, timeUnit = TimeUnit.MINUTES, cacheType = CacheType.REMOTE)
    List<WuliuSubDTO> getWuliuSubByWuliuId(Integer wuliuId);

    /**
     * 快递单查询订单（不包含调拨）
     * @param nu
     * @return
     */
    @Cached(name = "stock:IWuLiuSubService:getWuliuSubByNu", key="#nu", expire = 5, timeUnit = TimeUnit.MINUTES, cacheType = CacheType.REMOTE)
    List<WuliuSubDTO> getWuliuSubByNu(String nu);

    /**
     * 快递单查询订单
     *
     * @param nu
     * @return
     */
    @Cached(name = "stock:IWuLiuSubService:getDiySubByNu", key="#nu", expire = 5, timeUnit = TimeUnit.MINUTES, cacheType = CacheType.REMOTE)
    List<DiySubDTO> getDiySubByNu(String nu);

    /**
     * 快递单号调拨单订单
     * @param nu
     * @return
     */
    List<DiaoboWuliuSubDTO> getTransferWuliuSubByNu(String nu);

    /**
     * 物流单查询调拨单订单
     * @param wuliuId
     * @return
     */
    List<DiaoboWuliuSubDTO> getTransferWuliuSubByWuliuId(Integer wuliuId);

    /**
     * 订单查询物流单信息
     * @param subId
     * @return
     */
    QueryWuliuBySubResVO queryWuliuBySubId(Integer subId);

    /**
     * 包含调拨单
     * 物流单查询订单
     *
     * @param subId
     * @return
     */
    List<WuLiuEntity> getWuliuEntityBySubId(Integer subId);

    /**
     * 查询订单预计送达时间
     * @param subId
     * @return
     */
    SubExpectTimeDTO getSubExpectTimeBySubId(Integer subId);

    boolean updateSubExpectTime(Integer subId, LocalDateTime expectTime);

    /**
     * 查询订单收货位置信息
     * @param sub
     * @return
     */
    SubPositionDTO getSubPositionBySub(SubPositionReq sub);

    /**
     * 支付商户订单号查询订单物流信息
     * @param payId
     * @return
     */
    WuliuOrderInfoRes getOrderByPayId(Integer payId);
    WuliuOrderInfoRes getOrderByPayDes(String payDes);

    @Cached(name = "stock:IWuLiuSubService:getBySubId", key="#subId", expire = 1, timeUnit = TimeUnit.MINUTES, cacheType = CacheType.LOCAL)
    WuLiuSubEntity getBySubId(Long subId);

    /**
     * 查询订单信息
     * @param subId
     * @return
     */
    WuLiuSubEntity getSubBySubId(Integer subId);

    /**
     * 快递单查询订单门店电话号码
     * @param nu
     * @return
     */
    @Cached(name = "stock:IWuLiuSubService:getSubTelByNu", key="#nu", expire = 2, timeUnit = TimeUnit.MINUTES, cacheType = CacheType.REMOTE)
    String getSubTelByNu(String nu);

    /**
     * 查询订单地址经纬度
     * @param req
     * @return
     */
    SubPositionDTO getSubAddressPositionBySubId(SubPositionReq req);
}
