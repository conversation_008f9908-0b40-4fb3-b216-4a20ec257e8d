package com.jiuji.oa.wuliu.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.wuliu.entity.PrintCountEntity;
import com.jiuji.oa.wuliu.vo.ShelveDTO;
import java.math.BigDecimal;

/**
 * <p>
 * 打印次数,责任小组：运营 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-11
 */
public interface IPrintCountService extends IService<PrintCountEntity> {

    /**
     * 用订单号查询打印次数
     *
     * @param subId
     * @return
     */
    Integer getPrintCountBySubId(Integer subId);


    /**
     * 订单应付金额
     *
     * @param subId
     * @return
     */
    BigDecimal getOrderPayAble(Integer subId);


    /**
     * 调拨单应付金额
     *
     * @param subId
     * @return
     */
    BigDecimal getTransferPayAble(Integer subId);


    /**
     * 回收订单应付金额
     *
     * @param subId
     * @return
     */
    BigDecimal getReturnPayAble(Integer subId);


    /**
     * 子订单的数量
     *
     * @param subId
     * @return
     */
    Integer countBasket(Integer subId);


    /**
     * 根据转售订单号 获取货位号及数量
     *
     * @param subId
     * @return
     */
    ShelveDTO getShelvesNumBySubId(Integer subId);
}
