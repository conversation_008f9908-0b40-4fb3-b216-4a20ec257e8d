package com.jiuji.oa.wuliu.vo;


import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * shunfengApiServices.SfCargo
 * 运单货品信息实体
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-11
 */
@ApiModel(description = "运单货品信息实体")
@Data
@Accessors(chain = true)
public class SfCargoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商品名称
     */
    @JsonProperty("CargoName")
    @JSONField(name = "CargoName")
    private String cargoName;

    /**
     * 数量
     */
    @JsonProperty("count")
    @JSONField(name = "count")
    private String count;

    /**
     * 单位
     */
    @JsonProperty("unit")
    @JSONField(name = "unit")
    private String unit;

    /**
     * 单重
     */
    @JsonProperty("weight")
    @JSONField(name = "weight")
    private String weight;

    /**
     * 单价
     */
    @JsonProperty("amount")
    @JSONField(name = "amount")
    private String amount;

    /**
     * 结算货币（国际件用）
     */
    @JsonProperty("currency")
    @JSONField(name = "currency")
    private String currency;

    /**
     * 原产地(国际件用)
     */
    @JsonProperty("source_area")
    @JSONField(name = "source_area")
    private String sourceArea;

}
