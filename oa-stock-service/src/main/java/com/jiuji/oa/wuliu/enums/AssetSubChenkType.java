package com.jiuji.oa.wuliu.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 待审核 = 0,
 * 已通过 = 1,
 * 未通过 = 2,
 * 已发货 = 3,
 * 已收货 = 4,
 * 待发货 = 5
 *
 * C# AssetSubCheckType
 * <AUTHOR>
 * @date 2021/12/9 11:34
 */
@Getter
@AllArgsConstructor
public enum AssetSubChenkType {
    /**
     * 待审核
     */
    PENDING_REVIEW(0,"待审核"),
    /**
     * 已通过
     */
    PASSED(1,"已通过"),

    /**
     * 未通过
     */
    NOT_PASS(2,"未通过"),

    /**
     * 已发货
     */
    SHIPPED(3,"已发货"),

    /**
     * 已收货
     */
    RECEIVED(4,"已收货"),

    /**
     * 待发货
     */
    TO_BE_SHIPPED(5,"待发货"),

    ;

    private Integer code;

    private String message;
}
