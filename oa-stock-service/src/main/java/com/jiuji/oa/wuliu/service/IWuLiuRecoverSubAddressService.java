package com.jiuji.oa.wuliu.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.wuliu.entity.WuLiuRecoverSubAddressEntity;

/**
 * 良品订单地址[责任小组:回收] 服务类
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-27
 */
public interface IWuLiuRecoverSubAddressService extends IService<WuLiuRecoverSubAddressEntity> {
    /**
     * 清除良品订单中的快递公司和快递单号
     * @param subId
     * @param nu
     * @return
     */
    boolean clearWuliuNo(Integer subId, String nu);
}
