package com.jiuji.oa.wuliu.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * authModel
 * 特许授权管理模型
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-16
 */
@Data
@Accessors(chain = true)
public class AuthModelDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @JsonProperty("id")
    @JSONField(name = "id")
    private Integer id;

    /**
     * 特许授权管理名称
     */
    @JsonProperty("name")
    @JSONField(name = "name")
    private String name;

    /**
     * 账套编号
     */
    @JsonProperty("ztid")
    @JSONField(name = "ztid")
    private String ztid;

    /**
     * 短信验证码通道
     */
    @JsonProperty("vCodeChannel")
    @JSONField(name = "vCodeChannel")
    private String vCodeChannel;

    /**
     * 短信营销通道
     */
    @JsonProperty("marketChannel")
    @JSONField(name = "marketChannel")
    private String marketChannel;

    /**
     * 排序
     */
    @JsonProperty("rank")
    @JSONField(name = "rank")
    private Integer rank;

    /**
     *
     */
    @JsonProperty("dcAreaId")
    @JSONField(name = "dcAreaId")
    private Integer dcAreaId;

    /**
     *
     */
    @JsonProperty("HQAreaId")
    @JSONField(name = "HQAreaId")
    private Integer hqAreaId;

    /**
     *
     */
    @JsonProperty("H1AreaId")
    @JSONField(name = "H1AreaId")
    private Integer h1AreaId;

    /**
     *
     */
    @JsonProperty("D1AreaId")
    @JSONField(name = "D1AreaId")
    private Integer d1AreaId;

    /**
     * 售后退货，现货库存UserID
     */
    @JsonProperty("InStockUserId")
    @JSONField(name = "InStockUserId")
    private Long inStockUserId;

    /**
     *
     */
    @JsonProperty("YapingAreaId")
    @JSONField(name = "YapingAreaId")
    private Integer yapingAreaId;

}
