package com.jiuji.oa.wuliu.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 顺丰快递 Entity
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-09-29
 */
@Data
@Accessors(chain = true)
@TableName("shunfengNoInfo")
@ApiModel(value = "WuLiuShunfengNoInfoEntity 实体类", description = "顺丰电子面单信息[责任小组:物流] 实体类")
public class WuLiuShunfengNoInfoEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableField("id")
    private Integer id;

    /**
     * 快递单号
     */
    @TableField("mailno")
    private String mailNo;

    /**
     * 物流单 ID
     */
    @TableField("wuliuid")
    private String wuLiuId;

    /**
     * 目的地
     */
    @TableField("destRouteLabel")
    private String destRouteLabel;

    /**
     * 顺丰免单二维码信息
     */
    @TableField("twoDimensionCode")
    private String twoDimensionCode;

    /**
     * 发送方门店 ID
     */
    @TableField("sareaid")
    private Integer sareaid;

    /**
     * 月结卡号
     */
    @TableField("custid")
    private String custId;

    /**
     *
     */
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @TableField("adddate")
    private LocalDateTime addDate;

    /**
     * 发送方手机号
     */
    @TableField("j_mobile")
    private String jMobile;

    /**
     * 接收方手机号
     */
    @TableField("d_mobile")
    private String dMobile;

    /**
     * 签收时间
     */
    @TableField("sign_time")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime signTime;

    /**
     * 用来表示网站获取接口类别（1：九信）
     */
    @TableField("ckind")
    private Integer ckind;

    /**
     * 顺丰国补服务编码
     */
    @TableField("customized_service")
    private String customizedService;

}
