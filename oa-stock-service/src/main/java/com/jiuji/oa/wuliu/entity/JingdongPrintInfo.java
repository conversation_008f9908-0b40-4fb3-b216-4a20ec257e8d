package com.jiuji.oa.wuliu.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * JingdongPrintInfo
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-12-05
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@TableName("Jingdong_PrintInfo")
public class JingdongPrintInfo {

    @TableId
    private Long id;
    @TableField("orderId")
    private String orderId;
    @TableField("deliveryId")
    private String deliveryId;
    @TableField("promiseTimeType")
    private Integer promiseTimeType;
    @TableField("preSortResult")
    private String preSortResult;
    @TableField("transType")
    private Integer transType;
    @TableField("needRetry")
    private Boolean needRetry;
    @TableField("expressOperationMode")
    private Integer expressOperationMode;

    @TableField("areaid")
    private Integer areaId;
    @TableField("appKey")
    private String appKey;

}
