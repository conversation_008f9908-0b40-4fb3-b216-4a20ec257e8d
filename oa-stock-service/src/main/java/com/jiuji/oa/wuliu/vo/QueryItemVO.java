package com.jiuji.oa.wuliu.vo;

import lombok.Data;

import java.io.Serializable;


/**
 * 快递单取消模型
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-12-03
 */
@Data
public class QueryItemVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * C# delivery_id
     */
    private Long deliveryId;

    /**
     * mt_peisong_id
     */
    private String mtPeisongId;

    private String appkey;

    private String timestamp;

    private String version;

    private String sign;

    public String getVersion() {
        return version == null ? "1.0" : version;
    }
}
