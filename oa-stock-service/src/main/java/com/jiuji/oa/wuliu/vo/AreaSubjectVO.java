package com.jiuji.oa.wuliu.vo;


import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * AreaSubject
 * 运单货品信息实体
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-11
 */
@ApiModel(description = "运单货品信息实体")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class AreaSubjectVO extends AreaInfo {

    private static final long serialVersionUID = 1L;

    /**
     * is9JiSubject
     */
    @JsonProperty("is9JiSubject")
    @JSONField(name = "is9JiSubject")
    private Boolean is9JiSubject;

    private Integer areakind;

}
