package com.jiuji.oa.wuliu.vo;


import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * dadaWuliuService.dadaReceiver
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-16
 */
@Data
@Accessors(chain = true)
public class DadaReceiverDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("ShopCode")
    @JSONField(name = "ShopCode")
    private String shopCode;

    @JsonProperty("OrderId")
    @JSONField(name = "OrderId")
    private String orderId;

    @JsonProperty("name")
    @JSONField(name = "name")
    private String name;

    @JsonProperty("mobile")
    @JSONField(name = "mobile")
    private String mobile;

    @JsonProperty("Province")
    @JSONField(name = "Province")
    private String province;

    @JsonProperty("CityName")
    @JSONField(name = "CityName")
    private String cityName;

    /**
     * 区县名称
     * countryName
     */
    @JsonProperty("countryName")
    @JSONField(name = "countryName")
    private String countryName;

    /**
     * 经纬度
     * 位置信息
     */
    private String position;

    @JsonProperty("Address")
    @JSONField(name = "Address")
    private String address;

}
