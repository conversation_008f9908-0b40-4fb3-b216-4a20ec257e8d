package com.jiuji.oa.wuliu.mapper;

import com.jiuji.oa.wuliu.entity.Areaassetsub;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Entity com.jiuji.oa.wuliu.entity.Areaassetsub
 */
public interface AreaassetsubMapper extends BaseMapper<Areaassetsub> {
    Integer updateAreaAssetSub(@Param("userName") String userName, @Param("subChenkType") Integer subChenkType, @Param("danHaoBind") Integer danHaoBind);

    Integer selectAreaAssetSubById(@Param("danHaoBind") Integer danHaoBind);

}




