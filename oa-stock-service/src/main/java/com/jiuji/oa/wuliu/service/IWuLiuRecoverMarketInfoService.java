/*
 *     Copyright © 2006 - 2020 九机网 All Rights Reserved
 *
 */

package com.jiuji.oa.wuliu.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.wuliu.dto.SubPositionDTO;
import com.jiuji.oa.wuliu.dto.req.SubPositionReq;
import com.jiuji.oa.wuliu.entity.WuLiuRecoverMarketInfo2Entity;
import com.jiuji.oa.wuliu.entity.WuLiuRecoverMarketInfoEntity;
import com.jiuji.oa.wuliu.vo.RouteVO;
import com.jiuji.oa.wuliu.vo.res.WuliuOrderInfoRes;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 转售单（良品订单）,责任小组：回收 Service
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-09
 */
public interface IWuLiuRecoverMarketInfoService extends IService<WuLiuRecoverMarketInfoEntity> {

    /**
     * getRecoverMarketInfo
     *
     * @param subId
     * @return WuLiuRecoverMarketInfoEntity
     * @date 2021-10-11
     * <AUTHOR> [<EMAIL>]
     */
    WuLiuRecoverMarketInfoEntity getRecoverMarketInfo(Integer subId, Integer areaId);

    /**
     * recoverServices.getSubBasket
     * 获取订单商品
     *
     * @param subId Integer
     * @param showDel  Integer
     * @return List<WuLiuRecoverMarketInfo2Entity>
     * @date 2021-10-15
     * <AUTHOR> [<EMAIL>]
     */
    List<WuLiuRecoverMarketInfo2Entity> getSubBasket(Integer subId, Integer showDel);

    /**
     * getBasketId
     *
     * @param danHaoBind
     * @return Integer
     * <AUTHOR> [<EMAIL>]
     * @date 2021-10-27
     */
    Integer getBasketId(Integer danHaoBind);


    /**
     * selectSubdate
     *
     * @param subId
     * @return
     */
    List<LocalDateTime> selectSubdate(Long subId);

    /**
     * updateSellerBySubId
     *
     * @param routeVO
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-05
     */
    void updateSellerBySubId(RouteVO routeVO);

    /**
     * updatesubCheckBySubId
     *
     * @param routeVO
     * @return Boolean
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-05
     */
    Boolean updatesubCheckBySubId(RouteVO routeVO);
    /**
     * 查询良品订单收货位置信息
     * @param sub
     * @return
     */
    SubPositionDTO getSubPositionBySub(SubPositionReq sub);

    /**
     * 支付商户订单号查询订单物流信息
     *
     * @param payId
     * @return
     */
    WuliuOrderInfoRes getOrderByPayId(Integer payId);

    WuliuOrderInfoRes getOrderByPayDes(String payDes);

    /**
     * 查询良品订单
     * @param subId
     * @return
     */
    WuLiuRecoverMarketInfoEntity getSubBySubId(Integer subId);

    /**
     * 查询良品订单经纬度
     * @param req
     * @return
     */
    SubPositionDTO getSubAddressPositionBySubId(SubPositionReq req);
}
