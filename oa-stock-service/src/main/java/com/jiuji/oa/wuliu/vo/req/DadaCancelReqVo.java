package com.jiuji.oa.wuliu.vo.req;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2021/11/5 10:52
 */
@Data
public class DadaCancelReqVo {
    /**
     * 第三方订单编号
     */
    @NotEmpty(message = "第三方订单编号不能空")
    @JsonProperty("order_id")
    @JSONField(name = "order_id")
    @ApiModelProperty(value = "第三方订单编号", required = true)
    private String orderId;
    /**
     * 取消原因ID
     */
    @NotNull(message = "取消原因ID不能空")
    @JsonProperty("cancel_reason_id")
    @JSONField(name = "cancel_reason_id")
    @ApiModelProperty(value = "取消原因ID", required = true)
    private Integer cancelReasonId;
    /**
     * 取消原因(当取消原因ID为其他时，此字段必填)
     */
    @JsonProperty("cancel_reason")
    @JSONField(name = "cancel_reason")
    @ApiModelProperty(value = "取消原因(当取消原因ID为其他时，此字段必填)")
    private String cancelReason;
}
