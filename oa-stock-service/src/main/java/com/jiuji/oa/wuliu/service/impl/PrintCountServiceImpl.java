package com.jiuji.oa.wuliu.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.wuliu.entity.PrintCountEntity;
import com.jiuji.oa.wuliu.mapper.PrintCountMapper;
import com.jiuji.oa.wuliu.service.IPrintCountService;
import com.jiuji.oa.wuliu.vo.ShelveDTO;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * <p>
 * 打印次数,责任小组：运营 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-11
 */
@Service
@DS("oanewWrite")
public class PrintCountServiceImpl extends ServiceImpl<PrintCountMapper, PrintCountEntity> implements IPrintCountService {

    /**
     * 用订单号查询打印次数
     *
     * @param subId
     * @return
     */
    @Override
    public Integer getPrintCountBySubId(Integer subId) {
        return this.baseMapper.getPrintCountBySubId(subId);
    }

    /**
     * 订单应付金额
     *
     * @param subId
     * @return
     */
    @Override
    public BigDecimal getOrderPayAble(Integer subId) {
        return this.baseMapper.getOrderPayAble(subId);
    }

    /**
     * 调拨单应付金额
     *
     * @param subId
     * @return
     */
    @Override
    public BigDecimal getTransferPayAble(Integer subId) {
        return this.baseMapper.getTransferPayAble(subId);
    }

    /**
     * 回收订单应付金额
     *
     * @param subId
     * @return
     */
    @Override
    public BigDecimal getReturnPayAble(Integer subId) {
        return this.baseMapper.getReturnPayAble(subId);
    }

    /**
     * 子订单的数量
     *
     * @param subId
     * @return
     */
    @Override
    public Integer countBasket(Integer subId) {
        return this.baseMapper.countBasket(subId);
    }

    /**
     * 根据转售订单号 获取货位号及数量
     *
     * @param subId
     * @return
     */
    @Override
    public ShelveDTO getShelvesNumBySubId(Integer subId) {
        return this.baseMapper.getShelvesNumBySubId(subId);
    }
}
