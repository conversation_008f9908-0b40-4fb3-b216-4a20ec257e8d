package com.jiuji.oa.wuliu.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 销售单状态枚举
 *
 * <AUTHOR>
 * @date 2021/10/09
 */
@Getter
@AllArgsConstructor
public enum JdQuotationEnum implements CodeMessageEnumInterface {
    /**
     * 登记时间
     */
    京东快递0_20KG(0,"1000"),
    京东重货20_100(1,"500"),
    京东零担100KG(2,"2850")
    ;

    /**
     * 代码
     */
    private Integer code;
    /**
     * 消息
     */
    private String message;
}
