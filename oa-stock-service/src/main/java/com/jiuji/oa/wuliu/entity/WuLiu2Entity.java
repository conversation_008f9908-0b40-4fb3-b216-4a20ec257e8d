package com.jiuji.oa.wuliu.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 物流单 Entity
 *
 * <AUTHOR> liu ming
 * @since 2021-08-13
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("wuliu")
public class WuLiu2Entity extends WuLiuEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * //        item.orgcode = dt.Rows[0]["orgcode"].ToString(); //快递公司 目的地网点编码
     *     //        item.destcode = dt.Rows[0]["destcode"].ToString(); //快递公司
     */

    /**
     * 快递公司 目的地网点编码
     */
    @JsonProperty("orgcode")
    @JSONField(name = "orgcode")
    @TableField("orgcode")
    private String orgCode;

    /**
     * 快递公司
     */
    @JsonProperty("destcode")
    @JSONField(name = "destcode")
    @TableField("destcode")
    private String destCode;

    /**
     * 付款方式 1:寄付月结 2收件方付 3第三方付
     */
    @JsonProperty("payType")
    @JSONField(name = "payType")
    @TableField("payType")
    private String payType;

    /**
     * 运送方式 1：标准快递 2：顺风特惠 2：电商特惠
     */
    @JsonProperty("exepresstype")
    @JSONField(name = "exepresstype")
    @TableField("exepresstype")
    private String exepressType;

    /**
     * 月结卡号，付款方式为 寄付月结才有
     */
    @JsonProperty("yuejiekahao")
    @JSONField(name = "yuejiekahao")
    @TableField("yuejiekahao")
    private String yueJieKaHao;

}
