package com.jiuji.oa.wuliu.utils;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpException;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.ch999.common.util.utils.Exceptions;
import com.jiuji.oa.wuliu.entity.ShansongAppConfigs;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.DigestUtils;

import java.nio.charset.StandardCharsets;
import java.util.LinkedHashMap;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 闪送工具类
 *
 * <AUTHOR> liu ming
 * @date 2021 /8/17 10:35
 */
@Slf4j
public final class ShanSongUtil {

    private ShanSongUtil() {
    }

    /**
     * 生成签名
     *
     * @param map the map
     * @return the string
     */
    private static String createSign(Map<String, Object> map, String appSecret) {
        LinkedHashMap<String, Object> sortMap = map.entrySet().stream()
                .filter((Map.Entry<String, Object> x) -> ObjectUtil.isNotEmpty(x.getValue()))
                .sorted(Map.Entry.comparingByKey())
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (oldVal, newVal) -> newVal, LinkedHashMap::new));
        StringBuilder sb = new StringBuilder(appSecret);
        sortMap.forEach((key, value) -> sb.append(key).append(value));
        log.info("闪送快递待加密字符串:{}", sb);
        String sign = DigestUtils.md5DigestAsHex(sb.toString().getBytes(StandardCharsets.UTF_8)).toUpperCase(Locale.ENGLISH);
        log.info("闪送快递签名:{}", sign);
        return sign;
    }

    /**
     * Post string.
     *
     * @param url the url
     * @return the string
     */
    public static String post(ShansongAppConfigs config, String url, Object dataObj) {
        String timestamp = Convert.toStr(System.currentTimeMillis());
        String data = JSONUtil.toJsonStr(dataObj);
        log.info("data参数:{}", data);

        Map<String, Object> map = new LinkedHashMap<>();
        map.put("clientId", config.getClientid());
        map.put("shopId", config.getShopid());
        map.put("timestamp", timestamp);
        map.put("data", data);
        map.put("sign", createSign(map, config.getAppsecrty()));
        log.info("调用接口:{},请求参数:{}", url, JSONUtil.toJsonStr(map));
        try {
            String body = HttpUtil.createPost(url).form(map).contentType("application/x-www-form-urlencoded").execute().body();
            log.info("返回结果body:{}", body);
            return body;
        } catch (HttpException e) {
            log.error("调用闪送接口失败，调用连接: {}, 报错信息: {}", url, Exceptions.getStackTraceAsString(e), e);
        }
        return "";
    }
}
