/*
 *     Copyright © 2006 - 2020 九机网 All Rights Reserved
 *
 */

package com.jiuji.oa.wuliu.service;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ch999.common.util.vo.atlas.Coordinate;
import com.jiuji.oa.nc.abnormal.vo.ShowPrintingEnumVOV2;
import com.jiuji.oa.nc.common.bo.OaUserBO;
import com.jiuji.oa.nc.user.po.DepartInfo;
import com.jiuji.oa.orginfo.areainfo.vo.res.AreaListRes;
import com.jiuji.oa.stock.common.cache.CacheKey;
import com.jiuji.oa.stock.logistics.order.vo.CancelOrderDTO;
import com.jiuji.oa.stock.logistics.order.vo.req.WuliuWayBillNoReq;
import com.jiuji.oa.wuliu.bo.DiaoboPaotuiWuliuBO;
import com.jiuji.oa.wuliu.bo.DiaoboPaotuiWuliuResBO;
import com.jiuji.oa.wuliu.bo.SignatureWuliuBO;
import com.jiuji.oa.wuliu.dto.res.LogisticsMessage;
import com.jiuji.oa.wuliu.entity.ShunfengCustidConfigEntity;
import com.jiuji.oa.wuliu.entity.WuLiuClaimEntity;
import com.jiuji.oa.wuliu.entity.WuLiuEntity;
import com.jiuji.oa.wuliu.vo.*;
import com.jiuji.oa.wuliu.vo.req.*;
import com.jiuji.oa.wuliu.vo.res.ExpressEnumVO;
import com.jiuji.oa.wuliu.vo.res.InnerWuliuInfoRes;
import com.jiuji.oa.wuliu.vo.res.WuLiuResVO;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.enums.EnumVO;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 物流单Service
 *
 * <AUTHOR>
 * @date 2021-05-17 11:24:40
 */
public interface IWuLiuService extends IService<WuLiuEntity> {


    /**
     * 物流快递单查询涉及回收组调用
     * @return
     */
    List<LogisticsMessage> getLogisticsMessage(LogisticsMessageVo logisticsMessageVo);
    /**
     * shunfengApiServices.GetExepressType
     *
     * @param type
     * @return string
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-04
     */
    String getExepressType(String type);

    /**
     * weixinAndOaMessageSend
     *
     * @param msg
     * @param type
     * @param url
     * @param slias
     * @param msgType
     * @param agentId
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-05
     */
    void weixinAndOaMessageSend(String msg, Integer type, String url, Integer slias, Integer msgType, Integer agentId);

    /**
     * getWuLiuClaim
     *
     * @param wuliuid
     * @return WuLiuClaimEntity
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-10
     */
    WuLiuClaimEntity getWuLiuClaim(Integer wuliuid);

    /**
     * 取消订单
     *
     * @param cancelOrderDTO 请求参数
     * @return the boolean
     */
    Boolean cancelOrder(CancelOrderDTO cancelOrderDTO);

    /**
     * 取消订单
     * @param oaUserBO
     * @param deliveryId
     * @return
     */
    Boolean cancelOrder(OaUserBO oaUserBO, Integer deliveryId);

    /**
     * 查询物流单号
     *
     * @param deliveryId     物流单号
     * @param deliveryNumber 快递单号
     * @param bo
     */
    void getDeliveryId(Integer deliveryId, String deliveryNumber, OaUserBO bo);

    /**
     * getWuliuBywCateId
     *
     * @param wCateId
     * @param wutype
     * @param danhaobind
     * @return WuLiuEntity
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-05
     */
    WuLiuEntity getWuliuBywCateId(Integer wCateId, Integer wutype, Integer danhaobind);

    /**
     * 新增物流单
     *
     * @param currentUser OaUserBO
     * @param vo          WuLiuAddOrUpdateReqVO
     * @return R<Integer>
     * @date 2021-10-11
     * <AUTHOR> [<EMAIL>]
     */
    R<Integer> addOrUpdate(OaUserBO currentUser, WuLiuAddOrUpdateReqVO vo);

    @Cached(name = CacheKey.JetCache.AREA_LIST, localExpire = 1, expire = 1, timeUnit = TimeUnit.HOURS, cacheType = CacheType.LOCAL)
    List<AreaListRes> getAreaList();

    /**
     * subWLService.saveWuLiu
     * 保存物流单(actionName="save")
     *
     * @param model
     * @param sessionArea
     * @param userName
     * @return Integer 返回wuliuID
     * @date 2021-10-21
     * <AUTHOR> [<EMAIL>]
     */
    Integer newTransSaveWuLiu(WuLiuAddOrUpdateReqVO model, Integer sessionArea, String userName);

    /**
     * 自动创建物流单，非手动创建物流单调用此接口
     *
     * @param vo WuLiuAddOrUpdateReqV2VO
     * @param sessionAreaId Integer
     * @param username String
     * @return R<Integer>
     * <AUTHOR> [<EMAIL>]
     * @date 2022-01-12
     */
    R<Integer> saveWuliuV1(WuLiuAddOrUpdateReqV2VO vo, Integer sessionAreaId, String username);

    /**
     * 获取单个物流单
     *
     * @param id 物流单 ID
     * @return R<WuLiuResVO>
     * @date 2021-10-05
     * <AUTHOR> [<EMAIL>]
     */
    R<WuLiuResVO> getOne(Integer id);

    /**
     * 第三方快递公司列表
     *
     * @return
     */
    List<EnumVO> listExpressEnum();

    /**
     * 第三方快递公司列表
     *
     * @return
     */
    List<ExpressEnumVO> listExpressEnumV2(Integer wuLiuId);

    List<ShowPrintingEnumVOV2> getShowPrintingEnumVOList();



    /**
     * 获取物流信息
     *
     * @param actionName
     * @param wuLiuId
     * @param subId
     * @return
     */
    List<WuLiuDTO> getWuLiuList(String actionName, Integer wuLiuId, Integer subId);

    /**
     * 获取单个物流信息
     *
     * @param actionName
     * @param wuLiuId
     * @param subId
     * @return
     */
    WuLiuDTO getWuLiu(String actionName, Integer wuLiuId, Integer subId);

    /**
     * 是否是美团快送
     *
     * @param subId
     * @return
     */
    Boolean isFastMeiTuan(Integer subId);

    /**
     * get expectTime
     *
     * @param subId
     * @return
     */
    LocalDateTime getExpectTime(Integer subId);

    /**
     * 良品预计送达时间
     * @param subId
     * @return
     */
    LocalDateTime getLiangpinExpectTime(Integer subId);

    /**
     * 关联订单的物流单
     *
     * @param subId
     * @return
     */
    WuLiuSubDTO getWuLiuSub(Integer subId);


    /**
     * 自提点
     *
     * @param ziTiDianId
     * @return
     */
    ZiTiDianDTO getZiTiDian(Integer ziTiDianId);


    /**
     * 通过wuType获取物流信息
     *
     * @param sAreaId
     * @param wuType
     * @return
     */
    WuLiuEntity getWuLiuByWuType(Integer sAreaId, Integer wuType);

    /**
     * 二手良品订单物流
     *
     * @param subId
     * @return
     */
    WuLiuSubDTO getWuLiuReSub(Integer subId);


    /**
     * 原先就有物流单
     *
     * @param vo
     * @return
     */
    WuLiuDTO getMarkAbnormalWuLiu(WuLiuInfoReqVO vo);

    /**
     * 判断是否走历史库查询
     *
     * @param subId   Integer
     * @param subType Integer
     * @return boolean
     * <AUTHOR> [<EMAIL>]
     * @date 2021-10-15
     */
    boolean isHistory(Integer subId, Integer subType);

    /**
     * apiServices.getAreaIDByCityID
     * 根据 cityId 获取 pid zid did
     *
     * @param cityId
     * @param type
     * @return CityIdListDTO
     * @date 2021-10-13
     * <AUTHOR> [<EMAIL>]
     */
    CityIdListDTO getAreaIdByCityId(Integer cityId, Integer type);

    /**
     * 物流操作
     *
     * @param currentUser
     * @param vo
     * @return R<String>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-04
     */
    R<String> operateV1(OaUserBO currentUser, WuliucaozuoVO vo);

    /**
     * 作废物流单
     *
     * @param linkId   单号
     * @param username 用户名
     * @return {@link Integer}
     */
    Integer delWuliuByLinkId(Long linkId, String username);

    /**
     * 给 C# 发送 RabbitMQ 队列消息以调用 C# 方法
     *
     * @param message 要发送的消息
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-12
     */
    void setRabbitMqMessageForCsharp(String message);

    /**
     * 批量生成中通快递单号(子母件)
     *
     * @param currentUser OaUserBO
     * @param vo          WuLiuGenerateMoreWuLiuNoReqVO
     * @return R<String>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-13
     */
    R<String> generateMoreWuLiuNoV1(OaUserBO currentUser, WuLiuGenerateMoreWuLiuNoReqVO vo);

    /**
     * 作废物流单
     *
     * @param id      物流单 ID
     * @param request
     * @return R<Boolean>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-01
     */
    R<Boolean> invalidV1(Integer id, HttpServletRequest request);

    /**
     * apiServices.isCurAreaHQ_DC_H1
     * 当前门店是否为 HQ_DC_H1
     *
     * @param areaId
     * @return Boolean
     * @date 2021-10-18
     * <AUTHOR> [<EMAIL>]
     */
    boolean isCurAreaHqDcH1(Integer areaId);

    /**
     * apiServices.isCurAreaHQ_DC_H1_D1
     * 当前门店是否为 HQ_DC_H1_D1
     *
     * @param areaId
     * @return Boolean
     * @date 2021-10-18
     * <AUTHOR> [<EMAIL>]
     */
    boolean isCurAreaHqDcH1D1(Integer areaId);

    /**
     * 物流单取消快递，暂时支持顺丰快递
     *
     * @param id 物流单号
     * @return R<Boolean>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-02
     */
    R<Boolean> cancelExpress(Integer id);

    /**
     * 获取顺丰账号配置信息列表 v1
     *
     * @param authorization md5 日期鉴权值
     * @return R<List<ShunfengCustidConfigEntity>>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-28
     */
    R<List<ShunfengCustidConfigEntity>> shunfengCustidConfigListAllV1(String authorization);

    /**
     * 自定义推送内容
     * zitidianServices.sendDIYMsg(string ids, string msg, string userId, string userName)
     *
     * @param ids      id 数组
     * @param msg      消息
     * @param userIds  用户id 数组
     * @param userName 用户名
     * @return {@link R}<{@link Boolean}>
     */
    R<Boolean> sendDIYMsg(String ids, String msg, String userIds, String userName);

    /**
     * 消息推送、发货提醒
     * sendMsg
     **/
    R<Boolean> sendMsg(String ids, String type, String userName);

    /**
     * 物流单批量作废
     *
     * @param idList
     * @return
     */
    R<String> invalidBatch(String idList);

    /**
     * 批量生成中通快递单号(子母件)
     *
     * @param wuLiu
     * @param eCount
     */
    R<String> generateMoreWuLiuNo(WuLiuDTO wuLiu, Integer eCount);

    /**
     * 通知客户端打印
     *
     * @param printId 打印ID
     * @param type 类型
     * @param clientNo
     * @param printCount
     * @return
     */
    R<String> doPrint(String printId, Integer type, String clientNo, Integer printCount);

    /**
     * 物流轨迹
     *
     * @param id
     * @return R<String>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-21
     */
    R<String> traceV1(Integer id);

    /**
     * 物流轨迹
     *
     * @param isCache
     * @return R<String>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-21
     */
     List<DepartInfo> getAllDepartData(Boolean isCache) ;


    R<Boolean> saveScanLog(WuLiuLogReqVO req, OaUserBO currentUser);

    /**
     * 更新物流单的运单号
     * @param wuliuWayBillNoReq
     */
    void updateNuById(WuliuWayBillNoReq wuliuWayBillNoReq);

    /**
     * PayMethod
     * @param sessionAreaId
     * @return
     */
    Integer queryPayMethodByAreaId(Integer sessionAreaId);

    /**
     * 快递单号查询物流单号
     *
     * @param nu
     * @return
     */
    List<WuLiuEntity> getWuliuByNu(String nu);

    /**
     * id查询物流单
     * @param id
     * @return
     */
    WuLiuEntity getWuliuById(Integer id);

    AreaSubjectVO getAreaSubject(Integer id);

    String getWuliuStatsArea(Integer wuType, Integer sAreaId, Integer type);

    CityIdListDTO getAreaInfoByArea(Integer areaId);

    ShunFengCardVO getYueJieKaHao(Integer sAreaId, String expressType);

    String createOrderGroup(OrderGroupDTO conn, Integer sareaid);

    OrderResultDTO emsCreateOrder(OrderInfoDTO orderInfo);

    List<DadaCityVO> getCityCode();

    void subWuliuTransferLogPush(Integer wuliuid, String userName);

    String markGetmark(String sendmarkes, String sendaddress, String receivemarkes, String receiveaddress, String billCode, Boolean b);

    /**
     * 物流类型和绑定单号查询正在进行中（除了作废状态）的物流单
     * @param wuType
     * @param danhaobind
     * @return
     */
    WuLiuEntity getWuLiuByWuTypeAndDanhaobind(Integer wuType, Integer danhaobind);


    void updateStateById(Integer wuliuid, Integer code);

    /**
     * 查询超时未完结的物流单
     * @param days
     * @return
     */
    List<WuLiuEntity> getTimeoutWuliuList(Integer days);

    Coordinate getSendPositionByWuliuId(Long wuliuId);

    /**
     * 查询业务单据调拨单的物流单
     * @param vo
     * @return
     */
    List<InnerWuliuInfoRes> getInnerWuliuInfo(InnerWuliuInfoReq vo);

    /**
     * 内部物流单查询是否关联订单，良品，维修单订货调拨单
     * @param wuliuId
     * @return
     */
    Integer getInnerWuliuCount(Integer wuliuId);

    /**
     * 查询调拨物流单
     * @param wuliuId
     * @return
     */
    Integer getDiaobWuliuCount(Integer wuliuId);

    /**
     * 查询收货人是员工的代签收物流单
     * @return
     */
    Page<SignatureWuliuBO> querySignatureWuliuList(Page<SignatureWuliuBO> page);

    /**
     * 查询物流单关联发货调拨单信息
     * @param data
     * @return
     */
    List<DiaoboPaotuiWuliuResBO> queryDiaoboPaotuiWuliu(DiaoboPaotuiWuliuBO data);

    /**
     * 物流单未投妥
     * @param wuliu
     */
    boolean updateWuliuNotDelivered(WuLiuEntity wuliu);
}
