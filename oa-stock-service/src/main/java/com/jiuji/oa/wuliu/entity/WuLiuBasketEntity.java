package com.jiuji.oa.wuliu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单商品表,责任小组：销售 实体类
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-15
 */
@Data
@Accessors(chain = true)
@TableName("basket")
@ApiModel(value = "WuLiuBasketEntity 实体类", description = "订单商品表,责任小组：销售 实体类")
public class WuLiuBasketEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单详细ID
     */
    @ApiModelProperty("订单详细ID")
    @TableId(value = "basket_id", type = IdType.AUTO)
    private Integer basketId;

    /**
     * 商品数量
     */
    @ApiModelProperty("商品数量")
    @TableField("basket_count")
    private Integer basketCount;

    /**
     * 添加日期
     */
    @ApiModelProperty("添加日期")
    @TableField("basket_date")
    private LocalDateTime basketDate;

    /**
     * 商品配置
     */
    @ApiModelProperty("商品配置")
    @TableField("product_peizhi")
    private String productPeizhi;

    /**
     * 销售人员
     */
    @ApiModelProperty("销售人员")
    @TableField("seller")
    private String seller;

    /**
     * 是否是大件
     */
    @ApiModelProperty("是否是大件")
    @TableField("ismobile")
    private Boolean ismobile;

    /**
     * 单价
     */
    @ApiModelProperty("单价")
    @TableField("price")
    private BigDecimal price;

    /**
     * 订单号
     */
    @ApiModelProperty("订单号")
    @TableField("sub_id")
    private Long subId;

    /**
     * 原价
     */
    @ApiModelProperty("原价")
    @TableField("price1")
    private BigDecimal price1;

    /**
     * 商品规格ID
     */
    @ApiModelProperty("商品规格ID")
    @TableField("ppriceid")
    private Long ppriceid;

    /**
     * 参考成本
     */
    @ApiModelProperty("参考成本")
    @TableField("inprice")
    private BigDecimal inprice;

    /**
     * 搭配商品ID
     */
    @ApiModelProperty("搭配商品ID")
    @TableField("giftid")
    private Integer giftid;

    /**
     * 搭配商品类型
     */
    @ApiModelProperty("搭配商品类型")
    @TableField("type")
    private Integer type;

    /**
     * 是否删除
     */
    @ApiModelProperty("是否删除")
    @TableField("isdel")
    private Boolean isdel;

    /**
     * 是否出库
     */
    @ApiModelProperty("是否出库")
    @TableField("ischu")
    private Boolean ischu;

    /**
     * 可退款金额
     */
    @ApiModelProperty("可退款金额")
    @TableField("price2")
    private BigDecimal price2;

    /**
     * 下单时商品是否现货
     */
    @ApiModelProperty("下单时商品是否现货")
    @TableField("iskc")
    private Boolean iskc;

    /**
     * 是否门店下单
     */
    @ApiModelProperty("是否门店下单")
    @TableField("isOnShop")
    private Boolean isOnShop;

    @TableField("jifenPrice")
    private BigDecimal jifenPrice;

    @TableField("giftPrice")
    private BigDecimal giftPrice;

    @TableField("return_price")
    private BigDecimal returnPrice;

    @TableField("youhuiPrice")
    private BigDecimal youhuiPrice;

//    @TableField("basket_rv")
//    private LocalDateTime basketRv;

}