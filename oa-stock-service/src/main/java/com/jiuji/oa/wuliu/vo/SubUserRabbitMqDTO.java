package com.jiuji.oa.wuliu.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;


/**
 * SubUserRabbitMqDTO
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-12-05
 */
@Data
@Accessors(chain = true)
public class SubUserRabbitMqDTO {
    private String act;
    private SubRabbitMqData data;

    @Data
    @Accessors(chain = true)
    public static class SubRabbitMqData {
        @JsonProperty("sub_id")
        @JSONField(name = "sub_id")
        private Long subId;
        @JsonProperty("increaseInfo")
        @JSONField(name = "increaseInfo")
        private Boolean increaseInfo;
        @JsonProperty("areaid")
        @JSONField(name = "areaid")
        private Integer areaid;
        /**
         * 小件接件
         */
        @JsonProperty("isMsoftSv")
        @JSONField(name = "isMsoftSv")
        private Boolean isMsoftSv;
    }
}
