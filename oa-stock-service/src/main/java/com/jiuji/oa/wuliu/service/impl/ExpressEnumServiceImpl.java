package com.jiuji.oa.wuliu.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.nc.common.constant.RedisKeys;
import com.jiuji.oa.nc.common.db.MyDynamicRoutingDataSource;
import com.jiuji.oa.nc.dict.bo.LogisticsExpressConfigBo;
import com.jiuji.oa.stock.common.constant.CommonConst;
import com.jiuji.oa.stock.common.util.Builder;
import com.jiuji.oa.wuliu.entity.ExpressEnumEntity;
import com.jiuji.oa.wuliu.enums.WuLiuExpressEnum;
import com.jiuji.oa.wuliu.mapper.ExpressEnumMapper;
import com.jiuji.oa.wuliu.service.IExpressEnumService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/8/3 11:49
 */
@Service
@Slf4j
@DS("ch999oanew")
public class ExpressEnumServiceImpl extends ServiceImpl<ExpressEnumMapper, ExpressEnumEntity> implements IExpressEnumService {
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 查询快递公司
     *
     * @param expressCode
     * @return
     */
    @Override
    public String getWuliuCompanyName(String expressCode) {
        if (StringUtils.isBlank(expressCode)) {
            return "";
        }
        return queryAllExpressEnum().stream().filter(v -> expressCode.equals(v.getExpressCode()))
                .findFirst().orElse(Builder.of(ExpressEnumEntity::new).with(ExpressEnumEntity::setExpressName,"").build())
                .getExpressName();
    }

    /**
     * 查询ExpressEnum，包含isdel=1
     *
     * @return
     */
    @Override
    @DS("ch999oanew")
    public List<ExpressEnumEntity> queryAllExpressEnum() {
        String cacheKey = StrUtil.format("{}_{}", RedisKeys.EXPRESS_ENUM_ALL_KEY,
                MyDynamicRoutingDataSource.isTaxModel());
        String resJson = stringRedisTemplate.opsForValue().get(cacheKey);
        if (StringUtils.isNotBlank(resJson)) {
            return JSONUtil.toList(stringRedisTemplate.opsForValue().get(cacheKey), ExpressEnumEntity.class);
        }

        List<ExpressEnumEntity> allList = this.baseMapper.selectAllExpressEnum();
        if (CollectionUtils.isNotEmpty(allList)) {
            stringRedisTemplate.opsForValue()
                    .set(cacheKey, JSONUtil.toJsonStr(allList), 1, TimeUnit.DAYS);
        }
        return allList;
    }

    /**
     * 配置物流中台的快递
     *
     * @return
     */
    @Override
    @DS("oanewWrite")
    public void configLogisticsExpressEnum(LogisticsExpressConfigBo logisticsExpressConfigBo) {
        List<ExpressEnumEntity> allList = this.baseMapper.selectAllExpressEnum();
        for (ExpressEnumEntity enumEntity : allList) {
            String code = enumEntity.getExpressCode();
            String name = enumEntity.getExpressName();
            boolean delFlag = false;
            boolean logisticsFlag = false;
            if (WuLiuExpressEnum.SHUN_FENG_JIU_JI.getCode().equals(code)) {
                delFlag = logisticsExpressConfigBo.getShunfengJiuji();
                logisticsFlag = true;
            } else if (WuLiuExpressEnum.JINGDONG_JIUJI_NEW.getCode().equals(code)) {
                delFlag = logisticsExpressConfigBo.getJingdongJiuji();
                logisticsFlag = true;
            } else if (WuLiuExpressEnum.MEITUAN_JIUJI.getCode().equals(code)) {
                delFlag = logisticsExpressConfigBo.getMeituanJiuji();
                logisticsFlag = true;
            } else if (WuLiuExpressEnum.DADA_JIUJI.getCode().equals(code)) {
                delFlag = logisticsExpressConfigBo.getDadaJiuji();
                logisticsFlag = true;
            }
            if (logisticsFlag) {
                this.baseMapper.updateExpressEnumById(enumEntity.getId(), !delFlag);
            }
        }

    }
}
