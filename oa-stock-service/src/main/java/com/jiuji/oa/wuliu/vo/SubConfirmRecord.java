package com.jiuji.oa.wuliu.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * sub.subConfirmRecord
 * 订单确认记录
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-28
 */
@TableName("subConfirmRecord")
@Data
@Accessors(chain = true)
public class SubConfirmRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @JsonProperty("id")
    @JSONField(name = "id")
    private Integer id;

    /**
     *
     */
    @JsonProperty("sub_id")
    @JSONField(name = "sub_id")
    private Long subId;

    /**
     *
     */
    @JsonProperty("dtime")
    @JSONField(name = "dtime")
    private LocalDateTime dtime;

    /**
     * 1 订单  2 良品
     */
    @JsonProperty("subType")
    @JSONField(name = "subType")
    private Integer subType;

    /**
     *
     */
    @JsonProperty("inuser")
    @JSONField(name = "inuser")
    private String inuser;

    /**
     * 开始计算时间（根据下单和门店运营时间计算）
     */
    @JsonProperty("stime")
    @JSONField(name = "stime")
    private LocalDateTime stime;

    /**
     * 操作类型
     */
    @JsonProperty("operationKinds")
    @JSONField(name = "operationKinds")
    private Integer operationKinds;

}
