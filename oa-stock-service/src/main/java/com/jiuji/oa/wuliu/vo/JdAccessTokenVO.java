package com.jiuji.oa.wuliu.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 京东Token
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-12-13
 */
@Setter
@Getter
public class JdAccessTokenVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    private String appKey;

    private String access_token;

    @JsonProperty("expires_in")
    @JSONField(name = "expires_in")
    private Long expires_in;

    @JsonProperty("refresh_token")
    @JSONField(name = "refresh_token")
    private String refreshToken;

    private String scope;

    @JsonProperty("open_id")
    @JSONField(name = "open_id")
    private String openId;

    private LocalDateTime expiresDate;

}
