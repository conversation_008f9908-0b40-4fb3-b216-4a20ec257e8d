package com.jiuji.oa.wuliu.vo;


import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * AbnomalWuliuRequest
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-16
 */
@Data
@Accessors(chain = true)
public class AbnomalWuliuRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 物流单
     */
    @JsonProperty("WuliuId")
    @JSONField(name = "WuliuId")
    private String wuliuId;

    /**
     * 备注
     */
    @JsonProperty("Remark")
    @JSONField(name = "Remark")
    private String remark;

    /**
     * 添加员工
     */
    @JsonProperty("Ch999Id")
    @JSONField(name = "Ch999Id")
    private Integer ch999Id;

    /**
     * 是否取消
     */
    @JsonProperty("IsCancel")
    @JSONField(name = "IsCancel")
    private Boolean isCancel;

}
