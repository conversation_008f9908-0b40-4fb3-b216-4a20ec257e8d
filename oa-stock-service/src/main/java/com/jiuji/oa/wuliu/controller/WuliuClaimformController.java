package com.jiuji.oa.wuliu.controller;


import cn.hutool.json.JSONUtil;
import com.jiuji.oa.stock.publiccheck.annotation.AddLog;
import com.jiuji.oa.stock.publiccheck.entity.AddLogKind;
import com.jiuji.oa.wuliu.service.IWuliuClaimformService;
import com.jiuji.oa.wuliu.service.impl.WuliuClaimformServiceImpl;
import com.jiuji.oa.wuliu.vo.WuliuClaimformCheckerRes;
import com.jiuji.oa.wuliu.vo.WuliuClaimformReq;
import com.jiuji.oa.wuliu.vo.WuliuClaimformRes;
import com.jiuji.tc.common.vo.R;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR> yao yao
 * @since 2023-10-19
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/wuliu/claimform")
public class WuliuClaimformController {


    @Resource
    private IWuliuClaimformService wuliuClaimformService;

    /**
     * 物流报账单详情
     * @param req 请求数据
     */
    @ApiOperation("物流报账单详情")
    @PostMapping("/detail/v1")
    public R<WuliuClaimformRes> getDetail(@RequestBody @Valid WuliuClaimformReq req) {
        WuliuClaimformRes result = wuliuClaimformService.getDetail(req);
        return R.success(result);
    }

    @ApiOperation("获取物流报账单审核人")
    @AddLog(type = AddLogKind.REVIEWED_BY)
    @PostMapping("/getChecker/v1")
    public R<WuliuClaimformCheckerRes> getChecker(@RequestBody @Valid WuliuClaimformReq req) {
        WuliuClaimformCheckerRes result = wuliuClaimformService.getChecker(req);
        /**
         * 判断返回人是不是系统
         */
        if(WuliuClaimformServiceImpl.DEFAULT_CHECKERCH999_NAME.equals(result.getCheckerCh999Name())){
            wuliuClaimformService.approveClaimFormSave(req.getId());
        }
        return R.success(result);
    }

    @ApiOperation("删除物流报账待办")
    @PostMapping("/deleteTodoList/v1")
    public R<Boolean> deleteTodoList(@RequestBody @Valid WuliuClaimformReq req) {
        log.error("删除物流报账待办传入参数：{}", JSONUtil.toJsonStr(req));
        Boolean result = wuliuClaimformService.deleteTodoList(req);
        return R.success(result);
    }

    /**
     * 定时检查报账单
     *
     * @return
     */
    @ApiOperation(value = "定时检查报账单", notes = "定时检查报账单")
    @GetMapping("/checkWuliuClaimform24/v1")
    public R<String> checkWuliuClaimform24() {
        wuliuClaimformService.checkWuliuClaimform24();
        return R.success("执行成功");
    }

    /**
     * 定时检查报账单
     *
     * @return
     */
    @ApiOperation(value = "定时检查报账单", notes = "定时检查报账单")
    @GetMapping("/checkWuliuClaimform48/v1")
    public R<String> checkWuliuClaimform48() {
        wuliuClaimformService.checkWuliuClaimform48();
        return R.success("执行成功");
    }

}

