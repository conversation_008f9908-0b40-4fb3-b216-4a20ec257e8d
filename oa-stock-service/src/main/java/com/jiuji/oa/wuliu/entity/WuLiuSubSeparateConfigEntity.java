package com.jiuji.oa.wuliu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 实体类
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-15
 */
@Data
@Accessors(chain = true)
@TableName("subSeparateConfig")
@ApiModel(value = "WuLiuSubSeparateConfigEntity 实体类", description = "WuLiuSubSeparateConfigEntity 实体类")
public class WuLiuSubSeparateConfigEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("subDate")
    private LocalDateTime subDate;

    @TableField("subId")
    private Long subId;

    @TableField("subType")
    private Integer subType;

    @TableField("basketId")
    private Long basketId;

}