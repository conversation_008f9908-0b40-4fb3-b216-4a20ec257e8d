/*
 *     Copyright © 2006 - 2020 九机网 All Rights Reserved
 *
 */

package com.jiuji.oa.wuliu.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.wuliu.bo.WuliuExpressMqBO;
import com.jiuji.oa.wuliu.entity.WuLiuShunfengNoInfoEntity;

import java.util.List;

/**
 * Service
 *
 * <AUTHOR>
 * @date 2021-05-17 11:24:40
 */
public interface IWuLiuShunfengNoInfoService extends IService<WuLiuShunfengNoInfoEntity> {

    /**
     * 批量新增
     *
     * @param list
     * @return boolean
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-29
     */
    boolean addAll(List<WuLiuShunfengNoInfoEntity> list);

    /**
     * saveShunfengNoInfo
     * @param shunfengNoInfo
     */
    void saveShunfengNoInfo(WuliuExpressMqBO<WuLiuShunfengNoInfoEntity> shunfengNoInfo);

    /**
     * 查询顺丰面单
     * @param waybillCode
     * @param wuliuId
     * @return
     */
    WuLiuShunfengNoInfoEntity queryByMailNoAndWuliuId(String waybillCode, Integer wuliuId);
}
