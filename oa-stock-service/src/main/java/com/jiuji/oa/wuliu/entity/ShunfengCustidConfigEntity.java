package com.jiuji.oa.wuliu.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 顺丰账号配置信息表
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-15
 */
@Data
@Accessors(chain = true)
@TableName("shunfengCustidConfig")
@ApiModel(value = "ShunfengCustidConfigEntity 实体类", description = "顺丰账号配置信息")
public class ShunfengCustidConfigEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("id")
    private Integer id;

    @TableField("custid")
    private String custid;

    @TableField("clientCode")
    private String clientCode;

    @TableField("checkWord")
    private String checkWord;

    @TableField("xtenant")
    private String xtenant;

    @TableField("authorizeid")
    private String authorizeid;

    @TableField("areaids")
    private String areaids;

}