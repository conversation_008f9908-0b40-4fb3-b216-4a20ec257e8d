package com.jiuji.oa.wuliu.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 转售单（良品订单）,责任小组：回收 Entity
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-08
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("recover_marketInfo")
public class WuLiuRecoverMarketInfo2Entity extends WuLiuRecoverMarketInfoEntity {

    private static final long serialVersionUID = 1L;

    @TableField("product_name")
    private String productName;

}
