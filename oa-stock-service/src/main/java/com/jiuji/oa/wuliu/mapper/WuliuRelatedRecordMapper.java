package com.jiuji.oa.wuliu.mapper;

import com.jiuji.oa.wuliu.entity.WuliuRelatedRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Entity com.jiuji.oa.wuliu.entity.WuliuRelatedRecord
 */
@Mapper
public interface WuliuRelatedRecordMapper extends BaseMapper<WuliuRelatedRecord> {

    /**
     * 物流单号查询物流关联记录
     * @param wuliuId
     * @return
     */
    WuliuRelatedRecord getWuLiuRelatedRecordByWuliuId(@Param("wuliuId") Integer wuliuId);
}




