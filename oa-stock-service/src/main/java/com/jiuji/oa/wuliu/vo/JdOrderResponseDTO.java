package com.jiuji.oa.wuliu.vo;


import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * JdOrderResponse
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-16
 */
@Data
@Accessors(chain = true)
public class JdOrderResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 快递单号
     */
    @JsonProperty("waybillCode")
    @JSONField(name = "waybillCode")
    private String waybillCode;

}
