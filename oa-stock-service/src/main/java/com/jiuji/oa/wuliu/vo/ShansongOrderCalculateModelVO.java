package com.jiuji.oa.wuliu.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/8 19:30
 */
@Data
public class ShansongOrderCalculateModelVO {
    /**
     * 总距离，单位米
     */
    private BigDecimal totalDistance;

    /**
     * 总续重，单位kg
     */
    private BigDecimal totalWeight;

    /**
     * 闪送订单号
     */
    private String orderNumber;

    /**
     * 费用列表
     */
    private List<ShansongFeeInfoVO> feeInfoList;

    /**
     * 未优惠需要支付的费用
     */
    private BigDecimal totalAmount;

    /**
     * 优惠的额度
     */
    private BigDecimal couponSaveFee;

    /**
     * 实际支付的费用
     */
    private BigDecimal totalFeeAfterSave;

}
