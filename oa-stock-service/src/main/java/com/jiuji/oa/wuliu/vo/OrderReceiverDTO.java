package com.jiuji.oa.wuliu.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * zhongtongApiServices.OrderReceiver
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-16
 */
@Data
@Accessors(chain = true)
public class OrderReceiverDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 收件人姓名
     */
    @JsonProperty("name")
    @JSONField(name = "name")
    private String name;

    /**
     * 收件人手机号码
     */
    @JsonProperty("mobile")
    @JSONField(name = "mobile")
    private String mobile;

    /**
     * 收件人所在城市，逐级指定，用英文半角逗号分隔
     */
    @JsonProperty("city")
    @JSONField(name = "city")
    private String city;

    /**
     * 收件人详细地址
     */
    @JsonProperty("address")
    @JSONField(name = "address")
    private String address;


    // EMS 创建快递单需要额外增加以下字段 开始
    /**
     *
     */
    @ApiModelProperty("prov")
    private String prov;

    /**
     *
     */
    @ApiModelProperty("county")
    private String county;
    // EMS 创建快递单需要额外增加以下字段 结束

}
