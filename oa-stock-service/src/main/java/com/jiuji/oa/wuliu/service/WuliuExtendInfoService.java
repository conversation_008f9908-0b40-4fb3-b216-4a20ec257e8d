package com.jiuji.oa.wuliu.service;

import com.jiuji.oa.wuliu.entity.WuLiuEntity;
import com.jiuji.oa.wuliu.entity.WuliuExtendInfo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;
import java.util.List;

/**
 *
 */
public interface WuliuExtendInfoService extends IService<WuliuExtendInfo> {

    /**
     * 保存或者更新
     * @param wuliuExtendInfo
     * @return
     */
    boolean saveOrUpdateExtendInfo(WuliuExtendInfo wuliuExtendInfo);

    /**
     * 清除距离成本信息
     * @param id
     * @return
     */
    boolean clearInfoByWuLiuId(Integer id);

    /**
     * 物流单查询扩展信息
     *
     * @param wuliuId
     * @return
     */
    WuliuExtendInfo queryWuliuExtendInfoByWuliuId(Integer wuliuId);

    /**
     * 查询物流单骑行成本配置
     */
    BigDecimal queryDistributionCost(Long distance);

    /**
     * 查询未计算骑行距离的物流单
     * @param days
     * @return
     */
    List<WuLiuEntity> queryWuliuByNotDistributionCost(Integer days);

    /**
     * 查询物流单
     * @param days
     * @return
     */
    Integer queryWuliuIdByDtime(Integer days);
}
