package com.jiuji.oa.wuliu.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 取消快递通知C# 方法数据传输实体
 * <AUTHOR>
 * @date 2022/7/5 10:12
 */
@Data
@Accessors(chain = true)
public class CancelWuliuNoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 物流单号
     */
    @JsonProperty("wuliuId")
    private Long wuliuId;

    /**
     * 快递单号
     */
    @JsonProperty("waybillNo")
    private String nu;

    /**
     * 订单号
     */
    @JsonProperty("bussinessId")
    private Long subId;

    /**
     * wytype
     */
    @JsonProperty("wuliuType")
    private Integer wuliuType;

    /**
     * 操作人
     */
    @JsonProperty("employee")
    private String user;
}
