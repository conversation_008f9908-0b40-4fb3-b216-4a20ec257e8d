package com.jiuji.oa.wuliu.mapper;

import com.baomidou.mybatisplus.annotation.SqlParser;
import com.jiuji.oa.wuliu.entity.WuliuExpressTime;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Entity com.jiuji.oa.wuliu.entity.WuliuExpressTime
 */
public interface WuliuExpressTimeMapper extends BaseMapper<WuliuExpressTime> {

    @SqlParser(filter = true)
    void saveOrUpdateExpressCreateTime(@Param("wuliuExpressTime") WuliuExpressTime wuliuExpressTime);
}




