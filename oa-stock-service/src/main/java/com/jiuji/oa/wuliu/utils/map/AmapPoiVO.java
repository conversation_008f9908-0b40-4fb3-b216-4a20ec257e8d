package com.jiuji.oa.wuliu.utils.map;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * 高德地图 搜索POI 返回实体
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2022-01-29
 */
@Setter
@Getter
@ToString
public class AmapPoiVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 结果状态值，值为0或1
     * 0：请求失败；1：请求成功
     */
    private String status;
    /**
     * 返回状态说明
     * status为0时，info返回错误原因，否则返回“OK”。
     */
    private String info;
    /**
     * 搜索方案数目(最大值为1000)
     */
    private String count;
    /**
     * 搜索POI信息列表
     */
    private List<AmapPoisVO> pois;

    /**
     * AmapPoisVO
     *
     * <AUTHOR> [<EMAIL>]
     * @date 2022-01-29
     */
    @Setter
    @Getter
    @ToString
    public static class AmapPoisVO {

        private static final long serialVersionUID = 1L;

        /**
         * 唯一ID
         */
        private String id;
        /**
         * 名称
         */
        private String name;
        /**
         * 地址
         * 东四环中路189号百盛北门
         */
        private String address;
        /**
         * 经纬度
         * 格式：X,Y
         */
        private String location;
        /**
         * POI所在省份编码
         * extensions=all时返回
         */
        private String pcode;
        /**
         * POI所在省份名称
         * 若是直辖市的时候，此处直接显示市名，例如北京市
         */
        private String pname;
        /**
         * 城市编码
         * extensions=all时返回
         */
        private String citycode;
        /**
         * 城市名
         * 若是直辖市的时候，此处直接显示市名，例如北京市
         */
        private String cityname;
        /**
         * 区域编码
         * extensions=all时返回
         */
        private String adcode;
        /**
         * 区域名称
         * 区县级别的返回，例如朝阳区
         */
        private String adname;
    }

}
