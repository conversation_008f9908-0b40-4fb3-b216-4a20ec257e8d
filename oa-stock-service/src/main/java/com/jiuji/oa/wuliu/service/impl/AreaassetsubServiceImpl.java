package com.jiuji.oa.wuliu.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.wuliu.entity.Areaassetsub;
import com.jiuji.oa.wuliu.mapper.AreaassetsubMapper;
import com.jiuji.oa.wuliu.service.AreaassetsubService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * AreaassetsubServiceImpl
 *
 * <AUTHOR>
 * @date 2021-12-15
 */
@DS("officeWrite")
@Service
public class AreaassetsubServiceImpl extends ServiceImpl<AreaassetsubMapper, Areaassetsub>
implements AreaassetsubService{

    /**
     * updateAreaAssetSub
     *
     * @param userName
     * @param subChenkType
     * @param danHaoBind
     * @return List<Areaassetsub>
     * <AUTHOR> [<EMAIL>]
     * @date 2021-12-15
     */
    @Override
    public List<Areaassetsub> updateAreaAssetSub(String userName, Integer subChenkType, Integer danHaoBind) {
        // TODO 建议此处对接常用资产接口，而非翻写代码。
        List<Areaassetsub> list = lambdaQuery().eq(Areaassetsub::getId, danHaoBind).list();
        baseMapper.updateAreaAssetSub(userName, subChenkType, danHaoBind);
        return list;
    }

    /**
     * C# AssetCommon.CreateAreaAssetSubPingzheng
     * 常用资产接收凭证
     * @param subid
     * @param user
     */
    @DS("oanewOfficeWrite")
    @Override
    public void createAreaAssetSubPingzheng(Integer subid, String user) {
        // TODO 建议此处对接常用资产接口，而非翻写代码。
    }

}




