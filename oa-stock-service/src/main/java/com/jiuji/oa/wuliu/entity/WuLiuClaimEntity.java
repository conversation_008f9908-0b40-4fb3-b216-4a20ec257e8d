package com.jiuji.oa.wuliu.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR> [<EMAIL>]
 * @date 2021-11-10
 */
@Data
@Accessors(chain = true)
public class WuLiuClaimEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private String com;

    private LocalDateTime dtime;

    private Integer sareaid;

    private Integer areaid;

    /**
     * wuliu claim id
     */
    @TableField("Id")
    private Integer id;

    private Integer stats;

    private Integer rareaid;

    @TableField("isCreateManually")
    private Boolean isCreateManually;

    /**
     * 物流类别
     */
    private Integer wutype;
    /**
     * 关联单号
     */
    private Integer danhaobind;
    /**
     * 配送方式
     */
    private Integer delivery;
}
