package com.jiuji.oa.wuliu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> yao yao
 * @since 2023-10-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("wuliuClaimForm")
public class WuliuClaimform implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "Id", type = IdType.AUTO)
    private Integer id;

    @TableField("wuliuId")
    private Integer wuliuid;

    @TableField("Amount")
    private BigDecimal amount;

    @TableField("Mileage")
    private Double mileage;

    @TableField("Courier")
    private String courier;

    /**
     * 申请人
     */
    @TableField("CreatorId")
    private Integer creatorid;

    @TableField("CreateTime")
    private LocalDateTime createtime;

    /**
     * 审核人
     */
    @TableField("CheckerId")
    private Integer checkerid;

    @TableField("CheckTime")
    private LocalDateTime checktime;

    @TableField("Status")
    private Integer status;

    @TableField("Remark")
    private String remark;

    @TableField("RefuseReason")
    private String refusereason;

    @TableField("OrderTime")
    private LocalDateTime ordertime;

    @TableField("caiwuApplyId")
    private Integer caiwuapplyid;

    @TableField("trackingNo")
    private String trackingno;

    private Integer u8VoucherId;

    /**
     * 订单类型 新机订单 = 1,良品订单 = 2,售后预约 = 3,售后维修 = 4,小件接件单 = 5,回收订单 = 6
     */
    private Integer subType;

    /**
     * 关联单号
     */
    private Long subId;


}
