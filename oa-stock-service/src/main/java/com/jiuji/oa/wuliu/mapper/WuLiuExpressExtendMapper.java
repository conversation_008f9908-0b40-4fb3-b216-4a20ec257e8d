package com.jiuji.oa.wuliu.mapper;

import com.jiuji.oa.wuliu.entity.WuLiuExpressExtendEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
    import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 *  Mapper 接口
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-22
 */
@Mapper
public interface WuLiuExpressExtendMapper extends BaseMapper<WuLiuExpressExtendEntity> {
    /**
     * 查询京东快递快递类型
     * @param wuliuId
     * @return
     */
    @Select("select top 1 express_type from wuliu_express_extend with(nolock) where wuliu_id = #{wuliuId} order by id desc")
    String selectJdExpressType(@Param("wuliuId") Integer wuliuId);
}
