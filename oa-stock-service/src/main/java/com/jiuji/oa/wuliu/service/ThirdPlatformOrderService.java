package com.jiuji.oa.wuliu.service;

import com.jiuji.oa.wuliu.entity.ThirdPlatformOrderEntity;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * ThirdPlatformOrderService 服务类
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2022-01-19
 */
public interface ThirdPlatformOrderService extends IService<ThirdPlatformOrderEntity> {

    /**
     * buyer_mobile 是电话号码包含虚拟和真实的 cancel_check是平台取消订单(1为取消)
     *
     * @param subId 订单号
     * @return String
     * <AUTHOR> [<EMAIL>]
     * @date 2022-01-19
     */
    String getBuyerMobile(Long subId);

}
