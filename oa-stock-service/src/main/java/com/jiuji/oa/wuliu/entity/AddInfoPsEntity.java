package com.jiuji.oa.wuliu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 用户地址操作记录,责任小组：会员
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("Addinfops")
public class AddInfoPsEntity extends Model<AddInfoPsEntity> {

    private static final long serialVersionUID = 7899828266156065541L;

    /**
     * 编号
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 收货人
     */
    @TableField("Reciver")
    private String receiver;

    /**
     * 收货地址
     */
    @TableField("Address")
    private String address;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 座机
     */
    @TableField("Tel")
    private String tel;

    /**
     * 邮箱
     */
    @TableField("Email")
    private String email;

    /**
     * 城市地址ID
     */
    private Integer cityId;

    /**
     * 类型 1 售后取件地址 2 售后发货地址
     */
    private Integer type;

    /**
     * 售后ID
     */
    @TableField("BindId")
    private Integer bindId;

    /**
     * 配送人员
     */
    private String psUser;

    /**
     * 取件人
     */
    @TableField("Consignee")
    private String consignee;

    @TableField("detail_address")
    private String detailAddress;

    @TableField("position")
    private String position;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
