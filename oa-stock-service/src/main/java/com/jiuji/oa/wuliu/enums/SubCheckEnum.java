package com.jiuji.oa.wuliu.enums;

import com.jiuji.oa.nc.abnormal.vo.ShowPrintingEnumVO;
import com.jiuji.oa.procurementStatistics.enums.IsPrototypeEnum;
import com.jiuji.oa.stock.accountingRecords.enums.AccountingStateEnum;
import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 销售单状态枚举
 *
 * <AUTHOR>
 * @date 2021/10/09
 */
@Getter
@AllArgsConstructor
public enum SubCheckEnum  implements CodeMessageEnumInterface {
    /**
     * 登记时间
     */
    NOT_CONFIRM(0,"未确认"),
    ALREADY_CONFIRM(1,"已确认"),
    ALREADY_OUT(2,"已出库"),
    ALREADY_COMPLETE(3,"已完成"),
    ALREADY_DELETE(4,"已删除"),
    WAITING_CONFIRM(5,"等待确认"),
    DEBT(6,"欠款"),
    WAITING_HANDLE(7,"待处理"),
    CANCEL(8,"退订"),
    REFUND(9,"退款")
    ;

    /**
     * 进行中状态
     */
    public static List<Integer> IN_PROGRESS_CHECK = Stream.of(SubCheckEnum.NOT_CONFIRM.getCode(),SubCheckEnum.ALREADY_CONFIRM.getCode(),
            SubCheckEnum.ALREADY_OUT.getCode(),SubCheckEnum.DEBT.getCode()).collect(Collectors.toList());

    /**
     * 代码
     */
    private Integer code;
    /**
     * 消息
     */
    private String message;

    public static List<ShowPrintingEnumVO> getAllPrintingEnum() {
        SubCheckEnum[] array = SubCheckEnum.values();
        List<ShowPrintingEnumVO> arrayList = new ArrayList<>();
        for (SubCheckEnum t : array) {
            ShowPrintingEnumVO showPrintingEnumVO = new ShowPrintingEnumVO()
                    .setLabel(t.getMessage())
                    .setValue(t.getCode());
            arrayList.add(showPrintingEnumVO);
        }
        return arrayList;
    }
    public static String getMessage(Integer code) {
        for (SubCheckEnum typeEnum : values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum.getMessage();
            }
        }
        return "";
    }
}
