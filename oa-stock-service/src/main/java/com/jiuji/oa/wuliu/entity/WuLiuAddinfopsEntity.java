package com.jiuji.oa.wuliu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 用户地址操作记录[责任小组:会员] 实体类
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-11-04
 */
@Data
@Accessors(chain = true)
@TableName("Addinfops")
@ApiModel(value = "WuLiuAddinfopsEntity 实体类", description = "用户地址操作记录[责任小组:会员] 实体类")
public class WuLiuAddinfopsEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    @ApiModelProperty("编号")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 收货人
     */
    @ApiModelProperty("收货人")
    @TableField("Reciver")
    private String reciver;

    /**
     * 收货地址
     */
    @ApiModelProperty("收货地址")
    @TableField("Address")
    private String address;

    /**
     * 手机号码
     */
    @ApiModelProperty("手机号码")
    @TableField("mobile")
    private String mobile;

    /**
     * 座机
     */
    @ApiModelProperty("座机")
    @TableField("Tel")
    private String tel;

    /**
     * 邮箱
     */
    @ApiModelProperty("邮箱")
    @TableField("Email")
    private String email;

    /**
     * 城市地址ID
     */
    @ApiModelProperty("城市地址ID")
    @TableField("cityid")
    private Integer cityid;

    /**
     * 类型 1 售后取件地址 2 售后发货地址
     */
    @ApiModelProperty("类型 1 售后取件地址 2 售后发货地址")
    @TableField("type")
    private Integer type;

    /**
     * 售后ID
     */
    @ApiModelProperty("售后ID")
    @TableField("BindId")
    private Integer bindId;

    /**
     * 配送人员
     */
    @ApiModelProperty("配送人员")
    @TableField("psuser")
    private String psuser;

    /**
     * 取件人
     */
    @ApiModelProperty("取件人")
    @TableField("Consignee")
    private String consignee;

}