package com.jiuji.oa.wuliu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 银行卡日志[责任小组:财务] 实体类
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-11-04
 */
@Data
@Accessors(chain = true)
@TableName("cardLogs")
@ApiModel(value = "WuLiuCardLogsEntity 实体类", description = "银行卡日志[责任小组:财务] 实体类")
public class WuLiuCardLogsEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 优惠码id
     */
    @ApiModelProperty("优惠码id")
    @TableField("cardid")
    private Integer cardid;

    /**
     * 订单id
     */
    @ApiModelProperty("订单id")
    @TableField("sub_id")
    private Integer subId;

    /**
     * 添加时间
     */
    @ApiModelProperty("添加时间")
    @TableField("PushTime")
    private LocalDateTime pushTime;

    /**
     * 门店简码
     */
    @ApiModelProperty("门店简码")
    @TableField("area")
    private String area;

    /**
     * 使用用户
     */
    @ApiModelProperty("使用用户")
    @TableField("useren")
    private String useren;

    /**
     * 使用用户id
     */
    @ApiModelProperty("使用用户id")
    @TableField("userid")
    private Integer userid;

    /**
     * 使用类别
     */
    @ApiModelProperty("使用类别")
    @TableField("useType")
    private Integer useType;

    /**
     * 门店id
     */
    @ApiModelProperty("门店id")
    @TableField("areaid")
    private Integer areaid;

    /**
     * 良品标识
     */
    @ApiModelProperty("良品标识")
    @TableField("liangpin")
    private Integer liangpin;

    /**
     * 使用金额
     */
    @ApiModelProperty("使用金额")
    @TableField("prices")
    private Double prices;

    @TableField("cardLogs_rv")
    private LocalDateTime cardlogsRv;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("basket_id")
    private Integer basketId;

}