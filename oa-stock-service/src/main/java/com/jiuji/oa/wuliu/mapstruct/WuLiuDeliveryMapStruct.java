package com.jiuji.oa.wuliu.mapstruct;

import com.jiuji.oa.wuliu.entity.WuLiuDeliveryEntity;
import com.jiuji.tc.utils.enums.EnumVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * 物流单配送方式 MapStruct
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-09-028
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface WuLiuDeliveryMapStruct {

    WuLiuDeliveryMapStruct INSTANCE = Mappers.getMapper(WuLiuDeliveryMapStruct.class);

    /**
     * 转 EnumVO
     *
     * @param entity WuLiuDeliveryEntity
     * @return EnumVO
     * @date 2021-09-28
     * <AUTHOR> [<EMAIL>]
     */
    @Mapping(target = "value", source = "id")
    @Mapping(target = "label", source = "name")
    EnumVO toEnumVo(WuLiuDeliveryEntity entity);

}
