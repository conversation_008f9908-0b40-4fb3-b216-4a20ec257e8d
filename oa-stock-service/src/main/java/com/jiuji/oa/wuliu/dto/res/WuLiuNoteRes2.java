package com.jiuji.oa.wuliu.dto.res;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.jiuji.oa.stock.common.easyexcel.LocalDateTimeConverter;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * WuLiuNoteRes
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-12-05
 */
@Setter
@Getter
@ExcelIgnoreUnannotated
public class WuLiuNoteRes2 {

    @ExcelProperty(value = "类别")
    private String wuLiuCate;
    @ExcelProperty(value = "单号")
    private Long id;
    @ExcelProperty(value = "寄件人")
    private String sendName;
    @ExcelProperty(value = "寄件地")
    private String sendArea;
    private String sendAreaName;
    @ExcelProperty("寄件门店小区")
    private String sendSmallArea;

    @ExcelProperty("寄件门店大区")
    private String sendBigArea;

    @ExcelProperty("收件人")
    private String receiveName;

    @ExcelProperty("收件地")
    private String receiveAddress;

    @ExcelProperty("收件门店小区")
    private String receiveSmallArea;

    @ExcelProperty("收件门店大区")
    private String receiveBigArea;

    @ExcelProperty(value= "时间",converter = LocalDateTimeConverter.class)
    private LocalDateTime registerTime;

    @ExcelProperty("超时")
    private String overtime;

    @ExcelProperty("备注")
    private String comment;

    @ExcelProperty("提交人")
    private String operator;

    @ExcelProperty("状态")
    private String status;

    @ExcelProperty("报销费用")
    private BigDecimal fee;

    /**
     * 骑行配送成本
     */
    @ExcelProperty("成本")
    private BigDecimal distributionCost;
    /**
     * 骑行距离
     */
    @ExcelProperty("骑行距离")
    private Long distance;
}
