package com.jiuji.oa.wuliu.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class TodoListMqVo implements Serializable {
    private static final long serialVersionUID = 8640363645856630904L;

    /**
     * mq消息处理类型 1 生成待办 2 删除待办 3 更新待办
     */
    private Integer mqKind;

    private Integer areaId;
    /**
     * 工号
     */
    private Integer staffId;

    /**
     * 待办类型
     */
    private Integer type;
    /**
     * 业务单号
     */
    private String businessNo;

    /**
     * 待办内容
     */
    private String msg;

    /**
     * 待办跳转链接
     */
    private String link;

    /**
     * 完成标识
     */
    private Boolean finishFlag;

    /**
     * 待办标签
     */
    private String label;

    /**
     * 是否为固定项
     */
    private Boolean fixedFlag;

    /**
     * 前端排序
     */
    private Integer fortSort;

    /**
     * 后端排序
     */
    private Integer afterSort;

    /**
     * 颜色
     */
    private String color;

    /**
     * 更新工号
     */
    private Integer updateStaffId;
}
