package com.jiuji.oa.procurementStatistics.vo;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.oa.procurementStatistics.enums.ShowStatisticsEnumVO;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ProcurementVO {

    /**
     * 分页数据信息
     */
    private Page<ProcurementStatisticsVO> pageInfo;

    /**
     * 合计信息
     */
    private TotalInfoVO totalInfoVO;

    /**
     * 表头
     */
    private List<ShowStatisticsEnumVO> allPrintingEnum;




}
