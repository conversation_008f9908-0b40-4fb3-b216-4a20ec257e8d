package com.jiuji.oa.procurementStatistics.enums;


import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 统计方案
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum GaugeOutfitEnum implements CodeMessageEnumInterface {

    tenantId("tenantId", "租户ID",99999),
    channelId("channelId", "渠道ID",99999),
    channelName("channelName", "渠道名称",99999),
    totalInStorageCount("totalInStorageCount", "总入库数量",99999),
    totalPurchasePrice("totalPurchasePrice", "总采购价",99999),
    channel("channel", "渠道",5),
    area("area", "地区",7),
    brandName("brandName", "品牌",8),
    deptUpperName("deptUpperName", "大区",9),
    categoryName("categoryName", "分类",10),
    topCategroyName("topCategroyName", "顶级分类",11),
    productName("productName", "商品名称",14),
    productColor("productColor", "商品规格",15),
    inStorageCount("inStorageCount", "入库数量",20),
    returnCount("returnCount", "退货数量",30),
    avgPurchasePrice("avgPurchasePrice", "采购均价",40),
    purchasePrice("purchasePrice", "采购进价",50),
    statisticalPrice("statisticalPrice", "统计价",60),
    inStorageCountProportion("inStorageCountProportion", "入库数量占比(%)",70),
    purchasePriceProportion("purchasePriceProportion", "进价金额占比(%)",80);

    private final String code;

    private final String message;

    private final Integer order;


    /**
     * 按照门店
     */
    private static final List<String> CATEGORY_REGION_LIST = Arrays.asList(GaugeOutfitEnum.brandName.getCode(),GaugeOutfitEnum.deptUpperName.getCode(),
            GaugeOutfitEnum.inStorageCount.getCode(), GaugeOutfitEnum.returnCount.getCode(),
            GaugeOutfitEnum.avgPurchasePrice.getCode(), GaugeOutfitEnum.purchasePrice.getCode(),
            GaugeOutfitEnum.statisticalPrice.getCode(), GaugeOutfitEnum.inStorageCountProportion.getCode(),
            GaugeOutfitEnum.purchasePriceProportion.getCode());


    /**
     * 按照门店
     */
    private static final List<String> CATEGORY_LIST = Arrays.asList(GaugeOutfitEnum.brandName.getCode(), GaugeOutfitEnum.inStorageCount.getCode(),
            GaugeOutfitEnum.returnCount.getCode(),GaugeOutfitEnum.avgPurchasePrice.getCode(),
            GaugeOutfitEnum.purchasePrice.getCode(),GaugeOutfitEnum.statisticalPrice.getCode(),
            GaugeOutfitEnum.inStorageCountProportion.getCode(),GaugeOutfitEnum.purchasePriceProportion.getCode());


    /**
     * 按照门店
     */
    private static final List<String> AREA_LIST = Arrays.asList(GaugeOutfitEnum.area.getCode(), GaugeOutfitEnum.inStorageCount.getCode(),
            GaugeOutfitEnum.returnCount.getCode(),GaugeOutfitEnum.avgPurchasePrice.getCode(),
            GaugeOutfitEnum.purchasePrice.getCode(),GaugeOutfitEnum.statisticalPrice.getCode(),
            GaugeOutfitEnum.inStorageCountProportion.getCode(),GaugeOutfitEnum.purchasePriceProportion.getCode());

    /**
     * 按照门店+商品
     */
    private static final List<String> AREA_PRODUCT_LIST = Arrays.asList(GaugeOutfitEnum.area.getCode(),GaugeOutfitEnum.categoryName.getCode(),
            GaugeOutfitEnum.productName.getCode(),GaugeOutfitEnum.productColor.getCode(),
            GaugeOutfitEnum.inStorageCount.getCode(), GaugeOutfitEnum.returnCount.getCode(),
            GaugeOutfitEnum.avgPurchasePrice.getCode(), GaugeOutfitEnum.purchasePrice.getCode(),
            GaugeOutfitEnum.statisticalPrice.getCode(), GaugeOutfitEnum.inStorageCountProportion.getCode(),
            GaugeOutfitEnum.purchasePriceProportion.getCode());


    /**
     * 按照渠道+门店
     */
    private static final List<String> CHANNEL_AREA_LIST = Arrays.asList(GaugeOutfitEnum.channel.getCode(), GaugeOutfitEnum.area.getCode(),
            GaugeOutfitEnum.inStorageCount.getCode(), GaugeOutfitEnum.returnCount.getCode(),
            GaugeOutfitEnum.avgPurchasePrice.getCode(), GaugeOutfitEnum.purchasePrice.getCode(),
            GaugeOutfitEnum.statisticalPrice.getCode(), GaugeOutfitEnum.inStorageCountProportion.getCode(),
            GaugeOutfitEnum.purchasePriceProportion.getCode());
    /**
     * 按照渠道+商品
     */
    private static final List<String> CHANNEL_PRODUCT_LIST = Arrays.asList(GaugeOutfitEnum.channel.getCode(), GaugeOutfitEnum.categoryName.getCode(),
            GaugeOutfitEnum.productName.getCode(),GaugeOutfitEnum.productColor.getCode(),
            GaugeOutfitEnum.inStorageCount.getCode(), GaugeOutfitEnum.returnCount.getCode(),
            GaugeOutfitEnum.avgPurchasePrice.getCode(), GaugeOutfitEnum.purchasePrice.getCode(),
            GaugeOutfitEnum.statisticalPrice.getCode(), GaugeOutfitEnum.inStorageCountProportion.getCode(),
            GaugeOutfitEnum.purchasePriceProportion.getCode());
    /**
     * 按照商品分类(顶级)
     */
    private static final List<String> CATEGORY_TOP_LEVEL_LIST = Arrays.asList(GaugeOutfitEnum.topCategroyName.getCode(), GaugeOutfitEnum.inStorageCount.getCode(),
            GaugeOutfitEnum.returnCount.getCode(),GaugeOutfitEnum.avgPurchasePrice.getCode(),
            GaugeOutfitEnum.purchasePrice.getCode(),GaugeOutfitEnum.statisticalPrice.getCode(),
            GaugeOutfitEnum.inStorageCountProportion.getCode(),GaugeOutfitEnum.purchasePriceProportion.getCode());
    /**
     * 按照商品分类(末级)
     */
    private static final List<String> CATEGORY_LAST_LEVEL_LIST = Arrays.asList(GaugeOutfitEnum.categoryName.getCode(), GaugeOutfitEnum.inStorageCount.getCode(),
            GaugeOutfitEnum.returnCount.getCode(),GaugeOutfitEnum.avgPurchasePrice.getCode(),
            GaugeOutfitEnum.purchasePrice.getCode(),GaugeOutfitEnum.statisticalPrice.getCode(),
            GaugeOutfitEnum.inStorageCountProportion.getCode(),GaugeOutfitEnum.purchasePriceProportion.getCode());
    /**
     * 按照渠道
     */
    private static final List<String> CHANNEL_LIST = Arrays.asList(GaugeOutfitEnum.channel.getCode(), GaugeOutfitEnum.inStorageCount.getCode(),
            GaugeOutfitEnum.returnCount.getCode(),GaugeOutfitEnum.avgPurchasePrice.getCode(),
            GaugeOutfitEnum.purchasePrice.getCode(),GaugeOutfitEnum.statisticalPrice.getCode(),
            GaugeOutfitEnum.inStorageCountProportion.getCode(),GaugeOutfitEnum.purchasePriceProportion.getCode());

    public static String getMessage(String code) {
        for (GaugeOutfitEnum typeEnum : values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum.getMessage();
            }
        }
        return null;
    }

    /**
     * 将所有的枚举转换成list
     *
     * @return
     */
    public static List<ShowStatisticsEnumVO> getAllPrintingEnum(Integer statisticalScheme) {
        HashMap<Integer, List<String>> map = new HashMap<>();
        map.put(StatisticalPlanEnum.CATEGORY_TOP_LEVEL.getCode(),CATEGORY_TOP_LEVEL_LIST);
        map.put(StatisticalPlanEnum.CATEGORY_LAST_LEVEL.getCode(),CATEGORY_LAST_LEVEL_LIST);
        map.put(StatisticalPlanEnum.CHANNEL.getCode(),CHANNEL_LIST);
        map.put(StatisticalPlanEnum.CHANNEL_PRODUCT.getCode(),CHANNEL_PRODUCT_LIST);
        map.put(StatisticalPlanEnum.CHANNEL_AREA.getCode(),CHANNEL_AREA_LIST);
        map.put(StatisticalPlanEnum.AREA_PRODUCT.getCode(),AREA_PRODUCT_LIST);
        map.put(StatisticalPlanEnum.AREA.getCode(),AREA_LIST);
        map.put(StatisticalPlanEnum.CATEGORY.getCode(),CATEGORY_LIST);
        map.put(StatisticalPlanEnum.CATEGORY_REGION.getCode(),CATEGORY_REGION_LIST);
        List<String> gaugeOutfitList = map.get(statisticalScheme);
        GaugeOutfitEnum[] array = GaugeOutfitEnum.values();
        List<ShowStatisticsEnumVO> arrayList = new ArrayList<>();
        for (GaugeOutfitEnum t : array) {
            if(gaugeOutfitList.contains(t.getCode())){
                ShowStatisticsEnumVO showPrintingEnumVO = new ShowStatisticsEnumVO()
                        .setDataIndex(t.getCode())
                        .setOrder(t.getOrder())
                        .setTitle(t.getMessage());
                arrayList.add(showPrintingEnumVO);
            }
        }
        if(CollectionUtils.isEmpty(arrayList)){
            return arrayList;
        }
        return arrayList.stream().sorted(Comparator.comparing(ShowStatisticsEnumVO::getOrder)).collect(Collectors.toList());
    }
}
