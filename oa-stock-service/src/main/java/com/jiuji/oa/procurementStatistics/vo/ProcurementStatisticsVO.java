package com.jiuji.oa.procurementStatistics.vo;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ProcurementStatisticsVO {
    /**
     * 租户ID 10086
     */
    private Integer tenantId;
    /**
     * 电脑办公 顶级分类
     */
    private String topCategroyName;
    /**
     * 分类
     */
    private String categoryName;
    /**
     * 渠道ID
     */
    private Integer channelId;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 渠道 channelName+(channelId)
     */
    private String channel;

    /**
     * 商品名称
     */
    private String productName;
    /**
     * 商品规格
     */
    private String productColor;
    /**
     * 地区
     */
    private String area;
    /**
     * 品牌
     */
    private String brandName;
    /**
     * 大区
     */
    private String deptUpperName;
    /**
     * 入库数量
     */
    private String inStorageCount;
    /**
     * 退货数量
     */
    private Integer returnCount;
    /**
     * 采购均价
     */
    private String avgPurchasePrice;
    /**
     * 采购进价
     */
    private String purchasePrice;
    /**
     * 统计价
     */
    private String statisticalPrice;
    /**
     * 总入库数量
     */
    private String totalInStorageCount;
    /**
     * 总采购价
     */
    private String totalPurchasePrice;
    /**
     * 入库数量占比  (inStorageCount/totalInStorageCount)
     */
    private String inStorageCountProportion;
    /**
     * 进价金额占比 (purchasePrice/totalPurchasePrice)
     */
    private String purchasePriceProportion;
}
