package com.jiuji.oa.procurementStatistics.vo;

import com.jiuji.oa.procurementStatistics.enums.IsPrototypeEnum;
import com.jiuji.oa.procurementStatistics.enums.StatisticalPlanEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class DataSourceReq {

    private Integer tenantId;
    private String startTime;
    private String endTime;
    /**
     * @see StatisticalPlanEnum
     */
    private Integer statisticalScheme;
    private List<Integer> areaIds;
    private List<Integer> cids;
    private List<Integer> brandIds;
    private Integer channelId;
    private Integer skuId;
    private String productName;
    private Integer productId;
    /**
     * @see IsPrototypeEnum
     */
    private Integer isMould;
    private Integer current;
    private Integer size;
}
