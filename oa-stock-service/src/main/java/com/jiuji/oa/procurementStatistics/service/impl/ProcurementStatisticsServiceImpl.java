package com.jiuji.oa.procurementStatistics.service.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.oa.baozun.common.util.StringUtils;
import com.jiuji.oa.nc.common.enums.XtenantEnum;
import com.jiuji.oa.nc.common.exception.CustomizeException;
import com.jiuji.oa.procurementStatistics.enums.ShowStatisticsEnumVO;
import com.jiuji.oa.procurementStatistics.service.ProcurementStatisticsService;
import com.jiuji.oa.procurementStatistics.vo.DataSourceReq;
import com.jiuji.oa.procurementStatistics.vo.ProcurementStatisticsVO;
import com.jiuji.oa.procurementStatistics.vo.ProcurementVO;
import com.jiuji.oa.procurementStatistics.vo.TotalInfoVO;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.constants.NumberConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@DS("ch999oanew")
@Service
public class ProcurementStatisticsServiceImpl implements ProcurementStatisticsService {



    private static final String XSERVICE_NAME_HEADER = "xservicename";
    /**
     * 获取数据源
     *
     * @param dataSourceReq
     * @return
     */
    @Override
    public Page selectPageInfo(DataSourceReq dataSourceReq, TotalInfoVO totalInfoVO) {
        String domainName ;
        if(XtenantEnum.isSaasXtenant()){
            domainName="https://moa.dev.9ji.com";
        } else {
            domainName="https://moa.9ji.com";
        }
        String md5 = MD5.create().digestHex(LocalDateTimeUtil.format(LocalDateTime.now(), "yyyy-MM-dd"));
        String url = domainName + "/cloudapi_nc/data-service/saas/stock/bigStockPurchase/v1";
        String str = HttpUtil.createPost(url)
                .header(XSERVICE_NAME_HEADER, "data-service")
                .header("sign", md5)
                .body(JSONUtil.toJsonStr(dataSourceReq))
                .execute().body();
        log.warn("大件采购记录调用研发组接口获取结果：{},传入参数：{}", JSONUtil.toJsonStr(str), JSONUtil.toJsonStr(dataSourceReq));
        R result = JSONUtil.toBean(str, R.class);
        int code = result.getCode();
        if (code != NumberConstant.ZERO) {
            throw new CustomizeException(Optional.ofNullable(result.getMsg()).orElse(result.getUserMsg()));
        }
        TotalInfoVO totalInfoVOSource = JSONUtil.toBean((JSONObject) result.getData(), TotalInfoVO.class);
        totalInfoVOSource.setTotalAvgPurchasePrice(thousandthPercentile(totalInfoVOSource.getTotalAvgPurchasePrice()))
                .setTotalStatisticalPrice(thousandthPercentile(totalInfoVOSource.getTotalStatisticalPrice()))
                .setTotalPurchasePrice(thousandthPercentile(totalInfoVOSource.getTotalPurchasePrice()));
        BeanUtils.copyProperties(totalInfoVOSource,totalInfoVO);
        return JSONUtil.toBean((JSONObject) result.getData(), Page.class);
    }

    /**
     * 数据处理
     *
     * @param page
     */
    @Override
    public void handlePageInfo(Page<ProcurementStatisticsVO> page) {
        List<ProcurementStatisticsVO> records = page.getRecords();
        List<ProcurementStatisticsVO> recordsNew = new ArrayList<>();
        if(CollectionUtils.isEmpty(records)){
            return;
        }
        for (Object procurementStatisticsVO : records) {
            ProcurementStatisticsVO item = JSONUtil.toBean(JSONUtil.toJsonStr(procurementStatisticsVO), ProcurementStatisticsVO.class);
            //处理channel字段
            item.setChannel(item.getChannelName() + "(" + item.getChannelId() + ")");
            //处理需要转换成占比的字段
            item.setInStorageCountProportion(calculateTheProportion(item.getInStorageCount(), item.getTotalInStorageCount()))
                    .setPurchasePriceProportion(calculateTheProportion(item.getStatisticalPrice(), item.getTotalPurchasePrice()));
            //处理需要转换成千分位的字段
            item.setTotalPurchasePrice(thousandthPercentile(item.getTotalPurchasePrice()))
                    .setPurchasePrice(thousandthPercentile(item.getPurchasePrice()))
                    .setAvgPurchasePrice(thousandthPercentile(item.getAvgPurchasePrice()))
                    .setStatisticalPrice(thousandthPercentile(item.getStatisticalPrice()));
            recordsNew.add(item);
        }
        page.setRecords(recordsNew);
    }

    @Override
    public List<Map<String, Object>> createMapData(ProcurementVO procurementVO) {
        List<Map<String, Object>> list = new ArrayList<>();
        List<ShowStatisticsEnumVO> allPrintingEnum = procurementVO.getAllPrintingEnum();
        Page<ProcurementStatisticsVO> pageInfo = Optional.ofNullable(procurementVO.getPageInfo()).orElse(new Page<>());
        List<ProcurementStatisticsVO> records = pageInfo.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            throw new CustomizeException("导出数据为空");
        }
        //放入数据的key
        records.forEach(item -> {
            HashMap<String, Object> map = new HashMap<>();
            allPrintingEnum.forEach(obj -> {
                //获取key的数据
                String key = obj.getDataIndex();
                //获取value的数据
                Object value = getFieldByName(item, key);
                map.put(key, value);

            });
            list.add(map);
        });
        return list;
    }

    /**
     * 根据字段名获取字段值
     *
     * @param object
     * @param fieldName
     * @return
     */
    private static Object getFieldByName(Object object, String fieldName) {
        try {
            // 获取对象的 Class 对象
            Class<?> cls = object.getClass();
            // 获取字段
            Field field = cls.getDeclaredField(fieldName);
            // 设置字段可访问
            field.setAccessible(true);
            // 获取字段值
            return field.get(object);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            log.error("反射获取字段异常", e);
            return "";
        }
    }


    /**
     * 千分转换
     *
     * @param price
     * @return
     */
    private String thousandthPercentile(String price) {
        if(StringUtils.isEmpty(price)){
            return "";
        }
        BigDecimal number = new BigDecimal(price);
        // 创建 NumberFormat 实例
        NumberFormat formatter = new DecimalFormat("#,##0.00");
        // 将 BigDecimal 格式化为千分位并保留两位小数
        return formatter.format(number) ;
    }

    /**
     * 计算占比
     *
     * @param numerator   分子
     * @param denominator 分母
     * @return
     */
    private String calculateTheProportion(String numerator, String denominator) {
        BigDecimal numeratorNu = new BigDecimal(numerator);
        BigDecimal numeratorDe = new BigDecimal(denominator);
        return numeratorNu.divide(numeratorDe, NumberConstant.FOUR, RoundingMode.HALF_UP).toString();
    }


}
