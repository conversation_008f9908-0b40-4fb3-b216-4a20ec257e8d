package com.jiuji.oa.procurementStatistics.enums;

import com.jiuji.oa.nc.abnormal.vo.ShowPrintingEnumVO;
import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 统计方案
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum IsPrototypeEnum implements CodeMessageEnumInterface {

    NO(0, "否"),
    YES(1, "是");


    private final Integer code;
    private final String message;

    public static String getMessage(Integer code) {
        for (IsPrototypeEnum typeEnum : values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum.getMessage();
            }
        }
        return null;
    }

    /**
     * 将所有的枚举转换成list
     *
     * @return
     */
    public static List<ShowPrintingEnumVO> getAllPrintingEnum() {
        IsPrototypeEnum[] array = IsPrototypeEnum.values();
        List<ShowPrintingEnumVO> arrayList = new ArrayList<>();
        for (IsPrototypeEnum t : array) {
            ShowPrintingEnumVO showPrintingEnumVO = new ShowPrintingEnumVO()
                    .setLabel(t.getMessage())
                    .setValue(t.getCode());
            arrayList.add(showPrintingEnumVO);
        }
        return arrayList;
    }
}
