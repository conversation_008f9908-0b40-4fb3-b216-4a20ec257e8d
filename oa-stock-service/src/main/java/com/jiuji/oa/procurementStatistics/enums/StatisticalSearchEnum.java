package com.jiuji.oa.procurementStatistics.enums;

import com.jiuji.oa.nc.abnormal.vo.ShowPrintingEnumVO;
import com.jiuji.oa.nc.common.exception.CustomizeException;
import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> liu ming
 * @date 2021/6/4 18:56
 */
@AllArgsConstructor
@Getter
public enum StatisticalSearchEnum implements CodeMessageEnumInterface {
    /**
     * 查询选项枚举
     **/
    PRODUCT_NAME(1, "商品名称"),
    PPID(2, "sku"),
    PRODUCTID(3, "商品ID"),

    ;

    private final Integer code;

    private final String message;

    /**
     * 将所有的枚举转换成list
     *
     * @return
     */
    public static List<ShowPrintingEnumVO> getAllPrintingEnum() {
        StatisticalSearchEnum[] array = StatisticalSearchEnum.values();
        List<ShowPrintingEnumVO> arrayList = new ArrayList<>();
        for (StatisticalSearchEnum t : array) {
            ShowPrintingEnumVO showPrintingEnumVO = new ShowPrintingEnumVO()
                    .setLabel(t.getMessage())
                    .setValue(t.getCode());
            arrayList.add(showPrintingEnumVO);
        }
        return arrayList;
    }

    /**
     * 搜索内容仅为熟悉类型
     *
     * @param searchType  搜索类型
     * @param searchValue 搜索内容
     * @throws IllegalArgumentException 错误的参数
     */
    public static void onlyNumber(Integer searchType, String searchValue) {
        if (StringUtils.isEmpty(searchValue)) {
            return;
        }
        StatisticalSearchEnum[] onlyNumberTypes = {PPID,PRODUCTID};
        for (StatisticalSearchEnum searchEnum : onlyNumberTypes) {
            if (searchEnum.getCode().equals(searchType)) {
                try {
                    Integer.valueOf(searchValue);
                } catch (NumberFormatException e) {
                    throw new CustomizeException("请输入正确的"+ searchEnum.getMessage());
                }
            }
        }
    }

}
