package com.jiuji.oa.procurementStatistics.enums;

import com.jiuji.oa.nc.abnormal.vo.ShowPrintingEnumVO;
import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 统计方案
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum StatisticalPlanEnum  implements CodeMessageEnumInterface {

    CATEGORY_TOP_LEVEL(1, "商品分类(顶级)"),
    CATEGORY_LAST_LEVEL(2, "商品分类(末级)"),
    CHANNEL(3, "渠道"),
    CHANNEL_PRODUCT(4, "渠道+商品"),
    CHANNEL_AREA(5, "渠道+门店"),
    AREA_PRODUCT(6, "门店+商品"),
    AREA(7, "门店"),
    CATEGORY(8, "品牌"),
    CATEGORY_REGION(9, "品牌+大区");


    private final Integer code;
    private final String message;

    public static String getMessage(Integer code) {
        for (StatisticalPlanEnum typeEnum : values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum.getMessage();
            }
        }
        return null;
    }

    /**
     * 将所有的枚举转换成list
     *
     * @return
     */
    public static List<ShowPrintingEnumVO> getAllPrintingEnum() {
        StatisticalPlanEnum[] array = StatisticalPlanEnum.values();
        List<ShowPrintingEnumVO> arrayList = new ArrayList<>();
        for (StatisticalPlanEnum t : array) {
            ShowPrintingEnumVO showPrintingEnumVO = new ShowPrintingEnumVO()
                    .setLabel(t.getMessage())
                    .setValue(t.getCode());
            arrayList.add(showPrintingEnumVO);
        }
        return arrayList;
    }
}
