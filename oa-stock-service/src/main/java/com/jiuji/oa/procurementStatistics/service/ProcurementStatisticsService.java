package com.jiuji.oa.procurementStatistics.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.oa.procurementStatistics.vo.DataSourceReq;
import com.jiuji.oa.procurementStatistics.vo.ProcurementStatisticsVO;
import com.jiuji.oa.procurementStatistics.vo.ProcurementVO;
import com.jiuji.oa.procurementStatistics.vo.TotalInfoVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface ProcurementStatisticsService {


    /**
     * 数据查询
     * @param dataSourceReq
     * @return
     */
    Page<ProcurementStatisticsVO> selectPageInfo (DataSourceReq dataSourceReq, TotalInfoVO totalInfoVO);

    /**
     * 数据处理
     * @param page
     */
    void handlePageInfo (Page<ProcurementStatisticsVO> page);

    /**
     * 获取map数据
     * @param procurementVO
     * @return
     */
    List<Map<String, Object>> createMapData(ProcurementVO procurementVO);

}
