package com.jiuji.oa.procurementStatistics.controller;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.cloud.org.vo.enums.RoleTermModuleEnum;
import com.jiuji.oa.nc.abnormal.vo.ShowPrintingEnumVO;
import com.jiuji.oa.nc.common.bo.OaUserBO;
import com.jiuji.oa.nc.common.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.nc.common.exception.CustomizeException;
import com.jiuji.oa.nc.common.util.ExcelUtils;
import com.jiuji.oa.nc.dict.service.ISysConfigService;
import com.jiuji.oa.nc.user.po.Areainfo;
import com.jiuji.oa.nc.user.service.IAreaInfoService;
import com.jiuji.oa.procurementStatistics.enums.*;
import com.jiuji.oa.procurementStatistics.service.ProcurementStatisticsService;
import com.jiuji.oa.procurementStatistics.vo.DataSourceReq;
import com.jiuji.oa.procurementStatistics.vo.ProcurementStatisticsVO;
import com.jiuji.oa.procurementStatistics.vo.ProcurementVO;
import com.jiuji.oa.procurementStatistics.vo.TotalInfoVO;
import com.jiuji.oa.stock.common.util.BusinessUtil;
import com.jiuji.oa.stock.common.vo.CheckDataViewScopeReq;
import com.jiuji.oa.wuliu.enums.SubCheckEnum;
import com.jiuji.tc.common.vo.R;
import io.swagger.annotations.ApiOperation;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/procurementStatistics")
public class ProcurementStatisticsController {

    @Resource
    private ProcurementStatisticsService procurementStatisticsService;
    @Resource
    private ISysConfigService sysConfigService;
    @Resource
    private IAreaInfoService areaInfoService;
    @Resource
    private AbstractCurrentRequestComponent currentRequestComponent;


    /**
     * 获取到盘点重构的枚举
     * @return
     */
    @ApiOperation(value = "获取大件采购统计枚举")
    @GetMapping("/getEnum")
    public R<Map<String, List<ShowPrintingEnumVO>>> getEnum() {
        List<ShowPrintingEnumVO> statisticalPlanEnum = StatisticalPlanEnum.getAllPrintingEnum();
        List<ShowPrintingEnumVO> statisticalSearchEnum = StatisticalSearchEnum.getAllPrintingEnum();
        List<ShowPrintingEnumVO> isPrototypeEnum = IsPrototypeEnum.getAllPrintingEnum();
        HashMap<String, List<ShowPrintingEnumVO>> map = new HashMap<>(4);
        map.put("statisticalPlanEnum", statisticalPlanEnum);
        map.put("statisticalSearchEnum", statisticalSearchEnum);
        map.put("isPrototypeEnum", isPrototypeEnum);
        return R.success(map);
    }


    /**
     * 获取大件采购单统计信息
     * @return
     */
    @ApiOperation(value = "获取大件采购统计枚举")
    @PostMapping("/selectProcurement")
    public R<ProcurementVO> selectProcurement(@RequestBody DataSourceReq dataSourceReq) {

        return R.success(getProcurementVO(dataSourceReq));
    }



    @ApiOperation(value = "导出", notes = "导出")
    @PostMapping("/export")
    public void export(@RequestBody DataSourceReq dataSourceReq, HttpServletResponse response) throws IOException {
        dataSourceReq.setSize(50000).setCurrent(1);
        ProcurementVO procurementVO = getProcurementVO(dataSourceReq);
        List<ShowStatisticsEnumVO> allPrintingEnum = procurementVO.getAllPrintingEnum();
        ExcelWriter writer = ExcelUtil.getWriter(true);
        //自定义标题别名
        allPrintingEnum.forEach(item-> writer.addHeaderAlias(item.getDataIndex(), item.getTitle()));
        List<Map<String, Object>> mapData = procurementStatisticsService.createMapData(procurementVO);
        writer.write(mapData, true);
        // response为HttpServletResponse对象
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        // feedbackInfo.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
        String filename = ExcelUtils.getExportFileName("大件采购列表");
        filename = URLEncoder.encode(filename, "UTF-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + filename);
        ServletOutputStream out = response.getOutputStream();
        writer.flush(out, true);
        // 关闭writer，释放内存
        writer.close();
        //此处记得关闭输出Servlet流
        IoUtil.close(out);
    }

    /**
     * 获取数据
     * @param dataSourceReq
     * @return
     */
    private ProcurementVO getProcurementVO(DataSourceReq dataSourceReq){
        //角色数据查询
        R dataViewRes = BusinessUtil.checkDataViewScope(CheckDataViewScopeReq.builder().moduleEnum(RoleTermModuleEnum.LOGISTICS)
                .getStartTimeFun(() -> DateUtil.parseLocalDateTime(dataSourceReq.getStartTime(), DatePattern.NORM_DATETIME_PATTERN))
                .getEndTimeFun(() -> DateUtil.parseLocalDateTime(dataSourceReq.getEndTime(),DatePattern.NORM_DATETIME_PATTERN))
                .setStartTimeFun(startTime -> dataSourceReq.setStartTime(DateUtil.format(startTime, DatePattern.NORM_DATETIME_PATTERN)))
                .setEndTimeFun(endTime -> dataSourceReq.setEndTime(DateUtil.format(endTime, DatePattern.NORM_DATETIME_PATTERN)))
                .build(), null);
        if (!dataViewRes.isSuccess()) {
            throw new CustomizeException(dataViewRes.getUserMsg());
        }
        ProcurementVO procurementVO = new ProcurementVO();
        TotalInfoVO totalInfoVO = new TotalInfoVO();

        List<Integer> areaIds = dataSourceReq.getAreaIds();
        //授权隔离标识（订单、门店、库存等 所有地方都需要隔离）
        if(CollectionUtils.isEmpty(areaIds) && sysConfigService.isAuthorization() ){
            OaUserBO oaUserBO = Optional.ofNullable(currentRequestComponent.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("登录信息超时，请重新登录"));
//            List<Areainfo> areainfoList = areaInfoService.lambdaQuery().eq(Areainfo::getAuthorizeid, oaUserBO.getAuthorizeId())
//                    .eq(Areainfo::getIspass, Boolean.TRUE)
//                    .list();
            List<Areainfo> areainfoList = areaInfoService.selectAreaInfoByAuthorizeId(oaUserBO.getAuthorizeId());
            if(CollectionUtils.isEmpty(areainfoList)){
                throw new CustomizeException("该授权下门店为空");
            }
            dataSourceReq.setAreaIds(areainfoList.stream().map(Areainfo::getId).collect(Collectors.toList()));
        }

        //数据获取
        Page<ProcurementStatisticsVO> data = procurementStatisticsService.selectPageInfo(dataSourceReq,totalInfoVO);
        //数据处理
        procurementStatisticsService.handlePageInfo(data);
        //表头查询
        List<ShowStatisticsEnumVO> allPrintingEnum = GaugeOutfitEnum.getAllPrintingEnum(dataSourceReq.getStatisticalScheme());
        procurementVO.setPageInfo(data)
                .setTotalInfoVO(totalInfoVO)
                .setAllPrintingEnum(allPrintingEnum);
        return procurementVO;
    }
}
