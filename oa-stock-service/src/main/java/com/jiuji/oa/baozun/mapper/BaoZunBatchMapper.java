package com.jiuji.oa.baozun.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.baozun.dto.SubFromSourceDTO;
import com.jiuji.oa.baozun.entity.BaoZunBatchEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 宝尊出入库同步批量 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-07
 */
public interface BaoZunBatchMapper extends BaseMapper<BaoZunBatchEntity> {

    List<Long> getRetryList();

    void updateState(@Param("idList") List<Long> idList);

    @DS("ch999oanew")
    SubFromSourceDTO judgeRetail(Integer businessId);

}
