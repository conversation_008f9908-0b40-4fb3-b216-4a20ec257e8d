package com.jiuji.oa.baozun.dto.req.flow;

import java.io.Serializable;
import lombok.Data;

import java.util.List;

/**
 * 订单流水上传请求
 *
 * <AUTHOR>
 * @date 2021/09/02
 */
@Data
public class FlowReqDTO implements Serializable {

    private static final long serialVersionUID = 201271351104938424L;
    /**
     * 默认值，ERP请传输"routeIosp_om"
     */
    private String sourceMarkCode;
    /**
     * 流水数组
     */
    private List<InvChangeQtyCommand> invChangeQtyCommand;
}
