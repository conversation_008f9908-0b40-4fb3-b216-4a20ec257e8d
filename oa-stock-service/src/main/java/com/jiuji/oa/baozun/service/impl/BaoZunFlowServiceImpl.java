package com.jiuji.oa.baozun.service.impl;

import cn.hutool.core.text.StrPool;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.enums.SqlMethod;
import com.jiuji.oa.baozun.dto.req.flow.StockFlowVo;
import com.jiuji.oa.baozun.entity.BaoZunFlowEntity;
import com.jiuji.oa.baozun.mapper.BaoZunFlowMapper;
import com.jiuji.oa.baozun.service.IBaoZunFlowService;
import com.jiuji.oa.nc.dict.enums.ConfigEnum;
import com.jiuji.oa.nc.dict.service.ISysConfigService;
import com.jiuji.oa.stock.common.service.impl.BatchAffectRowsServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 宝尊出入库同步流水表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-07
 */
@Service
@DS("oa_nc")
public class BaoZunFlowServiceImpl extends BatchAffectRowsServiceImpl<BaoZunFlowMapper, BaoZunFlowEntity> implements
        IBaoZunFlowService {


    @Resource
    private ISysConfigService sysConfigService;

    /**
     * 查询需要全量同步的配件记录数
     *
     * @return
     * @Param fkTenantId
     */
    @Override
    @DS("ch999oanew")
    public Integer countAccessoryAreaPpid(Integer fkTenantId) {
        return this.getBaseMapper().countAccessoryAreaPpid(fkTenantId);
    }

    /**
     * 查询需要全量同步的大件记录数
     *
     * @param fkTenantId
     * @return
     */
    @Override
    @DS("ch999oanew")
    public Integer countMobileAreaPpid(Integer fkTenantId) {
        return this.getBaseMapper().countMobileAreaPpid(fkTenantId);
    }

    /**
     * 查询小件库存变动流水
     *
     * @param fkTenantId
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    @DS("ch999oanew")
    public List<StockFlowVo> searchAccessoryStockFlow(Integer fkTenantId, LocalDateTime startTime, LocalDateTime endTime) {
        return this.getBaseMapper().queryAccessoryStockFlow(fkTenantId, startTime, endTime);
    }

    /**
     * 查询当前小件库存
     *
     * @param fkTenantId
     * @param start
     * @param end
     * @return
     */
    @Override
    @DS("ch999oanew")
    public List<StockFlowVo> searchAccessoryStock(Integer fkTenantId, Integer start, Integer end) {
        return this.getBaseMapper().queryAccessoryStock(fkTenantId, start, end);
    }

    /**
     * 查询当前大件库存
     *
     * @param start
     * @param end
     * @return
     * @Param fkTenantId
     */
    @Override
    @DS("ch999oanew")
    public List<StockFlowVo> searchMobileStock(Integer fkTenantId, Integer start, Integer end) {
        List<StockFlowVo> result = new ArrayList<>();
        String inSourceIds = sysConfigService.getValueByCode(ConfigEnum.BAOZUN_CHANNEL);
        List<Long> inSourceIdList = org.springframework.util.StringUtils.commaDelimitedListToSet(inSourceIds).stream().map(Long::valueOf)
                .collect(Collectors.toList());
        List<StockFlowVo> stockFlowVos = this.getBaseMapper().queryMobileStock(inSourceIdList,fkTenantId, start, end);
        if (CollectionUtils.isNotEmpty(stockFlowVos)) {
            //查询大于0的串号
            List<StockFlowVo> existStockFlowVo = stockFlowVos.stream().filter(x ->
                    x.getCount() >= 0
            ).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(existStockFlowVo)) {
                for (StockFlowVo x : existStockFlowVo) {
                    List<StockFlowVo> mobileStockSnList = this.getBaseMapper().queryMobileStockSn(inSourceIdList,x.getAreaId(), x.getPpid());

                    List<StockFlowVo> newSnList = mobileStockSnList.stream()
                            .filter(z -> !z.getMouldFlag() && !z.getXcFlag()).collect(Collectors.toList());
                    List<StockFlowVo> mouldSnList = mobileStockSnList.stream()
                            .filter(StockFlowVo::getMouldFlag).collect(Collectors.toList());
                    List<StockFlowVo> xcSnList = mobileStockSnList.stream()
                            .filter(z -> !z.getMouldFlag() && z.getXcFlag()).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(newSnList)) {
                        StockFlowVo newSf = new StockFlowVo();
                        BeanUtils.copyProperties(x, newSf);
                        newSf.setMouldFlag(false);
                        newSf.setXcFlag(false);
                        String sn = StringUtils.join(newSnList.stream().map(StockFlowVo::getSn)
                                .collect(Collectors.toList()), StrPool.C_COMMA);
                        newSf.setSn(sn);
                        newSf.setCount((long) newSnList.size());
                        result.add(newSf);
                        mobileStockSnList.removeAll(newSnList);
                    }else {
                        StockFlowVo newSf = new StockFlowVo();
                        BeanUtils.copyProperties(x, newSf);
                        newSf.setMouldFlag(false);
                        newSf.setXcFlag(false);
                        newSf.setSn("");
                        newSf.setCount(0L);
                        result.add(newSf);
                    }

                    if (CollectionUtils.isNotEmpty(mouldSnList)) {
                        StockFlowVo mouldSf = new StockFlowVo();
                        BeanUtils.copyProperties(x, mouldSf);
                        mouldSf.setMouldFlag(true);
                        mouldSf.setXcFlag(false);
                        String mouldsn = StringUtils.join(mouldSnList.stream().map(StockFlowVo::getSn)
                                .collect(Collectors.toList()), StrPool.C_COMMA);
                        mouldSf.setSn(mouldsn);
                        mouldSf.setCount((long) mouldSnList.size());
                        result.add(mouldSf);
                        mobileStockSnList.removeAll(mouldSnList);
                    }else {
                        StockFlowVo mouldSf = new StockFlowVo();
                        BeanUtils.copyProperties(x, mouldSf);
                        mouldSf.setMouldFlag(true);
                        mouldSf.setXcFlag(false);
                        mouldSf.setSn("");
                        mouldSf.setCount(0L);
                        result.add(mouldSf);
                        mobileStockSnList.removeAll(mouldSnList);
                    }

                    if (CollectionUtils.isNotEmpty(xcSnList)) {
                        StockFlowVo xcSf = new StockFlowVo();
                        BeanUtils.copyProperties(x, xcSf);
                        xcSf.setMouldFlag(false);
                        xcSf.setXcFlag(true);
                        String xcsn = StringUtils.join(xcSnList.stream().map(StockFlowVo::getSn)
                                .collect(Collectors.toList()), StrPool.C_COMMA);
                        xcSf.setSn(xcsn);
                        xcSf.setCount((long) xcSnList.size());
                        result.add(xcSf);
                    }else {
                        StockFlowVo xcSf = new StockFlowVo();
                        BeanUtils.copyProperties(x, xcSf);
                        xcSf.setMouldFlag(false);
                        xcSf.setXcFlag(true);
                        xcSf.setSn("");
                        xcSf.setCount(0L);
                        result.add(xcSf);
                    }
                }
            }
        }
        return result;
    }

    /**
     * 批量保存返回影响条数
     *
     * @param list
     * @return
     */
    @Override
    public int saveBatchWithAffectRows(List<BaoZunFlowEntity> list) {
        String sqlStatement = getSqlStatement(SqlMethod.INSERT_ONE);
        return myExecuteBatch(list, list.size(), (sqlSession, entity) -> sqlSession.insert(sqlStatement, entity));
    }

    /**
     * 列出库存变动流水汇总
     *
     * @param batchId
     * @return
     */
    @Override
    public List<BaoZunFlowEntity> listBaoZunChangeQty(Long batchId) {
        return this.lambdaQuery().eq(BaoZunFlowEntity::getBatchId, batchId).ne(BaoZunFlowEntity::getCount, 0).list();
    }

    /**
     * 列出当前全量库存
     *
     * @param batchId
     * @return
     */
    @Override
    public List<BaoZunFlowEntity> listBaoZunFullSkuInventories(Long batchId) {
        return this.lambdaQuery().eq(BaoZunFlowEntity::getBatchId, batchId).list();
    }

    /**
     * listRefund
     *
     * @param batchId
     * @return
     */
    @Override
    @DS("ch999oanew")
    public List<BaoZunFlowEntity> listRefund(Integer fkTenantId, Long batchId) {
        return this.getBaseMapper().queryRefund(fkTenantId, batchId);
    }

    @Override
    public void updateBaoZunFlowWithRefund(BaoZunFlowEntity entity) {
        this.getBaseMapper().updateBaoZunFlowWithRefund(entity);
    }


}
