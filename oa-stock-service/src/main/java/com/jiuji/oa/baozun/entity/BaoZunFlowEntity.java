package com.jiuji.oa.baozun.entity;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Map;

/**
 * <p>
 * 宝尊出入库同步流水表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_baozun_flow")
@Slf4j
public class BaoZunFlowEntity extends Model<BaoZunFlowEntity> {

    private static final long serialVersionUID = 2773293583817248630L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 批次ID
     */
    private Long batchId;

    /**
     * 平台Id
     */
    private Integer fkTenantId;

    /**
     * 业务Id
     */
    private Integer businessId;

    /**
     * 业务Id
     */
    private Integer uniqueId;

    /**
     * 唯一标识，MD5生成
     */
    private String uniqueKey;


    /**
     * subId2
     */
    @TableField(exist = false)
    private Integer subId2;

    /**
     * subId3
     */
    @TableField(exist = false)
    private Integer subId3;

    /**
     * 备注
     */
    @TableField(exist = false)
    private String comment;

    /**
     * 门店Id
     */
    private Long areaId;

    /**
     * 门店编码
     */
    private String warehouseCode;

    /**
     * ppriceid
     */
    private Long ppid;

    /**
     * 商品对照表主键
     */
    private String upc;

    /**
     * 库存变动数量
     */
    private Long count;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标志：0-未删除，1-已删除
     */
    @TableLogic
    private Boolean deleteFlag;

    /**
     * 串号
     */
    private String sn;

    private String docType;

    private Integer orderType;

    private Integer invStatusCode = 1;



    /**
     * 类型
     */
    private Integer type;
    /**
     * md5缓存值
     */
    @TableField(exist = false)
    @JSONField(serialize = false, deserialize = false)
    @JsonIgnore
    private transient Map<String, String> businessMd5Cache = Collections.emptyMap();

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    /**
     * 业务数据md5唯一值
     * @return
     */
    public String businessMd5(){
        String template = "{fkTenantId}_{businessId}_{uniqueId}_{areaId}_{warehouseCode}_{ppid}_{count}_{createTime}_{sn}_{docType}_{orderType}_{invStatusCode}_{type}";
        Map<String, Object> beanMap = BeanUtil.beanToMap(this);
        // 将空字符串转换为null
        beanMap.forEach((k, v) -> {
            if (v instanceof String && StrUtil.isBlank((String) v)) {
                beanMap.put(k, null);
            }
        });
        String businessData = StrUtil.format(template, beanMap);
        if(businessMd5Cache.get(businessData) != null){
            return businessMd5Cache.get(businessData);
        }
        log.debug("md5 business data:{}", businessData);
        String md5Hex = DigestUtil.md5Hex(businessData);
        businessMd5Cache = Collections.singletonMap(businessData, md5Hex);
        return md5Hex;
    }

}
