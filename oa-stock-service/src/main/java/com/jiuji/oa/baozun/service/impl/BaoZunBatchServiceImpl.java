package com.jiuji.oa.baozun.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.baozun.entity.BaoZunBatchEntity;
import com.jiuji.oa.baozun.enums.BaoZunBatchStateEnum;
import com.jiuji.oa.baozun.mapper.BaoZunBatchMapper;
import com.jiuji.oa.baozun.service.IBaoZunBatchService;
import com.jiuji.tc.foundation.message.send.service.MessageSendService;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <p>
 * 宝尊出入库同步批量 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-07
 */
@Service
@DS("oa_nc")
public class BaoZunBatchServiceImpl extends ServiceImpl<BaoZunBatchMapper, BaoZunBatchEntity> implements IBaoZunBatchService {

    private final static Integer MAX_TRY_COUNT = 3;
    @Resource
    private MessageSendService messageSendService;

    /**
     * 获取上一次的批处理
     *
     * @param fkTenantId
     * @param batchType
     * @return
     */
    @Override
    @DS("oa_nc")
    public BaoZunBatchEntity getLastBatch(Integer fkTenantId, Integer batchType) {

        return this.lambdaQuery().eq(BaoZunBatchEntity::getFkTenantId, fkTenantId)
                .eq(BaoZunBatchEntity::getBatchType, batchType).last("limit 1")
                .orderByDesc(BaoZunBatchEntity::getId).one();
    }

    /**
     * 保存批处理
     *
     * @param entity
     */
    @Override
    @DS("oa_nc")
    public void saveBaoZunBatch(BaoZunBatchEntity entity) {
        boolean flag = this.save(entity);
        Assert.state(flag, "保存批处理失败");
    }

    /**
     * 更新批处理状态
     *
     * @param batchId
     * @param state
     */
    @Override
    @DS("oa_nc")
    public void updateBatchState(Long batchId, Integer state) {
        boolean flag = this.lambdaUpdate().eq(BaoZunBatchEntity::getId, batchId).set(BaoZunBatchEntity::getState, state).update();
        Assert.state(flag, "更新批处理状态失败");
    }

    @Override
    public boolean addBatchTryCount(Long batchId) {
        BaoZunBatchEntity batch = this.getByBatchId(batchId);
        int retryCount = Objects.isNull(batch.getRetryCount()) ? 0 : batch.getRetryCount();
        int newTryCount = retryCount + 1;
        if (newTryCount < MAX_TRY_COUNT) {
            this.lambdaUpdate().eq(BaoZunBatchEntity::getId, batchId)
                    .set(BaoZunBatchEntity::getRetryCount, newTryCount)
                    .update();
            return true;
        }
        if (newTryCount == MAX_TRY_COUNT) {
            this.lambdaUpdate().eq(BaoZunBatchEntity::getId, batchId)
                    .set(BaoZunBatchEntity::getState, BaoZunBatchStateEnum.ERROR.getCode())
                    .update();
            messageSendService.sendOaMessage(0, "", "13685,8942,13774", "宝尊数据同步批次次数超额：" + batchId);
        }
        return false;
    }

    /**
     * 通过Id获取批次
     *
     * @param batchId
     * @return
     */
    @Override
    @DS("oa_nc")
    public BaoZunBatchEntity getByBatchId(Long batchId) {
        return this.getById(batchId);
    }
}
