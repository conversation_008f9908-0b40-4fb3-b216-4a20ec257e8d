package com.jiuji.oa.baozun.util;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.jiuji.oa.baozun.dto.SubFromSourceDTO;
import com.jiuji.oa.baozun.entity.BaoZunFlowEntity;
import com.jiuji.oa.baozun.enums.BaoZunDocTypeEnum;
import com.jiuji.oa.baozun.mapper.BaoZunBatchMapper;
import com.jiuji.oa.baozun.service.IBaoZunFlowService;
import com.jiuji.oa.nc.dict.utils.ApplicationContextUtil;
import com.jiuji.oa.stock.stockmanage.enums.OrderTypeEnum;
import com.jiuji.oa.stock.stockmanage.enums.StockChangeTypeEnum;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Objects;

/**
 * <p>
 *
 * @description: BaoZunUtils
 * </p>
 * @author: David
 * @create: 2021-09-09 14:04
 */
public final class BaoZunUtils {

    private BaoZunUtils() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * @param id     id
     * @param type   变动类型
     * @param sn     串号
     * @param upc    upc
     * @param areaId 门店id
     * @return
     */
    public static String getDocNo(Long id, Integer businessId, Integer orderType, Integer type, String sn, String upc, Long areaId) {
        if (StockChangeTypeEnum.OCCUPIED.getCode().equals(type)) {
                return String.valueOf(id);
        }
        if (StockChangeTypeEnum.RELEASE.getCode().equals(type)) {
            IBaoZunFlowService baoZunFlowService = ApplicationContextUtil.getBean(IBaoZunFlowService.class);
            BaoZunFlowEntity baoZunFlowEntity = baoZunFlowService.lambdaQuery().eq(BaoZunFlowEntity::getAreaId, areaId)
                    .eq(BaoZunFlowEntity::getSn, sn).eq(BaoZunFlowEntity::getUpc, upc)
                    .eq(BaoZunFlowEntity::getType, StockChangeTypeEnum.OCCUPIED.getCode())
                    .ge(BaoZunFlowEntity::getCreateTime, LocalDateTime.now().minusDays(30L))
                    .orderByDesc(BaoZunFlowEntity::getCreateTime)
                    .list().stream().findFirst().orElse(null);
            if (Objects.isNull(baoZunFlowEntity)){
                return String.valueOf(id);
            }
            return String.valueOf(baoZunFlowEntity.getId());
        }
        if (StockChangeTypeEnum.FLOW.getCode().equals(type)) {
            if (OrderTypeEnum.NOT_RETAIL.getCode().equals(orderType)
                    || OrderTypeEnum.RETAIL.getCode().equals(orderType)) {
                return String.valueOf(businessId);
            }
        }
        return String.valueOf(businessId);
    }



    /**
     *
     *
     * @param count
     * @return
     */
    public static String getDocType(Integer businessId, Integer orderType, Integer type, long count) {
        String docType = "";
        if (StockChangeTypeEnum.FLOW.getCode().equals(type)) {
            if (OrderTypeEnum.SUB.getCode().equals(orderType)) {
                if (count >= 0L) {
                    docType = BaoZunDocTypeEnum.SUB_IN.getMessage();
                    return docType;
                } else {
                    docType = BaoZunDocTypeEnum.SUB_OUT.getMessage();
                    return docType;
                }
            }
            if (OrderTypeEnum.SUB.getCode().equals(orderType)) {
                if (count >= 0L) {
                    docType = BaoZunDocTypeEnum.SUB_IN.getMessage();
                    return docType;
                } else {
                    docType = BaoZunDocTypeEnum.SUB_OUT.getMessage();
                    return docType;
                }
            }
            if (OrderTypeEnum.TRANSFER.getCode().equals(orderType)) {
                if (count >= 0L) {
                    docType = BaoZunDocTypeEnum.TRANSFER_IN.getMessage();
                    return docType;
                } else {
                    docType = BaoZunDocTypeEnum.TRANSFER_OUT.getMessage();
                    return docType;
                }
            }
            if (OrderTypeEnum.ORDER.getCode().equals(orderType)) {
                docType = judgeByBusinessId(businessId,orderType, count);
                return docType;
            }
            if (OrderTypeEnum.LOSE.getCode().equals(orderType)) {
                if (count < 0L) {
                    docType = BaoZunDocTypeEnum.LOSE_OUT.getMessage();
                    return docType;
                }
            }
            if (OrderTypeEnum.RETURN.getCode().equals(orderType)) {
                if (count > 0L) {
                    docType = BaoZunDocTypeEnum.RETURN_IN.getMessage();
                    return docType;
                }else {
                    docType = BaoZunDocTypeEnum.SUB_OUT.getMessage();
                    return docType;
                }
            }
            if (OrderTypeEnum.YJ.getCode().equals(orderType)) {
                docType = BaoZunDocTypeEnum.YJ.getMessage();
                return docType;
            }
            if (OrderTypeEnum.YJJS.getCode().equals(orderType)) {
                docType = BaoZunDocTypeEnum.YJJS.getMessage();
                return docType;
            }
            if (OrderTypeEnum.XC_IN.getCode().equals(orderType)) {
                docType = BaoZunDocTypeEnum.XC_IN.getMessage();
                return docType;
            }
            if (OrderTypeEnum.XC_OUT.getCode().equals(orderType)) {
                docType = BaoZunDocTypeEnum.XC_OUT.getMessage();
                return docType;
            }
            if (OrderTypeEnum.CHANGE_IMEI_OUT.getCode().equals(orderType)) {
                docType = BaoZunDocTypeEnum.CHANGE_IMEI_OUT.getMessage();
                return docType;
            }
            if (OrderTypeEnum.CHANGE_IMEI_IN.getCode().equals(orderType)) {
                docType = BaoZunDocTypeEnum.CHANGE_IMEI_IN.getMessage();
                return docType;
            }
        }
        docType = judgeByBusinessId(businessId,orderType, count);
        return docType;
    }

    private static String judgeByBusinessId(Integer businessId,Integer orderType, long count) {
        String docType = "";
        BaoZunBatchMapper baoZunBatchMapper = ApplicationContextUtil.getBean(BaoZunBatchMapper.class);
        SubFromSourceDTO subFromSource;
        DynamicDataSourceContextHolder.push("oanewWrite");
        subFromSource = baoZunBatchMapper.judgeRetail(businessId);
        DynamicDataSourceContextHolder.clear();

        if (Objects.nonNull(businessId) && Objects.nonNull(subFromSource)) {
            Integer subtype = subFromSource.getSubtype();
            String fromName = subFromSource.getFromName();
            if (Arrays.asList(13, 14).contains(subtype)
                    || (!Arrays.asList(13, 14).contains(subtype) && "团购".equals(fromName))) {
                if (count > 0L) {
                    docType = BaoZunDocTypeEnum.AREA_RETURN_IN_NOT_RETAIL.getMessage();
                } else {
                    docType = BaoZunDocTypeEnum.AREA_SELL_OUT_NOT_RETAIL.getMessage();
                }
                return docType;
            }
            if (!Arrays.asList(13, 14).contains(subtype) && !"团购".equals(fromName)) {
                if (count > 0L) {
                    docType = BaoZunDocTypeEnum.AREA_RETURN_IN_RETAIL.getMessage();
                    return docType;
                } else {
                    docType = BaoZunDocTypeEnum.AREA_SELL_OUT_RETAIL.getMessage();
                    return docType;
                }
            }

        } else {
            if (OrderTypeEnum.NOT_RETAIL.getCode().equals(orderType)) {
                if (count > 0L) {
                    docType = BaoZunDocTypeEnum.AREA_RETURN_IN_NOT_RETAIL.getMessage();
                    return docType;
                }else {
                    docType = BaoZunDocTypeEnum.AREA_SELL_OUT_NOT_RETAIL.getMessage();
                    return docType;
                }
            }
            if (OrderTypeEnum.RETAIL.getCode().equals(orderType)) {
                if (count > 0L) {
                    docType = BaoZunDocTypeEnum.AREA_RETURN_IN_RETAIL.getMessage();
                    return docType;
                } else {
                    docType = BaoZunDocTypeEnum.AREA_SELL_OUT_RETAIL.getMessage();
                    return docType;
                }
            }

            if (count >= 0L) {
                docType = BaoZunDocTypeEnum.RETURN_IN.getMessage();
                return docType;

            } else {
                docType = BaoZunDocTypeEnum.ORDER_OUT.getMessage();
                return docType;
            }

        }

        return docType;
    }


}