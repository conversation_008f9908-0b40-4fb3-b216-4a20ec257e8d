package com.jiuji.oa.baozun.dto.req.flow;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 *
 * @description: AccessoryStockFlowVo
 * </p>
 * @author: David
 * @create: 2021-09-07 15:42
 */
@Data
public class StockFlowVo {

    private Integer businessId;

    private Integer uniqueId;

    /**
     * 平台Id
     */
    private Integer fkTenantId;

    /**
     * 门店Id
     */
    private Long areaId;

    /**
     * 门店编码
     */
    private String warehouseCode;

    /**
     * 备注
     */
    private String comment;

    /**
     * ppriceid
     */
    private Long ppid;

    /**
     * 商品对照表主键
     */
    private String upc;

    /**
     * 库存变动数量
     */
    private Long count;

    /**
     * 业务发生时间
     */
    private LocalDateTime businessTime;


    /**
     * 串号
     */
    private String sn;

    private Integer orderType;

    /**
     * 串号
     */
    private Integer type;


    /**
     * subId2
     */
    private Integer subId2;

    /**
     * subId2
     */
    private Integer subId3;


    /**
     * subtype
     */
    private Integer subtype;

    /**
     * 来源
     */
    private String fromName;

    private Boolean mouldFlag;

    private Boolean xcFlag;

    /**
     * 库存状态
     */
    private Integer invStatusCode = 1;

}