package com.jiuji.oa.baozun.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.baozun.entity.BaozunTenantArea;
import com.jiuji.oa.baozun.mapper.BaozunTenantAreaMapper;
import com.jiuji.oa.baozun.service.IBaozunTenantAreaService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 宝尊平台门店映射 服务实现类
 * </p>
 *
 * <AUTHOR> yao yao
 * @since 2022-05-19
 */
@Service
@RequiredArgsConstructor
@Slf4j
@DS("ch999oanew")
public class BaozunTenantAreaServiceImpl extends ServiceImpl<BaozunTenantAreaMapper, BaozunTenantArea> implements IBaozunTenantAreaService {

    @Override
    public List<String> getAreaIdList() {
        return this.lambdaQuery()
                .eq(BaozunTenantArea::getIsEnable, Boolean.TRUE)
                .list().stream().map(baozunTenantArea -> String.valueOf(baozunTenantArea.getAreaId())).collect(Collectors.toList());
    }
}
