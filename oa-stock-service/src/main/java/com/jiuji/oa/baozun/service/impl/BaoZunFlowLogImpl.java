package com.jiuji.oa.baozun.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.baozun.entity.BaoZunFlowLog;
import com.jiuji.oa.baozun.mapper.BaoZunFlowLogMapper;
import com.jiuji.oa.baozun.service.IBaoZunFlowLogService;
import org.springframework.stereotype.Service;

/**
 * 宝尊服务实现类
 *
 * <AUTHOR>
 * @Date 2021/09/06
 */
@Service
@DS("oa_nc")
public class BaoZunFlowLogImpl extends ServiceImpl<BaoZunFlowLogMapper, BaoZunFlowLog> implements IBaoZunFlowLogService {
    @Override
    public void saveLog(BaoZunFlowLog baoZunFlowLog) {
        this.save(baoZunFlowLog);
    }
}
