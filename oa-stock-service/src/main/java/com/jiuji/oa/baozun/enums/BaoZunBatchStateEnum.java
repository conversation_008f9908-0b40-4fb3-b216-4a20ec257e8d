package com.jiuji.oa.baozun.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 宝尊库存同步类型
 */
@AllArgsConstructor
@Getter
public enum BaoZunBatchStateEnum implements CodeMessageEnumInterface {

    /**
     * 批次状态：0-初始化,1-生成数据,2-失败,3-上传成功
     */
    INIT(0, "初始化"),
    DATA_READY(1, "生成数据"),
    FAILED(2, "失败"),
    SUCCESS(3, "成功"),
    ERROR(4, "异常"),;

    /**
     * 编码
     */
    private final Integer code;
    /**
     * 编码对应信息
     */
    private final String message;
}
