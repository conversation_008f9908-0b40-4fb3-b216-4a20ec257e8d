package com.jiuji.oa.baozun.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 宝樽流日志
 *
 * <AUTHOR>
 * @date 2021/09/06
 */
@TableName("t_baozun_flow_log")
@Data
@AllArgsConstructor
@Accessors(chain = true)
public class BaoZunFlowLog implements Serializable {

    private static final long serialVersionUID = 59882595303887693L;
    /**
     * id
     */
    private Long id;
    /**
     * 批次号
     */
    private Long batchId;
    /**
     * 备注
     */
    private String remark;
    /**
     * 日志类型
     */
    private Integer logType;
    /**
     * api响应参数
     */
    private String apiResponseParams;
    /**
     * 返回参数
     */
    private String returnParams;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 是否逻辑删除，0为未删除，1为已删除
     */
    @TableLogic
    @TableField("is_deleted")
    private Boolean deleted;

    public BaoZunFlowLog() {
        this.setCreateTime(LocalDateTime.now());
    }
}
