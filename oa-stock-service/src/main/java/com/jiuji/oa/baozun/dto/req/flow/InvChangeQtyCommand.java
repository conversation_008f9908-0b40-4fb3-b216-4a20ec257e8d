package com.jiuji.oa.baozun.dto.req.flow;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.jiuji.oa.baozun.enums.BaoZunDocTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 流水详情
 *
 * <AUTHOR>
 * @date 2021/09/02
 */
@Data
@Accessors(chain = true)
public class InvChangeQtyCommand implements Serializable {

    private static final long serialVersionUID = 2907341637973138740L;
    /**
     * 渠道编码
     */
    private String channelCode;
    /**
     * 客户代码
     */
    private String customerCode;
    /**
     * 库存主体代码
     */
    private String ownerCode;
    /**
     * 仓库编码
     */
    private String warehouseCode;
    /**
     * 逻辑库位
     */
    private String locationCode;
    /**
     * 宝尊sku编码，用于宝尊内部OMS-WMS对接唯一码，内部对接必填
     */
    private String skuCode;
    /**
     * 平台对接编码，对应宝尊内部的ext_code1
     */
    private String extCode;
    /**
     * upc，对应宝尊内部的ext_code2
     */
    private String upc;
    /**
     * 其他用于唯一标识sku的信息。对应宝尊内部的ext_code3
     */
    private String extCode3;
    /**
     * 条形码
     */
    private String barCode;
    /**
     * 商品库存状态
     */
    private String invStatusCode;
    /**
     * 执行数量，有正负
     */
    private Long qty;
    /**
     * 平台订单单号
     */
    private String pfDocNo;

    /**
     * SN信息
     */
    private String sn;

    /**
     * OMS系统单号
     */
    private String sourceDocNo;
    /**
     * 单据类型
     * @see BaoZunDocTypeEnum
     */
    private String docType;
    /**
     * 来源系统代码
     */
    private String sourceSys;
    /**
     * 交易时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime transactionTime;
    /**
     * 唯一标识
     */
    private String uniqueKey;

    /**
     * 宝尊流水id
     */
    @JSONField(serialize = false, deserialize = false)
    @JsonIgnore
    private String baozunFlowId;
    /**
     * 备注(相关明细信息拼接成的JSON字符串) 用于放置一些扩展信息
     */
    private String remark;
    /**
     * 类型,1.流水、2.占用、3.释放, 若为空则默认为1
     */
    private String type;

}
