package com.jiuji.oa.baozun.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * @description: BaoZunTenantEntity
 * </p>
 * @author: David
 * @create: 2021-09-09 18:02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("baozun_tenant")
public class BaoZunTenantEntity extends Model<BaoZunTenantEntity> {

    private static final long serialVersionUID = 3431765500284746979L;
    private Long id;
    private String tenantName;
    private String appKey;
    private String appSecret;
    @TableField("is_enable")
    private Boolean enable;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    @TableLogic
    @TableField("is_del")
    private Boolean delFlag;

}