package com.jiuji.oa.baozun.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jiuji.oa.baozun.service.BzTenantCloudStoreService;
import com.jiuji.oa.nc.common.constant.DataSourceConstants;
import com.jiuji.oa.nc.common.exception.RRExceptionHandler;
import com.jiuji.oa.nc.stock.service.ISmsService;
import com.jiuji.oa.stock.exempt.mapstruct.InventoryExemptQuotaMapStruct;
import com.jiuji.oa.stock.inventory.exception.InventoryException;
import com.jiuji.oa.stock.inventory.service.StockInventoryPlanService;
import com.jiuji.oa.stock.inventory.vo.HandleStock;
import com.jiuji.oa.stock.productkc.entity.ProductKclogs;
import com.jiuji.oa.stock.productkc.service.IProductKclogsService;
import com.jiuji.oa.stock.purchase.entity.*;
import com.jiuji.oa.stock.purchase.service.*;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.common.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 宝尊云仓服务类
 * <AUTHOR>
 * @since 2024/3/11 17:25
 */
@Service
@Slf4j
public class BzTenantCloudStoreServiceImpl implements BzTenantCloudStoreService {


    @Resource
    private CaigouSubBusService caigouSubBusService;
    @Resource
    private CaigouBasketService caigouBasketService;
    @Resource
    private CaigouSubCommentService caigouSubCommentService;
    @Resource
    private ICaigouInputSubService caigouInputSubService;
    @Override
    @DS(DataSourceConstants.OANEW_WRITE)
    @DSTransactional
    public R autoInputSmallKc(Integer caiGouId, String inUser, String businessName) {
        CaigouSub caigouSub = caigouSubBusService.lambdaQuery().eq(CaigouSub::getId, caiGouId).one();
        if (Objects.isNull(caigouSub)) {
            return R.error(StrUtil.format("找不到采购单记录：{}", caiGouId));
        }
        if(Objects.equals(caigouSub.getStats(), 3)){
            return R.error(StrUtil.format("采购单已入库：{}", caiGouId));
        }
        //获取采购商品
        List<CaigouBasket> caigouBasketList = caigouBasketService.lambdaQuery().eq(CaigouBasket::getSubId, caiGouId).list();
        List<HandleStock> stocks = new LinkedList<>();
        for (CaigouBasket basket : caigouBasketList) {
            HandleStock handleStock = new HandleStock();
            handleStock.setPpriceid(basket.getPpriceid())
                    .setCount(basket.getLcount())
                    .setInprice(basket.getInprice())
                    .setAreaid(caigouSub.getAreaid())
                    .setCheck2(1)
                    .setCheck1(1)
                    .setInsource(caigouSub.getInsourceid())
                    .setInuser(inUser)
                    .setDiaoboFlag(Boolean.FALSE)
                    .setComment(StrUtil.format("{}下单生成采购入库,采购明细id:{}", businessName, basket.getId()))
                    .setIsLP(Boolean.FALSE)
                    .setDiaoboFlag(Boolean.FALSE);
            stocks.add(handleStock);
        }
        //批量获取库存日志, 重复的不再进行库存操作
        List<String> kcComments = stocks.stream().map(HandleStock::getComment).collect(Collectors.toList());
        List<ProductKclogs> productKclogs = CommonUtils.bigDataInQuery(kcComments, ids ->
                SpringUtil.getBean(IProductKclogsService.class).lambdaQuery().in(ProductKclogs::getComment, ids)
                        .list());
        stocks.removeIf(hs -> {
            boolean isExists = productKclogs.stream().anyMatch(p -> Objects.equals(p.getComment(), hs.getComment()) && Objects.equals(p.getPpriceid(), hs.getPpriceid()));
            if(isExists){
                log.warn("库存日志已存在，不再进行库存操作：{}", hs.getComment());
            }
            return isExists;
        });

        CaigouSubComment caigouSubComment = new CaigouSubComment();
        caigouSubComment.setComment("[在途] 操作")
                .setShowType(false)
                .setCgId(caiGouId)
                .setInuser(inUser)
                .setDtime(LocalDateTime.now());
        caigouSubCommentService.save(caigouSubComment);

        CaigouInputSub caigouInputSub = new CaigouInputSub();
        caigouInputSub.setDtime(LocalDateTime.now());
        caigouInputSub.setCaigouid(Convert.toLong(caiGouId));
        caigouInputSub.setInpzid(1);
        caigouInputSub.setInuser(inUser);
        caigouInputSubService.save(caigouInputSub);
        Integer inputid = caigouInputSub.getId();
        List<CaigouInputBasket> caigouInputBaskets =
                SpringUtil.getBean(InventoryExemptQuotaMapStruct.class).toCaigouInputBasket(caigouBasketList);

        for (CaigouInputBasket x : caigouInputBaskets) {
            x.setInputid(inputid);
            SpringUtil.getBean(ICaigouInputBasketService.class).save(x);
            CaigouSubComment y = new CaigouSubComment();
            String comment = "ppid：" + x.getPpriceid() + " 入库操作，数量：" + x.getLcount();
            y.setComment(comment)
                    .setShowType(false)
                    .setCgId(caiGouId)
                    .setInuser(inUser)
                    .setDtime(LocalDateTime.now());
            caigouSubCommentService.save(y);
        }

        CaigouSubComment y = new CaigouSubComment();
        String comment = "入库操作，入库单号：<a href='/productKC/caigouInputDetail?id=" + inputid + "'>"
                + inputid + "</a> ";
        y.setComment(comment)
                .setShowType(false)
                .setCgId(caiGouId)
                .setInuser(inUser)
                .setDtime(LocalDateTime.now());
        caigouSubCommentService.save(y);

        CaigouSubComment z = new CaigouSubComment();
        z.setComment("采购订单已入库完成，采购订单自动为已完成！("+businessName+")")
                .setShowType(false)
                .setCgId(caiGouId)
                .setInuser(inUser)
                .setDtime(LocalDateTime.now());
        caigouSubCommentService.save(z);

        CaigouSubComment c = new CaigouSubComment();
        c.setComment("入库凭证号：")
                .setShowType(false)
                .setCgId(caiGouId)
                .setInuser(inUser)
                .setDtime(LocalDateTime.now());
        caigouSubCommentService.save(c);

        if ("3904".equals(String.valueOf(caigouSub.getInsourceid()))
                || "3903".equals(String.valueOf(caigouSub.getInsourceid()))){
            CaigouSubComment e = new CaigouSubComment();
            e.setComment(StrUtil.format("{}采购单完成自动标记无需付款", businessName))
                    .setShowType(false)
                    .setCgId(caiGouId)
                    .setInuser(inUser)
                    .setDtime(LocalDateTime.now());
            caigouSubCommentService.save(e);
            LambdaUpdateWrapper<CaigouSub> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
            lambdaUpdateWrapper.set(CaigouSub::getFreePayment, Convert.toInt(true));
            lambdaUpdateWrapper.eq(CaigouSub::getId, caigouSub.getId());
            caigouSubBusService.update(lambdaUpdateWrapper);
        }

        for (CaigouBasket x : caigouBasketList) {
            LambdaUpdateWrapper<CaigouBasket> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
            lambdaUpdateWrapper.set(CaigouBasket::getInputCount, x.getLcount());
            lambdaUpdateWrapper.eq(CaigouBasket::getId, x.getId());
            caigouBasketService.update(lambdaUpdateWrapper);
        }

        //出入库后操作
        LambdaUpdateWrapper<CaigouSub> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
        lambdaUpdateWrapper.set(CaigouSub::getStats, 3);
        lambdaUpdateWrapper.set(CaigouSub::getRukuDtime, LocalDateTime.now());
        lambdaUpdateWrapper.set(CaigouSub::getZaituDtime, LocalDateTime.now());
        lambdaUpdateWrapper.eq(CaigouSub::getId, caiGouId);
        caigouSubBusService.update(lambdaUpdateWrapper);

        JSONObject result = new JSONObject();
        try {
            result = JSON.parseObject(SpringUtil.getBean(StockInventoryPlanService.class).handleStock(stocks));
        } catch (Exception e){
            //出库发生任何未知异常，都不能回滚, 通知人工处理
            RRExceptionHandler.logError(StrUtil.format("调用C#接口{}采购单处理库存数据", businessName), stocks, e,
                    SpringUtil.getBean(ISmsService.class)::sendOaMsgTo9JiMan);
        }
        String code = result.getString("code");
        if (StringUtils.isEmpty(code) || Integer.parseInt(code) != 0) {
            String msg = Optional.ofNullable(result.getString("msg"))
                    .orElse(result.getString("userMsg"));
            throw new InventoryException(msg);
        }

        return R.success(caiGouId);
    }
}
