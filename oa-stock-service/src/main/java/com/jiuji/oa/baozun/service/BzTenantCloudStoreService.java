package com.jiuji.oa.baozun.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.jiuji.oa.nc.common.constant.DataSourceConstants;
import com.jiuji.tc.common.vo.R;

/**
 * 宝尊云仓服务类
 * <AUTHOR>
 * @since 2024/3/20 20:22
 */
public interface BzTenantCloudStoreService {

    /**
     * 宝尊采购单自动入库
     *
     * @param caiGouId
     * @param businessName
     * @return
     */
    @DS(DataSourceConstants.OANEW_WRITE)
    @DSTransactional
    R autoInputSmallKc(Integer caiGouId, String inUser, String businessName);
}
