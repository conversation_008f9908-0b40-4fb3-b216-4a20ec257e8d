package com.jiuji.oa.baozun.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.baozun.entity.BaoZunTenantEntity;
import com.jiuji.oa.baozun.mapper.BaoZunTenantMapper;
import com.jiuji.oa.baozun.service.IBaoZunTenantService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 *
 * @description: BaoZunTenantServiceImpl
 * </p>
 * @author: David
 * @create: 2021-09-09 18:10
 */
@Service
@Slf4j
@DS("ch999oanew")
public class BaoZunTenantServiceImpl extends ServiceImpl<BaoZunTenantMapper, BaoZunTenantEntity> implements
        IBaoZunTenantService {

    /**
     * getBaoZunTenantById
     *
     * @param id
     * @return
     */
    @Override
    public BaoZunTenantEntity getBaoZunTenantById(Integer id) {
        return this.lambdaQuery().eq(BaoZunTenantEntity::getId, id).eq(BaoZunTenantEntity::getEnable, true).one();
    }
}