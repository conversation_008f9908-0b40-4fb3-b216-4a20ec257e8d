package com.jiuji.oa.baozun.mapstruct;

import com.jiuji.oa.baozun.dto.AppInfoDTO;
import com.jiuji.oa.baozun.dto.req.flow.InvChangeQtyCommand;
import com.jiuji.oa.baozun.dto.req.flow.StockFlowVo;
import com.jiuji.oa.baozun.dto.req.skufull.FullSkuInventory;
import com.jiuji.oa.baozun.entity.BaoZunFlowEntity;
import com.jiuji.oa.baozun.entity.BaoZunTenantEntity;
import com.jiuji.oa.baozun.util.BaoZunUtils;
import com.jiuji.oa.stock.stockmanage.entity.StockMkcFlow;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", imports = BaoZunUtils.class, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BaoZunMapStruct {


    /**
     * 转BaoZunFlowEntity
     *
     * @param batchId
     * @param vo
     * @return
     */
    @Mapping(target = "batchId", source = "batchId")
    @Mapping(target = "createTime", source = "vo.businessTime")
    BaoZunFlowEntity toBaoZunFlowEntity(Long batchId, StockFlowVo vo);


    /**
     * 转StockFlowVo
     *
     * @param mkcFlow
     * @return
     */
    @Mapping(target = "sn", source = "mkcFlow.imei")
    @Mapping(target = "businessId", source = "mkcFlow.orderId")
    @Mapping(target = "type", source = "mkcFlow.stockChangeType")
    StockFlowVo toStockFlowVo(StockMkcFlow mkcFlow);


    /**
     * 转FullSkuInventory
     *
     * @param entity
     * @param invTime
     * @param sourceSys
     * @return
     */
    @Mapping(target = "uniqueKey", source = "entity.uniqueKey")
    @Mapping(target = "ownerCode", source = "entity.warehouseCode")
    @Mapping(target = "invQty", source = "entity.count")
    @Mapping(target = "invStatusCode", source = "entity.invStatusCode")
    @Mapping(target = "sourceSys", source = "sourceSys")
    FullSkuInventory toFullSkuInventory(BaoZunFlowEntity entity, LocalDateTime invTime, String sourceSys);


    /**
     * 转InvChangeQtyCommand
     *
     * @param entity
     * @param transactionTime
     * @param sourceSys
     * @return
     */
    @Mapping(target = "pfDocNo", expression = "java(BaoZunUtils.getDocNo(entity.getId(),entity.getBusinessId(),entity.getOrderType(),entity.getType(),entity.getSn(),entity.getUpc(),entity.getAreaId()))")
    @Mapping(target = "sourceDocNo", expression = "java(BaoZunUtils.getDocNo(entity.getId(),entity.getBusinessId(),entity.getOrderType(),entity.getType(),entity.getSn(),entity.getUpc(),entity.getAreaId()))")
    @Mapping(target = "ownerCode", source = "entity.warehouseCode")
    @Mapping(target = "sourceSys", source = "sourceSys")
    @Mapping(target = "invStatusCode", source = "entity.invStatusCode")
    @Mapping(target = "qty", source = "entity.count")
    @Mapping(target = "transactionTime", source = "entity.createTime")
    @Mapping(target = "uniqueKey", source = "entity.uniqueKey")
    @Mapping(target = "baozunFlowId", source = "entity.id")
    @Mapping(target = "type",  source = "entity.type")
    @Mapping(target = "docType", expression = "java(BaoZunUtils.getDocType(entity.getBusinessId(),entity.getOrderType(),entity.getType(),entity.getCount()))")
    InvChangeQtyCommand toInvChangeQtyCommand(BaoZunFlowEntity entity, LocalDateTime transactionTime, String sourceSys);


    /**
     * toAppInfoDTO
     *
     * @param entity
     * @return
     */
    AppInfoDTO toAppInfoDTO(BaoZunTenantEntity entity);
}
