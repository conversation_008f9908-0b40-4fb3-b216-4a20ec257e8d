package com.jiuji.oa.baozun.service;

import com.jiuji.oa.baozun.dto.req.flow.StockFlowVo;
import com.jiuji.oa.baozun.entity.BaoZunFlowEntity;
import com.jiuji.oa.stock.common.service.BatchAffectRowsService;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 宝尊出入库同步流水表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-07
 */
public interface IBaoZunFlowService extends BatchAffectRowsService<BaoZunFlowEntity> {

    /**
     * 查询需要全量同步的配件记录数
     *
     * @param fkTenantId
     * @return
     */
    Integer countAccessoryAreaPpid(Integer fkTenantId);

    /**
     * 查询需要全量同步的大件记录数
     *
     * @param fkTenantId
     * @return
     */
    Integer countMobileAreaPpid(Integer fkTenantId);


    /**
     * 查询小件库存变动流水
     *
     * @param fkTenantId
     * @param startTime
     * @param endTime
     * @return
     */
    List<StockFlowVo> searchAccessoryStockFlow(Integer fkTenantId, LocalDateTime startTime, LocalDateTime endTime);


    /**
     * 查询当前小件库存
     *
     * @param fkTenantId
     * @param start
     * @param end
     * @return
     */
    List<StockFlowVo> searchAccessoryStock(Integer fkTenantId, Integer start, Integer end);


    /**
     * 查询当前大件库存
     *
     * @param fkTenantId
     * @param start
     * @param end
     * @return
     */
    List<StockFlowVo> searchMobileStock(Integer fkTenantId, Integer start, Integer end);

    /**
     * 批量保存返回影响条数
     *
     * @param list
     * @return
     */
    int saveBatchWithAffectRows(List<BaoZunFlowEntity> list);


    /**
     * 列出库存变动流水汇总
     *
     * @param batchId
     * @return
     */
    List<BaoZunFlowEntity> listBaoZunChangeQty(Long batchId);

    /**
     * 列出当前全量库存
     *
     * @param batchId
     * @return
     */
    List<BaoZunFlowEntity> listBaoZunFullSkuInventories(Long batchId);


    /**
     * listRefund
     *
     * @param fkTenantId
     * @param batchId
     * @return
     */
    List<BaoZunFlowEntity> listRefund(Integer fkTenantId, Long batchId);

    /**
     * 退货数据更新流水
     *
     * @param entity
     */
    void updateBaoZunFlowWithRefund(BaoZunFlowEntity entity);
}
