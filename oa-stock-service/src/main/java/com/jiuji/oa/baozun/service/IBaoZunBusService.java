package com.jiuji.oa.baozun.service;

import com.jiuji.oa.baozun.dto.AppInfoDTO;
import com.jiuji.oa.baozun.entity.BaoZunBatchEntity;

/**
 * <p>
 * 宝尊出入库同步批量 业务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-07
 */
public interface IBaoZunBusService {

    /**
     * 同步库存流水
     *
     * @param fkTenantId
     * @return
     */
    Integer synchronizeStockFlow(Integer fkTenantId);


    /**
     * 同步库存流水
     *
     * @param fkTenantId id
     * @return Integer
     */
    Integer uploadFlowToBaoZun(Integer fkTenantId, BaoZunBatchEntity batch);


    /**
     * getAppInfoDTO
     *
     * @param fkTenantId
     * @return
     */
    AppInfoDTO getAppInfoDTO(Integer fkTenantId);

    /**
     * 同步当前所有库存
     *
     * @param fkTenantId
     * @return
     */
    Integer synchronizeAllStock(Integer fkTenantId);


    /**
     * 重试增量库存变动
     *
     * @param fkTenantId
     * @param batchId
     * @return
     */
    Integer retryStockFlow(Integer fkTenantId, Long batchId);


    /**
     * 重试全量库存同步
     *
     * @param fkTenantId
     * @param batchId
     * @return
     */
    Integer retryStockAll(Integer fkTenantId, Long batchId);

    /**
     * 自动重试增量库存变动
     *
     * @param fkTenantId
     * @return
     */
    Integer autoRetryStockFlow(Integer fkTenantId);
}
