package com.jiuji.oa.baozun.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.baozun.dto.req.flow.StockFlowVo;
import com.jiuji.oa.baozun.entity.BaoZunFlowEntity;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 宝尊出入库同步流水表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-07
 */
public interface BaoZunFlowMapper extends BaseMapper<BaoZunFlowEntity> {

    /**
     * 查询配件出入库流水
     *
     * @param fkTenantId
     * @param startTime
     * @param endTime
     * @return
     */
    List<StockFlowVo> queryAccessoryStockFlow(@Param("fkTenantId") Integer fkTenantId,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime);

    /**
     * 分页查询配件当前库存
     *
     * @param fkTenantId
     * @param start
     * @param end
     * @return
     */
    List<StockFlowVo> queryAccessoryStock(@Param("fkTenantId") Integer fkTenantId, @Param("start") Integer start,
            @Param("end") Integer end);

    /**
     * 查询需要全量同步的配件记录数
     *
     * @param fkTenantId
     * @return
     */
    Integer countAccessoryAreaPpid(@Param("fkTenantId") Integer fkTenantId);

    /**
     * 查询需要全量同步的大件记录数
     *
     * @param fkTenantId
     * @return
     */
    Integer countMobileAreaPpid(@Param("fkTenantId") Integer fkTenantId);

    /**
     * 分页查询大件当前库存
     *
     * @param fkTenantId
     * @param start
     * @param end
     * @return
     */
    List<StockFlowVo> queryMobileStock(@Param("inSourceIdList")List<Long> inSourceIdList,@Param("fkTenantId") Integer fkTenantId, @Param("start") Integer start,
            @Param("end") Integer end);

    /**
     * 分页查询大件当前库存Sn
     *
     * @param areaid
     * @param ppriceid
     * @return
     */
    List<StockFlowVo> queryMobileStockSn(@Param("inSourceIdList")List<Long> inSourceIdList,@Param("areaid") Long areaid, @Param("ppriceid") Long ppriceid);

    /**
     * queryRefund
     *
     * @param fkTenantId
     * @param batchId
     * @return
     */
    List<BaoZunFlowEntity> queryRefund(@Param("fkTenantId") Integer fkTenantId, @Param("batchId") Long batchId);

    /**
     * updateBaoZunFlowWithRefund
     *
     * @param entity
     */
    void updateBaoZunFlowWithRefund(@Param("entity")BaoZunFlowEntity entity);
}
