package com.jiuji.oa.baozun.dto.req.skufull;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 全量库存明细
 *
 * <AUTHOR>
 * @date 2021/09/02
 */
@Data
public class FullSkuInventory implements Serializable {

    private static final long serialVersionUID = 6749166390068874985L;
    /**
     * 客户代码
     */
    private String customerCode;
    /**
     * 库存时间 YYYY-MM-DD HH:MM:SS
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime invTime;
    /**
     * 经销商简称，请联系中台约定简称名称
     */
    private String ownerCode;
    /**
     * 门店编码
     */
    private String warehouseCode;
    /**
     * SKU编码SKU宝尊sku编码，用于宝尊内部OMS-WMS对接唯一码，内部对接必填
     */
    private String skuCode;
    /**
     * 平台对接编码，对应宝尊内部的ext_code1
     */
    private String extCode;
    /**
     * upc，对应宝尊内部的ext_code2
     */
    private String upc;
    /**
     * apple商品upc字段，对应M07 的 variants.properties.brandSkuCode
     */
    private String extCode3;
    /**
     * 条形码
     */
    private String barCode;
    /**
     * 库存数量
     */
    private Long invQty;
    /**
     * 标识本条数据的唯一值，中台用来做幂等性的校验（去重），
     * ERP需要保证在传输的过程中不同的流水值是不一样的，相同的数据在补偿传输时是一致的，避免中台重复处理。
     */
    private String uniqueKey;
    /**
     * 来源系统，中台只做记录，ERP可以传输ERP的简称，中文英文都可
     */
    private String sourceSys;
    /**
     * 库存状态，ERP只用传输良品，良品值传输：1，良品 = 全新机
     */
    private String invStatusCode;
    /**
     * 扩展字段(json格式)
     */
    private String extProps;
    /**
     * 货号
     */
    private String article;
    /**
     * BZ尺码
     */
    private String sizeIndex;


    /**
     * SN信息
     */
    private String sn;
}
