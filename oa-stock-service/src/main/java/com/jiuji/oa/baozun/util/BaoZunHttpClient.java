package com.jiuji.oa.baozun.util;

import com.jiuji.oa.baozun.common.client.DefaultBaoZunClient;
import com.jiuji.oa.baozun.common.client.IBaoZunClient;
import com.jiuji.oa.baozun.common.exception.BaoZunApiException;
import com.jiuji.oa.baozun.constant.Constants;
import com.jiuji.oa.baozun.dto.AppInfoDTO;
import com.jiuji.oa.nc.common.util.CommonUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * http 调用宝尊 api
 *
 * <AUTHOR>
 * @date 2021/09/02
 */
@Slf4j
public class BaoZunHttpClient {

    BaoZunHttpClient() {

    }

    /**
     * 同步Post请求
     *
     * @param methodName 方法名称
     * @param request    请求
     * @return {@link String}
     */
    public static String syncPost(String request, String methodName, AppInfoDTO infoDTO) {
        String appKey = infoDTO.getAppKey();
        String appSecret = infoDTO.getAppSecret();
        String url = infoDTO.getRequestUrl();

        IBaoZunClient baoZunClient = new DefaultBaoZunClient(url, appKey, appSecret);
        String apiRes;
        try {
            apiRes = baoZunClient.execute(request, methodName);
            return apiRes;
        } catch (Exception e) {
            log.error("请求参数： {}", request, e);
            throw new BaoZunApiException(e.getMessage(), e);
        }
    }

    public static String flowPost(String request, AppInfoDTO infoDTO) {
        if(CommonUtil.closeBaoZunEsApi()){
            throw new BaoZunApiException("响应宝尊要求, 接口已停用");
        }
        return syncPost(request, Constants.METHOD_NAME_INVENTORY_FLOW, infoDTO);
    }

    public static String fullPost(String request, AppInfoDTO infoDTO) {
        if(CommonUtil.closeBaoZunEsApi()){
            throw new BaoZunApiException("响应宝尊要求, 接口已停用");
        }
        return syncPost(request, Constants.METHOD_NAME_FULL_INVENTORY, infoDTO);
    }
}
