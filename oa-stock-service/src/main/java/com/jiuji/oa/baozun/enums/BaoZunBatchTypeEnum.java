package com.jiuji.oa.baozun.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 宝尊库存同步类型
 */
@AllArgsConstructor
@Getter
public enum BaoZunBatchTypeEnum implements CodeMessageEnumInterface {

    /**
     * 批次类型：1-流水,2-全量
     */
    INCREMENT(1, "增量"),
    FULL(2, "全量");

    /**
     * 编码
     */
    private final Integer code;
    /**
     * 编码对应信息
     */
    private final String message;
}
