package com.jiuji.oa.baozun.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jiuji.oa.baozun.enums.BaoZunBatchStateEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 宝尊出入库同步批量
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_baozun_batch")
public class BaoZunBatchEntity extends Model<BaoZunBatchEntity> {

    private static final long serialVersionUID = -5170190151522483289L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 批次类型：1-流水,2-全量
     */
    private Integer batchType;

    /**
     * 批次状态：0-未提交,1-处理中,2-失败,3-成功
     * @see BaoZunBatchStateEnum
     */
    private Integer state;

    /**
     * 平台Id
     */
    private Integer fkTenantId;

    /**
     * 标识本批次请求数据的唯一值，数据分批传输，一批次一个值
     */
    private String msgId;


    /**
     * 上次执行时间
     */
    private LocalDateTime lastExecuteTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标志：0-未删除，1-已删除
     */
    @TableLogic
    private Boolean deleteFlag;

    /**
     * 重试次数
     */
    private Integer retryCount = 0;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
