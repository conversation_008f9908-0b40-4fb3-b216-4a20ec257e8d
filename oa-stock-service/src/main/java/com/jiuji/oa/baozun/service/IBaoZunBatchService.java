package com.jiuji.oa.baozun.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.baozun.entity.BaoZunBatchEntity;

/**
 * <p>
 * 宝尊出入库同步批量 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-07
 */
public interface IBaoZunBatchService extends IService<BaoZunBatchEntity> {

    /**
     * 获取上一次的批处理
     *
     * @param fkTenantId
     * @param batchType
     * @return
     */
    BaoZunBatchEntity getLastBatch(Integer fkTenantId, Integer batchType);


    /**
     * 保存批处理
     *
     * @param entity
     * @return
     */
    void saveBaoZunBatch(BaoZunBatchEntity entity);

    /**
     * 更新批处理状态
     *
     * @param batchId
     * @param state
     */
    void updateBatchState(Long batchId, Integer state);

    boolean addBatchTryCount(Long batchId);

    /**
     * getByBatchId
     *
     * @param batchId
     * @return
     */
    BaoZunBatchEntity getByBatchId(Long batchId);


}
