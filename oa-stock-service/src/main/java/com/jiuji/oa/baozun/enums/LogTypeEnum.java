package com.jiuji.oa.baozun.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 日志类型
 *
 * <AUTHOR>
 * @date 2021/09/06
 */
@AllArgsConstructor
@Getter
public enum LogTypeEnum {
    /**
     * 上传当前批次流水
     */
    UPLOAD_FLOW(1,"按批次上传流水"),
    /**
     * 重新上传失败批次流水
     */
    UPLOAD_FULL(2,"每日库存全量更新");

    /**
     * 日志类型
     */
    private Integer logTypeCode;
    /**
     * 备注
     */
    private String remark;
}
