package com.jiuji.oa.baozun.service.impl;

import cn.hutool.json.JSONException;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.jiuji.oa.baozun.common.exception.BaoZunApiException;
import com.jiuji.oa.baozun.common.util.JsonUtil;
import com.jiuji.oa.baozun.dto.AppInfoDTO;
import com.jiuji.oa.baozun.dto.req.flow.FlowReqDTO;
import com.jiuji.oa.baozun.dto.req.flow.InvChangeQtyCommand;
import com.jiuji.oa.baozun.dto.req.flow.StockFlowVo;
import com.jiuji.oa.baozun.dto.req.skufull.FullSkuInventory;
import com.jiuji.oa.baozun.dto.req.skufull.FullSkuReqDTO;
import com.jiuji.oa.baozun.dto.res.BaoZunResponse;
import com.jiuji.oa.baozun.entity.BaoZunBatchEntity;
import com.jiuji.oa.baozun.entity.BaoZunFlowEntity;
import com.jiuji.oa.baozun.entity.BaoZunFlowLog;
import com.jiuji.oa.baozun.entity.BaoZunTenantEntity;
import com.jiuji.oa.baozun.enums.*;
import com.jiuji.oa.baozun.mapper.BaoZunBatchMapper;
import com.jiuji.oa.baozun.mapper.BaoZunFlowMapper;
import com.jiuji.oa.baozun.mapstruct.BaoZunMapStruct;
import com.jiuji.oa.baozun.service.*;
import com.jiuji.oa.baozun.util.BaoZunHttpClient;
import com.jiuji.oa.nc.common.constant.RedisKeys;
import com.jiuji.oa.nc.common.util.CommonUtil;
import com.jiuji.oa.nc.dict.enums.ConfigEnum;
import com.jiuji.oa.nc.dict.service.ISysConfigService;
import com.jiuji.oa.stock.common.service.impl.BatchAffectRowsServiceImpl;
import com.jiuji.oa.stock.common.util.Builder;
import com.jiuji.oa.stock.stockmanage.entity.StockMkcFlow;
import com.jiuji.oa.stock.stockmanage.enums.OrderTypeEnum;
import com.jiuji.oa.stock.stockmanage.enums.StockChangeTypeEnum;
import com.jiuji.oa.stock.stockmanage.service.IStockMkcFlowService;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.constants.NumberConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * @description: BaoZunBusServiceImpl
 * </p>
 * @author: David
 * @create: 2021-09-07 4:32
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BaoZunBusServiceImpl extends BatchAffectRowsServiceImpl<BaoZunFlowMapper, BaoZunFlowEntity> implements
        IBaoZunBusService {

    private static final int WRITE_BATCH_SIZE = 1000;
    private static final String SOURCE_MARK_CODE = "routeIosp_om";
    @Resource
    private IBaoZunBatchService baoZunBatchService;
    @Resource
    private IBaoZunFlowService baoZunFlowService;
    @Resource
    private IStockMkcFlowService stockMkcFlowService;
    @Resource
    private ISysConfigService sysConfigService;
    @Resource
    private IBaoZunFlowLogService baoZunFlowLogService;
    @Resource
    private BaoZunMapStruct baoZunMapStruct;
    @Resource
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    private IBaoZunTenantService baoZunTenantService;

    @Resource
    private BaoZunBatchMapper baoZunBatchMapper;

    @Value("${baozun.url}")
    private String url;

    /**
     * 构造日志
     *
     * @param log
     * @param batchId
     * @param apiResponseParams
     * @param returnParams
     */
    private static void constructBaoZunFlowLog(BaoZunFlowLog log, Long batchId, LogTypeEnum type, String apiResponseParams,
                                               String returnParams) {
        LocalDateTime now = LocalDateTime.now();
        log.setBatchId(batchId)
                .setLogType(type.getLogTypeCode())
                .setRemark(type.getRemark())
                .setApiResponseParams(apiResponseParams)
                .setReturnParams(returnParams)
                .setUpdateTime(now);
    }

    /**
     * 每5分钟同步库存流水
     *
     * @return
     */
    @Override
    public Integer synchronizeStockFlow(Integer fkTenantId) {
        //查询上一次执行的批处理
        BaoZunBatchEntity lastBatch = baoZunBatchService.getLastBatch(fkTenantId, BaoZunBatchTypeEnum.INCREMENT.getCode());


        Assert.notNull(lastBatch, "上一次执行的批处理为空");
        Assert.notNull(lastBatch.getCreateTime(), "上一次批处理的创建时间为空");

        //初始化本次批处理
        BaoZunBatchEntity batch = Builder.of(BaoZunBatchEntity::new)
                .with(BaoZunBatchEntity::setBatchType, BaoZunBatchTypeEnum.INCREMENT.getCode())
                .with(BaoZunBatchEntity::setState, BaoZunBatchStateEnum.INIT.getCode())
                .with(BaoZunBatchEntity::setFkTenantId, fkTenantId)
                .with(BaoZunBatchEntity::setMsgId, UUID.randomUUID().toString().replace("-", ""))
                .with(BaoZunBatchEntity::setLastExecuteTime, lastBatch.getCreateTime())
                .with(BaoZunBatchEntity::setCreateTime, LocalDateTime.now())
                .build();
        baoZunBatchService.saveBaoZunBatch(batch);

        //保存本次时间内库存变动情况
        //1、保存小件库存变动
        List<StockFlowVo> accessoryStockFlowVoList = baoZunFlowService
                .searchAccessoryStockFlow(fkTenantId, batch.getLastExecuteTime(), batch.getCreateTime());
        //写入t_baozun_flow表
        if (!CollectionUtils.isEmpty(accessoryStockFlowVoList)) {
            List<Integer> uniqueIdList = accessoryStockFlowVoList.stream().map(StockFlowVo::getUniqueId).collect(Collectors.toList());
            Map<Integer, BaoZunFlowEntity> existMap = baoZunFlowService.lambdaQuery().in(BaoZunFlowEntity::getUniqueId, uniqueIdList)
                    //过滤createTime7天以内的数据
                    .gt(BaoZunFlowEntity::getCreateTime, LocalDateTime.now().minusDays(7))
                    .list()
                    .stream().collect(Collectors.toMap(BaoZunFlowEntity::getUniqueId, Function.identity(), (v1, v2) -> v1));
            List<BaoZunFlowEntity> saveList = accessoryStockFlowVoList.stream()
                    .filter(x -> {
                        //排除美团、京东、抖音、闲鱼等订单
                        return !existMap.containsKey(x.getUniqueId())
                                && (!Arrays.asList(18, 19, 21, 22, 24, 25, 26, 27, 28).contains(x.getSubtype()));
                    })
                    .map(it -> {
                        Integer subtype = it.getSubtype();
                        String fromName = it.getFromName();
                        String comment = StringUtils.isEmpty(it.getComment()) ? "" : it.getComment();
                        Integer orderType = null;
                        BaoZunFlowEntity baoZunFlowEntity = baoZunMapStruct.toBaoZunFlowEntity(batch.getId(), it);
                        if ("调拨".equals(comment)) {
                            orderType = OrderTypeEnum.TRANSFER.getCode();
                            baoZunFlowEntity.setOrderType(orderType);
                            baoZunFlowEntity.setType(StockChangeTypeEnum.FLOW.getCode());
                            return baoZunFlowEntity;
                        }
                        if ("配件报损出库".equals(comment)) {
                            orderType = OrderTypeEnum.LOSE.getCode();
                            baoZunFlowEntity.setOrderType(orderType);
                            baoZunFlowEntity.setType(StockChangeTypeEnum.FLOW.getCode());
                            return baoZunFlowEntity;
                        }
                        if ("配件退货出库".equals(comment) || comment.contains("采购入库单")) {
                            orderType = OrderTypeEnum.SUB.getCode();
                            baoZunFlowEntity.setOrderType(orderType);
                            baoZunFlowEntity.setType(StockChangeTypeEnum.FLOW.getCode());
                            return baoZunFlowEntity;
                        }
                        if (Arrays.asList(13, 14).contains(subtype)
                                || (!Arrays.asList(13, 14).contains(subtype) && "团购".equals(fromName))) {
                            orderType = OrderTypeEnum.NOT_RETAIL.getCode();
                            baoZunFlowEntity.setOrderType(orderType);
                            baoZunFlowEntity.setType(StockChangeTypeEnum.FLOW.getCode());
                            return baoZunFlowEntity;
                        }
                        if (!Arrays.asList(13, 14).contains(subtype) && !"团购".equals(fromName)) {
                            orderType = OrderTypeEnum.RETAIL.getCode();
                            baoZunFlowEntity.setOrderType(orderType);
                            baoZunFlowEntity.setType(StockChangeTypeEnum.FLOW.getCode());
                            return baoZunFlowEntity;
                        }
                        baoZunFlowEntity.setOrderType(orderType);
                        baoZunFlowEntity.setType(StockChangeTypeEnum.FLOW.getCode());
                        return baoZunFlowEntity;
                    }).peek(x -> x.setUniqueKey(x.businessMd5()))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(saveList)) {
                saveList = saveList.stream().filter(Objects::nonNull)
                        .peek(x -> {
                            if (Objects.nonNull(x.getSubId2())
                                    && StockChangeTypeEnum.FLOW.getCode().equals(x.getType())
                                    && Arrays.asList(13, 14).contains(x.getOrderType())) {
                                x.setBusinessId(x.getSubId2());
                            }
                            if (x.getComment().contains("售后小件换货取件出库")) {
                                x.setBusinessId(x.getSubId3());
                            }
                        })
                        .collect(Collectors.toList());
                int result = baoZunFlowService.saveBatchWithAffectRows(saveList);
//                Assert.state(accessoryStockFlowVoList.size() == result, "小件变动流水保存失败");
            }
        }
        //2、保存大件库存变动
        String inSourceIds = sysConfigService.getValueByCode(ConfigEnum.BAOZUN_CHANNEL);
        Assert.notNull(inSourceIds, "缺少宝尊渠道配置信息");
        List<Long> inSourceIdList = StringUtils.commaDelimitedListToSet(inSourceIds).stream().map(Long::valueOf)
                .collect(Collectors.toList());
        List<StockMkcFlow> mkcFlow = stockMkcFlowService
                .listBaoZunMkcFlow(fkTenantId, batch.getLastExecuteTime(), batch.getCreateTime(), inSourceIdList);
        //写入t_baozun_flow表
        if (!CollectionUtils.isEmpty(mkcFlow)) {
            List<BaoZunFlowEntity> list = mkcFlow.stream().map(baoZunMapStruct::toStockFlowVo)
                    .map(it -> baoZunMapStruct.toBaoZunFlowEntity(batch.getId(), it))
                    .peek(x -> x.setUniqueKey(x.businessMd5()))
                    .distinct()
                    .collect(Collectors.toList());
            updateSyncState(mkcFlow);
            List<String> uniqueKeyList = list.stream().map(BaoZunFlowEntity::getUniqueKey).collect(Collectors.toList());
            List<String> existList = CommonUtils.bigDataInQuery(uniqueKeyList, uniqueKey -> baoZunFlowService.lambdaQuery()
                    .in(BaoZunFlowEntity::getUniqueKey, uniqueKey)
                    .gt(BaoZunFlowEntity::getCreateTime, LocalDateTime.now().minusDays(7))
                    .list().stream().map(BaoZunFlowEntity::getUniqueKey).collect(Collectors.toList()));
            list = list.stream().filter(x -> !existList.contains(x.getUniqueKey())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(list)){
                baoZunFlowService.saveBatchWithAffectRows(list);
            }
        }
        Integer flowEntityList = uploadFlowToBaoZun(fkTenantId, batch);
        if (flowEntityList != null) {
            return flowEntityList;
        }
        return 0;
    }

    private void updateSyncState(List<StockMkcFlow> mkcFlow) {
        for (StockMkcFlow x : mkcFlow) {
            LambdaUpdateChainWrapper<StockMkcFlow> updateChainWrapper = stockMkcFlowService.lambdaUpdate()
                    .eq(StockMkcFlow::getId, x.getId());
            updateChainWrapper.set(StockMkcFlow::getDeleteFlag, Boolean.TRUE);
            updateChainWrapper.update();
        }
    }

    @Override
    public Integer uploadFlowToBaoZun(Integer fkTenantId, BaoZunBatchEntity batch) {
        if(CommonUtil.closeBaoZunEsApi()){
            log.warn("响应宝尊要求, 接口已停用");
            return 0;
        }
        //更新批次状态
        baoZunBatchService.updateBatchState(batch.getId(), BaoZunBatchStateEnum.DATA_READY.getCode());

        //上传数据到宝尊
        List<BaoZunFlowEntity> flowEntityList = baoZunFlowService.listBaoZunChangeQty(batch.getId());
        AppInfoDTO appInfo = getAppInfoDTO(fkTenantId);
        appInfo.setRequestUrl(url);
        final String sourceSys = appInfo.getTenantName();
        if (!CollectionUtils.isEmpty(flowEntityList)) {

            List<String> uniqueKeyList = flowEntityList.stream().map(BaoZunFlowEntity::getUniqueKey).collect(Collectors.toList());
            List<String> existList = CommonUtils.bigDataInQuery(uniqueKeyList, uniqueKey -> baoZunFlowService.lambdaQuery()
                    .in(BaoZunFlowEntity::getUniqueKey, uniqueKey)
                    .gt(BaoZunFlowEntity::getCreateTime, LocalDateTime.now().minusDays(7))
                    .ne(BaoZunFlowEntity::getBatchId, batch.getId())
                    .list().stream().map(BaoZunFlowEntity::getUniqueKey).collect(Collectors.toList()));
            flowEntityList = flowEntityList.stream().filter(x -> !existList.contains(x.getUniqueKey())).collect(Collectors.toList());

            List<InvChangeQtyCommand> invChangeQtyCommandList = flowEntityList.stream()
                    .map((BaoZunFlowEntity it) -> {
                        if (!StringUtils.isEmpty(it.getWarehouseCode())) {
                            it.setWarehouseCode(it.getWarehouseCode().trim());
                        }
                        InvChangeQtyCommand invChangeQtyCommand = baoZunMapStruct.toInvChangeQtyCommand(it, it.getCreateTime(), sourceSys);
                        invChangeQtyCommand.setSourceDocNo(invChangeQtyCommand.getPfDocNo());
                        return invChangeQtyCommand;
                    }).collect(Collectors.toList());
            for (InvChangeQtyCommand invChangeQtyCommand : invChangeQtyCommandList) {
                String baozunFlowId = invChangeQtyCommand.getBaozunFlowId();
                String docType = invChangeQtyCommand.getDocType();
                LambdaUpdateChainWrapper<BaoZunFlowEntity> updateChainWrapper = baoZunFlowService.lambdaUpdate()
                        .eq(BaoZunFlowEntity::getId, baozunFlowId);
                updateChainWrapper.set(BaoZunFlowEntity::getDocType, docType);
                updateChainWrapper.update();
            }
            List<InvChangeQtyCommand> synInvChangeQtyCommandList = invChangeQtyCommandList.stream()
                    .filter(x -> {
                                if (!StringUtils.isEmpty(x.getSn())
                                        && (BaoZunDocTypeEnum.SUB_IN.getMessage().equals(x.getDocType())
                                        || BaoZunDocTypeEnum.TRANSFER_IN.getMessage().equals(x.getDocType()))) {
                                    //大件 流水宝尊自行同步
                                    return false;
                                }
                                return true;
                            }
                    ).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(synInvChangeQtyCommandList)){
                baoZunBatchService.updateBatchState(batch.getId(), BaoZunBatchStateEnum.ERROR.getCode());
                return 0;
            }
            FlowReqDTO flowReqDTO = Builder.of(FlowReqDTO::new)
                    .with(FlowReqDTO::setSourceMarkCode, SOURCE_MARK_CODE)
                    .with(FlowReqDTO::setInvChangeQtyCommand, synInvChangeQtyCommandList)
                    .build();
            // 初始化日志，给创建时间赋值
            BaoZunFlowLog flowLog = new BaoZunFlowLog();
            // 发送请求
            String request = JsonUtil.object2JsonNonNull(flowReqDTO);
            try {
                String result = BaoZunHttpClient.flowPost(request, appInfo);
                log.info("宝尊请求" + result);
                updateState(result, batch);
                constructBaoZunFlowLog(flowLog, batch.getId(), LogTypeEnum.UPLOAD_FLOW, request,
                        result);
                baoZunFlowLogService.saveLog(flowLog);
            } catch (Exception e) {
                constructBaoZunFlowLog(flowLog, batch.getId(), LogTypeEnum.UPLOAD_FLOW, request,
                        e.getMessage());
                baoZunFlowLogService.saveLog(flowLog);

                log.error(e.getMessage(), e);
                baoZunBatchService.updateBatchState(batch.getId(), BaoZunBatchStateEnum.FAILED.getCode());
                throw new BaoZunApiException("请求失败", e.getMessage());
            }
            //更新批次状态
            return flowEntityList.size();
        }
        baoZunBatchService.updateBatchState(batch.getId(), BaoZunBatchStateEnum.SUCCESS.getCode());
        return null;
    }

    @Override
    public AppInfoDTO getAppInfoDTO(Integer fkTenantId) {
        AppInfoDTO appInfo;
        Boolean hasKey = redisTemplate.hasKey(RedisKeys.BAO_ZUN_APP + fkTenantId);
        if (Boolean.TRUE.equals(hasKey)) {
            String infoString = redisTemplate.opsForValue().get(RedisKeys.BAO_ZUN_APP + fkTenantId);
            appInfo = JSONUtil.toBean(infoString, AppInfoDTO.class);
        } else {
            BaoZunTenantEntity tenant = baoZunTenantService.getBaoZunTenantById(fkTenantId);
            Assert.notNull(tenant, "从数据库中获取商户app信息失败");
            appInfo = baoZunMapStruct.toAppInfoDTO(tenant);
            redisTemplate.opsForValue().set(RedisKeys.BAO_ZUN_APP + fkTenantId, JSONUtil.toJsonStr(appInfo));
        }
        Assert.notNull(appInfo, "商户app信息获取失败");
        return appInfo;
    }

    @Override
    public Integer synchronizeAllStock(Integer fkTenantId) {
        if(CommonUtil.closeBaoZunEsApi()){
            log.warn("响应宝尊要求, 接口已停用");
            return 0;
        }
        //初始化本次批处理
        BaoZunBatchEntity batch = Builder.of(BaoZunBatchEntity::new)
                .with(BaoZunBatchEntity::setBatchType, BaoZunBatchTypeEnum.FULL.getCode())
                .with(BaoZunBatchEntity::setState, BaoZunBatchStateEnum.INIT.getCode())
                .with(BaoZunBatchEntity::setFkTenantId, fkTenantId)
                .with(BaoZunBatchEntity::setMsgId, UUID.randomUUID().toString().replace("-", ""))
                .with(BaoZunBatchEntity::setLastExecuteTime, LocalDateTime.now())
                .with(BaoZunBatchEntity::setCreateTime, LocalDateTime.now())
                .build();
        baoZunBatchService.saveBaoZunBatch(batch);
        //查询当前库存写入t_baozun_flow表
        this.saveCurrentStockByBatch(batch.getId(), fkTenantId);
        baoZunBatchService.updateBatchState(batch.getId(), BaoZunBatchStateEnum.DATA_READY.getCode());

        List<BaoZunFlowEntity> flowEntityList = baoZunFlowService.listBaoZunFullSkuInventories(batch.getId());

        //更新退货
        List<BaoZunFlowEntity> listRefund = baoZunFlowService.listRefund(fkTenantId, batch.getId());
        if (!CollectionUtils.isEmpty(listRefund)) {
            listRefund.forEach(baoZunFlowService::updateBaoZunFlowWithRefund);
        }
        AppInfoDTO appInfo = getAppInfoDTO(fkTenantId);
        appInfo.setRequestUrl(url);
        final String sourceSys = appInfo.getTenantName();
        if (!CollectionUtils.isEmpty(flowEntityList)) {
            List<FullSkuInventory> fullSkuInventory = flowEntityList.stream()
                    .map(it -> {
                        if (!StringUtils.isEmpty(it.getWarehouseCode())) {
                            it.setWarehouseCode(it.getWarehouseCode().trim());
                        }
                        return baoZunMapStruct.toFullSkuInventory(it, LocalDateTime.now(), sourceSys);
                    })
                    .collect(Collectors.toList());
            //分批推送批量同步数据
            CommonUtils.bigDataPage(NumberConstant.ONE_THOUSAND, fullSkuInventory, fsiList -> httpStockToBaozun(batch, appInfo, fsiList));

            return flowEntityList.size();
        }
        baoZunBatchService.updateBatchState(batch.getId(), BaoZunBatchStateEnum.SUCCESS.getCode());
        return 0;
    }

    private String httpStockToBaozun(BaoZunBatchEntity batch, AppInfoDTO appInfo, List<FullSkuInventory> fullSkuInventory) {
        FullSkuReqDTO reqDTO = Builder.of(FullSkuReqDTO::new)
                .with(FullSkuReqDTO::setMsgId, batch.getMsgId())
                .with(FullSkuReqDTO::setSourceMarkCode, SOURCE_MARK_CODE)
                .with(FullSkuReqDTO::setFullSkuInventory, fullSkuInventory)
                .build();

        // 创建日志
        BaoZunFlowLog flowLog = new BaoZunFlowLog();
        // 发送器请求
        String request = null;
        String result = null;
        try {
            request = JsonUtil.object2JsonNonNull(reqDTO);
            result = BaoZunHttpClient.fullPost(request, appInfo);
            updateState(result, batch);

        } catch (BaoZunApiException | JSONException e) {
            log.error(e.getMessage(), e);
            baoZunBatchService.updateBatchState(batch.getId(), BaoZunBatchStateEnum.FAILED.getCode());
            constructBaoZunFlowLog(flowLog, batch.getId(), LogTypeEnum.UPLOAD_FULL, e.getMessage(), e.getMessage());
            baoZunFlowLogService.saveLog(flowLog);
        } finally {
            constructBaoZunFlowLog(flowLog, batch.getId(), LogTypeEnum.UPLOAD_FLOW, request, result);
            baoZunFlowLogService.saveLog(flowLog);
        }
        return result;
    }

    @Override
    public Integer retryStockFlow(Integer fkTenantId, Long batchId) {
        if(CommonUtil.closeBaoZunEsApi()){
            log.warn("响应宝尊要求, 接口已停用");
            return 0;
        }
        //查询重试的批次
        BaoZunBatchEntity batch = baoZunBatchService.getByBatchId(batchId);
        if (Objects.isNull(batch)) {
            log.info("批处理为空");
            return 0;
        }
        if (!BaoZunBatchTypeEnum.INCREMENT.getCode().equals(batch.getBatchType())) {
            log.info("批处理的类型不是增量库存");
            return 0;
        }
        if (BaoZunBatchStateEnum.SUCCESS.getCode().equals(batch.getState())) {
            return 0;
        }

        //上传数据到宝尊
        List<BaoZunFlowEntity> flowEntityList = baoZunFlowService.listBaoZunChangeQty(batch.getId());
        AppInfoDTO appInfo = getAppInfoDTO(fkTenantId);
        appInfo.setRequestUrl(url);
        final String sourceSys = appInfo.getTenantName();
        if (!CollectionUtils.isEmpty(flowEntityList)) {
            List<String> uniqueKeyList = flowEntityList.stream().map(BaoZunFlowEntity::getUniqueKey).collect(Collectors.toList());
            List<String> existList = CommonUtils.bigDataInQuery(uniqueKeyList, uniqueKey -> baoZunFlowService.lambdaQuery()
                    .in(BaoZunFlowEntity::getUniqueKey, uniqueKey)
                    .gt(BaoZunFlowEntity::getCreateTime, LocalDateTime.now().minusDays(7))
                    .ne(BaoZunFlowEntity::getBatchId, batchId)
                    .list().stream().map(BaoZunFlowEntity::getUniqueKey).collect(Collectors.toList()));
            List<InvChangeQtyCommand> invChangeQtyCommandList = flowEntityList.stream()
                    .filter(x -> !existList.contains(x.getUniqueKey()))
                    .map((BaoZunFlowEntity it) -> {
                        if (!StringUtils.isEmpty(it.getWarehouseCode())) {
                            it.setWarehouseCode(it.getWarehouseCode().trim());
                        }
                        InvChangeQtyCommand invChangeQtyCommand = baoZunMapStruct.toInvChangeQtyCommand(it, it.getCreateTime(), sourceSys);
                        invChangeQtyCommand.setSourceDocNo(invChangeQtyCommand.getPfDocNo());
                        return invChangeQtyCommand;
                    })
                    .collect(Collectors.toList());

            for (InvChangeQtyCommand invChangeQtyCommand : invChangeQtyCommandList) {
                String baozunFlowId = invChangeQtyCommand.getBaozunFlowId();
                String docType = invChangeQtyCommand.getDocType();
                LambdaUpdateChainWrapper<BaoZunFlowEntity> updateChainWrapper = baoZunFlowService.lambdaUpdate()
                        .eq(BaoZunFlowEntity::getId, baozunFlowId);
                updateChainWrapper.set(BaoZunFlowEntity::getDocType, docType);
                updateChainWrapper.update();
            }
            List<InvChangeQtyCommand> synInvChangeQtyCommandList = invChangeQtyCommandList.stream()
                    .filter(x -> {
                        if (!StringUtils.isEmpty(x.getSn())
                                && (BaoZunDocTypeEnum.SUB_IN.getMessage().equals(x.getDocType())
                                || BaoZunDocTypeEnum.TRANSFER_IN.getMessage().equals(x.getDocType()))) {
                            return false;
                        }
                        return true;
                    }).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(synInvChangeQtyCommandList)){
                baoZunBatchService.updateBatchState(batch.getId(), BaoZunBatchStateEnum.ERROR.getCode());
                return 0;
            }
            FlowReqDTO flowReqDTO = Builder.of(FlowReqDTO::new)
                    .with(FlowReqDTO::setSourceMarkCode, SOURCE_MARK_CODE)
                    .with(FlowReqDTO::setInvChangeQtyCommand, synInvChangeQtyCommandList)
                    .build();
            // 初始化日志，给创建时间赋值
            BaoZunFlowLog flowLog = new BaoZunFlowLog();
            // 发送请求
            String request = JsonUtil.object2JsonNonNull(flowReqDTO);
            try {
                boolean executeFlag = baoZunBatchService.addBatchTryCount(batch.getId());
                if (executeFlag) {
                    String result = BaoZunHttpClient.flowPost(request, appInfo);
                    updateState(result, batch);
                    constructBaoZunFlowLog(flowLog, batch.getId(), LogTypeEnum.UPLOAD_FLOW, request,
                            result);
                    baoZunFlowLogService.saveLog(flowLog);
                }
            } catch (BaoZunApiException e) {
                constructBaoZunFlowLog(flowLog, batch.getId(), LogTypeEnum.UPLOAD_FLOW, request,
                        e.getMessage());
                baoZunFlowLogService.saveLog(flowLog);

                log.error(e.getMessage(), e);
                baoZunBatchService.updateBatchState(batch.getId(), BaoZunBatchStateEnum.FAILED.getCode());
                throw new BaoZunApiException("请求失败", e.getMessage());
            }
            //更新批次状态
            return flowEntityList.size();
        }
        baoZunBatchService.updateBatchState(batch.getId(), BaoZunBatchStateEnum.SUCCESS.getCode());
        return 0;
    }

    @Override
    public Integer retryStockAll(Integer fkTenantId, Long batchId) {
        if(CommonUtil.closeBaoZunEsApi()){
            log.warn("响应宝尊要求, 接口已停用");
            return 0;
        }
        //查询重试的批次
        BaoZunBatchEntity batch = baoZunBatchService.getByBatchId(batchId);
        Assert.notNull(batch, "批处理为空");
        Assert.state(BaoZunBatchTypeEnum.FULL.getCode().equals(batch.getBatchType()), "批处理的类型不是全量同步");
        Assert.state(!BaoZunBatchStateEnum.SUCCESS.getCode().equals(batch.getState()), "批处理已经成功");

        List<BaoZunFlowEntity> flowEntityList = baoZunFlowService.listBaoZunFullSkuInventories(batch.getId());
        AppInfoDTO appInfo = getAppInfoDTO(fkTenantId);
        appInfo.setRequestUrl(url);
        final String sourceSys = appInfo.getTenantName();
        if (!CollectionUtils.isEmpty(flowEntityList)) {

            List<String> uniqueKeyList = flowEntityList.stream().map(BaoZunFlowEntity::getUniqueKey).collect(Collectors.toList());
            List<String> existList = CommonUtils.bigDataInQuery(uniqueKeyList, uniqueKey -> baoZunFlowService.lambdaQuery()
                    .in(BaoZunFlowEntity::getUniqueKey, uniqueKey)
                    .gt(BaoZunFlowEntity::getCreateTime, LocalDateTime.now().minusDays(7))
                    .ne(BaoZunFlowEntity::getBatchId, batchId)
                    .list().stream().map(BaoZunFlowEntity::getUniqueKey).collect(Collectors.toList()));
            List<FullSkuInventory> fullSkuInventory = flowEntityList.stream()
                    .filter(x -> !existList.contains(x.getUniqueKey()))
                    .map(it -> {
                        if (!StringUtils.isEmpty(it.getWarehouseCode())) {
                            it.setWarehouseCode(it.getWarehouseCode().trim());
                        }
                        return baoZunMapStruct.toFullSkuInventory(it, LocalDateTime.now(), sourceSys);
                    })
                    .collect(Collectors.toList());
            FullSkuReqDTO reqDTO = Builder.of(FullSkuReqDTO::new)
                    .with(FullSkuReqDTO::setMsgId, batch.getMsgId())
                    .with(FullSkuReqDTO::setSourceMarkCode, SOURCE_MARK_CODE)
                    .with(FullSkuReqDTO::setFullSkuInventory, fullSkuInventory)
                    .build();

            // 创建日志
            BaoZunFlowLog flowLog = new BaoZunFlowLog();
            String request = JsonUtil.object2JsonNonNull(reqDTO);

            // 发送器请求
            try {
                String result = BaoZunHttpClient.fullPost(request, appInfo);
                updateState(result, batch);
                constructBaoZunFlowLog(flowLog, batch.getId(), LogTypeEnum.UPLOAD_FLOW, request,
                        result);
                baoZunFlowLogService.saveLog(flowLog);
            } catch (BaoZunApiException | JSONException e) {
                log.error(e.getMessage(), e);
                baoZunBatchService.updateBatchState(batch.getId(), BaoZunBatchStateEnum.FAILED.getCode());
                constructBaoZunFlowLog(flowLog, batch.getId(), LogTypeEnum.UPLOAD_FULL, request, e.getMessage());
                baoZunFlowLogService.saveLog(flowLog);
            }

            return flowEntityList.size();
        }
        baoZunBatchService.updateBatchState(batch.getId(), BaoZunBatchStateEnum.SUCCESS.getCode());
        return 0;
    }

    @Override
    public Integer autoRetryStockFlow(Integer fkTenantId) {
        List<Long> retryList = getRetryList();
        if (CollectionUtils.isEmpty(retryList)) {
            return 0;
        }
        baoZunBatchMapper.updateState(retryList);
        retryList.forEach((Long x) -> this.retryStockFlow(fkTenantId, x));
        return retryList.size();
    }

    private List<Long> getRetryList() {
        List<Long> retryList = baoZunBatchMapper.
                getRetryList().stream()
                .distinct().collect(Collectors.toList());
        return retryList;
    }

    /**
     * 分页保存当前库存
     *
     * @param batchId    批处理Id
     * @param fkTenantId 租户Id
     */
    private void saveCurrentStockByBatch(Long batchId, Integer fkTenantId) {
        int pageSize = WRITE_BATCH_SIZE;
        //1、查询配件的总记录条数
        int totalCount = baoZunFlowService.countAccessoryAreaPpid(fkTenantId);
        //2、分页循环
        int toIndex = pageSize;
        for (int i = 0; i < totalCount; i += pageSize) {
            if (i + pageSize > totalCount) {
                toIndex = totalCount - i;
            }
            //3、小件库存
            List<StockFlowVo> accessoryStockFlowVoList = baoZunFlowService.searchAccessoryStock(fkTenantId, i + 1, i + toIndex);
            //写入t_baozun_flow表
            if (!CollectionUtils.isEmpty(accessoryStockFlowVoList)) {
                List<BaoZunFlowEntity> collect = accessoryStockFlowVoList.stream().map(it -> baoZunMapStruct.toBaoZunFlowEntity(batchId, it))
                        .collect(Collectors.toList());
                collect.stream().peek(x -> x.setUniqueKey(x.businessMd5()));
                int result = baoZunFlowService.saveBatchWithAffectRows(
                        collect);
//                Assert.state(accessoryStockFlowVoList.size() == result, "小件当前库存保存失败");
            }
        }
        totalCount = baoZunFlowService.countMobileAreaPpid(fkTenantId);
        toIndex = pageSize;
        for (int i = 0; i < totalCount; i += pageSize) {
            if (i + pageSize > totalCount) {
                toIndex = totalCount - i;
            }
            //4、大件库存
            List<StockFlowVo> mkcStockFlowVoList = baoZunFlowService.searchMobileStock(fkTenantId, i + 1, i + toIndex);
            mkcStockFlowVoList = mkcStockFlowVoList.stream().distinct().collect(Collectors.toList());
            //写入t_baozun_flow表
            if (!CollectionUtils.isEmpty(mkcStockFlowVoList)) {
                int result = baoZunFlowService.saveBatchWithAffectRows(
                        mkcStockFlowVoList.stream().map(it -> {
                            BaoZunFlowEntity baoZunFlowEntity = baoZunMapStruct.toBaoZunFlowEntity(batchId, it);
                            Boolean mouldFlag = it.getMouldFlag();
                            Boolean xcFlag = it.getXcFlag();

                            if (!mouldFlag && !xcFlag) {
                                baoZunFlowEntity.setInvStatusCode(1);
                            } else if (!mouldFlag) {
                                baoZunFlowEntity.setInvStatusCode(2);
                            } else if (!xcFlag) {
                                baoZunFlowEntity.setInvStatusCode(6);
                            }
                            return baoZunFlowEntity;
                        }).peek(x -> x.setUniqueKey(x.businessMd5())).distinct().collect(Collectors.toList()));
//                Assert.state(mkcStockFlowVoList.size() == result, "大件当前库存保存失败");
            }
        }
    }

    private void updateState(String result, BaoZunBatchEntity batch) {
        BaoZunResponse baoZunResponse = JSONUtil.toBean(result, BaoZunResponse.class);
        if (BaoZunResponseEnum.SUCCESS.getCode().equals(baoZunResponse.getResponse())) {
            baoZunBatchService.updateBatchState(batch.getId(), BaoZunBatchStateEnum.SUCCESS.getCode());
        } else {
            baoZunBatchService.updateBatchState(batch.getId(), BaoZunBatchStateEnum.FAILED.getCode());
        }
    }
}
