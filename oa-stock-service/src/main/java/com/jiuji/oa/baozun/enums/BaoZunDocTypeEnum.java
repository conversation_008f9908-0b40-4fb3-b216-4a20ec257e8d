package com.jiuji.oa.baozun.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 宝尊单据类型
 */
@AllArgsConstructor
@Getter
public enum BaoZunDocTypeEnum implements CodeMessageEnumInterface {

    /**
     * 单据类型
     */
    SUB_IN(1, "T04"),
    SUB_OUT(2, "T05"),
    TRANSFER_IN(3, "T42"),
    TRANSFER_OUT(4, "T43"),
    ORDER_OUT(5, "T45"),
    <PERSON><PERSON>E_OUT(6, "T45"),
    RETURN_IN(7, "T45"),
    <PERSON><PERSON>(8, "T45"),
    Y<PERSON><PERSON><PERSON>(9, "T45"),
    XC_IN(10, "T45"),
    XC_OUT(11, "T45"),
    AREA_SELL_OUT_NOT_RETAIL(12, "T58"),
    AREA_RETURN_IN_NOT_RETAIL(13, "T59"),
    AREA_SELL_OUT_RETAIL(14, "T60"),
    AREA_RETURN_IN_RETAIL(15, "T61"),
    CHANGE_IMEI_OUT(16, "T45"),
    CHANGE_IMEI_IN(17, "T45"),
    ;

    /**
     * 编码
     */
    private final Integer code;
    /**
     * 编码对应信息
     */
    private final String message;
}
