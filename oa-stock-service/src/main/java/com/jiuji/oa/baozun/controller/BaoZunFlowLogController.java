package com.jiuji.oa.baozun.controller;

import com.jiuji.oa.baozun.entity.BaoZunFlowLog;
import com.jiuji.oa.baozun.service.IBaoZunFlowLogService;
import com.jiuji.tc.common.vo.R;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
public class BaoZunFlowLogController {
    @Resource
    private IBaoZunFlowLogService service;

    @PostMapping("/savelog")
    public R<String> saveLog(@RequestBody BaoZunFlowLog baoZunFlowLog) {
        service.saveLog(baoZunFlowLog);
        return R.success("插入成功");
    }
}
