package com.jiuji.oa.baozun.controller;


import cn.hutool.extra.spring.SpringUtil;
import com.jiuji.oa.baozun.service.BzTenantCloudStoreService;
import com.jiuji.oa.baozun.service.IBaoZunBusService;
import com.jiuji.oa.nc.common.util.CommonUtil;
import com.jiuji.oa.stock.common.aspect.RepeatSubmitCheck;
import com.jiuji.tc.common.vo.R;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 宝尊出入库同步流水表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-07
 */
@RestController
@RequestMapping("/api/baozun")
@RequiredArgsConstructor
public class BaoZunFlowController {

    public static final String UPLOAD_SUCCESS = "上传成功:";
    private final IBaoZunBusService baoZunBusService;


    /**
     * 每5分钟同步
     *
     * @param fkTenantId
     * @return
     */
    @GetMapping("/synchronize-stock-flow/{fkTenantId}")
    @RepeatSubmitCheck(expression = "#{packageFullName}:synchronize-stock-flow:#{fkTenantId}")
    public R<String> synchronizeStockFlow(@PathVariable Integer fkTenantId) {
        if(CommonUtil.closeBaoZunEsApi()){
            return R.error("响应宝尊要求, 接口已停用");
        }
        Integer i = baoZunBusService.synchronizeStockFlow(fkTenantId);
        return R.success(UPLOAD_SUCCESS + i + "条");
    }

    /**
     * 每天夜里12点30同步全量
     *
     * @param fkTenantId
     * @return
     */
    @GetMapping("/synchronize-all-stock/{fkTenantId}")
    @RepeatSubmitCheck(expression = "#{packageFullName}:synchronize-stock-flow:#{fkTenantId}")
    public R<String> synchronizeStockAll(@PathVariable Integer fkTenantId) {
        if(CommonUtil.closeBaoZunEsApi()){
            return R.error("响应宝尊要求, 接口已停用");
        }
        Integer i = baoZunBusService.synchronizeAllStock(fkTenantId);
        return R.success(UPLOAD_SUCCESS + i + "条");
    }


    /**
     * 每5分钟同步
     *
     * @param fkTenantId
     * @return
     */
    @GetMapping("/retry-stock-flow/{fkTenantId}/{batchId}")
    @RepeatSubmitCheck(expression = "#{packageFullName}:synchronize-stock-flow:#{fkTenantId}")
    public R<String> retryStockFlow(@PathVariable Integer fkTenantId, @PathVariable Long batchId) {
        if(CommonUtil.closeBaoZunEsApi()){
            return R.error("响应宝尊要求, 接口已停用");
        }
        Integer i = baoZunBusService.retryStockFlow(fkTenantId, batchId);
        return R.success(UPLOAD_SUCCESS + i + "条");
    }

    /**
     * 每5分钟同步
     *
     * @param fkTenantId
     * @return
     */
    @GetMapping("/auto-retry-stock-flow/{fkTenantId}")
    @RepeatSubmitCheck(expression = "#{packageFullName}:synchronize-stock-flow:#{fkTenantId}")
    public R<String> autoRetryStockFlow(@PathVariable Integer fkTenantId) {
        if(CommonUtil.closeBaoZunEsApi()){
            return R.error("响应宝尊要求, 接口已停用");
        }
        Integer i = baoZunBusService.autoRetryStockFlow(fkTenantId);
        return R.success(UPLOAD_SUCCESS + i + "条");
    }

    /**
     * 每天夜里12点30同步全量
     *
     * @param fkTenantId
     * @return
     */
    @GetMapping("/retry-all-stock/{fkTenantId}/{batchId}")
    @RepeatSubmitCheck(expression = "#{packageFullName}:synchronize-stock-flow:#{fkTenantId}")
    public R<String> retryStockAll(@PathVariable Integer fkTenantId, @PathVariable Long batchId) {
        if(CommonUtil.closeBaoZunEsApi()){
            return R.error("响应宝尊要求, 接口已停用");
        }
        Integer i = baoZunBusService.retryStockAll(fkTenantId, batchId);
        return R.success(UPLOAD_SUCCESS + i + "条");
    }

    /**
     * 云仓小件采购单入库
     * @return
     */
    @GetMapping("/cloudstore/inputSmallKc")
    @RepeatSubmitCheck(expression = "#{packageFullName}:cloudStoreInputSmallKc:#{caigouId}")
    public R cloudStoreInputSmallKc(@RequestParam("caigouId") Integer caigouId, @RequestParam("inUser") String inUser) {
        return SpringUtil.getBean(BzTenantCloudStoreService.class).autoInputSmallKc(caigouId, inUser, "宝尊云仓");
    }
}

