package com.jiuji.oa.baozun.dto.req.skufull;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 全量库存更新请求 DTO
 *
 * <AUTHOR>
 * @date 2021/09/02
 */
@Data
public class FullSkuReqDTO implements Serializable {

    /**
     * 默认值，ERP请传输"routeIosp_om"
     */
    private String sourceMarkCode;
    /**
     * 标识本批次请求数据的唯一值，数据分批传输，一批次一个值。
     */

    private String msgId;
    /**
     * 全量库存更新列表
     */
    private List<FullSkuInventory> fullSkuInventory;
}
