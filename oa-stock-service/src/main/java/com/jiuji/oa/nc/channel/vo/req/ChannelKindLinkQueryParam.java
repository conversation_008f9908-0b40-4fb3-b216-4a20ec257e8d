package com.jiuji.oa.nc.channel.vo.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 渠道类型关系查询参数
 *
 * <AUTHOR>
 * @date 2024
 */
@Data
@ApiModel(value = "渠道类型关系查询参数")
public class ChannelKindLinkQueryParam {

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private Integer kind;

    /**
     * 开票标识
     */
    @ApiModelProperty(value = "开票标识")
    private Integer invoicingFlag;

    /**
     * 合作状态
     */
    @ApiModelProperty(value = "合作状态")
    private Integer passFlag;

    /**
     * 合作公司ID
     */
    @ApiModelProperty(value = "合作公司ID")
    private Integer cooperationId;
} 