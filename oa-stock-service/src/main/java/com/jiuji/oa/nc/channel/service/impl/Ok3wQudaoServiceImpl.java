/*
 *     Copyright © 2006 - 2020 九机网 All Rights Reserved
 *
 */
package com.jiuji.oa.nc.channel.service.impl;


import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ch999.common.util.utils.XtenantJudgeUtil;
import com.ch999.common.util.vo.Result;
import com.google.common.collect.Lists;
import com.jiuji.cloud.office.service.ContractCloud;
import com.jiuji.cloud.office.vo.ContractInfoVO;
import com.jiuji.oa.apollo.WuliuApolloConfig;
import com.jiuji.oa.applyinfo.service.IApplyInfoService;
import com.jiuji.oa.applyinfo.vo.req.ApplyInfoReq;
import com.jiuji.oa.finance.CaiwuApplyClient;
import com.jiuji.oa.finance.VoucherRecordCloud;
import com.jiuji.oa.finance.vo.req.SupplierEnableVO;
import com.jiuji.oa.logapi.pojo.dto.req.Ok3wQudaoLogReq;
import com.jiuji.oa.logapi.service.IOk3wQuDaoLogService;
import com.jiuji.oa.nc.bbsxpusers.po.BbsxpUsers;
import com.jiuji.oa.nc.bbsxpusers.service.BbsxpUsersService;
import com.jiuji.oa.nc.channel.config.ChannelConfig;
import com.jiuji.oa.nc.channel.constant.ChannelRankConstant;
import com.jiuji.oa.nc.channel.document.Ok3wQudaoLogDocument;
import com.jiuji.oa.nc.channel.document.Ok3wQudaoLogDocument.Conts;
import com.jiuji.oa.nc.channel.entity.*;
import com.jiuji.oa.nc.channel.enums.*;
import com.jiuji.oa.nc.channel.mapper.Ok3wQudaoMapper;
import com.jiuji.oa.nc.channel.mapstruct.ChannelMapStruct;
import com.jiuji.oa.nc.channel.repository.Ok3wQudaoLogRepository;
import com.jiuji.oa.nc.channel.service.*;
import com.jiuji.oa.nc.channel.vo.dto.*;
import com.jiuji.oa.nc.channel.vo.req.*;
import com.jiuji.oa.nc.channel.vo.res.*;
import com.jiuji.oa.nc.common.bo.OaUserBO;
import com.jiuji.oa.nc.common.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.nc.common.constant.AttachmentsTypeEnum;
import com.jiuji.oa.nc.common.constant.DataSourceConstants;
import com.jiuji.oa.nc.common.constant.RedisKeys;
import com.jiuji.oa.nc.common.db.MyDynamicRoutingDataSource;
import com.jiuji.oa.nc.common.enums.XtenantEnum;
import com.jiuji.oa.nc.common.exception.CustomizeException;
import com.jiuji.oa.nc.common.req.OaAttachmentsReq;
import com.jiuji.oa.nc.common.req.OaAttachmentsReq.FileBO;
import com.jiuji.oa.nc.dict.enums.ConfigEnum;
import com.jiuji.oa.nc.dict.service.ISysConfigService;
import com.jiuji.oa.nc.oaapp.po.SysConfig;
import com.jiuji.oa.nc.stock.entity.Brand;
import com.jiuji.oa.nc.stock.entity.Category;
import com.jiuji.oa.nc.stock.service.BrandService;
import com.jiuji.oa.nc.stock.service.CategoryService;
import com.jiuji.oa.nc.user.po.Areainfo;
import com.jiuji.oa.nc.user.po.Authorize;
import com.jiuji.oa.nc.user.service.AuthorizeService;
import com.jiuji.oa.nc.user.service.IAreaInfoService;
import com.jiuji.oa.office.api.AuthorizeSwitchClient;
import com.jiuji.oa.stock.chw.enums.ChwUserInfoEnum;
import com.jiuji.oa.stock.common.entity.AreaListEntity;
import com.jiuji.oa.stock.common.service.IAreaListService;
import com.jiuji.oa.stock.common.util.CodeExtensionUtils;
import com.jiuji.oa.stock.common.util.OptionalUtils;
import com.jiuji.oa.stock.insource.service.IInSourceService;
import com.jiuji.oa.stock.logistics.order.entity.Attachments;
import com.jiuji.oa.stock.logistics.order.service.AttachmentsService;
import com.jiuji.oa.stock.purchase.enums.PaymentTypeEnum;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.foundation.message.send.service.MessageSendService;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.enums.EnumUtil;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 渠道ServiceImpl
 *
 * <AUTHOR>
 * @date 2021-03-01 19:04:25
 */
@Slf4j
@Service
@DS("oanewWrite")
@RequiredArgsConstructor
public class Ok3wQudaoServiceImpl extends ServiceImpl<Ok3wQudaoMapper, Ok3wQudao> implements Ok3wQudaoService {

    private static final String ENDORSEMENT_ADDRESS = " <a target='_blank' href='/office/Apply/Detail.aspx?id=";
    private static final Integer ENABLE_ACCOUNTS = 1;
    private static final String CONFIG_AUTHORIZED = "1";
    private final AbstractCurrentRequestComponent requestComponent;
    private final CaiwuApplyClient caiwuApplyClient;
    private final ChannelKindLinkService channelKindLinkService;
    private final QudaoaccountsService qudaoaccountsService;
    private final QudaocontactssService qudaocontactssService;
    private final BrandService brandService;
    private final CategoryService categoryService;
    private final ChannelMapStruct channelMapStruct;
    private final IApplyInfoService applyInfoService;
    private final BbsxpUsersService bbsxpUsersService;
    private final AttachmentsService attachmentsService;
    private final StringRedisTemplate stringRedisTemplate;
    private final ChannelProvinceCityService channelProvinceCityService;
    private final IAreaInfoService areainfoService;
    private final ISysConfigService sysConfigService;
    private final Ok3wQudaoLogRepository ok3wQudaoLogRepository;
    private final CodeExtensionUtils codeExtensionUtils;
    private final WuliuApolloConfig wuliuApolloConfig;
    //合作公司
    private static final Integer PARTNER_COMPANY = 44;
    //供应商财务
    private static final Integer SUPPLIER_FINANCE = 92;
    // sysconfig表里的域名的code
    private static final Integer DOMAIN_NAME_CODE = 19;
    private static final Integer OA_DOMAIN_NAME_CODE = 17;
    private static final Integer DOMAIN_NAME_OAWCF_CODE = 20;
    private final ChannelConfig channelConfig;
    private final ContractCloud contractCloud;
    private static final Integer SET_PARTITION = 2000;
    @Resource
    @Lazy
    private RedissonClient redissonClient;
    public static final String CHANNEL_REDIS_KEY = "qudaoUser";
    private final IOk3wQuDaoLogService quDaoLogService;

    private static final Long CHANNEL_LIST_MAXSIZE = 100L;
    private static final String CHANNEL_AUTHORIZE = "qudao";
    private static final String CHANNEL_STOCKTAKING = "qdpd";
    private final AuthorizeSwitchClient authorizeSwitchClient;
    private final IInSourceService inSourceService;
    private final AuthorizeService authorizeService;
    private final MessageSendService messageSendService;
    private final VoucherRecordCloud voucherRecordCloud;
    private final IAreaListService areaListService;

    private static final String OPEN_VALUE = "1";

    /**
     * 渠道公司全称和简称的重复校验
     * @param checkRepeatVo
     */
    @Override
    public void checkCompanyAndCompanyJc(CheckRepeatVo checkRepeatVo) {
        //判断是否开启授权隔离
        OaUserBO userBO = requestComponent.getCurrentStaffId();
        //获取渠道授权
        R<Boolean> booleanR = getChannelAuthorizeConfig(userBO);
        if (booleanR != null && Boolean.TRUE.equals(booleanR.getData())) {
            checkRepeatVo.setAuthorizeId(userBO.getAuthorizeId());
        }
        checkQudaoList(checkRepeatVo);
    }

    @Override
    public void checkChannelLink(ChannelVo channelVo) {
        List<FinanceDto> financeList = channelVo.getFinanceList();
        if(CollectionUtils.isEmpty(financeList)){
            return;
        }
        List<ChannelKindLink> list = channelKindLinkService.lambdaQuery().eq(ChannelKindLink::getChannelId, channelVo.getId()).list();
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        //数据库原有的业务类型
        Map<Integer, ChannelKindLink> linkMap = list.stream().collect(Collectors.toMap(ChannelKindLink::getKind, Function.identity(), (n1, n2) -> n2));
        //判断新加的业务类型数据库是否存在
        financeList.forEach(item->{
            ChannelKindLink kindLink = item.getKindLink();
            Integer id = kindLink.getId();
            if(ObjectUtil.isNull(id)){
                ChannelKindLink channelKindLink = linkMap.get(kindLink.getKind());
                if(ObjectUtil.isNotNull(channelKindLink)){
                    if(XtenantEnum.isJiujiXtenant()){
                        throw new CustomizeException("业务类型："+ChannelKindEnum.getValue(channelKindLink.getKind())+"已经存在");
                    } else {
                        throw new CustomizeException("业务类型："+ChannelKindSaasEnum.getValue(channelKindLink.getKind())+"已经存在");
                    }
                }
            }
        });
    }

    /**
     *
     * @param checkRepeatVo
     */
    private void checkQudaoList(CheckRepeatVo checkRepeatVo){
        Integer authorizeId = checkRepeatVo.getAuthorizeId();
        String companyJc = checkRepeatVo.getCompanyJc();
        String company = checkRepeatVo.getCompany();
        Integer channelId = checkRepeatVo.getId();

        if(StringUtils.isNotEmpty(companyJc)){
            //可以替换大部分空白字符， 不限于空格
            List<Ok3wQudao> list =this.lambdaQuery().eq(ObjectUtil.isNotEmpty(authorizeId), Ok3wQudao::getAuthorizeid, authorizeId)
                    .ne(channelId != null, Ok3wQudao::getId, channelId)
                    .eq(Ok3wQudao::getCompanyJc, companyJc.replaceAll("\\s*", ""))
                    .list();
            if(CollectionUtils.isNotEmpty(list)){
                throw new CustomizeException("公司简称已存在，请重新输入");
            }
        }
        if(StringUtils.isNotEmpty(company)){
            //可以替换大部分空白字符， 不限于空格
            List<Ok3wQudao> list =this.lambdaQuery().eq(ObjectUtil.isNotEmpty(authorizeId), Ok3wQudao::getAuthorizeid, authorizeId)
                    .ne(channelId != null, Ok3wQudao::getId, channelId)
                    .eq(Ok3wQudao::getCompany, company.replaceAll("\\s*", ""))
                    .list();
            if(CollectionUtils.isNotEmpty(list)){
                throw new CustomizeException("公司名称已存在，请重新输入");
            }
        }
    }



    /**
     * 获取到sysconfig表里  44 92的name
     *
     * @return
     */
    @Override
    public Map<Integer, List<SysConfig>> selectChannelSysconfigInfo(Integer xTent, Integer authorizeId) {

        HashMap<Integer, List<SysConfig>> map = new HashMap<>();

        //获取到code 为44的name
        List<SysConfig> list = sysConfigService.lambdaQuery().eq(SysConfig::getCode, PARTNER_COMPANY)
                .eq(SysConfig::getXtenant, xTent)
                .eq(authorizeId != null, SysConfig::getAuthId, authorizeId)
                .list();
        map.put(PARTNER_COMPANY, list);
        //获取到code 为92的name
        List<SysConfig> list1 = sysConfigService.lambdaQuery().eq(SysConfig::getCode, SUPPLIER_FINANCE)
                .eq(SysConfig::getXtenant, xTent)
                .eq(authorizeId != null, SysConfig::getAuthId, authorizeId)
                .list();
        map.put(SUPPLIER_FINANCE, list1);
        return map;
    }


    @Override
    public Ok3wQudaoLogDocument addChannelLog(Integer channelId, String comment, String inUser, LocalDateTime dTime) {
        if (channelId == null || StringUtils.isBlank(comment) || StringUtils.isBlank(inUser) || dTime == null) {
            return null;
        }
        LocalDateTime localDateTime = LocalDateTime.now();
        Ok3wQudaoLogReq ok3wQudaoLogReq = new Ok3wQudaoLogReq();
        ok3wQudaoLogReq.setComment(comment)
                .setDisplayId(channelId.longValue())
                .setInUser(inUser)
                .setDTime(localDateTime);
        quDaoLogService.insertQuDaoLog(ok3wQudaoLogReq);

        Ok3wQudaoLogDocument ok3wQudaoLogDocument = new Ok3wQudaoLogDocument();
        List<Conts> contList = new ArrayList<>();
        Conts cont = new Conts();
        cont.setDTime(ok3wQudaoLogReq.getDTime());
        cont.setComment(ok3wQudaoLogReq.getComment());
        cont.setDisplayId(ok3wQudaoLogReq.getDisplayId().intValue());
        cont.setInUser(ok3wQudaoLogReq.getInUser());
        contList.add(cont);
        ok3wQudaoLogDocument.setId(channelId);
        ok3wQudaoLogDocument.setConts(contList);

        // todo:双写，过渡
        // 日志的id和渠道的id是同一个
        // 因为修改渠道会在原来日志的基础上，加一条日志。所以每次需要在原来的conts的基础上，添加一条。如果原来没有就新建。
        Ok3wQudaoLogDocument logDocument;
        Optional<Ok3wQudaoLogDocument> oldLogDocument = ok3wQudaoLogRepository.findById(channelId);
        //获取到老的日志
        logDocument = oldLogDocument.orElseGet(Ok3wQudaoLogDocument::new);
        Conts logCont = new Conts();
        logCont.setComment(comment);
        logCont.setDisplayId(channelId);
        logCont.setInUser(inUser);
        logCont.setDTime(dTime);
        logDocument.setId(channelId);
        if (CollectionUtils.isEmpty(logDocument.getConts())) {
            logDocument.setConts(Collections.singletonList(logCont));
        } else {
            logDocument.getConts().add(logCont);
        }

        return ok3wQudaoLogRepository.save(logDocument);
    }

    /**
     * 因为mongodb迁移所以把原有的日志迁移到tidb上所以修改方法
     *
     * @param channelId 不能为空，渠道id
     * @param comment   不能为空，日志内容
     * @param inUser    不能为空，添加人
     * @param dTime     不能为空，添加时间
     * @return
     */
    @Override
    public Ok3wQudaoLogDocument addChannelLognew(Integer channelId, String comment, String inUser, LocalDateTime dTime) {
        if (channelId == null || StringUtils.isBlank(comment) || StringUtils.isBlank(inUser) || dTime == null) {
            return null;
        }
        Ok3wQudaoLogDocument logDocument;
        Ok3wQudaoLogReq ok3wQudaoLogReq = new Ok3wQudaoLogReq();
        try {
            ok3wQudaoLogReq.setComment(comment).setDTime(dTime)
                    .setDisplayId(channelId.longValue())
                    .setInUser(inUser);
            quDaoLogService.insertQuDaoLog(ok3wQudaoLogReq);
        } catch (CustomizeException e) {
            log.error("渠道日志tidb写入失败，写出参数{}", JSONUtil.toJsonStr(ok3wQudaoLogReq), e);
        } finally {
            // todo:双写，过渡
            // 日志的id和渠道的id是同一个
            // 因为修改渠道会在原来日志的基础上，加一条日志。所以每次需要在原来的conts的基础上，添加一条。如果原来没有就新建。

            Optional<Ok3wQudaoLogDocument> oldLogDocument = ok3wQudaoLogRepository.findById(channelId);
            //获取到老的日志
            logDocument = oldLogDocument.orElseGet(Ok3wQudaoLogDocument::new);
            Conts logCont = new Conts();
            logCont.setComment(comment);
            logCont.setDisplayId(channelId);
            logCont.setInUser(inUser);
            logCont.setDTime(dTime);
            logDocument.setId(channelId);
            if (CollectionUtils.isEmpty(logDocument.getConts())) {
                logDocument.setConts(Collections.singletonList(logCont));
            } else {
                logDocument.getConts().add(logCont);
            }

            return ok3wQudaoLogRepository.save(logDocument);
        }
    }

    @Override
    public IPage<ChannelPageVo> queryChannelPage(ChannelPageReq channelPageReq, OaUserBO user) {
        if (StringUtils.isNotBlank(channelPageReq.getBrandId())) {
            channelPageReq.setBrandIdList(Arrays.asList(channelPageReq.getBrandId().split(",")));
        }

        if (CollectionUtils.isNotEmpty(channelPageReq.getProvinceCityList())) {
            List<ChannelProvinceCityDto> provinceCityList = channelPageReq.getProvinceCityList();
            provinceCityList.forEach(ChannelProvinceCityDto::cleanData);
            List<Integer> provinceIdList = provinceCityList.stream().filter(it -> it.getProvinceId() != null)
                    .map(ChannelProvinceCityDto::getProvinceId).collect(
                            Collectors.toList());
            List<Integer> cityIdList = provinceCityList.stream().filter(it -> it.getCityId() != null)
                    .map(ChannelProvinceCityDto::getCityId).collect(
                            Collectors.toList());
            List<Integer> countyIdList = provinceCityList.stream().filter(it -> it.getCountyId() != null)
                    .map(ChannelProvinceCityDto::getCountyId).collect(
                            Collectors.toList());
            channelPageReq.setProvinceIdList(provinceIdList);
            channelPageReq.setCidList(cityIdList);
            channelPageReq.setCountyIdList(countyIdList);
        }

        //根据kind查询渠道id, 过滤盘点状态查询条件
        List<ChannelKindLink> linkList = channelKindLinkService.lambdaQuery()
                .eq(channelPageReq.getKind() != null, ChannelKindLink::getKind, channelPageReq.getKind())
                .eq(Boolean.TRUE.equals(user.getIsTaxModel()), ChannelKindLink::getInvoicingFlag, 1)
//                .in(ChannelKindLink::getKind, kinds)
                .eq(channelPageReq.getPassFlag() != null, ChannelKindLink::getChannelState, channelPageReq.getPassFlag())
                .eq(channelPageReq.getCooperationId() != null
                        , ChannelKindLink::getPartnerCompany, channelPageReq.getCooperationId())
                .select(ChannelKindLink::getChannelId, ChannelKindLink::getKind).list();
        if (CollectionUtils.isEmpty(linkList)) {
            return new Page<>();
        }
        channelPageReq.setAuthorizeId(user.getAuthorizeId());
        channelPageReq.setIsTaxModel(user.getIsTaxModel());
        //分页查询
        Long CURRENT_PAGE = 10L;
        Long current = Optional.of(channelPageReq.getPage().getCurrent()).orElse(CURRENT_PAGE);
        Long PAGE_SIZE = 10L;
        Long size = Optional.of(channelPageReq.getPage().getSize()).orElse(PAGE_SIZE);
        Long end = current * size;
        Long begin = end - size + 1;
        Integer total = this.baseMapper.getTotalCount(channelPageReq);
        IPage<ChannelPageVo> channelPageVoPage = new Page<>(current, size, total);
        channelPageVoPage.setPages(total / size);

        //分类和品牌 id转换name
        List<ChannelPageVo> channelVos = this.baseMapper.queryChannelPage(channelPageReq, begin, end);
        List<Integer> cidList = channelVos.stream()
                .map(ChannelPageVo::getCids).filter(Objects::nonNull).collect(Collectors.toList());
        Map<Integer, Category> categoryMap;
        if (CollectionUtils.isNotEmpty(cidList)) {
            categoryMap = categoryService.getCategoryMap(cidList);
        } else {
            categoryMap = new HashMap<>();
        }
        List<String> brandIdStrList = channelVos.stream().map(ChannelPageVo::getBrandId)
                .filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<Integer> brandIdList = new ArrayList<>();
        brandIdStrList.forEach((String it) -> {
            String[] split = it.split(",");
            for (String s : split) {
                if (StringUtils.isNotEmpty(s)) {
                    brandIdList.add(Integer.valueOf(s));
                }
            }
        });
        Map<Integer, Brand> brandMap;
        if (CollectionUtils.isNotEmpty(brandIdStrList)) {
            brandMap = brandService.getBrandMap(brandIdList);
        } else {
            brandMap = new HashMap<>();
        }
        Map<Integer, List<ChannelKindLink>> linkMap = linkList.stream()
                .collect(Collectors.groupingBy(ChannelKindLink::getChannelId));
        channelVos.forEach((ChannelPageVo it) -> {
            Category category = Optional.ofNullable(categoryMap.get(it.getCids())).orElseGet(Category::new);
            it.setCategory(category.getName());
            it.setBrandName(brandIdToName(it.getBrandId(), brandMap));
            List<ChannelKindLink> links = linkMap.get(it.getId());
            if (CollectionUtils.isNotEmpty(links)) {
                List<Integer> collect = links.stream().map(ChannelKindLink::getKind).collect(Collectors.toList());
                if (collect.contains(ChannelKindEnum.MOBILE.getCode())
                        || collect.contains(ChannelKindEnum.MAINTENANCE_ACCESSORIES.getCode())
                        || collect.contains(ChannelKindEnum.MOBILE_ACCESSORIES.getCode())) {
                    it.setType(1);
                } else {
                    it.setType(2);
                }
                StringBuilder builder = new StringBuilder();
                Integer xTenant = user.getXTenant();
                if (xTenant == 0) {
                    collect.forEach(kind -> builder.append(ChannelKindEnum.getValue(kind)).append(','));
                } else {
                    collect.forEach(kind -> builder.append(ChannelKindSaasEnum.getValue(kind)).append(','));
                }

                it.setKinds(builder.deleteCharAt(builder.length() - 1).toString());
            }
        });
        channelPageVoPage.setRecords(channelVos);
        return channelPageVoPage;
    }

    @Override
    public IPage<ChannelPageVo> queryChannelPageV1(ChannelPageReq channelPageReq, OaUserBO user) {
        if (StringUtils.isNotBlank(channelPageReq.getBrandId())) {
            channelPageReq.setBrandIdList(Arrays.asList(channelPageReq.getBrandId().split(",")));
        }

        if (CollectionUtils.isNotEmpty(channelPageReq.getProvinceCityList())) {
            List<ChannelProvinceCityDto> provinceCityList = channelPageReq.getProvinceCityList();
            provinceCityList.forEach(ChannelProvinceCityDto::cleanData);
            List<Integer> provinceIdList = provinceCityList.stream().filter(it -> it.getProvinceId() != null)
                    .map(ChannelProvinceCityDto::getProvinceId).collect(
                            Collectors.toList());
            List<Integer> cityIdList = provinceCityList.stream().filter(it -> it.getCityId() != null)
                    .map(ChannelProvinceCityDto::getCityId).collect(
                            Collectors.toList());
            List<Integer> countyIdList = provinceCityList.stream().filter(it -> it.getCountyId() != null)
                    .map(ChannelProvinceCityDto::getCountyId).collect(
                            Collectors.toList());
            channelPageReq.setProvinceIdList(provinceIdList);
            channelPageReq.setCidList(cityIdList);
            channelPageReq.setCountyIdList(countyIdList);
        }

        //根据kind查询渠道id, 过滤盘点状态查询条件
        List<ChannelKindLink> linkList = channelKindLinkService.lambdaQuery()
                .eq(channelPageReq.getKind() != null, ChannelKindLink::getKind, channelPageReq.getKind())
                .eq(Boolean.TRUE.equals(user.getIsTaxModel()), ChannelKindLink::getInvoicingFlag, 1)
//                .in(ChannelKindLink::getKind, kinds)
                .eq(channelPageReq.getPassFlag() != null, ChannelKindLink::getChannelState, channelPageReq.getPassFlag())
                .eq(channelPageReq.getCooperationId() != null
                        , ChannelKindLink::getPartnerCompany, channelPageReq.getCooperationId())
                .select(ChannelKindLink::getChannelId, ChannelKindLink::getKind).list();
        if (CollectionUtils.isEmpty(linkList)) {
            return new Page<>();
        }

        //全局配置
        List<SysConfig> configList = sysConfigService.getSysConfig();
        //授权独立
        SysConfig authConfig = configList.stream()
                .filter(v -> ConfigEnum.AUTHORIZED.getCode().equals(v.getCode()) && user.getXTenant().equals(v.getXtenant().intValue()))
                .findFirst().orElse(new SysConfig().setValue("0"));
        //完整组织架构配置地区
        SysConfig orgConfig = configList.stream()
                .filter(v -> ConfigEnum.ORGANIZATION_AREA.getCode().equals(v.getCode()) && user.getXTenant().equals(v.getXtenant().intValue()))
                .findFirst().orElse(new SysConfig().setValue("0"));
        boolean orgAuth = Stream.of(orgConfig.getValue().split(",")).collect(Collectors.toList()).contains(user.getAreaId() + "");
        //授权独立启用且完整组织架构配置地区未配置，按照授权隔离查询数据，否则查所有数据
        if (CONFIG_AUTHORIZED.equals(authConfig.getValue()) && !orgAuth) {
            channelPageReq.setAuthorizeId(user.getAuthorizeId());
        } else {
            channelPageReq.setAuthorizeId(null);
        }
        log.info("渠道列表查询，authConfig:{}\norgConfig:{}\nareaId:{}\nchannelPageReq:{}", authConfig, orgConfig, user.getAreaId(), channelPageReq);

        channelPageReq.setIsTaxModel(user.getIsTaxModel());
        //分页查询
        Long CURRENT_PAGE = 10L;
        Long current = Optional.of(channelPageReq.getPage().getCurrent()).orElse(CURRENT_PAGE);
        Long PAGE_SIZE = 10L;
        Long size = Optional.of(channelPageReq.getPage().getSize()).orElse(PAGE_SIZE);
        Long end = current * size;
        Long begin = end - size + 1;
        Integer total = this.baseMapper.getTotalCountV1(channelPageReq);
        IPage<ChannelPageVo> channelPageVoPage = new Page<>(current, size, total);
        channelPageVoPage.setPages(total / size);

        //分类和品牌 id转换name
        List<ChannelPageVo> channelVos = this.baseMapper.queryChannelPageV1(channelPageReq, begin, end);
        List<Integer> cidList = channelVos.stream()
                .map(ChannelPageVo::getCids).filter(Objects::nonNull).collect(Collectors.toList());
        Map<Integer, Category> categoryMap;
        if (CollectionUtils.isNotEmpty(cidList)) {
            categoryMap = categoryService.getCategoryMap(cidList);
        } else {
            categoryMap = new HashMap<>();
        }
        List<String> brandIdStrList = channelVos.stream().map(ChannelPageVo::getBrandId)
                .filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<Integer> brandIdList = new ArrayList<>();
        brandIdStrList.forEach((String it) -> {
            String[] split = it.split(",");
            for (String s : split) {
                if (StringUtils.isNotEmpty(s)) {
                    brandIdList.add(Integer.valueOf(s));
                }
            }
        });
        Map<Integer, Brand> brandMap;
        if (CollectionUtils.isNotEmpty(brandIdStrList)) {
            brandMap = brandService.getBrandMap(brandIdList);
        } else {
            brandMap = new HashMap<>();
        }
        Map<Integer, List<ChannelKindLink>> linkMap = linkList.stream()
                .collect(Collectors.groupingBy(ChannelKindLink::getChannelId));
        channelVos.forEach((ChannelPageVo it) -> {
            Category category = Optional.ofNullable(categoryMap.get(it.getCids())).orElseGet(Category::new);
            it.setCategory(category.getName());
            it.setBrandName(brandIdToName(it.getBrandId(), brandMap));
            List<ChannelKindLink> links = linkMap.get(it.getId());
            if (CollectionUtils.isNotEmpty(links)) {
                List<Integer> collect = links.stream().map(ChannelKindLink::getKind).collect(Collectors.toList());
                if (collect.contains(ChannelKindEnum.MOBILE.getCode())
                        || collect.contains(ChannelKindEnum.MAINTENANCE_ACCESSORIES.getCode())
                        || collect.contains(ChannelKindEnum.MOBILE_ACCESSORIES.getCode())) {
                    it.setType(1);
                } else {
                    it.setType(2);
                }
                StringBuilder builder = new StringBuilder();
                collect.forEach(kind -> builder.append(ChannelKindEnum.getValue(kind)).append(','));
                it.setKinds(builder.deleteCharAt(builder.length() - 1).toString());
            }
        });
        channelPageVoPage.setRecords(channelVos);
        return channelPageVoPage;
    }


    /**
     * @return
     */
    private List<ChannelListExportDto> createChannelList(List<Integer> channlIds) {
        int channelSize = channlIds.size();
        if (channelSize < SET_PARTITION) {
            return this.baseMapper.getChannelListExport(channlIds);
        } else {
            //集合划分
            int count = (channelSize / SET_PARTITION) + 1;
            List<ChannelListExportDto> array = new ArrayList<>();
            List<List<Integer>> lists = avgList(channlIds, count);
            for (List<Integer> ids : lists) {
                List<ChannelListExportDto> channelListExport = this.baseMapper.getChannelListExport(ids);
                array.addAll(channelListExport);

            }
            return array;
        }
    }


    /**
     * 划分数据库
     *
     * @param list
     * @param n
     * @param <T>
     * @return
     */
    public <T> List<List<T>> avgList(List<T> list, int n) {
        List<List<T>> strListlist = new ArrayList<>();
        int remainder = list.size() % n;//计算余数
        int Quotient = list.size() / n;//计算商
        int offset = 0;
        for (int i = 0; i < n; i++) {
            List<T> strList = null;
            if (remainder > 0) {
                strList = list.subList(i * Quotient + offset, (i + 1) * Quotient + offset + 1);
                remainder--;
                offset++;
            } else {
                strList = list.subList(i * Quotient + offset, (i + 1) * Quotient + offset);
            }
            strListlist.add(strList);
        }
        return strListlist;
    }

    /**
     * 查询渠道导出列表
     *
     * @return
     */
    @Override
    public List<ChannelListExportDto> selectChannelListExport(List<Integer> channlIds) {
        // 获取当前登录人的权限
        OaUserBO currentStaffId = requestComponent.getCurrentStaffId();
        if (currentStaffId == null) {
            throw new CustomizeException("没有当前登录人信息");
        }
        Integer authorizeId = currentStaffId.getAuthorizeId();
        if (authorizeId == null) {
            throw new CustomizeException("当前登录人权限为空");
        }


        List<ChannelListExportDto> channelListExport = createChannelList(channlIds);
        if (CollectionUtils.isEmpty(channelListExport)) {
            log.error("渠道导出查询列表为空,当前登录人的信息:{}", currentStaffId);
            return null;

        }
        //在sysConfig表里面查询出合作伙伴然后再进行 收集成一个map
        Map<String, String> companyMap = sysConfigService.lambdaQuery().eq(SysConfig::getCode, PARTNER_COMPANY)
                .eq(SysConfig::getXtenant, currentStaffId.getXTenant())
                .eq(SysConfig::getAuthId, authorizeId)
                .list().stream().collect(Collectors.toMap(SysConfig::getValue, SysConfig::getName));
        channelListExport.parallelStream().forEach((ChannelListExportDto item) -> {
            //渠道类型枚举转 换成文字（枚举太多所以不在sql写）
            String kinds = item.getKinds();
            if (StringUtil.isNotBlank(kinds)) {
                String[] split = kinds.split(",");
                if (split != null) {
                    StringJoiner kindsValue = new StringJoiner(",");
                    for (String str : split) {
                        kindsValue.add(ChannelKindEnum.getValue(Integer.parseInt(str)));
                    }
                    item.setKinds(kindsValue + "");
                }
            }
            //将合作公司转成文字
            String partnerCompany = item.getPartnerCompany();
            if (StringUtils.isNotEmpty(partnerCompany)) {
                String[] splitPartner = partnerCompany.split(",");
                if (splitPartner != null) {
                    StringJoiner kindsValuePartner = new StringJoiner(",");
                    for (String str : splitPartner) {
                        String s = companyMap.get(str);
                        if (StringUtil.isNotBlank(s)) {
                            kindsValuePartner.add(s);
                        }

                    }
                    item.setPartnerCompany(kindsValuePartner + "");
                }
            }
        });
        return channelListExport;
    }

    @Override
    public List<ChannelSimpleVo> getChannelSimpleInfo(String searchKey) {
        Integer tempId;
        try {
            tempId = Integer.valueOf(searchKey);
        } catch (Exception e) {
            tempId = null;
        }
        return this.baseMapper.getChannelSimpleInfo(tempId, searchKey);
    }


    /**
     * 根据areaId获取Authorizeid
     *
     * @param areaId
     * @return
     */
    private Integer getAuthorizeidByAreaId(Integer areaId) {
        Areainfo areaInfo = areainfoService.getById(areaId);
        return areaInfo.getAuthorizeid();

    }


    @Override
    @DSTransactional
    public R<Boolean> saveOrUpdateChannelChw(ChannelVo channelVo, Boolean isAdd) {
//        增加修改的日志
        StringBuilder updateLog = new StringBuilder();
        if (!Boolean.TRUE.equals(isAdd)) {
            generateUpdateLog(channelVo, updateLog);

            addChannelLognew(channelVo.getId(), updateLog.toString(), ChwUserInfoEnum.CHW_USERNAME.getMessage()
                    , LocalDateTime.now());
        }

//            保存或修改渠道类
        Ok3wQudao newChannel = channelMapStruct.toChannel(channelVo);
        newChannel.setAuthorizeid(channelVo.getAuthorizeid());
        newChannel.setAdddate(LocalDateTime.now());

        //判断该授权下insource表是否已经存在商城
        Integer insourceId = inSourceService.checkInsourceName(channelVo.getAuthorizeid());
        newChannel.setInsourceid(insourceId);

        saveOrUpdate(newChannel);
        channelVo.setId(newChannel.getId());
        //采货王日志增加
        addChannelLognew(newChannel.getId(),"采货王新建渠道","采货王",LocalDateTime.now());

        //渠道省市区地址保存
        ChannelProvinceCity channelProvinceCity = new ChannelProvinceCity();
        channelProvinceCity
                .setChannelId(newChannel.getId())
                .setProvinceId(channelVo.getPid())
                .setCityId(channelVo.getZid())
                .setCountyId(channelVo.getDid());
        channelProvinceCityService.save(channelProvinceCity);


//            保存或修改附件
        List<Attachments> existAttachmentList = attachmentsService.getAttachments(newChannel.getId()
                , AttachmentsTypeEnum.OK3W_QD.getType(), null, true);
        List<String> oldFidList = existAttachmentList.stream().map(Attachments::getFid).collect(Collectors.toList());
        List<FileBO> addFileList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(channelVo.getAttachments())) {
            addFileList = channelVo.getAttachments().stream().filter((FileBO it) -> {
                if (oldFidList.contains(it.getFid())) {
                    oldFidList.remove(it.getFid());
                    return false;
                }
                return true;
            }).collect(Collectors.toList());
        }
//            删除被删的附件
        attachmentsService.removeByFidList(oldFidList, newChannel.getId(), AttachmentsTypeEnum.OK3W_QD.getType());
//            增加新增的附件
        OaAttachmentsReq oaAttachmentsReq = new OaAttachmentsReq();
        oaAttachmentsReq.setFileList(addFileList);
        oaAttachmentsReq.setLinkId(newChannel.getId());
        oaAttachmentsReq.setType(AttachmentsTypeEnum.OK3W_QD.getType());
        oaAttachmentsReq.setUserId(ChwUserInfoEnum.CHW_USERID.getCode());
        attachmentsService.addAttachments(oaAttachmentsReq);

//            添加联系人
        qudaocontactssService.addListByChannelId(channelVo.getContactsList(), newChannel.getId());

//            添加财务信息
        List<ChannelKindLink> channelKindLinkList = new ArrayList<>();
        List<Qudaoaccounts> accountsList = new ArrayList<>();
        AtomicReference<String> kindName = new AtomicReference<>("");
        channelVo.getFinanceList().forEach((FinanceDto it) -> {
            ChannelKindLink kindLinkTemp = it.getKindLink();
            if (kindLinkTemp.getId() == null) {
                kindLinkTemp.setChannelId(newChannel.getId());
                kindLinkTemp.setCreateTime(LocalDateTime.now());
                kindLinkTemp.setChannelState(ChannelCooperationStatusEnum.COOPERATION.getCode());
            }

            //盘点状态修改用盘点接口，修改接口不修改
            //kindLinkTemp.setChannelState(null);
            channelKindLinkService.saveOrUpdate(kindLinkTemp);
            kindName.set(kindName.get() + ChannelKindEnum.getValue(kindLinkTemp.getKind()) + "/");
            channelKindLinkList.add(kindLinkTemp);
            it.setKindLink(kindLinkTemp);
        });

        channelVo.getFinanceList().forEach((FinanceDto it) -> {
            ChannelKindLink kindLinkTemp2 = it.getKindLink();
            List<Qudaoaccounts> accountsListTemp = it.getAccountsList().stream().map((QudaoaccountsDTO account) -> {
                Qudaoaccounts tempAccounts = channelMapStruct.toAccounts(account);
                if (account.getId() == null) {
                    tempAccounts.setLinkId(kindLinkTemp2.getId());
                    tempAccounts.setQudaoid(kindLinkTemp2.getChannelId());
                    tempAccounts.setIsenable(ENABLE_ACCOUNTS);
                    tempAccounts.setIsdel(0);
                }
                return tempAccounts;
            }).collect(Collectors.toList());
            accountsList.addAll(accountsListTemp);
        });
        accountsList.forEach(qudaoaccountsService::saveOrUpdate);
        stringRedisTemplate.delete(CHANNEL_REDIS_KEY);
        stringRedisTemplate.delete(StrUtil.format("{}_{}", RedisKeys.CHANNEL_LIST_KEY,
                MyDynamicRoutingDataSource.isTaxModel()));
        stringRedisTemplate.delete(StrUtil.format("{}_{}", RedisKeys.CHANNEL_LIST_KEY_ALL,
                MyDynamicRoutingDataSource.isTaxModel()));
        if (Boolean.TRUE.equals(isAdd)) {
            return R.success("创建成功");
        } else {
            return R.success("修改成功");
        }

    }

    /**
     * 过滤出 accountnumber username openingbank 都为空的数据
     * @param accountsList
     * @return
     */
    @DSTransactional
    private void handleAccountsList(List<Qudaoaccounts> accountsList){
        if(CollectionUtils.isEmpty(accountsList)){
            return ;
        }
        accountsList.forEach((Qudaoaccounts item)->{
            Long id = item.getId();
            item.setDtime(LocalDateTime.now());
            //通过id来进行判断该数据是修改还是新增
            if(id!=null){
                //修改的情况
                if(!getBol(item)){
                    item.setIsdel(1);
                }
                qudaoaccountsService.updateById(item);
            } else {
                //新增情况
                if(getBol(item)){
                    qudaoaccountsService.save(item);
                }
            }

        });
    }

    private Boolean getBol(Qudaoaccounts item){
        String accountnumber = item.getAccountnumber();
        String username = item.getUsername();
        String openingbank = item.getOpeningbank();
        if (StringUtils.isEmpty(accountnumber) && StringUtils.isEmpty(username) && StringUtils.isEmpty(openingbank)) {
            return Boolean.FALSE;
        } else {
            return Boolean.TRUE;
        }

    }


    @Override
    @DSTransactional
    public R<Boolean> saveOrUpdateChannel(ChannelVo channelVo, Boolean isAdd) {
        OaUserBO curStaffUser = requestComponent.getCurrentStaffId();
        Integer userId = curStaffUser.getUserId();

        //重新设置品牌id
        if (StringUtils.isNotEmpty(channelVo.getBrandid())) {
            StringBuilder brandidSb = new StringBuilder(channelVo.getBrandid());
            brandidSb.insert(0, ',');
            brandidSb.append(',');
            channelVo.setBrandid(brandidSb.toString());
        }
        //是否是财务
        boolean finance = isFinance(curStaffUser);
        // cityId、pid、zid、did都让前端传上来了，这里就不搞了

        String samekindqudao = channelVo.getSamekindqudao();
        if (StringUtils.isNotBlank(samekindqudao)) {
            Set<String> sameKindQudaoSet = new HashSet<>(Arrays.asList(samekindqudao.split(",")));
            String samekindqudaoTemp = sameKindQudaoSet.stream()
                    .filter(StringUtils::isNotBlank).collect(Collectors.joining(","));
            channelVo.setSamekindqudao(samekindqudaoTemp);
        }
        Integer applyCount = 0;
        if (channelVo.getApplyId() != null) {
            CompletableFuture<Integer> cf1 = CompletableFuture.supplyAsync(() -> applyInfoService.getCount(channelVo.getApplyId()));
            applyCount = cf1.join();
            if (applyCount <= 0) {
                return R.error("批签号不存在");
            }
        }

//        根据kind类型，对数据进行判空
        R<Boolean> existChannel = verificationNotEmptyValue(channelVo, isAdd);
        if (existChannel != null) {
            return existChannel;
        }

//        增加修改的日志
        StringBuilder updateLog = new StringBuilder();
        if (!Boolean.TRUE.equals(isAdd)) {
            //日志拼接
            generateUpdateLog(channelVo, updateLog);
            addChannelLognew(channelVo.getId(), updateLog.toString(), curStaffUser.getUserName(), LocalDateTime.now());
        } else {
            subjectAddLog(channelVo,updateLog);
        }
        //添加分布式锁(锁租户)
        Integer xTenant = curStaffUser.getXTenant();
        RLock lock = redissonClient.getLock("oa-stock-channel-add:" + xTenant);
        try {
            boolean isLock = lock.tryLock(0, 2L, TimeUnit.SECONDS);
            if (!isLock) {
                throw new CustomizeException("已经有其他人在操作");
            }

            //            保存或修改渠道类
            Ok3wQudao newChannel = channelMapStruct.toChannel(channelVo);
            //如果是跨授权调拨那就是传递的参数AuthorizeId
            Integer sourceType = Optional.ofNullable(channelVo.getSourceType()).orElse(Integer.MAX_VALUE);
            if(SourceTypeEnum.CROSS_AUTHORIZATION.getCode().equals(sourceType)){
                newChannel.setAuthorizeid(channelVo.getAuthorizeid());
            } else {
                Integer authorizeId = Optional.ofNullable(channelVo.getAuthorizeid()).orElse(curStaffUser.getAuthorizeId());
                newChannel.setAuthorizeid(authorizeId);
            }
            newChannel.setAdddate(LocalDateTime.now());
            saveOrUpdate(newChannel);

            List<ChannelProvinceCity> list = channelProvinceCityService.lambdaQuery()
                    .eq(ChannelProvinceCity::getChannelId, newChannel.getId()).list();
            if (CollectionUtils.isNotEmpty(list)) {
                list.forEach((ChannelProvinceCity it) -> channelProvinceCityService.removeById(it.getId()));

            }
            List<ChannelProvinceCityDto> provinceCityList = channelVo.getProvinceCityList();
            if (CollectionUtils.isNotEmpty(provinceCityList)) {
                List<ChannelProvinceCity> collect = provinceCityList.stream().map(channelMapStruct::toChannelProvinceCity)
                        .collect(Collectors.toList());
                collect.forEach((ChannelProvinceCity it) -> {
                    it.setChannelId(newChannel.getId());
                    channelProvinceCityService.save(it);
                });
            }
            StringBuilder attachmentsLog = new StringBuilder();
            //            保存或修改附件
            List<Attachments> existAttachmentList = attachmentsService
                    .getAttachments(newChannel.getId(), AttachmentsTypeEnum.OK3W_QD.getType(), null,
                            true);
            Map<String, Attachments> attachmentsMap = existAttachmentList.stream().collect(Collectors.toMap(Attachments::getFid, Function.identity(), (v1,v2) -> v2));
            List<String> oldFidList = existAttachmentList.stream().map(Attachments::getFid).collect(Collectors.toList());
            List<FileBO> addFileList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(channelVo.getAttachments())) {
                addFileList = channelVo.getAttachments().stream().filter((FileBO it) -> {
                    if (oldFidList.contains(it.getFid())) {
                        oldFidList.remove(it.getFid());
                        List<Attachments> attachments = attachmentsService.newList(new LambdaQueryWrapper<Attachments>()
                                        .eq(Attachments::getFid, it.getFid())
                                        .eq(Attachments::getLinkedID, newChannel.getId())
                                        .eq(Attachments::getType, AttachmentsTypeEnum.OK3W_QD.getType())
                                , newChannel.getId(), AttachmentsTypeEnum.OK3W_QD.getType());
                        attachmentsService.newUpdate(attachments, it.getFileName());

                        if (!Objects.equals(Optional.ofNullable(attachmentsMap.get(it.getFid())).map(Attachments::getFilename).orElse(""),it.getFileName())) {
                            attachmentsLog.append("附件名称由").append(Optional.ofNullable(attachmentsMap.get(it.getFid())).map(Attachments::getFilename).orElse("")).append("改为").append(it.getFileName()).append(";");
                        }
                        return false;
                    }
                    return true;
                }).collect(Collectors.toList());
            }
            //            删除被删的附件
            attachmentsService.removeByFidList(oldFidList, newChannel.getId(), AttachmentsTypeEnum.OK3W_QD.getType());
            //            增加新增的附件
            OaAttachmentsReq oaAttachmentsReq = new OaAttachmentsReq();
            oaAttachmentsReq.setFileList(addFileList);
            oaAttachmentsReq.setLinkId(newChannel.getId());
            oaAttachmentsReq.setType(AttachmentsTypeEnum.OK3W_QD.getType());
            oaAttachmentsReq.setUserId(userId);
            attachmentsService.addAttachments(oaAttachmentsReq);
            if (CollectionUtils.isNotEmpty(addFileList)) {
                attachmentsLog.append("新增附件");
                addFileList.forEach(v -> {
                    attachmentsLog.append(v.getFileName()).append(",");
                });
            }
            if (CollectionUtils.isNotEmpty(oldFidList)) {
                attachmentsLog.append("删除附件");
                oldFidList.forEach(v -> {
                    attachmentsLog.append(Optional.ofNullable(attachmentsMap.get(v)).map(Attachments::getFilename).orElse("")).append(",");
                });
            }
            addChannelLognew(channelVo.getId(), attachmentsLog.toString(), curStaffUser.getUserName(), LocalDateTime.now());
            //            添加联系人
            qudaocontactssService.addListByChannelId(channelVo.getContactsList(), newChannel.getId());
            //            添加财务信息（九机和输出的财务添加方式不一样）
            if (xTenant == 0) {
                List<String> rank = Optional.ofNullable(curStaffUser.getRank()).orElseGet(Collections::emptyList);
                //业务信息设置
                List<Qudaoaccounts> accountsList = new ArrayList<>();
                AtomicReference<String> kindName = new AtomicReference<>("");
                channelVo.getFinanceList().forEach((FinanceDto it) -> {
                    ChannelKindLink kindLinkTemp = it.getKindLink();
                    if (kindLinkTemp.getId() == null) {
                        kindLinkTemp.setChannelId(newChannel.getId());
                        kindLinkTemp.setCreateTime(LocalDateTime.now());
                        kindLinkTemp.setChannelState(ChannelCooperationStatusEnum.APPLICATION.getCode());

                    }
                    //如果没有qdkm权限则不可以对科目进行操作
                    if(!rank.contains("qdkm")){
                        kindLinkTemp.setSubject(null);
                    }

                    //盘点状态修改用盘点接口，修改接口不修改
                    kindLinkTemp.setChannelState(null);
                    boolean flag = channelKindLinkService.saveOrUpdate(kindLinkTemp);
                    Assert.state(flag, "渠道信息保存失败");
                    kindName.set(kindName.get() + ChannelKindEnum.getValue(kindLinkTemp.getKind()) + "/");
                    it.setKindLink(kindLinkTemp);
                });
                //如果过没有qdzh则不可以对财务信息进行操作
                if(rank.contains("qdzh")){
                    // 财务信息设置
                    channelVo.getFinanceList().forEach((FinanceDto it) -> {
                        ChannelKindLink kindLinkTemp2 = it.getKindLink();
                        List<Qudaoaccounts> accountsListTemp = it.getAccountsList().stream().map((QudaoaccountsDTO account) -> {
                            Qudaoaccounts tempAccounts = channelMapStruct.toAccounts(account);
                            if (account.getId() == null) {
                                tempAccounts.setLinkId(kindLinkTemp2.getId());
                                tempAccounts.setQudaoid(kindLinkTemp2.getChannelId());
                                tempAccounts.setIsenable(ENABLE_ACCOUNTS);
                                tempAccounts.setIsdel(0);
                                addChannelLognew(newChannel.getId(), generateFinanceLog(tempAccounts, new StringBuilder()).toString()
                                        , curStaffUser.getUserName(), LocalDateTime.now());
                            } else {
                                addChannelLognew(newChannel.getId(), generateFinanceUpdateLog(tempAccounts, new StringBuilder()).toString()
                                        , curStaffUser.getUserName(), LocalDateTime.now());
                            }
                            return tempAccounts;
                        }).collect(Collectors.toList());
                        accountsList.addAll(accountsListTemp);
                    });
                      handleAccountsList(accountsList);
                    //accountsListNew.forEach(qudaoaccountsService::saveOrUpdate);
                }
            } else {
                //            添加财务信息
                List<Qudaoaccounts> accountsList = new ArrayList<>();
                AtomicReference<String> kindName = new AtomicReference<>("");
                for (FinanceDto it : channelVo.getFinanceList()) {
                    ChannelKindLink kindLinkTemp = it.getKindLink();
                    if (kindLinkTemp.getId() == null) {
                        kindLinkTemp.setChannelId(newChannel.getId());
                        kindLinkTemp.setCreateTime(LocalDateTime.now());
                        kindLinkTemp.setChannelState(ChannelCooperationStatusEnum.APPLICATION.getCode());
                    }
                    //没有财务权限不能修改
                    if (!finance) {
                        kindLinkTemp.setMargin(null);
                        kindLinkTemp.setSubject(null);
                        kindLinkTemp.setDepositHasReceipt(null);
                    }
                    //盘点状态修改用盘点接口，修改接口不修改
                    kindLinkTemp.setChannelState(null);
                    if(SourceTypeEnum.CROSS_AUTHORIZATION.getCode().equals(sourceType)){
                        kindLinkTemp.setChannelState(ChannelCooperationStatusEnum.COOPERATION.getCode());
                    }
                    boolean flag = channelKindLinkService.saveOrUpdate(kindLinkTemp);
                    Assert.state(flag, "渠道信息保存失败");
                    kindName.set(kindName.get() + ChannelKindEnum.getValue(kindLinkTemp.getKind()) + "/");
                    it.setKindLink(kindLinkTemp);
                }
                if (finance) {
                    for (FinanceDto it : channelVo.getFinanceList()) {
                        ChannelKindLink kindLinkTemp2 = it.getKindLink();
                        if(CollectionUtils.isNotEmpty(it.getAccountsList())){
                            List<Qudaoaccounts> accountsListTemp = it.getAccountsList()
                                    .stream()
                                    .map((QudaoaccountsDTO account) -> getAccounts(account, kindLinkTemp2, newChannel, curStaffUser))
                                    .collect(Collectors.toList());
                            accountsList.addAll(accountsListTemp);
                        }

                    }
                    handleAccountsList(accountsList);
                    //accountsListNew.forEach(qudaoaccountsService::saveOrUpdate);
                }
            }
            //判断是否生成批签（九机和输出的批签添加方式不一样）
            if (channelVo.getId() == null) {
                if (xTenant == 0) {
                    List<FinanceDto> financeList = channelVo.getFinanceList();
                    if (CollectionUtils.isNotEmpty(financeList)) {
                        //收集该渠道的业务类型
                        List<Integer> collect = financeList.stream().map((FinanceDto item) -> item.getKindLink().getKind())
                                .collect(Collectors.toList());
                       //所有渠道都要生成批签
                        String approve = createApprove(channelVo, curStaffUser, newChannel, collect);
                        updateLog.append("批签号：").append(ENDORSEMENT_ADDRESS).append(approve).append("'>").append(approve).append("</a>");
                        addChannelLognew(newChannel.getId(), updateLog.toString(), curStaffUser.getUserName(), LocalDateTime.now());
                    }
                } else {
                    //判断是否生成批签
                    List<FinanceDto> financeList = channelVo.getFinanceList();
                    if (CollectionUtils.isNotEmpty(financeList)) {
                        //收集该渠道的业务类型
                        List<Integer> collect = financeList.stream().map((FinanceDto item) -> item.getKindLink().getKind()).collect(Collectors.toList());
                        //大件，小件，维修配件，维修商 类型才进行生成批签
                        List<Integer> integers = Arrays.asList(ChannelKindEnum.MOBILE.getCode(), ChannelKindEnum.MOBILE_ACCESSORIES.getCode(),
                                ChannelKindEnum.MAINTENANCE_ACCESSORIES.getCode(), ChannelKindEnum.REPAIRER.getCode());
                        //如果两个集合存在交集就进行批签申请
                        boolean b = Collections.disjoint(collect, integers);
                        if (!b) {
                            String approve = createApprove(channelVo, curStaffUser, newChannel, collect);
                            updateLog.append("批签号：").append(ENDORSEMENT_ADDRESS)
                                    .append(approve).append("'>").append(approve).append("</a>");
                            addChannelLognew(newChannel.getId(), updateLog.toString(), curStaffUser.getUserName(), LocalDateTime.now());
                        }
                    }
                }
            }
            if (applyCount > 0) {
                updateLog.append("批签号：").append(ENDORSEMENT_ADDRESS).append(channelVo.getApplyId()).append("'>").append(channelVo.getApplyId()).append("</a>");
                addChannelLognew(newChannel.getId(), updateLog.toString(), curStaffUser.getUserName(), LocalDateTime.now());
            }
            //只有营销渠道商类型才做这个操作 且数据不进数据库
            if (StringUtils.isNotEmpty(channelVo.getPolicyDescription())) {
                addChannelLognew(newChannel.getId(), "政策描述：" + channelVo.getPolicyDescription(), curStaffUser.getUserName(), LocalDateTime.now());
            }
            //不入数据库
            if (StringUtils.isNotEmpty(channelVo.getRemark())) {
                addChannelLognew(newChannel.getId(), "备注：" + channelVo.getRemark(), curStaffUser.getUserName(), LocalDateTime.now());
            }
            stringRedisTemplate.delete(CHANNEL_REDIS_KEY);
            stringRedisTemplate.delete(StrUtil.format("{}_{}", RedisKeys.CHANNEL_LIST_KEY,
                    MyDynamicRoutingDataSource.isTaxModel()));
            stringRedisTemplate.delete(StrUtil.format("{}_{}", RedisKeys.CHANNEL_LIST_KEY_ALL,
                    MyDynamicRoutingDataSource.isTaxModel()));
            channelVo.setId(newChannel.getId());
            if (Boolean.TRUE.equals(isAdd)) {
                R<Boolean> success = R.success("创建成功");
                Map<String, Object> exData = new HashMap<>();
                exData.put("id",newChannel.getId());
                success.setExData(exData);
                return success;
            } else {
                R<Boolean> success = R.success("修改成功");
                Map<String, Object> exData = new HashMap<>();
                exData.put("id",newChannel.getId());
                success.setExData(exData);
                return success;
            }

        } catch (CustomizeException e) {
            log.error("创建渠道失败", e);
            throw new CustomizeException(e.getMessage());
        } catch (Exception e) {
            log.error("创建渠道异常", e);
            return R.error();
        } finally {
            lock.unlock();
        }
    }


    private Qudaoaccounts getAccounts(QudaoaccountsDTO account, ChannelKindLink kindLinkTemp2, Ok3wQudao newChannel, OaUserBO curStaffUser) {
        Qudaoaccounts tempAccounts = channelMapStruct.toAccounts(account);
        if (account.getId() == null) {
            tempAccounts.setLinkId(kindLinkTemp2.getId());
            tempAccounts.setQudaoid(kindLinkTemp2.getChannelId());
            tempAccounts.setIsenable(ENABLE_ACCOUNTS);
            tempAccounts.setIsdel(0);
            addChannelLognew(newChannel.getId(), generateFinanceLogNew(tempAccounts).toString()
                    , curStaffUser.getUserName(), LocalDateTime.now());
        } else {
            addChannelLognew(newChannel.getId(), generateFinanceUpdateLogNew(tempAccounts).toString()
                    , curStaffUser.getUserName(), LocalDateTime.now());
        }
        return tempAccounts;
    }


    /**
     * 创建渠道批签
     *
     * @param channelVo
     * @param curStaffUser
     * @return
     */
    @DSTransactional
    private String createApprove(ChannelVo channelVo, OaUserBO curStaffUser, Ok3wQudao newChannel, List<Integer> collect) {
        //获取到当前xtenant
        Integer xTenant = curStaffUser.getXTenant();
        if (xTenant == null) {
            throw new CustomizeException("获取xTenant失败");
        }
        //sysconfig表里获取域名
        SysConfig sysConfig = sysConfigService.lambdaQuery().eq(SysConfig::getCode, DOMAIN_NAME_OAWCF_CODE)
                .eq(SysConfig::getXtenant, xTenant).one();
        String domainName = sysConfig.getValue();
        //title
        StringBuffer title = new StringBuffer("新入围");
        List<FinanceDto> financeList = channelVo.getFinanceList();
        if (!CollectionUtils.isEmpty(financeList)) {
            financeList.forEach((FinanceDto item) -> {
                Integer kind = item.getKindLink().getKind();
                String value = ChannelKindEnum.getValue(kind);
                title.append(value).append("/");
            });
            //拼接好之后去除最后以为
            title.deleteCharAt(title.length() - 1);
            title.append("-").append(newChannel.getCompany()).append("(").append(newChannel.getId()).append(")");
        }
        //拼接files
        List<Attachments> existAttachmentList = attachmentsService.getAttachments(newChannel.getId()
                , AttachmentsTypeEnum.OK3W_QD.getType(), null, true);

        StringJoiner joiner = new StringJoiner("@");
        if (CollectionUtils.isNotEmpty(existAttachmentList)) {
            try {
                existAttachmentList.forEach((Attachments item) -> {
                    StringBuffer files = new StringBuffer();
                    files.append(item.getId()).append("|").append(item.getFilename())
                            .append("|").append(item.getFilepath()).append("|");
                    Optional.ofNullable(item.getFid()).ifPresent((String fid)->{
                        String replace = fid.replace("/", ",");
                        String newFid = replace.substring(0, replace.indexOf("."));
                        files.append(newFid);
                    });
                    joiner.add(files);
                });

            } catch (Exception e) {
                log.error("前端接受文件信息：[{}],数据查询文件信息：[{}],报错信息：[{}]"
                        , channelVo.getAttachments(), existAttachmentList, e);
                throw new CustomizeException("文件格式有误！");
            }
        } else {
            throw new CustomizeException("当前渠道业务类型需要生成批签，请添加附件");
        }
        //拼接comment
        String comment = generateComment(channelVo);
        //拼接请求地址
        String url = domainName + channelConfig.getApprove();
        //生成channel_link的字符串
        String kinds = collect.toString();
        String kindList = kinds.substring(1, kinds.length() - 1);
        //进行参数封装
        Map<String, Object> params = new HashMap<>(5);
        params.put("title", title + "");
        params.put("files", joiner.toString());
        params.put("comment", comment);
        params.put("kindList", kindList);
        params.put("id", newChannel.getId());
        //接口调用并且获取返回参数
        String res;
        try {
            HttpRequest post = HttpUtil.createPost(url);
            res = post.header("Authorization", requestComponent.getCurrentToken())
                    .body(JSONUtil.toJsonStr(params))
                    .contentType("application/json")
                    .execute()
                    .body();
            log.error("调用C#生成批签接口返回结果：[{}],传入的参数：[{}],请求地址：[{}]", res, params,url);
        } catch (Exception e) {
            log.error("调用生成批签接口异常：传入的参数：{}", params, e);
            throw new CustomizeException("调用生成批签接口异常");
        }
        if (StringUtils.isEmpty(res)) {
            throw new CustomizeException("调用C#生成批签接口没有获取到返回信息");
        }
        JSONObject jsonObject = JSON.parseObject(res);
        String code = jsonObject.getString("code");
        if (StringUtils.isEmpty(code) || Integer.parseInt(code) != 0) {
            throw new CustomizeException("批签生成失败");
        }
        String data = jsonObject.getString("data");
        return data;
    }

    @Override
    public IPage<ChannelPageVo> queryChannelPageV2(ChannelPageReq channelPageReq, OaUserBO user) {
        if (StringUtils.isNotBlank(channelPageReq.getBrandId())) {
            channelPageReq.setBrandIdList(Arrays.asList(channelPageReq.getBrandId().split(",")));
        }
        List<String> productCategoryList = channelPageReq.getProductCategory();
        if(CollectionUtils.isNotEmpty(productCategoryList)){
            List<String> newList = productCategoryList.stream().distinct().collect(Collectors.toList());
            if(newList.contains(ProductCategoryEnum.FIXED_ASSETS.getMessage())){
                channelPageReq.setProductCategoryType(ProductCategoryEnum.FIXED_ASSETS.getCode());
            }
            if(newList.contains(ProductCategoryEnum.COMMON_ASSETS.getMessage())){
                channelPageReq.setProductCategoryType(ProductCategoryEnum.COMMON_ASSETS.getCode());
            }
            if(newList.contains(ProductCategoryEnum.FIXED_ASSETS.getMessage()) && newList.contains(ProductCategoryEnum.COMMON_ASSETS.getMessage())){
                channelPageReq.setProductCategoryType(ProductCategoryEnum.FIXED_ASSETS.getCode()+ProductCategoryEnum.COMMON_ASSETS.getCode());
            }
        }
        if (CollectionUtils.isNotEmpty(channelPageReq.getProvinceCityList())) {
            List<ChannelProvinceCityDto> provinceCityList = channelPageReq.getProvinceCityList();
            provinceCityList.forEach(ChannelProvinceCityDto::cleanData);
            List<Integer> provinceIdList = provinceCityList.stream().filter(it -> it.getProvinceId() != null)
                    .map(ChannelProvinceCityDto::getProvinceId).collect(
                            Collectors.toList());
            List<Integer> cityIdList = provinceCityList.stream().filter(it -> it.getCityId() != null)
                    .map(ChannelProvinceCityDto::getCityId).collect(
                            Collectors.toList());
            List<Integer> countyIdList = provinceCityList.stream().filter(it -> it.getCountyId() != null)
                    .map(ChannelProvinceCityDto::getCountyId).collect(
                            Collectors.toList());
            channelPageReq.setProvinceIdList(provinceIdList);
            channelPageReq.setCidList(cityIdList);
            channelPageReq.setCountyIdList(countyIdList);
        }
        //获取渠道授权
        R<Boolean> booleanR = getChannelAuthorizeConfig(user);
        if (booleanR != null && Boolean.TRUE.equals(booleanR.getData())) {
            channelPageReq.setAuthorizeId(user.getAuthorizeId());
        } else {
            channelPageReq.setAuthorizeId(null);
        }
        log.info("渠道列表查询，channelPageReq:{}", channelPageReq);

        channelPageReq.setIsTaxModel(user.getIsTaxModel());
        //分页查询
        Long currentPage = 1L;
        Long current = Optional.of(channelPageReq.getPage().getCurrent()).orElse(currentPage);
        Long pageSize = 10L;
        Long size = Optional.of(channelPageReq.getPage().getSize()).orElse(pageSize);
        Long end = current * size;
        Long begin = end - size + 1;
        Integer total = this.baseMapper.getTotalCountV1(channelPageReq);
        IPage<ChannelPageVo> channelPageVoPage = new Page<>(current, size, total);
        channelPageVoPage.setPages(total / size);

        //分类和品牌 id转换name
        List<ChannelPageVo> channelVos = this.baseMapper.queryChannelPageV1(channelPageReq, begin, end);
        Map<Integer, Category> categoryMap = new HashMap<>(MapUtil.DEFAULT_INITIAL_CAPACITY);
        Map<Integer, ChannelListStateDTO> kindStateMap = new HashMap<>(MapUtil.DEFAULT_INITIAL_CAPACITY);
        if (CollectionUtils.isNotEmpty(channelVos)) {
            List<Integer> cidList = channelVos.stream()
                    .map(ChannelPageVo::getCids).filter(Objects::nonNull).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(cidList)) {
                categoryMap = categoryService.getCategoryMap(cidList);
            }
            List<ChannelListStateDTO> stateDTOList = new ArrayList<>();
            List<Integer> collect = channelVos.stream().map(ChannelPageVo::getId).collect(Collectors.toList());
            List<List<Integer>> partition = Lists.partition(collect, 2000);
            for (List<Integer> item : partition) {
                List<ChannelListStateDTO> channelListStateDTOS = this.baseMapper.listChannelState(item);
                stateDTOList.addAll(channelListStateDTOS);
            }
            if (CollectionUtils.isNotEmpty(stateDTOList)) {
                stateDTOList.forEach(it -> it.getKindList().forEach(child -> setChannelKindState(it, child)));
                kindStateMap = stateDTOList.stream().collect(Collectors.toMap(ChannelListStateDTO::getId, Function.identity()));
            }
        }

        List<String> brandIdStrList = channelVos.stream().map(ChannelPageVo::getBrandId)
                .filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<Integer> brandIdList = new ArrayList<>();
        brandIdStrList.forEach((String it) -> {
            String[] split = it.split(",");
            for (String s : split) {
                if (StringUtils.isNotEmpty(s)) {
                    brandIdList.add(Integer.valueOf(s));
                }
            }
        });
        Map<Integer, Brand> brandMap = new HashMap<>(MapUtil.DEFAULT_INITIAL_CAPACITY);
        if (CollectionUtils.isNotEmpty(brandIdStrList)) {
            brandMap = brandService.getBrandMap(brandIdList);
        }
        final Map<Integer, Brand> finalBrandMap = brandMap;
        final Map<Integer, Category> finalCategoryMap = categoryMap;
        final Map<Integer, ChannelListStateDTO> finalKindStateMap = kindStateMap;
        channelVos.forEach((ChannelPageVo it) -> {
            Category category = Optional.ofNullable(finalCategoryMap.get(it.getCids())).orElseGet(Category::new);
            it.setCategory(category.getName());
            it.setBrandName(brandIdToName(it.getBrandId(), finalBrandMap));
            ChannelListStateDTO dto = Optional.ofNullable(finalKindStateMap.get(it.getId()))
                    .orElse(this.newChannelListStateDTO(it.getId()));
            it.setKinds(dto.getKindsName());
            it.setStatesName(dto.getStatesName());
            if (CollectionUtils.isNotEmpty(dto.getKindList())) {
                List<Integer> collect = dto.getKindList().stream().map(ChannelKindStateDTO::getKind).collect(Collectors.toList());
                if (collect.contains(ChannelKindEnum.MOBILE.getCode())
                        || collect.contains(ChannelKindEnum.MAINTENANCE_ACCESSORIES.getCode())
                        || collect.contains(ChannelKindEnum.MOBILE_ACCESSORIES.getCode())) {
                    it.setType(1);
                } else {
                    it.setType(2);
                }
            }
        });
        channelPageVoPage.setRecords(channelVos);
        return channelPageVoPage;
    }

    @Override
    public Map<Integer, Ok3wQudao> getChannelMap(List<Integer> channelIds) {
        if(CollectionUtils.isEmpty(channelIds)){
            return new HashMap<>();
        }
        return CommonUtils.bigDataInQuery(channelIds, ids -> this.lambdaQuery().in(Ok3wQudao::getId, ids).list()).stream().collect(Collectors.toMap(Ok3wQudao::getId, Function.identity()));
    }

    /**
     * 获取渠道授权信息
     *
     * @param user
     * @return
     */
    @Override
    public R<Boolean> getChannelAuthorizeConfig(OaUserBO user) {
        R<Boolean> booleanR = null;
        try {
            booleanR = authorizeSwitchClient.checkIsolate(CHANNEL_AUTHORIZE, user.getXTenant());
            log.info("远程调用获取渠道授权，参数：sign:{},xTenant:{},\n响应：{}", CHANNEL_AUTHORIZE, user.getXTenant(), booleanR);
        } catch (Exception e) {
            log.error("远程调用office/api/authorizeSwitchConfig/checkIsolate/v1异常,param={},error={}", user.getXTenant(), e);
            throw new CustomizeException("远程调用office/api/authorizeSwitchConfig/checkIsolate/v1异常", e);
        }
        return booleanR;
    }

    /**
     * newChannelListStateDTO
     *
     * @param id
     * @return
     */
    private ChannelListStateDTO newChannelListStateDTO(Integer id) {
        ChannelListStateDTO stateDTO = new ChannelListStateDTO();
        stateDTO.setId(id);
        stateDTO.setKindsName("未知类型");
        stateDTO.setStatesName("未知状态");
        return stateDTO;
    }

    /**
     * 拼接comment字段
     *
     * @param channelVo
     * @return
     */
    private String generateComment(ChannelVo channelVo) {
        List<FinanceDto> financeList = channelVo.getFinanceList();
        List<Integer> kinds = financeList.stream().map((FinanceDto item) -> {
            ChannelKindLink kindLink = item.getKindLink();
            Integer kind = kindLink.getKind();
            return kind;
        }).collect(Collectors.toList());
        //判断是否是维修商入围
        StringBuffer comment = new StringBuffer();
        //公共信息拼接（基本信息拼接）
        comment.append("基本信息").append("\n")
                .append("公司名称：").append(channelVo.getCompany()).append("\n")
                .append("法人代表：").append(channelVo.getLegalrepresent()).append("\n")
                .append("企业性质：").append(ChannelCompanyNatureEnum.getValue(channelVo.getCompanynature())).append("\n");
        //公共信息拼接（业务联系人信息拼接）
        List<ContactsDto> contactsList = channelVo.getContactsList();
        if (CollectionUtils.isNotEmpty(contactsList)) {
            contactsList.forEach((ContactsDto item) -> {
                comment.append("业务联系人：").append(item.getUsername()).append("\n")
                        .append("业务联系人电话：").append(item.getTel()).append("\n");
            });
        }
        //财务信息根据类型进行拼接
        if (kinds.contains(ChannelKindEnum.REPAIRER.getCode())) {
            comment.append("\n").append("财务信息").append("\n");
            financeList.forEach((FinanceDto item) -> {
                ChannelKindLink kindLink = item.getKindLink();
                comment.append("业务类型：").append(ChannelKindEnum.getValue(kindLink.getKind())).append("\n")
                        .append("能否开发票：").append(kindLink.getInvoicingFlag() == 1 ? "能" : "不能").append("\n");
            });
            comment.append("渠道新增");
        } else {
            comment.append("\n").append("财务信息").append("\n");
            financeList.forEach((FinanceDto item) -> {
                ChannelKindLink kindLink = item.getKindLink();
                comment.append("业务类型：").append(ChannelKindEnum.getValue(kindLink.getKind())).append("\n")
                        .append("能否开发票：").append(Optional.ofNullable(kindLink.getInvoicingFlag()).orElse(1) == 1 ? "能" : "不能").append("\n");
                List<QudaoaccountsDTO> accountsList = item.getAccountsList();
                if(CollectionUtils.isNotEmpty(accountsList)){

                    accountsList.forEach((QudaoaccountsDTO obj) -> comment.append(obj.getIsgs() == 1 ? PaymentTypeEnum.getPublic()+"账号" : PaymentTypeEnum.getPublic()+"账号").append(obj.getAccountnumber()).append("\n")
                            .append("户名：").append(obj.getUsername()).append("\n")
                            .append("开户行：").append(obj.getOpeningbank()).append("\n"));
                }
                comment.append("\n");
            });
        }
        String comment1 = channelVo.getComment1();
        if(StrUtil.isNotEmpty(comment1)){
            comment.append("主营：").append(comment1).append("\n");
        }
        String remark = channelVo.getRemark();
        if(StrUtil.isNotEmpty(remark)){
            comment.append("备注：").append(remark).append("\n");
        }
        return comment.toString();
    }


    /**
     * 判断渠道数据中，是否有必填数据为空
     *
     * @param channelVo
     * @param isAdd
     * @return
     */
    private R<Boolean> verificationNotEmptyValue(ChannelVo channelVo, Boolean isAdd) {
        if (CollectionUtils.isEmpty(channelVo.getFinanceList())) {
            return R.error("至少需要一个财务类型数据");
        }
        // 渠道中大件类型限制一个渠道中的收款账户，仅允许为同一个公司主体（即：对公账号的户名必须一样）
        if(XtenantJudgeUtil.isJiujiMore()){
            List<FinanceDto> financeList = channelVo.getFinanceList();
            for (FinanceDto item: financeList) {
                ChannelKindLink link = Optional.ofNullable(item.getKindLink()).orElse(new ChannelKindLink());
                //只有大件的时候进行判断
                if(ChannelKindEnum.MOBILE.getCode().equals(link.getKind())){
                    List<QudaoaccountsDTO> accountsList = item.getAccountsList();
                    if(CollectionUtils.isNotEmpty(accountsList)){
                        HashSet<String> set = new HashSet<>();
                        accountsList.forEach(obj->{
                            if(NumberConstant.ONE.equals(obj.getIsgs())){
                                if(StringUtils.isNotEmpty(obj.getUsername())){
                                    set.add(obj.getUsername());
                                }
                            }
                        });
                        if(set.size()>NumberConstant.ONE){
                            ChannelKindLink channelKindLink = Optional.ofNullable(item.getKindLink()).orElse(new ChannelKindLink());
                            String value = ChannelKindEnum.getValue(channelKindLink.getKind());
                            return R.error("业务类型："+value+PaymentTypeEnum.getPublic()+"账号的收款主体必须一样");
                        }
                    }
                }
            }
        }
        // 判断父级编号是否不存在
        if (StringUtils.isNotBlank(channelVo.getCustomcode())) {
            Ok3wQudao parent = getById(channelVo.getCustomcode());
            if (parent == null) {
                return R.error("父级编号错误，请核对：" + channelVo.getCustomcode());
            }
        }
        if (CollectionUtils.isEmpty(channelVo.getKinds())) {
            return R.error("业务类型不能空");
        }
        //大小件、和维修配件不能为空的字段
        if (channelVo.getKinds().contains(ChannelKindEnum.MOBILE.getCode()) ||
                channelVo.getKinds().contains(ChannelKindEnum.MOBILE_ACCESSORIES.getCode()) ||
                channelVo.getKinds().contains(ChannelKindEnum.MAINTENANCE_ACCESSORIES.getCode())) {
            if (StringUtils.isEmpty(channelVo.getShouhoumobile())) {
                return R.error("售后联系电话不能为空");
            }
            if (StringUtils.isEmpty(channelVo.getShouhoucontacts())) {
                return R.error("售后联系人不能为空");
            }
            if (StringUtils.isEmpty(channelVo.getAfterAddress())) {
                return R.error("售后联系地址不能为空");
            }
            if (channelVo.getAfterCityid() == null) {
                return R.error("售后收货地址cityId，不能为空");
            }
            if (CollectionUtils.isEmpty(channelVo.getContactsList())) {
                return R.error("业务联系人不能为空");
            }
            if (channelVo.getChannelscale() == null) {
                return R.error("渠道规模不能为空");
            }
            if (channelVo.getCompanynature() == null) {
                return R.error("企业性质不能为空");
            }
            if (StringUtils.isEmpty(channelVo.getCwFzr())) {
                return R.error("财务负责人不能为空");
            }
            if (StringUtils.isEmpty(channelVo.getCwLxfs())) {
                return R.error("财务负责人电话不能为空");
            }
            if (StringUtils.isEmpty(channelVo.getLegalrepresent())) {
                return R.error("法人代表不能为空");
            }
            if (CollectionUtils.isEmpty(channelVo.getAttachments())) {
                return R.error("附件不能为空");
            }
            if (channelVo.getRegisteredcapital() == null) {
                return R.error("注册资金不能为空");
            }
            if (BigDecimal.ZERO.compareTo(channelVo.getRegisteredcapital()) > 0) {
                return R.error("注册资金不能为负数");
            }
            //            判断是否需要商品分类
            if (StringUtils.isEmpty(channelVo.getClassification())) {
                return R.error("商品分类不能为空");
            }
//            非 大小件和维修配件  的非空判断
        } else {
            if (!channelVo.getKinds().contains(ChannelKindEnum.RECYCLE_GOODS.getCode())){
                if (StringUtils.isEmpty(channelVo.getTel())) {
                    return R.error("联系电话不能为空");
                }
                if (StringUtils.isEmpty(channelVo.getUsername())) {
                    return R.error("联系人不能为空");
                }
            }
            if (channelVo.getUserid() != null) {
                BbsxpUsers bbsxpUsers = bbsxpUsersService.getNameById(channelVo.getUserid());
                if (bbsxpUsers == null) {
                    return R.error("会员编号不存在，请核对：" + channelVo.getUserid());
                }
            }
        }

        if (Boolean.TRUE.equals(isAdd)) {
            Ok3wQudao existChannel = getByBasicInfo(channelVo);
            if (existChannel != null) {
                return R.error("已存在相同信息的渠道[" + existChannel.getId() + "]！");
            }
        }
        return null;
    }


    /**
     * 科目日志记录的修改
     * @param channelVo
     * @param updateLog
     */
    private void subjectAddLog(ChannelVo channelVo, StringBuilder updateLog){
        List<FinanceDto> financeList = channelVo.getFinanceList();
        if(CollectionUtils.isNotEmpty(financeList)){
            StringJoiner joiner = new StringJoiner("，");
            for (FinanceDto item:financeList) {
                Optional.ofNullable(item.getKindLink()).ifPresent((ChannelKindLink obj)->{
                    Integer id = obj.getId();
                    ChannelKindLink oldLink = Optional.ofNullable(channelKindLinkService.getById(id)).orElse(new ChannelKindLink());
                    String oldValue = oldLink.getSubject();
                    String newValue = obj.getSubject();
                    //获取到当前科目的类型
                    Integer kind = Optional.ofNullable(obj.getKind()).orElse(Integer.MAX_VALUE);
                    String title = ChannelKindEnum.getValue(kind);
                    String updateLog2 = getUpdateLog2(oldValue, newValue, title + "科目");
                    if(!"".equals(updateLog2)){
                        joiner.add(updateLog2);
                    }
                    updateLog.append(getUpdateLog2(Convert.toStr(oldLink.getMargin(),""), Convert.toStr(obj.getMargin(),""), title + "渠道保证金/押金"));
                    updateLog.append(getUpdateLog2(Objects.equals(1, oldLink.getDepositHasReceipt()) ? "有" : "没有", Objects.equals(1, obj.getDepositHasReceipt()) ? "有" : "没有", title + "押金是否有收据"));
                    updateLog.append(getUpdateLog2(ChannelPayTypeEnum.getValue(oldLink.getSettlementType()), ChannelPayTypeEnum.getValue(obj.getSettlementType()), title + "结款性质"));
                    updateLog.append(getUpdateLog2(Objects.equals(1, oldLink.getInvoicingFlag()) ? "能" : "不能",Objects.equals(1, obj.getInvoicingFlag()) ? "能" : "不能", title + "能否开增票"));
                    updateLog.append(getUpdateLog2(InvoiceTypeEnum.getValue(oldLink.getInvoiceType()), InvoiceTypeEnum.getValue(obj.getInvoiceType()), title + "发票类型"));
                    if(ChannelKindEnum.ADMINISTRATIVE_GOODS.getCode().equals(kind)){
                        String productCategoryOld = oldLink.getProductCategory();
                        String productCategoryNew = obj.getProductCategory();
                        String comment = getUpdateLog2(productCategoryOld, productCategoryNew, title + "产品类别");
                        if(!"".equals(comment)){
                            joiner.add(comment);
                        }

                    }
                    //判断如果是大件的情况下处理脱敏日志信息
                    if(ChannelKindEnum.MOBILE.getCode().equals(kind)){
                        codeExtensionUtils.judgmentExtension((StringBuilder comment)->{
                            String oldDesensitizeFlag = Optional.ofNullable(oldLink.getDesensitizeFlag()).orElse(0)==0?"":oldLink.getDesensitizeFlag().toString();
                            String newDesensitizeFlag = Optional.ofNullable(obj.getDesensitizeFlag()).orElse(0)==0?"":obj.getDesensitizeFlag().toString();
                            comment.append(getUpdateLog2(oldDesensitizeFlag,newDesensitizeFlag,"imei显示脱敏"));
                            comment.append(getUpdateLog2(getBrandName(oldLink.getDesensitizeFlagBrandId()),getBrandName(obj.getDesensitizeFlagBrandId()),"IMEI脱敏品牌"));
                            comment.append(getUpdateLog2(getProvinceName(oldLink.getDesensitizeFlagProvinceIdList()),getProvinceName(obj.getDesensitizeFlagProvinceIdList()),"脱敏省份"));
                        },"脱敏日志记录异常 渠道id："+channelVo.getId(),updateLog);
                    }
                    if(XtenantEnum.isJiujiXtenant()){
                        String partnerCompanyNew = obj.getPartnerCompany();
                        String partnerCompanyOld = oldLink.getPartnerCompany();
                        String comment = getUpdateLog2(channelKindLinkService.selectCompanyTypeByIdList(partnerCompanyOld),
                                channelKindLinkService.selectCompanyTypeByIdList(partnerCompanyNew), title +"合作公司");
                        if(StringUtils.isNotEmpty(comment)){
                            joiner.add(comment);
                        }
                    }
                });
            }
            updateLog.append(joiner.toString());
        }
    }

    private String getBrandName(String oldDesensitizeFlagBrandId){
        return OptionalUtils.ifTrue(StringUtils.isNotEmpty(oldDesensitizeFlagBrandId), () -> {
            List<Long> brandIdList = Arrays.stream(oldDesensitizeFlagBrandId.split(","))
                    .filter(StringUtils::isNotBlank)
                    .map(Long::new).collect(Collectors.toList());
            StringJoiner oldJoiner = new StringJoiner(",");
            brandService.listBrand(brandIdList).forEach(brand -> oldJoiner.add(brand.getName()));
            return oldJoiner.toString();
        }, () -> "");
    }

    /**
     * 获取省份名称
     * @param desensitizeFlagProvinceIdList
     * @return
     */
    private String getProvinceName(String desensitizeFlagProvinceIdList){
        if(StringUtils.isEmpty(desensitizeFlagProvinceIdList)){
            return "";
        }
        List<Integer> pidList = Arrays.stream(desensitizeFlagProvinceIdList.split(",")).map(Integer::new).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(pidList)){
            return "";
        }
        List<AreaListEntity> areaListLevel3ByCode = areaListService.getAreaListLevel3ByCode(pidList);
        if(CollectionUtils.isEmpty(areaListLevel3ByCode)){
            return "";
        }
        return areaListLevel3ByCode.stream()
                .sorted(Comparator.comparing(AreaListEntity::getPid, Comparator.nullsFirst(Comparator.naturalOrder())))
                .map(AreaListEntity::getName)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.joining(","));
    }



    private void generateUpdateLog(ChannelVo channelVo, StringBuilder updateLog) {
        if (channelVo.getId() == null) {
            return;
        }
        Ok3wQudao oldChannel = getById(channelVo.getId());
        if (oldChannel == null) {
            return;
        }
        //科目处理
        subjectAddLog(channelVo,updateLog);
        updateLog.append(getUpdateLog(oldChannel.getCompany(), channelVo.getCompany(), "公司名称"));
        //判断包含大小件并且事九机
        List<Integer> list = Arrays.asList(ChannelKindEnum.MOBILE.getCode(), ChannelKindEnum.MOBILE_ACCESSORIES.getCode());
        if(XtenantEnum.isJiujiXtenant() && !Collections.disjoint(list, channelVo.getKinds())){
            Integer supplierTypeOld = oldChannel.getSupplierType();
            Integer supplierTypeNew = channelVo.getSupplierType();
            updateLog.append(getUpdateLog(FinancialTypeEnum.getValue(supplierTypeOld),FinancialTypeEnum.getValue(supplierTypeNew) , "供应商财务类型"));
        }
        updateLog.append(getUpdateLog(oldChannel.getCompanyJc(), channelVo.getCompanyJc(), "公司简称"));
        String oldValue = "空";
        String newValue = "空";
        if (oldChannel.getRegisteredcapital() != null) {
            oldValue = oldChannel.getRegisteredcapital().setScale(2, BigDecimal.ROUND_HALF_UP).toString();
        }
        if (channelVo.getRegisteredcapital() != null) {
            newValue = channelVo.getRegisteredcapital().setScale(2, BigDecimal.ROUND_HALF_UP).toString();
        }
        updateLog.append(getUpdateLog(oldValue, newValue, "注册资金"));
        updateLog.append(getUpdateLog(EnumUtil
                        .getMessageByCode(ChannelCompanyNatureEnum.class, oldChannel.getCompanynature()),
                EnumUtil.getMessageByCode(ChannelCompanyNatureEnum.class, channelVo.getCompanynature()), "企业性质"));
        updateLog.append(getUpdateLog(oldChannel.getUsername(), channelVo.getUsername(), "联系人"));
        updateLog.append(getUpdateLog(oldChannel.getTel(), channelVo.getTel(), "联系电话"));
        updateLog.append(getUpdateLog(oldChannel.getFax(), channelVo.getFax(), "传真"));
        updateLog.append(getUpdateLog(oldChannel.getUserid(), channelVo.getUserid(), "关联会员编号"));
        updateLog.append(getUpdateLog(oldChannel.getMobile(), channelVo.getMobile(), "联系手机号码"));
        updateLog.append(getUpdateLog(oldChannel.getQq(), channelVo.getQq(), "QQ"));
        updateLog.append(getUpdateLog(oldChannel.getWangwang(), channelVo.getWangwang(), "旺旺"));
        updateLog.append(getUpdateLog(oldChannel.getAddress(), channelVo.getAddress(), "详细地址"));
        updateLog.append(getUpdateLog(oldChannel.getArea(), channelVo.getArea(), "地区"));
        updateLog.append(getUpdateLog(oldChannel.getComment(), channelVo.getComment(), "备注信息"));
        updateLog.append(getUpdateLog(oldChannel.getComment1(), channelVo.getComment1(), "主营业务备注"));
        updateLog.append(getUpdateLog(oldChannel.getInuser(), channelVo.getInuser(), "操作人"));
        updateLog.append(getUpdateLog(oldChannel.getEmail(), channelVo.getEmail(), "邮箱"));
        updateLog.append(getUpdateLog(oldChannel.getInsourceid() == null ? "空" : oldChannel.getInsourceid().toString()
                , channelVo.getInsourceid() == null ? "空" : channelVo.getInsourceid().toString(), "渠道id"));
        updateLog.append(getUpdateLog(oldChannel.getQudaolevel() == null ? "空" : oldChannel.getQudaolevel().toString()
                , channelVo.getQudaolevel() == null ? "空" : channelVo.getQudaolevel().toString(), "渠道级别"));
        updateLog.append(getUpdateLog(oldChannel.getLegalrepresent(), channelVo.getLegalrepresent(), "法人代表"));
        updateLog.append(getUpdateLog(oldChannel.getLegalmobile(), channelVo.getLegalmobile(), "法人联系方式"));
        updateLog.append(getUpdateLog(oldChannel.getWeixin(), channelVo.getWeixin(), " 联系人微信"));
        updateLog.append(getUpdateLog(oldChannel.getBrandid(), channelVo.getBrandid(), "品牌id"));
        updateLog.append(getUpdateLog(oldChannel.getClassification(), channelVo.getClassification(), "商品分类"));
        updateLog.append(getUpdateLog(oldChannel.getCwFzr(), channelVo.getCwFzr(), "财务负责人"));
        updateLog.append(getUpdateLog(oldChannel.getCwLxfs(), channelVo.getCwLxfs(), "财务负责人电话"));
        updateLog.append(getUpdateLog(transfer(oldChannel.getSeltpurchase()), transfer(channelVo.getSeltpurchase()), "自采渠道"));
        if (!Objects.equals(Optional.ofNullable(oldChannel.getCustomcode()).orElse(""), Optional.ofNullable(channelVo.getCustomcode()).orElse(""))) {
            String oldCustomcode = "";
            String newCustomcode = "";
            if (StringUtils.isNotBlank(oldChannel.getCustomcode())) {
                oldCustomcode = oldChannel.getCustomcode() + Optional.ofNullable(this.getById(oldChannel.getCustomcode())).map(Ok3wQudao::getCompanyJc).orElse("");
            }
            if (StringUtils.isNotBlank(channelVo.getCustomcode())) {
                newCustomcode = channelVo.getCustomcode() + Optional.ofNullable(this.getById(channelVo.getCustomcode())).map(Ok3wQudao::getCompanyJc).orElse("");
            }
            updateLog.append(getUpdateLog(oldCustomcode, newCustomcode, "父级渠道"));
        }
        updateLog.append(getUpdateLog(Boolean.TRUE.equals(oldChannel.getIsauction()) ? "是" : "否", Boolean.TRUE.equals(channelVo.getIsauction()) ? "是" : "否", "参与竞价"));
        updateLog.append(getUpdateLog(oldChannel.getShouhoucontacts(), channelVo.getShouhoucontacts(), "售后联系人"));
        updateLog.append(getUpdateLog(oldChannel.getShouhoumobile(), channelVo.getShouhoumobile(), "售后联系人电话"));
        updateLog.append(getUpdateLog(oldChannel.getAfterAddress(), channelVo.getAfterAddress(), "售后联系地址"));
        updateLog.append(getUpdateLog(oldChannel.getSamekindqudao(), channelVo.getSamekindqudao(), "同类渠道"));
        updateLog.append(getUpdateLog(ChannelScaleEnum.getValue(oldChannel.getChannelscale()), ChannelScaleEnum.getValue(channelVo.getChannelscale()), "渠道规模"));
        if (!(Objects.nonNull(oldChannel.getLoanamountM()) && Objects.nonNull(channelVo.getLoanamountM()) && oldChannel.getLoanamountM().compareTo(channelVo.getLoanamountM()) == 0)) {
            updateLog.append(getUpdateLog(oldChannel.getLoanamountM(), channelVo.getLoanamountM(), "(贷款金额)月平均金额"));
        }
        if (!(Objects.nonNull(oldChannel.getLoanamount()) && Objects.nonNull(channelVo.getLoanamount()) && oldChannel.getLoanamount().compareTo(channelVo.getLoanamount()) == 0)) {
            updateLog.append(getUpdateLog(oldChannel.getLoanamount(), channelVo.getLoanamount(), "(贷款金额)总计结款金额"));
        }
        if (!Objects.equals(Optional.ofNullable(oldChannel.getBindareaid()).orElse(0), Optional.ofNullable(channelVo.getBindareaid()).orElse(0))) {
            String oldArea = Optional.ofNullable(areainfoService.getAreaInfoByAreaId2(oldChannel.getBindareaid())).map(Areainfo::getArea).orElse("");
            String newArea = Optional.ofNullable(areainfoService.getAreaInfoByAreaId2(channelVo.getBindareaid())).map(Areainfo::getArea).orElse("");
            updateLog.append(getUpdateLog(oldArea, newArea, "绑定地区"));
        }
        updateLog.append(getUpdateLog(LocalDateTimeUtil.format(oldChannel.getSignstarttime(), "yyyy-MM-dd HH:mm") + "~" + LocalDateTimeUtil.format(oldChannel.getSignendtime(), "yyyy-MM-dd HH:mm"), LocalDateTimeUtil.format(channelVo.getSignstarttime(), "yyyy-MM-dd HH:mm") + "~" + LocalDateTimeUtil.format(channelVo.getSignendtime(), "yyyy-MM-dd HH:mm"), "合同签约时间"));
    }

    private StringBuilder generateFinanceUpdateLog(Qudaoaccounts qudaoaccounts, StringBuilder updateLog) {
        Qudaoaccounts accountDb = qudaoaccountsService.getById(qudaoaccounts.getId());
        if (accountDb == null) {
            return updateLog;
        }
        if (accountDb.getIsgs() == 1) {
            updateLog.append(getUpdateLog(accountDb.getAccountnumber(), qudaoaccounts.getAccountnumber(), PaymentTypeEnum.getPublic()+"账号"));
        } else {
            updateLog.append(getUpdateLog(accountDb.getAccountnumber(), qudaoaccounts.getAccountnumber(), PaymentTypeEnum.getPrivate()+"账号"));
        }
        updateLog.append(getUpdateLog(accountDb.getUsername(), qudaoaccounts.getUsername(), "户名"));
        updateLog.append(getUpdateLog(accountDb.getOpeningbank(), qudaoaccounts.getOpeningbank(), "开户行"));
        updateLog.append(getUpdateLog(accountDb.getBranchBank(), qudaoaccounts.getBranchBank(), "开户支行"));
        updateLog.append(getUpdateLog(accountDb.getCity(), qudaoaccounts.getCity(), "city"));
        updateLog.append(getUpdateLog(accountDb.getBanknumber(), qudaoaccounts.getBanknumber(), "银行编号"));
        updateLog.append(getUpdateLog(transfer(accountDb.getIssamecity())
                , transfer(qudaoaccounts.getIssamecity()), "是否同城"));
        updateLog.append(getUpdateLog(transfer(accountDb.getIsenable())
                , transfer(qudaoaccounts.getIsenable()), "是否启用"));
        return updateLog;
    }

    private StringBuilder generateFinanceUpdateLogNew(Qudaoaccounts qudaoaccounts) {
        StringBuilder updateLog = new StringBuilder();
        Qudaoaccounts accountDb = qudaoaccountsService.getById(qudaoaccounts.getId());
        if (accountDb == null) {
            return updateLog;
        }
        if (accountDb.getIsgs() == 1) {
            updateLog.append(getUpdateLog(accountDb.getAccountnumber(), qudaoaccounts.getAccountnumber(), PaymentTypeEnum.getPublic()+"账号"));
        } else {
            updateLog.append(getUpdateLog(accountDb.getAccountnumber(), qudaoaccounts.getAccountnumber(), PaymentTypeEnum.getPrivate()+"账号"));
        }
        updateLog.append(getUpdateLog(accountDb.getUsername(), qudaoaccounts.getUsername(), "户名"));
        updateLog.append(getUpdateLog(accountDb.getOpeningbank(), qudaoaccounts.getOpeningbank(), "开户行"));
        updateLog.append(getUpdateLog(accountDb.getCity(), qudaoaccounts.getCity(), "city"));
        updateLog.append(getUpdateLog(accountDb.getBanknumber(), qudaoaccounts.getBanknumber(), "银行编号"));
        updateLog.append(getUpdateLog(transfer(accountDb.getIssamecity())
                , transfer(qudaoaccounts.getIssamecity()), "是否同城"));
        return updateLog;
    }

    private static String transfer(Integer value) {
        if (value == null) {
            return "空";
        }
        if (value == 0) {
            return "否";
        } else {
            return "是";
        }
    }

    private static StringBuilder generateFinanceLog(Qudaoaccounts newQudaoaccounts, StringBuilder updateLog) {
        if (StringUtils.isBlank(newQudaoaccounts.getAccountnumber())
                && StringUtils.isBlank(newQudaoaccounts.getOpeningbank())
                && StringUtils.isBlank(newQudaoaccounts.getUsername())) {
            return updateLog;
        }
        updateLog.append("新增");
        if (newQudaoaccounts.getIsgs() == 1) {
            updateLog.append(PaymentTypeEnum.getPublic());
        } else {
            updateLog.append(PaymentTypeEnum.getPrivate());
        }
        updateLog.append("账户：账号【").append(newQudaoaccounts.getAccountnumber()).append("】,");
        updateLog.append("户名【").append(newQudaoaccounts.getUsername()).append("】");
        updateLog.append("开户行【").append(newQudaoaccounts.getOpeningbank()).append("】");
        updateLog.append("开户支行【").append(newQudaoaccounts.getBranchBank()).append("】");
        updateLog.append("是否同城【").append(transfer(newQudaoaccounts.getIssamecity())).append("】");
        updateLog.append("是否启用【").append(transfer(newQudaoaccounts.getIsenable())).append("】");
        return updateLog;
    }


    private static StringBuilder generateFinanceLogNew(Qudaoaccounts newQuDaoAccounts) {
        StringBuilder updateLog = new StringBuilder();
        if (StringUtils.isBlank(newQuDaoAccounts.getAccountnumber())
                && StringUtils.isBlank(newQuDaoAccounts.getOpeningbank())
                && StringUtils.isBlank(newQuDaoAccounts.getUsername())) {
            return updateLog;
        }
        if (newQuDaoAccounts.getIsgs() == 1) {
            updateLog.append("新增"+PaymentTypeEnum.getPublic());
        } else {
            updateLog.append("新增"+PaymentTypeEnum.getPrivate());
        }
        updateLog.append("账户：账号【").append(newQuDaoAccounts.getAccountnumber()).append("】,");
        updateLog.append("户名【").append(newQuDaoAccounts.getUsername()).append("】");
        updateLog.append("开户行【").append(newQuDaoAccounts.getOpeningbank()).append("】");
        return updateLog;
    }

    private ApplyInfoReq automaticGenerationApply(ChannelVo channelVo
            , OaUserBO curStaffUser, Ok3wQudao newChannel, String kindName) {
        ApplyInfoReq applyInfo = new ApplyInfoReq();
        applyInfo.setApplytitle("新入围" + kindName.substring(0
                , kindName.length() - 1) + "-" + channelVo.getCompanyJc() + "(" + newChannel.getId() + ")");
        applyInfo.setInuser(curStaffUser.getUserId());
        applyInfo.setInusername(curStaffUser.getUserName());
        // 财务类 CategoryEnum枚举类中
        applyInfo.setCategoryid(3);
        // 供应商入围 CategoryEnum 枚举类中
        applyInfo.setScategoryid(80);
        applyInfo.setAreaid(curStaffUser.getArea1id());
        applyInfo.setAmount(BigDecimal.ZERO);
        applyInfo.setRelatedapply("");
        applyInfo.setHaspreload(Boolean.FALSE);
        applyInfo.setQudaochannelid(newChannel.getId());
//        添加附件信息到批签中
        List<Attachments> attachmentsTemp = attachmentsService
                .getAttachments(newChannel.getId(), AttachmentsTypeEnum.OK3W_QD.getType(), null,
                        true);
        String attachmetsStr = attachmentsTemp.stream().map(it -> it.getId().toString())
                .collect(Collectors.joining(","));
        applyInfo.setAttachfiles(attachmetsStr);
//        生成详细的说明
        StringBuilder applyRemark = new StringBuilder();
        applyRemark.append("基本信息").append('\n');
        applyRemark.append("公司名称：").append(channelVo.getCompany());
        applyRemark.append("法人代表：").append(channelVo.getLegalrepresent());
        if (StringUtils.isNotBlank(channelVo.getLegalmobile())) {
            applyRemark.append("法人联系方式：").append(channelVo.getLegalmobile());
        }
        if (StringUtils.isNotBlank(channelVo.getUsername())) {
            applyRemark.append("联系人：").append(channelVo.getUsername());
        }
        if (channelVo.getCompanynature() != null && channelVo.getCompanynature() > 0) {
            applyRemark.append("企业性质：").append(EnumUtil.getMessageByCode(ChannelCompanyNatureEnum.class
                    , channelVo.getCompanynature()));
        }
        if (channelVo.getPaytype() != null && channelVo.getPaytype() > 0) {
            applyRemark.append("结款方式：").append(channelVo.getPaytype());
        }
        if (StringUtils.isNotBlank(channelVo.getTel())) {
            applyRemark.append("联系电话：").append(channelVo.getTel());
        }
        if (CollectionUtils.isNotEmpty(channelVo.getContactsList())) {
            channelVo.getContactsList().forEach((ContactsDto contactsDto) -> {
                applyRemark.append("业务人联系人：").append(contactsDto.getUsername());
                applyRemark.append("业务人联系电话：").append(contactsDto.getTel());
            });
        }
        applyRemark.append("财务信息").append('\n');
        if (StringUtils.isNotBlank(channelVo.getCwFzr())) {
            applyRemark.append("财务负责人：").append(channelVo.getCwFzr());
        }
        if (StringUtils.isNotBlank(channelVo.getCwLxfs())) {
            applyRemark.append("财务联系方式：").append(channelVo.getCwLxfs());
        }
        if (CollectionUtils.isNotEmpty(channelVo.getFinanceList())) {
            channelVo.getFinanceList().forEach((FinanceDto financeDto) -> {
                ChannelKindLink basicKindInfo = financeDto.getKindLink();
                applyRemark.append("业务类型：").append(ChannelKindEnum.getValue(basicKindInfo.getKind()));
                if (StringUtils.isNotBlank(basicKindInfo.getSubject())) {
                    applyRemark.append("科目：").append(basicKindInfo.getSubject());
                }
                Integer INVOICING_FLAG_TRUE = 1;
                if (INVOICING_FLAG_TRUE.equals(basicKindInfo.getInvoicingFlag())) {
                    applyRemark.append("能否开发票：能");
                } else {
                    applyRemark.append("能否开发票：不能");
                }
                if (CollectionUtils.isNotEmpty(financeDto.getAccountsList())) {
                    Integer COMPANY_ACCOUNT_TYPE = 1;
                    financeDto.getAccountsList().forEach((QudaoaccountsDTO accountTemp) -> {
                        if (COMPANY_ACCOUNT_TYPE.equals(accountTemp.getIsgs())) {
                            applyRemark.append(PaymentTypeEnum.getPublic()+"账号：");
                        } else {
                            applyRemark.append(PaymentTypeEnum.getPrivate()+"账号：");
                        }
                        applyRemark.append(accountTemp.getAccountnumber());
                        applyRemark.append("户名：").append(accountTemp.getUsername())
                                .append("开户行：").append(accountTemp.getOpeningbank());
                    });
                }
            });
        }
        applyInfo.setRemark(applyRemark.toString());
        applyInfoService.saveApplyInfo(applyInfo);
        return applyInfo;
    }


    /**
     * 设置支付质保金
     * @param channelVo
     */
    private void setAssuranceDeposit(ChannelVo channelVo){
        //判断如果过不是九机  就不执行
        if(!XtenantEnum.isJiujiXtenant()){
            return;
        }
        if(wuliuApolloConfig.getAssuranceDepositFlag()){
            List<FinanceDto> financeList = channelVo.getFinanceList();
            if(CollectionUtils.isEmpty(financeList)){
                return;
            }
            financeList.stream().filter(item->ObjectUtil.isNotNull(item.getKindLink()) && ChannelKindEnum.ADMINISTRATIVE_GOODS.getCode().equals(item.getKindLink().getKind()))
                    .findFirst().ifPresent(link->{
                ChannelKindLink kindLink = link.getKindLink();
                Integer channelId = Optional.ofNullable(channelVo.getId()).orElseThrow(()->new CustomizeException("渠道id不能为空"));
                log.warn("调用财务组获取未支付质保金传入参数：{}",channelId);
                R<BigDecimal> result = caiwuApplyClient.getWarrantyDepositByChannelId(channelId);
                log.warn("调用财务组获取未支付质保金返回结果：{}",JSONUtil.toJsonStr(result));
                if(Result.SUCCESS == result.getCode()){
                    kindLink.setUnExpensedProcurement(result.getData());
                } else {
                    throw new CustomizeException("调用财务组获取未支付质保金异常:"+Optional.ofNullable(result.getUserMsg()).orElse(result.getMsg()));
                }
            });
        }
    }
    /**
     * 设置采购金额信息
     * @param channelVo
     */
    private void setProcurementInfo(ChannelVo channelVo){
        //判断如果过不是九机  就不执行
        if(!XtenantEnum.isJiujiXtenant() ){
            return;
        }
        List<FinanceDto> financeList = channelVo.getFinanceList();
        if(CollectionUtils.isEmpty(financeList)){
            return;
        }
        try {
            List<Integer> kindList = Arrays.asList(ChannelKindEnum.MARKETING_MATERIALS.getCode(), ChannelKindEnum.ADMINISTRATIVE_GOODS.getCode());
            financeList.stream().filter(item->ObjectUtil.isNotNull(item.getKindLink()) && kindList.contains(item.getKindLink().getKind()))
                    .forEach(link->{
                        //sysconfig表里获取域名
                        OaUserBO oaUserBO = Optional.ofNullable(requestComponent.getCurrentStaffId()).orElse(new OaUserBO());
                        List<SysConfig> sysConfigList = sysConfigService.lambdaQuery().eq(SysConfig::getCode, DOMAIN_NAME_OAWCF_CODE)
                                .eq(SysConfig::getXtenant, oaUserBO.getXTenant())
                                .list();
                        if(CollectionUtils.isEmpty(sysConfigList)){
                            throw new CustomizeException("域名获取为空");
                        }
                        //获取到域名
                        String domainName = sysConfigList.get(0).getValue();
                        //调用支撑组的接口查询金额
                        String url = domainName+"/kcApi/GetChannelAmountData?channelId="+channelVo.getId();
                        log.warn("调用支撑组获取金额请求地址：{}，Authorization：{}",url, requestComponent.getCurrentToken());
                        String result = HttpUtil.createGet(url).header("Authorization", requestComponent.getCurrentToken()).execute().body();
                        log.warn("调用支撑组获取金额返回结果：{}",result);
                        if (StringUtils.isEmpty(result)) {
                            throw new CustomizeException("用支撑组获取金额数据为空");
                        }
                        JSONObject jsonObject = JSON.parseObject(result);
                        Integer code = Integer.parseInt(Optional.ofNullable(jsonObject.getString("code")).orElseThrow(() -> new CustomizeException("获code为空")));
                        if(!NumberConstant.ZERO.equals(code)){
                            String msg = Optional.ofNullable(jsonObject.getString("userMsg")).orElse(jsonObject.getString("msg"));
                            throw new CustomizeException(String.format("支撑组获取金额数据异常:%s",msg));
                        }
                        String data1 = Optional.ofNullable(jsonObject.getString("data")).orElseThrow(() -> new CustomizeException("获取data为空"));
                        String caiGouListAmount = Optional.ofNullable(JSON.parseObject(data1).getString("caiGouListAmount")).orElseThrow(()-> new CustomizeException("获取常用采购金额为空"));
                        String assetListAmount = Optional.ofNullable(JSON.parseObject(data1).getString("assetListAmount")).orElseThrow(()-> new CustomizeException("获取固定采购金额为空"));
                        String materialApplyListAmount = Optional.ofNullable(JSON.parseObject(data1).getString("materialApplyListAmount")).orElseThrow(()-> new CustomizeException("获取营销采购金额为空"));
                        ChannelKindLink kindLink = link.getKindLink();
                        kindLink.setCommonProcurement(new BigDecimal(caiGouListAmount));
                        kindLink.setFixedProcurement(new BigDecimal(assetListAmount));
                        kindLink.setMarketingProcurement(new BigDecimal(materialApplyListAmount));
                    });
            //进行合同数据的开发
            log.warn("调用支撑组获取合同传入参数：{}",channelVo.getId());
            R<List<ContractInfoVO>> listR = contractCloud.infoListByChannelId(channelVo.getId());
            log.warn("调用支撑组获取合同返回结果：{}",JSONUtil.toJsonStr(listR));
            if(!NumberConstant.ZERO.equals(listR.getCode())){
                throw new CustomizeException(String.format("调用支撑组获取合同返回异常：%s",Optional.ofNullable(listR.getMsg()).orElse(listR.getUserMsg())));
            }
            channelVo.setContractInfoVOList(listR.getData());
        } catch (Exception e){
            String comment = String.format("获取支撑组额外信息异常,渠道id为：%s,异常信息为：%s", channelVo.getId(), e.getMessage());
            log.error(comment,e);
            messageSendService.sendOaMessage(0, "", "13495", comment);
        }

    }

    @Override
    public R<ChannelVo> getChannelVo(Integer id) {
        Ok3wQudao qudao = this.getById(id);
        if (qudao == null) {
            throw new CustomizeException("该渠道不存在");
        }
        ChannelVo channelVo = channelMapStruct.toVo(qudao);
        channelVo.setKinds(new ArrayList<>());
        //查询渠道类型关联表
        List<ChannelKindLink> channelKindLinks = channelKindLinkService.lambdaQuery()
                .eq(ChannelKindLink::getChannelId, id)
                .eq(ChannelKindLink::getDelFlag, Boolean.FALSE)
                .list();
        if (CollectionUtils.isNotEmpty(channelKindLinks)) {
            //查询类型关联的财务信息
//        账户是否启用没有设置的时候，数据库默认给的0
            Integer ACCOUNT_ENABLE_DEFAULT = 0;
            Map<Integer, List<QudaoaccountsDTO>> accountMap = qudaoaccountsService.lambdaQuery()
                    .eq(Qudaoaccounts::getIsdel, 0)
                    .in(Qudaoaccounts::getLinkId, channelKindLinks.stream()
                            .map(ChannelKindLink::getId).collect(Collectors.toList()))
                    .list().stream().map(channelMapStruct::toAccountsDTO).peek((QudaoaccountsDTO it) -> {
                        if (ACCOUNT_ENABLE_DEFAULT.equals(it.getIsenable())) {
                            it.setIsenable(null);
                        }

                    }).collect(Collectors.groupingBy(QudaoaccountsDTO::getLinkId));
            //组装财务信息
            List<FinanceDto> financeDtos = new ArrayList<>();
            channelKindLinks.forEach((ChannelKindLink it) -> {
                channelVo.getKinds().add(it.getKind());
                FinanceDto financeDto = new FinanceDto();
                financeDto.setKindLink(it);
                financeDto.setAccountsList(accountMap.getOrDefault(it.getId(), Collections.emptyList()));
                financeDtos.add(financeDto);
            });
            channelVo.setFinanceList(financeDtos);
            //获取业务联系人信息
            List<Qudaocontactss> qudaocontactsses = qudaocontactssService.lambdaQuery()
                    .eq(Qudaocontactss::getQudaoid, id)
                    .eq(Qudaocontactss::getIsdel, 0)
                    .list();
            if (CollectionUtils.isNotEmpty(qudaocontactsses)) {
                channelVo.setContactsList(qudaocontactsses.stream().map(channelMapStruct::toContactsDto)
                        .collect(Collectors.toList()));
            }
        }
        List<Attachments> existAttachmentList = attachmentsService
                .getAttachments(id, AttachmentsTypeEnum.OK3W_QD.getType(), null, true);
        channelVo.setAttachments(existAttachmentList.stream().map(channelMapStruct::toFileBO)
                .collect(Collectors.toList()));

        String usingOaLogConfigValue = sysConfigService.getValueByCode(ConfigEnum.USING_TIDB_LOG);
        if (OPEN_VALUE.equals(usingOaLogConfigValue)) {
            Ok3wQudaoLogDocument logDocument = quDaoLogService.getOk3wQudaoLogDocument(id.longValue());
            if (logDocument != null) {
                List<Conts> conts = logDocument.getConts();
                if (CollectionUtils.isNotEmpty(conts)) {
                    conts.forEach((Conts it) -> {
                        it.setComment(it.getComment().replace("<", "&lt;"));
                        it.setComment(it.getComment().replace(">", "&gt;"));
                    });
                }
            }
            channelVo.setLogs(logDocument);
        } else {
            Optional<Ok3wQudaoLogDocument> logDocument = ok3wQudaoLogRepository.findById(id);
            if (logDocument.isPresent()) {
                List<Conts> conts = logDocument.get().getConts();
                if (CollectionUtils.isNotEmpty(conts)) {
                    conts.forEach((Conts it) -> {
                        it.setComment(it.getComment().replace("<", "&lt;"));
                        it.setComment(it.getComment().replace(">", "&gt;"));
                    });
                }
            }
            logDocument.ifPresent(channelVo::setLogs);
        }

        try{
            Optional.ofNullable(channelVo.getLogs()).ifPresent(item->{
                List<Conts> conts = item.getConts();
                if(CollectionUtils.isNotEmpty(conts)){
                    item.setConts(conts.stream()
                            .sorted(Comparator.comparing(Conts::getDTime, Comparator.nullsFirst(Comparator.naturalOrder())))
                            .collect(Collectors.toList()));
                }
            });
        } catch (Exception ex){
            String content = String.format("渠道日志排序异常，渠道id：%s", id);
            messageSendService.sendOaMessage(0, "", "13495", content);
            log.error(content,ex);
        }


        List<ChannelProvinceCity> list = channelProvinceCityService.lambdaQuery()
                .eq(ChannelProvinceCity::getChannelId, id).list();
        if (CollectionUtils.isNotEmpty(list)) {
            List<ChannelProvinceCityDto> provinceCityList = list.stream().map(channelMapStruct::toChannelProvinceCityDto)
                    .collect(Collectors.toList());
            channelVo.setProvinceCityList(provinceCityList);
        }
        //设置采购金额信息
        setProcurementInfo(channelVo);
        //设置支付质保金
        setAssuranceDeposit(channelVo);
        return R.success(channelVo);
    }


    @Override
    public R<Boolean> channelInventoryV1(ChannelInventory channelInventory) {
        OaUserBO currentStaffId = requestComponent.getCurrentStaffId();
        Integer xTenant = currentStaffId.getXTenant();
        //九机的为判断该渠道下的业务类型有没有填写科目只有大件 小件 维修配件的情况下才进行校验
        if (xTenant == 0) {
            List<String> rank = currentStaffId.getRank();
            if(!rank.contains(CHANNEL_STOCKTAKING)){
                throw new CustomizeException("没有该权限操作此功能");
            }
            String comment = checkSubject(channelInventory);
            if (StringUtils.isNotEmpty(comment)) {
                throw new CustomizeException(comment);
            }
        }
        List<ChannelKindLink> channelKindLinkList = channelKindLinkService.lambdaQuery()
                .eq(ChannelKindLink::getChannelId, channelInventory.getId())
                .eq(ChannelKindLink::getKind, channelInventory.getKind()).list();
        boolean update = channelKindLinkService.lambdaUpdate()
                .set(ChannelKindLink::getChannelState, channelInventory.getState())
                .eq(ChannelKindLink::getChannelId, channelInventory.getId())
                .eq(ChannelKindLink::getKind, channelInventory.getKind()).update();
        //通知运营组修改
        updateOperateChannel(Collections.singletonList(channelInventory));
        //通知财务修改数据
        noticeFinance(Collections.singletonList(channelInventory));
        if (update) {
            String msg = getUpdateLog(ChannelCooperationStatusEnum.getValue(channelKindLinkList.stream().findFirst().orElse(new ChannelKindLink()).getChannelState()), ChannelCooperationStatusEnum.getValue(channelInventory.getState()), ChannelKindEnum.getValue(channelInventory.getKind())+"渠道盘点");
            addChannelLognew(channelInventory.getId(), msg, currentStaffId.getUserName(), LocalDateTime.now());
            stringRedisTemplate.delete(CHANNEL_REDIS_KEY);
            stringRedisTemplate.delete(StrUtil.format("{}_{}", RedisKeys.CHANNEL_LIST_KEY,
                    MyDynamicRoutingDataSource.isTaxModel()));
            stringRedisTemplate.delete(StrUtil.format("{}_{}", RedisKeys.CHANNEL_LIST_KEY_ALL,
                    MyDynamicRoutingDataSource.isTaxModel()));
            return R.success(Boolean.TRUE);
        } else {
            return R.error("渠道盘点失败");
        }

    }

    @Override
    public R<Boolean> channelInventory(ChannelInventory channelInventory) {
        OaUserBO currentStaffId = requestComponent.getCurrentStaffId();
        Integer xTenant = currentStaffId.getXTenant();
        //九机的为判断该渠道下的业务类型有没有填写科目只有大件 小件 维修配件的情况下才进行校验
        if (xTenant == 0) {
            String comment = checkSubject(channelInventory);
            if (StringUtils.isNotEmpty(comment)) {
                throw new CustomizeException(comment);
            }
        }
        List<ChannelKindLink> channelKindLinkList = channelKindLinkService.lambdaQuery()
                .eq(ChannelKindLink::getChannelId, channelInventory.getId())
                .eq(ChannelKindLink::getKind, channelInventory.getKind()).list();
        boolean update = channelKindLinkService.lambdaUpdate()
                .set(ChannelKindLink::getChannelState, channelInventory.getState())
                .eq(ChannelKindLink::getChannelId, channelInventory.getId())
                .eq(ChannelKindLink::getKind, channelInventory.getKind()).update();
        //通知运营组修改
        updateOperateChannel(Collections.singletonList(channelInventory));
        //通知财务修改数据
        noticeFinance(Collections.singletonList(channelInventory));
        if (update) {
            String msg = getUpdateLog(ChannelCooperationStatusEnum.getValue(channelKindLinkList.stream().findFirst().orElse(new ChannelKindLink()).getChannelState()), ChannelCooperationStatusEnum.getValue(channelInventory.getState()), ChannelKindEnum.getValue(channelInventory.getKind())+"渠道盘点");
            addChannelLognew(channelInventory.getId(), msg, currentStaffId.getUserName(), LocalDateTime.now());
            stringRedisTemplate.delete(CHANNEL_REDIS_KEY);
            stringRedisTemplate.delete(StrUtil.format("{}_{}", RedisKeys.CHANNEL_LIST_KEY,
                    MyDynamicRoutingDataSource.isTaxModel()));
            stringRedisTemplate.delete(StrUtil.format("{}_{}", RedisKeys.CHANNEL_LIST_KEY_ALL,
                    MyDynamicRoutingDataSource.isTaxModel()));
            return R.success(Boolean.TRUE);
        } else {
            return R.error("渠道盘点失败");
        }

    }

    /**
     * 渠道对对外修改状态接口提供
     *
     * @param list
     * @return
     */
    @Override
    @DSTransactional
    public R<Boolean> changeChannelState(List<ChannelInventory> list, OaUserBO currentStaffId) {
        try {
            if (CollectionUtils.isEmpty(list)) {
                return R.success("传入参数为空");
            }
            Integer xTenant = currentStaffId.getXTenant();
            if (xTenant == 0) {
                for (ChannelInventory channelInventory : list) {
                    String comment = checkSubject(channelInventory);
                    if (StringUtils.isNotEmpty(comment)) {
                        //开启多线程进行日志的保存
                        addChannelLognew(channelInventory.getId(), "批签完结，" + comment, currentStaffId.getUserName(), LocalDateTime.now());
                    } else {
                        boolean update = channelKindLinkService.lambdaUpdate()
                                .set(ChannelKindLink::getChannelState, channelInventory.getState())
                                .eq(ChannelKindLink::getChannelId, channelInventory.getId())
                                .eq(ChannelKindLink::getKind, channelInventory.getKind()).update();
                        if (!update) {
                            return R.success("渠道盘点失败");
                        }
                    }
                }
            } else {
                list.forEach((ChannelInventory channelInventory) -> {
                    boolean update = channelKindLinkService.lambdaUpdate()
                            .set(ChannelKindLink::getChannelState, channelInventory.getState())
                            .eq(ChannelKindLink::getChannelId, channelInventory.getId())
                            .eq(ChannelKindLink::getKind, channelInventory.getKind()).update();
                    if (!update) {
                        throw new CustomizeException("渠道盘点失败!");
                    }
                });
            }
            stringRedisTemplate.delete(CHANNEL_REDIS_KEY);
            stringRedisTemplate.delete(StrUtil.format("{}_{}", RedisKeys.CHANNEL_LIST_KEY,
                    MyDynamicRoutingDataSource.isTaxModel()));
            stringRedisTemplate.delete(StrUtil.format("{}_{}", RedisKeys.CHANNEL_LIST_KEY_ALL,
                    MyDynamicRoutingDataSource.isTaxModel()));
        } catch (Exception e) {
            log.error("渠道状态修改失败,接收参数：[{}],报错信息：[{}]", list, e.getMessage(), e);
            return R.error("渠道状态更新失败！");
        }
        //通知运营组修改
        updateOperateChannel(list);
        //通知财务修改数据
        noticeFinance(list);
        return R.success("渠道状态更新成功");
    }

    /**
     * 通知财务进行数据修改
     * @param list
     */
    private void noticeFinance(List<ChannelInventory> list){
        //判断是否为九机
        if(XtenantJudgeUtil.isJiujiMore() && CollectionUtils.isNotEmpty(list)){
            try {
                for (ChannelInventory channelInventory:list) {
                    //查询出渠道所有的业务类型
                    Integer channelId = channelInventory.getId();
                    //判断渠道授权 如果过不在1, 75, 76, 77 那就停止
                    Ok3wQudao channel = this.lambdaQuery().eq(Ok3wQudao::getId, channelId).in(Ok3wQudao::getAuthorizeid, Arrays.asList(1, 75, 76, 77)).one();
                    if(ObjectUtil.isNull(channel)){
                        continue ;
                    }
                    List<Integer> kindList = Arrays.asList(ChannelKindEnum.MOBILE_ACCESSORIES.getCode(), ChannelKindEnum.MAINTENANCE_ACCESSORIES.getCode(), ChannelKindEnum.MOBILE.getCode());
                    //判断修改是否是大件、小件、维修配件,如果过不包含那就直接结束
                    if(!kindList.contains(channelInventory.getKind())){
                        continue ;
                    }
                    List<ChannelKindLink> kindLinkList = channelKindLinkService.lambdaQuery().eq(ChannelKindLink::getChannelId, channelId).list();
                    List<ChannelKindLink> handleKindList = kindLinkList.stream().filter(item -> kindList.contains(item.getKind())).collect(Collectors.toList());
                    //如果没有 大件 小件 维修配件的业务类型那就停止
                    if(CollectionUtils.isEmpty(handleKindList)){
                        continue;
                    }
                    List<Integer> disableList = Arrays.asList(ChannelCooperationStatusEnum.CANCELLED.getCode(), ChannelCooperationStatusEnum.STOP_COOPERATION.getCode());
                    Integer state = channelInventory.getState();
                    SupplierEnableVO supplierEnableVO = new SupplierEnableVO();
                    supplierEnableVO.setSupplierId(channelId);
                    //禁用：供应商下面所有kind 都（停止合作/作废）才触发禁用
                    if(disableList.contains(state)){
                        List<ChannelKindLink> handleKindListNew = handleKindList.stream().filter(obj -> disableList.contains(obj.getChannelState())).collect(Collectors.toList());
                        if(handleKindList.size()==handleKindListNew.size()){
                            supplierEnableVO.setEnableFlag(Boolean.FALSE);
                        }
                    }
                    //启用：供应商禁用的前提下   有任一一个kind = 合作中 触发启用
                    List<Integer> enableList = Arrays.asList(ChannelCooperationStatusEnum.COOPERATION.getCode(), ChannelCooperationStatusEnum.APPLICATION.getCode());
                    if(enableList.contains(state)){
                        Optional.of(handleKindList.stream().filter(obj -> enableList.contains(obj.getChannelState())).findAny()).ifPresent(link->{
                            supplierEnableVO.setEnableFlag(Boolean.TRUE);
                        });
                    }
                    if(ObjectUtil.isNotNull(supplierEnableVO.getEnableFlag())){
                        R<String> result = voucherRecordCloud.enableVendor(supplierEnableVO);
                        String comment = String.format("通知财务组修改信息失败 传入参数：%s，返回结果：%s", supplierEnableVO, result);
                        log.warn(comment);
                        if(!result.isSuccess()){
                            throw new CustomizeException(comment+"失败原因"+Optional.ofNullable(result.getMsg()).orElse(result.getUserMsg()));
                        }
                    }

                }
            } catch (Exception e){
                log.error("通知财务组修改信息失败:{}",e.getMessage(),e);
                messageSendService.sendOaMessage(0, "", "13495", "通知财务组修改信息失败传入数据"+list.toString()+e.getMessage());
            }
        }
    }

    /**
     * 如果是九机并且修改的渠道类型为营销物料，修改状态为 合作中 或者 作废的时候通知
     * @param list
     */
    private void updateOperateChannel(List<ChannelInventory> list){
        //判断是否为九机
        if(XtenantJudgeUtil.isJiujiMore() && CollectionUtils.isNotEmpty(list)){
            list.forEach(item -> {
                try {
                    Integer kind = Optional.ofNullable(item.getKind()).orElse(Integer.MAX_VALUE);
                    Integer state = Optional.ofNullable(item.getState()).orElse(Integer.MAX_VALUE);
                    //判断是营销物料 并且修改状态为 作废或者停止合作 的时候进行通知
                    List<Integer> statusList = Arrays.asList(ChannelCooperationStatusEnum.STOP_COOPERATION.getCode(), ChannelCooperationStatusEnum.CANCELLED.getCode());
                    if(ChannelKindEnum.MARKETING_MATERIALS.getCode().equals(kind) && statusList.contains(state)){
                        //调用运营组接口进行通知
                        String toekn = Optional.ofNullable(requestComponent.getCurrentToken()).orElseThrow(() -> new CustomizeException("登录失效"));
                        Integer channelId =  Optional.ofNullable(item.getId()).orElseThrow(() -> new CustomizeException("渠道id不能为空"));
                        //sysconfig表里获取域名
                        List<SysConfig> sysConfigList = sysConfigService.lambdaQuery().eq(SysConfig::getCode, OA_DOMAIN_NAME_CODE)
                                .eq(SysConfig::getXtenant, requestComponent.getCurrentStaffId().getXTenant())
                                .list();
                        if(CollectionUtils.isNotEmpty(sysConfigList)){
                            String domain = sysConfigList.get(0).getValue();
                            String url = domain+"/Medium/ClearMaterialPrcieProducer?qudaoId="+channelId;
                            String result = HttpUtil.createGet(url).header("token", toekn).execute().body();
                            log.warn("通知运营组删除渠道信息 传入参数：{}，返回结果：{}",url,result);
                        }
                    }
                } catch (Exception e){
                    log.error("通知运营组删除渠道信息失败",e);
                    messageSendService.sendOaMessage(0, "", "13495", "通知运营组删除渠道信息失败传入数据"+list.toString());
                }
            });

        }
    }

    /**
     * 判断该渠道下的业务类型有没有填写科目
     * 只有大件 小件 维修配件的情况下才进行校验
     *
     * @param channelInventory
     * @return
     */
    private String checkSubject(ChannelInventory channelInventory) {
        //如果过是九机那就不进行判断
        if(XtenantEnum.isJiujiXtenant()){
            return null;
        }

        List<Integer> kindList = Arrays.asList(ChannelKindEnum.MOBILE.getCode(), ChannelKindEnum.MOBILE_ACCESSORIES.getCode(), ChannelKindEnum.MAINTENANCE_ACCESSORIES.getCode());
        Integer kind = Optional.ofNullable(channelInventory.getKind()).orElseThrow(() -> new CustomizeException("业务类型不能为空"));

        if (kindList.contains(kind)) {
            List<ChannelKindLink> list = channelKindLinkService.lambdaQuery()
                    .eq(ChannelKindLink::getChannelId, channelInventory.getId())
                    .eq(ChannelKindLink::getKind, channelInventory.getKind())
                    .isNotNull(ChannelKindLink::getSubject).list();
            if (CollectionUtils.isEmpty(list)) {
                StringBuilder comment = new StringBuilder("{").append(ChannelKindEnum.getValue(kind)).append("}的科目为空，盘点状态不变更");
                return comment.toString();
            } else {
                return null;
            }

        } else {
            return null;
        }

    }


    @Override
    public IPage<ChannelStockQueryVo> getChannelStockQuery(ChannelStockQueryReq channelStockQueryReq) {
        IPage<ChannelStockQueryVo> page = this.baseMapper
                .getChannelStockQuery(new Page(channelStockQueryReq.getCurrent(), channelStockQueryReq.getSize())
                        , channelStockQueryReq);
        if (page.getTotal() == 0) {
            return this.baseMapper
                    .getChannelStockQueryOld(new Page(channelStockQueryReq.getCurrent(), channelStockQueryReq.getSize())
                            , channelStockQueryReq);
        } else {
            return page;
        }
    }

    /**
     * 渠道id和授权id校验渠道是否匹配
     *
     * @param channelId
     * @param authId
     * @return
     */
    @Override
    @DS("ch999oanew")
    public Boolean check(Long channelId, Integer authId) {
        if (channelId == null) {
            throw new CustomizeException("渠道id不能为空");
        }
        if (authId == null) {
            throw new CustomizeException("授权id不能为空");
        }
        checkAuthorize(authId);
        //判断该授权下的渠道是否存在
        Ok3wQudao one = this.lambdaQuery().eq(Ok3wQudao::getId, channelId).eq(Ok3wQudao::getAuthorizeid, authId).one();
        return !ObjectUtils.isEmpty(one);
    }

    /**
     *
     * @param authId
     */
    @Override
    @DS("ch999oanew")
    public void checkAuthorize(Integer authId){
        //进行authI的的校验
        List<Authorize> authorizeList = authorizeService.list();
        if (CollectionUtils.isEmpty(authorizeList)) {
            throw new CustomizeException("当前租户授权查询为空");
        }
        List<Integer> collect = authorizeList.stream().map(Authorize::getId).collect(Collectors.toList());
        if (!collect.contains(authId)) {
            throw new CustomizeException("授权id不在当前租户授权体系内");
        }

    }

    /**
     * 查询渠道类型
     *
     * @return
     */
    @Override
    public List<InSourceResVo> getInSourceList() {
        OaUserBO currentStaffId = requestComponent.getCurrentStaffId();
        List<InSourceResVo> list = null;
        //渠道隔离全局配置是否启用
        R<Boolean> booleanR = getChannelAuthorizeConfig(currentStaffId);

        //渠道隔离全局配置是否启用
        if (booleanR != null && Boolean.TRUE.equals(booleanR.getData())) {
            list = inSourceService.findByAuthorizeId(currentStaffId.getAuthorizeId());
        } else {
            list = inSourceService.findByAuthorizeId(null);
        }
        return list;
    }

    @Override
    public List<ChannelResVo> getChannelListByHost(ChannelReqVo vo){
        String host = vo.getHost() + "/api/ok3wqudao/channel-list/crossSystem";
        HttpResponse response = HttpUtil.createPost(host)
                .header("xservicename", "oa-stock")
                .body(JSONUtil.toJsonStr(vo))
                .execute();
        log.warn("快系统渠道调用查询域名：{}，传入参数：{}",host, JSONUtil.toJsonStr(vo));
        if(response.isOk()){
            String body = response.body();
            log.warn("快系统渠道调用查询域名：{}，返回结果：{}",host, JSONUtil.toJsonStr(body));
            R result = JSONUtil.toBean(JSONUtil.toJsonStr(body), R.class);
            if(result.isSuccess()){
                return JSONUtil.toList(JSONUtil.toJsonStr(result.getData()),ChannelResVo.class);
            }
            throw new CustomizeException(Optional.ofNullable(result.getUserMsg()).orElse(result.getMsg()));
        }
        return new ArrayList<>();
    }

    /**
     * 渠道id查询渠道信息
     *
     * @param ids
     * @return
     */
    @Override
    @DS(DataSourceConstants.CH999_OA_NEW)
    public List<Ok3wQudao> getQudaoByIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return CommonUtils.bigDataInQuery(ids , qudaoId -> this.lambdaQuery().in(Ok3wQudao::getId, qudaoId).list());
    }

    @Override
    public List<ChannelResVo> getChannelListCrossSystem(ChannelReqVo vo){
        //获取渠道
        List<Ok3wQuDaoDto> list = this.baseMapper.selectAllList();
        list = filterKindAndInsourceV2(vo, list);
        //搜索渠道id和名称
        if (StringUtils.isNotEmpty(vo.getQ())) {
            list = list.stream().filter(v -> {
                String idStr = v.getId() + "";
                return idStr.contains(vo.getQ())
                        || (StringUtils.isNotEmpty(v.getCustomCode()) && v.getCustomCode().contains(vo.getQ()))
                        || v.getCompanyJc().contains(vo.getQ());
            }).collect(Collectors.toList());
        }

        if (vo.getLimit() == null || vo.getLimit() == 0) {
            vo.setLimit(CHANNEL_LIST_MAXSIZE);
        }
        List<ChannelResVo> resultList = list.stream().map(channelMapStruct::toChannelResVo).collect(Collectors.toList());
        resultList = resultList.stream().distinct().limit(vo.getLimit()).collect(Collectors.toList());
        return resultList;
    }


    /**
     * 获取渠道list
     *
     * @param vo
     * @return
     */
    @Override
    public List<ChannelResVo> getChannelListV2(ChannelReqVo vo) {
        OaUserBO currentStaffId = requestComponent.getCurrentStaffId();
        log.info("调用查询渠道信息，参数：{}", vo);
        //获取渠道
        boolean isTaxModel = MyDynamicRoutingDataSource.isTaxModel();
        String key = StrUtil.format("{}_{}", RedisKeys.CHANNEL_LIST_KEY_ALL, isTaxModel);
        String channelStr = stringRedisTemplate.opsForValue().get(key);
        if (StringUtils.isEmpty(channelStr)) {
            List<Ok3wQuDaoDto> list = this.baseMapper.selectAllListV2();
            stringRedisTemplate.opsForValue()
                    .set(key, JSONUtil.toJsonStr(list), 24, TimeUnit.HOURS);
            channelStr = JSONUtil.toJsonStr(list);
        }
        List<Ok3wQuDaoDto> list = JSONUtil.toList(channelStr, Ok3wQuDaoDto.class).stream()
                // 税务模式只取合作中的渠道
                .filter(o3qd -> !(isTaxModel && ObjectUtil.notEqual(1, o3qd.getChannelState())))
                .collect(Collectors.toList());

        //渠道隔离全局配置是否启用
        R<Boolean> booleanR = getChannelAuthorizeConfig(currentStaffId);

        //授权隔离
        if (booleanR != null && Boolean.TRUE.equals(booleanR.getData())) {
            list = list.stream().filter(v -> v.getAuthorizeId() != null && v.getAuthorizeId().equals(currentStaffId.getAuthorizeId())).collect(Collectors.toList());
        }

        list = filterKindAndInsourceV2(vo, list);

        //搜索渠道id和名称
        if (StringUtils.isNotEmpty(vo.getQ())) {
            list = list.stream().filter(v -> {
                String idStr = v.getId() + "";
                return idStr.contains(vo.getQ())
                        || (StringUtils.isNotEmpty(v.getCustomCode()) && v.getCustomCode().contains(vo.getQ()))
                        || v.getCompanyJc().contains(vo.getQ());
            }).collect(Collectors.toList());
        }

        if (vo.getLimit() == null || vo.getLimit() == 0) {
            vo.setLimit(CHANNEL_LIST_MAXSIZE);
        }
        List<ChannelResVo> resultList = list.stream().map(channelMapStruct::toChannelResVo).collect(Collectors.toList());
        resultList = resultList.stream().distinct().limit(vo.getLimit()).collect(Collectors.toList());

        return resultList;
    }

    /**
     * 获取渠道list
     *
     * @param vo
     * @return
     */
    @Override
    public List<ChannelResVo> getChannelList(ChannelReqVo vo) {
        OaUserBO currentStaffId = requestComponent.getCurrentStaffId();
        log.info("调用查询渠道信息，参数：{}", vo);
        //获取渠道
        String channelStr = stringRedisTemplate.opsForValue().get(StrUtil.format("{}_{}", RedisKeys.CHANNEL_LIST_KEY,
                MyDynamicRoutingDataSource.isTaxModel()));
        if (StringUtils.isEmpty(channelStr)) {
            List<Ok3wQuDaoDto> list = this.baseMapper.selectAllList();
            stringRedisTemplate.opsForValue()
                    .set(StrUtil.format("{}_{}", RedisKeys.CHANNEL_LIST_KEY, MyDynamicRoutingDataSource.isTaxModel()), JSONUtil.toJsonStr(list), 10, TimeUnit.MINUTES);
            channelStr = JSONUtil.toJsonStr(list);
        }
        List<Ok3wQuDaoDto> list = JSONUtil.toList(channelStr, Ok3wQuDaoDto.class);

        //渠道隔离全局配置是否启用
        R<Boolean> booleanR = getChannelAuthorizeConfig(currentStaffId);

        //授权隔离
        if (booleanR != null && Boolean.TRUE.equals(booleanR.getData())) {
            list = list.stream().filter(v -> v.getAuthorizeId() != null && v.getAuthorizeId().equals(currentStaffId.getAuthorizeId())).collect(Collectors.toList());
        }

        list = filterKindAndInsource(vo, list);

        //搜索渠道id和名称
        if (StringUtils.isNotEmpty(vo.getQ())) {
            list = list.stream().filter(v -> {
                String idStr = v.getId() + "";
                return idStr.contains(vo.getQ())
                        || (StringUtils.isNotEmpty(v.getCustomCode()) && v.getCustomCode().contains(vo.getQ()))
                        || v.getCompanyJc().contains(vo.getQ());
            }).collect(Collectors.toList());
        }

        if (vo.getLimit() == null || vo.getLimit() == 0) {
            vo.setLimit(CHANNEL_LIST_MAXSIZE);
        }
        List<ChannelResVo> resultList = list.stream().map(channelMapStruct::toChannelResVo).collect(Collectors.toList());
        resultList = resultList.stream().distinct().limit(vo.getLimit()).collect(Collectors.toList());

        return resultList;
    }

    /**
     * 渠道InsourceId过滤
     * 渠道类别过滤
     *
     * @param vo
     * @param list
     * @return
     */
    private static List<Ok3wQuDaoDto> filterKindAndInsource(ChannelReqVo vo, List<Ok3wQuDaoDto> list) {
        List<Ok3wQuDaoDto> resultList = new ArrayList<>(list);
        if (StringUtils.isNotEmpty(vo.getInsourceid())) {
            //渠道InsourceId过滤
            List<String> insourceIdList = Arrays.stream(vo.getInsourceid().trim().split(",")).collect(Collectors.toList());
            resultList = resultList.stream().filter(v -> insourceIdList.contains(v.getInsourceId() + "")).collect(Collectors.toList());
        }

        if (StringUtils.isNotEmpty(vo.getKinds())) {
            //渠道类别过滤
            List<String> kindList = Arrays.stream(vo.getKinds().trim().split(",")).collect(Collectors.toList());
            resultList = resultList.stream().filter(v -> kindList.contains(v.getKinds() + "")).collect(Collectors.toList());
        }
        return resultList;
    }


    /**
     * 渠道InsourceId过滤
     * 渠道类别过滤
     *
     * @param vo
     * @param list
     * @return
     */
    private static List<Ok3wQuDaoDto> filterKindAndInsourceV2(ChannelReqVo vo, List<Ok3wQuDaoDto> list) {
        List<Ok3wQuDaoDto> resultList = new ArrayList<>(list);
        if (StringUtils.isNotEmpty(vo.getInsourceid())) {
            //渠道InsourceId过滤
            List<String> insourceIdList = Arrays.stream(vo.getInsourceid().trim().split(",")).collect(Collectors.toList());
            resultList = resultList.stream().filter(v -> insourceIdList.contains(v.getInsourceId() + "")).collect(Collectors.toList());
        }

        if (StringUtils.isNotEmpty(vo.getKinds())) {
            //渠道类别过滤
            List<String> kindList = Arrays.stream(vo.getKinds().trim().split(",")).collect(Collectors.toList());
            resultList = resultList.stream().filter(v -> kindList.contains(v.getKinds() + "")).collect(Collectors.toList());
        }

        if(CollectionUtils.isNotEmpty(vo.getChannelStateList())){
            //渠道状态判断
            List<Integer> channelStateList = vo.getChannelStateList();
            resultList = resultList.stream().filter(v -> channelStateList.contains(v.getChannelState())).collect(Collectors.toList());
        }

        return resultList;
    }

    private static String brandIdToName(String brandId, Map<Integer, Brand> brandMap) {
        if (StringUtils.isBlank(brandId)) {
            return "";
        }
        StringBuilder builder = new StringBuilder();
        String[] split = brandId.split(",");
        for (String id : split) {
            if (StringUtils.isNotEmpty(id)) {
                Brand brand = brandMap.get(Integer.valueOf(id));
                if (brand != null) {
                    builder.append(brand.getName()).append(',');
                }
            }
        }
        if (builder.length() > 0) {
            return builder.deleteCharAt(builder.length() - 1).toString();
        } else {
            return "";
        }

    }

    /**
     * 根据渠道的基本信息查询是否有已存在的渠道
     *
     * @param channelVo
     * @return
     */
    private Ok3wQudao getByBasicInfo(ChannelVo channelVo) {
        List<Ok3wQudao> channelList = this.lambdaQuery().eq(Ok3wQudao::getCompany, channelVo.getCompany())
                .eq(Ok3wQudao::getAuthorizeid, channelVo.getAuthorizeid())
                .eq(channelVo.getTel() != null, Ok3wQudao::getTel, channelVo.getTel())
                .eq(channelVo.getMobile() != null, Ok3wQudao::getMobile, channelVo.getMobile())
                .eq(channelVo.getQq() != null, Ok3wQudao::getQq, channelVo.getQq())
                .eq(channelVo.getWangwang() != null, Ok3wQudao::getWangwang, channelVo.getWangwang())
                .list();
        if (CollectionUtils.isEmpty(channelList)) {
            return null;
        }
        return channelList.get(0);
    }

    /**
     * 根据渠道的基本信息查询是否有已存在的渠道
     * 并且还要校验是否存在相同类型
     *
     * @param channelVo
     * @return
     */
    @Override
    public Integer getByBasicInfoChw(ChannelVo channelVo) {
        List<Ok3wQudao> channelList = this.lambdaQuery()
                .eq(Ok3wQudao::getAuthorizeid, channelVo.getAuthorizeid())
                .eq(channelVo.getCompany() != null, Ok3wQudao::getCompany, channelVo.getCompany())
                .eq(Ok3wQudao::getSourceType, 1)
                .list();
        if (CollectionUtils.isEmpty(channelList)) {
            return null;
        } else {
            Ok3wQudao ok3wQudao = channelList.get(0);
            return ok3wQudao.getId();
        }

    }


    private static String object2String(Object value) {
        if (value == null) {
            return "";
        }
        if (value instanceof Integer) {
            Integer valueInt = (Integer) value;
            return valueInt.toString();
        }
        if (value instanceof BigDecimal) {
            BigDecimal valueDecimal = (BigDecimal) value;
            return valueDecimal.toString();
        }
        if (value instanceof String) {
            return (String) value;
        }
        return "";
    }

    private static Boolean isFinance(OaUserBO oaUser) {
        List<String> rank = Optional.ofNullable(oaUser.getRank()).orElseGet(Collections::emptyList);
        return (rank.contains(ChannelRankConstant.PURCHASING_ACCOUNTING) || oaUser.getAreaKind1() != 1);
    }

    /**
     * 拼接渠道修改的日志
     *
     * @param oldValue
     * @param newValue
     * @param title
     * @return
     */
    private static String getUpdateLog(Object oldValue, Object newValue, String title) {
        String oldStr = object2String(oldValue);
        String newStr = object2String(newValue);
//        if (StringUtils.isEmpty(newStr)) {
//            return "";
//        }
        if (oldStr.equals(newStr)) {
            return "";
        }
        return title + "由【" + oldStr + "】修改为【" + newStr + "】，";
    }
    /**
     * 拼接渠道修改的日志
     *
     * @param oldValue
     * @param newValue
     * @param title
     * @return
     */
    private static String getUpdateLog2(String oldValue, String newValue, String title) {
        String oldStr = object2String(oldValue);
        String newStr = object2String(newValue);
        //科目新增记录
        if(StringUtils.isEmpty(oldStr) && StringUtils.isNotEmpty(newStr)){
            return "新增"+title+"【"+newStr+"】";
        }
        if (oldStr.equals(newStr)) {
            return "";
        }
        //科目修改记录
        return title + "由【" + oldStr + "】修改为【" + newStr + "】";
    }

    /**
     * 赋中文值
     *
     * @param it
     * @param child
     */
    private void setChannelKindState(ChannelListStateDTO it, ChannelKindStateDTO child) {
        OaUserBO currentStaffId = requestComponent.getCurrentStaffId();
        Integer xTenant = currentStaffId.getXTenant();
        String kindName;
        if (xTenant == 0) {
            kindName = Optional.ofNullable(EnumUtil.getMessageByCode(ChannelKindEnum.class, child.getKind()))
                    .orElse("未知类型");
        } else {
            kindName = Optional.ofNullable(EnumUtil.getMessageByCode(ChannelKindSaasEnum.class, child.getKind()))
                    .orElse("未知类型");
        }
        if (StringUtils.isEmpty(it.getKindsName())) {
            it.setKindsName(kindName);
        } else {
            it.setKindsName(it.getKindsName() + "," + kindName);
        }
        String stateName = Optional.ofNullable(EnumUtil.getMessageByCode(ChannelCooperationStatusEnum.class, child.getState())).orElse("未知状态");
        if (StringUtils.isEmpty(it.getStatesName())) {
            it.setStatesName(stateName);
        } else {
            it.setStatesName(it.getStatesName() + "," + stateName);
        }
    }

}
