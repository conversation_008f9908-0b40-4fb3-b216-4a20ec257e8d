/*
 *    Copyright © 2006 - 2020 九机网 All Rights Reserved
 *
 */

package com.jiuji.oa.nc.channel.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.nc.channel.entity.ChannelKindLink;
import com.jiuji.oa.nc.channel.entity.Ok3wQudao;
import com.jiuji.oa.nc.channel.vo.dto.BeAccountConfigDto;
import com.jiuji.oa.nc.channel.vo.dto.PickingKingChannel;
import com.jiuji.oa.nc.channel.vo.dto.PickingKingChannelState;
import com.jiuji.oa.nc.channel.vo.res.ChannelPageVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 渠道类型关系表Mapper
 *
 * <AUTHOR>
 * @date 2021-03-03 11:12:30
 */
@Mapper
public interface ChannelKindLinkMapper extends BaseMapper<ChannelKindLink> {


    List<ChannelPageVo> getLinkNull();

    List<PickingKingChannel> getPickingKingChannel();

    List<PickingKingChannelState> getPickingKingChannelState();

    /**
     * 根据业务类型查询渠道
     * @param kind
     * @param channelState
     * @return
     */
    List<Ok3wQudao> selectChannelByKindAndState(@Param("kind") Integer kind, @Param("channelState") Integer channelState);

    List<Integer> getDesensitizeChannel();

    @DS("oanewWrite")
    List<BeAccountConfigDto> selectCompanyType();

    /**
     * 获取合作公司名称
     * @param idList
     * @return
     */
    List<String> selectCompanyTypeByIdList(@Param("idList")List<Integer> idList);

    List<ChannelKindLink> selectChannelByKindAndDesensitizeFlag(@Param("kind") Integer kind,
                                                                @Param("desensitizeFlag") Integer desensitizeFlag);

    /**
     * 根据条件查询渠道类型关系
     * @param params 查询参数
     * @return 渠道类型关系列表
     */
    List<ChannelKindLink> selectChannelKindLinkByCondition(@Param("params") com.jiuji.oa.nc.channel.vo.req.ChannelKindLinkQueryParam params);
}
