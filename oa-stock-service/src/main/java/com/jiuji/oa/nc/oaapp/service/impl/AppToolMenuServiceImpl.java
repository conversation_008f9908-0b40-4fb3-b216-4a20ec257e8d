package com.jiuji.oa.nc.oaapp.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ch999.common.util.utils.Exceptions;
import com.jiuji.oa.apollo.WuliuApolloConfig;
import com.jiuji.oa.nc.common.bo.OaUserBO;
import com.jiuji.oa.nc.common.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.nc.common.constant.RedisKeys;
import com.jiuji.oa.nc.common.enums.XtenantEnum;
import com.jiuji.oa.nc.dict.enums.ConfigEnum;
import com.jiuji.oa.nc.dict.service.ISysConfigService;
import com.jiuji.oa.nc.oaapp.enums.AppToolPendingEnum;
import com.jiuji.oa.nc.oaapp.mapper.AppToolMenuMapper;
import com.jiuji.oa.nc.oaapp.po.AppToolMenu;
import com.jiuji.oa.nc.oaapp.po.AppToolUserCommon;
import com.jiuji.oa.nc.oaapp.service.AppToolMenuService;
import com.jiuji.oa.nc.oaapp.service.AppToolUserCommonService;
import com.jiuji.oa.nc.oaapp.vo.req.AppMenuReqVO;
import com.jiuji.oa.nc.oaapp.vo.res.*;
import com.jiuji.oa.nc.stock.service.IAppToolPendingService;
import com.jiuji.oa.nc.user.po.Areainfo;
import com.jiuji.oa.nc.user.service.IAreaInfoService;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.oa.stock.common.util.SysUtils;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.enums.EnumUtil;
import io.lettuce.core.RedisException;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <p>
 * app工具菜单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-19
 */
@Service
@RequiredArgsConstructor
@Slf4j
@DS("oa_nc")
public class AppToolMenuServiceImpl extends ServiceImpl<AppToolMenuMapper, AppToolMenu> implements AppToolMenuService {
    @Resource
    private WuliuApolloConfig wuliuApolloConfig;

    private static final String CGRK = "cgrk";
    private static final String CACHE_KEY = "OA:JAVA:NC:OAAPP:ALLUSEMENU";
    private static final String LOCK_KEY = "OA:JAVA:NC:OAAPP:ALLUSEMENU:LOCK";
    private static final Pattern DIGIT_PATTERN = Pattern.compile("^[\\d]*$");
    private static final String DEFAULT_MOA_URL = "https://moa.9ji.com";
    @Lazy
    @Resource(name = "stringRedisTemplate")
    private StringRedisTemplate redisTemplate;
    @Lazy
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private AppToolUserCommonService appToolUserCommonService;
    @Resource
    private AreaInfoClient areaInfoClient;
    @Resource
    private SysConfigClient sysConfigClient;
    @Resource
    private ISysConfigService sysConfigService;
    @Resource
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;
    @Resource
    private IAppToolPendingService appToolPendingService;

    @Override
    public List<AppToolMenu> getAllUseMenu() {
        if (Boolean.TRUE.equals(redisTemplate.hasKey(CACHE_KEY))) {
            return JSONObject.parseArray(redisTemplate.opsForValue().get(CACHE_KEY), AppToolMenu.class);
        }
        RLock fairLock = redissonClient.getFairLock(LOCK_KEY);
        List<AppToolMenu> results = this.lambdaQuery().eq(AppToolMenu::getIsDel, 0).orderByAsc(AppToolMenu::getOrderRank).list();
        try {
            boolean flag = fairLock.tryLock(3, 10, TimeUnit.SECONDS);
            if (flag) {
                redisTemplate.opsForValue().set(CACHE_KEY, JSON.toJSONString(results), 3600L, TimeUnit.SECONDS);
            }
        } catch (Exception e) {
            log.error("获取有效菜单信息异常:" + e.getMessage());
        } finally {
            try {
                //解锁
                if (fairLock.isHeldByCurrentThread()) {
                    fairLock.unlock();
                }
            } catch (RedisException e) {
                log.error("getAllUseMenu->fairLock.unlock error", e);
            }
        }
        return results;
    }


    /**
     * 获取redis的key
     *
     * @param oaUserBO
     * @return
     */
    public String getRedisKey(OaUserBO oaUserBO) {
        String platformName = oaUserBO.getPlatformName();
        Integer version = oaUserBO.getVersion();
        Integer userId = oaUserBO.getUserId();
        Integer areaId = oaUserBO.getAreaId();
        if (StringUtil.isNotEmpty(platformName) && !Objects.isNull(version)) {
            switch (platformName) {
                case "Android":
                    return RedisKeys.APP_TOOL_PLATFORM_MENU_ANDROID + platformName + version + userId + areaId;
                case "iOS":
                    return RedisKeys.APP_TOOL_PLATFORM_MENU_IOS + platformName + version + userId + areaId;
                case "pda":
                    return RedisKeys.APP_TOOL_PLATFORM_MENU_PDA + platformName + version + userId + areaId;
                default:
                    return null;
            }
        } else {
            return null;
        }
    }


    @Override
    public R<Boolean> cleanAppToolMenu() {
        try {
            Set<String> redisKeys = scan(RedisKeys.APP_TOOL_PLATFORM_MENU);
            if (CollectionUtils.isEmpty(redisKeys)) {
                log.info("未找到需要清理的APP工具菜单缓存");
                return R.success("缓存清空成功！！");
            }

            log.info("开始清理APP工具菜单缓存，待删除key数量: {}", redisKeys.size());
            // 使用批量删除提升性能，避免逐个删除的网络开销
            Long deletedCount = redisTemplate.delete(redisKeys);
            log.info("清理APP工具菜单缓存完成，删除key数量: {}", deletedCount);

            return R.success("缓存清空成功！！");
        } catch (Exception e) {
            log.error("清理APP工具菜单缓存异常", e);
            return R.error("缓存清空失败：" + e.getMessage());
        }
    }

    public Set<String> scan(String matchKey) {
        return redisTemplate.execute((RedisCallback<Set<String>>) connection -> {
            Set<String> keysTmp = new HashSet<>();
            Cursor<byte[]> cursor = connection
                    .scan(new ScanOptions.ScanOptionsBuilder().match(matchKey + "*").count(5000).build());
            while (cursor.hasNext()) {
                keysTmp.add(new String(cursor.next()));
            }
            return keysTmp;
        });
    }


    @Override
    public R<AppToolVO> getAppToolMenu(OaUserBO oaUserBO, AppMenuReqVO req) {
        String redisKey = getRedisKey(oaUserBO);
        Integer areaId = oaUserBO.getAreaId();
        ValueOperations<String, String> opsForValue = redisTemplate.opsForValue();
        String appToolVOString = opsForValue.get(redisKey);
        Boolean jiuJi = sysConfigService.isJiuJi();
        if (StringUtils.isNotEmpty(appToolVOString)) {
            AppToolVO appToolVO = JSON.parseObject(appToolVOString, AppToolVO.class);
            if (jiuJi) {
                try {
                    if (wuliuApolloConfig.getPendFlag() && !Objects.equals(1, req.getPendType())) {
                        searchPendingCountValue(areaId, appToolVO);
                    }
                    addLabel(appToolVO,req);
                } catch (Exception e) {
                    log.error("查询待办异常{}", Exceptions.getStackTraceAsString(e));
                }
            }
            return R.success("查询成功", appToolVO);
        }
        R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(areaId);
        Integer areaType = null;
        if (ResultCode.SUCCESS == areaInfoR.getCode() && areaInfoR.getData() != null) {
            AreaInfo areaInfo = areaInfoR.getData();
            areaType = areaInfo.getAreaType();
        }
        List<AppToolMenu> menus = getAppToolMenuByRank(oaUserBO, areaType);
        if (CollectionUtils.isEmpty(menus)) {
            return R.error("获取菜单信息失败!");
        }
        Map<Integer, List<AppToolMenu>> parentIdMap = menus.stream().collect(Collectors.groupingBy(AppToolMenu::getParentId));
        Map<Integer, AppToolMenu> menuMap = new HashMap<>();
        menus.forEach(m -> menuMap.put(m.getId(), m));

        AppToolVO appToolVO = new AppToolVO();
        List<AppToolMenuVO> list = new ArrayList<>();
        List<AppToolMenuVO> commonList = new ArrayList<>();
        if (parentIdMap.containsKey(0)) {
            List<AppToolMenu> mainMenus = parentIdMap.get(0);
            for (AppToolMenu menu : mainMenus) {
                list.add(fitAppToolMenu(menu, parentIdMap, oaUserBO));
            }
        }

        //过滤没有子菜单的菜单
        list = list.stream().filter(v -> {
            if (CollectionUtils.isEmpty(v.getChild())) {
                return false;
            }
            AppToolMenuVO childVo = v.getChild().get(0);
            if (CollectionUtils.isEmpty(childVo.getChild())) {
                return false;
            }
            return true;
        }).collect(Collectors.toList());

        AppToolUserCommon userTool = appToolUserCommonService.lambdaQuery().eq(AppToolUserCommon::getUserId, oaUserBO.getUserId())
                .one();
        if (userTool == null || StringUtils.isBlank(userTool.getMenuIds())) {
            // 获取默认的常用功能
            userTool = appToolUserCommonService.lambdaQuery().eq(AppToolUserCommon::getUserId, 0).one();
        }
        if (userTool != null && StringUtils.isNotBlank(userTool.getMenuIds())) {
            for (String menuId : userTool.getMenuIds().split(",")) {
                if (DIGIT_PATTERN.matcher(menuId).matches() && menuMap.containsKey(Integer.parseInt(menuId))) {
                    AppToolMenu menu = menuMap.get(Integer.parseInt(menuId));
                    AppToolMenuVO menuVO = toAppToolMenuVO(menu, oaUserBO);
                    commonList.add(menuVO);
                }
                if (commonList.size() > 6) {
                    break;
                }
            }
        }
        appToolVO.setList(list);
        appToolVO.setCommonList(commonList);
        if (jiuJi && wuliuApolloConfig.getPendFlag() && !Objects.equals(1, req.getPendType())) {
            searchPendingCountValue(areaId, appToolVO);
        }
        addLabel(appToolVO,req);
        String stringAppToolVO = JSON.toJSONString(appToolVO);
        opsForValue.set(redisKey, stringAppToolVO, 1, TimeUnit.HOURS);
        return R.success("查询成功", appToolVO);
    }

    private void addLabel(AppToolVO appToolVO,AppMenuReqVO req) {
        String xtenant = sysConfigService.getValueByCode(ConfigEnum.XTENANT);
        if ("0".equals(xtenant)) {
            List<AppToolMenuVO> appToolMenuVOList = appToolVO.getList();
            List<Integer> list = Arrays.asList(AppToolPendingEnum.AppToolMenu_3.getCode(), AppToolPendingEnum.AppToolMenu_4.getCode(), AppToolPendingEnum.AppToolMenu_5.getCode());
            List<AppToolMenuVO> appToolMenuVOx = appToolMenuVOList.stream()
                    .filter((AppToolMenuVO x) -> list.contains(x.getId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(appToolMenuVOx)) {
                for (AppToolMenuVO appToolMenuVO : appToolMenuVOx) {
                    InventoryManagement(appToolMenuVO,req);
                    if ("发货管理".equals(appToolMenuVO.getName())) {
                        List<AppToolLabelEnumVO> labelList = new ArrayList<>();
                        AppToolLabelEnumVO temp1 = new AppToolLabelEnumVO();
                        temp1.setLabel("发货时效要求");
                        temp1.setValue("");
                        temp1.setColor("");
                        AppToolLabelEnumVO temp2 = new AppToolLabelEnumVO();
                        temp2.setLabel("风险订单-定时");
                        temp2.setValue("需审核后30分钟内完成发货");
                        temp2.setColor("#F15643");
                        AppToolLabelEnumVO temp3 = new AppToolLabelEnumVO();
                        temp3.setLabel("加急调货-跑腿");
                        temp3.setValue("需审核后10分钟内完成发货");
                        temp3.setColor("#F15643");
                        AppToolLabelEnumVO temp4 = new AppToolLabelEnumVO();
                        temp4.setLabel("派送单");
                        temp4.setValue("需审核后10分钟内完成发货");
                        temp4.setColor("#F15643");
                        AppToolLabelEnumVO temp5 = new AppToolLabelEnumVO();
                        temp5.setLabel("订");
                        temp5.setValue("需每趟物流车/每天快递到店前发出");
                        temp5.setColor("#F15643");
                        AppToolLabelEnumVO temp6 = new AppToolLabelEnumVO();
                        temp6.setLabel("无标记");
                        temp6.setValue("需当天最后一趟前发出");
                        temp6.setColor("");
                        labelList.add(temp1);
                        labelList.add(temp2);
                        labelList.add(temp3);
                        labelList.add(temp4);
                        labelList.add(temp5);
                        labelList.add(temp6);
                        appToolMenuVO.setLabelList(labelList);
                    }
                    if ("收货管理".equals(appToolMenuVO.getName())) {
                        List<AppToolLabelEnumVO> labelList = new ArrayList<>();
                        AppToolLabelEnumVO temp1 = new AppToolLabelEnumVO();
                        temp1.setLabel("收货时效要求");
                        temp1.setValue("");
                        temp1.setColor("");
                        AppToolLabelEnumVO temp5 = new AppToolLabelEnumVO();
                        temp5.setLabel("订");
                        temp5.setValue("需到店后优先收货, 30分钟内完成收货");
                        temp5.setColor("#F15643");
                        AppToolLabelEnumVO temp6 = new AppToolLabelEnumVO();
                        temp6.setLabel("无标记");
                        temp6.setValue("需到店2小时完成收货");
                        temp6.setColor("");
                        labelList.add(temp1);
                        labelList.add(temp5);
                        labelList.add(temp6);
                        appToolMenuVO.setLabelList(labelList);
                    }
                }
            }
        }
    }

    /**
     * 盘点管理加入
     * @param appToolMenuVO
     */
    private void InventoryManagement(AppToolMenuVO appToolMenuVO,AppMenuReqVO req) {
        if(AppToolPendingEnum.AppToolMenu_5.getMessage().equals(appToolMenuVO.getName()) && NumberConstant.ONE.equals(req.getQueryUpgrade())){
            List<AppToolLabelEnumVO> labelList = new ArrayList<>();
            AppToolLabelEnumVO temp0 = new AppToolLabelEnumVO();
            temp0.setLabel("盘点前事项提示");
            temp0.setValue("");
            temp0.setColor("");
            AppToolLabelEnumVO temp1 = new AppToolLabelEnumVO();
            temp1.setLabel("大件盘点");
            temp1.setValue("\n1、请检查大件调拨是否已完成收发\n" +
                    "2、请将客户已取走商品出库\n" +
                    "3、请注意陈列样机也需要完成盘点");
            temp1.setColor("#F15643");
            AppToolLabelEnumVO temp2 = new AppToolLabelEnumVO();
            temp2.setLabel("配件/维修配件盘点");
            temp2.setValue("\n1、请检查配件调拨是否已完成收发\n" +
                    "2、请注意售前/售后小件单是否已完成清理\n" +
                    "3、请将未审核的报损单审核出库\n" +
                    "4、请注意陈列样机也需要完成盘点\n" +
                    "5、订单预留框内客户删单的商品放回原位\n" +
                    "6、请将客户已取走商品出库");
            temp2.setColor("#F15643");
            AppToolLabelEnumVO temp3 = new AppToolLabelEnumVO();
            temp3.setLabel("良品/回收盘点");
            temp3.setValue("\n1、请检查良品/回收调拨是否已完成收发\n" +
                    "2、请注意回收单是否已完成入库");
            temp3.setColor("#F15643");
            labelList.add(temp0);
            labelList.add(temp1);
            labelList.add(temp2);
            labelList.add(temp3);
            appToolMenuVO.setLabelList(labelList);
            appToolMenuVO.setLabelIcon("https://img2.ch999img.com/newstatic/54459/0f812c5ad80a2bcf.png");

        }
    }


    private void searchPendingCountValue(Integer areaId, AppToolVO appToolVO) {
        ConcurrentHashMap<Integer, CompletableFuture<Void>> pendingJoinMap = new ConcurrentHashMap<>();
        ConcurrentHashMap<Integer, Integer> pendingCountMap = new ConcurrentHashMap<>();
        appToolPendingService.mkcToareaListAllCount(areaId, pendingCountMap);
        appToolPendingService.pjdiaoboListCount(areaId, pendingCountMap);
        appToolPendingService.mkcLpToareaListCount(areaId, pendingCountMap);
        appToolPendingService.getSmallProOldGoodsWaitingForSelectCount(areaId, pendingCountMap);
        appToolPendingService.getYpPjDiaoboListCount(areaId, pendingCountMap);
        appToolPendingService.mkcToAreaNoteListV2Count(areaId, pendingCountMap);
        appToolPendingService.kcTransferReceiveListCount(areaId, pendingCountMap);
        appToolPendingService.mkcLpToareaListsCount(areaId, pendingCountMap);
        appToolPendingService.oldPartsSendList(areaId, pendingCountMap);
        appToolPendingService.oldPartsReceiveList(areaId, pendingCountMap);

        List<AppToolMenuVO> appToolMenuVOList = appToolVO.getList();
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        List<String> rank = oaUserBO.getRank();
        AppToolMenuVO appToolMenuVO2 = appToolMenuVOList.stream()
                .filter((AppToolMenuVO x) -> AppToolPendingEnum.AppToolMenu_2.getCode().equals(x.getId())).findFirst().orElse(null);
        if (Objects.nonNull(appToolMenuVO2) && CollectionUtils.isNotEmpty(appToolMenuVO2.getChild())) {
            List<AppToolMenuVO> appToolMenuVOChild = appToolMenuVO2.getChild();
            AppToolMenuVO appToolMenuVO = appToolMenuVOChild.get(0);
            List<AppToolMenuVO> child = appToolMenuVO.getChild();
            List<AppToolMenuVO> newChild = new ArrayList<>();
            for (AppToolMenuVO x : child) {
                if (!"采购入库".equals(x.getName())
                        || !SysUtils.isJiuJiProd()
                        || "采购入库".equals(x.getName()) && rank.contains(CGRK)) {
                    newChild.add(x);
                }
            }
            appToolMenuVO.setChild(newChild);
        }

        AppToolMenuVO appToolMenuVO3 = appToolMenuVOList.stream()
                .filter((AppToolMenuVO x) -> AppToolPendingEnum.AppToolMenu_3.getCode().equals(x.getId())).findFirst().orElse(null);
        if (Objects.nonNull(appToolMenuVO3) && CollectionUtils.isNotEmpty(appToolMenuVO3.getChild())) {
            List<AppToolMenuVO> appToolMenuVOChild = appToolMenuVO3.getChild();
            AppToolMenuVO appToolMenuVO = appToolMenuVOChild.get(0);
            searchPendingCount(pendingJoinMap, appToolMenuVO.getChild(), pendingCountMap);
        }

        AppToolMenuVO appToolMenuVO4 = appToolMenuVOList.stream()
                .filter((AppToolMenuVO x) -> AppToolPendingEnum.AppToolMenu_4.getCode().equals(x.getId())).findFirst().orElse(null);
        if (Objects.nonNull(appToolMenuVO4) && CollectionUtils.isNotEmpty(appToolMenuVO4.getChild())) {
            List<AppToolMenuVO> appToolMenuVOChild = appToolMenuVO4.getChild();
            AppToolMenuVO appToolMenuVO = appToolMenuVOChild.get(0);
            searchPendingCount(pendingJoinMap, appToolMenuVO.getChild(), pendingCountMap);
        }
    }

    private void searchPendingCountValue(Integer menuId, List<AppToolMenuVO> appToolMenuChildList) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        Integer areaId = oaUserBO.getAreaId();
        ConcurrentHashMap<Integer, CompletableFuture<Void>> pendingJoinMap = new ConcurrentHashMap<>();
        ConcurrentHashMap<Integer, Integer> pendingCountMap = new ConcurrentHashMap<>();
        if (AppToolPendingEnum.AppToolMenu_3.getCode().equals(menuId)) {
            appToolPendingService.mkcToareaListAllCount(areaId, pendingCountMap);
            appToolPendingService.pjdiaoboListCount(areaId, pendingCountMap);
            appToolPendingService.mkcLpToareaListCount(areaId, pendingCountMap);
            appToolPendingService.getSmallProOldGoodsWaitingForSelectCount(areaId, pendingCountMap);
            appToolPendingService.createFixedAssetReqList(pendingCountMap);
            appToolPendingService.oldPartsSendList(areaId, pendingCountMap);
            //appToolPendingService.getYpPjDiaoboListCount(areaId, pendingCountMap);
        } else if (AppToolPendingEnum.AppToolMenu_4.getCode().equals(menuId)) {
            appToolPendingService.mkcToAreaNoteListV2Count(areaId, pendingCountMap);
            appToolPendingService.kcTransferReceiveListCount(areaId, pendingCountMap);
            appToolPendingService.mkcLpToareaListsCount(areaId, pendingCountMap);
            appToolPendingService.oldPartsReceiveList(areaId, pendingCountMap);
        }
        List<String> rank = oaUserBO.getRank();
        appToolMenuChildList = appToolMenuChildList.stream()
                .filter(x -> !"采购入库".equals(x.getName())
                        || !SysUtils.isJiuJiProd()
                        || "采购入库".equals(x.getName()) && rank.contains(CGRK)).collect(Collectors.toList());
        searchPendingCount(pendingJoinMap, appToolMenuChildList, pendingCountMap);
    }

    private void searchPendingCount(ConcurrentHashMap<Integer, CompletableFuture<Void>> pendingJoinMap, List<AppToolMenuVO> appToolMenuChildList, ConcurrentHashMap<Integer, Integer> pendingMap) {
        for (AppToolMenuVO x : appToolMenuChildList) {
            if (AppToolPendingEnum.AppToolMenu_854.getCode().equals(x.getId()) || AppToolPendingEnum.AppToolMenu_854.getMessage().equals(x.getName())) {
                x.setSign(pendingMap.get(AppToolPendingEnum.AppToolMenu_854.getCode()));
            }
            AppToolPendingEnum appToolPendingEnum = EnumUtil.getEnumByCode(AppToolPendingEnum.class, x.getId());
            if (Objects.isNull(appToolPendingEnum)) {
                continue;
            }
            switch (appToolPendingEnum) {
                case AppToolMenu_12:
//                    CompletableFuture<Void> mkcToareaListAllCountFuture = pendingJoinMap.get(AppToolPendingEnum.AppToolMenu_12.getCode());
//                    mkcToareaListAllCountFuture.join();
                    x.setSign(pendingMap.get(AppToolPendingEnum.AppToolMenu_12.getCode()));
                    break;
                case AppToolMenu_13:
//                    CompletableFuture<Void> pjdiaoboListCountFuture = pendingJoinMap.get(AppToolPendingEnum.AppToolMenu_13.getCode());
//                    pjdiaoboListCountFuture.join();
                    x.setSign(pendingMap.get(AppToolPendingEnum.AppToolMenu_13.getCode()));
                    break;
                case AppToolMenu_14:
//                    CompletableFuture<Void> mkcLpToareaListCountFuture = pendingJoinMap.get(AppToolPendingEnum.AppToolMenu_14.getCode());
//                    mkcLpToareaListCountFuture.join();
                    x.setSign(pendingMap.get(AppToolPendingEnum.AppToolMenu_14.getCode()));
                    break;
                case AppToolMenu_834:
//                    CompletableFuture<Void> getSmallProOldGoodsWaitingForSelectCountFuture = pendingJoinMap.get(AppToolPendingEnum.AppToolMenu_834.getCode());
//                    getSmallProOldGoodsWaitingForSelectCountFuture.join();
                    x.setSign(pendingMap.get(AppToolPendingEnum.AppToolMenu_834.getCode()));
                    break;
                case AppToolMenu_841:
//                    CompletableFuture<Void> getYpPjDiaoboListCountFuture = pendingJoinMap.get(AppToolPendingEnum.AppToolMenu_841.getCode());
//                    getYpPjDiaoboListCountFuture.join();
                    x.setSign(pendingMap.get(AppToolPendingEnum.AppToolMenu_841.getCode()));
                    break;
                case AppToolMenu_19:
//                    CompletableFuture<Void> mkcToAreaNoteListV2CountFuture = pendingJoinMap.get(AppToolPendingEnum.AppToolMenu_19.getCode());
//                    mkcToAreaNoteListV2CountFuture.join();
                    //手机收货 改成手机、良品、回收收货菜单合并
                    int pendingCount = pendingMap.getOrDefault(AppToolPendingEnum.AppToolMenu_19.getCode(),0) + pendingMap.getOrDefault(AppToolPendingEnum.AppToolMenu_21.getCode(),0);
                    x.setSign(pendingCount);
                    break;
                case AppToolMenu_20:
//                    CompletableFuture<Void> kcTransferReceiveListCountFuture = pendingJoinMap.get(AppToolPendingEnum.AppToolMenu_20.getCode());
//                    kcTransferReceiveListCountFuture.join();
                    x.setSign(pendingMap.get(AppToolPendingEnum.AppToolMenu_20.getCode()));
                    break;
                case AppToolMenu_21:
//                    CompletableFuture<Void> mkcLpToareaListsCountFuture = pendingJoinMap.get(AppToolPendingEnum.AppToolMenu_21.getCode());
//                    mkcLpToareaListsCountFuture.join();
                    x.setSign(pendingMap.get(AppToolPendingEnum.AppToolMenu_21.getCode()));
                    break;
                case AppToolMenu_870:
                    x.setSign(pendingMap.get(AppToolPendingEnum.AppToolMenu_870.getCode()));
                    break;
                case AppToolMenu_871:
                    x.setSign(pendingMap.get(AppToolPendingEnum.AppToolMenu_871.getCode()));
                    break;
                default:
                    break;
            }
        }
    }


    private AppToolMenuVO fitAppToolMenu(AppToolMenu menu, Map<Integer, List<AppToolMenu>> parentIdMap, OaUserBO oaUserBO) {
        AppToolMenuVO menuVO = toAppToolMenuVO(menu, oaUserBO);
        if (parentIdMap.containsKey(menu.getId())) {
            List<AppToolMenu> menuList = parentIdMap.get(menu.getId());
            List<AppToolMenuVO> childs = new ArrayList<>();
            for (AppToolMenu toolMenu : menuList) {
                AppToolMenuVO childMenuVO = fitAppToolMenu(toolMenu, parentIdMap, oaUserBO);
                childs.add(childMenuVO);
            }
            menuVO.setChild(childs);
        }
        return menuVO;
    }

    private AppToolMenuVO toAppToolMenuVO(AppToolMenu po, OaUserBO oaUserBO) {
        AppToolMenuVO vo = new AppToolMenuVO();
        BeanUtils.copyProperties(po, vo);
        R<String> result = sysConfigClient.getValueByCodeAndXtenant(14, oaUserBO.getXTenant());
        if (StringUtils.isEmpty(result.getData())) {
            Optional.ofNullable(vo.getIcon()).ifPresent(item -> {
                if (item.startsWith("http")) {
                    vo.setIcon(item);
                } else {
                    vo.setIcon(DEFAULT_MOA_URL + item);
                }
            });
            vo.setLink(DEFAULT_MOA_URL + vo.getLink());
            return vo;
        }
        if (StringUtils.isNotEmpty(vo.getIcon())) {
            if (vo.getIcon().startsWith("http")) {
                vo.setIcon(vo.getIcon());
            } else {
                vo.setIcon(result.getData() + vo.getIcon());
            }
        }
        if (StringUtils.isNotEmpty(vo.getLink())) {
            vo.setLink(result.getData() + vo.getLink());
        }
        return vo;
    }


    /**
     * 查询常用菜单和tab接口及时效要求的接口
     *
     * @return
     */
    @Override
    public R<AppMenuTabVO> getAppMenuTab(Integer queryUpgrade) {
        OaUserBO oaUserBo = abstractCurrentRequestComponent.getCurrentStaffId();
        String redisKey = getRedisKey(oaUserBo) + "_tab";
        String tabMapRedisKey = redisKey + "_map";
        Integer areaId = oaUserBo.getAreaId();
        String appTabMenuString = redisTemplate.opsForValue().get(redisKey);
        if (StringUtils.isNotBlank(appTabMenuString)) {
            AppMenuTabVO appTabMenu = JSON.parseObject(appTabMenuString, AppMenuTabVO.class);
            return R.success("查询成功", appTabMenu);
        }
        Integer areaType = Optional.ofNullable(SpringUtil.getBean(IAreaInfoService.class).getAreaInfoByAreaId2(areaId))
                .filter(v -> Objects.nonNull(v.getAreaType()))
                .map(Areainfo::getAreaType).orElse(0);
        List<AppToolMenu> menus = getAppToolMenuByRank(oaUserBo, areaType);
        if (CollectionUtils.isEmpty(menus)) {
            return R.error("获取菜单信息失败!");
        }
        Map<Integer, List<AppToolMenu>> parentIdMap = menus.stream().collect(Collectors.groupingBy(AppToolMenu::getParentId));
        Map<Integer, AppToolMenu> menuMap = menus.stream().collect(Collectors.toMap(AppToolMenu::getId, Function.identity(), (v1, v2) -> v2));
        AppToolVO appToolVO = new AppToolVO();
        List<AppToolMenuVO> commonList = new ArrayList<>();
        List<AppToolMenuVO> list = Optional.ofNullable(parentIdMap.get(0)).orElse(Collections.emptyList()).stream()
                .map(menu -> toAppToolMenuVO(menu, oaUserBo)).collect(Collectors.toList());

        Map<String, String> appMenuTabMap = new HashMap<>();
        for (AppToolMenuVO appToolMenu : list) {
            List<AppToolMenuVO> appToolMenuList = parentIdMap.getOrDefault(appToolMenu.getId(), Collections.emptyList()).stream()
                    .flatMap(v -> parentIdMap.getOrDefault(v.getId(), Collections.emptyList()).stream()).map(menu -> toAppToolMenuVO(menu, oaUserBo))
                    .collect(Collectors.toList());
            AppChildMenuVO appChildMenu = new AppChildMenuVO();
            appChildMenu.setChild(appToolMenuList);
            appMenuTabMap.put(Convert.toStr(appToolMenu.getId()), JSON.toJSONString(appChildMenu));
        }

        AppToolUserCommon userTool = appToolUserCommonService.lambdaQuery().eq(AppToolUserCommon::getUserId, oaUserBo.getUserId()).one();
        if (userTool == null || StringUtils.isBlank(userTool.getMenuIds())) {
            // 获取默认的常用功能
            userTool = appToolUserCommonService.lambdaQuery().eq(AppToolUserCommon::getUserId, 0).one();
        }
        if (userTool != null && StringUtils.isNotBlank(userTool.getMenuIds())) {
            for (String menuId : userTool.getMenuIds().split(",")) {
                if (DIGIT_PATTERN.matcher(menuId).matches() && menuMap.containsKey(Integer.parseInt(menuId))) {
                    AppToolMenu menu = menuMap.get(Integer.parseInt(menuId));
                    AppToolMenuVO menuVO = toAppToolMenuVO(menu, oaUserBo);
                    commonList.add(menuVO);
                }
                if (commonList.size() > 6) {
                    break;
                }
            }
        }
        appToolVO.setList(list);
        appToolVO.setCommonList(commonList);
        AppMenuReqVO appMenuReqVO = new AppMenuReqVO();
        appMenuReqVO.setQueryUpgrade(queryUpgrade);
        addLabel(appToolVO,appMenuReqVO);
        AppMenuTabVO appMenuTab = new AppMenuTabVO();
        appMenuTab.setCommonList(appToolVO.getCommonList());
        appMenuTab.setTabList(appToolVO.getList());
        String stringAppToolVO = JSON.toJSONString(appMenuTab);
        redisTemplate.opsForValue().set(redisKey, stringAppToolVO, 1, TimeUnit.DAYS);
        redisTemplate.opsForHash().putAll(tabMapRedisKey, appMenuTabMap);
        redisTemplate.expire(tabMapRedisKey, NumberConstant.SEVEN, TimeUnit.DAYS);
        return R.success("查询成功", appMenuTab);
    }

    /**
     * 根据tabId，获取菜单列表接口
     *
     * @param id
     * @return
     */
    @Override
    public R<AppChildMenuVO> getAppMenuById(Integer id) {
        OaUserBO oaUserBo = abstractCurrentRequestComponent.getCurrentStaffId();
        String tabMapRedisKey = getRedisKey(oaUserBo) + "_tab_map";
        String appChildString = Convert.toStr(redisTemplate.opsForHash().get(tabMapRedisKey, Convert.toStr(id, "0")), "");
        if (StringUtils.isNotBlank(appChildString)) {
            AppChildMenuVO appChildMenu = JSON.parseObject(appChildString, AppChildMenuVO.class);
            if (XtenantEnum.isJiujiXtenant() && wuliuApolloConfig.getPendFlag()) {
                List<AppToolMenuVO> appToolMenuChildList = appChildMenu.getChild();
                searchPendingCountValue(id, appToolMenuChildList);
            }
            return R.success("查询成功", appChildMenu);
        }
        Integer areaType = Optional.ofNullable(SpringUtil.getBean(IAreaInfoService.class).getAreaInfoByAreaId2(oaUserBo.getAreaId()))
                .filter(v -> Objects.nonNull(v.getAreaType()))
                .map(Areainfo::getAreaType).orElse(0);
        List<AppToolMenu> menus = getAppToolMenuByRank(oaUserBo, areaType);
        Map<Integer, List<AppToolMenu>> parentIdMap = menus.stream().collect(Collectors.groupingBy(AppToolMenu::getParentId));
        Optional<AppToolMenuVO> appToolMenuOptional = Optional.ofNullable(parentIdMap.get(0)).orElse(Collections.emptyList()).stream()
                .filter(v -> Objects.equals(v.getId(), id))
                .map(menu -> toAppToolMenuVO(menu, oaUserBo))
                .findFirst();
        AppChildMenuVO appChildMenu = new AppChildMenuVO();
        appChildMenu.setChild(Collections.emptyList());
        if (appToolMenuOptional.isPresent()) {
            AppToolMenuVO appToolMenu = appToolMenuOptional.get();
            List<AppToolMenuVO> appToolMenuList = parentIdMap.getOrDefault(appToolMenu.getId(), Collections.emptyList()).stream()
                    .flatMap(v -> parentIdMap.getOrDefault(v.getId(), Collections.emptyList()).stream()).map(menu -> toAppToolMenuVO(menu, oaUserBo))
                    .collect(Collectors.toList());
            if (XtenantEnum.isJiujiXtenant() && wuliuApolloConfig.getPendFlag()) {
                searchPendingCountValue(appToolMenu.getId(), appToolMenuList);
            }
            appChildMenu.setChild(appToolMenuList);
            redisTemplate.opsForHash().put(tabMapRedisKey, Convert.toStr(appToolMenu.getId()), JSON.toJSONString(appChildMenu));
            redisTemplate.expire(tabMapRedisKey, NumberConstant.ONE, TimeUnit.DAYS);
        }
        return R.success(appChildMenu);
    }


    @Override
    public List<AppToolMenu> getAppToolMenuByRank(OaUserBO oaUser, Integer areaType) {
        List<AppToolMenu> menus = baseMapper.getAppToolMenu(oaUser, areaType);
        if (XtenantEnum.isJiujiXtenant() && CollectionUtils.isNotEmpty(menus)) {
            menus = menus.stream().filter(v -> StringUtils.isBlank(v.getRanks()) ||
                    StrUtil.splitTrim(v.getRanks(), StrPool.COMMA).stream().anyMatch(r -> oaUser.getRank().contains(r)))
                    .collect(Collectors.toList());
        }
        return menus;
    }

}
