package com.jiuji.oa.stock.displayproductinfo.service.impl;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.jiuji.oa.nc.common.exception.CustomizeException;
import com.jiuji.oa.nc.product.entity.ProductInfoEntity;
import com.jiuji.oa.nc.product.service.IProductInfoService;
import com.jiuji.oa.nc.stock.entity.ProductMkc;
import com.jiuji.oa.nc.stock.service.IProductMkcService;
import com.jiuji.oa.stock.common.util.Builder;
import com.jiuji.oa.stock.displayproductinfo.dto.DisplayProductAddReq;
import com.jiuji.oa.stock.displayproductinfo.entity.DisplayProductInfo;
import com.jiuji.oa.stock.displayproductinfo.entity.MkcDelLogEntity;
import com.jiuji.oa.stock.displayproductinfo.enums.DpStateEnum;
import com.jiuji.oa.stock.displayproductinfo.service.IDisplayProductInfoBusService;
import com.jiuji.oa.stock.displayproductinfo.service.IDisplayProductInfoService;
import com.jiuji.oa.stock.displayproductinfo.service.IMkcDelLogService;
import com.jiuji.oa.stock.displayproductinfo.service.IOaDisplayLogService;
import com.jiuji.oa.stock.displayproductinfo.vo.req.DisplayProductAddVO;
import com.jiuji.oa.stock.displayproductinfo.vo.req.DisplayProductReqVO;
import com.jiuji.oa.stock.displayproductinfo.vo.req.DisplayProductStatusReqVo;
import com.jiuji.oa.stock.displayproductinfo.vo.res.DisplayProductReqDTO;
import com.jiuji.oa.stock.displayproductinfo.vo.res.DisplayProductStatusResVo;
import com.jiuji.oa.stock.productkc.service.IProductKcService;
import com.jiuji.oa.stock.purchaseList.enums.KcCheckEnum;
import com.jiuji.tc.utils.enums.EnumUtil;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

/**
 * <p>
 *
 * @description: DisplayProductInfoBusServiceImpl
 * </p>
 * @author: David
 * @create: 2021-03-15 17:11
 */
@Service
@RequiredArgsConstructor
public class DisplayProductInfoBusServiceImpl implements IDisplayProductInfoBusService {

    private final IDisplayProductInfoService displayProductInfoService;
    private final IProductMkcService productMkcService;
    private final IProductInfoService productInfoService;
    private final IOaDisplayLogService oaDisplayLogService;
    private final IProductKcService productKcService;
    private final IMkcDelLogService mkcDelLogService;

    @Override
    public DisplayProductInfo getById(Long id) {
        return displayProductInfoService.getById(id);
    }

    @Override
    public List<DisplayProductReqDTO> getDisplayProductInfo(DisplayProductReqVO reqVO) {
        if (reqVO == null) {
            throw new CustomizeException("订单号或陈列id不能都为空");
        }
        if (StringUtils.isEmpty(reqVO.getSubId()) && StringUtils.isEmpty(reqVO.getId())) {
            throw new CustomizeException("订单号或陈列id不能为空");
        }
        if (StringUtils.isNotEmpty(reqVO.getSubId())) {
            return displayProductInfoService.getDisplayProductInfo(reqVO.getSubId());
        } else {
            DisplayProductInfo displayProductInfo = displayProductInfoService.getById(Long.parseLong(reqVO.getId()));
            if (displayProductInfo == null) {
                return Collections.emptyList();
            }
            DisplayProductReqDTO dto = new DisplayProductReqDTO();
            dto.setId(displayProductInfo.getId());
            dto.setState(displayProductInfo.getState());
            return Collections.singletonList(dto);
        }
    }

    @Override
    public DisplayProductStatusResVo verifyStockStatus(DisplayProductStatusReqVo reqVO) {
        DisplayProductStatusResVo resVo = new DisplayProductStatusResVo();
        //大件通过mkcid传参，返回对应mkcid的库存状态，库存状态可上架
        if (Boolean.TRUE.equals(reqVO.getMobile())) {
            ProductMkc productMkc = productMkcService.getById(reqVO.getSearchId());
            //查不到或者productMkc的BasketId不为空 则不可以创建陈列
            if (null != productMkc && null == productMkc.getBasketId()) {
                KcCheckEnum kcCheckEnum = EnumUtil.getEnumByCode(KcCheckEnum.class, productMkc.getKcCheck());
                resVo.setStatus(KcCheckEnum.K_C == kcCheckEnum);
                resVo.setStatusName(kcCheckEnum.getMessage());
                return resVo;
            }
            resVo.setStatus(false);
            resVo.setStatusName("MkcId不存在或者已有订单锁定该库存,不能上架");
        } else {
            //小件通过陈列编号传参，返回对应陈列编号的审核状态，已审核状态可上架
            DisplayProductInfo displayProductInfo = displayProductInfoService.getById(reqVO.getSearchId());
            if (null != displayProductInfo) {
                DpStateEnum dpKcCheckEnum = EnumUtil.getEnumByCode(DpStateEnum.class, displayProductInfo.getState());
                if (dpKcCheckEnum == null) {
                    resVo.setStatus(false);
                    resVo.setStatusName("陈列审核状态异常");
                } else {
                    resVo.setStatus(DpStateEnum.AUDITED == dpKcCheckEnum);
                    resVo.setStatusName(dpKcCheckEnum.getMessage());
                }
                return resVo;
            }
            resVo.setStatus(false);
            resVo.setStatusName("陈列Id不存在");
        }
        return resVo;
    }

    @Override
    @DSTransactional
    public void add(@Valid DisplayProductAddVO reqVO) {
        //查询商品的售价、成本
        ProductInfoEntity productInfoEntity = productInfoService.getByPpid(reqVO.getPpid());
        Assert.notNull(productInfoEntity, "没有skuId:" + reqVO.getPpid() + "的商品");
        Assert.isTrue(!Boolean.TRUE.equals(productInfoEntity.getMobile()), "不能增加大件陈列商品");

        DisplayProductInfo displayProductInfo = Builder.of(DisplayProductInfo::new)
                .with(DisplayProductInfo::setMaintainId, 0)
                .with(DisplayProductInfo::setDisplay, false)
                .with(DisplayProductInfo::setPpid, reqVO.getPpid())
                .with(DisplayProductInfo::setCount, reqVO.getCount())
                .with(DisplayProductInfo::setFlag, true)
                .with(DisplayProductInfo::setInuser, reqVO.getInUserName())
                .with(DisplayProductInfo::setDtime, LocalDateTime.now())
                .with(DisplayProductInfo::setAreaId, reqVO.getAreaId())
                .with(DisplayProductInfo::setMobile, false)
                .with(DisplayProductInfo::setSellPrice, productInfoEntity.getMemberPrice())
                .with(DisplayProductInfo::setFlaw, true)
                .with(DisplayProductInfo::setState, 1)
                .with(DisplayProductInfo::setKcCheck, 3)
                .with(DisplayProductInfo::setFlawReason, "售后退换")
                .with(DisplayProductInfo::setCurAreaId, reqVO.getAreaId())
                .build();
        displayProductInfoService.addDisplayProductInfo(displayProductInfo);
        productKcService.lockProductKc(reqVO.getAreaId(), reqVO.getPpid(), reqVO.getCount());
        oaDisplayLogService.addLog(displayProductInfo.getId().intValue(), "小件接件转瑕疵添加陈列", reqVO.getInUserName());
    }



    @Override
    @DSTransactional
    public DisplayProductAddReq addV2(@Valid DisplayProductAddVO reqVO) {
        //查询商品的售价、成本
        ProductInfoEntity productInfoEntity = productInfoService.getByPpid(reqVO.getPpid());
        Assert.notNull(productInfoEntity, "没有skuId:" + reqVO.getPpid() + "的商品");
        Assert.isTrue(!Boolean.TRUE.equals(productInfoEntity.getMobile()), "不能增加大件陈列商品");
        DisplayProductAddReq displayProductAddReq = new DisplayProductAddReq();
        DisplayProductInfo displayProductInfo = Builder.of(DisplayProductInfo::new)
                .with(DisplayProductInfo::setMaintainId, 0)
                .with(DisplayProductInfo::setDisplay, false)
                .with(DisplayProductInfo::setPpid, reqVO.getPpid())
                .with(DisplayProductInfo::setCount, reqVO.getCount())
                .with(DisplayProductInfo::setFlag, true)
                .with(DisplayProductInfo::setInuser, reqVO.getInUserName())
                .with(DisplayProductInfo::setDtime, LocalDateTime.now())
                .with(DisplayProductInfo::setAreaId, reqVO.getAreaId())
                .with(DisplayProductInfo::setMobile, false)
                .with(DisplayProductInfo::setSellPrice, productInfoEntity.getMemberPrice())
                .with(DisplayProductInfo::setFlaw, true)
                .with(DisplayProductInfo::setState, 1)
                .with(DisplayProductInfo::setKcCheck, 3)
                .with(DisplayProductInfo::setFlawReason, "售后退换")
                .with(DisplayProductInfo::setCurAreaId, reqVO.getAreaId())
                .build();
        displayProductInfoService.addDisplayProductInfo(displayProductInfo);
        productKcService.lockProductKc(reqVO.getAreaId(), reqVO.getPpid(), reqVO.getCount());
        oaDisplayLogService.addLog(displayProductInfo.getId().intValue(), "小件接件转瑕疵添加陈列", reqVO.getInUserName());
        displayProductAddReq.setId(displayProductInfo.getId());
        return displayProductAddReq;
    }

    /**
     * 获取陈列商品的审核日期
     *
     * @param reqVO
     * @return
     */
    @Override
    public String getCheckTimeV1(DisplayProductStatusReqVo reqVO) {
        String checkTime = "";
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        //大件
        if (Boolean.TRUE.equals(reqVO.getMobile())) {
            MkcDelLogEntity mkcDelLogEntity = mkcDelLogService.getCheckedMkcDelLog(reqVO.getSearchId());
            if (Objects.nonNull(mkcDelLogEntity) && Objects.nonNull(mkcDelLogEntity.getCheck1dtime())) {
                checkTime = mkcDelLogEntity.getCheck1dtime().format(dtf);
            }
            //小件
        } else {
            DisplayProductInfo displayProductInfo = displayProductInfoService.getCheckedDisplayProductInfo(reqVO.getSearchId());
            if (Objects.nonNull(displayProductInfo) && Objects.nonNull(displayProductInfo.getDtime())) {
                checkTime = displayProductInfo.getDtime().format(dtf);
            }
        }
        Assert.isTrue(StringUtils.isNotEmpty(checkTime), "没有审核日期");
        return checkTime;
    }
}
