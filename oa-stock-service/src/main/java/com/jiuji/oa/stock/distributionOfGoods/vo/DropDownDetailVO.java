package com.jiuji.oa.stock.distributionOfGoods.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class DropDownDetailVO {

    /**
     * 转出地区
     */
    private List<Integer> transferOutAreaIdList;

    /**
     * 查询ppid
     */
    private List<Integer> ppidList;

    /**
     * 库存状态
     */
    private List<Integer> kcCheckList;

     /**
     * 在途情况-10
     */
    private Integer type;


}
