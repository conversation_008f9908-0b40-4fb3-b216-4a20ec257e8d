package com.jiuji.oa.stock.authorizationtransfer.controller;

import com.jiuji.oa.stock.authorizationtransfer.entity.CrossAuthTransferSubLog;
import com.jiuji.oa.stock.authorizationtransfer.service.CrossAuthTransferSubLogService;
import com.jiuji.oa.stock.authorizationtransfer.vo.SaveLogVO;
import com.jiuji.tc.common.vo.R;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("api/transferSubLog")
public class CrossAuthTransferSubLogController {

    @Resource
    private CrossAuthTransferSubLogService logService;

    /**
     * 跨授权调拨 日志查询
     * @return
     */
    @GetMapping("/selectLogById/v1")
    public R<List<CrossAuthTransferSubLog>> selectLogById(@RequestParam(value = "id") Integer id) {
        List<CrossAuthTransferSubLog> list = logService.findLogById(id);
        return R.success(list);
    }


    /**
     * 跨授权调拨 日志查询
     * @return
     */
    @PostMapping("/saveLog/v1")
    public R<String> saveLog(@RequestBody SaveLogVO saveLogVO) {
        logService.saveLog(saveLogVO);
        return R.success("日志保存成功");
    }
}
