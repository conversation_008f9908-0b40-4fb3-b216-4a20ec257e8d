package com.jiuji.oa.stock.authorizationtransfer.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName(value = "save_money_timing")
public class SaveMoneyTiming {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    @TableField("userid")
    private Integer userid;
    @TableField("timingday")
    private Integer timingday;
    @TableField("timingdate")
    private LocalDateTime timingdate;
}
