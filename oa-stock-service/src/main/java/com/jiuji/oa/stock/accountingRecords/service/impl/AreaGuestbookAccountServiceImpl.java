package com.jiuji.oa.stock.accountingRecords.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.nc.common.constant.RedisKeys;
import com.jiuji.oa.nc.common.exception.CustomizeException;
import com.jiuji.oa.nc.user.po.Areainfo;
import com.jiuji.oa.nc.user.service.IAreaInfoService;
import com.jiuji.oa.stock.accountingRecords.entity.AreaGuestbookAccount;
import com.jiuji.oa.stock.accountingRecords.enums.StepInfoEnum;
import com.jiuji.oa.stock.accountingRecords.mapper.AreaGuestbookAccountMapper;
import com.jiuji.oa.stock.accountingRecords.service.AreaGuestbookAccountService;
import com.jiuji.oa.stock.accountingRecords.vo.req.AffiliatedAreaIdInfo;
import com.jiuji.oa.stock.accountingRecords.vo.req.UpdateAffiliatedAreaIdReq;
import com.jiuji.oa.stock.accountingRecords.vo.res.StepInfoRes;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.constants.NumberConstant;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@DS("oanewWrite")
public class AreaGuestbookAccountServiceImpl extends ServiceImpl<AreaGuestbookAccountMapper, AreaGuestbookAccount> implements AreaGuestbookAccountService {

    @Resource
    private IAreaInfoService areaInfoService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateShowAreaId() {
        List<AreaGuestbookAccount> areaGuestbookAccounts = this.lambdaQuery().isNull(AreaGuestbookAccount::getShowAreaId).list();
        //如果showAreaId没有空的数据那就结束
        if(CollectionUtil.isEmpty(areaGuestbookAccounts)){
            return;
        }
        List<String> areaCodeList = areaGuestbookAccounts.stream().map(AreaGuestbookAccount::getShowAreaCode).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
        List<Areainfo> areainfos = CommonUtils.bigDataInQuery(areaCodeList, ids -> areaInfoService.lambdaQuery().in(Areainfo::getArea, ids).list());
        if(CollectionUtil.isEmpty(areainfos)){
            return;
        }
        Map<String, Integer> areaMap = areainfos.stream().collect(Collectors.toMap(Areainfo::getArea, Areainfo::getId, (n1, n2) -> n2));
        areaGuestbookAccounts.forEach(item->{
            String showAreaCode = item.getShowAreaCode();
            Integer showAreaId = areaMap.get(showAreaCode);
            if(ObjectUtil.isNotNull(showAreaId)){
                boolean updated = this.lambdaUpdate().eq(AreaGuestbookAccount::getId, item.getId())
                        .set(AreaGuestbookAccount::getShowAreaId, showAreaId)
                        .update();
                if(!updated){
                    throw new CustomizeException(showAreaCode+"更新失败");
                }
            }
        });
    }

    @Override
    public List<StepInfoRes> selectByAreaId(Integer saleAreaId, Integer purchaseAreaId) {
        List<StepInfoRes> stepInfoResList = new ArrayList<>();
        //销售门店和采购门店是同一个门店情况
        if(saleAreaId.equals(purchaseAreaId)){
            List<AreaGuestbookAccount> areaGuestbookAccountList = this.baseMapper.selectSameAreaData(saleAreaId, purchaseAreaId);
            if(CollectionUtil.isNotEmpty(areaGuestbookAccountList)){
                AreaGuestbookAccount areaGuestbookAccount = areaGuestbookAccountList.stream()
                        .filter(item -> purchaseAreaId.equals(item.getShowAreaId()))
                        .findFirst()
                        .orElse(areaGuestbookAccountList.get(NumberConstant.ZERO));
                stepInfoResList.add(createStepInfoRes(areaGuestbookAccount,NumberConstant.ONE, StepInfoEnum.SALE,saleAreaId));
                return stepInfoResList;
            }
        }
        //销售门店和库存门店在同一条数据的 使用库存门店GB账号 【扫码-销售】
        List<AreaGuestbookAccount> areaGuestbookAccount = this.baseMapper.selectSameData(saleAreaId, purchaseAreaId);
        if(CollectionUtil.isNotEmpty(areaGuestbookAccount)){
            AreaGuestbookAccount areaGuestbookAccountPurchaseArea = areaGuestbookAccount.stream()
                    .filter(item -> purchaseAreaId.equals(item.getShowAreaId()))
                    .findFirst()
                    .orElse(areaGuestbookAccount.get(NumberConstant.ZERO));
            stepInfoResList.add(createStepInfoRes(areaGuestbookAccountPurchaseArea,NumberConstant.ONE, StepInfoEnum.SALE,saleAreaId));
            return stepInfoResList;
        }
        List<AreaGuestbookAccount> mappingAreaGuestbookAccount = this.baseMapper.selectMappingAreaGuestbookAccount(saleAreaId);
        List<AreaGuestbookAccount> saleAreaGuestbookList = this.lambdaQuery().eq(AreaGuestbookAccount::getShowAreaId, saleAreaId).list();
        List<AreaGuestbookAccount> purchaseAreaGuestbookList = this.lambdaQuery().eq(AreaGuestbookAccount::getShowAreaId, purchaseAreaId).list();
        //采购门店和销售门店都是主GB门店
        if(CollectionUtil.isNotEmpty(saleAreaGuestbookList) &&  CollectionUtil.isNotEmpty(purchaseAreaGuestbookList)){
            //使用采购门店GB账号 【扫码-出库】
            AreaGuestbookAccount account = purchaseAreaGuestbookList.get(NumberConstant.ZERO);
            StepInfoRes stepInfoRes = createStepInfoRes(account, NumberConstant.ONE, StepInfoEnum.OUT_BOUND,saleAreaId);
            stepInfoResList.add(stepInfoRes);
            //使用销售门店GB账号 【扫码-入库】 【扫码-销售】
            AreaGuestbookAccount saleAccount = saleAreaGuestbookList.get(NumberConstant.ZERO);
            StepInfoRes enterInventoryStepInfo = createStepInfoRes(saleAccount, NumberConstant.TWO, StepInfoEnum.ENTER_INVENTORY,saleAreaId);
            stepInfoResList.add(enterInventoryStepInfo);
            return stepInfoResList;
        }
        //采购门店为主GB门店 销售门店为映射门店
        if(CollectionUtil.isNotEmpty(mappingAreaGuestbookAccount) && CollectionUtil.isNotEmpty(purchaseAreaGuestbookList)){
            //使用采购门店GB账号 【扫码-出库】
            AreaGuestbookAccount account = purchaseAreaGuestbookList.get(NumberConstant.ZERO);
            StepInfoRes stepInfoRes = createStepInfoRes(account, NumberConstant.ONE, StepInfoEnum.OUT_BOUND,saleAreaId);
            stepInfoResList.add(stepInfoRes);
            //使用销售门店对应主GB账号 【扫码-入库】 【扫码-销售】
            Integer showAreaId = mappingAreaGuestbookAccount.stream().filter(item -> ObjectUtil.isNotNull(item.getShowAreaId())).findFirst().orElse(new AreaGuestbookAccount()).getShowAreaId();
            List<AreaGuestbookAccount> mainMappingAccount = this.lambdaQuery().eq(AreaGuestbookAccount::getShowAreaId, showAreaId).list();
            //映射主账号不为空
            if(CollectionUtil.isNotEmpty(mainMappingAccount)){
                AreaGuestbookAccount mappingAccount = mainMappingAccount.get(NumberConstant.ZERO);
                StepInfoRes enterInventoryStepInfo = createStepInfoRes(mappingAccount, NumberConstant.TWO, StepInfoEnum.ENTER_INVENTORY,saleAreaId);
                stepInfoResList.add(enterInventoryStepInfo);
            }
            return stepInfoResList;
        }

        return stepInfoResList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateAffiliatedAreaId(UpdateAffiliatedAreaIdReq updateAffiliatedAreaIdReq) {
        List<AffiliatedAreaIdInfo> affiliatedAreaIdInfoList = updateAffiliatedAreaIdReq.getAffiliatedAreaIdInfoList();
        if(CollectionUtil.isEmpty(affiliatedAreaIdInfoList)){
            return;
        }
        Set<String> areaSet = new HashSet<>();
        Set<String> MainAreaList = new HashSet<>();
        affiliatedAreaIdInfoList.forEach(item->{
            areaSet.add(item.getMainAreaId().toUpperCase());
            MainAreaList.add(item.getMainAreaId().toUpperCase());
            Arrays.stream(item.getAreaIdStrs().split(",")).filter(StrUtil::isNotBlank).forEach(areaId->areaSet.add(areaId.toUpperCase()));
        });
        List<AreaGuestbookAccount> areaGuestbookAccounts = this.lambdaQuery().in(AreaGuestbookAccount::getShowAreaCode, MainAreaList).list();
        MainAreaList.removeAll(areaGuestbookAccounts.stream().map(AreaGuestbookAccount::getShowAreaCode).map(String::toUpperCase).collect(Collectors.toSet()));
        log.warn("新增门店："+MainAreaList.toString());
        //获取除门店对应的id
        Map< String,Integer> areaMap = CommonUtils.bigDataInQuery(areaSet, ids -> areaInfoService.lambdaQuery().in(Areainfo::getArea, ids)
                .select(Areainfo::getArea, Areainfo::getId).list()).stream()
                .map(item->item.setArea(item.getArea().toUpperCase()))
                .collect(Collectors.toMap(Areainfo::getArea, Areainfo::getId, (n1, n2) -> n2));
        HashMap<Integer, String> map = new HashMap<>();
        affiliatedAreaIdInfoList.forEach(item->{
            Integer mainAreaId = Optional.ofNullable(areaMap.get(item.getMainAreaId().toUpperCase())).orElseThrow(()->new CustomizeException(item.getMainAreaId()+"门店不存在"));
            String area = Arrays.stream(item.getAreaIdStrs().split(","))
                    .map(areaItem -> {
                        Integer i = Optional.ofNullable(areaMap.get(areaItem.toUpperCase())).orElseThrow(() -> new CustomizeException(areaItem + "门店不存在"));
                        return Convert.toStr(i);
                    })
                    .collect(Collectors.joining(","));
            map.put(mainAreaId, area);
        });
        map.forEach((k,v)->{
            boolean updated = this.lambdaUpdate().eq(AreaGuestbookAccount::getShowAreaId, k)
                    .set(AreaGuestbookAccount::getAffiliatedAreaId, v)
                    .update();
            if(!updated){
                throw new CustomizeException("更新失败门店id"+k);
            }
        });
    }

    /**
     * 创建步骤信息
     * @param areaGuestbookAccount
     * @return
     */
    private StepInfoRes createStepInfoRes(AreaGuestbookAccount areaGuestbookAccount, Integer rank, StepInfoEnum stepInfoEnum,Integer saleAreaId){
        StepInfoRes stepInfoRes = new StepInfoRes();
        String account = areaGuestbookAccount.getGuestbookAccount();
        String password = areaGuestbookAccount.getGuestbookPassword();
        String accountPassword = String.format("账号：%s,密码%s",account, password);
        stepInfoRes.setAccount(account)
                .setAccountPassword(accountPassword)
                .setPassword(password)
                .setRank(rank);
        String infoMessage = "";
        String infoMessageArea = "";
        String showArea = areaInfoService.getAreaById(areaGuestbookAccount.getShowAreaId());
        String saleArea = areaInfoService.getAreaById(saleAreaId);
        if(StepInfoEnum.OUT_BOUND.getCode().equals(stepInfoEnum.getCode())){
            infoMessage = String.format("使用GuestBook账号进行%s", stepInfoEnum.getMessage());
            infoMessageArea = showArea;
        }else if(StepInfoEnum.ENTER_INVENTORY.getCode().equals(stepInfoEnum.getCode())){
            infoMessage = String.format("使用GuestBook账号进行%s，入库完成后操作%s", stepInfoEnum.getMessage(),StepInfoEnum.SALE.getMessage());
            infoMessageArea = saleArea;
        }else if(StepInfoEnum.SALE.getCode().equals(stepInfoEnum.getCode())){
            infoMessage = String.format("使用GuestBook账号进行%s", stepInfoEnum.getMessage());
            infoMessageArea = saleArea;
        }
        stepInfoRes.setInfoMessage(infoMessage);
        stepInfoRes.setInfoMessageArea(infoMessageArea);
        return stepInfoRes;
    }
}
