package com.jiuji.oa.stock.authorizationtransfer.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.nc.common.bo.OaUserBO;
import com.jiuji.oa.nc.common.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.nc.common.exception.CustomizeException;
import com.jiuji.oa.nc.product.service.IProductInfoService;
import com.jiuji.oa.nc.product.vo.req.ProductInfoVo;
import com.jiuji.oa.nc.stock.entity.ProductMkc;
import com.jiuji.oa.nc.stock.service.impl.ProductMkcServiceImpl;
import com.jiuji.oa.nc.user.po.Areainfo;
import com.jiuji.oa.nc.user.po.Authorize;
import com.jiuji.oa.nc.user.service.AuthorizeService;
import com.jiuji.oa.nc.user.service.IAreaInfoService;
import com.jiuji.oa.stock.authorizationtransfer.entity.CrossAuthTransferDetail;
import com.jiuji.oa.stock.authorizationtransfer.entity.CrossAuthTransferSub;
import com.jiuji.oa.stock.authorizationtransfer.enums.TransferStateEnums;
import com.jiuji.oa.stock.authorizationtransfer.mapper.CrossAuthTransferDetailMapper;
import com.jiuji.oa.stock.authorizationtransfer.service.CrossAuthTransferDetailService;
import com.jiuji.oa.stock.authorizationtransfer.service.CrossAuthTransferSubLogService;
import com.jiuji.oa.stock.authorizationtransfer.service.CrossAuthTransferSubService;
import com.jiuji.oa.stock.authorizationtransfer.vo.*;
import com.jiuji.oa.stock.productkc.entity.ProductKc;
import com.jiuji.oa.stock.productkc.service.IProductKcService;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.constants.NumberConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@DS("oanewWrite")
public class CrossAuthTransferDetailServiceImpl extends ServiceImpl<CrossAuthTransferDetailMapper, CrossAuthTransferDetail> implements CrossAuthTransferDetailService {

    @Resource
    private IProductKcService kcService;
    
    @Resource
    private ProductMkcServiceImpl mkcService;
    @Resource
    private AuthorizeService authorizeService;
    @Resource
    private IAreaInfoService areaInfoService;
    @Resource
    private IProductInfoService productInfoService;
    @Resource
    private CrossAuthTransferSubLogService logService;
    @Resource
    private AbstractCurrentRequestComponent requestComponent;



    /**
     * 修改商品
     * @param req
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSubDetail(UpdateSubDetailVO req) {
        CrossAuthTransferSubService transferSubService = SpringUtil.getBean(CrossAuthTransferSubService.class);
        CrossAuthTransferSub transferSub = Optional.ofNullable(transferSubService.getById(req.getId())).orElseThrow(() -> new CustomizeException("调拨单查询为空"));
        if(!TransferStateEnums.SUBMITTED.getCode().equals(transferSub.getStatus())){
            throw new CustomizeException("只有调拨单状态为"+TransferStateEnums.SUBMITTED.getMessage()+"才能修改商品");
        }
        //查询原来的数据
        List<CrossAuthTransferDetail> list = this.lambdaQuery().eq(CrossAuthTransferDetail::getTransferId, req.getId()).list();
        if(CollectionUtils.isEmpty(list)){
            throw new CustomizeException("调拨单商品为空");
        }
        //前端修改数据
        List<DetailInfoVO> detailInfoVOList = req.getDetailVOList();
        //新增
        List<DetailInfoVO> addList = new ArrayList<>();
        //修改
        List<DetailInfoVO> updateList = new ArrayList<>();
        //删除
        List<CrossAuthTransferDetail> delList = new ArrayList<>();
        //传进来的数据
        List<Integer> collect = detailInfoVOList.stream().map(DetailInfoVO::getDetailId).collect(Collectors.toList());
        detailInfoVOList.forEach(obj->{
            //判断如果没有详情id 那就是需要新增
            if(ObjectUtil.isNull(obj.getDetailId())){
                addList.add(obj);
            }
        });
        //比较两份数据进行逻辑划分
        list.forEach(item->{
            if(CollectionUtils.isEmpty(detailInfoVOList) || !collect.contains(item.getDetailId())){
                delList.add(item);
            }
            detailInfoVOList.forEach(obj->{
                BigDecimal transferPriceObj = Optional.ofNullable(obj.getTransferPrice()).orElse(BigDecimal.ZERO);
                BigDecimal bigDecimalitem = Optional.ofNullable(item.getTransferPrice()).orElse(BigDecimal.ZERO);
                Integer objDetailId = Optional.ofNullable(obj.getDetailId()).orElse(Integer.MAX_VALUE);
                Integer itemDetailId = Optional.ofNullable(item.getDetailId()).orElse(Integer.MIN_VALUE);
                //判断如果是详情id 相等 并且调拨价格不相等那就修改价格
                if(objDetailId.equals(itemDetailId) && transferPriceObj.compareTo(bigDecimalitem) != 0){
                    updateList.add(obj);
                }
                Integer countObj = Optional.ofNullable(obj.getCount()).orElse(Integer.MIN_VALUE);
                Integer countItem = Optional.ofNullable(item.getCount()).orElse(Integer.MAX_VALUE);
                //判断如果是详情id 相等 并且调拨价格不相等那就修改价格
                if(objDetailId.equals(itemDetailId) && !countObj.equals(countItem)){
                    updateList.add(obj);
                }
            });
        });
        Map<Integer, CrossAuthTransferDetail> map = list.stream().collect(Collectors.toMap(CrossAuthTransferDetail::getDetailId, Function.identity(), (n1, n2) -> n2));
        //删除调拨单详情
        delDetailList(delList);
        //修改调拨单详情
        updateDetailList(updateList,map);
        //添加调拨单详情
        addDetailList(addList,req.getId());
        //重新计算调拨总价
        recalculateTransferPrice(req.getId(),transferSub);
    }


    /**
     * 重新计算调拨总价
     * @param transferId
     */
    private void recalculateTransferPrice(Integer transferId,CrossAuthTransferSub transferSubOld){
        CrossAuthTransferSubService transferSubService = SpringUtil.getBean(CrossAuthTransferSubService.class);
        List<CrossAuthTransferDetail> list = this.lambdaQuery().eq(CrossAuthTransferDetail::getTransferId, transferId).list();
        AtomicReference<BigDecimal> transferPriceNew = new AtomicReference<>(BigDecimal.ZERO);
        AtomicReference<BigDecimal> costPriceNew = new AtomicReference<>(BigDecimal.ZERO);
        list.forEach(item->{
            BigDecimal transferPrice = item.getTransferPrice();
            BigDecimal costPrice = item.getCostPrice();
            BigDecimal count = new BigDecimal(Optional.ofNullable(item.getCount()).orElse(NumberConstant.ZERO).toString());
            transferPriceNew.getAndSet(transferPriceNew.get().add(transferPrice.multiply(count)));
            costPriceNew.getAndSet(costPriceNew.get().add(costPrice.multiply(count)));
        });
        boolean update = transferSubService.lambdaUpdate().eq(CrossAuthTransferSub::getId, transferId)
                .eq(CrossAuthTransferSub::getStatus, TransferStateEnums.SUBMITTED.getCode())
                .set(CrossAuthTransferSub::getTotalTransferPrice, transferPriceNew.get())
                .set(CrossAuthTransferSub::getTotalCost, costPriceNew.get())
                .update();
        if(costPriceNew.get().compareTo(transferSubOld.getTotalCost()) != 0){
            String commentCost = String.format("成本总价由：%s 修改为：%s", transferSubOld.getTotalCost(), costPriceNew.get());
            logService.saveLog(new SaveLogVO(commentCost,transferId));
        }
        if(transferPriceNew.get().compareTo(transferSubOld.getTotalTransferPrice()) != 0){
            String commentTransfer = String.format("调拨单总价由：%s 修改为：%s", transferSubOld.getTotalTransferPrice(), transferPriceNew.get());
            logService.saveLog(new SaveLogVO(commentTransfer,transferId));
        }
        if(!update){
            throw new CustomizeException("保存失败");
        }
    }
    /**
     * 删除调拨单详情
     * @param delList
     */
    private void delDetailList(List<CrossAuthTransferDetail> delList){
        if(CollectionUtils.isEmpty(delList)){
            return;
        }
        Integer transferId = delList.get(0).getTransferId();
        StringJoiner joiner = new StringJoiner(",");
        delList.forEach(item->{
            boolean remove = this.removeById(item.getDetailId());
            String comment = String.format("删除商品ppid:%s", item.getPpid());
            if(!remove){
                throw new CustomizeException(comment+"执行失败");
            }
            joiner.add(comment);
        });
        //解锁库存
        CrossAuthTransferSubService transferSubService = SpringUtil.getBean(CrossAuthTransferSubService.class);
        transferSubService.unlockProduct(delList);
        //修改日志保存
        logService.saveLog(new SaveLogVO(joiner.toString(),transferId));
    }


    /**
     * 修改调拨单详情
     * @param updateList
     * @param map
     */
    private void updateDetailList(List<DetailInfoVO> updateList,Map<Integer, CrossAuthTransferDetail> map){
        if(CollectionUtils.isEmpty(updateList)){
            return;
        }List<CrossAuthTransferDetail> lockdetailList = new ArrayList<>();
        Integer transferId = new ArrayList<>(map.values()).get(0).getTransferId();
        StringJoiner joiner = new StringJoiner(",");
        updateList.forEach(item->{
            CrossAuthTransferDetail crossAuthTransferDetail = Optional.ofNullable(map.get(item.getDetailId())).orElse(new CrossAuthTransferDetail());
            BigDecimal transferPriceOld = Optional.ofNullable(crossAuthTransferDetail.getTransferPrice()).orElse(BigDecimal.ZERO);
            BigDecimal transferPriceNew = Optional.ofNullable(item.getTransferPrice()).orElse(BigDecimal.ZERO);
            if(transferPriceNew.compareTo(transferPriceOld) !=0){
                String comment = String.format("商品ppid：%s,调拨单价由：%s 修改为：%s", item.getPpid(), transferPriceOld, transferPriceNew);
                joiner.add(comment);
            }
            Integer countNew = Optional.ofNullable(item.getCount()).orElse(NumberConstant.ZERO);
            Integer countOld = Optional.ofNullable(crossAuthTransferDetail.getCount()).orElse(NumberConstant.ZERO);
            //如果过是数量不一样那就需要进行库存的锁定
            if(!countNew.equals(countOld)){
                if(!Optional.ofNullable(item.getIsmobile()).orElse(Boolean.TRUE)){
                    //小件库存锁定
                    CrossAuthTransferDetail detail = new CrossAuthTransferDetail();
                    detail.setPpid(item.getPpid())
                            .setTransferId(transferId)
                            .setMkcId(item.getMkcId())
                            .setCount(countNew-countOld);
                    lockdetailList.add(detail);
                }
                String comment = String.format("商品ppid：%s,调拨数量由：%s 修改为：%s", item.getPpid(), countOld, countNew);
                joiner.add(comment);
            }
            boolean update = this.lambdaUpdate().eq(CrossAuthTransferDetail::getDetailId, item.getDetailId())
                    .set(CrossAuthTransferDetail::getTransferPrice, transferPriceNew)
                    .set(CrossAuthTransferDetail::getCount,countNew)
                    .update();
            if(!update){
                throw new CustomizeException(joiner.toString()+"修改失败");
            }
        });
        //小件库存锁定
        if(!CollectionUtils.isEmpty(lockdetailList)){
            kcService.lockProductTransferKc(lockdetailList);
        }
        //修改日志保存
        logService.saveLog(new SaveLogVO(joiner.toString(),transferId));
    }

    /**
     * 添加调拨单详情
     * @param addList
     */
    private void addDetailList(List<DetailInfoVO> addList,Integer transferId){
        if(CollectionUtils.isEmpty(addList)){
            return;
        }
        OaUserBO oaUserBO = Optional.ofNullable(requestComponent.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("登录信息失效，请重新登录"));
        List<CrossAuthTransferDetail> detailList = addList.stream().map(item -> {
            CrossAuthTransferDetail crossAuthTransferDetail = new CrossAuthTransferDetail();
            BeanUtils.copyProperties(item,crossAuthTransferDetail);
            crossAuthTransferDetail.setTransferId(transferId);
            crossAuthTransferDetail.setCreateTime(LocalDateTime.now());
            crossAuthTransferDetail.setCreateUser(oaUserBO.getUserName());
            return crossAuthTransferDetail;
        }).collect(Collectors.toList());
        CrossAuthTransferSubService subService = SpringUtil.getBean(CrossAuthTransferSubService.class);
        subService.saveCrossAuthTransferDetailList(detailList);
    }



    
    @Override
    public Map<Integer, List<CrossAuthTransferDetail>> getTransferDetailInfo(List<Integer> transferIdList) {
        Map<Integer, List<CrossAuthTransferDetail>> map = new HashMap<>();
        List<CrossAuthTransferDetail> list =  CommonUtils.bigDataInQuery(transferIdList,ids->this.lambdaQuery().in(CrossAuthTransferDetail::getTransferId, ids).list());
        if(CollectionUtils.isEmpty(list)){
            return map;
        }
        map = list.stream().collect(Collectors.groupingBy(CrossAuthTransferDetail::getTransferId));
        return map;
    }

    @Override
    public SubTotalInfo selectTotalCost(Integer transferId) {
        SubTotalInfo subTotalInfo = new SubTotalInfo();
        List<CrossAuthTransferDetail> list = this.lambdaQuery().eq(CrossAuthTransferDetail::getTransferId, transferId).list();
        if(CollectionUtils.isEmpty(list)){
            return subTotalInfo;
        }
        //划分大小件商品
        List<CrossAuthTransferDetail> detailListSmall=new ArrayList<>();
        List<CrossAuthTransferDetail> detailListBig=new ArrayList<>();
        list.forEach(item->{
            if(item.getIsmobile()){
                detailListBig.add(item);
            } else {
                detailListSmall.add(item);
            }
        });
        AtomicReference<BigDecimal> sum = new AtomicReference<>(new BigDecimal("0.00"));
        AtomicInteger countTotal = new AtomicInteger(NumberConstant.ZERO);

        //小件价格统计
        if(!CollectionUtils.isEmpty(detailListSmall)){
            detailListSmall.forEach(item->{
                Integer mkcId = item.getMkcId();
                Integer count = Optional.ofNullable(item.getCount()).orElse(NumberConstant.ZERO);
                countTotal.getAndSet(countTotal.get()+count);
                ProductKc productKc = Optional.ofNullable(kcService.getById(mkcId)).orElse(new ProductKc());
                sum.getAndSet(sum.get().add(Optional.of(productKc.getInprice().multiply(new BigDecimal(count.toString()))).orElse(BigDecimal.ZERO)));
            });
        }
        //大件价格统计
        if(!CollectionUtils.isEmpty(detailListBig)){
            detailListBig.forEach(item->{
                Integer mkcId = item.getMkcId();
                countTotal.getAndSet(countTotal.get()+1);
                ProductMkc productMkc = Optional.ofNullable(mkcService.getById(mkcId)).orElse(new ProductMkc());
                BigDecimal bigDecimal = new BigDecimal(Optional.ofNullable(productMkc.getStaticPrice()).orElse(0.00).toString());
                sum.getAndSet(sum.get().add(bigDecimal));
            });
        }
        subTotalInfo.setTotalCost(sum.get());
        subTotalInfo.setTotalCount(countTotal.get());
        return subTotalInfo;
    }

    @Override
    public CostDetailVo selectTotalCostDetail(Integer detailId,Integer status) {
        CostDetailVo detailVo = new CostDetailVo();
        CrossAuthTransferDetail detail = this.lambdaQuery().eq(CrossAuthTransferDetail::getDetailId, detailId).one();
        if(ObjectUtil.isNull(detail)){
            return detailVo;
        }
        Integer mkcId = detail.getMkcId();
        Integer count = Optional.ofNullable(detail.getCount()).orElse(NumberConstant.ZERO);
        //判断如果过订单不是 已完成 那就显示实时价格
        if(AuthorizationTransferServiceImpl.LIST_STATE.contains(status)){
            if(detail.getIsmobile()){
                ProductMkc productMkc = Optional.ofNullable(mkcService.getById(mkcId)).orElse(new ProductMkc());
                BigDecimal bigDecimal = new BigDecimal(Optional.ofNullable(productMkc.getStaticPrice()).orElse(0.00).toString());
                detailVo.setTotalCost(bigDecimal);
            } else {
                ProductKc productKc = Optional.ofNullable(kcService.getById(mkcId)).orElse(new ProductKc());
                BigDecimal bigDecimal = Optional.of(productKc.getInprice().multiply(new BigDecimal(count.toString()))).orElse(BigDecimal.ZERO);
                detailVo.setTotalCost(bigDecimal);
            }
        } else {
            detailVo.setTotalCost(detail.getCostPrice().multiply(new BigDecimal(count.toString())));
        }
        BigDecimal totalTransferPrice = Optional.ofNullable(detail.getTransferPrice()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(count.toString()));
        detailVo.setTotalTransferPrice(totalTransferPrice);
        detailVo.setTotalCount(count);
        return detailVo;
    }


    @Override
    public SubInfoVO selectSubInfo(SubInfoReqVO req) {
        //调拨主表信息
        SubInfoVO subInfo = createSubInfo(req);
        //详情信息查询
        List<CrossAuthTransferDetail> list = this.lambdaQuery().eq(CrossAuthTransferDetail::getTransferId, req.getId()).list();
        if(CollectionUtils.isEmpty(list)){
            throw new CustomizeException("详情信息查询为空");
        }
        List<Long> ppidList = list.stream().map(item -> Optional.ofNullable(item.getPpid()).orElse(NumberConstant.ZERO).longValue()).collect(Collectors.toList());
        List<Long> kcIdList = new ArrayList<>();
        List<Integer> mkcIdList = new ArrayList<>();
        list.forEach(item->{
            Boolean aBoolean = Optional.ofNullable(item.getIsmobile()).orElse(Boolean.FALSE);
            if(aBoolean){
                mkcIdList.add(item.getMkcId());
            } else {
                kcIdList.add(item.getMkcId().longValue());
            }
        });
        List<DetailInfoVO> detailInfoVOList = new ArrayList<>();
        Map<Long, ProductKc> kcMap = kcService.selectKcMap(kcIdList);
        Map<Integer, ProductMkc> mkcMap = mkcService.selectKcMap(mkcIdList);
        Map<Long, ProductInfoVo> map = productInfoService.listByppids(ppidList);
        Map<Integer, CrossAuthTransferDetailVO> categoryMap = new HashMap<>();
        List<CrossAuthTransferDetailVO> crossAuthTransferDetailVOS = baseMapper.getCategoryName(req.getId());
        if(CollUtil.isNotEmpty(crossAuthTransferDetailVOS)){
            categoryMap = crossAuthTransferDetailVOS.stream().collect(Collectors.toMap(CrossAuthTransferDetailVO::getPpid, Function.identity(), (a, b) -> b));
        }
        final Map<Integer, CrossAuthTransferDetailVO> categoryFinalMap = categoryMap;
        list.forEach(item->{
            DetailInfoVO detailInfoVO = new DetailInfoVO();
            BeanUtils.copyProperties(item,detailInfoVO);
            //商品信息设置
            Optional.ofNullable(map.get(Optional.ofNullable(item.getPpid()).orElse(NumberConstant.ZERO).longValue())).ifPresent(product->{
                detailInfoVO.setProductColor(product.getProductColor())
                        .setProductName(product.getProductName());
            });
            // 设置分类信息
            CrossAuthTransferDetailVO crossAuthTransferDetailVO = categoryFinalMap.get(item.getPpid());
            if(null != crossAuthTransferDetailVO){
                detailInfoVO.setCategoryName(crossAuthTransferDetailVO.getCategoryName());
            }

            Boolean ismoblie = Optional.ofNullable(detailInfoVO.getIsmobile()).orElse(Boolean.FALSE);
            if(ismoblie){
                //获取大件库存信息
                ProductMkc mkc = mkcMap.getOrDefault(detailInfoVO.getMkcId(), new ProductMkc());
                detailInfoVO.setImei(mkc.getImei());
                detailInfoVO.setKcCount(NumberConstant.ONE);
                detailInfoVO.setMouldFlag(Convert.toInt(mkc.getMouldFlag()));
                if(AuthorizationTransferServiceImpl.LIST_STATE.contains(subInfo.getStatus())){
                    detailInfoVO.setCostPrice(new BigDecimal(Optional.ofNullable(mkc.getStaticPrice()).orElse(0.00).toString()));
                }
            } else {
                //获取小件库存信息
                ProductKc kc = kcMap.getOrDefault(Optional.ofNullable(detailInfoVO.getMkcId()).orElse(NumberConstant.ZERO).longValue(), new ProductKc());
                detailInfoVO.setKcCount(Optional.ofNullable(kc.getLeftcount()).orElse(NumberConstant.ZERO));
                if(AuthorizationTransferServiceImpl.LIST_STATE.contains(subInfo.getStatus())){
                    detailInfoVO.setCostPrice(kc.getInprice());
                }
            }
            BigDecimal costPrice = Optional.ofNullable(detailInfoVO.getCostPrice()).orElse(BigDecimal.ZERO);
            Integer count = Optional.ofNullable(detailInfoVO.getCount()).orElse(NumberConstant.ZERO);
            detailInfoVO.setCostTotalPrice(costPrice.multiply(new BigDecimal(count.toString())));
            detailInfoVOList.add(detailInfoVO);
        });
        subInfo.setDetailVOList(detailInfoVOList);
        return subInfo;
    }

    /**
     * 创建调拨主表信息
     * @param req
     * @return
     */
    private SubInfoVO createSubInfo(SubInfoReqVO req){
        SubInfoVO subInfoVO = new SubInfoVO();
        //订单主要信息查询
        CrossAuthTransferSubService subService = SpringUtil.getBean(CrossAuthTransferSubService.class);
        CrossAuthTransferSub transferSub = Optional.ofNullable(subService.getById(req.getId())).orElseThrow(() -> new CustomizeException("调拨单查询为空"));
        BeanUtils.copyProperties(transferSub,subInfoVO);
        List<Integer> areaIdList = Arrays.asList(transferSub.getToAreaId(), transferSub.getFromAreaId());
        List<Integer> idList = Arrays.asList(transferSub.getFromAuthId(), transferSub.getToAuthId());
        //获取门店信息
        Map<Integer, Areainfo> areaMap = areaInfoService.getAreaMapByIdsNew(areaIdList.stream().distinct().collect(Collectors.toList()));
        //获取授权信息
        Map<Integer, Authorize> authorizeMap = authorizeService.getAuthorizeMap(idList.stream().distinct().collect(Collectors.toList()));
        //调拨单状态转换
        subInfoVO.setStatusValue(TransferStateEnums.getMessageByCode(subInfoVO.getStatus()));
        //门店名称转换
        Areainfo areaFrom = Optional.ofNullable(areaMap.get(subInfoVO.getFromAreaId())).orElse(new Areainfo());
        Areainfo areaTo = Optional.ofNullable(areaMap.get(subInfoVO.getToAreaId())).orElse(new Areainfo());
        subInfoVO.setFromArea(areaFrom.getArea()).setToArea(areaTo.getArea());
        //授权名称转换
        Authorize authorizeFrom = Optional.ofNullable(authorizeMap.get(subInfoVO.getFromAuthId())).orElse(new Authorize());
        Authorize authorizeTo = Optional.ofNullable(authorizeMap.get(subInfoVO.getToAuthId())).orElse(new Authorize());
        subInfoVO.setFromAuth(authorizeFrom.getName()).setToAuth(authorizeTo.getName());
        return subInfoVO;
    }

    /**
     * 批量获取小件商品库存信息
     * @param mkcIds 小件商品ID集合
     * @return 商品ID到库存信息的映射
     */
    private Map<Integer, ProductKc> batchGetProductKcMap(Set<Integer> mkcIds) {
        if (mkcIds.isEmpty()) {
            return new HashMap<>();
        }
        return CommonUtils.bigDataInQuery(mkcIds, ids -> 
            kcService.listByIds(ids)
        ).stream()
            .filter(Objects::nonNull)
            .collect(Collectors.toMap(
                kc -> kc.getId().intValue(), 
                Function.identity(), 
                (v1, v2) -> v2,
                HashMap::new
            ));
    }
    
    /**
     * 批量获取大件商品库存信息
     * @param mkcIds 大件商品ID集合
     * @return 商品ID到库存信息的映射
     */
    private Map<Integer, ProductMkc> batchGetProductMkcMap(Set<Integer> mkcIds) {
        if (mkcIds.isEmpty()) {
            return new HashMap<>();
        }
        return CommonUtils.bigDataInQuery(mkcIds, ids -> 
            mkcService.listByIds(ids)
        ).stream()
            .filter(Objects::nonNull)
            .collect(Collectors.toMap(
                ProductMkc::getId,
                Function.identity(),
                (v1, v2) -> v2,
                HashMap::new
            ));
    }

    @Override
    public Map<Integer, SubTotalInfo> batchSelectTotalCost(List<Integer> transferIdList) {
        if (CollectionUtils.isEmpty(transferIdList)) {
            return new HashMap<>();
        }
        
        Map<Integer, SubTotalInfo> resultMap = new HashMap<>(transferIdList.size());
        
        // 批量查询所有调拨单的明细
        Map<Integer, List<CrossAuthTransferDetail>> transferDetailMap = getTransferDetailInfo(transferIdList);
        
        // 收集所有小件商品和大件商品的mkcId
        Set<Integer> smallProductMkcIds = new HashSet<>();
        Set<Integer> bigProductMkcIds = new HashSet<>();
        
        transferDetailMap.values().stream()
            .filter(details -> !CollectionUtils.isEmpty(details))
            .flatMap(Collection::stream)
            .forEach(detail -> {
                if (Boolean.TRUE.equals(detail.getIsmobile())) {
                    bigProductMkcIds.add(detail.getMkcId());
                } else {
                    smallProductMkcIds.add(detail.getMkcId());
                }
            });
        
        // 批量查询并构建映射
        Map<Integer, ProductKc> kcMap = batchGetProductKcMap(smallProductMkcIds);
        Map<Integer, ProductMkc> mkcMap = batchGetProductMkcMap(bigProductMkcIds);
        
        // 处理每个调拨单的总金额计算
        for (Integer transferId : transferIdList) {
            List<CrossAuthTransferDetail> details = transferDetailMap.getOrDefault(transferId, Collections.emptyList());
            SubTotalInfo subTotalInfo = new SubTotalInfo();
            
            if (!CollectionUtils.isEmpty(details)) {
                //划分大小件商品
                List<CrossAuthTransferDetail> detailListSmall = new ArrayList<>();
                List<CrossAuthTransferDetail> detailListBig = new ArrayList<>();
                details.forEach(item -> {
                    if(item.getIsmobile()) {
                        detailListBig.add(item);
                    } else {
                        detailListSmall.add(item);
                    }
                });
                
                AtomicReference<BigDecimal> sum = new AtomicReference<>(new BigDecimal("0.00"));
                AtomicInteger countTotal = new AtomicInteger(NumberConstant.ZERO);
                
                //小件价格统计
                if(!CollectionUtils.isEmpty(detailListSmall)) {
                    detailListSmall.forEach(item -> {
                        Integer mkcId = item.getMkcId();
                        Integer count = Optional.ofNullable(item.getCount()).orElse(NumberConstant.ZERO);
                        countTotal.getAndSet(countTotal.get() + count);
                        ProductKc productKc = Optional.ofNullable(kcMap.get(mkcId)).orElse(new ProductKc());
                        sum.getAndSet(sum.get().add(Optional.of(productKc.getInprice().multiply(new BigDecimal(count.toString()))).orElse(BigDecimal.ZERO)));
                    });
                }
                
                //大件价格统计
                if(!CollectionUtils.isEmpty(detailListBig)) {
                    detailListBig.forEach(item -> {
                        Integer mkcId = item.getMkcId();
                        countTotal.getAndSet(countTotal.get() + 1);
                        ProductMkc productMkc = Optional.ofNullable(mkcMap.get(mkcId)).orElse(new ProductMkc());
                        BigDecimal bigDecimal = new BigDecimal(Optional.ofNullable(productMkc.getStaticPrice()).orElse(0.00).toString());
                        sum.getAndSet(sum.get().add(bigDecimal));
                    });
                }
                
                subTotalInfo.setTotalCost(sum.get());
                subTotalInfo.setTotalCount(countTotal.get());
            }
            
            resultMap.put(transferId, subTotalInfo);
        }
        
        return resultMap;
    }
    
    @Override
    public Map<Integer, CostDetailVo> batchSelectTotalCostDetail(List<Integer> detailIdList, Map<Integer, Integer> detailStatusMap) {
        if (CollectionUtils.isEmpty(detailIdList)) {
            return new HashMap<>();
        }
        
        Map<Integer, CostDetailVo> resultMap = new HashMap<>(detailIdList.size());
        
        // 批量查询所有明细
        List<CrossAuthTransferDetail> details = CommonUtils.bigDataInQuery(detailIdList,ids  ->this.lambdaQuery()
                .in(CrossAuthTransferDetail::getDetailId, ids)
                .list());

        // 按detailId分组
        Map<Integer, CrossAuthTransferDetail> detailMap = details.stream()
                .collect(Collectors.toMap(CrossAuthTransferDetail::getDetailId, Function.identity(), (v1, v2) -> v1));
        
        // 收集需要查询的mkcId
        Set<Integer> smallProductMkcIds = new HashSet<>();
        Set<Integer> bigProductMkcIds = new HashSet<>();
        
        for (CrossAuthTransferDetail detail : details) {
            Integer status = detailStatusMap.get(detail.getDetailId());
            // 只有在需要实时价格时才收集ID
            if (AuthorizationTransferServiceImpl.LIST_STATE.contains(status)) {
                if (Boolean.TRUE.equals(detail.getIsmobile())) {
                    bigProductMkcIds.add(detail.getMkcId());
                } else {
                    smallProductMkcIds.add(detail.getMkcId());
                }
            }
        }
        
        // 批量查询小件商品库存信息
        Map<Integer, ProductKc> kcMap = batchGetProductKcMap(smallProductMkcIds);
        
        // 批量查询大件商品库存信息
        Map<Integer, ProductMkc> mkcMap = batchGetProductMkcMap(bigProductMkcIds);
        
        // 处理每个明细ID
        for (Integer detailId : detailIdList) {
            CrossAuthTransferDetail detail = detailMap.get(detailId);
            
            if (detail != null) {
                Integer status = detailStatusMap.get(detailId);
                CostDetailVo costDetailVo = new CostDetailVo();
                
                Integer mkcId = detail.getMkcId();
                Integer count = Optional.ofNullable(detail.getCount()).orElse(NumberConstant.ZERO);
                
                // 判断如果订单不是已完成，那就显示实时价格
                if(AuthorizationTransferServiceImpl.LIST_STATE.contains(status)){
                    if(detail.getIsmobile()){
                        ProductMkc productMkc = Optional.ofNullable(mkcMap.get(mkcId)).orElse(new ProductMkc());
                        BigDecimal bigDecimal = new BigDecimal(Optional.ofNullable(productMkc.getStaticPrice()).orElse(0.00).toString());
                        costDetailVo.setTotalCost(bigDecimal);
                    } else {
                        ProductKc productKc = Optional.ofNullable(kcMap.get(mkcId)).orElse(new ProductKc());
                        BigDecimal bigDecimal = Optional.of(productKc.getInprice().multiply(new BigDecimal(count.toString()))).orElse(BigDecimal.ZERO);
                        costDetailVo.setTotalCost(bigDecimal);
                    }
                } else {
                    costDetailVo.setTotalCost(detail.getCostPrice().multiply(new BigDecimal(count.toString())));
                }
                
                BigDecimal transferPrice = Optional.ofNullable(detail.getTransferPrice()).orElse(BigDecimal.ZERO);
                costDetailVo.setTotalCount(count);
                costDetailVo.setTotalTransferPrice(transferPrice.multiply(new BigDecimal(count.toString())));
                
                resultMap.put(detailId, costDetailVo);
            }
        }
        
        return resultMap;
    }
}
