package com.jiuji.oa.stock.authorizationtransfer.vo;

import com.jiuji.oa.stock.authorizationtransfer.enums.SubmitReqEnums;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ProductReq {

    private Integer size;

    private Integer current;

    /**
     * 查询类型
     * @see SubmitReqEnums
     */
    private Integer queryType;

    private String key;

    @NotNull(message = "调拨门店不能为空")
    private Integer transferAreaId;
    /**
     * 商品 分类
     */
    private List<Integer> cidList;

    /**
     * 商品 品牌
     */
    private List<Integer> brandList;

    /**
     * 串号list
     */
    private List<String> imeiList;

    private List<Integer> ppidList;
}
