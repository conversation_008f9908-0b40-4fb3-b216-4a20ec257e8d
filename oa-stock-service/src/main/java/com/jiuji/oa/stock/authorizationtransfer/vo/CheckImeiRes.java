package com.jiuji.oa.stock.authorizationtransfer.vo;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class CheckImeiRes {

    /**
     * 错误串号
     */
    private List<ImeiErrorInfo> errorImeiList;

    /**
     * 错误信息文案
     */
    private String errorMsg;


    /**
     * 成功数据
     */
    private IPage<ProductSubmitInfo> pageInfo;
}
