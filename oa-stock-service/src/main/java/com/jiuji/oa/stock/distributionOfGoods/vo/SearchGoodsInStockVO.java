package com.jiuji.oa.stock.distributionOfGoods.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SearchGoodsInStockVO {

    /**
     * 销售开始时间
     */
    private LocalDateTime salesStartTime;

    /**
     * 销售结束时间
     */
    private LocalDateTime salesEndTime;

    private List<Integer> ppidList;

    private List<Integer> dropDownAreaIdList;

    /**
     * 门店类别
     */
    private List<Integer> kind1List;

    /**
     * 区域门店
     */
    private List<Integer> areaIdList;

    /**
     * 门店属性
     */
    private List<Integer> attributeList;

}
