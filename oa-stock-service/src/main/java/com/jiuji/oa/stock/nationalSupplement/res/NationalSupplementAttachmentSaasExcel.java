package com.jiuji.oa.stock.nationalSupplement.res;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class NationalSupplementAttachmentSaasExcel{



    /**
     * 小区
     */
    @ExcelProperty(value = "小区",order = 10)
    private String departName;

    /**
     * 门店
     */
    @ExcelProperty(value="门店",order = 20)
    private String area;

    @ExcelProperty(value="单号",order = 30)
    private Integer subId;
    /**
     * 销售人
     */
    @ExcelProperty(value = "销售人", order = 40)
    private String seller;
    /**
     * 审核状态
     */
    @ExcelProperty(value = "审核状态",order = 50)
    private String checkStateValue;
    /**
     * 审核备注原因
     */
    @ExcelProperty(value = "审核备注原因",order = 60)
    private String checkComment;

    /**
     * 审核时间
     */
    @ExcelProperty(value = "审核时间",order = 70)
    private String checkTimeStr;

    /**
     * 审核人
     */
    @ExcelProperty(value = "审核人",order = 80)
    private String checkUser;
    /**
     * 交易完成时间
     */
    @ExcelProperty(value = "交易完成时间",order = 90)
    private String tradeDate1Str;


}
