package com.jiuji.oa.stock.authorizationtransfer.enums;

import com.jiuji.oa.nc.abnormal.vo.ShowPrintingEnumVO;
import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum SelectTimeEnums implements CodeMessageEnumInterface {


    SUBMIT_TIME(0, "提交时间"),
    CHECK_TIME(1, "审核时间"),
    HANDLE_TIME(2, "办理时间");

    private Integer code;
    private String message;

    /**
     * 根据code获取其对应的名称
     *
     * @return String
     */
    public static String getMessageByCode(Integer code) {
        for (SelectTimeEnums purchaseState : values()) {
            if (purchaseState.getCode().equals(code)) {
                return purchaseState.getMessage();
            }
        }
        return "";
    }
    /**
     * 将所有的枚举转换成list
     *
     * @return
     */
    public static List<ShowPrintingEnumVO> getAllPrintingEnum() {
        SelectTimeEnums[] array = SelectTimeEnums.values();
        List<ShowPrintingEnumVO> arrayList = new ArrayList<>();
        for (SelectTimeEnums t : array) {
            ShowPrintingEnumVO showPrintingEnumVO = new ShowPrintingEnumVO()
                    .setLabel(t.getMessage())
                    .setValue(t.getCode());
            arrayList.add(showPrintingEnumVO);
        }
        return arrayList;
    }

}
