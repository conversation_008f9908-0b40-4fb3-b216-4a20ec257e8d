package com.jiuji.oa.stock.authorizationtransfer.entity;
import com.jiuji.oa.nc.user.po.Areainfo;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class CrossAuthTransferConfigExtend extends CrossAuthTransferConfig{


    /**
     * 名称
     */
    private String name;

    private String authorizeToName;

    private String mobile;

    private Areainfo area;
}
