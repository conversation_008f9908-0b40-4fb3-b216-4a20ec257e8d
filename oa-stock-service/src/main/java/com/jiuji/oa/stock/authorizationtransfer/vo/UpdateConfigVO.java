package com.jiuji.oa.stock.authorizationtransfer.vo;


import lombok.Data;

import javax.validation.constraints.NotNull;


/**
 * <AUTHOR>
 */
@Data
public class UpdateConfigVO {

    @NotNull(message = "id不能为空")
    private Integer id;

    /**
     * 描述
     */
    @NotNull(message = "描述不能为空")
    private String dsc;


    /**
     * 值
     */
    @NotNull(message = "值不能为空")
    private String value;
}
