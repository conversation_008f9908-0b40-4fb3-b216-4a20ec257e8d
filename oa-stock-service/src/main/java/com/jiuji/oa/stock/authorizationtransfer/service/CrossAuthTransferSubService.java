package com.jiuji.oa.stock.authorizationtransfer.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.stock.authorizationtransfer.entity.CrossAuthTransferDetail;
import com.jiuji.oa.stock.authorizationtransfer.entity.CrossAuthTransferSub;
import com.jiuji.oa.stock.authorizationtransfer.vo.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;


/**
 * <AUTHOR>
 */
public interface CrossAuthTransferSubService extends IService<CrossAuthTransferSub> {

    /**
     * 提交跨域调拨单
     * @param submitOrderVO
     * @return
     */
    SubmitResultVO submit(SubmitOrderVO submitOrderVO);

    /**
     * 根据门店查询授权名称
     * @param req
     * @return
     */
    AuthRes selectAuthByAreaId(AuthReq req);


    /**
     * 提单商品查询
     * @param req
     * @return
     */
    IPage<ProductSubmitInfo> selectProductSubmitInfo(ProductReq req);

    /**
     * 查询小件信息
     * @param req
     * @return
     */
    List<ProductSubmitInfo> selectProductKcList(ProductReq req);

    /**
     * 串号批量校验
     * @param req
     * @return
     */
    CheckImeiRes checkImei(CheckImeiReq req);

    /**
     * 调拨单删除
     * @param id
     */
    void delTransferSub(Integer id);

    /**
     * 库存解锁
     * @param detailList
     */
    void unlockProduct(List<CrossAuthTransferDetail> detailList);

    /**
     * 库存锁定
     * @param detailList
     */
    void lockProduct(List<CrossAuthTransferDetail> detailList);

    /**
     * 保存调拨单详情
     * @param detailList
     */
    void saveCrossAuthTransferDetailList(List<CrossAuthTransferDetail> detailList);

    /**
     * 校验导入数据
     * @param file
     * @return
     */
    List<VerifyProductDetailRes> verifyProduct(MultipartFile file,Integer transferAreaId) throws IOException;
}
