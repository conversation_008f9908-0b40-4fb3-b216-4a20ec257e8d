package com.jiuji.oa.stock.authorizationtransfer.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SubInfoVO {

    /**
     * 调拨单id
     */
    private Integer id;


    /**
     * 标题
     */
    private String title;

    /**
     *
     */
    private Integer status;
    private String statusValue;


    /**
     * 发货门店id
     */
    private Integer fromAreaId;
    private String fromArea;

    /**
     * 收货门店id
     */
    private Integer toAreaId;
    private String toArea;

    /**
     * 发货授权id
     */
    private Integer fromAuthId;
    private String fromAuth;

    /**
     * 收货授权id
     */
    private Integer toAuthId;
    private String toAuth;


    private List<DetailInfoVO> detailVOList;



}
