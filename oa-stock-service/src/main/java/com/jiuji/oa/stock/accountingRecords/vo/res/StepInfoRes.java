package com.jiuji.oa.stock.accountingRecords.vo.res;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class StepInfoRes {

    /**
     * 排序值
     */
    private Integer rank;

    /**
     * 步骤详情
     */
    private String infoMessage;

    /**
     * 步骤对应的门店
     */
    private String infoMessageArea;

    /**
     * 账号密码
     */
    private String accountPassword;

    /**
     * 账号
     */
    private String account;


    /**
     * 密码
     */
    private String password;
}
