package com.jiuji.oa.stock.nationalSupplement.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.oa.logapi.service.ISubLogService;
import com.jiuji.oa.loginfo.order.service.SubLogsCloud;
import com.jiuji.oa.loginfo.order.vo.req.SubLogsNewReq;
import com.jiuji.oa.nc.common.bo.OaUserBO;
import com.jiuji.oa.nc.common.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.nc.common.constant.DataSourceConstants;
import com.jiuji.oa.nc.common.enums.XtenantEnum;
import com.jiuji.oa.nc.common.exception.CustomizeException;
import com.jiuji.oa.nc.common.exception.RRExceptionHandler;
import com.jiuji.oa.nc.dict.service.ISysConfigService;
import com.jiuji.oa.nc.oaapp.po.SysConfig;
import com.jiuji.oa.nc.stock.entity.ProductMkc;
import com.jiuji.oa.nc.stock.service.IProductMkcService;
import com.jiuji.oa.nc.stock.service.ISmsService;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.oa.stock.common.component.AreaCache;
import com.jiuji.oa.stock.nationalSupplement.enums.NationalSupplementKind;
import com.jiuji.oa.stock.nationalSupplement.enums.NationalSupplementSelectEnum;
import com.jiuji.oa.stock.nationalSupplement.enums.RejectionReasonEnum;
import com.jiuji.oa.stock.nationalSupplement.req.*;
import com.jiuji.oa.stock.nationalSupplement.res.*;
import com.jiuji.oa.stock.nationalSupplement.service.NationalSupplementService;
import com.jiuji.oa.stock.sub.service.ISubService;
import com.jiuji.oa.wuliu.entity.WuLiuSubFlagRecordEntity;
import com.jiuji.oa.wuliu.enums.*;
import com.jiuji.oa.wuliu.mapper.WuLiuSubFlagRecordMapper;
import com.jiuji.oa.wuliu.service.IWuLiuSubFlagRecordService;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.foundation.message.send.constants.OaMesTypeEnum;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.enums.EnumVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import com.jiuji.oa.stock.sub.entity.Sub;


@Slf4j
@Service
@DS("oanewWrite")
public class NationalSupplementServiceImpl implements NationalSupplementService {
    @Resource
    private  AreaCache areaCache;
    @Resource
    private IWuLiuSubFlagRecordService subFlagRecordService;
    @Resource
    private WuLiuSubFlagRecordMapper subFlagRecordMapper;
    @Resource
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;
    @Resource
    private ISubLogService subLogService;
    @Resource
    private ISmsService smsService;
    @Resource
    private ISysConfigService sysConfigService;
    @Resource
    private SysConfigClient sysConfigClient;
    @Resource
    private IProductMkcService productMkcService;
    @Resource
    private SubLogsCloud subLogsCloud;
    @Resource
    private ISubService subService;

    public static final String NATIONAL_SUPPLEMENT_RANK = "fjsh";
    public static final String FINANCIAL_REVIEW_RANK = "qthd";
    public static final String OPERATION_RANK = "gdgl";
    public static final String SUCCESSMESSAGE = "操作成功";
    public static final String AUTOMATIC_DECLARATION_SYSTEM = "自动申报系统";
    public static final String OA_SYSTEM = "OA系统";

    @Override
    public List<DeclarationNotApprovedRes> selectDeclarationNotApproved(DeclarationNotApprovedReq declarationNotApprovedReq) {
        DeclarationNotApprovedReq req = new DeclarationNotApprovedReq();
        OaUserBO userBO = Optional.ofNullable(abstractCurrentRequestComponent.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("登录信息失效"));
        req.setUserId(userBO.getUserId());
        req.setNotPushList(RejectionReasonEnum.getNotPush());
        List<DeclarationNotApprovedRes> declarationNotApprovedRes = getDeclarationNotApprovedRes(req);
        if(CollUtil.isEmpty(declarationNotApprovedRes)){
            return new ArrayList<>();
        }
        List<Integer> subIdList = declarationNotApprovedRes.stream().map(DeclarationNotApprovedRes::getSubId).filter(ObjectUtil::isNotNull).collect(Collectors.toList());
        List<WuLiuSubFlagRecordEntity> wuLiuSubFlagRecordEntities = CommonUtils.bigDataInQuery(subIdList, ids -> subFlagRecordService.lambdaQuery()
                .eq(WuLiuSubFlagRecordEntity::getFlagType, NumberConstant.SEVEN)
                .in(WuLiuSubFlagRecordEntity::getSubId, ids)
                .select(WuLiuSubFlagRecordEntity::getSubId, WuLiuSubFlagRecordEntity::getStatusComment)
                .list());
        if(CollUtil.isNotEmpty(wuLiuSubFlagRecordEntities)){
            Map<Integer, String> subFlagRecordMap = wuLiuSubFlagRecordEntities.stream()
                    .collect(Collectors.toMap(item->Convert.toInt(item.getSubId()), WuLiuSubFlagRecordEntity::getStatusComment, (n1, n2) -> n2));
            declarationNotApprovedRes.forEach(item->item.setStatusComment(subFlagRecordMap.getOrDefault(item.getSubId(), "")));
        }
        return declarationNotApprovedRes;
    }

    /**
     * 推送信息查询
     * @param req
     * @return
     */
    private List<DeclarationNotApprovedRes> getDeclarationNotApprovedRes(DeclarationNotApprovedReq req){
        List<DeclarationNotApprovedRes> declarationNotApprovedRes = subFlagRecordMapper.selectDeclarationNotApproved(req);
        //查询V区门店
        List<Integer> departVareaIdList = subFlagRecordMapper.selectDepartVAreaId();
        //"附件缺失-能效标识", "3" 	仅V区推送
        declarationNotApprovedRes = declarationNotApprovedRes.stream().filter(item->{
            String statusComment = item.getStatusComment();
            if(RejectionReasonEnum.MISSING_ENERGY_LABEL.getMessage().equals(statusComment)){
                return departVareaIdList.contains(item.getAreaid());
            } else {
                return Boolean.TRUE;
            }
        }).collect(Collectors.toList());
        return declarationNotApprovedRes;
    }

    /**
     * 推送申报不通过
     * @return
     */
    @Override
    public List<DeclarationNotApprovedRes> sendDeclarationNotApproved() {
        DeclarationNotApprovedReq req = new DeclarationNotApprovedReq();
        req.setNotPushList(RejectionReasonEnum.getNotPush());
        List<DeclarationNotApprovedRes> declarationNotApprovedRes = getDeclarationNotApprovedRes(req);
        if(CollUtil.isEmpty(declarationNotApprovedRes)){
            return new ArrayList<>();
        }
        //收集没有销售的国补订单
        List<DeclarationNotApprovedRes> noUserIdList = new ArrayList<>();
        Map<Integer, List<DeclarationNotApprovedRes>> declarationList = declarationNotApprovedRes.stream()
                .filter(item -> {
                    if(Objects.nonNull(item.getUserId())){
                        return Boolean.TRUE;
                    }
                    noUserIdList.add(item);
                    return Boolean.FALSE;
                })
                .collect(Collectors.groupingBy(DeclarationNotApprovedRes::getUserId));
        if(CollUtil.isNotEmpty(noUserIdList)){
            String subIds = noUserIdList.stream().filter(item -> ObjectUtil.isNotNull(item.getSubId())).map(item -> Convert.toStr(item.getSubId())).collect(Collectors.joining(","));
            log.warn("国补订单没有销售人员：{}", subIds);
        }
        LocalDateTime now = LocalDateTime.now();
        String moaHost = Optional.of(sysConfigClient.getValueByCode(SysConfigConstant.MOA_URL))
                .filter(t -> t.getCode() == ResultCode.SUCCESS)
                .map(R::getData)
                .orElseThrow(() -> new CustomizeException("获取M端域名出错"));
        declarationList.forEach((k,v)->{
            if(CollUtil.isNotEmpty(v)){
                String content = String.format("%s月%s日共有%s个国补订单申报未提交成功，点击【查看详情】查看订单号及原因，请及时相关负责人跟进处理", now.getMonthValue(),now.getDayOfMonth(),v.size());
                String url =moaHost+"/new/#/market/national-subsidy/audit-list";
                smsService.sendOaMsg(content,url ,Convert.toStr(k), OaMesTypeEnum.SYSTEM);
            }
        });
        return declarationNotApprovedRes;
    }

    @Override
    public List<Integer> selectDeclarationData(DeclarationDataReq declarationDataReq) {
        List<Integer> subIds = subFlagRecordService.lambdaQuery().eq(WuLiuSubFlagRecordEntity::getCheckState, declarationDataReq.getCheckState())
                .eq(WuLiuSubFlagRecordEntity::getFinanceCheckState, declarationDataReq.getFinanceCheckState())
                .eq(WuLiuSubFlagRecordEntity::getStatus, declarationDataReq.getStatus())
                .eq(WuLiuSubFlagRecordEntity::getFlagType, NumberConstant.SEVEN)
                .select(WuLiuSubFlagRecordEntity::getSubId)
                .list().stream()
                .map(item -> Convert.toInt(item.getSubId()))
                .distinct().collect(Collectors.toList());
        if(CollUtil.isNotEmpty(subIds)){
            //过滤出云南省的订单
           return CommonUtils.bigDataInQuery(subIds, ids -> subService.lambdaQuery().in(Sub::getSubId, ids)
                    .select(Sub::getSubId, Sub::getAreaid)
                    .last("and exists(select 1 from dbo.areainfo a with(nolock ) where  a.id = areaid and a.pid = 53)")
                    .list()).stream().map(Sub::getSubId).filter(Objects::nonNull).collect(Collectors.toList());
        }
        return subIds;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ApproveRes updateNationalSupplementState(ApproveReq approveReq) {
        ApproveRes approveRes = new ApproveRes();
        Integer id = approveReq.getId();
        Boolean pendingApprove = Optional.ofNullable(approveReq.getPendingApprove()).orElse(Boolean.TRUE);
        WuLiuSubFlagRecordEntity wuLiuSubFlagRecordEntity = Optional.ofNullable(subFlagRecordService.getById(id)).orElseThrow(() -> new CustomizeException("找不到对应的审核记录，审核失败"));
        Integer checkStateOld = Optional.ofNullable(wuLiuSubFlagRecordEntity.getCheckState()).orElse(SubFlagRecordStateEnum.PENDING.getCode());
        if(pendingApprove && !SubFlagRecordStateEnum.PENDING.getCode().equals(checkStateOld)){
            throw new CustomizeException("审核状态不是待审核，审核失败");
        }
        OaUserBO userBO = approveReq.getUserBO();
        String userName = userBO.getUserName();
        Integer checkState = Optional.ofNullable(approveReq.getCheckState()).orElseThrow(()->new CustomizeException("审核状态不能为空"));
        boolean updated = subFlagRecordService.lambdaUpdate()
                .eq(WuLiuSubFlagRecordEntity::getId, id)
                .and(pendingApprove,item->item.isNull(WuLiuSubFlagRecordEntity::getCheckState).or().eq(WuLiuSubFlagRecordEntity::getCheckState, SubFlagRecordStateEnum.PENDING.getCode()))
                .set(WuLiuSubFlagRecordEntity::getCheckUser, userName)
                .set(WuLiuSubFlagRecordEntity::getCheckTime, LocalDateTime.now())
                .set(WuLiuSubFlagRecordEntity::getCheckComment, approveReq.getCheckComment())
                .set(WuLiuSubFlagRecordEntity::getCheckState,checkState)
                .set(SubFlagRecordStateEnum.PENDING.getCode().equals(checkState),WuLiuSubFlagRecordEntity::getOperationCheckState, SubFlagRecordStateEnum.PENDING.getCode())
                .set(SubFlagRecordStateEnum.PENDING.getCode().equals(checkState),WuLiuSubFlagRecordEntity::getOperationCheckUser, null)
                .set(SubFlagRecordStateEnum.PENDING.getCode().equals(checkState),WuLiuSubFlagRecordEntity::getOperationCheckTime, null)
                .update();
        if(!updated){
            throw new CustomizeException("审核失败");
        }
        //订单日志记录
        String comment = String.format("配置附件状态由%s变为%s", SubFlagRecordStateEnum.getMessageByCode(checkStateOld),SubFlagRecordStateEnum.getMessageByCode(approveReq.getCheckState()));
        //只有输出才加备注
        String checkComment = approveReq.getCheckComment();
        if(StrUtil.isNotEmpty(checkComment)){
            comment = comment+",备注:"+checkComment;
        }
        SubLogsNewReq subLogsNewReq = new SubLogsNewReq();
        subLogsNewReq.setSubId(Convert.toInt(wuLiuSubFlagRecordEntity.getSubId()));
        subLogsNewReq.setType(1);
        subLogsNewReq.setShowType(false);
        subLogsNewReq.setComment(comment);
        subLogsNewReq.setDTime(LocalDateTime.now());
        subLogsNewReq.setInUser(userName);
        subLogService.saveSubLog(subLogsNewReq);
        approveRes.setId(id);
        //如果修改为已审核，则自动更新申报状态
        if(XtenantEnum.isJiujiXtenant() && SubFlagRecordStateEnum.APPROVED.getCode().equals(approveReq.getCheckState())){
            AutoUpdateStateReq autoUpdateStateReq = new AutoUpdateStateReq();
            String commentAuto = String.format("审核状态修改由%s修改为%s", SubFlagRecordStateEnum.PENDING.getMessage(), SubFlagRecordStateEnum.APPROVED.getMessage());
            autoUpdateStateReq.setId(id)
                    .setComment(commentAuto)
                    .setSubId(Convert.toInt(wuLiuSubFlagRecordEntity.getSubId()))
                    .setUserName(userName);
            autoUpdateState(autoUpdateStateReq);
        }
        return approveRes;
    }
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ApproveRes uploadAttachments(UploadAttachmentsReq attachmentsReq) {
        ApproveRes approveRes = new ApproveRes();
        Integer subId = attachmentsReq.getSubId();
        List<WuLiuSubFlagRecordEntity> list = subFlagRecordService.lambdaQuery()
                .eq(WuLiuSubFlagRecordEntity::getFlagType, NumberConstant.SEVEN)
                .eq(WuLiuSubFlagRecordEntity::getSubId, subId)
                .select(WuLiuSubFlagRecordEntity::getId,WuLiuSubFlagRecordEntity::getCheckState)
                .list();
        if(CollUtil.isEmpty(list)){
            throw new CustomizeException("订单号无效");
        }
        WuLiuSubFlagRecordEntity recordEntity = list.get(NumberConstant.ZERO);
        Integer anInt = Convert.toInt(recordEntity.getId());
        approveRes.setId(anInt);
        if(SubFlagRecordStateEnum.PENDING.getCode().equals(recordEntity.getCheckState())){
            String msg = String.format("修改审核状态为%s 原始状态为：%s 所以不用处理", SubFlagRecordStateEnum.PENDING.getCode(), SubFlagRecordStateEnum.PENDING.getCode());
            log.warn(msg);
           return approveRes;
        }
        //【审核状态】变更为【已拒绝】
        OaUserBO  userBO = Optional.ofNullable(abstractCurrentRequestComponent.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("获取当前登录用户失败"));
        ApproveReq approveReq =new ApproveReq();
        userBO.setUserName(OA_SYSTEM);
        approveReq.setUserBO(userBO);
        approveReq.setId(anInt);
        approveReq.setCheckComment("附件上传");
        approveReq.setPendingApprove(Boolean.FALSE);
        approveReq.setCheckState(SubFlagRecordStateEnum.PENDING.getCode());
        updateNationalSupplementState(approveReq);
        return approveRes;
    }


    /**
     * 数据校验
     * @param req
     */
    private void checkInfo(NationalAttachmentReq req){
        Integer selectType = req.getSelectType();
        String selectValue = req.getSelectValue();
        List<Integer> list = Arrays.asList(NationalSupplementSelectEnum.PPID.getCode(), NationalSupplementSelectEnum.SUBID.getCode());
        if(list.contains(selectType) &&  StrUtil.isNotEmpty(selectValue) && !NumberUtil.isInteger(selectValue)){
            throw new CustomizeException("请输入合法的PPID和订单号");
        }
    }

    /**
     * 数据处理
     * @param req
     */
    private HandleDataRes handleData(NationalAttachmentReq req,OaUserBO userBO) {
        HandleDataRes handleDataRes = new HandleDataRes();
        //获取当前登录的人地区权限
        List<Integer> areaIdList = req.getAreaIdList();
        if(CollUtil.isEmpty(areaIdList)){
            List<Integer> rankAreaIds = areaCache.getAreaIdsByAreas(userBO.getAreas()).stream().map(Convert::toInt).collect(Collectors.toList());
            if(CollUtil.isEmpty(rankAreaIds)){
                throw new CustomizeException("当前登录人没有地区权限");
            }
            req.setAreaIdList(rankAreaIds) ;
        }
        List<Integer> nationalAttachmentCashier = req.getNationalAttachmentCashier();
        //输出的数据需要转换一下
        if(XtenantEnum.isSaasXtenant() && CollUtil.isNotEmpty(nationalAttachmentCashier)){
            List<SysConfig> sysConfigs = sysConfigService.lambdaQuery().in(SysConfig::getId, nationalAttachmentCashier)
                    .select(SysConfig::getValue).list();
            if(CollUtil.isNotEmpty(sysConfigs)){
                List<Integer> cashierIds = sysConfigs.stream().map(SysConfig::getValue).filter(NumberUtil::isNumber).map(Convert::toInt).collect(Collectors.toList());
                req.setNationalAttachmentCashier(cashierIds);
            }
        }
        //处理国补类型
        boolean isOnlineNational = true;
        if (XtenantEnum.isJiujiXtenant()) {
            //线上国补
            if (NationalSupplementKind.ONLINE.getCode().equals(req.getNationalSupplementKind())) {
                if (CollUtil.isEmpty(req.getNationalAttachmentCashier()) || req.getNationalAttachmentCashier().stream().anyMatch(v -> Objects.equals(0,v))) {
                    req.setOnlineNationalAttachmentCashier(Collections.singletonList(0));
                } else {
                    //线上国补，政府以旧换新选了线下，直接返回
                    handleDataRes.setIsDeReturn(Boolean.TRUE);
                }
            }
            if (NationalSupplementKind.OFFLINE.getCode().equals(req.getNationalSupplementKind())) {
                if (CollUtil.isEmpty(req.getNationalAttachmentCashier())) {
                    //查询线下国补方式
                    req.setNationalAttachmentCashier(sysConfigService.getNationalSupplementCashier().stream().map(v -> Convert.toInt(v.getValue())).filter(Objects::nonNull).collect(Collectors.toList()));
                } else if (req.getNationalAttachmentCashier().stream().allMatch(v -> Objects.equals(0,v))) {
                    //线下国补，政府以旧换新只选了线上，直接返回
                    handleDataRes.setIsDeReturn(Boolean.TRUE);
                } else {
                    //移除线上类型
                    req.setNationalAttachmentCashier(req.getNationalAttachmentCashier().stream().filter(v -> !Objects.equals(0,v)).collect(Collectors.toList()));
                }
            }
            if (CollUtil.isNotEmpty(req.getNationalAttachmentCashier())) {
                List<Integer> nationalAttachmentCashiers = req.getNationalAttachmentCashier().stream().filter(v -> !Objects.equals(0,v)).distinct().collect(Collectors.toList());
                List<Integer> onlineNationalAttachmentCashier = req.getNationalAttachmentCashier().stream().filter(v -> Objects.equals(0,v)).distinct().collect(Collectors.toList());
                if (CollUtil.isNotEmpty(onlineNationalAttachmentCashier)) {
                    req.setOnlineNationalAttachmentCashier(onlineNationalAttachmentCashier);
                } else {
                    isOnlineNational = false;
                }
                req.setNationalAttachmentCashier(nationalAttachmentCashiers);
            }
        }
        handleDataRes.setIsOnlineNational(isOnlineNational);
        return handleDataRes;
    }


    /**
     * 国补附件列表审核查询
     * @param req
     * @return
     */
    @DS(DataSourceConstants.CH999_OA_NEW)
    @Override
    public IPage<NationalSupplementAttachmentRes> selectNationalSupplementState(NationalAttachmentReq req) {
        OaUserBO userBO = Optional.ofNullable(abstractCurrentRequestComponent.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("当前登录信息失效，请登录"));
        //数据校验
        checkInfo(req);
        //数据处理
        HandleDataRes handleDataRes = handleData(req, userBO);
        if(Optional.ofNullable(handleDataRes.getIsDeReturn()).orElse(Boolean.FALSE)){
            return new Page<>();
        }
        // 1. 分页查询
        Page<NationalSupplementAttachmentRes> page = new Page<>(req.getCurrent(), req.getSize());
        IPage<NationalSupplementAttachmentRes> resultPage = new Page<>();
        Boolean isExport = Optional.ofNullable(req.getIsExport()).orElse(Boolean.FALSE);
        if(!isExport){
            resultPage = subFlagRecordMapper.pageNationalSupplementState(page, req);
        } else {
            Integer count = subFlagRecordMapper.selectNationalSupplementStateCount(req);
            Integer size = Convert.toInt(req.getSize());
            if(count>size){
                throw new CustomizeException("导出数据量不能超过："+size);
            }
            resultPage.setRecords(subFlagRecordMapper.selectNationalSupplementState(req));
        }
        List<NationalSupplementAttachmentRes> records = resultPage.getRecords();
        // 2. 如果没有记录直接返回
        if (CollUtil.isEmpty(records)) {
            return resultPage;
        }
        List<Integer> areaIdList = records.stream()
                .map(NationalSupplementAttachmentRes::getAreaId)
                .filter(Objects::nonNull).distinct()
                .collect(Collectors.toList());
        Map<Integer, String> departNameMap = getDepartNameMap(areaIdList);
        // 4. 获取附件信息映射（确保返回非null的Map）

        Map<Integer, List<NationalAttachmentInfo>> attachmentMap;
        Map<Integer, String> imeiMap;
        if(!isExport){
            // 3. 获取所有subId并查询附件信息 获取所有的门店
            List<Integer> subIds = records.stream()
                    .map(NationalSupplementAttachmentRes::getSubId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            attachmentMap = getAttachmentInfoMap(subIds);
            List<Integer> basketIdList = records.stream()
                    .map(NationalSupplementAttachmentRes::getBasketId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            imeiMap = getImeiMap(basketIdList);
        } else {
            attachmentMap = new HashMap<>();
            imeiMap = new HashMap<>();
        }
        //处理类型
        Map<Integer, Integer> onlineKindMap = new HashMap<>();
        if (Optional.ofNullable(handleDataRes.getIsOnlineNational()).orElse(Boolean.TRUE)) {
            List<String> subIds = records.stream().map(v -> Convert.toStr(v.getSubId())).collect(Collectors.toList());
            onlineKindMap.putAll(this.getNationalSupplementKindList(subIds).stream().collect(Collectors.toMap(NationalSupplementKindRes::getSubId, NationalSupplementKindRes::getKind, (v1, v2) -> v1)));
        }

        // 5. 处理结果数据
        records.parallelStream().forEach(record -> {
            tranState(record);
            // 处理附件列表
            if(!isExport) {
                Optional.ofNullable(attachmentMap.get(record.getSubId())).ifPresent(record::setNationalAttachmentInfoList);
                Optional.ofNullable(imeiMap.get(record.getBasketId())).ifPresent(record::setImei);
            }
            Optional.ofNullable(departNameMap.get(record.getAreaId())).ifPresent(record::setDepartName);
            //国补类型
            if (onlineKindMap.containsKey(record.getSubId())) {
                record.setNationalSupplementKindName(NationalSupplementKind.ONLINE.getMessage());
            } else {
                record.setNationalSupplementKindName(NationalSupplementKind.OFFLINE.getMessage());
            }
        });
        return resultPage;
    }

    /**
     * 状态转化
     * @param record
     */
    private static void tranState(NationalSupplementAttachmentRes record) {
        //状态转换
        Integer checkState = Optional.ofNullable(record.getCheckState()).orElse(SubFlagRecordStateEnum.PENDING.getCode());
        record.setCheckStateValue(SubFlagRecordStateEnum.getMessageByCode(checkState));
        //申报状态转换
        Integer declareState = Optional.ofNullable(record.getStatus()).orElse(DeclareStateEnum.NOT_DECLARE.getCode());
        record.setStatusValue(DeclareStateEnum.getMessageByCode(declareState));
        //财务状态转换
        Integer financeCheckState = Optional.ofNullable(record.getFinanceCheckState()).orElse(FinanceCheckStateEnum.PENDING.getCode());
        record.setFinanceCheckStateValue(FinanceCheckStateEnum.getMessageByCode(financeCheckState));
        //运营复核状态转换
        Integer operationState = Optional.ofNullable(record.getOperationCheckState()).orElse(OperationStateEnum.PENDING.getCode());
        record.setOperationCheckStateValue(OperationStateEnum.getMessageByCode(operationState));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReDeclareJiuJiRes reDeclare(ReDeclareJiuJiReq reDeclareReq) {
        OaUserBO userBO = Optional.ofNullable(abstractCurrentRequestComponent.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("当前人登录信息为空"));
        List<Integer> idList = reDeclareReq.getIdList();
        if(CollUtil.isEmpty(idList)){
            throw new CustomizeException("申报数据不能为空");
        }
        if(idList.size()>NumberConstant.ONE_THOUSAND){
            throw new CustomizeException("申报数据不能超过1000个");
        }
        List<WuLiuSubFlagRecordEntity> list = subFlagRecordService.lambdaQuery().in(WuLiuSubFlagRecordEntity::getId, idList).list();
        List<SubLogsNewReq> collect = list.stream().map(item -> {
            SubLogsNewReq subLogsNewReq = new SubLogsNewReq();
            String comment = String.format("国补订单申报状态由%s更改为%s，原因：%s", DeclareStateEnum.getMessageByCode(item.getStatus()),DeclareStateEnum.NOT_DECLARE.getMessage(),"操作重新申报");
            subLogsNewReq.setSubId(Convert.toInt(item.getSubId()));
            subLogsNewReq.setType(1);
            subLogsNewReq.setShowType(false);
            subLogsNewReq.setComment(comment);
            subLogsNewReq.setDTime(LocalDateTime.now());
            subLogsNewReq.setInUser(userBO.getUserName());
            return subLogsNewReq;
        }).collect(Collectors.toList());
        boolean updated = subFlagRecordService.lambdaUpdate().in(WuLiuSubFlagRecordEntity::getId, idList)
                .eq(WuLiuSubFlagRecordEntity::getStatus, DeclareStateEnum.DECLARE_FAIL.getCode())
                .eq(WuLiuSubFlagRecordEntity::getFlagType, NumberConstant.SEVEN)
                .set(WuLiuSubFlagRecordEntity::getFinanceCheckState, FinanceCheckStateEnum.APPROVED.getCode())
                .set(WuLiuSubFlagRecordEntity::getStatus, DeclareStateEnum.NOT_DECLARE.getCode())
                .set(WuLiuSubFlagRecordEntity::getCheckState, SubFlagRecordStateEnum.APPROVED.getCode())
                .set(WuLiuSubFlagRecordEntity::getStatusComment, "")
                .update();
        if(!updated){
            throw new CustomizeException("更新失败，请检查订单状态或数据。");
        }
        subLogsCloud.addSubLogBatch(collect);
        return new ReDeclareJiuJiRes();
    }


    @Override
    public NationalSupplementAttachmentRes selectNationalById(Integer id) {
        NationalSupplementAttachmentRes nationalSupplementAttachmentRes = new NationalSupplementAttachmentRes();
        WuLiuSubFlagRecordEntity recordEntity = subFlagRecordService.getById(id);
        if(ObjectUtil.isNull(recordEntity)){
            return nationalSupplementAttachmentRes;
        }
        BeanUtil.copyProperties(recordEntity,nationalSupplementAttachmentRes);
        tranState(nationalSupplementAttachmentRes);
        return nationalSupplementAttachmentRes;
    }


    @Override
    @DS(DataSourceConstants.CH999_OA_NEW)
    public NationalAttachmentCount selectNationalSupplementSum(NationalAttachmentReq req) {
        OaUserBO userBO = Optional.ofNullable(abstractCurrentRequestComponent.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("当前登录信息失效，请登录"));
        //数据校验
        checkInfo(req);
        //数据处理
        HandleDataRes handleDataRes = handleData(req, userBO);
        if(Optional.ofNullable(handleDataRes.getIsDeReturn()).orElse(Boolean.FALSE)){
            return new NationalAttachmentCount();
        }
        return subFlagRecordMapper.selectNationalSupplementCount(req);
    }

    private Map<Integer, String> getDepartNameMap(List<Integer> areaIdList){
        Map<Integer, String> departNameMap = new HashMap<>();
        List<DepartNameInfo> departNameInfos = CommonUtils.bigDataInQuery(areaIdList, ids -> subFlagRecordMapper.selectDepartNameInfo(ids));
        if(CollUtil.isNotEmpty(departNameInfos)){
            departNameMap=departNameInfos.stream().filter(item-> ObjectUtil.isNotNull(item) && ObjectUtil.isNotNull(item.getDepartName()))
                    .collect(Collectors.toMap(DepartNameInfo::getAreaId, DepartNameInfo::getDepartName, (oldValue, newValue) -> oldValue));
        }
        return departNameMap;
    }

    /**
     * 财务复合
     * @param req
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public FinancialReviewRes financialReview(FinancialReviewReq req) {
        FinancialReviewRes financialReviewRes = new FinancialReviewRes();
        OaUserBO userBO = Optional.ofNullable(abstractCurrentRequestComponent.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("当前登录信息失效，请登录"));
        if(!Optional.ofNullable(userBO.getRank()).orElse(new ArrayList<>()).contains(FINANCIAL_REVIEW_RANK)){
            throw new CustomizeException("没有权限"+FINANCIAL_REVIEW_RANK+"不能进行复核");
        }
        List<Integer> subIds = req.getSubIds();
        if(CollUtil.isEmpty(subIds)){
            throw new CustomizeException("请选择需要复核的订单");
        }
        //找出重复的数据
        List<Integer> subIdFrequency = subIds.stream()
                .filter(id -> Collections.frequency(subIds, id) > 1)
                .distinct()
                .collect(Collectors.toList());
        if(CollUtil.isNotEmpty(subIdFrequency)){
            throw new CustomizeException("复核订单号重复："+subIdFrequency);
        }
        if(subIds.size()>NumberConstant.FIVE_HUNDRED){
            throw new CustomizeException("一次最多复核500个订单");
        }
        List<WuLiuSubFlagRecordEntity> list = subFlagRecordService.lambdaQuery().eq(WuLiuSubFlagRecordEntity::getFlagType, NumberConstant.SEVEN)
                .in(WuLiuSubFlagRecordEntity::getSubId, subIds)
                .select(WuLiuSubFlagRecordEntity::getSubId, WuLiuSubFlagRecordEntity::getId)
                .list();
        if(CollUtil.isEmpty(list)){
            throw new CustomizeException("订单号无效");
        }
        //数据库subId
        List<Integer> subIdsOr = list.stream().map(WuLiuSubFlagRecordEntity::getSubId).filter(ObjectUtil::isNotNull).map(Convert::toInt).distinct().collect(Collectors.toList());
        //找出额外的数据
        List<Integer> findExtraSubIds = subIds.stream().filter(id -> !subIdsOr.contains(id)).collect(Collectors.toList());
        if(CollUtil.isNotEmpty(findExtraSubIds)){
            String join = findExtraSubIds.stream().map(Convert::toStr).collect(Collectors.joining(","));
            throw new CustomizeException("提交失败！订单"+join+" 复核失败，请确认订单状态是否完成，修正数据后重新提交。");
        }

        String userName = userBO.getUserName();
        String comment = String.format("财务复核状态由%s修改为%s", FinanceCheckStateEnum.PENDING.getMessage(), FinanceCheckStateEnum.APPROVED.getMessage());
        subIds.forEach(item->{
            boolean updated = subFlagRecordService.lambdaUpdate().eq(WuLiuSubFlagRecordEntity::getSubId, item)
                    .eq(WuLiuSubFlagRecordEntity::getFlagType,NumberConstant.SEVEN)
                    .and(x->x.isNull(WuLiuSubFlagRecordEntity::getFinanceCheckState).or().eq(WuLiuSubFlagRecordEntity::getFinanceCheckState, FinanceCheckStateEnum.PENDING.getCode()))
                    .set(WuLiuSubFlagRecordEntity::getFinanceCheckState, FinanceCheckStateEnum.APPROVED.getCode())
                    .set(WuLiuSubFlagRecordEntity::getFinanceCheckUser, userName)
                    .set(WuLiuSubFlagRecordEntity::getFinanceCheckTime, LocalDateTime.now())
                    .update();
            if(!updated){
                throw new CustomizeException("订单："+item+"复核失败，请确认是否已完成或者已经复核");
            }
            SubLogsNewReq subLogsNewReq = new SubLogsNewReq();
            subLogsNewReq.setSubId(item);
            subLogsNewReq.setType(1);
            subLogsNewReq.setShowType(false);
            subLogsNewReq.setComment(comment);
            subLogsNewReq.setDTime(LocalDateTime.now());
            subLogsNewReq.setInUser(userName);
            subLogService.saveSubLog(subLogsNewReq);
        });
        //附件审核通过+财务审核通过+申报状态为提交失败， 则更新申报状态为未提交
        list.forEach(item->{
            AutoUpdateStateReq autoUpdateStateReq = new AutoUpdateStateReq();
            autoUpdateStateReq.setId(Convert.toInt(item.getId()))
                    .setComment(comment)
                    .setSubId(Convert.toInt(item.getSubId()))
                    .setUserName(userName);
            autoUpdateState(autoUpdateStateReq);
        });
        financialReviewRes.setResult("本次成功复核订单"+subIds.size()+"个");
        return financialReviewRes;
    }

    /**
     * 附件审核通过+财务审核通过+申报状态为提交失败， 则更新申报状态为未提交
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void autoUpdateState(AutoUpdateStateReq autoUpdateStateReq){
        Integer id = autoUpdateStateReq.getId();
        boolean updated = subFlagRecordService.lambdaUpdate().eq(WuLiuSubFlagRecordEntity::getId, id)
                .eq(WuLiuSubFlagRecordEntity::getCheckState, SubFlagRecordStateEnum.APPROVED.getCode())
                .eq(WuLiuSubFlagRecordEntity::getFinanceCheckState, FinanceCheckStateEnum.APPROVED.getCode())
                .eq(WuLiuSubFlagRecordEntity::getStatus, DeclareStateEnum.DECLARE_FAIL.getCode())
                .set(WuLiuSubFlagRecordEntity::getStatus, DeclareStateEnum.NOT_DECLARE.getCode())
                .set(WuLiuSubFlagRecordEntity::getStatusComment, "")
                .update();
        //如果操作成功进行日志记录
        if(updated){
            Integer subId = autoUpdateStateReq.getSubId();
            String userName = autoUpdateStateReq.getUserName();
            String comment = Optional.ofNullable(autoUpdateStateReq.getComment()).orElse("");
            SubLogsNewReq subLogsNewReq = new SubLogsNewReq();
            subLogsNewReq.setSubId(subId);
            subLogsNewReq.setType(1);
            subLogsNewReq.setShowType(false);
            subLogsNewReq.setComment(comment+"，满足附件审核通过+财务审核通过+申报状态为提交失败， 则更新申报状态为未提交");
            subLogsNewReq.setDTime(LocalDateTime.now());
            subLogsNewReq.setInUser(userName);
            subLogService.saveSubLog(subLogsNewReq);
        }
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public OperationReviewRes operationReview(OperationReviewReq req) {
        OperationReviewRes operationReviewRes = new OperationReviewRes();
        OaUserBO userBO = Optional.ofNullable(abstractCurrentRequestComponent.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("当前登录信息失效，请登录"));
        if(!Optional.ofNullable(userBO.getRank()).orElse(new ArrayList<>()).contains(OPERATION_RANK)){
            throw new CustomizeException("没有权限"+OPERATION_RANK+"不能进行复核");
        }
        Integer id = req.getId();
        WuLiuSubFlagRecordEntity subFlagRecord = Optional.ofNullable(subFlagRecordService.getById(id)).orElseThrow(() -> new CustomizeException("复核数据查询为空"));
        if(SubFlagRecordStateEnum.PENDING.getCode().equals(Optional.ofNullable(subFlagRecord.getCheckState()).orElse(SubFlagRecordStateEnum.PENDING.getCode()))){
            throw new CustomizeException("该订单状态为待审核，不可以进行审核");
        }
        String userName = userBO.getUserName();
        Integer operationState = req.getOperationState();
        //如果是拒绝 附件【审核状态】变为相反状态，需要记录订单日志
        if(OperationStateEnum.REJECTED.getCode().equals(operationState)){
            Integer checkState = subFlagRecord.getCheckState();
            ApproveReq approveReq = new ApproveReq();
            approveReq.setUserBO(userBO);
            approveReq.setId(Convert.toInt(subFlagRecord.getId()));
            approveReq.setCheckComment(req.getCheckComment());
            approveReq.setPendingApprove(Boolean.FALSE);
            if(SubFlagRecordStateEnum.APPROVED.getCode().equals(checkState)){
                approveReq.setCheckState(SubFlagRecordStateEnum.REJECTED.getCode());
            } else if(SubFlagRecordStateEnum.REJECTED.getCode().equals(checkState)){
                approveReq.setCheckState(SubFlagRecordStateEnum.APPROVED.getCode());
            } else {
                throw new CustomizeException("订单："+subFlagRecord.getSubId()+"复核失败，请确认是否已完成或者待复核");
            }
            updateNationalSupplementState(approveReq);
        }
        //运营状态修改
        boolean updated = subFlagRecordService.lambdaUpdate().eq(WuLiuSubFlagRecordEntity::getId, id)
                .in(WuLiuSubFlagRecordEntity::getCheckState,Arrays.asList(SubFlagRecordStateEnum.REJECTED.getCode(), SubFlagRecordStateEnum.APPROVED.getCode()))
                .set(WuLiuSubFlagRecordEntity::getOperationCheckState, operationState)
                .set(WuLiuSubFlagRecordEntity::getOperationCheckUser, userName)
                .set(WuLiuSubFlagRecordEntity::getOperationCheckTime, LocalDateTime.now())
                .update();
        if(!updated){
            throw new CustomizeException("订单："+subFlagRecord.getSubId()+"复核失败，请确认是否已完成或者已经复核");
        }
        return operationReviewRes;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public DeclarationCallbackRes declarationCallback(DeclarationCallbackReq req) {
        Integer subId = req.getSubId();
        Integer states = req.getStates();
        List<WuLiuSubFlagRecordEntity> list = subFlagRecordService.lambdaQuery().eq(WuLiuSubFlagRecordEntity::getSubId, subId)
                .eq(WuLiuSubFlagRecordEntity::getFlagType, NumberConstant.SEVEN)
                .list();
        if(CollUtil.isEmpty(list) || list.size()!=1){
            throw new CustomizeException("订单："+subId+"回调失败，subFlagRecord表数量查询为："+list.size());
        }
        WuLiuSubFlagRecordEntity wuLiuSubFlagRecordEntity = list.get(NumberConstant.ZERO);
        boolean updated = subFlagRecordService.lambdaUpdate().eq(WuLiuSubFlagRecordEntity::getSubId, subId)
                .eq(WuLiuSubFlagRecordEntity::getFlagType, NumberConstant.SEVEN)
                .eq(WuLiuSubFlagRecordEntity::getCheckState, SubFlagRecordStateEnum.APPROVED.getCode())
                .eq(WuLiuSubFlagRecordEntity::getFinanceCheckState, FinanceCheckStateEnum.APPROVED.getCode())
                .eq(WuLiuSubFlagRecordEntity::getStatus, DeclareStateEnum.NOT_DECLARE.getCode())
                .set(WuLiuSubFlagRecordEntity::getStatus, states)
                .set(WuLiuSubFlagRecordEntity::getStatusComment, req.getStatesComment())
                .update();
        if(!updated){
            throw new CustomizeException("订单：" + subId + "更新失败，请检查订单状态或数据。");
        } else {
            String comment = String.format("国补订单申报状态由%s更改为%s，原因：%s", DeclareStateEnum.getMessageByCode(wuLiuSubFlagRecordEntity.getStatus()),DeclareStateEnum.getMessageByCode(states),req.getStatesComment());
            SubLogsNewReq subLogsNewReq = new SubLogsNewReq();
            subLogsNewReq.setSubId(Convert.toInt(wuLiuSubFlagRecordEntity.getSubId()));
            subLogsNewReq.setType(1);
            subLogsNewReq.setShowType(false);
            subLogsNewReq.setComment(comment);
            subLogsNewReq.setDTime(LocalDateTime.now());
            subLogsNewReq.setInUser(AUTOMATIC_DECLARATION_SYSTEM);
            subLogService.saveSubLog(subLogsNewReq);
        }
        //提交失败的情况
        String statesComment = req.getStatesComment();
        if (DeclareStateEnum.DECLARE_FAIL.getCode().equals(states)) {
            List<String> checkUpdateList = RejectionReasonEnum.getUpdateList(RejectionReasonEnum.CHECK_STATE_UPDATE);
            List<String> financeUpdateList = RejectionReasonEnum.getUpdateList(RejectionReasonEnum.FINANCE_CHECK_STATE_UPDATE);
            if(checkUpdateList.contains(statesComment)){
                //【审核状态】变更为【已拒绝】
                OaUserBO userBO = new OaUserBO();
                ApproveReq approveReq =new ApproveReq();
                userBO.setUserName(AUTOMATIC_DECLARATION_SYSTEM);
                approveReq.setUserBO(userBO);
                approveReq.setId(Convert.toInt(wuLiuSubFlagRecordEntity.getId()));
                approveReq.setCheckComment(statesComment);
                approveReq.setPendingApprove(Boolean.FALSE);
                approveReq.setCheckState(SubFlagRecordStateEnum.REJECTED.getCode());
                updateNationalSupplementState(approveReq);
            }
            if(financeUpdateList.contains(statesComment)){
                //【财务复核状态】变更为【待复核】
                boolean updatedFinance = subFlagRecordService.lambdaUpdate().eq(WuLiuSubFlagRecordEntity::getId, wuLiuSubFlagRecordEntity.getId())
                        .eq(WuLiuSubFlagRecordEntity::getFinanceCheckState, FinanceCheckStateEnum.APPROVED.getCode())
                        .set(WuLiuSubFlagRecordEntity::getFinanceCheckState, FinanceCheckStateEnum.PENDING.getCode())
                        .set(WuLiuSubFlagRecordEntity::getFinanceCheckUser, AUTOMATIC_DECLARATION_SYSTEM)
                        .set(WuLiuSubFlagRecordEntity::getFinanceCheckTime, LocalDateTime.now())
                        .update();
                if(!updatedFinance){
                    throw new CustomizeException("订单："+wuLiuSubFlagRecordEntity.getSubId()+"自动申报财务修改失败");
                }
                String comment = String.format("财务复核状态由%s修改为%s", FinanceCheckStateEnum.APPROVED.getMessage(), FinanceCheckStateEnum.PENDING.getMessage());
                SubLogsNewReq subLogsNewReq = new SubLogsNewReq();
                subLogsNewReq.setSubId(Convert.toInt(wuLiuSubFlagRecordEntity.getSubId()));
                subLogsNewReq.setType(1);
                subLogsNewReq.setShowType(false);
                subLogsNewReq.setComment(comment);
                subLogsNewReq.setDTime(LocalDateTime.now());
                subLogsNewReq.setInUser(AUTOMATIC_DECLARATION_SYSTEM);
                subLogService.saveSubLog(subLogsNewReq);
            }
        }
        return new DeclarationCallbackRes();
    }

    @Override
    public String sysUpdate(SysUpdateReq sysUpdateReq) {
        Integer subId = sysUpdateReq.getSubId();
        Integer auditResult = sysUpdateReq.getAuditResult();
        if(ObjectUtil.isNull(subId)){
            return "subId不能为空";
        }
        if(ObjectUtil.isNull(auditResult)){
            return "审核结果不能为空";
        }
        List<WuLiuSubFlagRecordEntity> list = subFlagRecordService.lambdaQuery().eq(WuLiuSubFlagRecordEntity::getSubId, subId)
                .eq(WuLiuSubFlagRecordEntity::getFlagType, NumberConstant.SEVEN)
                .orderByDesc(WuLiuSubFlagRecordEntity::getId).list();

        if(CollUtil.isEmpty(list)){
            return "该订单没有配置附件";
        }
        WuLiuSubFlagRecordEntity wuLiuSubFlagRecordEntity = list.get(NumberConstant.ZERO);
        ApproveReq approveReq =new ApproveReq();
        if(NumberConstant.ONE.equals(auditResult)){
            //审核成功
            approveReq.setCheckState(SubFlagRecordStateEnum.APPROVED.getCode());
            return "系统自动审核不能修改为已审核";
        } else if(NumberConstant.ZERO.equals(auditResult)){
            //审核失败
            approveReq.setCheckState(SubFlagRecordStateEnum.REJECTED.getCode());
        } else {
            return "审核状态异常："+auditResult;
        }
        try {
            OaUserBO userBO = new OaUserBO();
            userBO.setUserName("系统");
            approveReq.setUserBO(userBO);
            approveReq.setId(Convert.toInt(wuLiuSubFlagRecordEntity.getId()));
            updateNationalSupplementState(approveReq);
        }catch (Exception e){
            RRExceptionHandler.logError("系统审核国补附件异常", sysUpdateReq, e, smsService::sendOaMsgTo9JiMan);
            return e.getMessage();
        }

        return SUCCESSMESSAGE;
    }

    /**
     * 查询订单是否线上国补订单
     *
     * @param subIds
     */
    @DS(DataSourceConstants.CH999_OA_NEW)
    @Override
    public List<NationalSupplementKindRes> getNationalSupplementKindList(List<String> subIds) {
        if (CollUtil.isEmpty(subIds)) {
            return new ArrayList<>();
        }
        return CommonUtils.bigDataInQuery(subIds,ids -> subFlagRecordMapper.getNationalSupplementKindList(ids)) ;
    }

    /**
     * 获取附件
     * @param subIds
     * @return
     */
    private Map<Integer, List<NationalAttachmentInfo>> getAttachmentInfoMap(List<Integer> subIds) {
        if (CollUtil.isEmpty(subIds)) {
            return new HashMap<>();  // 返回空Map而不是null
        }
        List<NationalAttachmentInfo> attachmentInfos = CommonUtils.bigDataInQuery(subIds, subFlagRecordMapper::selectNationalAttachmentInfo);
        if (CollUtil.isEmpty(attachmentInfos)) {
            return new HashMap<>();  // 如果查询结果为空，返回空Map
        }
        return attachmentInfos.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(NationalAttachmentInfo::getSubId, HashMap::new, Collectors.toList()));
    }


    /**
     * 获取串号
     * @param subIds
     * @return
     */
    private Map<Integer, String> getImeiMap(List<Integer> basketIdList) {
        if (CollUtil.isEmpty(basketIdList)) {
            return new HashMap<>();
        }
        List<ProductMkc> productMkcList = productMkcService.lambdaQuery().in(ProductMkc::getBasketId, basketIdList).select(ProductMkc::getBasketId, ProductMkc::getImei).list();

        if (CollUtil.isEmpty(productMkcList)) {
            return new HashMap<>();
        }
        return productMkcList.stream().filter(Objects::nonNull).collect(Collectors.toMap(ProductMkc::getBasketId,ProductMkc::getImei, (n1,n2)->n2));
    }

    /**
     * 根据退货日期查询订单
     *
     * @param req 包含开始时间和结束时间
     * @return 订单号和退货日期列表
     */
    @Override
    @DS(DataSourceConstants.CH999_OA_NEW)
    public List<SubReturnDateRes> querySubsByReturnDate(SubReturnDateReq req) {
        if (req.getStartTime().isAfter(req.getEndTime())) {
            throw new CustomizeException("开始时间不能晚于结束时间");
        }
        return subFlagRecordMapper.querySubsByReturnDate(req);
    }
}
