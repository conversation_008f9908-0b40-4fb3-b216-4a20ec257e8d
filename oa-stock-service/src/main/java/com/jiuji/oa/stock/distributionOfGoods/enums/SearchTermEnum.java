package com.jiuji.oa.stock.distributionOfGoods.enums;

import com.jiuji.oa.nc.abnormal.vo.ShowPrintingEnumVO;
import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/1/12
 * @description 小件品类统计：门店类别
 */
@Getter
@AllArgsConstructor
public enum SearchTermEnum implements CodeMessageEnumInterface {

    /**
     * 门店类别
     */
    SELF_EMPLOYED(1, "sku_id"),
    JOIN(2, "商品ID");

    /**
     * key
     */
    private final Integer code;
    /**
     * value
     */
    private final String message;


    /**
     * 将所有的枚举转换成list
     *
     * @return
     */
    public static List<ShowPrintingEnumVO> getAllPrintingEnum() {
        SearchTermEnum[] array = SearchTermEnum.values();
        List<ShowPrintingEnumVO> arrayList = new ArrayList<>();
        for (SearchTermEnum t : array) {
            ShowPrintingEnumVO showPrintingEnumVO = new ShowPrintingEnumVO()
                    .setLabel(t.getMessage())
                    .setValue(t.getCode());
            arrayList.add(showPrintingEnumVO);
        }
        return arrayList;
    }
}