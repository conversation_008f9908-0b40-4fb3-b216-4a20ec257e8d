package com.jiuji.oa.stock.displayproductinfo.service;

import com.jiuji.oa.stock.displayproductinfo.dto.DisplayProductAddReq;
import com.jiuji.oa.stock.displayproductinfo.entity.DisplayProductInfo;
import com.jiuji.oa.stock.displayproductinfo.vo.req.DisplayProductAddVO;
import com.jiuji.oa.stock.displayproductinfo.vo.req.DisplayProductReqVO;
import com.jiuji.oa.stock.displayproductinfo.vo.req.DisplayProductStatusReqVo;
import com.jiuji.oa.stock.displayproductinfo.vo.res.DisplayProductReqDTO;
import com.jiuji.oa.stock.displayproductinfo.vo.res.DisplayProductStatusResVo;
import java.util.List;

/**
 * <p>
 *
 * @description: IDisplayProductInfoBusService
 * </p>
 * @author: David
 * @create: 2021-03-15 17:11
 */
public interface IDisplayProductInfoBusService {

    /**
     * 获取
     *
     * @param id
     * @return
     */
    DisplayProductInfo getById(Long id);


    /**
     * 通过订单Id或陈列Id查询商品陈列状态
     *
     * @param reqVO
     * @return
     */
    List<DisplayProductReqDTO> getDisplayProductInfo(DisplayProductReqVO reqVO);


    /**
     * 大件优品和小件优品商品库存状态查询接口
     *
     * @param reqVO
     * @return
     */
    DisplayProductStatusResVo verifyStockStatus(DisplayProductStatusReqVo reqVO);


    /**
     * 小件接件转瑕疵添加陈列
     *
     * @param reqVO
     */
    void add(DisplayProductAddVO reqVO);

    /**
     * 小件接件转瑕疵添加陈列
     *
     * @param reqVO
     */
    DisplayProductAddReq addV2(DisplayProductAddVO reqVO);

    /**
     * 获取陈列商品的审核日期
     *
     * @param reqVO
     * @return
     */
    String getCheckTimeV1(DisplayProductStatusReqVo reqVO);
}