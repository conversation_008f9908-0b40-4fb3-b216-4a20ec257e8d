package com.jiuji.oa.stock.nationalSupplement.res;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class NationalAttachmentCount {

    /**
     * 国补订单合计数量
     */
    private Integer allCount;
    /**
     * 审核通过数量
     */
    private Integer pendingCount;
    /**
     * 待审核数量
     */
    private Integer approvedCount;
    /**
     * 已拒绝数量
     */
    private Integer rejectedCount;
    /**
     * 待审核数量
     */
    private Integer notDeclareCount;
    /**
     * 已审核数量
     */
    private Integer declareCount;
    /**
     * 提交失败数量
     */
    private Integer declareFailCount;


}
