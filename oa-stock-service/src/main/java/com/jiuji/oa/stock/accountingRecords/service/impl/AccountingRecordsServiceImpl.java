package com.jiuji.oa.stock.accountingRecords.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.BeanUtils;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ch999.common.util.tenant.Namespaces;
import com.jiuji.oa.apollo.WuliuApolloConfig;
import com.jiuji.oa.baozun.common.util.StringUtils;
import com.jiuji.oa.logapi.pojo.dto.req.MkcLogNewReq;
import com.jiuji.oa.logapi.service.IMkcLogNewService;
import com.jiuji.oa.loginfo.order.service.SubLogsCloud;
import com.jiuji.oa.loginfo.order.vo.req.SubLogsNewReq;
import com.jiuji.oa.nc.channel.entity.Ok3wQudao;
import com.jiuji.oa.nc.channel.service.Ok3wQudaoService;
import com.jiuji.oa.nc.common.bo.OaUserBO;
import com.jiuji.oa.nc.common.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.nc.common.exception.CustomizeException;
import com.jiuji.oa.nc.common.exception.RRExceptionHandler;
import com.jiuji.oa.nc.dict.service.ISysConfigService;
import com.jiuji.oa.nc.product.entity.ProductInfoEntity;
import com.jiuji.oa.nc.product.service.IProductInfoService;
import com.jiuji.oa.nc.product.vo.req.ProductInfoVo;
import com.jiuji.oa.nc.stock.entity.ProductMkc;
import com.jiuji.oa.nc.stock.service.CategoryService;
import com.jiuji.oa.nc.stock.service.IProductMkcService;
import com.jiuji.oa.nc.stock.service.ISmsService;
import com.jiuji.oa.nc.user.po.Areainfo;
import com.jiuji.oa.nc.user.po.Ch999User;
import com.jiuji.oa.nc.user.service.Ch999UserService;
import com.jiuji.oa.nc.user.service.IAreaInfoService;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.oa.stock.accountingRecords.entity.AccountingRecords;
import com.jiuji.oa.stock.accountingRecords.enums.AccountingStateEnum;
import com.jiuji.oa.stock.accountingRecords.enums.SelectTypeEnum;
import com.jiuji.oa.stock.accountingRecords.mapper.AccountingRecordsMapper;
import com.jiuji.oa.stock.accountingRecords.service.AccountingRecordsService;
import com.jiuji.oa.stock.accountingRecords.service.AreaGuestbookAccountService;
import com.jiuji.oa.stock.accountingRecords.vo.req.*;
import com.jiuji.oa.stock.accountingRecords.vo.res.AccountingPageRes;
import com.jiuji.oa.stock.accountingRecords.vo.res.HandleBusinessRes;
import com.jiuji.oa.stock.accountingRecords.vo.res.SaveInfoRes;
import com.jiuji.oa.stock.accountingRecords.vo.res.StepInfoRes;
import com.jiuji.oa.stock.common.util.ImeiUtils;
import com.jiuji.oa.stock.common.util.StockUtils;
import com.jiuji.oa.stock.common.vo.AppleDeviceInfo;
import com.jiuji.oa.stock.common.vo.AppleInfoPrintVo;
import com.jiuji.oa.stock.common.vo.GeneralPrintVO;
import com.jiuji.oa.stock.inventory.vo.PictureInfo;
import com.jiuji.oa.stock.publiccheck.service.InteractiveLogService;
import com.jiuji.oa.stock.purchase.entity.ApplePurchaseStoreInventory;
import com.jiuji.oa.stock.purchase.service.ApplePurchaseStoreInventoryService;
import com.jiuji.oa.stock.sub.entity.Basket;
import com.jiuji.oa.stock.sub.entity.Sub;
import com.jiuji.oa.stock.sub.entity.SubSubCheckInfo;
import com.jiuji.oa.stock.sub.service.IBasketService;
import com.jiuji.oa.stock.sub.service.ISubService;
import com.jiuji.oa.wuliu.enums.MsgTypeEnum;
import com.jiuji.oa.wuliu.enums.SubCheckEnum;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.foundation.message.send.service.MessageSendService;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@DS("oanewWrite")
public class AccountingRecordsServiceImpl extends ServiceImpl<AccountingRecordsMapper, AccountingRecords> implements AccountingRecordsService {

    @Resource
    private AbstractCurrentRequestComponent component;
    @Resource
    private IBasketService basketService;
    @Resource
    private SubLogsCloud subLogsCloud;
    @Resource
    private ISubService subService;
    @Resource
    private IProductMkcService mkcService;
    @Resource
    private IProductInfoService productInfoService;
    @Resource
    private SysConfigClient sysConfigClient;
    @Resource
    private Ok3wQudaoService qudaoService;
    @Resource
    private Ch999UserService ch999UserService;
    @Resource
    private IMkcLogNewService mkcLogNewService;

    @Resource
    private InteractiveLogService logService;
    @Resource
    private WuliuApolloConfig wuliuApolloConfig;
    @Resource
    private CategoryService categoryService;
    @Resource
    private ISysConfigService sysConfigService;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private ApplePurchaseStoreInventoryService applePurchaseStoreInventoryService;

    @Resource
    private MessageSendService messageSendService;

    @Resource
    private AreaGuestbookAccountService areaGuestbookAccountService;

    /**
     * 下账分类
     */
    private static final List<Integer> ACCOUNTING_RECORDS_CID_LIST= Arrays.asList(2,20,21,22,99,100,113,144,316,466,403,194);
    private static final Integer MSC_AREA_ID=342;

 

    @Resource
    private IAreaInfoService areaInfoService;
    private static final String ACCOUNTING_TYPE="storage";
    private static final String ACCOUNTING_PLAIN_TEXT_ONE ="设备下账报量";
    private static final String ACCOUNTING_PLAIN_TEXT_TWO = "设备已下账";
    //private static final Integer ACCOUNTING_SEND_COUNT_MAX = 50;
    private static final Integer ACCOUNTING_HANDLE_TIME_OUT = 30;
    private static final Integer HUAWEI_BRAND_ID = 7;
    /**
     * 数据预处理
     * @param basketId
     * @return
     */
    private void preHandleData(List<Integer> basketIds,Integer mkcId,Integer cancelUserId){
        Integer basketId = basketIds.get(NumberConstant.ZERO);
        List<AccountingRecords> accountingRecords = this.lambdaQuery()
                .eq(AccountingRecords::getMkcId, mkcId)
                .ne(AccountingRecords::getAccountingState,AccountingStateEnum.DELETED.getCode())
                .list();
        //如果传入basketId的数据和数据库basketId不对应  那就直接删除原来数据库的数据
        accountingRecords.forEach(item->{
            if(!basketId.equals(item.getBasketId())){
                boolean update = this.lambdaUpdate().eq(AccountingRecords::getId, item.getId())
                        .set(AccountingRecords::getUpdateTime, LocalDateTime.now())
                        .set(AccountingRecords::getCancelUserId, cancelUserId)
                        .set(AccountingRecords::getAccountingCancelTime, LocalDateTime.now())
                        .set(AccountingRecords::getAccountingState, AccountingStateEnum.DELETED.getCode())
                        .update();
                String message = String.format("相同mkcId：%s，传入basketId：%s，数据库原有basketId：%s，删除下账记录id：%s" , mkcId, basketId,item.getBasketId(), item.getId());
                log.warn(message);
                if(!update){
                    throw new CustomizeException(message+"失败");
                }
            } 
        });
    }
    @Transactional
    @Override
    public R<String> accountingPush(AccountingPushReq approvalPushReq) {
        Integer receiveUserId = approvalPushReq.getReceiveUserId();
        if(ObjectUtil.isNull(receiveUserId)|| receiveUserId==NumberConstant.ZERO){
            return R.error("接收人不能为0或者空");
        }

        List<Integer> basketIdList = approvalPushReq.getBasketId();
        if(CollectionUtil.isEmpty(basketIdList)){
            throw new CustomizeException("basketId不能为空");
        }
        //数据预处理
        preHandleData(approvalPushReq.getBasketId(),approvalPushReq.getMkcId(),approvalPushReq.getSendUserId());
        //获取基础信息
        HandleBusinessRes handleBusinessRes = getHandleBusinessRes(approvalPushReq.getBasketId(),approvalPushReq.getMkcId());
        Sub sub = handleBusinessRes.getSub();
        Map<Integer, Basket> basketMap = handleBusinessRes.getBasketMap();
        Map<Integer, ProductMkc> productMkcMap = handleBusinessRes.getProductMkcMap();
        Map<Long, ProductInfoVo> productInfoMap = handleBusinessRes.getProductInfoEntityMap();
        List<AccountingRecords> accountingRecordsList = new ArrayList<>();
        List<SubLogsNewReq> subLogsList = new ArrayList<>();
        //获取发送人姓名
        AtomicReference<String> inUser = new AtomicReference<>("");

        Integer sendUserId = Optional.ofNullable(approvalPushReq.getSendUserId()).orElse(NumberConstant.ZERO);
        if(NumberConstant.ZERO.equals(sendUserId)){
            inUser.getAndSet("系统");
        } else {
            inUser.getAndSet(Optional.ofNullable(ch999UserService.getUserByCh999Id(sendUserId)).orElse(new Ch999User()).getCh999Name());
        }
        basketIdList.forEach(item->{
            SaveInfoRes saveInfoRes = new SaveInfoRes();
            saveInfoRes.setBasketId(item)
                    .setTriggerNode(approvalPushReq.getTriggerNode())
                    .setReceiveUserId(approvalPushReq.getReceiveUserId())
                    .setSendUserId(approvalPushReq.getSendUserId());
            //数据库保存
            ProductMkc productMkc = productMkcMap.getOrDefault(item, new ProductMkc());
            AccountingRecords records = saveOrUpdateAccountingRecords(saveInfoRes,productMkc,sub,basketMap.getOrDefault(item,new Basket()));
            accountingRecordsList.add(records);
            //封装订单日志
            String ch999Name = Optional.ofNullable(ch999UserService.getUserByCh999Id(records.getReceiveUserId())).orElse(new Ch999User()).getCh999Name();
            ProductInfoVo productInfoVo = productInfoMap.getOrDefault(records.getPpid().longValue(), new ProductInfoVo());
            String message = String.format("%s mkcId：%s 需要进行下账处理 才能进行出库操作，已和 %s 建立内部聊天"
                    ,Optional.ofNullable(productInfoVo.getProductName()).orElse("")+productInfoVo.getProductColor(),productMkc.getId(),ch999Name);
            SubLogsNewReq subLogsNewReq = new SubLogsNewReq();
            subLogsNewReq.setComment(message);
            Integer subId = Optional.ofNullable(basketMap.getOrDefault(records.getBasketId(), new Basket()).getSubId()).orElseThrow(() -> new CustomizeException("basketId:" + records.getBasketId() + "订单为空")).intValue();
            subLogsNewReq.setSubId(subId);
            subLogsNewReq.setShowType(Boolean.FALSE);
            subLogsNewReq.setType(NumberConstant.ONE);
            subLogsNewReq.setDTime(LocalDateTime.now());
            subLogsNewReq.setInUser(inUser.get());
            subLogsList.add(subLogsNewReq);
        });
        // accountingRecordsList根据basketId收集成为map
        Map<Integer, AccountingRecords> accountingRecordsMap = accountingRecordsList.stream().collect(Collectors.toMap(AccountingRecords::getBasketId, Function.identity(),(n1,n2)->n2));
        List<Integer> receiveUserIds = accountingRecordsList.stream().map(AccountingRecords::getReceiveUserId).distinct().collect(Collectors.toList());
        //参数封装
        CallImServiceReq callImServiceReq = new CallImServiceReq();
        callImServiceReq.setFromOa(true)
                .setSubTenant(Convert.toInt(Namespaces.get()))
                .setSendUserId(approvalPushReq.getSendUserId())
                .setReceiveUserIds(receiveUserIds);
        CallInfoReq callInfoReq = new CallInfoReq();
        callInfoReq.setType(ACCOUNTING_TYPE)
                .setCardContent(createCardContent(handleBusinessRes,accountingRecordsMap))
                .setPlainText(ACCOUNTING_PLAIN_TEXT_ONE);
        callImServiceReq.setMessageBody(callInfoReq);
        //调用发送消息的服务
        R<String> stringR = logService.callImService(callImServiceReq);
        if(!stringR.isSuccess()){
            throw new CustomizeException("调用im服务异常："+ Optional.ofNullable(stringR.getMsg()).orElse(stringR.getUserMsg()));
        }
        //日志记录
        subLogsCloud.addSubLogBatch(subLogsList);
        return stringR;
    }

    public List<CallInfoDetailsReq> createCardContent(HandleBusinessRes handleBusinessRes,Map<Integer, AccountingRecords> accountingRecordsMap){
        List<CallInfoDetailsReq> list = new ArrayList<>();
        Sub sub = handleBusinessRes.getSub();
        String area = Optional.ofNullable(areaInfoService.getAreaById(sub.getAreaid())).orElseThrow(() -> new CustomizeException("下单门店查询为空"));
        Map<Integer, Basket> basketMap = handleBusinessRes.getBasketMap();
        Map<Integer, ProductMkc> productMkcMap = handleBusinessRes.getProductMkcMap();
        Map<Integer, PictureInfo> pictureInfoMap = handleBusinessRes.getPictureInfoMap();
        Map<Long, ProductInfoVo> productInfoEntityMap = handleBusinessRes.getProductInfoEntityMap();
        List<Basket> basketList = basketMap.values().stream().collect(Collectors.toList());
        basketList.forEach(item->{
            CallInfoDetailsReq detailsReq = new CallInfoDetailsReq();
            ProductInfoVo productInfo = productInfoEntityMap.getOrDefault(item.getPpriceid(), new ProductInfoVo());
            ProductMkc productMkc = productMkcMap.getOrDefault(item.getBasketId(), new ProductMkc());
            PictureInfo pictureInfo = pictureInfoMap.getOrDefault(item.getPpriceid().intValue(), new PictureInfo());
            AccountingRecords accountingRecords = accountingRecordsMap.getOrDefault(item.getBasketId(), new AccountingRecords());
            detailsReq.setArea(area)
                    .setTitle(Optional.ofNullable(productInfo.getProductName()).orElse("")+productInfo.getProductColor())
                    .setImei(productMkc.getImei())
                    .setMkcId(productMkc.getId())
                    .setType(ACCOUNTING_TYPE)
                    .setPoster(pictureInfo.getProductImageUrl())
                    //获取图片logo
                    .setLink(getMoaUrl()+"/new/#/logistics/peripherals?userid="+accountingRecords.getReceiveUserId());
            list.add(detailsReq);
        });
        return list;
    }

    public List<CallInfoDetailsReq> createCardContentV2(HandleBusinessRes handleBusinessRes,Map<Integer, List<AccountingRecords>> accountingRecordsMap){
        List<CallInfoDetailsReq> list = new ArrayList<>();
        Sub sub = handleBusinessRes.getSub();
        String area = Optional.ofNullable(areaInfoService.getAreaById(sub.getAreaid())).orElseThrow(() -> new CustomizeException("下单门店查询为空"));
        Map<Integer, Basket> basketMap = handleBusinessRes.getBasketMap();
        Map<Integer, ProductMkc> productMkcMap = handleBusinessRes.getProductMkcMap();
        Map<Integer, PictureInfo> pictureInfoMap = handleBusinessRes.getPictureInfoMap();
        Map<Long, ProductInfoVo> productInfoEntityMap = handleBusinessRes.getProductInfoEntityMap();
        List<Basket> basketList = basketMap.values().stream().collect(Collectors.toList());
        String moaUrl = getMoaUrl();
        basketList.forEach(item->{
            ProductInfoVo productInfo = productInfoEntityMap.getOrDefault(item.getPpriceid(), new ProductInfoVo());
            PictureInfo pictureInfo = pictureInfoMap.getOrDefault(item.getPpriceid().intValue(), new PictureInfo());
            List<AccountingRecords> accountingRecords = accountingRecordsMap.getOrDefault(item.getBasketId(), new ArrayList<>());
            accountingRecords.forEach(accountingRecord -> {
                ProductMkc productMkc = productMkcMap.getOrDefault(accountingRecord.getMkcId(), new ProductMkc());
                String linkUrl = "";
                if (Objects.equals(1, accountingRecord.getRecordsType())) {
                    linkUrl = StrUtil.format("{}/new/#/logistics/applePeripherals?userid={}", moaUrl, accountingRecord.getReceiveUserId());
                } else {
                    linkUrl = StrUtil.format("{}/new/#/logistics/peripherals?userid={}", moaUrl, accountingRecord.getReceiveUserId());
                }

                CallInfoDetailsReq detailsReq = new CallInfoDetailsReq();
                detailsReq.setArea(area)
                        .setTitle(Optional.ofNullable(productInfo.getProductName()).orElse("")+productInfo.getProductColor())
                        .setImei(productMkc.getImei())
                        .setMkcId(productMkc.getId())
                        .setType(ACCOUNTING_TYPE)
                        .setPoster(pictureInfo.getProductImageUrl())
                        //获取图片logo
                        .setLink(linkUrl);
                list.add(detailsReq);
            });
        });
        return list;
    }

    /**
     * 获取基础信息
     * @param basketId
     * @return
     */
    private HandleBusinessRes getHandleBusinessRes(List<Integer> basketId,Integer mkcId){
        HandleBusinessRes handleBusinessRes = new HandleBusinessRes();
        List<Basket> basketList = CommonUtils.bigDataInQuery(basketId, ids->basketService.lambdaQuery().in(Basket::getBasketId, ids).last("and ISNULL(isdel,0)=0").list());
        if(CollectionUtil.isEmpty(basketList)){
            throw new CustomizeException("basket查询为空");
        }
        Sub sub = Optional.ofNullable(subService.getById(basketList.get(NumberConstant.ZERO).getSubId())).orElseThrow(() -> new CustomizeException("订单信息查询为空"));
        List<ProductMkc> productMkcList = new ArrayList<>();
        if(ObjectUtil.isNotNull(mkcId)){
            productMkcList.add(mkcService.getById(mkcId));
        } else {
            productMkcList.addAll(CommonUtils.bigDataInQuery(basketId, ids->mkcService.lambdaQuery().in(ProductMkc::getBasketId, ids).list()));
        }
        if(CollectionUtil.isEmpty(productMkcList)){
            throw new CustomizeException("productMkc查询为空");
        }
        Map<Long, ProductInfoVo> longProductInfoVoMap = productInfoService.listByppidsNew(basketList.stream().map(Basket::getPpriceid).collect(Collectors.toList()));
        List<ProductInfoVo> collect = longProductInfoVoMap.values().stream().collect(Collectors.toList());
        //获取支持下账的分类
        Set<Integer> cidSet = categoryService.selectChildrenCategory(ACCOUNTING_RECORDS_CID_LIST);
        //商品分类为智能手机
        collect.forEach(item->{
            Long cid = Optional.ofNullable(item.getCid()).orElse(0L);
            if(!cidSet.contains(cid.intValue())){
                throw new CustomizeException("商品"+item.getProductName()+"不是符合下账分类");
            }
        });
        Map<Integer, PictureInfo> pictureInfoMap = productInfoService.selectPictureByEntityMap(collect);
        handleBusinessRes.setSub(sub)
                .setBasketMap(basketList.stream().collect(Collectors.toMap(Basket::getBasketId, Function.identity(),(n1,n2)->n2)))
                .setProductInfoEntityMap(longProductInfoVoMap)
                .setPictureInfoMap(pictureInfoMap)
                .setProductMkcMap(productMkcList.stream().collect(Collectors.toMap(ProductMkc::getBasketId, Function.identity(),(n1,n2)->n2)));
        return handleBusinessRes;
    }
    /**
     * 获取基础信息
     * @param basketId
     * @return
     */
    private HandleBusinessRes getHandleBusinessResV2(List<Integer> basketId){
        HandleBusinessRes handleBusinessRes = new HandleBusinessRes();
        List<Basket> basketList = CommonUtils.bigDataInQuery(basketId, ids->basketService.lambdaQuery().in(Basket::getBasketId, ids).last("and ISNULL(isdel,0)=0").list());
        if(CollectionUtil.isEmpty(basketList)){
            throw new CustomizeException("basket查询为空");
        }
        Sub sub = Optional.ofNullable(subService.getById(basketList.get(NumberConstant.ZERO).getSubId())).orElseThrow(() -> new CustomizeException("订单信息查询为空"));
        List<ProductMkc> productMkcList = CommonUtils.bigDataInQuery(basketId, ids->mkcService.lambdaQuery().in(ProductMkc::getBasketId, ids).list());
        if(CollectionUtil.isEmpty(productMkcList)){
            throw new CustomizeException("productMkc查询为空");
        }
        Map<Long, ProductInfoVo> longProductInfoVoMap = productInfoService.listByppidsNew(basketList.stream().map(Basket::getPpriceid).collect(Collectors.toList()));
        List<ProductInfoVo> collect = longProductInfoVoMap.values().stream().collect(Collectors.toList());
        //获取支持下账的分类
        Set<Integer> cidSet = categoryService.selectChildrenCategory(ACCOUNTING_RECORDS_CID_LIST);
        //商品分类为智能手机
        collect.forEach(item->{
            Long cid = Optional.ofNullable(item.getCid()).orElse(0L);
            if(!cidSet.contains(cid.intValue())){
                throw new CustomizeException("商品"+item.getProductName()+"不是符合下账分类");
            }
        });
        Map<Integer, PictureInfo> pictureInfoMap = productInfoService.selectPictureByEntityMap(collect);
        handleBusinessRes.setSub(sub)
                .setBasketMap(basketList.stream().collect(Collectors.toMap(Basket::getBasketId, Function.identity(),(n1,n2)->n2)))
                .setProductInfoEntityMap(longProductInfoVoMap)
                .setPictureInfoMap(pictureInfoMap)
                .setProductMkcMap(productMkcList.stream().collect(Collectors.toMap(ProductMkc::getId, Function.identity(),(n1,n2)->n2)));
        return handleBusinessRes;
    }

    /**
     * 获取基础信息
     * @param approvalPushReq
     * @return
     */
    private HandleBusinessRes getHandleBusinessRes(AccountingPushReq approvalPushReq){
        HandleBusinessRes handleBusinessRes = new HandleBusinessRes();
        List<Integer> basketId = approvalPushReq.getBasketId();
        List<Basket> basketList = CommonUtils.bigDataInQuery(basketId, ids->basketService.lambdaQuery().in(Basket::getBasketId, ids).last("and ISNULL(isdel,0)=0").list());
        if(CollectionUtil.isEmpty(basketList)){
            throw new CustomizeException("basket查询为空");
        }
        Sub sub = Optional.ofNullable(subService.getById(basketList.get(NumberConstant.ZERO).getSubId())).orElseThrow(() -> new CustomizeException("订单信息查询为空"));
        List<ProductMkc> productMkcList = CommonUtils.bigDataInQuery(basketId, ids->mkcService.lambdaQuery().in(ProductMkc::getBasketId, ids).eq(ProductMkc::getId,approvalPushReq.getMkcId()).list());
        if(CollectionUtil.isEmpty(productMkcList)){
            throw new CustomizeException("productMkc查询为空");
        }
        Map<Long, ProductInfoVo> longProductInfoVoMap = productInfoService.listByppidsNew(basketList.stream().map(Basket::getPpriceid).collect(Collectors.toList()));
        List<ProductInfoVo> collect = longProductInfoVoMap.values().stream().collect(Collectors.toList());
        //获取支持下账的分类
        Set<Integer> cidSet = categoryService.selectChildrenCategory(ACCOUNTING_RECORDS_CID_LIST);
        //商品分类为智能手机
        collect.forEach(item->{
            Long cid = Optional.ofNullable(item.getCid()).orElse(0L);
            if(!cidSet.contains(cid.intValue())){
                throw new CustomizeException("商品"+item.getProductName()+"不是符合下账分类");
            }
        });
        Map<Integer, PictureInfo> pictureInfoMap = productInfoService.selectPictureByEntityMap(collect);
        handleBusinessRes.setSub(sub)
                .setBasketMap(basketList.stream().collect(Collectors.toMap(Basket::getBasketId, Function.identity(),(n1,n2)->n2)))
                .setProductInfoEntityMap(longProductInfoVoMap)
                .setPictureInfoMap(pictureInfoMap)
                .setProductMkcMap(productMkcList.stream().collect(Collectors.toMap(ProductMkc::getId, Function.identity(),(n1,n2)->n2)));
        return handleBusinessRes;
    }

    @Transactional(rollbackFor = Exception.class)
    public AccountingRecords saveOrUpdateAccountingRecords(SaveInfoRes approvalPushReq, ProductMkc productMkc, Sub sub, Basket basket){
        Integer basketId = approvalPushReq.getBasketId();
        List<AccountingRecords> accountingRecords = this.lambdaQuery().eq(AccountingRecords::getBasketId, basketId)
                .eq(AccountingRecords::getMkcId, productMkc.getId())
                .ne(AccountingRecords::getAccountingState,AccountingStateEnum.DELETED.getCode())
                .list();
        AccountingRecords records = new AccountingRecords();
        if(CollectionUtil.isNotEmpty(accountingRecords)){
            //判断 accountingRecords 是否已经存在以下账的数据
            Optional<AccountingRecords> any = accountingRecords.stream().filter(item -> AccountingStateEnum.POSTED.getCode().equals(item.getAccountingState())).findAny();
            if(any.isPresent()){
                throw new CustomizeException("该订单已经下账，请勿重复下账");
            }
            if(accountingRecords.size() > NumberConstant.ONE){
                throw new CustomizeException("下账订单数量异常");
            }
            records = accountingRecords.get(NumberConstant.ZERO);
            Integer sendCount = Optional.ofNullable(records.getSendCount()).orElse(NumberConstant.ZERO);
            if(sendCount>wuliuApolloConfig.getAccountingSendCountMax()){
                throw new CustomizeException("basketId:"+records.getBasketId()+"推送次数过多，请联系管理员");
            }
            records.setSendCount(sendCount+NumberConstant.ONE)
                    .setSendUserId(approvalPushReq.getSendUserId())
                    .setUpdateTime(LocalDateTime.now());

        } else {

            //获取下账门店
            Integer accountingAreaId = 0;
            if (Objects.equals(1, approvalPushReq.getRecordsType())) {
                ApplePurchaseStoreInventory applePurchase = Optional.ofNullable(applePurchaseStoreInventoryService.getApplePurchaseByMkcId(productMkc.getId())).orElseThrow(() -> new CustomizeException("mkcId:" + productMkc.getId() + "未查询到采购信息"));
                Areainfo areainfo = Optional.ofNullable(areaInfoService.getById(applePurchase.getAreaId())).orElseThrow(() -> new CustomizeException("苹果采购门店信息为空"));
                accountingAreaId = areainfo.getId();
            } else {
                Integer insourceid = Optional.ofNullable(productMkc.getInsourceid2()).orElseThrow(() ->new CustomizeException("采购渠道查询异常mkcId:"+productMkc.getId()));
                Ok3wQudao ok3wQudao = Optional.ofNullable(qudaoService.getById(insourceid)).orElseThrow(() -> new CustomizeException("渠道查询为空"));
                accountingAreaId = Optional.ofNullable(ok3wQudao.getBindareaid()).orElseThrow(() -> new CustomizeException("渠道：" + ok3wQudao.getId() + "绑定地区为空无须下账"));
            }
            records.setPpid(basket.getPpriceid().intValue())
                    .setMkcId(productMkc.getId())
                    .setTriggerNode(approvalPushReq.getTriggerNode())
                    .setBasketId(approvalPushReq.getBasketId())
                    .setTransferTime(LocalDateTime.now())
                    .setSendCount(NumberConstant.ONE)
                    .setUpdateTime(LocalDateTime.now())
                    .setSendUserId(approvalPushReq.getSendUserId())
                    .setAccountingState(AccountingStateEnum.PENDING_PROCESSING.getCode())
                    .setReceiveUserId(approvalPushReq.getReceiveUserId())
                    .setRecordsType(approvalPushReq.getRecordsType())
                    .setAccountingAreaId(accountingAreaId);
        }
        boolean flag = this.saveOrUpdate(records);
        if(flag){
            return records;
        }
        throw new CustomizeException("下账数据保存失败");
    }



    @Transactional(rollbackFor = Exception.class)
    public AccountingRecords saveOrUpdateAccountingRecordsNew(SaveInfoRes approvalPushReq, ProductMkc productMkc, Sub sub, Basket basket){
        Integer basketId = approvalPushReq.getBasketId();
        List<AccountingRecords> accountingRecords = this.lambdaQuery()
                .eq(AccountingRecords::getMkcId, productMkc.getId())
                .ne(AccountingRecords::getAccountingState,AccountingStateEnum.DELETED.getCode())
                .list();
        AccountingRecords records = new AccountingRecords();
        if(CollectionUtil.isNotEmpty(accountingRecords)){
            //判断 accountingRecords 是否已经存在以下账的数据
            Optional<AccountingRecords> any = accountingRecords.stream().filter(item -> AccountingStateEnum.POSTED.getCode().equals(item.getAccountingState())).findAny();
            if(any.isPresent()){
                throw new CustomizeException("该订单已经下账，请勿重复下账");
            }
            if(accountingRecords.size() > NumberConstant.ONE){
                throw new CustomizeException("下账订单数量异常");
            }
            records = accountingRecords.get(NumberConstant.ZERO);
            Integer sendCount = Optional.ofNullable(records.getSendCount()).orElse(NumberConstant.ZERO);
            if(sendCount>wuliuApolloConfig.getAccountingSendCountMax()){
                throw new CustomizeException("mkcId:"+records.getMkcId()+"推送次数过多，请联系管理员");
            }
            //接收人更新
            String receiveUserIdStr = records.getReceiveUserIdStr();
            List<String> receiveUserIdStrListNew = Arrays.asList(approvalPushReq.getReceiveUserIdStr().split(",")).stream().collect(Collectors.toList());
            if(StrUtil.isNotBlank(receiveUserIdStr)){
                List<String> receiveUserIdStrListOld = Arrays.asList(receiveUserIdStr.split(",")).stream().collect(Collectors.toList());
                receiveUserIdStrListOld.addAll(receiveUserIdStrListNew);
                records.setReceiveUserIdStr(receiveUserIdStrListOld.stream().distinct().collect(Collectors.joining(",")));
            } else {
                records.setReceiveUserIdStr(receiveUserIdStrListNew.stream().distinct().collect(Collectors.joining(",")));
            }
            records.setSendCount(sendCount+NumberConstant.ONE)
                    .setSendUserId(approvalPushReq.getSendUserId())
                    .setUpdateTime(LocalDateTime.now());

        } else {

            //获取下账门店
            Integer accountingAreaId = 0;
            if (Objects.equals(1, approvalPushReq.getRecordsType())) {
                ApplePurchaseStoreInventory applePurchase = Optional.ofNullable(applePurchaseStoreInventoryService.getApplePurchaseByMkcId(productMkc.getId())).orElseThrow(() -> new CustomizeException("mkcId:" + productMkc.getId() + "未查询到采购信息"));
                Areainfo areainfo = Optional.ofNullable(areaInfoService.getById(applePurchase.getAreaId())).orElseThrow(() -> new CustomizeException("苹果采购门店信息为空"));
                accountingAreaId = areainfo.getId();
            } else {
                Integer insourceid = Optional.ofNullable(productMkc.getInsourceid2()).orElseThrow(() ->new CustomizeException("采购渠道查询异常mkcId:"+productMkc.getId()));
                Ok3wQudao ok3wQudao = Optional.ofNullable(qudaoService.getById(insourceid)).orElseThrow(() -> new CustomizeException("渠道查询为空"));
                accountingAreaId = Optional.ofNullable(ok3wQudao.getBindareaid()).orElseThrow(() -> new CustomizeException("渠道：" + ok3wQudao.getId() + "绑定地区为空无须下账"));
            }
            records.setPpid(basket.getPpriceid().intValue())
                    .setMkcId(productMkc.getId())
                    .setTriggerNode(approvalPushReq.getTriggerNode())
                    .setBasketId(approvalPushReq.getBasketId())
                    .setTransferTime(LocalDateTime.now())
                    .setSendCount(NumberConstant.ONE)
                    .setUpdateTime(LocalDateTime.now())
                    .setReceiveUserIdStr(approvalPushReq.getReceiveUserIdStr())
                    .setSendUserId(approvalPushReq.getSendUserId())
                    .setAccountingState(AccountingStateEnum.PENDING_PROCESSING.getCode())
                    .setReceiveUserId(approvalPushReq.getReceiveUserId())
                    .setRecordsType(approvalPushReq.getRecordsType())
                    .setAccountingAreaId(accountingAreaId);
        }
        boolean flag = this.saveOrUpdate(records);
        if(flag){
            return records;
        }
        throw new CustomizeException("下账数据保存失败");
    }

    @Override
    public IPage<AccountingPageRes> accountingPage(AccountingPageReq pageReq) {
        OaUserBO oaUserBO = Optional.ofNullable(component.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("当前登录人失效"));
        List<String> rank = oaUserBO.getRank();
        Page<AccountingPageRes> page = new Page<>(pageReq.getCurrent(), pageReq.getSize());
        //数据校验
        this.checkReq(pageReq);
        //数据处理
        this.handleReq(pageReq);
        //分页查询
        page = this.baseMapper.accountingPage(page, pageReq);
        List<AccountingPageRes> records = page.getRecords();
        if(CollectionUtil.isNotEmpty(records)){
            //门店id收集
            List<Integer> areaIdList = new ArrayList<>();
            List<Integer> userIdList = new ArrayList<>();
            records.forEach(item -> {
                areaIdList.add(item.getAccountingAreaId());
                areaIdList.add(item.getSaleAreaId());
                userIdList.add(item.getAccountingUserId());
                String receiveUserIdStr = item.getReceiveUserIdStr();
                if(StrUtil.isNotEmpty(receiveUserIdStr)){
                    userIdList.addAll(Arrays.asList(receiveUserIdStr.split(",")).stream().filter(NumberUtil::isNumber).map(Convert::toInt).collect(Collectors.toList()));
                }
            });
            Map<Integer, Ch999User> userByCh999IdMap = ch999UserService.getUserByCh999IdMap(userIdList);
            Map<Integer, Areainfo> areaMap = areaInfoService.getAreaMapByIdsNew(areaIdList);
            records.forEach(item->{
                //门店转换
                item.setAccountingArea(areaMap.getOrDefault(item.getAccountingAreaId(),new Areainfo()).getArea());
                item.setSaleArea(areaMap.getOrDefault(item.getSaleAreaId(),new Areainfo()).getArea());
                //人名转换
                String receiveUserIdStr = item.getReceiveUserIdStr();
                if(StrUtil.isNotEmpty(receiveUserIdStr)){
                    List<Integer> collect = Arrays.asList(receiveUserIdStr.split(",")).stream().filter(NumberUtil::isNumber).map(Convert::toInt).collect(Collectors.toList());
                    item.setAccountingUserIdList(collect);
                    item.setAccountingUser(collect.stream().map(userId->userByCh999IdMap.getOrDefault(userId,new Ch999User()).getCh999Name()).collect(Collectors.joining(",")));
                } else {
                    item.setAccountingUser(userByCh999IdMap.getOrDefault(item.getAccountingUserId(),new Ch999User()).getCh999Name());
                }
                //分钟转成小时
                Optional.ofNullable(item.getHandleTimeout()).ifPresent(obj->{
                    BigDecimal dividend = new BigDecimal(item.getHandleTimeout());
                    BigDecimal divisor = new BigDecimal(NumberConstant.SIXTY);
                    BigDecimal resultBigDecimal = dividend.divide(divisor, RoundingMode.HALF_UP).setScale(0, RoundingMode.HALF_UP); // 四舍五入
                    item.setHandleTimeout(resultBigDecimal.intValue());
                });
                //订单状态转换
                item.setSubCheckValue(SubCheckEnum.getMessage(item.getSubCheck()));
                //判断如果是苹果的情况 并且 状态为待处理
                if(Objects.equals(1, item.getRecordsType()) && Objects.equals(AccountingStateEnum.PENDING_PROCESSING.getCode(), item.getAccountingState())){
                    handleStepInfo(item);
                }
                //处理状态转换
                item.setAccountingStateValue(AccountingStateEnum.getMessage(item.getAccountingState(), item.getRecordsType()));
                //串号脱敏
                Boolean desensitize = StockUtils.isDesensitize(item.getDesensitizeFlag(), item.getDesensitizeFlagBrandId(), item.getDesensitizeFlagProvinceIdList(), item.getProductMkcPid(), item.getProductMkcBrandId());
                if(desensitize)
                {
                    item.setImei(computeDesensitizeImei(item.getImei()));
                    item.setImei2(computeDesensitizeImei(item.getImei2()));
                    item.setImei3(computeDesensitizeImei(item.getImei3()));
                }
            });
        }
        return page;
    }

    /**
     * 处理步骤信息
     * @param accountingPageRes
     */
    @Override
    public void handleStepInfo(AccountingPageRes accountingPageRes){
        //获取销售门店
        Integer saleAreaId = Optional.ofNullable(accountingPageRes.getSaleAreaId()).orElse(Integer.MAX_VALUE);
        //获取采购门店
        ApplePurchaseStoreInventory applePurchaseStoreInventory = Optional.ofNullable(applePurchaseStoreInventoryService.getApplePurchaseByMkcId(accountingPageRes.getMkcId())).orElse(new ApplePurchaseStoreInventory());
        Integer purchaseAreaId = Optional.ofNullable(applePurchaseStoreInventory.getAreaId()).orElse(Integer.MIN_VALUE);
        List<StepInfoRes> stepInfoResList = areaGuestbookAccountService.selectByAreaId(saleAreaId, purchaseAreaId);
        accountingPageRes.setStepInfoResList(stepInfoResList);
    }
    /**
     * 串号脱敏
     * @param imei
     * @return
     */
    private String computeDesensitizeImei(String imei) {
        try {
            if (imei == null || imei.isEmpty()) {
                return imei;
            }
            int len = imei.length();
            if (len <= 10) {
                return "****" + imei.substring(len - 6);
            } else {
                return imei.substring(0, 4) + "****" + imei.substring(len - 6);
            }
        }catch (Exception e){
            log.error("串号脱敏异常："+imei,e);
            return imei;
        }

    }

    /**
     * 找出已经被删除的basketId
     * @return
     */
    private List<Integer> getDelBasketId(Set<Integer> basketIdList){
        Set<Integer> originalBasketIdsCopy = new HashSet<>(basketIdList);
        List<Basket> basketList = CommonUtils.bigDataInQuery(originalBasketIdsCopy, ids->basketService.lambdaQuery().in(Basket::getBasketId, ids).last("and ISNULL(isdel,0)=0").list());
        if(CollectionUtil.isEmpty(basketList)){
            return new ArrayList<>(originalBasketIdsCopy);
        }
        List<Integer> collect = basketList.stream().map(Basket::getBasketId).collect(Collectors.toList());
        //求collect和basketIdList的差集
        originalBasketIdsCopy.removeAll(collect);
        return new ArrayList<>(originalBasketIdsCopy);
    }

    @Override
    @DSTransactional
    public R<String> accountingHandle(AccountingHandleReq req) {
        List<Integer> accountingId = req.getAccountingId();
        if(CollectionUtil.isEmpty(accountingId)){
            throw new CustomizeException("下账id不能为空");
        }
        OaUserBO oaUserBO = Optional.ofNullable(component.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("当前用户信息为空"));
        List<AccountingRecords> accountingRecords = this.lambdaQuery().in(AccountingRecords::getId, accountingId).list();

        List<Long> ppidList = accountingRecords.stream().map(AccountingRecords::getPpid).filter(Objects::nonNull).map(Integer::longValue).distinct().collect(Collectors.toList());
        Map<Long, ProductInfoVo> productInfoMap = productInfoService.listByppidsNew(ppidList);
        List<MkcLogNewReq> mkcLogNewReqList = new ArrayList<>();
        List<SubLogsNewReq> subLogsList = new ArrayList<>();
        List<Integer> basketIdList = accountingRecords.stream().map(AccountingRecords::getBasketId).collect(Collectors.toList());
        Map<Integer, Basket> basketMap = basketService.getBasketListMap(basketIdList);
        //basket 校验
        List<Integer> delBasketId = getDelBasketId(basketMap.keySet());
        if(CollectionUtils.isNotEmpty(delBasketId)){
            StringJoiner joiner = new StringJoiner(",");
            delBasketId.forEach(item->{
                Basket basket = basketMap.getOrDefault(item,new Basket());
                joiner.add(basket.getSubId()+"");
            });
            throw new CustomizeException("以下订单已经被删除所以不可以下账："+joiner);
        }
        //根据basketIdList 查询订单信息
        Map<Integer, SubSubCheckInfo> subListMap = basketService.getSubListMap(basketIdList);
        //获取当前用户信息
        Integer userId = oaUserBO.getUserId();
        accountingRecords.forEach(item -> {
            //判断是否msc门店
            SubSubCheckInfo subCheckInfo = subListMap.getOrDefault(item.getBasketId(), new SubSubCheckInfo());
            Integer subCheck = subCheckInfo.getSubCheck();
            if(isMscArea(item.getAccountingAreaId())){
               //下账门店是msc店：校验订单状态为“已确认/已出库/欠款/已出库/已完成/退订”才能操作“下账”按钮 异常提示：下账门店为msc门店，只有订单状态为“已确认/已出库/欠款/已完成/退订”才能操作下账
                List<Integer> subCheckList = Arrays.asList(SubCheckEnum.ALREADY_CONFIRM.getCode(), SubCheckEnum.ALREADY_OUT.getCode(), SubCheckEnum.DEBT.getCode(), SubCheckEnum.ALREADY_COMPLETE.getCode(), SubCheckEnum.CANCEL.getCode());
                if(!subCheckList.contains(subCheck)){
                    throw new CustomizeException("订单："+subCheckInfo.getSubId()+"下账门店为msc门店，只有订单状态为已确认/已出库/欠款/已完成/退订才能操作下账");
                }
            } else {
                //下账门店不是msc店：校验订单状态为“已确认”才能操作“下账”按钮 异常提示：下账门店非msc门店，只有订单状态为“已确认”才能操作下账
                if(!SubCheckEnum.ALREADY_CONFIRM.getCode().equals(subCheck)){
                    throw new CustomizeException("订单："+subCheckInfo.getSubId()+"下账门店非msc门店，只有订单状态为已确认才能操作下账");
                }
            }
            Integer accountingState = item.getAccountingState();
            if(!AccountingStateEnum.PENDING_PROCESSING.getCode().equals(accountingState)){
                throw new CustomizeException("mkcid:"+item.getMkcId()+"下账状态不是待处理");
            }
            Integer sendCount = Optional.ofNullable(item.getSendCount()).orElse(NumberConstant.ZERO);
            if(sendCount>wuliuApolloConfig.getAccountingSendCountMax()){
                throw new CustomizeException("basketId:"+item.getBasketId()+"推送次数过多，请联系管理员");
            }
            boolean update = this.lambdaUpdate().eq(AccountingRecords::getId, item.getId())
                    .set(AccountingRecords::getUpdateTime, LocalDateTime.now())
                    .set(AccountingRecords::getAccountingTime, LocalDateTime.now())
                    .set(AccountingRecords::getSendCount, sendCount+1)
                    .set(AccountingRecords::getAccountingSendUserId,userId)
                    .set(AccountingRecords::getAccountingState, AccountingStateEnum.POSTED.getCode())
                    .update();
            if(!update){
                throw new CustomizeException("下账状态修改失败");
            }
            ProductInfoVo product = productInfoMap.getOrDefault(item.getPpid().longValue(), new ProductInfoVo());
            String message = Optional.ofNullable(product.getProductName()).orElse("")+product.getProductColor()+"mkcId:"+item.getMkcId()+"下账操作";
            //封装库存日志
            MkcLogNewReq mkcLogNewReq = new MkcLogNewReq();
            mkcLogNewReq.setMkcId(item.getMkcId().longValue())
                    .setDTime(LocalDateTime.now())
                    .setInUser(oaUserBO.getUserName())
                    .setShowType(Boolean.FALSE)
                    .setComment(message);
            mkcLogNewReqList.add(mkcLogNewReq);
            //封装订单日志
            SubLogsNewReq subLogsNewReq = new SubLogsNewReq();
            subLogsNewReq.setComment(message);
            Integer subId = Optional.ofNullable(basketMap.getOrDefault(item.getBasketId(), new Basket()).getSubId()).orElseThrow(() -> new CustomizeException("basketId:" + item.getBasketId() + "订单为空")).intValue();
            subLogsNewReq.setSubId(subId);
            subLogsNewReq.setShowType(Boolean.FALSE);
            subLogsNewReq.setType(NumberConstant.ONE);
            subLogsNewReq.setDTime(LocalDateTime.now());
            subLogsNewReq.setInUser(oaUserBO.getUserName());
            subLogsList.add(subLogsNewReq);
        });
        //日志记录
        mkcLogNewService.insertMkcLogBatch(mkcLogNewReqList);
        subLogsCloud.addSubLogBatch(subLogsList);
        return R.success("下账成功");
    }

    @Override
    @DSTransactional
    public R<String> accountingHandleV2(AccountingHandleReq req) {
        Map<Integer, String> msgMap = new HashMap<>();
        msgMap.put(0, "下账操作");
        msgMap.put(1, "扫码操作");
        List<Integer> accountingId = req.getAccountingId();
        if(CollectionUtil.isEmpty(accountingId)){
            throw new CustomizeException("下账id不能为空");
        }
        OaUserBO oaUserBO = Optional.ofNullable(component.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("当前用户信息为空"));
        List<AccountingRecords> accountingRecords = this.lambdaQuery().in(AccountingRecords::getId, accountingId).list();
        accountingRecords.forEach(item->{
            if(!AccountingStateEnum.PENDING_PROCESSING.getCode().equals(item.getAccountingState())){
                throw new CustomizeException("只有待处理状态才能扫码");
            }
        });
        List<Long> ppidList = accountingRecords.stream().map(AccountingRecords::getPpid).filter(Objects::nonNull).map(Integer::longValue).distinct().collect(Collectors.toList());
        Map<Long, ProductInfoVo> productInfoMap = productInfoService.listByppidsNew(ppidList);
        List<MkcLogNewReq> mkcLogNewReqList = new ArrayList<>();
        List<SubLogsNewReq> subLogsList = new ArrayList<>();
        List<Integer> basketIdList = accountingRecords.stream().map(AccountingRecords::getBasketId).collect(Collectors.toList());
        Map<Integer, Basket> basketMap = basketService.getBasketListMap(basketIdList);
        //basket 校验
        List<Integer> delBasketId = getDelBasketId(basketMap.keySet());
        if(CollectionUtils.isNotEmpty(delBasketId)){
            StringJoiner joiner = new StringJoiner(",");
            delBasketId.forEach(item->{
                Basket basket = basketMap.getOrDefault(item,new Basket());
                joiner.add(basket.getSubId()+"");
            });
            throw new CustomizeException("以下订单已经被删除所以不可以扫码："+joiner);
        }
        //根据basketIdList 查询订单信息
        Map<Integer, ApplePurchaseStoreInventory> applePurchaseStoreInventoryMap = new HashMap<>();
        Map<Integer, SubSubCheckInfo> subListMap = basketService.getSubListMap(basketIdList);
        if (Objects.equals(1,req.getRecordsType())) {
            List<Integer> mkcIdList = accountingRecords.stream().map(AccountingRecords::getMkcId).collect(Collectors.toList());
            List<ApplePurchaseStoreInventory> applePurchaseList = CommonUtils.bigDataInQuery(mkcIdList, ids -> applePurchaseStoreInventoryService.lambdaQuery().in(ApplePurchaseStoreInventory::getMkcId, ids).list());
            applePurchaseStoreInventoryMap.putAll(applePurchaseList.stream().collect(Collectors.toMap(ApplePurchaseStoreInventory::getMkcId, Function.identity())));
        }
        //获取当前用户信息
        Integer userId = oaUserBO.getUserId();
        accountingRecords.forEach(item -> {
            //华为
            if (Objects.equals(0,item.getRecordsType())) {
                //判断是否msc门店
                SubSubCheckInfo subCheckInfo = subListMap.getOrDefault(item.getBasketId(), new SubSubCheckInfo());
                Integer subCheck = subCheckInfo.getSubCheck();
                if(isMscArea(item.getAccountingAreaId())){
                    //下账门店是msc店：校验订单状态为“已确认/已出库/欠款/已出库/已完成/退订”才能操作“下账”按钮 异常提示：下账门店为msc门店，只有订单状态为“已确认/已出库/欠款/已完成/退订”才能操作下账
                    List<Integer> subCheckList = Arrays.asList(SubCheckEnum.ALREADY_CONFIRM.getCode(), SubCheckEnum.ALREADY_OUT.getCode(), SubCheckEnum.DEBT.getCode(), SubCheckEnum.ALREADY_COMPLETE.getCode(), SubCheckEnum.CANCEL.getCode());
                    if(!subCheckList.contains(subCheck)){
                        throw new CustomizeException("订单："+subCheckInfo.getSubId()+"下账门店为msc门店，只有订单状态为已确认/已出库/欠款/已完成/退订才能操作下账");
                    }
                } else {
                    //下账门店不是msc店：校验订单状态为“已确认”才能操作“下账”按钮 异常提示：下账门店非msc门店，只有订单状态为“已确认”才能操作下账
                    if(!SubCheckEnum.ALREADY_CONFIRM.getCode().equals(subCheck)){
                        throw new CustomizeException("订单："+subCheckInfo.getSubId()+"下账门店非msc门店，只有订单状态为已确认才能操作下账");
                    }
                }
            } else if (Objects.equals(1,item.getRecordsType())) {
                if (!applePurchaseStoreInventoryMap.containsKey(item.getMkcId())) {
                    throw new CustomizeException("mkc_id："+item.getMkcId()+"非苹果采购导入");
                }
            }
            Integer accountingState = item.getAccountingState();
            if(!AccountingStateEnum.PENDING_PROCESSING.getCode().equals(accountingState)){
                throw new CustomizeException("mkcid:"+item.getMkcId()+"扫码状态不是待处理");
            }
            Integer sendCount = Optional.ofNullable(item.getSendCount()).orElse(NumberConstant.ZERO);
            if(sendCount>wuliuApolloConfig.getAccountingSendCountMax()){
                throw new CustomizeException("basketId:"+item.getBasketId()+"推送次数过多，请联系管理员");
            }
            boolean update = this.lambdaUpdate().eq(AccountingRecords::getId, item.getId())
                    .eq(AccountingRecords::getAccountingState, AccountingStateEnum.PENDING_PROCESSING.getCode())
                    .set(AccountingRecords::getUpdateTime, LocalDateTime.now())
                    .set(AccountingRecords::getAccountingTime, LocalDateTime.now())
                    .set(AccountingRecords::getSendCount, sendCount+1)
                    .set(AccountingRecords::getAccountingSendUserId,userId)
                    .set(AccountingRecords::getAccountingState, AccountingStateEnum.POSTED.getCode())
                    .update();
            if(!update){
                throw new CustomizeException("下账状态修改失败");
            }
            ProductInfoVo product = productInfoMap.getOrDefault(item.getPpid().longValue(), new ProductInfoVo());
            String message = Optional.ofNullable(product.getProductName()).orElse("")+product.getProductColor()+msgMap.get(item.getRecordsType());
            //封装库存日志
            MkcLogNewReq mkcLogNewReq = new MkcLogNewReq();
            mkcLogNewReq.setMkcId(item.getMkcId().longValue())
                    .setDTime(LocalDateTime.now())
                    .setInUser(oaUserBO.getUserName())
                    .setShowType(Boolean.FALSE)
                    .setComment(message);
            mkcLogNewReqList.add(mkcLogNewReq);
            //封装订单日志
            SubLogsNewReq subLogsNewReq = new SubLogsNewReq();
            subLogsNewReq.setComment(message);
            Integer subId = Optional.ofNullable(basketMap.getOrDefault(item.getBasketId(), new Basket()).getSubId()).orElseThrow(() -> new CustomizeException("basketId:" + item.getBasketId() + "订单为空")).intValue();
            subLogsNewReq.setSubId(subId);
            subLogsNewReq.setShowType(Boolean.FALSE);
            subLogsNewReq.setType(NumberConstant.ONE);
            subLogsNewReq.setDTime(LocalDateTime.now());
            subLogsNewReq.setInUser(oaUserBO.getUserName());
            subLogsList.add(subLogsNewReq);
        });
        //日志记录
        mkcLogNewService.insertMkcLogBatch(mkcLogNewReqList);
        subLogsCloud.addSubLogBatch(subLogsList);
        //打印机器信息
        if(StrUtil.isNotBlank(req.getPrinterCode())) {
            accountingPrint(req);
        }
        return R.success("扫码成功");
    }

    public void pushImiMessage(AccountingHandleReq req) {
        List<AccountingRecords> accountingRecords = this.lambdaQuery().in(AccountingRecords::getId, req.getAccountingId()).list();
        OaUserBO oaUserBO = Optional.ofNullable(component.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("当前用户信息为空"));
        Integer userId = oaUserBO.getUserId();
        //判断当前用户 有没有在AccountingRecords里面的sendUserId 如果有那就过滤这种数据 并且过滤 msc门店的数据
        List<AccountingRecords> accountingRecordsFilter = accountingRecords.stream().filter(item -> {
            Integer sendUserId = Optional.ofNullable(item.getSendUserId()).orElse(NumberConstant.ZERO);
            if (sendUserId.equals(userId) || isMscArea(item.getAccountingAreaId())) {
                return Boolean.FALSE;
            }
            return Boolean.TRUE;
        }).collect(Collectors.toList());
        //accountingRecords 按照accountingUserId来进行分组
        Map<Integer, List<AccountingRecords>> accountingRecordsMap = accountingRecordsFilter.stream().collect(Collectors.groupingBy(AccountingRecords::getSendUserId));
        accountingRecordsMap.forEach((k,v)->{
            sendImService(v, oaUserBO);
        });
    }

    public void pushImiMessageV2(AccountingHandleReq req) {
        List<AccountingRecords> accountingRecords = this.lambdaQuery().in(AccountingRecords::getId, req.getAccountingId()).list();
        OaUserBO oaUserBO = Optional.ofNullable(component.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("当前用户信息为空"));
        Integer userId = oaUserBO.getUserId();
        List<AccountingRecords> accountingRecordsFilter = null;
        if (Objects.equals(0, req.getRecordsType())) {
            //判断当前用户 有没有在AccountingRecords里面的sendUserId 如果有那就过滤这种数据 并且过滤 msc门店的数据
            accountingRecordsFilter = accountingRecords.stream().filter(item -> {
                Integer sendUserId = Optional.ofNullable(item.getSendUserId()).orElse(NumberConstant.ZERO);
                if (sendUserId.equals(userId) || isMscArea(item.getAccountingAreaId())) {
                    return Boolean.FALSE;
                }
                return Boolean.TRUE;
            }).collect(Collectors.toList());
        } else if (Objects.equals(1, req.getRecordsType())) {
            accountingRecordsFilter = accountingRecords;
        }
        if (CollectionUtils.isEmpty(accountingRecordsFilter)) {
            return;
        }
        //accountingRecords 按照accountingUserId来进行分组
        Map<Integer, List<AccountingRecords>> accountingRecordsMap = accountingRecordsFilter.stream().collect(Collectors.groupingBy(AccountingRecords::getSendUserId));
        accountingRecordsMap.forEach((k,v)->{
            sendImServiceV2(v, oaUserBO);
        });
    }

    /**
     * 驻店推送
     * @param req
     */
    @Override
    public void pushImiMessageInStore(AccountingHandleReq req) {
        OaUserBO oaUserBO = Optional.ofNullable(component.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("登录信息失效"));
        List<AccountingRecords> accountingRecords = this.lambdaQuery().in(AccountingRecords::getId, req.getAccountingId()).list();
        if(CollectionUtils.isEmpty(accountingRecords)){
            throw new CustomizeException("下账信息查询为空");
        }
        Map<Integer, AccountingRecords> accountingRecordsMap = accountingRecords.stream().collect(Collectors.toMap(AccountingRecords::getBasketId, Function.identity(),(n1,n2)->n2));
        List<Integer> basketIdList = accountingRecords.stream().map(AccountingRecords::getBasketId).collect(Collectors.toList());
        //获取基础信息
        HandleBusinessRes handleBusinessRes = getHandleBusinessRes(basketIdList,null);
        for(AccountingRecords item:accountingRecords){
            //判断是否为msc门店
            if(isMscArea(item.getAccountingAreaId())){
                continue;
            }
            //判断商品是否为指定分类
            Basket basket = handleBusinessRes.getBasketMap().get(item.getBasketId());
            if(ObjectUtil.isNull(basket)){
                continue;
            }
            ProductInfoVo productInfo = handleBusinessRes.getProductInfoEntityMap().get(basket.getPpriceid());
            if(ObjectUtil.isNull(productInfo)){
                continue;
            }
            //如果不是华为商品那就不进行推送
            if(!Optional.ofNullable(productInfo.getBrandId()).orElse(NumberConstant.ZERO).equals(HUAWEI_BRAND_ID)){
                continue;
            }
            //订单查询
            Integer subId = Optional.ofNullable(basket.getSubId()).orElse(0L).intValue();
            Sub sub = handleBusinessRes.getSub();
            if(ObjectUtil.isNull(sub)){
                continue;
            }
            //订单门店
            Integer areaid = Optional.ofNullable(sub.getAreaid()).orElse(NumberConstant.ZERO);
            //查询门店主要驻店人员
            List<Integer> inStoreUserList = this.baseMapper.selectInStoreUser(areaid);
            if(CollectionUtils.isEmpty(inStoreUserList)){
                continue;
            }
            CallImServiceReq callImServiceReq = new CallImServiceReq();
            callImServiceReq.setFromOa(true)
                    .setSubTenant(oaUserBO.getXTenant())
                    .setSendUserId(oaUserBO.getUserId())
                    .setReceiveUserIds(inStoreUserList);
            CallInfoReq callInfoReq = new CallInfoReq();
            callInfoReq.setType(ACCOUNTING_TYPE)
                    .setCardContent(createCardContent(handleBusinessRes,accountingRecordsMap))
                    .setPlainText(ACCOUNTING_PLAIN_TEXT_TWO);
            callImServiceReq.setMessageBody(callInfoReq);
            //调用发送消息的服务
            R<String> stringR = logService.callImService(callImServiceReq);
            if(!stringR.isSuccess()){
                throw new CustomizeException("调用im服务异常(下账驻店推送)："+ Optional.ofNullable(stringR.getMsg()).orElse(stringR.getUserMsg())+"推送数据："+ JSONUtil.toJsonStr(callImServiceReq));
            }
        }
    }

    @Override
    public List<Integer> getAccountingIdsByBasketIds(List<Integer> basketIds) {
        if(CollectionUtils.isEmpty(basketIds)){
            throw new CustomizeException("basketIds不能为空");
        }
        //进行basketId转id
        List<AccountingRecords> recordsList = this.lambdaQuery().in(AccountingRecords::getBasketId, basketIds).list();
        if(recordsList.size() != basketIds.size()){
            throw new CustomizeException("basketIds和下账记录不匹配");
        }
        return recordsList.stream().map(AccountingRecords::getId).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<String> accountingHandleCancel(AccountingHandleReq req) {
        List<Integer> basketIds = req.getBasketIds();
        if(CollectionUtil.isEmpty(basketIds)){
            throw new CustomizeException("basketIds不能为空");
        }
        List<AccountingRecords> accountingRecords = CommonUtils.bigDataInQuery(basketIds, ids -> this.lambdaQuery().in(AccountingRecords::getBasketId, ids)
                .ne(AccountingRecords::getAccountingState, AccountingStateEnum.DELETED.getCode())
                .isNull(AccountingRecords::getAccountingCancelTime).list());
        if(basketIds.size() !=accountingRecords.size()){
            throw new CustomizeException("basketIds和下账记录不匹配");
        }
        accountingRecords.forEach(item -> {
            Integer accountingState = item.getAccountingState();
            if(AccountingStateEnum.DELETED.getCode().equals(accountingState)){
                throw new CustomizeException("basketId:"+item.getBasketId()+"下账状态已删除");
            }
            boolean update = this.lambdaUpdate().eq(AccountingRecords::getId, item.getId())
                    .set(AccountingRecords::getUpdateTime, LocalDateTime.now())
                    .set(AccountingRecords::getCancelUserId, req.getCh999Id())
                    .set(AccountingRecords::getAccountingCancelTime, LocalDateTime.now())
                    .set(AccountingRecords::getAccountingState, AccountingStateEnum.DELETED.getCode())
                    .update();
            if(!update){
                throw new CustomizeException("下账状态修改失败basketId"+item.getBasketId());
            }
                });
        return R.success("取消成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<String> accountingHandleCancelV2(AccountingHandleReq req) {
        List<Integer> mkcIds = req.getMkcIds();
        if(CollectionUtil.isEmpty(mkcIds)){
            throw new CustomizeException("mkcIds不能为空");
        }
        List<AccountingRecords> accountingRecords = CommonUtils.bigDataInQuery(mkcIds, ids -> this.lambdaQuery().in(AccountingRecords::getMkcId, ids)
                .ne(AccountingRecords::getAccountingState, AccountingStateEnum.DELETED.getCode())
                .isNull(AccountingRecords::getAccountingCancelTime)
                .list());
        if(mkcIds.size() !=accountingRecords.size()){
            throw new CustomizeException("mkcIds和下账记录不匹配");
        }
        accountingRecords.forEach(item -> {
            Integer accountingState = item.getAccountingState();
            if(AccountingStateEnum.DELETED.getCode().equals(accountingState)){
                throw new CustomizeException("mkcId:"+item.getMkcId()+"下账状态已删除");
            }
            boolean update = this.lambdaUpdate().eq(AccountingRecords::getId, item.getId())
                    .set(AccountingRecords::getUpdateTime, LocalDateTime.now())
                    .set(AccountingRecords::getCancelUserId, req.getCh999Id())
                    .set(AccountingRecords::getAccountingCancelTime, LocalDateTime.now())
                    .set(AccountingRecords::getAccountingState, AccountingStateEnum.DELETED.getCode())
                    .update();
            if(!update){
                throw new CustomizeException("下账状态修改失败mkcId"+item.getMkcId());
            }
        });
        return R.success("取消成功");
    }

    /**
     * 下载机器图片
     *
     * @param req
     * @param response
     */
    @Override
    public void downloadAppleImg(AccountingDownloadImgReq req, HttpServletResponse response) {
        //查询库存信息
        ProductMkc productMkc = mkcService.getProductMkc(req.getMkcId());
        if (ObjectUtil.isNull(productMkc)) {
            throw new CustomizeException("查询库存信息不存在");
        }
        //查询苹果机器信息
        AppleDeviceInfo appleDeviceInfo = ImeiUtils.appleInfoByImei(productMkc.getImei());
        if (ObjectUtil.isNull(appleDeviceInfo)) {
            throw new CustomizeException("查询苹果机器信息不存在");
        }
        //查询苹果图片信息
        ResponseEntity<byte[]> forEntity = null;
        OutputStream os = null;
        try {
            Map<String, Object> dataMap = BeanUtils.beanToMap(appleDeviceInfo);
            AppleInfoPrintVo printVo = new AppleInfoPrintVo();
            printVo.setData(dataMap);
            printVo.setPage(AppleInfoPrintVo.PrintPage.builder().url("http://localhost:7088/render/#/jiuji/apple-shell").build());
            String url = "https://www.9ji.com/node/receipt";
            forEntity = StockUtils.uploadNodeServer(url, printVo);
            if (forEntity != null && forEntity.getBody() != null) {
                //设置响应内容为图片
                response.setContentType("image/jpeg");
                String fileName = URLEncoder.encode(appleDeviceInfo.getImei() + "苹果报量附件" , "UTF-8") + ".jpg";
                response.setHeader("Content-disposition", "attachment;filename=" + fileName);
                os = response.getOutputStream();
                IoUtil.write(os,false, forEntity.getBody());
            }
        } catch (Exception e) {
            log.error("调用node服务生成图片失败，forEntity：{}，printVo：{}", forEntity, null , e);
        } finally {
            if (os != null) {
                IoUtil.close(os);
            }
        }
    }

    /**
     * @param approvalPushReq
     * @return
     */
    @Override
    public R<String> accountingPushV2(AccountingPushReq approvalPushReq) {
        if (approvalPushReq.getRecordsType() == null) {
            approvalPushReq.setRecordsType(0);
        }
        Integer receiveUserId = approvalPushReq.getReceiveUserId();
        if(ObjectUtil.isNull(receiveUserId)|| receiveUserId==NumberConstant.ZERO){
            return R.error("接收人不能为0或者空");
        }

        List<Integer> basketIdList = approvalPushReq.getBasketId();
        if(CollectionUtil.isEmpty(basketIdList)){
            throw new CustomizeException("basketId不能为空");
        }
        //获取基础信息
        HandleBusinessRes handleBusinessRes = getHandleBusinessRes(approvalPushReq);
        Sub sub = handleBusinessRes.getSub();
        Map<Integer, Basket> basketMap = handleBusinessRes.getBasketMap();
        Map<Integer, ProductMkc> productMkcMap = handleBusinessRes.getProductMkcMap();
        Map<Long, ProductInfoVo> productInfoMap = handleBusinessRes.getProductInfoEntityMap();
        List<AccountingRecords> accountingRecordsList = new ArrayList<>();
        List<SubLogsNewReq> subLogsList = new ArrayList<>();
        //获取发送人姓名
        AtomicReference<String> inUser = new AtomicReference<>("");

        Integer sendUserId = Optional.ofNullable(approvalPushReq.getSendUserId()).orElse(NumberConstant.ZERO);
        if(NumberConstant.ZERO.equals(sendUserId)){
            inUser.getAndSet("系统");
        } else {
            inUser.getAndSet(Optional.ofNullable(ch999UserService.getUserByCh999Id(sendUserId)).orElse(new Ch999User()).getCh999Name());
        }
        basketIdList.forEach(item->{
            SaveInfoRes saveInfoRes = new SaveInfoRes();
            saveInfoRes.setBasketId(item)
                    .setTriggerNode(approvalPushReq.getTriggerNode())
                    .setReceiveUserId(approvalPushReq.getReceiveUserId())
                    .setSendUserId(approvalPushReq.getSendUserId())
                    .setRecordsType(approvalPushReq.getRecordsType());
            //数据库保存
            AccountingRecords records = saveOrUpdateAccountingRecords(saveInfoRes,productMkcMap.getOrDefault(approvalPushReq.getMkcId(),new ProductMkc()),sub,basketMap.getOrDefault(item,new Basket()));
            accountingRecordsList.add(records);
            //封装订单日志
            String ch999Name = Optional.ofNullable(ch999UserService.getUserByCh999Id(records.getReceiveUserId())).orElse(new Ch999User()).getCh999Name();
            ProductInfoVo productInfoVo = productInfoMap.getOrDefault(records.getPpid().longValue(), new ProductInfoVo());
            String message = String.format("%s 需要进行下账处理 才能进行出库操作，已和 %s 建立内部聊天"
                    ,Optional.ofNullable(productInfoVo.getProductName()).orElse("")+productInfoVo.getProductColor(),ch999Name);
            if (Objects.equals(1, records.getRecordsType())) {
                message = String.format("%s 需要进行报量处理 才能进行出库操作，已和 %s 建立内部聊天"
                        ,Optional.ofNullable(productInfoVo.getProductName()).orElse("")+productInfoVo.getProductColor(),ch999Name);
            }
            SubLogsNewReq subLogsNewReq = new SubLogsNewReq();
            subLogsNewReq.setComment(message);
            Integer subId = Optional.ofNullable(basketMap.getOrDefault(records.getBasketId(), new Basket()).getSubId()).orElseThrow(() -> new CustomizeException("basketId:" + records.getBasketId() + "订单为空")).intValue();
            subLogsNewReq.setSubId(subId);
            subLogsNewReq.setShowType(Boolean.FALSE);
            subLogsNewReq.setType(NumberConstant.ONE);
            subLogsNewReq.setDTime(LocalDateTime.now());
            subLogsNewReq.setInUser(inUser.get());
            subLogsList.add(subLogsNewReq);
        });
        // accountingRecordsList根据basketId收集成为map
        Map<Integer, List<AccountingRecords>> accountingRecordsMap = accountingRecordsList.stream().collect(Collectors.groupingBy(AccountingRecords::getBasketId));
        List<Integer> receiveUserIds = accountingRecordsList.stream().map(AccountingRecords::getReceiveUserId).distinct().collect(Collectors.toList());
        Map<Integer, String> plainTextMap = new HashMap<>();
        plainTextMap.put(0, ACCOUNTING_PLAIN_TEXT_ONE);
        plainTextMap.put(1, "苹果设备销售报量");

        //参数封装
        CallImServiceReq callImServiceReq = new CallImServiceReq();
        callImServiceReq.setFromOa(true)
                .setSubTenant(Convert.toInt(Namespaces.get()))
                .setSendUserId(approvalPushReq.getSendUserId())
                .setReceiveUserIds(receiveUserIds);
        CallInfoReq callInfoReq = new CallInfoReq();
        callInfoReq.setType(ACCOUNTING_TYPE)
                .setCardContent(createCardContentV2(handleBusinessRes,accountingRecordsMap))
                .setPlainText(plainTextMap.get(approvalPushReq.getRecordsType()));
        callImServiceReq.setMessageBody(callInfoReq);
        //调用发送消息的服务
        R<String> stringR = logService.callImService(callImServiceReq);
        if(!stringR.isSuccess()){
            throw new CustomizeException("调用im服务异常："+ Optional.ofNullable(stringR.getMsg()).orElse(stringR.getUserMsg()));
        }
        //日志记录
        subLogsCloud.addSubLogBatch(subLogsList);
        return stringR;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public R<String> accountingPushV3(AccountingPushNewReq approvalPushReq) {
        if (approvalPushReq.getRecordsType() == null) {
            approvalPushReq.setRecordsType(1);
        }
        String receiveUserIdStr = approvalPushReq.getReceiveUserIdStr();
        if(StringUtil.isBlank(receiveUserIdStr)){
            return R.error("接收人不能为0或者空");
        }
        List<Integer> receiveUserIdList = Arrays.asList(receiveUserIdStr.split(",")).stream().filter(NumberUtil::isNumber).map(Convert::toInt).collect(Collectors.toList());
        approvalPushReq.setReceiveUserId(receiveUserIdList.get(NumberConstant.ZERO));
        List<Integer> basketIdList = approvalPushReq.getBasketId();
        if(CollectionUtil.isEmpty(basketIdList)){
            throw new CustomizeException("basketId不能为空");
        }
        String moaHost = Optional.of(sysConfigClient.getValueByCode(SysConfigConstant.MOA_URL))
                .filter(t -> t.getCode() == ResultCode.SUCCESS)
                .map(R::getData)
                .orElseThrow(() -> new CustomizeException("获取M端域名出错"));
        //获取基础信息
        HandleBusinessRes handleBusinessRes = getHandleBusinessRes(BeanUtil.copyProperties(approvalPushReq,AccountingPushReq.class));
        Sub sub = handleBusinessRes.getSub();
        Map<Integer, Basket> basketMap = handleBusinessRes.getBasketMap();
        Map<Integer, ProductMkc> productMkcMap = handleBusinessRes.getProductMkcMap();
        Map<Long, ProductInfoVo> productInfoMap = handleBusinessRes.getProductInfoEntityMap();

        List<SubLogsNewReq> subLogsList = new ArrayList<>();

        basketIdList.forEach(item->{
            SaveInfoRes saveInfoRes = new SaveInfoRes();
            saveInfoRes.setBasketId(item)
                    .setTriggerNode(approvalPushReq.getTriggerNode())
                    .setReceiveUserIdStr(approvalPushReq.getReceiveUserIdStr())
                    .setReceiveUserId(approvalPushReq.getReceiveUserId())
                    .setRecordsType(approvalPushReq.getRecordsType());
            //数据库保存
            Integer mkcId = approvalPushReq.getMkcId();
            ProductMkc productMkc = productMkcMap.getOrDefault(mkcId, new ProductMkc());
            AccountingRecords records = saveOrUpdateAccountingRecordsNew(saveInfoRes,productMkc,sub,basketMap.getOrDefault(item,new Basket()));
            //封装订单日志
            List<Ch999User> userByCh999Ids = ch999UserService.getUserByCh999Ids(receiveUserIdList);
            String ch999Name = userByCh999Ids.stream().map(Ch999User::getCh999Name).collect(Collectors.joining(","));
            ProductInfoVo productInfoVo = productInfoMap.getOrDefault(records.getPpid().longValue(), new ProductInfoVo());
            String productName = Optional.ofNullable(productInfoVo.getProductName()).orElse("") + productInfoVo.getProductColor();
            String message = String.format("%s 需要进行扫码处理 才能进行出库操作，已推送处理人%s",productName,ch999Name);
            SubLogsNewReq subLogsNewReq = new SubLogsNewReq();
            subLogsNewReq.setComment(message);
            Integer subId = Optional.ofNullable(basketMap.getOrDefault(records.getBasketId(), new Basket()).getSubId()).orElseThrow(() -> new CustomizeException("basketId:" + records.getBasketId() + "订单为空")).intValue();
            subLogsNewReq.setSubId(subId);
            subLogsNewReq.setShowType(Boolean.FALSE);
            subLogsNewReq.setType(NumberConstant.ONE);
            subLogsNewReq.setDTime(LocalDateTime.now());
            subLogsNewReq.setInUser(AbstractCurrentRequestComponent.SYS_OA_USER_NAME);
            subLogsList.add(subLogsNewReq);
            String comment = String.format("订单%s中 ，库存编号：%s， IMEI：%s   机型：%s，需要进行guestbook 扫码，请点击查看详情按照操作步骤进行扫码", subId,mkcId, productMkc.getImei(), productName);
            receiveUserIdList.forEach(receiveUserId->{
                String link = moaHost+"/new/#/logistics/applePeripherals?userid="+receiveUserId;
                Boolean messageSend = messageSendService.sendOaMessageV1(0, link, Convert.toStr(receiveUserId), comment, MsgTypeEnum.ORDER_NOTICE.getCode());
                if(!messageSend){
                    RRExceptionHandler.logError("OA推送订单通知消息异常", approvalPushReq, null, SpringUtil.getBean(ISmsService.class)::sendOaMsgTo9JiMan);
                }
            });

        });
        //日志记录
        subLogsCloud.addSubLogBatch(subLogsList);
        return R.success("操作成功");
    }

    /**
     * 打印
     *
     * @param req
     * @return
     */
    @Override
    public R<String> accountingPrint(AccountingHandleReq req) {
        List<Integer> accountingId = req.getAccountingId();
        if(CollectionUtil.isEmpty(accountingId)){
            throw new CustomizeException("下账id不能为空");
        }
        OaUserBO oaUserBO = Optional.ofNullable(component.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("当前用户信息为空"));
        List<AccountingRecords> accountingRecords = this.lambdaQuery().in(AccountingRecords::getId, accountingId).list();
        //打印机器信息
        if(StrUtil.isNotBlank(req.getPrinterCode())) {
            List<Integer> mkcIdList = accountingRecords.stream().map(AccountingRecords::getMkcId).collect(Collectors.toList());
            List<ProductMkc> productMkcList = CommonUtils.bigDataInQuery(mkcIdList, ids -> mkcService.lambdaQuery().in(ProductMkc::getId, ids).list());
            Map<Integer, Areainfo> areainfoMap = areaInfoService.mapAll();
            for (ProductMkc productMkc : productMkcList) {
                StockUtils.generalPrint(GeneralPrintVO.builder()
                        .clientNo(req.getPrinterCode()).ch999Id(Convert.toStr(oaUserBO.getUserId()))
                        .source(areainfoMap.getOrDefault(oaUserBO.getAreaId(), new Areainfo()).getArea()).printId(productMkc.getImei()).type(67).build());
            }
        }
        return R.success("操作成功");
    }

    /**
     * 判断是否为msc门店
     * @param areaId
     * @return
     */
    private Boolean isMscArea(Integer areaId){
        String value = sysConfigService.getValueByCode2(MSC_AREA_ID);
        if(StringUtil.isEmpty(value)){
            return Boolean.FALSE;
        }
        List<MscConfigIngoVo> list = JSONUtil.toList(JSONUtil.toJsonStr(value), MscConfigIngoVo.class);
        if(CollectionUtils.isEmpty(list)){
            return Boolean.FALSE;
        }
        List<Integer> mscAreaIdList = list.stream()
                .map(MscConfigIngoVo::getFromAreaId)
                .filter(StringUtil::isNotBlank)
                .flatMap(id -> Arrays.stream(id.split(",")).filter(StringUtil::isNotBlank))
                .map(Integer::valueOf)
                .collect(Collectors.toList());
        return mscAreaIdList.contains(areaId);
    }

    /**
     *
     * @param accountingRecords
     * @param oaUserBO
     */
    private void sendImService(List<AccountingRecords> accountingRecords, OaUserBO oaUserBO) {
        //卡片推送
        List<Integer> basketIdList = accountingRecords.stream().map(AccountingRecords::getBasketId).collect(Collectors.toList());
        //获取基础信息
        HandleBusinessRes handleBusinessRes = getHandleBusinessRes(basketIdList,null);
        Map<Integer, AccountingRecords> accountingRecordsMap = accountingRecords.stream().collect(Collectors.toMap(AccountingRecords::getBasketId, Function.identity(),(n1, n2)->n2));
        //根据accountingRecords 的发送人进行分组
        Map<Integer, List<AccountingRecords>> SendUserMap = accountingRecords.stream().collect(Collectors.groupingBy(AccountingRecords::getAccountingSendUserId));
        List<Integer> receiveUserIds = accountingRecords.stream().map(AccountingRecords::getSendUserId).distinct().collect(Collectors.toList());
        SendUserMap.forEach((k,v)->{
            CallImServiceReq callImServiceReq = new CallImServiceReq();
            callImServiceReq.setFromOa(true)
                    .setSubTenant(oaUserBO.getXTenant())
                    .setSendUserId(k)
                    .setReceiveUserIds(receiveUserIds);
            CallInfoReq callInfoReq = new CallInfoReq();
            callInfoReq.setType(ACCOUNTING_TYPE)
                    .setCardContent(createCardContent(handleBusinessRes,accountingRecordsMap))
                    .setPlainText(ACCOUNTING_PLAIN_TEXT_TWO);
            callImServiceReq.setMessageBody(callInfoReq);
            //调用发送消息的服务
            R<String> stringR = logService.callImService(callImServiceReq);
            if(!stringR.isSuccess()){
                throw new CustomizeException("调用im服务异常(下账推送)："+ Optional.ofNullable(stringR.getMsg()).orElse(stringR.getUserMsg())+"推送数据："+ JSONUtil.toJsonStr(callImServiceReq));
            }
        });
    }

    /**
     *
     * @param accountingRecords
     * @param oaUserBO
     */
    private void sendImServiceV2(List<AccountingRecords> accountingRecords, OaUserBO oaUserBO) {
        if (CollectionUtil.isEmpty(accountingRecords)) {
            return;
        }
        //卡片推送
        List<Integer> basketIdList = accountingRecords.stream().map(AccountingRecords::getBasketId).collect(Collectors.toList());
        //获取基础信息
        HandleBusinessRes handleBusinessRes = getHandleBusinessResV2(basketIdList);
        Map<Integer, List<AccountingRecords>> accountingRecordsMap = accountingRecords.stream().collect(Collectors.groupingBy(AccountingRecords::getBasketId));
        //根据accountingRecords 的发送人进行分组
        Map<Integer, List<AccountingRecords>> SendUserMap = accountingRecords.stream().collect(Collectors.groupingBy(AccountingRecords::getAccountingSendUserId));
        List<Integer> receiveUserIds = accountingRecords.stream().map(AccountingRecords::getSendUserId).distinct().collect(Collectors.toList());
        Map<Integer, String> accountingPlainTextMap = new HashMap<>();
        accountingPlainTextMap.put(0, ACCOUNTING_PLAIN_TEXT_TWO);
        accountingPlainTextMap.put(1, "苹果设备已销售报量");
        Integer recordsType = accountingRecords.get(0).getRecordsType() != null ? accountingRecords.get(0).getRecordsType() : 0;
        String accountingPlainText = accountingPlainTextMap.get(recordsType);
        SendUserMap.forEach((k,v)->{
            CallImServiceReq callImServiceReq = new CallImServiceReq();
            callImServiceReq.setFromOa(true)
                    .setSubTenant(oaUserBO.getXTenant())
                    .setSendUserId(k)
                    .setReceiveUserIds(receiveUserIds);
            CallInfoReq callInfoReq = new CallInfoReq();
            callInfoReq.setType(ACCOUNTING_TYPE)
                    .setCardContent(createCardContentV2(handleBusinessRes,accountingRecordsMap))
                    .setPlainText(accountingPlainText);
            callImServiceReq.setMessageBody(callInfoReq);
            //调用发送消息的服务
            R<String> stringR = logService.callImService(callImServiceReq);
            if(!stringR.isSuccess()){
                throw new CustomizeException("调用im服务异常(下账推送)："+ Optional.ofNullable(stringR.getMsg()).orElse(stringR.getUserMsg())+"推送数据："+ JSONUtil.toJsonStr(callImServiceReq));
            }
        });
    }

    /**
     * 数据校验
     * @param pageReq
     */
    private void checkReq(AccountingPageReq pageReq){
        //数据校验
        Integer selectType = pageReq.getSelectType();
        String selectValue = pageReq.getSelectValue();
        List<Integer> numberList = Arrays.asList(SelectTypeEnum.ACCOUNTING_USER_ID.getCode(), SelectTypeEnum.SKU_ID.getCode(), SelectTypeEnum.SPU_ID.getCode());
        if(numberList.contains(selectType) && !StringUtils.isEmpty(selectValue) && !StringUtils.isNumeric(selectValue)){
            String str = SelectTypeEnum.ACCOUNTING_USER_ID.getMessage() + "," + SelectTypeEnum.SKU_ID.getMessage() + "," + SelectTypeEnum.SPU_ID.getMessage();
            throw new CustomizeException("选择"+str+"查询的时候必须为数字");
        }
        Integer handleTimeoutMax = pageReq.getHandleTimeoutMax();
        Integer handleTimeoutMin = pageReq.getHandleTimeoutMin();
        if(ObjectUtil.isNotNull(handleTimeoutMax) && !StringUtils.isNumeric(handleTimeoutMax)){
            throw new CustomizeException("处理超时最大值必须为数字");
        }
        if(ObjectUtil.isNotNull(handleTimeoutMin) && !StringUtils.isNumeric(handleTimeoutMin)){
            throw new CustomizeException("处理超时最小值必须为数字");
        }
    }

    /**
     * 数据处理
     * @param pageReq
     */
    private void handleReq(AccountingPageReq pageReq){
        //超时转换
        Boolean isHandleTimeout = pageReq.getIsHandleTimeout();
        if(ObjectUtil.isNotNull(isHandleTimeout)){
            if(isHandleTimeout){
                pageReq.setHandleTimeoutMin(ACCOUNTING_HANDLE_TIME_OUT);
                pageReq.setHandleTimeoutMax(Integer.MAX_VALUE);
            } else {
                pageReq.setHandleTimeoutMin(NumberConstant.ZERO);
                pageReq.setHandleTimeoutMax(ACCOUNTING_HANDLE_TIME_OUT);
            }
        } else {
            //将小时转换成为分钟
            Integer handleTimeoutMin = pageReq.getHandleTimeoutMin();
            if(ObjectUtil.isNull(handleTimeoutMin)){
                pageReq.setHandleTimeoutMin(NumberConstant.ZERO);
            } else {
                pageReq.setHandleTimeoutMin(handleTimeoutMin * NumberConstant.SIXTY);
            }
            //将小时转换成为分钟
            Integer handleTimeoutMax = pageReq.getHandleTimeoutMax();
            if(ObjectUtil.isNull(handleTimeoutMax)){
                pageReq.setHandleTimeoutMax(Integer.MAX_VALUE);
            } else {
                pageReq.setHandleTimeoutMax(handleTimeoutMax * NumberConstant.SIXTY);
            }

        }
    }




    /**
     * 获取MOA域名
     * @return
     */
    private String getMoaUrl(){
        //获取MOA域名
        R<String> hostResult = sysConfigClient.getValueByCode(SysConfigConstant.MOA_URL);
        if(!hostResult.isSuccess()){
            throw new CustomizeException("获取域名失败"+ Optional.ofNullable(hostResult.getMsg()).orElse(hostResult.getUserMsg()));
        }
        return hostResult.getData();
    }
}
