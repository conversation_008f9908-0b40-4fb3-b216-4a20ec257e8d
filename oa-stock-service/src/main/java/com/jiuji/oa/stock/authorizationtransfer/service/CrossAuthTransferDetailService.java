package com.jiuji.oa.stock.authorizationtransfer.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.stock.authorizationtransfer.entity.CrossAuthTransferDetail;
import com.jiuji.oa.stock.authorizationtransfer.vo.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface CrossAuthTransferDetailService extends IService<CrossAuthTransferDetail> {

    /**
     * 获取订单信息
     * @param transferIdList
     * @return
     */
    Map<Integer,List<CrossAuthTransferDetail>> getTransferDetailInfo(List<Integer> transferIdList);

    /**
     * 根据调拨单id获取实时总价
     * @param transferId
     * @return
     */
    SubTotalInfo selectTotalCost(Integer transferId);

    /**
     * 调拨单明细查询
     * @param detailId
     * @return
     */
    CostDetailVo selectTotalCostDetail(Integer detailId,Integer status);

    /**
     * 订单详情查询
     * @param req
     * @return
     */
    SubInfoVO selectSubInfo(SubInfoReqVO req);

    /**
     * 商品修改
     * @param req
     */
    void updateSubDetail(UpdateSubDetailVO req);

    /**
     * 批量查询订单总金额
     * @param transferIdList 调拨单ID列表
     * @return 调拨单ID到总金额信息的映射
     */
    Map<Integer, SubTotalInfo> batchSelectTotalCost(List<Integer> transferIdList);

    /**
     * 批量查询调拨单明细
     * @param detailIdList 明细ID列表
     * @param detailStatusMap 明细ID到状态的映射
     * @return 明细ID到详情的映射
     */
    Map<Integer, CostDetailVo> batchSelectTotalCostDetail(List<Integer> detailIdList, Map<Integer, Integer> detailStatusMap);
}
