package com.jiuji.oa.stock.authorizationtransfer.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SubmitOrderDetailVO {

    private Integer ppid;

    /**
     * 大小件
     */
    private Boolean ismobile;

    private Integer mkcId;

    /**
     * 库存数量
     */
    private Integer count;

    /**
     * 成本
     */
    private BigDecimal costPrice;

    /**
     * 调拨成本
     */
    private BigDecimal transferPrice;
}
