package com.jiuji.oa.stock.authorizationtransfer.enums;

import com.jiuji.oa.nc.abnormal.vo.ShowPrintingEnumVO;
import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum ErrorImeiMsgEnums implements CodeMessageEnumInterface {


    ILLEGAL_OR_FLAW(0, "该串号为瑕疵机或者是非法串号或者不是库存状态"),
    ORDER_LOCK(1, "该串号已被订单锁定，BASKET_ID:%s"),
    IS_MOULDFLAG(2, "该串号为样机"),
    NO_STOCK(3, "该串号不是库存状态"),
    NO_AREA(4, "该串号不在门店");

    private Integer code;
    private String message;

    /**
     * 根据code获取其对应的名称
     *
     * @return String
     */
    public static String getMessageByCode(Integer code) {
        for (ErrorImeiMsgEnums purchaseState : values()) {
            if (purchaseState.getCode().equals(code)) {
                return purchaseState.getMessage();
            }
        }
        return "";
    }
    /**
     * 将所有的枚举转换成list
     *
     * @return
     */
    public static List<ShowPrintingEnumVO> getAllPrintingEnum() {
        ErrorImeiMsgEnums[] array = ErrorImeiMsgEnums.values();
        List<ShowPrintingEnumVO> arrayList = new ArrayList<>();
        for (ErrorImeiMsgEnums t : array) {
            ShowPrintingEnumVO showPrintingEnumVO = new ShowPrintingEnumVO()
                    .setLabel(t.getMessage())
                    .setValue(t.getCode());
            arrayList.add(showPrintingEnumVO);
        }
        return arrayList;
    }

}
