package com.jiuji.oa.stock.authorizationtransfer.enums;

import com.jiuji.oa.nc.abnormal.vo.ShowPrintingEnumVO;
import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum SubPageReqEnums implements CodeMessageEnumInterface {

    category(8, "商品分类"),
    PRODUCTNAME(0, "商品名称"),
    PRODUCTID(1, "商品ID"),
    PPID(2, "PPID"),
    IMEI(3, "串号"),
    create_user(4, "提交人"),
    check_user(5, "审核人"),
    handle_user(6, "办理人"),
    order(7, "单号"),

    ;

    private Integer code;
    private String message;

    /**
     * 根据code获取其对应的名称
     *
     * @return String
     */
    public static String getMessageByCode(Integer code) {
        for (SubPageReqEnums purchaseState : values()) {
            if (purchaseState.getCode().equals(code)) {
                return purchaseState.getMessage();
            }
        }
        return "";
    }
    /**
     * 将所有的枚举转换成list
     *
     * @return
     */
    public static List<ShowPrintingEnumVO> getAllPrintingEnum() {
        SubPageReqEnums[] array = SubPageReqEnums.values();
        List<ShowPrintingEnumVO> arrayList = new ArrayList<>();
        for (SubPageReqEnums t : array) {
            ShowPrintingEnumVO showPrintingEnumVO = new ShowPrintingEnumVO()
                    .setLabel(t.getMessage())
                    .setValue(t.getCode());
            arrayList.add(showPrintingEnumVO);
        }
        return arrayList;
    }

}
