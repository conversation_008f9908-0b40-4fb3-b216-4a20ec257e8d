package com.jiuji.oa.stock.authorizationtransfer.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SaveLogVO {

    @NotNull(message = "日志内容不能为空")
    private String comment;

    @NotNull(message = "调拨单号不能为空")
    private Integer subId;

    public SaveLogVO(String comment, Integer subId) {
        this.comment = comment;
        this.subId = subId;
    }

    public SaveLogVO() {
    }
}
