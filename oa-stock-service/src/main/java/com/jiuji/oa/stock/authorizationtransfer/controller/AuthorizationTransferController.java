package com.jiuji.oa.stock.authorizationtransfer.controller;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jiuji.oa.nc.common.exception.CustomizeException;
import com.jiuji.oa.nc.common.util.ExcelUtils;
import com.jiuji.oa.nc.dict.service.ISysConfigService;
import com.jiuji.oa.nc.oaapp.po.SysConfig;
import com.jiuji.oa.stock.authorizationtransfer.service.AuthorizationTransferService;
import com.jiuji.oa.stock.authorizationtransfer.service.CrossAuthTransferDetailService;
import com.jiuji.oa.stock.authorizationtransfer.vo.*;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.constants.NumberConstant;
import org.springframework.beans.BeanUtils;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("api/authorizationtransfer")
public class AuthorizationTransferController {


    @Resource
    private ISysConfigService sysConfigService;
    @Resource
    private AuthorizationTransferService authorizationTransferService;
    @Resource
    private CrossAuthTransferDetailService detailService;

    /**
     * 跨授权调拨 配置查询
     * @return
     */
    @GetMapping("/selectConfig/v1")
    public R<SysConfig> selectConfig() {
        // SysConfig sysConfig = sysConfigService.selectAuthorizationTransferConfig();
        return R.success(null);
    }



    /**
     * 跨授权调拨 配置 修改
     * @return
     */
    @PostMapping("/updateConfig/v1")
    public R<String> updateConfig(@RequestBody @Valid UpdateConfigVO updateConfigVO) {
        authorizationTransferService.updateConfig(updateConfigVO);
        return R.success("开启成功并且不能关闭");
    }


    /**
     * 跨授权调拨 调拨单分页查询
     * @return
     */
    @PostMapping("/pageSubInfo/v1")
    public R<IPage<PageSubInfoVO>> pageSubInfo(@RequestBody PageSubInfoVOReq req) {
        IPage<PageSubInfoVO> pageSubInfoVOIPage = authorizationTransferService.pageSubInfo(req);
        return R.success(pageSubInfoVOIPage);
    }

    /**
     * 跨授权调拨 调拨单分页查询 导出
     * @return
     */
    @PostMapping("/export/v1")
    public void export(HttpServletResponse response,@RequestBody PageSubInfoVOReq req)  throws IOException {
        req.setCurrent(1).setSize(50000);
        IPage<PageSubInfoVO> pageSubInfoVOIPage = authorizationTransferService.pageSubInfo(req);
        List<PageSubInfoVO> records = pageSubInfoVOIPage.getRecords();
        if(CollectionUtils.isEmpty(records)){
            throw new CustomizeException("导出数据为空");
        }
        List<PageSubInfoExcel> collect = records.stream().map(item -> {
            PageSubInfoExcel pageSubInfoExcel = new PageSubInfoExcel();
            if(NumberConstant.ONE.equals(req.getExportType())){
                pageSubInfoExcel = new PageSubInfoDetailExcel();
            }
            BeanUtils.copyProperties(item, pageSubInfoExcel);
            if(ObjectUtil.isNotNull(item.getCheckTime())){
                pageSubInfoExcel.setCheckTime(item.getCheckTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            }
            if(ObjectUtil.isNotNull(item.getCreateTime())){
                pageSubInfoExcel.setCreateTime(item.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            }
            if(ObjectUtil.isNotNull(item.getHandleTime())){
                pageSubInfoExcel.setHandleTime(item.getHandleTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            }
             return pageSubInfoExcel;
        }).collect(Collectors.toList());

        ExcelWriter writer = ExcelUtil.getWriter(true);
        //自定义标题别名
        writer.addHeaderAlias("id", "订单号");
        writer.addHeaderAlias("fromAreaIdValue", "发货门店");
        writer.addHeaderAlias("fromAuthIdValue", "发货授权");
        writer.addHeaderAlias("toAreaIdValue", "收货门店");
        writer.addHeaderAlias("toAuthIdValue", "收货授权");
        writer.addHeaderAlias("title", "标题");
        if(NumberConstant.ONE.equals(req.getExportType())){
            writer.addHeaderAlias("categoryName", "商品分类");
            writer.addHeaderAlias("productName", "商品名称");
            writer.addHeaderAlias("productColor", "规格");
            writer.addHeaderAlias("imei", "串号");
        }
        writer.addHeaderAlias("count", "数量");
        writer.addHeaderAlias("totalCost", "总成本");
        writer.addHeaderAlias("totalTransferPrice", "调拨金额");
        writer.addHeaderAlias("transferDifferencePrice", "调拨差额");
        writer.addHeaderAlias("statusValue", "状态");
        writer.addHeaderAlias("createTime", "提交时间");
        writer.addHeaderAlias("createUser", "提交人");
        writer.addHeaderAlias("checkTime", "审核时间");
        writer.addHeaderAlias("checkUser", "审核人");
        writer.addHeaderAlias("handleTime", "办理时间");
        writer.addHeaderAlias("handleUser", "办理人");
        writer.write(collect, true);
        // response为HttpServletResponse对象
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        // feedbackInfo.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
        String filename = ExcelUtils.getExportFileName("跨授权调拨");
        if(NumberConstant.ONE.equals(req.getExportType())){
            filename = ExcelUtils.getExportFileName("跨授权调拨详情");
        }
        filename = URLEncoder.encode(filename, "UTF-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + filename);
        ServletOutputStream out = response.getOutputStream();
        writer.flush(out, true);
        // 关闭writer，释放内存
        writer.close();
        //此处记得关闭输出Servlet流
        IoUtil.close(out);

    }



    /**
     * 跨授权调拨 订单详情查询
     * @return
     */
    @PostMapping("/selectSubInfo/v1")
    public R<SubInfoVO> selectSubInfo(@RequestBody SubInfoReqVO req) {
        SubInfoVO subInfoVO = detailService.selectSubInfo(req);
        return R.success(subInfoVO);
    }

    /**
     * 跨授权调拨 订单详情查询
     * @return
     */
    @PostMapping("/updateSubDetail/v1")
    public R<String> updateSubDetail(@RequestBody @Valid UpdateSubDetailVO req) {
        req.getDetailVOList().forEach(item->{
            Assert.isTrue(ObjectUtil.isNotNull(item.getPpid()), "商品ppid不能为空");
            Assert.isTrue(ObjectUtil.isNotNull(item.getIsmobile()), "商品大小件属性不能为空");
            Assert.isTrue(ObjectUtil.isNotNull(item.getMkcId()), "商品库存id不能为空");
            Assert.isTrue(ObjectUtil.isNotNull(item.getCount()), "商品调拨数量不能为空");
            Assert.isTrue(ObjectUtil.isNotNull(item.getTransferPrice()), "商品调拨价格不能为空");
            Assert.isTrue(ObjectUtil.isNotNull(item.getCostPrice()) , "商品成本价格不能为空");
        });
        detailService.updateSubDetail(req);
        return R.success("保存成功");
    }



    /**
     * 添加授权 添加数据
     * @return
     */
    @GetMapping("/addAuthorize/v1")
    public R<String> addAuthorize() {
        authorizationTransferService.createDataByAuthorize();
        return R.success("授权添加成功");
    }
}
