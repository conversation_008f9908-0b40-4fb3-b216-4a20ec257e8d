package com.jiuji.oa.stock.authorizationtransfer.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("cross_auth_transfer_sub_log")
public class CrossAuthTransferSubLog {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 调拨单id
     */
    @TableField("sub_id")
    private Integer subId;
    /**
     * 日志内容
     */
    private String comment;

    @TableLogic
    private Boolean isDel;

    /**
     * 创建人工号
     */
    @TableField("create_user_id")
    private Integer createUserId;

    /**
     * 创建人
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

}
