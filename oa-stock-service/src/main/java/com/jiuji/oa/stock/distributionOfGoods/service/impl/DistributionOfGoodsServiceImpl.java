package com.jiuji.oa.stock.distributionOfGoods.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.jiuji.oa.nc.common.bo.OaUserBO;
import com.jiuji.oa.nc.common.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.nc.common.exception.CustomizeException;
import com.jiuji.oa.nc.product.entity.ProductInfoEntity;
import com.jiuji.oa.nc.product.service.IProductInfoService;
import com.jiuji.oa.nc.user.po.Areainfo;
import com.jiuji.oa.nc.user.service.IAreaInfoService;
import com.jiuji.oa.stock.distributionOfGoods.enums.SearchTermEnum;
import com.jiuji.oa.stock.distributionOfGoods.mapper.DistributionOfGoodsServiceMapper;
import com.jiuji.oa.stock.distributionOfGoods.mapstruct.DistributionOfGoodsMapper;
import com.jiuji.oa.stock.distributionOfGoods.service.DistributionOfGoodsService;
import com.jiuji.oa.stock.distributionOfGoods.utils.MqUtils;
import com.jiuji.oa.stock.distributionOfGoods.vo.*;
import com.jiuji.oa.stock.inventory.enums.AreaLevelEnum;
import com.jiuji.oa.stock.pdapurchase.enums.KcCheckEnum;
import com.jiuji.tc.common.vo.R;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Service
@DS("oanewWrite")
public class DistributionOfGoodsServiceImpl implements DistributionOfGoodsService {



    private static final int MAX_SEARCHTERMVALUE=20;
    private static final long MAX_MONTH=3L;
    private static final String TYPE_PID="pid";
    private static final String TYPE_ZID="zid";
    private static final String TYPE_CID="cid";
    private static final String TRANSFER_BATCH="MkcTransferBatch";
    private static final List<Integer> kunMingList=Arrays.asList(530102,530103,530111,530112);
    private static final Integer KUNMING_SHI =530102;
    private static final int MAP_SIZE =32;
    private static final int ZERO=0;
    private static final String NOT="0";
    private static final int ONE=1;
    private static final int TWO=2;
    private static final int TEN=10;
    private static final Long FIFTEEN_DAY= 15L;

    private static final String DEFAULT_PERCENTAGE="0.00%";


    @Resource
    private DistributionOfGoodsServiceMapper distributionOfGoodsServiceMapper;
    @Resource
    private IProductInfoService productInfoService;
    @Resource
    private IAreaInfoService areaInfoService;
    @Resource
    private DistributionOfGoodsMapper distributionOfGoodsMapper;
    @Resource
    private AbstractCurrentRequestComponent requestComponent;
    @Resource
    private MqUtils mqUtils;


    /**
     * 批量调货功能
     * @param list
     * @return
     */
    @Override
    public R<Boolean> automaticGoodsTransfer(List<AutomaticGoodsTransferVO> list) {
        OaUserBO oaUserBO = Optional.ofNullable(requestComponent.getCurrentStaffId())
                .orElseThrow(()->new CustomizeException("当前登录信息不能为空"));
        if(CollectionUtils.isEmpty(list)){
            throw new CustomizeException("调拨信息不能为空");
        }
        list.forEach((AutomaticGoodsTransferVO item)->{
            if(item.getPpid()==null){
                throw new CustomizeException("skuId不能为空");
            }
            if(item.getCount()==null){
                throw new CustomizeException("调拨数量不能空");
            }
            if(item.getFromAreaId()==null){
                throw new CustomizeException("调出地区不能为空");
            }
            if(item.getToAreaId()==null){
                throw new CustomizeException("调入地区不能为空");
            }
            if(item.getMinimumGuarantee()==null){
                throw new CustomizeException("保底数量不能为空");
            }
        });
        String userName = Optional.ofNullable(oaUserBO.getUserName()).orElseThrow(() -> new CustomizeException("登录信息异常，请重新登录"));
        //构造MQ参数
        GoodsTransferMq goodsTransferMq = new GoodsTransferMq();
        GoodsTransferData data = new GoodsTransferData();
        data.setItems(list).setEmployeeName(userName);
        goodsTransferMq.setAct(TRANSFER_BATCH).setData(data);
        //MQ推送信息
        R<Boolean> booleanR = mqUtils.goodsTransferMqSend(JSONUtil.toJsonStr(goodsTransferMq));
        return booleanR;
    }


    /**
     * 查询商品树状结构
     * @param searchCriteriaVO
     * @return
     */
    @Override
    public R<TreeVo> getProportionOfSalesVolume(SearchCriteriaVO searchCriteriaVO) {
        checkDate(searchCriteriaVO);
        List<ProductDetailVO> productDetailVoS = distributionOfGoodsServiceMapper.selectReportFormProduct(searchCriteriaVO);
        if(CollectionUtils.isEmpty(productDetailVoS)){
            return R.success(new TreeVo());
        }
        TreeVo tree=new TreeVo();
        List<Integer> ppidList = productDetailVoS.stream().map(ProductDetailVO::getPpriceid).collect(Collectors.toList());
        HashMap<Integer, ProportionOfSalesVolumeVO> map = new HashMap<>(MAP_SIZE);
        //销量设置以及地区逻辑的设置
        setSale(ppidList,map,searchCriteriaVO,searchCriteriaVO.getAreaIdList());
        //如果过查询不到地区的信息那就直接返回数据
        if(map.isEmpty()){
            return R.success(tree);
        }
        //设置库存部分
        setStock(ppidList,map);
        //设置现货率
        Map<Integer, GoodsInStockVO> goodsInStockMap = setGoodsInStockRatio(ppidList, map, searchCriteriaVO, searchCriteriaVO.getAreaIdList());
        //设置门店信息
        setAreaInfo(map);
        //聚合数据
        List<ProportionOfSalesVolumeVO> encapsulation = encapsulation(map, goodsInStockMap);
        //给聚合数据设置等级
        createEncapsulation(encapsulation);
        //获取到行政地区的树形统计数据
        ArrayList<ProportionOfSalesVolumeVO> proportionOfSalesVolumeVoS = new ArrayList<>(map.values());
        //把为空的数据修改为0
        fixDate(proportionOfSalesVolumeVoS);
        //根据cid进行分组合并，并且对于昆明市区的合并处理
        Map<Integer, List<ProportionOfSalesVolumeVO>> cidMap = getCidMap(proportionOfSalesVolumeVoS);
        tree = createTree(encapsulation,cidMap);
        return R.success(tree);
    }

    /**
     * 根据cid进行分组合并，并且对于昆明市区的合并处理
     * @param proportionOfSalesVolumeVoS
     * @return
     */
    private  Map<Integer, List<ProportionOfSalesVolumeVO>> getCidMap( ArrayList<ProportionOfSalesVolumeVO> proportionOfSalesVolumeVoS){
        Map<Integer, List<ProportionOfSalesVolumeVO>> cidMap = proportionOfSalesVolumeVoS.stream().collect(Collectors.groupingBy(ProportionOfSalesVolumeVO::getCid));
        ArrayList<ProportionOfSalesVolumeVO> salesVolumeVOArrayList = new ArrayList<>();
        for (Integer item: kunMingList) {
            List<ProportionOfSalesVolumeVO> list = cidMap.get(item);
            if(!CollectionUtils.isEmpty(list)){
                salesVolumeVOArrayList.addAll(list);
                cidMap.remove(item);
            }
        }
        if(!CollectionUtils.isEmpty(salesVolumeVOArrayList)){
            cidMap.put(KUNMING_SHI,salesVolumeVOArrayList);
        }
         return cidMap;
    }


    /**
     * 把为空的数据全部转换成O
     * @param proportionOfSalesVolumeVoS
     */
    private void fixDate(ArrayList<ProportionOfSalesVolumeVO> proportionOfSalesVolumeVoS){
        proportionOfSalesVolumeVoS.forEach((ProportionOfSalesVolumeVO item)->{
            if(item.getDaySales()==null){
                item.setDaySales(ZERO);
            }
            if(StringUtils.isEmpty(item.getDaySalesRatio())){
                item.setDaySalesRatio(DEFAULT_PERCENTAGE);
            }
            if(item.getDaySearchSales()==null){
                item.setDaySearchSales(ZERO);
            }
            if(StringUtils.isEmpty(item.getDaySearchSalesRatio())){
                item.setDaySearchSalesRatio(DEFAULT_PERCENTAGE);
            }
            if(item.getDayFifteenSales()==null){
                item.setDayFifteenSales(ZERO);
            }
            if(StringUtils.isEmpty(item.getDayFifteenSalesRatio())){
                item.setDayFifteenSalesRatio(DEFAULT_PERCENTAGE);
            }
            if(item.getTotal()==null){
                item.setTotal(ZERO);
            }
            if(item.getStock()==null){
                item.setStock(ZERO);
            }
            if(item.getOnTheWay()==null){
                item.setOnTheWay(ZERO);
            }
            if(StringUtils.isEmpty(item.getGoodsInStockRatio())){
                item.setGoodsInStockRatio(DEFAULT_PERCENTAGE);
            }
        });
    }

    /**
     * 递归获取到行政地区的树形统计数据
     * @param encapsulation
     * @return
     */
    private TreeVo createTree(List<ProportionOfSalesVolumeVO> encapsulation, Map<Integer, List<ProportionOfSalesVolumeVO>> cidMap ){
        TreeVo treeVo = new TreeVo();
        treeVo.setCode(ZERO);
        Integer flag = ZERO;
        List<TreeVo> data = getData(encapsulation, treeVo.getCode(),cidMap,flag);
        List<TreeVo> collectSort = data.stream().sorted(Comparator.comparing(TreeVo::getRank)).collect(Collectors.toList());
        treeVo.setChildren(collectSort);
        return treeVo;
    }

    /**
     * 递归获取数据
     * @param encapsulation
     * @param parentCode
     * @param cidMap
     * @param flag
     * @return
     */
    private List<TreeVo> getData(List<ProportionOfSalesVolumeVO> encapsulation,Integer parentCode,Map<Integer, List<ProportionOfSalesVolumeVO>> cidMap,Integer flag){
        //递归保护
        ++flag;
        if(flag>TEN){
            throw new CustomizeException("门店父子级递归异常");
        }
        List<TreeVo> treeVos = new ArrayList<>();
        List<ProportionOfSalesVolumeVO> collect = encapsulation.stream()
                .filter((ProportionOfSalesVolumeVO item) -> parentCode.equals(item.getParentCode())).collect(Collectors.toList());
        for (ProportionOfSalesVolumeVO item: collect) {
            TreeVo treeVo = distributionOfGoodsMapper.toTreeVo(item);
            treeVo.setUuid(UUID.randomUUID().toString());
            Integer cid = Optional.ofNullable(treeVo.getCid()).orElse(Integer.MAX_VALUE);
            List<ProportionOfSalesVolumeVO> proportionOfSalesVolumeVOS = cidMap.get(cid);
            if(!CollectionUtils.isEmpty(proportionOfSalesVolumeVOS)){
                List<TreeVo> collect1 = proportionOfSalesVolumeVOS.stream().map(distributionOfGoodsMapper::toTreeVo).collect(Collectors.toList());
                collect1.forEach((TreeVo obj)->obj.setUuid(UUID.randomUUID().toString()));
                List<TreeVo> collectSort = collect1.stream().sorted(Comparator.comparing(TreeVo::getDaySales).reversed().thenComparing(TreeVo::getRank)).collect(Collectors.toList());
                treeVo.setChildren(collectSort);
            }

            if(item.getParentCode().equals(parentCode)){
                List<TreeVo> data = getData(encapsulation, treeVo.getCode(),cidMap,flag);
                List<TreeVo> collectSort = data.stream().sorted(Comparator.comparing(TreeVo::getRank)).collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(collectSort)){
                    treeVo.setChildren(collectSort);
                }
                 treeVos.add(treeVo);
            }
        }
      return treeVos;
    }

    /**
     * 给聚合数据设置等级
     * @param encapsulation
     */
    private void createEncapsulation(List<ProportionOfSalesVolumeVO> encapsulation){
        ArrayList<Integer> codeList = new ArrayList<>();
        encapsulation.forEach((ProportionOfSalesVolumeVO item)->{
            codeList.add(item.getCid());
            codeList.add(item.getPid());
            codeList.add(item.getZid());
        });
        List<Integer> collect = codeList.stream().distinct().collect(Collectors.toList());
        List<AreaInfoVO> areaInfoVOS = distributionOfGoodsServiceMapper.selectAreaInfo(collect);
        Map<Integer, AreaInfoVO> areaInfoMap = areaInfoVOS.stream().collect(Collectors.toMap(AreaInfoVO::getCode, Function.identity(), (n1, n2) -> n2));
        encapsulation.forEach((ProportionOfSalesVolumeVO item)->{
            boolean pid = item.getPid() == null;
            boolean zid = item.getZid() == null;
            boolean cid = item.getCid() == null;
            AreaInfoVO areaInfoVO=new AreaInfoVO();
            if(!pid&&zid&&cid){
                areaInfoVO = areaInfoMap.getOrDefault(item.getPid(),new AreaInfoVO());

            }
            if(!pid&&!zid&&cid){
                areaInfoVO = areaInfoMap.getOrDefault(item.getZid(),new AreaInfoVO());
            }
            if(!pid&&!zid&&!cid){
                areaInfoVO = areaInfoMap.getOrDefault(item.getCid(),new AreaInfoVO());
            }
            item.setCode(areaInfoVO.getCode())
                    .setRank(areaInfoVO.getRank())
                    .setParentCode(areaInfoVO.getParentCode());
            if(KUNMING_SHI.equals(areaInfoVO.getCode())){
                item.setAreaName("昆明市区");
            } else {
                item.setAreaName(areaInfoVO.getName());
            }
        });

    }

    /**
     * 聚合数据
     * @param map
     * @param goodsInStockMap
     * @return
     */
    private List<ProportionOfSalesVolumeVO> encapsulation(HashMap<Integer, ProportionOfSalesVolumeVO> map,Map<Integer, GoodsInStockVO> goodsInStockMap){
        List<ProportionOfSalesVolumeVO> list = new ArrayList<>(map.values());
        List<ProportionOfSalesVolumeVO> proportionOfSalesVolumeVOS = new ArrayList<>();
        if(!CollectionUtils.isEmpty(list)){
            int totalDaySum = list.stream().mapToInt((ProportionOfSalesVolumeVO item)-> Optional.ofNullable(item.getDaySales()).orElse(ZERO)).sum();
            int totalDaySearchSum = list.stream().mapToInt((ProportionOfSalesVolumeVO item)-> Optional.ofNullable(item.getDaySearchSales()).orElse(ZERO)).sum();
            int totalDayFifteenSum =  list.stream().mapToInt((ProportionOfSalesVolumeVO item)-> Optional.ofNullable(item.getDayFifteenSales()).orElse(ZERO)).sum();

            //按照省来聚合
            Map<Integer, List<ProportionOfSalesVolumeVO>> pidMap = list.stream().collect(Collectors.groupingBy(ProportionOfSalesVolumeVO::getPid));
            List<ProportionOfSalesVolumeVO> polymerizationPid = polymerization(pidMap, totalDaySum, totalDaySearchSum, totalDayFifteenSum, TYPE_PID, goodsInStockMap);
            proportionOfSalesVolumeVOS.addAll(polymerizationPid);
            //按照市来聚合
            Map<Integer, List<ProportionOfSalesVolumeVO>> zidMap = list.stream().collect(Collectors.groupingBy(ProportionOfSalesVolumeVO::getZid));
            List<ProportionOfSalesVolumeVO> polymerizationZid = polymerization(zidMap, totalDaySum, totalDaySearchSum, totalDayFifteenSum, TYPE_ZID, goodsInStockMap);
            proportionOfSalesVolumeVOS.addAll(polymerizationZid);
            //按照区来聚合(区的进行处理，需要把530102,530103,530111,530112四个区合并成昆明市区)
            Map<Integer, List<ProportionOfSalesVolumeVO>> cidMap = list.stream().collect(Collectors.groupingBy(ProportionOfSalesVolumeVO::getCid));
            if(!CollectionUtils.isEmpty(cidMap)){
                ArrayList<ProportionOfSalesVolumeVO> arrayList = new ArrayList<>();
                kunMingList.forEach((Integer item)->{
                    List<ProportionOfSalesVolumeVO> proportionOfSalesVolumeVoS1 = cidMap.get(item);
                    if(!CollectionUtils.isEmpty(proportionOfSalesVolumeVoS1)){
                        arrayList.addAll(proportionOfSalesVolumeVoS1);
                        cidMap.remove(item);
                    }
                });
                cidMap.put(KUNMING_SHI,arrayList);
            }
            List<ProportionOfSalesVolumeVO> polymerizationCid = polymerization(cidMap, totalDaySum, totalDaySearchSum, totalDayFifteenSum, TYPE_CID, goodsInStockMap);
            proportionOfSalesVolumeVOS.addAll(polymerizationCid);
        }
        return proportionOfSalesVolumeVOS;
    }

    /**
     * 聚合数据
     * @param map
     * @param totalDaySum
     * @param totalDaySearchSum
     * @param totalDayFifteenSum
     * @param type
     * @param goodsInStockMap
     * @return
     */
    private List<ProportionOfSalesVolumeVO> polymerization(Map<Integer, List<ProportionOfSalesVolumeVO>> map,int totalDaySum,int totalDaySearchSum,int totalDayFifteenSum,String type,Map<Integer, GoodsInStockVO> goodsInStockMap){
        List<ProportionOfSalesVolumeVO> proportionOfSalesVolumeVOS = new ArrayList<>();
        //获取区域等级信息
        if(!CollectionUtils.isEmpty(map)){
            DecimalFormat df2 = new DecimalFormat(DEFAULT_PERCENTAGE);
            for (Map.Entry<Integer, List<ProportionOfSalesVolumeVO>> item: map.entrySet()) {
                List<ProportionOfSalesVolumeVO> list = item.getValue();
                if(CollectionUtils.isEmpty(list)){
                    continue;
                }
                int daySum = list.stream().mapToInt((ProportionOfSalesVolumeVO obj)->Optional.ofNullable(obj.getDaySales()).orElse(ZERO)).sum();
                int daySearchSum = list.stream().mapToInt((ProportionOfSalesVolumeVO obj)->Optional.ofNullable(obj.getDaySearchSales()).orElse(ZERO)).sum();
                int dayFifteenSum =  list.stream().mapToInt((ProportionOfSalesVolumeVO obj)->Optional.ofNullable(obj.getDayFifteenSales()).orElse(ZERO)).sum();
                //设置聚合销售
                ProportionOfSalesVolumeVO proportionOfSalesVolumeVO = new ProportionOfSalesVolumeVO();
                BigDecimal divideDay = BigDecimal.valueOf(daySum).divide(BigDecimal.valueOf(totalDaySum==ZERO?ONE:totalDaySum), TWO, RoundingMode.HALF_UP);
                BigDecimal divideDaySearch = BigDecimal.valueOf(daySearchSum).divide(BigDecimal.valueOf(totalDaySearchSum==ZERO?ONE:totalDaySearchSum), TWO, RoundingMode.HALF_UP);
                BigDecimal divideDayFifteen = BigDecimal.valueOf(dayFifteenSum).divide(BigDecimal.valueOf(totalDayFifteenSum==ZERO?ONE:totalDayFifteenSum), TWO, RoundingMode.HALF_UP);
                proportionOfSalesVolumeVO.setDaySales(daySum).setDaySalesRatio(df2.format(divideDay))
                        .setDayFifteenSales(dayFifteenSum).setDayFifteenSalesRatio(df2.format(divideDaySearch))
                        .setDaySearchSales(daySearchSum).setDaySearchSalesRatio(df2.format(divideDayFifteen));
                //设置库存、在途、总量
                int stockSum = list.stream().mapToInt((ProportionOfSalesVolumeVO obj)->Optional.ofNullable(obj.getStock()).orElse(ZERO)).sum();
                int onTheWaySum = list.stream().mapToInt((ProportionOfSalesVolumeVO obj)->Optional.ofNullable(obj.getOnTheWay()).orElse(ZERO)).sum();
                int totalSum = list.stream().mapToInt((ProportionOfSalesVolumeVO obj)->Optional.ofNullable(obj.getTotal()).orElse(ZERO)).sum();
                proportionOfSalesVolumeVO.setStock(stockSum).setOnTheWay(onTheWaySum).setTotal(totalSum);
                //设置聚合的现货率
                AtomicReference<Integer> totalAllCount= new AtomicReference<>(ZERO);
                AtomicReference<Integer> totalKcCount= new AtomicReference<>(ZERO);
                if(!CollectionUtils.isEmpty(list)){
                    for (ProportionOfSalesVolumeVO obj : list) {
                        Integer areaId = obj.getAreaId();
                        Optional.ofNullable(goodsInStockMap.get(areaId)).ifPresent((GoodsInStockVO goods)->{
                            Integer allCount = Optional.ofNullable(goods.getAllCount()).orElse(ZERO);
                            Integer kcCount = Optional.ofNullable(goods.getKcCount()).orElse(ZERO);
                            totalAllCount.updateAndGet(v -> v + allCount);
                            totalKcCount.updateAndGet(v -> v + kcCount);
                        });
                    }
                }
                BigDecimal goodsInStock = BigDecimal.valueOf(totalKcCount.get()).divide(BigDecimal.valueOf(totalAllCount.get()==ZERO?ONE:totalAllCount.get()), TWO, RoundingMode.HALF_UP);
                proportionOfSalesVolumeVO.setGoodsInStockRatio(df2.format(goodsInStock));
                //聚合省份数据
                if(TYPE_PID.equals(type)){
                    proportionOfSalesVolumeVO.setPid(item.getKey());
                }
                //聚合市数据
                if(TYPE_ZID.equals(type)){
                    proportionOfSalesVolumeVO.setZid(item.getKey())
                            .setPid(list.get(ZERO).getPid());
                }
                if(TYPE_CID.equals(type)){
                    proportionOfSalesVolumeVO.setCid(item.getKey())
                            .setZid(list.get(ZERO).getZid())
                            .setPid(list.get(ZERO).getPid());
                }
                proportionOfSalesVolumeVOS.add(proportionOfSalesVolumeVO);
            }
        }
        return proportionOfSalesVolumeVOS;
    }

    /**
     * 设置门店信息
     * @param map
     */
    private void setAreaInfo(HashMap<Integer, ProportionOfSalesVolumeVO> map){
        ArrayList<ProportionOfSalesVolumeVO> proportionOfSalesVolumeVOS = new ArrayList<>(map.values());
        if(!CollectionUtils.isEmpty(proportionOfSalesVolumeVOS)){
            List<Integer> areaIds = proportionOfSalesVolumeVOS.stream().map(ProportionOfSalesVolumeVO::getAreaId).collect(Collectors.toList());
            List<Areainfo> list = areaInfoService.lambdaQuery().in(Areainfo::getId, areaIds).select().list();
            if(!CollectionUtils.isEmpty(list)){
                list.forEach((Areainfo item)->{
                    Integer areaId = item.getId();
                    Optional.ofNullable(map.get(areaId)).ifPresent((ProportionOfSalesVolumeVO obj)->{
                        String value = AreaLevelEnum.getValue(Optional.ofNullable(item.getLevel1()).orElse(Integer.MAX_VALUE));
                        String area = item.getArea();
                        if(StringUtils.isEmpty(value)){
                            obj.setAreaName(area);
                        }else {
                            obj.setAreaName(area+"("+value+")");
                        }
                        obj.setRank(item.getRank());
                        obj.setPid(item.getPid()).setCid(item.getCityid()).setZid(item.getZid());
                    });
                });
            }
        }
    }

    /**
     * 设置现货率
     * @param ppidList
     * @param map
     * @param searchCriteriaVO
     * @param dropDownAreaIdList
     */
    private Map<Integer, GoodsInStockVO> setGoodsInStockRatio(List<Integer> ppidList,HashMap<Integer, ProportionOfSalesVolumeVO> map,SearchCriteriaVO searchCriteriaVO,List<Integer> dropDownAreaIdList){
        SearchGoodsInStockVO searchGoodsInStockVO = new SearchGoodsInStockVO();
        searchGoodsInStockVO.setPpidList(ppidList)
                .setAttributeList(searchCriteriaVO.getAttributeList())
                .setKind1List(searchCriteriaVO.getKind1List())
                .setDropDownAreaIdList(dropDownAreaIdList)
                .setSalesStartTime(searchCriteriaVO.getSalesStartTime())
                .setSalesEndTime(searchCriteriaVO.getSalesEndTime());
        List<GoodsInStockVO> goodsInStockVOS = distributionOfGoodsServiceMapper.selectGoodsInStock(searchGoodsInStockVO);
        Map<Integer, GoodsInStockVO> goodsInStockMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(goodsInStockVOS)){
            DecimalFormat df2 = new DecimalFormat(DEFAULT_PERCENTAGE);
            goodsInStockVOS.forEach((GoodsInStockVO item)->{
                Integer areaId = item.getAreaId();
                Integer allCount = Optional.ofNullable(item.getAllCount()).orElse(ZERO);
                Integer kcCount = Optional.ofNullable(item.getKcCount()).orElse(ZERO);
                BigDecimal divide = BigDecimal.valueOf(kcCount).divide(BigDecimal.valueOf(allCount), TWO, RoundingMode.HALF_UP);
                ProportionOfSalesVolumeVO proportionOfSalesVolumeVO = map.get(areaId);
                if(proportionOfSalesVolumeVO!=null){
                    proportionOfSalesVolumeVO.setGoodsInStockRatio(df2.format(divide));
                } else {
                    ProportionOfSalesVolumeVO proportionOfSalesVolumeVO1 = new ProportionOfSalesVolumeVO();
                    proportionOfSalesVolumeVO1.setAreaId(areaId)
                            .setGoodsInStockRatio(df2.format(divide));
                    map.put(areaId,proportionOfSalesVolumeVO1);
                }
            });
            goodsInStockMap = goodsInStockVOS.stream().collect(Collectors.toMap(GoodsInStockVO::getAreaId, Function.identity(),(n1,n2)->n2));
        }
        return goodsInStockMap;
    }


    /**
     * 设置库存部分
     * @param ppidList
     * @param map
     */
    private void setStock(List<Integer> ppidList,HashMap<Integer, ProportionOfSalesVolumeVO> map){
        DropDownDetailVO dropDownDetailVO = new DropDownDetailVO();
        dropDownDetailVO.setPpidList(ppidList)
                .setKcCheckList(Collections.singletonList(KcCheckEnum.ON_WAY.getCode()));
        if(!map.isEmpty()){
            ArrayList<Integer> areaIdlist = new ArrayList<>();
            map.values().forEach((ProportionOfSalesVolumeVO item)->{
                Integer areaId = item.getAreaId();
                areaIdlist.add(areaId);
            });
            dropDownDetailVO.setTransferOutAreaIdList(areaIdlist);
        }
        //在途库存
        dropDownDetailVO.setType(KcCheckEnum.ON_WAY.getCode());
        List<StoreSales> storeSales = distributionOfGoodsServiceMapper.selectDropDownStock(dropDownDetailVO);
        if(!CollectionUtils.isEmpty(storeSales)){
            storeSales.forEach((StoreSales item)->{
                Integer areaId = item.getAreaId();
                ProportionOfSalesVolumeVO proportionOfSalesVolumeVO = map.get(areaId);
                Integer storeSum = Optional.ofNullable(item.getStoreSum()).orElse(ZERO);
                if(proportionOfSalesVolumeVO!=null){
                    proportionOfSalesVolumeVO.setOnTheWay(storeSum).setTotal(storeSum);
                } else {
                    ProportionOfSalesVolumeVO proportionOfSalesVolumeVo1 = new ProportionOfSalesVolumeVO();
                    proportionOfSalesVolumeVo1.setAreaId(areaId).setTotal(storeSum).setStock(storeSum);
                    map.put(areaId,proportionOfSalesVolumeVo1);
                }
            });
        }
        //库存、已到货
        dropDownDetailVO.setType(KcCheckEnum.STOCK.getCode());
        dropDownDetailVO.setKcCheckList(Arrays.asList(KcCheckEnum.STOCK.getCode(),KcCheckEnum.ARRIVED.getCode()));
        List<StoreSales> storeSales1 = distributionOfGoodsServiceMapper.selectDropDownStock(dropDownDetailVO);
        if(!CollectionUtils.isEmpty(storeSales1)){
            storeSales1.forEach((StoreSales item)->{
                Integer areaId = item.getAreaId();
                ProportionOfSalesVolumeVO proportionOfSalesVolumeVO = map.get(areaId);
                Integer storeSum = Optional.ofNullable(item.getStoreSum()).orElse(ZERO);
                if(proportionOfSalesVolumeVO!=null){
                    int total = Optional.ofNullable(proportionOfSalesVolumeVO.getTotal()).orElse(ZERO) + Optional.ofNullable(item.getStoreSum()).orElse(ZERO);
                    proportionOfSalesVolumeVO.setStock(storeSum).setTotal(total);
                } else {
                    ProportionOfSalesVolumeVO proportionOfSalesVolumeVo1 = new ProportionOfSalesVolumeVO();
                    proportionOfSalesVolumeVo1.setAreaId(areaId).setStock(storeSum).setTotal(storeSum);
                    map.put(areaId,proportionOfSalesVolumeVo1);
                }
            });
        }

    }
    /**
     * 设置销量数据
     * @param ppidList
     * @param map
     * @param searchCriteriaVO
     */
    private void setSale(List<Integer> ppidList,HashMap<Integer, ProportionOfSalesVolumeVO> map,SearchCriteriaVO searchCriteriaVO,List<Integer> dropDownAreaIdList){
        //获取到当天时间00：00：00-24：00：00
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime toDayStart = LocalDateTime.of(now.toLocalDate(), LocalTime.MIN);
        LocalDateTime toDayEnd = LocalDateTime.of(now.toLocalDate(), LocalTime.MAX);
        List<StoreSales> searchStoreSalesListToDay = getSearchStoreSalesList(ppidList, toDayStart, toDayEnd,dropDownAreaIdList,searchCriteriaVO);
        DecimalFormat df2 = new DecimalFormat(DEFAULT_PERCENTAGE);
        if(!CollectionUtils.isEmpty(searchStoreSalesListToDay)){
            Integer sum = searchStoreSalesListToDay.stream().mapToInt(StoreSales::getStoreSum).sum();
            searchStoreSalesListToDay.forEach((StoreSales item)->{
                Integer areaId = item.getAreaId();
                Integer storeSum =Optional.ofNullable(item.getStoreSum()).orElse(ZERO);
                BigDecimal divide = BigDecimal.valueOf(storeSum).divide(BigDecimal.valueOf(sum==ZERO?ONE:sum), TWO, RoundingMode.HALF_UP);
                ProportionOfSalesVolumeVO proportionOfSalesVolumeVO = new ProportionOfSalesVolumeVO();
                proportionOfSalesVolumeVO.setAreaId(areaId)
                        .setDaySales(storeSum)
                        .setDaySalesRatio(df2.format(divide));
                map.put(areaId,proportionOfSalesVolumeVO);
            });
        }
        //获取当前搜索时间
        List<StoreSales> searchStoreSalesListSearchDay = getSearchStoreSalesList(ppidList, searchCriteriaVO.getSalesStartTime(), searchCriteriaVO.getSalesEndTime(),dropDownAreaIdList,searchCriteriaVO);
        if(!CollectionUtils.isEmpty(searchStoreSalesListSearchDay)){
            Integer sum = searchStoreSalesListSearchDay.stream().mapToInt(StoreSales::getStoreSum).sum();
            searchStoreSalesListSearchDay.forEach((StoreSales item)->{
                Integer areaId = item.getAreaId();
                Integer storeSum = Optional.ofNullable(item.getStoreSum()).orElse(ZERO);
                BigDecimal divide = BigDecimal.valueOf(storeSum).divide(BigDecimal.valueOf(sum==ZERO?ONE:sum), TWO, RoundingMode.HALF_UP);
                ProportionOfSalesVolumeVO proportionOfSalesVolumeVo1 = map.get(areaId);
                if(proportionOfSalesVolumeVo1==null){
                    ProportionOfSalesVolumeVO proportionOfSalesVolumeVO = new ProportionOfSalesVolumeVO();
                    proportionOfSalesVolumeVO.setAreaId(areaId)
                            .setDaySearchSales(storeSum)
                            .setDaySearchSalesRatio(df2.format(divide));
                    map.put(areaId,proportionOfSalesVolumeVO);
                } else {
                    proportionOfSalesVolumeVo1.setDaySearchSales(item.getStoreSum())
                            .setDaySearchSalesRatio(df2.format(divide));
                }
            });
        }

        //获取当前时间以及15天之前
        List<StoreSales> searchStoreSalesListFifteenDay = getSearchStoreSalesList(ppidList, now.minusDays(FIFTEEN_DAY), now,dropDownAreaIdList,searchCriteriaVO);
        if(!CollectionUtils.isEmpty(searchStoreSalesListFifteenDay)){
            Integer sum = searchStoreSalesListFifteenDay.stream().mapToInt(StoreSales::getStoreSum).sum();
            searchStoreSalesListFifteenDay.forEach((StoreSales item)->{
                Integer areaId = item.getAreaId();
                Integer storeSum = Optional.ofNullable(item.getStoreSum()).orElse(ZERO);
                BigDecimal divide = BigDecimal.valueOf(storeSum).divide(BigDecimal.valueOf(sum==ZERO?ONE:sum), TWO, RoundingMode.HALF_UP);
                ProportionOfSalesVolumeVO proportionOfSalesVolumeVo2 = map.get(areaId);
                if(proportionOfSalesVolumeVo2==null){
                    ProportionOfSalesVolumeVO proportionOfSalesVolumeVO = new ProportionOfSalesVolumeVO();
                    proportionOfSalesVolumeVO.setAreaId(areaId)
                            .setDayFifteenSales(storeSum)
                            .setDayFifteenSalesRatio(df2.format(divide));
                    map.put(areaId,proportionOfSalesVolumeVO);
                } else {
                    proportionOfSalesVolumeVo2.setDayFifteenSales(item.getStoreSum())
                            .setDayFifteenSalesRatio(df2.format(divide));
                }
            });
        }
    }

    private List<StoreSales> getSearchStoreSalesList(List<Integer> ppidList,LocalDateTime toDayStart,LocalDateTime toDayEnd,List<Integer> dropDownAreaIdList,SearchCriteriaVO searchCriteriaVO){
        SearchStoreSales searchStoreSales = new SearchStoreSales();
        searchStoreSales.setSalesStartTime(toDayStart)
                .setAreaIdList(dropDownAreaIdList)
                .setKind1List(searchCriteriaVO.getKind1List())
                .setAttributeList(searchCriteriaVO.getAttributeList())
                .setPpidList(ppidList)
                .setSalesEndTime(toDayEnd);
//        List<StoreSales> storeSales1 = distributionOfGoodsServiceMapper.selectStoreSales(searchStoreSales);
        List<StoreSales> storeSales = getStoreSales(searchStoreSales);
        //如果过在没有选择门店过滤的情况下
         List<Integer> kind1List = searchCriteriaVO.getKind1List();
        List<Integer> areaIdList = searchCriteriaVO.getAreaIdList();
        List<Integer> attributeList = searchCriteriaVO.getAttributeList();
        //当区域门店都为空的情况那就查询所有的门店
        List<Integer> areaIds= new ArrayList<>();
        if(CollectionUtils.isEmpty(areaIdList)){
            if(!CollectionUtils.isEmpty(storeSales)){
                areaIds = storeSales.stream().map(StoreSales::getAreaId).collect(Collectors.toList());
            }
            List<StoreSales> areaList = distributionOfGoodsServiceMapper.selectStoreSalesNoAreaIds(areaIds,kind1List,attributeList);
            storeSales.addAll(areaList);
        }
        //当区域门不为空的时候那就查询门店的信息
        if(!CollectionUtils.isEmpty(areaIdList)){
            if(!CollectionUtils.isEmpty(storeSales)){
                areaIds = storeSales.stream().map(StoreSales::getAreaId).collect(Collectors.toList());
            }
            areaIdList.removeAll(areaIds);
            List<StoreSales> areaList = distributionOfGoodsServiceMapper.selectStoreSalesAreaIds(areaIdList,kind1List,attributeList);
            storeSales.addAll(areaList);
        }
        return storeSales ;
    }
    private List<StoreSales> getStoreSales(SearchStoreSales searchStoreSales){
        ArrayList<StoreSales> storeSales = new ArrayList<>();
        List<StoreSalesV2> storeSalesV2 = distributionOfGoodsServiceMapper.selectStoreSalesV2(searchStoreSales);
        if(CollectionUtils.isEmpty(storeSalesV2)){
            return storeSales;
        }
        //按照地区进行分组
        Map<Integer, List<StoreSalesV2>> areaMap = storeSalesV2.stream().collect(Collectors.groupingBy(StoreSalesV2::getAreaId));
        for (Map.Entry<Integer, List<StoreSalesV2>> item: areaMap.entrySet()) {
            StoreSales sales = new StoreSales();
            sales.setAreaId(item.getKey());
            List<StoreSalesV2> value = item.getValue();
            AtomicReference<Integer> storeSum= new AtomicReference<>(0);
            value.forEach((StoreSalesV2 obj)->{
                Boolean mobile = obj.getIsmobile();
                if(mobile){
                    storeSum.getAndSet(storeSum.get() + 1);
                }else {
                    storeSum.set(storeSum.get() + obj.getBasketCount());
                }
            });
            sales.setStoreSum(storeSum.get());
            storeSales.add(sales);
        }
        return storeSales;
    }




    @Override
    public R<List<ProductDetailVO>> getReportFormProduct(SearchCriteriaVO searchCriteriaVO) {
        //数据校验
        checkDate(searchCriteriaVO);
        List<ProductDetailVO> productDetailVOS = distributionOfGoodsServiceMapper.selectReportFormProduct(searchCriteriaVO);
        return R.success(productDetailVOS);
    }

    @Override
    public R<List<StockDetailVO>> getReportFormStock(SearchStockDetailVO searchStockDetailVO) {
        Optional.ofNullable(searchStockDetailVO.getTransferOutAreaId())
                .orElseThrow(() -> new CustomizeException("转出地区不能为空"));
        List<Integer> ppidList = searchStockDetailVO.getPpidList();
        if(CollectionUtils.isEmpty(ppidList)){
            throw new CustomizeException("查询sku_id不能为空");
        }
        List<StockDetailVO> stockDetailOnWay = distributionOfGoodsServiceMapper.selectReportFormStockV2(searchStockDetailVO);
        //计算库存总量
        if(!CollectionUtils.isEmpty(stockDetailOnWay)){
            stockDetailOnWay.forEach((StockDetailVO item)->{
                Integer stockCount = Optional.ofNullable(item.getStockCount()).orElse(ZERO);
                item.setStockCount(stockCount);
                Integer stockCountOnTheWay = Optional.ofNullable(item.getStockCountOnTheWay()).orElse(ZERO);
                item.setStockCountOnTheWay(stockCountOnTheWay);
                item.setTotalStockCount(stockCount+stockCountOnTheWay);
            });
        }
        return R.success(stockDetailOnWay);
    }



    private void checkDate(SearchCriteriaVO searchCriteriaVO){

        Integer searchTermType = searchCriteriaVO.getSearchTermType();
        if(searchTermType==null){
            throw new CustomizeException("查询类型不能为空");
        }
        List<Long> searchTermValue = searchCriteriaVO.getSearchTermValue();
        if(CollectionUtils.isEmpty(searchTermValue)){
            throw new CustomizeException("sku_id或者商品id不能为空");
        }
        if(searchTermValue.size()>MAX_SEARCHTERMVALUE){
            throw new CustomizeException("最多输入20个sku或商品id");
        }
        //开始时间
        LocalDateTime salesStartTime = Optional.ofNullable(searchCriteriaVO.getSalesStartTime()).orElseThrow(()->new CustomizeException("销售开始时间不能为空"));
        LocalDateTime salesEndTime = Optional.ofNullable(searchCriteriaVO.getSalesEndTime()).orElseThrow(()->new CustomizeException("销售结束时间不能为空"));
        if( salesStartTime.compareTo(salesEndTime)>ZERO){
            throw new CustomizeException("开始时间不能大于结束时间");
        }
        LocalDateTime now = LocalDateTime.now();
        if(ZERO>salesStartTime.compareTo(now.minusMonths(MAX_MONTH))){
            throw new CustomizeException("不能查询3个月之前的数据");
        }
        List<ProductInfoEntity> list=new ArrayList<>();
        if(searchTermType.equals(SearchTermEnum.SELF_EMPLOYED.getCode())){
            list = productInfoService.lambdaQuery().in(ProductInfoEntity::getPpid, searchTermValue)
                    .select(ProductInfoEntity::getPpid, ProductInfoEntity::getIsMobile).list();
        }
        if(searchTermType.equals(SearchTermEnum.JOIN.getCode())){
            list = productInfoService.lambdaQuery().in(ProductInfoEntity::getProductId, searchTermValue)
                    .select(ProductInfoEntity::getPpid, ProductInfoEntity::getIsMobile).list();
        }
        //判断是否为大件
        if(!CollectionUtils.isEmpty(list)){
            list.forEach((ProductInfoEntity item)->{
                String isMobile = Optional.ofNullable(item.getIsMobile()).orElse(NOT);
                if(isMobile.equals(NOT)){
                    throw new CustomizeException("sku_id为"+item.getPpid()+"的商品不是大件商品");
                }
            });
        }
    }
}
