package com.jiuji.oa.stock.authorizationtransfer.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jiuji.oa.nc.abnormal.vo.ShowPrintingEnumVO;
import com.jiuji.oa.stock.authorizationtransfer.enums.SelectTimeEnums;
import com.jiuji.oa.stock.authorizationtransfer.enums.SubPageReqEnums;
import com.jiuji.oa.stock.authorizationtransfer.enums.SubmitReqEnums;
import com.jiuji.oa.stock.authorizationtransfer.enums.TransferStateEnums;
import com.jiuji.oa.stock.authorizationtransfer.service.CrossAuthTransferSubService;
import com.jiuji.oa.stock.authorizationtransfer.vo.*;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.constants.NumberConstant;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("api/crossAuthTransferSub")
public class CrossAuthTransferSubController {

    private static final Integer errorCode = 5000;
    private static final Integer successCode = 0;

    @Resource
    private CrossAuthTransferSubService transferSubService;

    /**
     * 跨授权调拨 配置 修改
     * @return
     */
    @PostMapping("/submit/v1")
    public R<SubmitResultVO> submit(@RequestBody @Valid SubmitOrderVO submitOrderVO) {
        SubmitResultVO submit = transferSubService.submit(submitOrderVO);
        return R.success(submit);
    }

    /**
     * 跨区域调拨获取枚举接口
     * @return
     */
    @GetMapping("/getEnum")
    public R<Map<String, List<ShowPrintingEnumVO>>> getEnum() {
        List<ShowPrintingEnumVO> transferStateEnums = TransferStateEnums.getAllPrintingEnum();
        List<ShowPrintingEnumVO> submitReqEnums = SubmitReqEnums.getAllPrintingEnum();
        List<ShowPrintingEnumVO> subPageReqEnums = SubPageReqEnums.getAllPrintingEnum();
        List<ShowPrintingEnumVO> selectTimeEnums = SelectTimeEnums.getAllPrintingEnum();
        HashMap<String, List<ShowPrintingEnumVO>> map = new HashMap<>();
        map.put("transferStateEnums", transferStateEnums);
        map.put("subPageReqEnums", subPageReqEnums);
        map.put("submitReqEnums", submitReqEnums);
        map.put("selectTimeEnums", selectTimeEnums);
        return R.success(map);
    }


    /**
     * 根据门店查询授权名称
     * @return
     */
    @PostMapping("/selectAuthByAreaId/v1")
    public R<AuthRes> selectAuthByAreaId(@RequestBody AuthReq req) {
        AuthRes authRes = transferSubService.selectAuthByAreaId(req);
        return R.success(authRes);
    }


    /**
     * 根据门店查询授权名称
     * @return
     */
    @PostMapping("/selectProductSubmitInfo/v1")
    public R<IPage<ProductSubmitInfo>> selectProductSubmitInfo(@RequestBody @Valid ProductReq req) {
         return R.success(transferSubService.selectProductSubmitInfo(req));
    }


    /**
     * 串号校验
     * @return
     */
    @PostMapping("/checkImei/v1")
    public R<CheckImeiRes> checkImei(@RequestBody @Valid CheckImeiReq req) {
        return R.success(transferSubService.checkImei(req));
    }


    /**
     * 跨区域调拨获取枚举接口
     * @return
     */
    @GetMapping("/delTransferSub/v1")
    public R<String> delTransferSub(@RequestParam(name = "id") Integer id) {
        transferSubService.delTransferSub(id);
        return R.success("删除成功");
    }


    /**
     * 商品导入校验 规则校验
     */
    @PostMapping("/verifyProduct/v1")
    public R<VerifyProductRes> verifyProduct(@RequestParam("file") MultipartFile file,@RequestParam("transferAreaId")Integer transferAreaId) throws IOException {
        List<VerifyProductDetailRes> verifyProductDetailRes = transferSubService.verifyProduct(file,transferAreaId);
        VerifyProductRes checkVo = new VerifyProductRes();
        List<VerifyProductDetailRes> successList = new ArrayList<>();
        List<VerifyProductDetailRes> failList = new ArrayList<>();
        verifyProductDetailRes.forEach((VerifyProductDetailRes item) -> {
            String reason = item.getCheckReason();
            if (StringUtils.isEmpty(reason)) {
                successList.add(item);
            } else {
                failList.add(item);
            }
        });
        checkVo.setFailCheck(failList)
                .setSuccessCheck(successList)
                .setCount(failList.size() + successList.size())
                .setFailCount(failList.size())
                .setSuccessCount(successList.size());
        if (!CollectionUtils.isEmpty(failList)) {
            checkVo.setResult("校验失败").setResultCode(errorCode);
        } else {
            checkVo.setResult("校验成功").setResultCode(successCode);
        }
        //如果校验全部成功
        if(CollectionUtils.isEmpty(failList)){
            ProductReq req = new ProductReq();
            List<Integer> ppidList = successList.stream().map(VerifyProductDetailRes::getPpid).map(Integer::new).collect(Collectors.toList());
            req.setPpidList(ppidList)
                    .setTransferAreaId(transferAreaId);
            List<ProductSubmitInfo> productSubmitInfoIPage = transferSubService.selectProductKcList(req);
            if(!CollectionUtils.isEmpty(successList)){
                Map<String, VerifyProductDetailRes> map = successList.stream().collect(Collectors.toMap(VerifyProductDetailRes::getPpid, Function.identity(), (n1, n2) -> n2));
                productSubmitInfoIPage.forEach(item->{
                    Integer ppid = item.getPpid();
                    VerifyProductDetailRes orDefault = map.getOrDefault(ppid + "",new VerifyProductDetailRes());
                    Integer count = Integer.parseInt(Optional.ofNullable(orDefault.getCount()).orElse(NumberConstant.ZERO.toString()));
                    item.setCount(count);
                });
            }
            checkVo.setPage(productSubmitInfoIPage);
        }
        return R.success(checkVo);
    }
}
