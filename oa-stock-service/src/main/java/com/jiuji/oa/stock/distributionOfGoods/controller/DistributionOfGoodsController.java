package com.jiuji.oa.stock.distributionOfGoods.controller;


import com.jiuji.oa.nc.abnormal.vo.ShowPrintingEnumVO;
import com.jiuji.oa.nc.stock.enums.StoreCategoryEnum;
import com.jiuji.oa.stock.distributionOfGoods.enums.SearchTermEnum;
import com.jiuji.oa.stock.distributionOfGoods.service.DistributionOfGoodsService;
import com.jiuji.oa.stock.distributionOfGoods.vo.*;
import com.jiuji.tc.common.vo.R;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/stock/DistributionOfGoodsController")
public class DistributionOfGoodsController {

    @Resource
    private DistributionOfGoodsService distributionOfGoodsService;


    /**
     * 获取分货枚举
     *
     * @return
     */
    @ApiOperation(value = "获取到盘点重构的枚举", notes = "获取到盘点重构的枚举")
    @GetMapping("/getEnum")
    public R<Map<String, List<ShowPrintingEnumVO>>> getEnum() {
        List<ShowPrintingEnumVO> storeCategoryEnum = StoreCategoryEnum.getAllPrintingEnum();
        List<ShowPrintingEnumVO> searchTermEnum = SearchTermEnum.getAllPrintingEnum();
        HashMap<String, List<ShowPrintingEnumVO>> map = new HashMap<>(8);
        map.put("storeCategoryEnum", storeCategoryEnum);
        map.put("searchTermEnum", searchTermEnum);
        return R.success(map);
    }

    /**
     * 获取报表商品部分
     *
     * @param searchCriteriaVO
     * @return
     */
    @PostMapping("/getReportFormProduct/v1")
    public R<List<ProductDetailVO>> getReportFormProduct(@RequestBody SearchCriteriaVO searchCriteriaVO) {
        return distributionOfGoodsService.getReportFormProduct(searchCriteriaVO);
    }

    /**
     * 获取报表库存部分
     *
     * @param searchCriteriaVO
     * @return
     */
    @PostMapping("/getReportFormStock/v1")
    public R<List<StockDetailVO>> getReportFormStock(@RequestBody SearchStockDetailVO searchCriteriaVO) {
        return distributionOfGoodsService.getReportFormStock(searchCriteriaVO);
    }


    /**
     * 获取报表销量占比部分
     *
     * @param searchCriteriaVO
     * @return
     */
    @PostMapping("/getProportionOfSalesVolume/v1")
    public R<TreeVo> getProportionOfSalesVolume(@RequestBody SearchCriteriaVO searchCriteriaVO) {
        return distributionOfGoodsService.getProportionOfSalesVolume(searchCriteriaVO);
    }

    /**
     * 批量调货接口
     *
     * @param list
     * @return
     */
    @PostMapping("/automaticGoodsTransfer/v1")
    public R<Boolean> automaticGoodsTransfer(@RequestBody List<AutomaticGoodsTransferVO> list) {
        return distributionOfGoodsService.automaticGoodsTransfer(list);
    }


}
