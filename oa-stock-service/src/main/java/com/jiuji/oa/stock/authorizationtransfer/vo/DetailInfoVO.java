package com.jiuji.oa.stock.authorizationtransfer.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class DetailInfoVO {

    private Integer detailId;

    private Integer ppid;

    private String productName;

    private String productColor;
    private String categoryName;

    private Integer mkcId;

    private String imei;

    private Boolean ismobile;



    /**
     * 数量
     */
    private Integer count;

    /**
     * 可调拨库存
     */
    private Integer kcCount;

    /**
     * 成本
     */
    private BigDecimal costPrice;

    /**
     * 总成本
     */
    private BigDecimal costTotalPrice;

    /**
     * 调拨成本
     */
    private BigDecimal transferPrice;

    /**
     * 是否样机
     */
    private Integer mouldFlag;

}
