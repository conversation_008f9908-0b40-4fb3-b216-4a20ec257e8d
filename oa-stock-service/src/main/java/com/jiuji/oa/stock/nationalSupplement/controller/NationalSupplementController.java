/*
 *    Copyright © 2006 - 2020 九机网 All Rights Reserved
 *
 */

package com.jiuji.oa.stock.nationalSupplement.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ch999.common.util.utils.XtenantJudgeUtil;
import com.jiuji.oa.apollo.WuliuApolloConfig;
import com.jiuji.oa.nc.common.bo.OaUserBO;
import com.jiuji.oa.nc.common.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.nc.common.enums.XtenantEnum;
import com.jiuji.oa.nc.common.exception.CustomizeException;
import com.jiuji.oa.nc.common.exception.RRExceptionHandler;
import com.jiuji.oa.nc.common.util.ExcelUtils;
import com.jiuji.oa.nc.dict.service.ISysConfigService;
import com.jiuji.oa.nc.stock.service.ISmsService;
import com.jiuji.oa.stock.common.easyexcel.NormalHeadStyleWriteHandler;
import com.jiuji.oa.stock.nationalSupplement.enums.NationalSupplementKind;
import com.jiuji.oa.stock.nationalSupplement.enums.NationalSupplementSelectEnum;
import com.jiuji.oa.stock.nationalSupplement.enums.SelectTypeTimeEnum;
import com.jiuji.oa.stock.nationalSupplement.req.*;
import com.jiuji.oa.stock.nationalSupplement.res.*;
import com.jiuji.oa.stock.nationalSupplement.service.NationalSupplementService;
import com.jiuji.oa.stock.publiccheck.annotation.CheckHeader;
import com.jiuji.oa.wuliu.entity.WuLiuSubFlagRecordEntity;
import com.jiuji.oa.wuliu.enums.DeclareStateEnum;
import com.jiuji.oa.wuliu.enums.FinanceCheckStateEnum;
import com.jiuji.oa.wuliu.enums.OperationStateEnum;
import com.jiuji.oa.wuliu.enums.SubFlagRecordStateEnum;
import com.jiuji.oa.wuliu.service.IWuLiuSubFlagRecordService;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.common.annotation.CheckHeaderSign;
import com.jiuji.tc.utils.common.annotation.LogRecordAround;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.enums.EnumUtil;
import com.jiuji.tc.utils.enums.EnumVO;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.jiuji.oa.stock.nationalSupplement.service.impl.NationalSupplementServiceImpl.NATIONAL_SUPPLEMENT_RANK;


/**
 * 组织管理Controller
 *
 * <AUTHOR> code generator
 * @date 2021-06-03 15:44:11
 */
@Slf4j
@RestController
@RequestMapping("/api/NationalSupplementController")
public class NationalSupplementController {



    @Resource
    private ISysConfigService sysConfigService;
    @Resource
    private NationalSupplementService nationalSupplementService;
    @Resource
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;
    @Resource
    private WuliuApolloConfig wuliuApolloConfig;


    /**
     * 重新申报
     * @param selectAgencyReq
     * @return
     */
    @PostMapping("/reDeclare/v1")
    public R<ReDeclareJiuJiRes> reDeclare(@RequestBody ReDeclareJiuJiReq reDeclareReq) {
        ReDeclareJiuJiRes result = nationalSupplementService.reDeclare(reDeclareReq);
        log.warn("九机重新申报 传入参数：{}，返回结果：{}", JSONUtil.toJsonStr(reDeclareReq), JSONUtil.toJsonStr(result));
        return R.success(result);
    }

    /**
     * 修改审核状态
     * @return
     */
    @PostMapping("/updateNationalSupplementState/v1")
    public R<ApproveRes> updateNationalSupplementState(@RequestBody ApproveReq approveReq) {
        OaUserBO userBO = Optional.ofNullable(abstractCurrentRequestComponent.getCurrentStaffId()).orElseThrow(() -> new RuntimeException("当前登录信息失效，请登录"));
        approveReq.setUserBO(userBO);
        List<String> rankList = Optional.ofNullable(userBO.getRank()).orElse(new ArrayList<>());
        if(!rankList.contains(NATIONAL_SUPPLEMENT_RANK)){
            throw new CustomizeException("没有权限"+NATIONAL_SUPPLEMENT_RANK+"不能进行审核");
        }
        ApproveRes approveRes = nationalSupplementService.updateNationalSupplementState(approveReq);
        approveRes.setEntity(nationalSupplementService.selectNationalById(approveReq.getId()));
        return R.success(approveRes);
    }



    /**
     * 上传附件
     * @return
     */
    @PostMapping("/uploadAttachments/v1")
    public R<ApproveRes> uploadAttachments(@RequestBody @Validated UploadAttachmentsReq attachmentsReq) {
        log.warn("上传附件修改审核状态传入参数：{}", JSONUtil.toJsonStr(attachmentsReq));
        ApproveRes approveRes = nationalSupplementService.uploadAttachments(attachmentsReq);
        return R.success(approveRes);
    }


    /**
     * 申报失败发推送
     * @return
     */
    @GetMapping("/sendDeclarationNotApproved/v1")
    public R<List<DeclarationNotApprovedRes>> sendDeclarationNotApproved() {
        List<DeclarationNotApprovedRes> declarationNotApprovedRes = nationalSupplementService.sendDeclarationNotApproved();
        log.warn("申报失败发推送返回结果：{}", JSONUtil.toJsonStr(declarationNotApprovedRes));
        return R.success(declarationNotApprovedRes);
    }


    /**
     * 查询申报失败
     * @return
     */
    @PostMapping("/selectDeclarationNotApproved/v1")
    public R<List<DeclarationNotApprovedRes>> selectDeclarationNotApproved(@RequestBody DeclarationNotApprovedReq declarationNotApprovedReq) {
        List<DeclarationNotApprovedRes> declarationNotApprovedRes = nationalSupplementService.selectDeclarationNotApproved(declarationNotApprovedReq);
        log.warn("查询申报失败：{}", JSONUtil.toJsonStr(declarationNotApprovedRes));
        return R.success(declarationNotApprovedRes);
    }

    /**
     * 根据退货日期查询订单
     * @param req 包含开始时间和结束时间的请求对象
     * @return 订单号和退货日期列表
     */
    @PostMapping("/querySubsByReturnDate/v1")
    @LogRecordAround(value = "国补订单退款按日期查询")
    @CheckHeaderSign(value ="国补订单退款按日期查询")
    public R<List<SubReturnDateRes>> querySubsByReturnDate(@RequestBody @Validated SubReturnDateReq req) {
        List<SubReturnDateRes> results = nationalSupplementService.querySubsByReturnDate(req);
        return R.success(results);
    }

    /**
     * 修改审核状态
     * @return
     */
    @CheckHeader
    @PostMapping("/sysUpdate/v1")
    public R<String> sysUpdate(@RequestBody SysUpdateReq sysUpdateReq) {
        if(wuliuApolloConfig.getCloseSysUpdate()){
            return R.success("系统关闭");
        }
        String result = nationalSupplementService.sysUpdate(sysUpdateReq);
        log.warn("系统修改审核状态传入参数：{}，返回结果：{}", JSONUtil.toJsonStr(sysUpdateReq), result);
        return R.success(result);
    }


    /**
     * 附件查询
     * @return
     */
    @PostMapping("/selectNationalSupplementState/v1")
    public R<IPage<NationalSupplementAttachmentRes>> selectNationalSupplementState(@RequestBody NationalAttachmentReq req) {
        return R.success(nationalSupplementService.selectNationalSupplementState(req));
    }



    /**
     * 附件查询
     * @return
     */
    @PostMapping("/selectNationalSupplementSum/v1")
    public R<NationalAttachmentCount> selectNationalSupplementSum(@RequestBody NationalAttachmentReq req) {
        return R.success(nationalSupplementService.selectNationalSupplementSum(req));
    }


    /**
     * 财务复核
     * @return
     */
    @PostMapping("/financialReview/v1")
    public R<FinancialReviewRes> financialReview(@RequestBody FinancialReviewReq req) {
        FinancialReviewRes financialReviewRes = nationalSupplementService.financialReview(req);
        log.warn("财务复核传入参数：{}，返回结果：{}", JSONUtil.toJsonStr(req), JSONUtil.toJsonStr(financialReviewRes));
        return R.success(financialReviewRes);
    }


    /**
     * 运营复核
     * @return
     */
    @PostMapping("/operationReview/v1")
    public R<OperationReviewRes> operationReview(@RequestBody OperationReviewReq req) {
        OperationReviewRes operationReview = nationalSupplementService.operationReview(req);
        operationReview.setEntity(nationalSupplementService.selectNationalById(req.getId()));
        log.warn("运营复核传入参数：{}，返回结果：{}", JSONUtil.toJsonStr(req), JSONUtil.toJsonStr(operationReview));
        return R.success(operationReview);
    }

    /**
     * 获取申报数据
     * @return
     */
    @CheckHeader
    @PostMapping("/selectDeclarationData/v1")
    public R<List<Integer>> selectDeclarationData(@RequestBody @Validated DeclarationDataReq declarationDataReq) {
        List<Integer> subIds = nationalSupplementService.selectDeclarationData(declarationDataReq);
        log.warn("获取申报数据传入参数：{}，返回结果：{}", JSONUtil.toJsonStr(declarationDataReq), JSONUtil.toJsonStr(subIds));
        return R.success(subIds);
    }

    /**
     * 申报回调
     * @return
     */
    @CheckHeader
    @PostMapping("/declarationCallback/v1")
    public R<DeclarationCallbackRes> declarationCallback(@RequestBody @Validated DeclarationCallbackReq req) {
        DeclarationCallbackRes declarationCallbackRes = new DeclarationCallbackRes();
        try {
            declarationCallbackRes = nationalSupplementService.declarationCallback(req);
        } catch (Exception e){
            if(!Optional.ofNullable(e.getMessage()).orElse("").contains("更新失败，请检查订单状态或数据。")){
                RRExceptionHandler.logError("国补附件调用申报回调异常", req, e, SpringUtil.getBean(ISmsService.class)::sendOaMsgTo9JiMan);
            }
            log.warn("申报回调异常传入参数：{}，返回结果：{}", JSONUtil.toJsonStr(req), e.getMessage());
            throw new CustomizeException(e.getMessage());
        }
        log.warn("申报回调传入参数：{}，返回结果：{}", JSONUtil.toJsonStr(req), JSONUtil.toJsonStr(declarationCallbackRes));
        return R.success(declarationCallbackRes);
    }


    /**
     * 附件查询导出
     *
     * @param response HttpServletResponse
     * @throws IOException IO异常
     */
    @ApiOperation(value = "导出", notes = "导出")
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody NationalAttachmentReq req) throws IOException {
        OaUserBO userBO = Optional.ofNullable(abstractCurrentRequestComponent.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("当前登录信息失效，请登录"));
        req.setSize(NumberConstant.FIVE_THOUSAND * 3L)
                .setCurrent(Convert.toLong(NumberConstant.ZERO));
        req.setIsExport(Boolean.TRUE);
        IPage<NationalSupplementAttachmentRes> resIPage = nationalSupplementService.selectNationalSupplementState(req);
        List<NationalSupplementAttachmentRes> records = resIPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            throw new CustomizeException("导出数据为空");
        }
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        List<NationalSupplementAttachmentJiuJiExcel> exportExcelJiuJi= new ArrayList<>();
        List<NationalSupplementAttachmentSaasExcel> exportExcelSaas= new ArrayList<>();
        if(XtenantEnum.isJiujiXtenant()){
            exportExcelJiuJi = records.parallelStream().map(item -> {
                NationalSupplementAttachmentJiuJiExcel jiuJiExcel = new NationalSupplementAttachmentJiuJiExcel();
                BeanUtil.copyProperties(item, jiuJiExcel);
                Optional.ofNullable(item.getCheckTime()).ifPresent(time->jiuJiExcel.setCheckTimeStr(time.format(dateTimeFormatter)));
                Optional.ofNullable(item.getTradeDate1()).ifPresent(time->jiuJiExcel.setTradeDate1Str(time.format(dateTimeFormatter)));
                Optional.ofNullable(item.getOperationCheckTime()).ifPresent(time->jiuJiExcel.setOperationCheckTimeStr(time.format(dateTimeFormatter)));
                return jiuJiExcel;
            }).collect(Collectors.toList());
        } else {
            exportExcelSaas = records.parallelStream().map(item -> {
                NationalSupplementAttachmentSaasExcel saasExcel = null;
                //由gbsf权限控制，有权限才可以导出。身份证控制权限默认关闭
                if(Optional.ofNullable(userBO.getRank()).orElse(new ArrayList<>()).contains("gbsf")){
                    saasExcel = new NationalSupplementAttachmentWithIdCardExcel();
                } else {
                    saasExcel = new NationalSupplementAttachmentSaasExcel();
                }
                BeanUtil.copyProperties(item, saasExcel);

                NationalSupplementAttachmentSaasExcel finalSaasExcel = saasExcel;
                Optional.ofNullable(item.getCheckTime()).ifPresent(time-> finalSaasExcel.setCheckTimeStr(time.format(dateTimeFormatter)));
                Optional.ofNullable(item.getTradeDate1()).ifPresent(time->finalSaasExcel.setTradeDate1Str(time.format(dateTimeFormatter)));
                return saasExcel;
            }).collect(Collectors.toList());
        }
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        try {
            String fileName = ExcelUtils.getExportFileName("国补附件审核");
            fileName = URLEncoder.encode(fileName, "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName);
            if(XtenantEnum.isJiujiXtenant()){
                ExcelWriterSheetBuilder sheet1 = EasyExcelFactory.write(response.getOutputStream(), NationalSupplementAttachmentJiuJiExcel.class).sheet("Sheet1")
                        .registerWriteHandler(new NormalHeadStyleWriteHandler());
                sheet1.doWrite(exportExcelJiuJi);
            } else {
                ExcelWriterSheetBuilder sheet1 = EasyExcelFactory.write(response.getOutputStream(), exportExcelSaas.get(NumberConstant.ZERO).getClass()).sheet("Sheet1")
                        .registerWriteHandler(new NormalHeadStyleWriteHandler());
                sheet1.doWrite(exportExcelSaas);
            }
        } catch (IOException e) {
            log.error("国补附件审核导出报错：{}", e.getMessage());
            throw new CustomizeException("国补附件审核据导出报错：", e);
        }

    }



    @GetMapping("/getEnum")
    @ApiOperation("获取枚举选项")
    public R<Map<String, List<EnumVO>>> getEnum() {
        Map<String, List<EnumVO>> enumMap = new HashMap<>();
        List<EnumVO> nationalSupplementSelectEnum = EnumUtil.toEnumVOList(NationalSupplementSelectEnum.class);
        List<EnumVO> selectTypeTimeEnum = EnumUtil.toEnumVOList(SelectTypeTimeEnum.class);
        List<EnumVO> subFlagRecordStateEnum = EnumUtil.toEnumVOList(SubFlagRecordStateEnum.class);
        List<EnumVO> financeCheckStateEnum = EnumUtil.toEnumVOList(FinanceCheckStateEnum.class);
        List<EnumVO> declareStateEnum = EnumUtil.toEnumVOList(DeclareStateEnum.class);
        List<EnumVO> operationStateEnum = EnumUtil.toEnumVOList(OperationStateEnum.class);
        if(XtenantJudgeUtil.isJiujiMore()){
            //国补类型
            enumMap.put("nationalSupplementKind", EnumUtil.toEnumVOList(NationalSupplementKind.class));

            List<EnumVO> nationalSupplementCashier = sysConfigService.getNationalSupplementCashier();
            //2025年云南省以旧换新补贴(九机线上)
            EnumVO e = new EnumVO();
            e.setValue(0);
            e.setLabel("2025年云南省以旧换新补贴(九机线上)");
            if (CollUtil.isNotEmpty(nationalSupplementCashier)) {
                nationalSupplementCashier.add(e);
            } else {
                nationalSupplementCashier = new ArrayList<>();
                nationalSupplementCashier.add(e);
            }
            enumMap.put("nationalSupplementCashier", nationalSupplementCashier);
        } else {
            //输出没有 运营复核时间，运营复核人
            selectTypeTimeEnum.removeIf(item->SelectTypeTimeEnum.OPERATION_TIME.getMessage().equals(item.getLabel()));
            nationalSupplementSelectEnum.removeIf(item->NationalSupplementSelectEnum.OPERATION_CHECK_USER.getMessage().equals(item.getLabel()));
        }
        enumMap.put("subFlagRecordStateEnum", subFlagRecordStateEnum);
        enumMap.put("declareStateEnum", declareStateEnum);
        enumMap.put("selectTypeTimeEnum", selectTypeTimeEnum);
        enumMap.put("operationStateEnum", operationStateEnum);
        enumMap.put("financeCheckStateEnum", financeCheckStateEnum);
        enumMap.put("financeCheckStateEnum", financeCheckStateEnum);
        enumMap.put("nationalSupplementSelectEnum", nationalSupplementSelectEnum);
        return R.success(enumMap);
    }

}
