package com.jiuji.oa.stock.authorizationtransfer.enums;

import com.jiuji.oa.nc.abnormal.vo.ShowPrintingEnumVO;
import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum TransferStateEnums implements CodeMessageEnumInterface {


    SUBMITTED(0, "已提交"),
    AUDITED(1, "已审核"),
    CANCELED(2, "已取消"),
    COMPLETE(3, "已完成");

    private Integer code;
    private String message;

    /**
     * 根据code获取其对应的名称
     *
     * @return String
     */
    public static String getMessageByCode(Integer code) {
        for (TransferStateEnums purchaseState : values()) {
            if (purchaseState.getCode().equals(code)) {
                return purchaseState.getMessage();
            }
        }
        return "";
    }
    /**
     * 将所有的枚举转换成list
     *
     * @return
     */
    public static List<ShowPrintingEnumVO> getAllPrintingEnum() {
        TransferStateEnums[] array = TransferStateEnums.values();
        List<ShowPrintingEnumVO> arrayList = new ArrayList<>();
        for (TransferStateEnums t : array) {
            ShowPrintingEnumVO showPrintingEnumVO = new ShowPrintingEnumVO()
                    .setLabel(t.getMessage())
                    .setValue(t.getCode());
            arrayList.add(showPrintingEnumVO);
        }
        return arrayList;
    }

}
