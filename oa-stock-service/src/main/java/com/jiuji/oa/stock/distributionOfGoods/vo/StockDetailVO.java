package com.jiuji.oa.stock.distributionOfGoods.vo;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class StockDetailVO {
    /**
     * sku_id
     */
    private Long ppriceid;
    /**
     * 库存数量
     */
    private Integer stockCount;
    /**
     * 在途--10
     */
    private Integer stockCountOnTheWay;

    /**
     * 库存总量
     */
    private Integer totalStockCount;


    /**
     * dc保底量
     */
    private int minimumGuarantee;

    /**
     * 分货量
     */
    private int splitVolume;
    /**
     * 分货量
     */
    private int preSplitVolume;

    /**
     * 已分量
     */
    private int dividedQuantity;

    /**
     * 剩余分货量
     */
    private int remainingDistributionVolume;

}
