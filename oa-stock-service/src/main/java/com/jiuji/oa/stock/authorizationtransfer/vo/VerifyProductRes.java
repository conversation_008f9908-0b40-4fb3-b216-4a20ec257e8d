package com.jiuji.oa.stock.authorizationtransfer.vo;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class VerifyProductRes {

    /**
     * 校验结果
     */
    @ApiModelProperty(value = "校验结果")
    private String result;

    /**
     * 校验结果
     */
    @ApiModelProperty(value = "校验结果")
    private Integer resultCode;

    /**
     * 总数
     */
    @ApiModelProperty(value = "总数")
    private int count;

    /**
     * 成功数量
     */
    @ApiModelProperty(value = "成功数量")
    private int successCount;

    /**
     * 失败数量
     */
    @ApiModelProperty(value = "失败数量")
    private int failCount;

    /**
     * 校验成功数据
     */
    @ApiModelProperty(value = "校验成功数据")
    private List<VerifyProductDetailRes> successCheck;

    /**
     * 校验成功数据
     */
    @ApiModelProperty(value = "校验成功数据")
    private List<VerifyProductDetailRes> failCheck;


    /**
     * 全部校验通过返回数据
     */
    private List<ProductSubmitInfo> page;



}
