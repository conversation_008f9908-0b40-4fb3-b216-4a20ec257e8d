package com.jiuji.oa.stock.nationalSupplement.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@AllArgsConstructor
@Getter
public enum RejectionReasonEnum implements CodeMessageEnumInterface {

    WAITING_FOR_ENTRY(0, "待录入", "3"),
    PREPARING_FOR_ENTRY(1, "准备录入", "3"),
    ENTRY_SUCCESS(2, "录入成功", "3"),
    ATTACHMENT_MISSING(3, "附件缺失", "1"),
    ORDER_STATUS_ABNORMAL(4, "订单状态异常", "2"),
    PRODUCT_DATA_ABNORMAL(5, "商品数据异常", "2"),
    ENERGY_EFFICIENCY_LEVEL_MISMATCH(6, "能效等级匹配异常", "2"),
    ORDER_PROVINCE_CITY_AREA_EMPTY(7, "订单省市区/地址为空", "3"),
    POS_RECEIPT_RECOGNITION_FAILED(8, "POS小票识别失败", "1"),
    INVOICE_IMAGE_RECOGNITION_FAILED(9, "发票图片识别失败", "2"),
    MOBILE_SERIAL_NUMBER_EMPTY(10, "手机串号为空", "3"),
    PRODUCT_TYPE_MISMATCH(11, "商品类型匹配异常", "2"),
    PRODUCT_SN_EMPTY(12, "商品SN为空", "2"),
    SUBMISSION_INTERFACE_EXCEPTION(13, "提交接口异常", "3"),
    USER_PHONE_NUMBER_EMPTY(14, "用户手机号为空", "3"),
    USER_ID_CARD_NUMBER_EMPTY(15, "用户身份证号为空", "3"),
    ORDER_ADDRESS_EMPTY(16, "订单地址为空", "3"),
    PRODUCT_NAME_EMPTY(17, "商品名称为空", "2"),
    PRODUCT_CATEGORY_EMPTY(18, "商品分类为空", "2"),
    INVALID_PROVINCE_FORMAT(19, "省市区格式错误/非云南省", "3"),
    MISSING_POS_RECEIPT(20, "附件缺失-POS小票", "1"),
    MISSING_SN_RECEIPT(21, "附件缺失-SN小票", "1"),
    MISSING_INVOICE(22, "附件缺失-发票", "1"),
    MISSING_ENERGY_LABEL(23, "附件缺失-能效标识", "1"),
    MISSING_SALES_RECEIPT(24, "附件缺失-销售小票", "1"),
    MISSING_PACKAGING_PHOTOS(25, "附件缺失-外包装/其他激活图片", "1"),
    OTHER_EXCEPTION(99, "其他异常", "3"),
    DUPLICATE_SUBMISSION(1004, "重复提交", "3"),
    EXTERNAL_ORDER_NUMBER_INCORRECT(1005, "外部订单号不正确", "1");

    /**
     * 【审核状态】变更为【已拒绝】
     */
    public static final String CHECK_STATE_UPDATE = "1";
    /**
     * 【财务复核状态】变更为【待复核】
     */
    public static final String FINANCE_CHECK_STATE_UPDATE = "2";
    /**
     * 不做操作
     */
    public static final String NOT_UPDATE = "3";

    /**
     * 编码
     */
    private Integer code;
    /**
     * 名称
     */
    private String message;

    /**
     * 1--【审核状态】变更为【已拒绝】
     * 2--【财务复核状态】变更为【待复核】
     */
    private String codeType;


    /**
     * 获取状态修改文案
     * @param codeType
     * @return
     */
    public static List<String> getUpdateList(String codeType) {
       return Arrays.stream(RejectionReasonEnum.values()).filter(value -> Arrays.asList(value.getCodeType().split(",")).contains(codeType))
               .map(RejectionReasonEnum::getMessage).collect(Collectors.toList());

    }

    /**
     * 获取不推送的状态
     * @return
     */
    public static List<String> getNotPush() {
        return Arrays.asList(RejectionReasonEnum.ENERGY_EFFICIENCY_LEVEL_MISMATCH.getMessage(),
                RejectionReasonEnum.PRODUCT_TYPE_MISMATCH.getMessage(),
                RejectionReasonEnum.MISSING_INVOICE.getMessage());
    }
}
