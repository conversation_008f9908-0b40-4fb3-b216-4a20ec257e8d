package com.jiuji.oa.stock.nationalSupplement.req;

import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class DeclarationDataReq {


    /**
     * 申报状态
     */
    @NotNull(message = "申报状态不能为空")
    private Integer status;

    /**
     * 财务审核状态
     */
    @NotNull(message = "财务审核状态不能为空")
    private Integer financeCheckState;

    /**
     * 附件审核状态
     */
    @NotNull(message = "附件审核状态不能为空")
    private Integer checkState;
}
