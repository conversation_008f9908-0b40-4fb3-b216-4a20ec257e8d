package com.jiuji.oa.stock.authorizationtransfer.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ProductSubmitInfo {

    private Integer ppid;

    private String productName;

    private String productColor;

    private Integer mkcId;

    /**
     * 库存数量
     */
    private Integer kcCount;

    /**
     * 成本价
     */
    private BigDecimal costPrice;

    private String imei;

    /**
     * 调拨数量
     */
    private Integer transferQuantity;

    private Boolean ismobile;

    /**
     * 小件商品导入数量
     */
    private Integer count;
    /**
     * 样机
     */
    private Integer mouldFlag;
}