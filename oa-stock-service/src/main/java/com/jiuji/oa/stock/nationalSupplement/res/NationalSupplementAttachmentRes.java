package com.jiuji.oa.stock.nationalSupplement.res;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Accessors(chain = true)
public class NationalSupplementAttachmentRes {

    private Integer id;

    private Integer subId;

    private String area;

    /**
     * 门店id
     */
    private Integer areaId;

    /**
     * 小区
     */
    private String departName;
    /**
     * 销售人
     */
    private String seller;

    /**
     * 收货人
     */
    private String subTo;
    /**
     * 身份证
     */
    private String invoiceIdCard;
    /**
     * 邮箱
     */
    private String invoiceReceivedEmail;


    private String productName;

    private String productColor;

    private BigDecimal price;

    /**
     * 交易完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8" )
    private LocalDateTime tradeDate1;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8" )
    private LocalDateTime checkTime;

    /**
     * 加单时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8" )
    private LocalDateTime subDate;

    private Integer checkState;
    /**
     * 审核人
     */
    private String checkUser;

    private String checkStateValue;

    private List<NationalAttachmentInfo> nationalAttachmentInfoList;

    /**
     * 财务 审核状态
     * @see FinanceCheckStateEnum
     */
    private Integer financeCheckState;

    private String financeCheckStateValue;


    /**
     * @see DeclareStateEnum
     * 申报状态
     */
    private Integer status;

    private String statusComment;


    /**
     * @see DeclareStateEnum
     * 申报状态
     */
    private String statusValue;


    /**
     * 运营审核时间
     */
    private LocalDateTime operationCheckTime;

    /**
     * 运营审核状态
     * @see com.jiuji.oa.wuliu.enums.OperationStateEnum
     */
    private Integer operationCheckState;

    /**
     * 运营审核状态
     */
    private String operationCheckStateValue;

    /**
     * 审核备注原因
     */
    private String checkComment;

    private Integer basketId;

    private String imei;
    /**
     * 运营复核人
     */
    private String operationCheckUser;

    /**
     * 国补类型
     */
    private String nationalSupplementKindName;
}
