package com.jiuji.oa.stock.stockstatistics.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ch999.common.util.utils.Exceptions;
import com.ch999.common.util.utils.HttpClientUtils;
import com.ch999.common.util.utils.XtenantJudgeUtil;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.jiuji.cloud.after.service.ApiAfterClient;
import com.jiuji.cloud.after.vo.res.ChangeInfoRes;
import com.jiuji.oa.logapi.pojo.dto.req.MkcLogNewReq;
import com.jiuji.oa.logapi.service.IMkcLogNewService;
import com.jiuji.oa.logapi.service.ISubLogService;
import com.jiuji.oa.loginfo.order.vo.req.SubLogsNewReq;
import com.jiuji.oa.nc.abnormal.vo.ShowPrintingEnumVO;
import com.jiuji.oa.nc.common.bo.OaUserBO;
import com.jiuji.oa.nc.common.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.nc.common.constant.DataSourceConstants;
import com.jiuji.oa.nc.common.constant.RedisKeys;
import com.jiuji.oa.nc.common.db.MyDynamicRoutingDataSource;
import com.jiuji.oa.nc.common.enums.XtenantEnum;
import com.jiuji.oa.nc.common.exception.CustomizeException;
import com.jiuji.oa.nc.common.exception.RRException;
import com.jiuji.oa.nc.common.util.CommonUtil;
import com.jiuji.oa.nc.dict.entity.SysconfigLog;
import com.jiuji.oa.nc.dict.enums.ConfigEnum;
import com.jiuji.oa.nc.dict.enums.OperationTypeEnum;
import com.jiuji.oa.nc.dict.service.ISysConfigService;
import com.jiuji.oa.nc.dict.service.SysconfigLogService;
import com.jiuji.oa.nc.dict.vo.req.ScannedProductConfiguration;
import com.jiuji.oa.nc.largepurchasesStock.res.LPBidResultEnumExtendRes;
import com.jiuji.oa.nc.largepurchasesStock.res.LPBidResultEnumRes;
import com.jiuji.oa.nc.oaapp.po.SysConfig;
import com.jiuji.oa.nc.product.entity.ProductBarcodeEntity;
import com.jiuji.oa.nc.product.entity.ProductInfoEntity;
import com.jiuji.oa.nc.product.service.IProductBarcodeService;
import com.jiuji.oa.nc.product.service.IProductInfoService;
import com.jiuji.oa.nc.product.vo.req.ProductInfoVo;
import com.jiuji.oa.nc.stock.entity.ProductMkc;
import com.jiuji.oa.nc.stock.entity.RecoverBasket;
import com.jiuji.oa.nc.stock.entity.RecoverMkc;
import com.jiuji.oa.nc.stock.service.CategoryService;
import com.jiuji.oa.nc.stock.service.IProductMkcService;
import com.jiuji.oa.nc.stock.service.IRecoverBasketService;
import com.jiuji.oa.nc.stock.service.IRecoverMkcService;
import com.jiuji.oa.nc.user.po.Areainfo;
import com.jiuji.oa.nc.user.po.Ch999User;
import com.jiuji.oa.nc.user.service.Ch999UserService;
import com.jiuji.oa.nc.user.service.IAreaInfoService;
import com.jiuji.oa.nc.utils.ShowPrintingListUtil;
import com.jiuji.oa.orderdynamics.dto.MqInfo;
import com.jiuji.oa.orderdynamics.dto.MqInfoData;
import com.jiuji.oa.orderdynamics.util.JournalPush;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.oa.stock.common.cache.RedisUtils;
import com.jiuji.oa.stock.common.config.xtenant.MultitenancyInterceptor;
import com.jiuji.oa.stock.common.util.SysUtils;
import com.jiuji.oa.stock.develivery.vo.ScanVO;
import com.jiuji.oa.stock.develivery.vo.req.OrderOutStockDetailReq;
import com.jiuji.oa.stock.develivery.vo.req.OrderOutStockPageReqVO;
import com.jiuji.oa.stock.develivery.vo.req.OrderOutStockPrintReq;
import com.jiuji.oa.stock.develivery.vo.req.OrderStateChangeReq;
import com.jiuji.oa.stock.develivery.vo.res.OrderOutStockDetailRes;
import com.jiuji.oa.stock.develivery.vo.res.OrderOutStockPageRes;
import com.jiuji.oa.stock.develivery.vo.res.ScanPickStockDetailRes;
import com.jiuji.oa.stock.displayproductinfo.entity.DisplayProductInfo;
import com.jiuji.oa.stock.displayproductinfo.service.IDisplayProductInfoService;
import com.jiuji.oa.stock.inventory.enums.AreaLevelEnum;
import com.jiuji.oa.stock.inventory.exception.InventoryException;
import com.jiuji.oa.stock.logistics.dada.utils.HttpClientUtil;
import com.jiuji.oa.stock.logistics.order.enums.WuliuStatusEnum;
import com.jiuji.oa.stock.logisticscenter.enums.LogisticsExpressTypeEnum;
import com.jiuji.oa.stock.nationalSupplement.res.NationalSupplementKindRes;
import com.jiuji.oa.stock.nationalSupplement.service.NationalSupplementService;
import com.jiuji.oa.stock.organization.service.ICategoryBusService;
import com.jiuji.oa.stock.organization.vo.BrandVo;
import com.jiuji.oa.stock.purchaseList.enums.KcCheckEnum;
import com.jiuji.oa.stock.stockmanage.enums.BoolEnum;
import com.jiuji.oa.stock.stockstatistics.bo.SmallproBill;
import com.jiuji.oa.stock.stockstatistics.dto.AreaStockItemDTO;
import com.jiuji.oa.stock.stockstatistics.dto.BrandDTO;
import com.jiuji.oa.stock.stockstatistics.dto.OrderOutStockExpressDTO;
import com.jiuji.oa.stock.stockstatistics.dto.PpidInfoDTO;
import com.jiuji.oa.stock.stockstatistics.enums.*;
import com.jiuji.oa.stock.stockstatistics.mapper.StockMapper;
import com.jiuji.oa.stock.stockstatistics.mapstruct.MkcLogMapStruct;
import com.jiuji.oa.stock.stockstatistics.service.IAccessoryStockStatisticsService;
import com.jiuji.oa.stock.stockstatistics.service.IStockService;
import com.jiuji.oa.stock.stockstatistics.service.IStockStatisticsService;
import com.jiuji.oa.stock.stockstatistics.vo.req.*;
import com.jiuji.oa.stock.stockstatistics.vo.res.*;
import com.jiuji.oa.stock.sub.entity.Basket;
import com.jiuji.oa.stock.sub.entity.NahuoBasketRecord;
import com.jiuji.oa.stock.sub.entity.Nahuoduilie;
import com.jiuji.oa.stock.sub.entity.Sub;
import com.jiuji.oa.stock.sub.service.IBasketService;
import com.jiuji.oa.stock.sub.service.INahuoBasketRecordService;
import com.jiuji.oa.stock.sub.service.INahuoduilieService;
import com.jiuji.oa.stock.sub.service.ISubService;
import com.jiuji.oa.stock.sub.util.Encode64Util;
import com.jiuji.oa.wuliu.bo.ChangWuliuStatsBO;
import com.jiuji.oa.wuliu.entity.RecoverMarketinfo;
import com.jiuji.oa.wuliu.entity.RecoverMarketsubinfo;
import com.jiuji.oa.wuliu.entity.WuLiuEntity;
import com.jiuji.oa.wuliu.entity.WuLiuRecoverSubEntity;
import com.jiuji.oa.wuliu.enums.DeliveryEnum;
import com.jiuji.oa.wuliu.enums.SubCheckEnum;
import com.jiuji.oa.wuliu.enums.SubDeliveryEnum;
import com.jiuji.oa.wuliu.enums.WuLiuTypeEnum;
import com.jiuji.oa.wuliu.service.*;
import com.jiuji.oa.wuliu.vo.WuLiuSubDTO;
import com.jiuji.oa.wuliu.vo.req.WuLiuAddOrUpdateReqVO;
import com.jiuji.oa.wuliu.vo.req.WuLiuInfoReqVO;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.common.TraceIdUtil;
import com.jiuji.tc.utils.common.build.LambdaBuild;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.constants.TimeFormatConstant;
import com.jiuji.tc.utils.enums.EnumUtil;
import com.jiuji.tc.utils.enums.area.AreaGradeEnum;
import com.jiuji.tc.utils.enums.order.SubDynamicsBusinessNodeEnum;
import com.jiuji.tc.utils.enums.order.SubDynamicsNodeEnum;
import joptsimple.internal.Strings;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2021/6/2 16:11
 */
@Service
@RequiredArgsConstructor
@Slf4j
@DS("ch999oanew")
public class StockServiceImpl implements IStockService {

    private static final String UP_NAHUO_URL = "/oa/take/upnahuo";
    private static final String NAHUO_URL = "/kcApi/nahuo";
    private static final String CHECK_SUB_PEIJIAN_BEI_URL = "/api/checkSubPeijianBei";
    private static final String SUB_CHECK_OP_URL = "{}/oaApi.svc/rest/SubCheckOp";
    private static final String RECOVER_SUB_CHECK_OP_URL = "{}/Recover.svc/rest/SubCheckOp";
    private static final String inwcf2Url = "";
    private static final Integer ZERO = 0;
    private static final Integer ONE = 1;
    private static final Integer TIME_DIFF_BOUND = 4;
    private static final Integer COLUMN_BOUND = 2000;
    private static final String RECORDS = "records";
    private static final String TOTAL = "total";
    private static final String URL = "https://www.9xun.com/cloudapi_nc/data-service/jiuji/stockControl/big/areaStockControlReport/v1?xservicename=data-service";

    private static final int LOCK_MILLISECONDS = 20000;

    public final static String ORDER_OUT_STOCK_PRINT = "ORDER_OUT_STOCK_PRINT";
    public final static String SCAN_PICK_OUT_STOCK_PRINT = "SCAN_PICK_OUT_STOCK_PRINT";
    public final static String SCAN_PICK_NAHUO = "SCAN_PICK_NAHUO";

    private final StockMapper mapper;

    @Resource
    private final AbstractCurrentRequestComponent currentRequestComponent;

    @Resource
    private final CategoryService categoryService;
    @Resource
    private final Ch999UserService ch999UserService;

    @Resource
    private final ApiAfterClient apiAfterClient;

    @Resource
    @Lazy
    private RedissonClient redissonClient;

    @Resource
    private final IAreaInfoService areaInfoService;
    @Resource
    private final ICategoryBusService categoryBusService;

    @Resource
    private final IProductInfoService productInfoService;
    @Resource
    private final IStockStatisticsService stockStatisticsService;
    @Resource
    private final IAccessoryStockStatisticsService accessoryStockStatisticsService;
    @Resource
    private final IProductBarcodeService productBarcodeService;
    @Resource
    private final IProductMkcService productMkcService;
    @Resource
    private final IRecoverMkcService recoverMkcService;

    @Resource
    private final IWuLiuRecoverSubService recoverSubService;

    @Resource
    private final ISubLogService subLogService;

    @Resource
    private final IRecoverBasketService recoverBasketService;

    @Resource
    private final IDisplayProductInfoService displayProductInfoService;

    @Resource
    private final IMkcLogNewService mkcLogNewService;

    @Resource
    private final ISubService subService;
    @Resource
    private final IBasketService basketService;

    @Resource
    private final IWuLiuBusService wuLiuBusService;

    @Resource
    private final IWuLiuService wuLiuService;
    @Resource
    private final IRecoverMarketsubinfoService recoverMarketsubinfoService;
    @Resource
    private final INahuoBasketRecordService nahuoBasketRecordService;
    @Resource
    private final INahuoduilieService nahuoduilieService;
    @Resource
    private final IRecoverMarketinfoService recoverMarketinfoService;

    @Resource
    private SysConfigClient sysConfigClient;


    @Resource
    private ISysConfigService sysConfigService;


    @Resource
    private MkcLogMapStruct mkcMapStruct;
    @Autowired
    private StockMapper stockMapper;

    @Override
    public Map<String, List<ShowPrintingEnumVO>> getSearchEnum() {
        List<ShowPrintingEnumVO> riskLevel = ShowPrintingListUtil.convert(RiskLevelEnum.class);
        List<ShowPrintingEnumVO> areaLevel = XtenantEnum.isJiujiXtenant() ? ShowPrintingListUtil.convert(AreaGradeEnum.class) : ShowPrintingListUtil.convert(AreaLevelEnum.class);
        List<ShowPrintingEnumVO> compareResult = ShowPrintingListUtil.convert(NonShareAreaEnum.class);
        List<ShowPrintingEnumVO> shopCategory = ShowPrintingListUtil.convert(NonGroupBuyingEnum.class);
        Map<String, List<ShowPrintingEnumVO>> map = new HashMap<>(MapUtil.DEFAULT_INITIAL_CAPACITY);

        map.put("riskLevel", riskLevel);
        map.put("areaLevel", areaLevel);
        map.put("compareResult", compareResult);
        map.put("shopCategory", shopCategory);
        return map;
    }

    /**
     * 分页查询统计条目
     *
     * @param req 分页查询数据
     */
    @Override
    public Map<String, Object> pageStatistic(@Valid AreaStockReqVO req) {
        ConcurrentHashMap<String, Object> res = new ConcurrentHashMap<>(1);

        AreaStockReqVO.ShowFlag showFlag = req.getShowFlag();
        if (Objects.isNull(showFlag)) {
            throw new CustomizeException("请选择显示字段");
        }
        Boolean kcFlag = showFlag.getKcFlag();
        Boolean displayKcFlag = showFlag.getDisplayKcFlag();
        Boolean diaoboOnWayFlag = showFlag.getDiaoboOnWayFlag();
        Boolean purchaseOnWayFlag = showFlag.getPurchaseOnWayFlag();
        Boolean basketKcFlag = showFlag.getBasketKcFlag();
        Boolean basketOnWayFlag = showFlag.getBasketOnWayFlag();
        Boolean unsatisfiedAmountFlag = showFlag.getUnsatisfiedAmountFlag();
        Boolean totalKcFlag = showFlag.getTotalKcFlag();
        Boolean saleFlag = showFlag.getSaleFlag();

        if (CollectionUtils.isEmpty(req.getShowFieldList())) {
            throw new CustomizeException("请至少选择一个显示字段");
        }
        AreaStockResVO result = new AreaStockResVO();
        List<String> areas = new ArrayList<>();
        result.setAreas(areas);
        areas.add("合计");
        List<Integer> areaIdList = req.getAreaIds();
        if (CollectionUtils.isEmpty(areaIdList)) {
            throw new CustomizeException("没有符合查询条件的门店");
        }
        if (req.getStartTime() != null || req.getEndTime() != null) {
            int startTimeDiff = this.monthDiff(req.getStartTime(), LocalDateTime.now());
            if (startTimeDiff >= TIME_DIFF_BOUND) {
                throw new CustomizeException("最多查询近" + TIME_DIFF_BOUND + "个月的销量数据");
            }
        }
        Map<Integer, Areainfo> areainfoMap;
        areainfoMap = areaInfoService.lambdaQuery().in(Areainfo::getId, areaIdList).list().stream()
                .collect(Collectors.toMap(Areainfo::getId, Function.identity(), (v1, v2) -> v1));
        for (Integer areaId : areaIdList) {
            Areainfo areainfo = areainfoMap.get(areaId);
            if (!Objects.isNull(areainfo)) {
                areas.add(areainfo.getArea());
            }
        }

        List<Map<String, String>> dataSource = new ArrayList<>();
        result.setDataSource(dataSource);
        // 查询数据
        List<AreaStockItemDTO> records = this.mapper.pageStatisticItem(req);

        if (CollectionUtils.isEmpty(records)) {
            res.put(RECORDS, result);
            Map<String, String> totalMap = new HashMap<>();
            res.put(TOTAL, totalMap);
            return res;
        }

        List<String> ppids = records.stream().map(AreaStockItemDTO::getPpid).collect(Collectors.toList());
        req.setPpids(ppids);
        HashBasedTable<String, String, AreaStockItemDTO> ppidAreaStockHashBasedTable = HashBasedTable.create();


//        Map<String, PpidInfoDTO> ppidToCidMap = this.mapper.selectCidByPpid(ppids).stream()
//                .collect(Collectors.toMap(PpidInfoDTO::getPpid, Function.identity(), (v1, v2) -> v1));

        Map<String, PpidInfoDTO> ppidToCidMap = CommonUtils.bigDataInQuery(ppids, this.mapper::selectCidByPpid)
                .stream().collect(Collectors.toMap(PpidInfoDTO::getPpid, Function.identity(), (v1, v2) -> v1));


        //初始化
        for (String ppid : ppids) {
            AreaStockItemDTO areaStockItemDTO = records.stream()
                    .filter((AreaStockItemDTO x) -> ppid.equals(x.getPpid()))
                    .findFirst().orElse(null);
            areaStockItemDTO.setDisplayKc(ZERO);
            areaStockItemDTO.setDiaoboOnWay(ZERO);
            areaStockItemDTO.setPurchaseOnWay(ZERO);
            areaStockItemDTO.setBasketKc(ZERO);
            areaStockItemDTO.setBasketOnWay(ZERO);
            areaStockItemDTO.setUnsatisfiedAmount(ZERO);
            ppidAreaStockHashBasedTable.put(ppid, ZERO.toString(), areaStockItemDTO);
            for (Integer areaId : areaIdList) {
                AreaStockItemDTO temp = new AreaStockItemDTO();
                temp.setAreaId(areaId);
                temp.setPpid(ppid);
                temp.setProductName(ppidToCidMap.get(ppid).getProductName());
                temp.setKc(ZERO);
                temp.setDisplayKc(ZERO);
                temp.setDiaoboOnWay(ZERO);
                temp.setPurchaseOnWay(ZERO);
                temp.setBasketKc(ZERO);
                temp.setBasketOnWay(ZERO);
                temp.setUnsatisfiedAmount(ZERO);
                temp.setTotalKc(ZERO);
                temp.setSale(ZERO);
                ppidAreaStockHashBasedTable.put(ppid, areaId.toString(), temp);
            }
        }

        //大件
        List<String> mobilePpidListTotal = ppidToCidMap.values().stream()
                .filter((PpidInfoDTO x) -> BoolEnum.TRUE.getCode().toString().equals(x.getIsmobile()))
                .map(PpidInfoDTO::getPpid).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(mobilePpidListTotal)) {
            List<List<String>> partition = Lists.partition(mobilePpidListTotal, NumberConstant.TWO_THOUSAND);
            for (List<String> mobilePpidList: partition) {
                AreaStockReqVO mobileReq = new AreaStockReqVO();
                BeanUtils.copyProperties(req, mobileReq);
                mobileReq.setPpids(mobilePpidList);
                if (Boolean.TRUE.equals(kcFlag)) {
                    //大件库存
                    List<AreaStockItemDTO> mobileKc = this.mapper.getMobileKc(mobileReq);
                    for (AreaStockItemDTO x : mobileKc) {
                        Integer areaId = x.getAreaId();
                        String ppid = x.getPpid();
                        Integer kc = x.getKc();
                        AreaStockItemDTO areaStockItemDTO = ppidAreaStockHashBasedTable.get(ppid, areaId.toString());
                        areaStockItemDTO.setKc(kc);
                    }
                }
                if (Boolean.TRUE.equals(saleFlag)) {
                    //大件销量
                    List<AreaStockItemDTO> mobileSale = this.mapper.getMobileSale(mobileReq);
                    for (AreaStockItemDTO x : mobileSale) {
                        Integer areaId = x.getAreaId();
                        String ppid = x.getPpid();
                        Integer sale = x.getSale();
                        AreaStockItemDTO areaStockItemDTO = ppidAreaStockHashBasedTable.get(ppid, areaId.toString());
                        areaStockItemDTO.setSale(sale);
                    }
                }
                if (Boolean.TRUE.equals(diaoboOnWayFlag)) {
                    //大件调拨在途
                    List<AreaStockItemDTO> mobileDiaoboOnWay = this.mapper.getMobileDiaoboOnWay(mobileReq);
                    Map<String, Integer> ppidToDiaoboOnWayMap = mobileDiaoboOnWay.stream()
                            .collect(Collectors.groupingBy(AreaStockItemDTO::getPpid, Collectors.summingInt(AreaStockItemDTO::getDiaoboOnWay)));
                    for (AreaStockItemDTO x : mobileDiaoboOnWay) {
                        Integer areaId = x.getAreaId();
                        String ppid = x.getPpid();
                        Integer diaoboOnWay = x.getDiaoboOnWay();
                        AreaStockItemDTO totalAreaStockItemDTO = ppidAreaStockHashBasedTable.get(ppid, ZERO.toString());
                        Integer totalDiaoboOnWay = ppidToDiaoboOnWayMap.get(ppid);
                        totalAreaStockItemDTO.setDiaoboOnWay(totalDiaoboOnWay);
                        AreaStockItemDTO areaStockItemDTO = ppidAreaStockHashBasedTable.get(ppid, areaId.toString());
                        areaStockItemDTO.setDiaoboOnWay(diaoboOnWay);
                    }
                }
                if (Boolean.TRUE.equals(purchaseOnWayFlag)) {
                    //大件采购在途
                    List<AreaStockItemDTO> mobilePurchaseOnWay = this.mapper.getMobilePurchaseOnWay(mobileReq);
                    Map<String, Integer> ppidToPurchaseOnWayMap = mobilePurchaseOnWay.stream()
                            .collect(Collectors.groupingBy(AreaStockItemDTO::getPpid, Collectors.summingInt(AreaStockItemDTO::getPurchaseOnWay)));
                    for (AreaStockItemDTO x : mobilePurchaseOnWay) {
                        Integer areaId = x.getAreaId();
                        String ppid = x.getPpid();
                        Integer purchaseOnWay = x.getPurchaseOnWay();
                        AreaStockItemDTO totalAreaStockItemDTO = ppidAreaStockHashBasedTable.get(ppid, ZERO.toString());
                        Integer totalPurchaseOnWay = ppidToPurchaseOnWayMap.get(ppid);
                        totalAreaStockItemDTO.setPurchaseOnWay(totalPurchaseOnWay);
                        AreaStockItemDTO areaStockItemDTO = ppidAreaStockHashBasedTable.get(ppid, areaId.toString());
                        areaStockItemDTO.setPurchaseOnWay(purchaseOnWay);
                    }
                }
                if (Boolean.TRUE.equals(basketKcFlag)) {
                    //大件已订库存
                    List<AreaStockItemDTO> mobileBasketKc = this.mapper.getMobileBasketKc(mobileReq);
                    Map<String, Integer> ppidToBasketKcMap = mobileBasketKc.stream()
                            .collect(Collectors.groupingBy(AreaStockItemDTO::getPpid, Collectors.summingInt(AreaStockItemDTO::getBasketKc)));
                    for (AreaStockItemDTO x : mobileBasketKc) {
                        Integer areaId = x.getAreaId();
                        String ppid = x.getPpid();
                        Integer basketKc = x.getBasketKc();
                        AreaStockItemDTO totalAreaStockItemDTO = ppidAreaStockHashBasedTable.get(ppid, ZERO.toString());
                        Integer totalBasketKc = ppidToBasketKcMap.get(ppid);
                        totalAreaStockItemDTO.setBasketKc(totalBasketKc);
                        AreaStockItemDTO areaStockItemDTO = ppidAreaStockHashBasedTable.get(ppid, areaId.toString());
                        areaStockItemDTO.setBasketKc(basketKc);
                    }
                }
                if (Boolean.TRUE.equals(basketOnWayFlag)) {
                    //大件已订在途
                    List<AreaStockItemDTO> mobileBasketOnWayOnWay = this.mapper.getMobileBasketOnWayOnWay(mobileReq);
                    Map<String, Integer> ppidToBasketOnWayMap = mobileBasketOnWayOnWay.stream()
                            .collect(Collectors.groupingBy(AreaStockItemDTO::getPpid, Collectors.summingInt(AreaStockItemDTO::getBasketOnWay)));
                    for (AreaStockItemDTO x : mobileBasketOnWayOnWay) {
                        Integer areaId = x.getAreaId();
                        String ppid = x.getPpid();
                        Integer basketOnWay = x.getBasketOnWay();
                        AreaStockItemDTO totalAreaStockItemDTO = ppidAreaStockHashBasedTable.get(ppid, ZERO.toString());
                        Integer totalBasketOnWay = ppidToBasketOnWayMap.get(ppid);
                        totalAreaStockItemDTO.setBasketOnWay(totalBasketOnWay);
                        AreaStockItemDTO areaStockItemDTO = ppidAreaStockHashBasedTable.get(ppid, areaId.toString());
                        areaStockItemDTO.setBasketOnWay(basketOnWay);
                    }
                }
            }
        }
        //查询cid
        List<String> repairCategoryChildrenList = this.mapper.selectRepairCategoryChildren();
        //小件
        List<String> accessoriesPpidListTotal = ppidToCidMap.values().stream()
                .filter((PpidInfoDTO x) -> !BoolEnum.TRUE.getCode().toString().equals(x.getIsmobile())
                        && !repairCategoryChildrenList.contains(x.getCid()))
                .map(PpidInfoDTO::getPpid).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(accessoriesPpidListTotal)) {
            List<List<String>> partition = Lists.partition(accessoriesPpidListTotal, NumberConstant.TWO_THOUSAND);
            for (List<String> accessoriesPpidList: partition) {
                AreaStockReqVO accessaoriesReq = new AreaStockReqVO();
                BeanUtils.copyProperties(req, accessaoriesReq);
                accessaoriesReq.setPpids(accessoriesPpidList);
                //小件库存
                if (Boolean.TRUE.equals(kcFlag)) {
                    List<AreaStockItemDTO> accessoriesKc = this.mapper.getAccessoriesKc(accessaoriesReq);
                    for (AreaStockItemDTO x : accessoriesKc) {
                        Integer areaId = x.getAreaId();
                        String ppid = x.getPpid();
                        Integer kc = x.getKc();
                        AreaStockItemDTO areaStockItemDTO = ppidAreaStockHashBasedTable.get(ppid, areaId.toString());
                        areaStockItemDTO.setKc(kc);
                    }
                }

                //小件销量
                if (Boolean.TRUE.equals(saleFlag)) {
                    List<AreaStockItemDTO> accessoriesSale = this.mapper.getAccessoriesSale(accessaoriesReq);
                    for (AreaStockItemDTO x : accessoriesSale) {
                        Integer areaId = x.getAreaId();
                        String ppid = x.getPpid();
                        Integer sale = x.getSale();
                        AreaStockItemDTO areaStockItemDTO = ppidAreaStockHashBasedTable.get(ppid, areaId.toString());
                        areaStockItemDTO.setSale(sale);
                    }
                }

                if (Boolean.TRUE.equals(displayKcFlag)) {
                    //小件陈列库存
                    List<AreaStockItemDTO> accessoriesDisplayKc = this.mapper.getAccessoriesDisplayKc(accessaoriesReq);
                    Map<String, Integer> ppidToDisplayKcMap = accessoriesDisplayKc.stream()
                            .collect(Collectors.groupingBy(AreaStockItemDTO::getPpid, Collectors.summingInt(AreaStockItemDTO::getDisplayKc)));
                    for (AreaStockItemDTO x : accessoriesDisplayKc) {
                        Integer areaId = x.getAreaId();
                        String ppid = x.getPpid();
                        Integer displayKc = x.getDisplayKc();
                        AreaStockItemDTO totalAreaStockItemDTO = ppidAreaStockHashBasedTable.get(ppid, ZERO.toString());
                        Integer totalDisplayKc = ppidToDisplayKcMap.get(ppid);
                        totalAreaStockItemDTO.setDisplayKc(totalDisplayKc);
                        AreaStockItemDTO areaStockItemDTO = ppidAreaStockHashBasedTable.get(ppid, areaId.toString());
                        areaStockItemDTO.setDisplayKc(displayKc);
                    }
                }
                if (Boolean.TRUE.equals(diaoboOnWayFlag)) {
                    //小件调拨在途
                    List<AreaStockItemDTO> accessoriesDiaoboOnWay = this.mapper.getAccessoriesDiaoboOnWay(accessaoriesReq);
                    Map<String, Integer> ppidToDiaoboOnWayMap = accessoriesDiaoboOnWay.stream()
                            .collect(Collectors.groupingBy(AreaStockItemDTO::getPpid, Collectors.summingInt(AreaStockItemDTO::getDiaoboOnWay)));
                    for (AreaStockItemDTO x : accessoriesDiaoboOnWay) {
                        Integer areaId = x.getAreaId();
                        String ppid = x.getPpid();
                        Integer diaoboOnWay = x.getDiaoboOnWay();
                        AreaStockItemDTO totalAreaStockItemDTO = ppidAreaStockHashBasedTable.get(ppid, ZERO.toString());
                        Integer totalDiaoboOnWay = ppidToDiaoboOnWayMap.get(ppid);
                        totalAreaStockItemDTO.setDiaoboOnWay(totalDiaoboOnWay);
                        AreaStockItemDTO areaStockItemDTO = ppidAreaStockHashBasedTable.get(ppid, areaId.toString());
                        areaStockItemDTO.setDiaoboOnWay(diaoboOnWay);
                    }
                }

                if (Boolean.TRUE.equals(purchaseOnWayFlag)) {
                    //小件采购在途
                    List<AreaStockItemDTO> accessaoriesPurchaseOnWay = this.mapper.getAccessaoriesPurchaseOnWay(accessaoriesReq);
                    Map<String, Integer> ppidToPurchaseOnWayMap = accessaoriesPurchaseOnWay.stream()
                            .collect(Collectors.groupingBy(AreaStockItemDTO::getPpid, Collectors.summingInt(AreaStockItemDTO::getPurchaseOnWay)));
                    for (AreaStockItemDTO x : accessaoriesPurchaseOnWay) {
                        Integer areaId = x.getAreaId();
                        String ppid = x.getPpid();
                        Integer purchaseOnWay = x.getPurchaseOnWay();
                        AreaStockItemDTO totalAreaStockItemDTO = ppidAreaStockHashBasedTable.get(ppid, ZERO.toString());
                        Integer totalPurchaseOnWay = ppidToPurchaseOnWayMap.get(ppid);
                        totalAreaStockItemDTO.setPurchaseOnWay(totalPurchaseOnWay);
                        AreaStockItemDTO areaStockItemDTO = ppidAreaStockHashBasedTable.get(ppid, areaId.toString());
                        areaStockItemDTO.setPurchaseOnWay(purchaseOnWay);
                    }
                }
                if (Boolean.TRUE.equals(basketKcFlag)) {
                    //小件已订库存
                    List<AreaStockItemDTO> accessaoriesBasketKc = this.mapper.getAccessaoriesBasketKc(accessaoriesReq);
                    Map<String, Integer> ppidToBasketKcMap = accessaoriesBasketKc.stream()
                            .collect(Collectors.groupingBy(AreaStockItemDTO::getPpid, Collectors.summingInt(AreaStockItemDTO::getBasketKc)));
                    for (AreaStockItemDTO x : accessaoriesBasketKc) {
                        Integer areaId = x.getAreaId();
                        String ppid = x.getPpid();
                        Integer basketKc = x.getBasketKc();
                        AreaStockItemDTO totalAreaStockItemDTO = ppidAreaStockHashBasedTable.get(ppid, ZERO.toString());
                        Integer totalBasketKc = ppidToBasketKcMap.get(ppid);
                        totalAreaStockItemDTO.setBasketKc(totalBasketKc);
                        AreaStockItemDTO areaStockItemDTO = ppidAreaStockHashBasedTable.get(ppid, areaId.toString());
                        areaStockItemDTO.setBasketKc(basketKc);
                    }
                }
                if (Boolean.TRUE.equals(basketOnWayFlag)) {
                    //小件已订在途
                    List<AreaStockItemDTO> accessaoriesBasketOnWayOnWay = this.mapper.getAccessaoriesBasketOnWayOnWay(accessaoriesReq);
                    Map<String, Integer> ppidToBasketOnWayMap = accessaoriesBasketOnWayOnWay.stream()
                            .collect(Collectors.groupingBy(AreaStockItemDTO::getPpid, Collectors.summingInt(AreaStockItemDTO::getBasketOnWay)));
                    for (AreaStockItemDTO x : accessaoriesBasketOnWayOnWay) {
                        Integer areaId = x.getAreaId();
                        String ppid = x.getPpid();
                        Integer basketOnWay = x.getBasketOnWay();
                        AreaStockItemDTO totalAreaStockItemDTO = ppidAreaStockHashBasedTable.get(ppid, ZERO.toString());
                        Integer totalBasketOnWay = ppidToBasketOnWayMap.get(ppid);
                        totalAreaStockItemDTO.setBasketOnWay(totalBasketOnWay);
                        AreaStockItemDTO areaStockItemDTO = ppidAreaStockHashBasedTable.get(ppid, areaId.toString());
                        areaStockItemDTO.setBasketOnWay(basketOnWay);
                    }
                }
                if (Boolean.TRUE.equals(unsatisfiedAmountFlag)) {
                    //小件未满足量
                    List<AreaStockItemDTO> accessoriesUnsatisfiedAmount = this.mapper.getAccessoriesUnsatisfiedAmount(accessaoriesReq);
                    Map<String, Integer> ppidToUnsatisfiedAmountMap = accessoriesUnsatisfiedAmount.stream()
                            .collect(Collectors.groupingBy(AreaStockItemDTO::getPpid, Collectors.summingInt(AreaStockItemDTO::getUnsatisfiedAmount)));
                    for (AreaStockItemDTO x : accessoriesUnsatisfiedAmount) {
                        Integer areaId = x.getAreaId();
                        String ppid = x.getPpid();
                        Integer unsatisfiedAmount = x.getUnsatisfiedAmount();
                        AreaStockItemDTO totalAreaStockItemDTO = ppidAreaStockHashBasedTable.get(ppid, ZERO.toString());
                        Integer totalUnsatisfiedAmount = ppidToUnsatisfiedAmountMap.get(ppid);
                        totalAreaStockItemDTO.setUnsatisfiedAmount(totalUnsatisfiedAmount);
                        AreaStockItemDTO areaStockItemDTO = ppidAreaStockHashBasedTable.get(ppid, areaId.toString());
                        areaStockItemDTO.setUnsatisfiedAmount(unsatisfiedAmount);
                    }
                }
            }

        }

        //维修配件
        List<String> repairAccessoriesPpidListTotal = ppidToCidMap.values().stream()
                .filter((PpidInfoDTO x) -> !BoolEnum.TRUE.getCode().toString().equals(x.getIsmobile())
                        && repairCategoryChildrenList.contains(x.getCid()))
                .map(PpidInfoDTO::getPpid).collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(repairAccessoriesPpidListTotal)) {
            List<List<String>> partition = Lists.partition(repairAccessoriesPpidListTotal, NumberConstant.TWO_THOUSAND);
            for (List<String> repairAccessoriesPpidList: partition) {
                AreaStockReqVO repairAccessoriesReq = new AreaStockReqVO();
                BeanUtils.copyProperties(req, repairAccessoriesReq);
                repairAccessoriesReq.setPpids(repairAccessoriesPpidList);
                if (Boolean.TRUE.equals(kcFlag)) {
                    //维修配件库存
                    List<AreaStockItemDTO> mobileKc = this.mapper.getAccessoriesKc(repairAccessoriesReq);
                    for (AreaStockItemDTO x : mobileKc) {
                        Integer areaId = x.getAreaId();
                        String ppid = x.getPpid();
                        Integer kc = x.getKc();
                        AreaStockItemDTO areaStockItemDTO = ppidAreaStockHashBasedTable.get(ppid, areaId.toString());
                        areaStockItemDTO.setKc(kc);
                    }
                }
                if (Boolean.TRUE.equals(saleFlag)) {
                    //维修配件销量
                    List<AreaStockItemDTO> repairAccessoriesSale = this.mapper.getRepairAccessoriesSale(repairAccessoriesReq);
                    for (AreaStockItemDTO x : repairAccessoriesSale) {
                        Integer areaId = x.getAreaId();
                        String ppid = x.getPpid();
                        Integer sale = x.getSale();
                        AreaStockItemDTO areaStockItemDTO = ppidAreaStockHashBasedTable.get(ppid, areaId.toString());
                        areaStockItemDTO.setSale(sale);
                    }
                }
                if (Boolean.TRUE.equals(diaoboOnWayFlag)) {
                    //维修配件调拨在途
                    List<AreaStockItemDTO> repairAccessoriesDiaoboOnWay = this.mapper.getRepairAccessoriesDiaoboOnWay(repairAccessoriesReq);
                    Map<String, Integer> ppidToDiaoboOnWayMap = repairAccessoriesDiaoboOnWay.stream()
                            .collect(Collectors.groupingBy(AreaStockItemDTO::getPpid, Collectors.summingInt(AreaStockItemDTO::getDiaoboOnWay)));
                    for (AreaStockItemDTO x : repairAccessoriesDiaoboOnWay) {
                        Integer areaId = x.getAreaId();
                        String ppid = x.getPpid();
                        Integer diaoboOnWay = x.getDiaoboOnWay();
                        AreaStockItemDTO totalAreaStockItemDTO = ppidAreaStockHashBasedTable.get(ppid, ZERO.toString());
                        Integer totalDiaoboOnWay = ppidToDiaoboOnWayMap.get(ppid);
                        totalAreaStockItemDTO.setDiaoboOnWay(totalDiaoboOnWay);
                        AreaStockItemDTO areaStockItemDTO = ppidAreaStockHashBasedTable.get(ppid, areaId.toString());
                        areaStockItemDTO.setDiaoboOnWay(diaoboOnWay);
                    }
                }
                if (Boolean.TRUE.equals(purchaseOnWayFlag)) {
                    //维修配件采购在途
                    List<AreaStockItemDTO> repairAccessaoriesPurchaseOnWay = this.mapper.getRepairAccessaoriesPurchaseOnWay(repairAccessoriesReq);
                    Map<String, Integer> ppidToPurchaseOnWayMap = repairAccessaoriesPurchaseOnWay.stream()
                            .collect(Collectors.groupingBy(AreaStockItemDTO::getPpid, Collectors.summingInt(AreaStockItemDTO::getPurchaseOnWay)));
                    for (AreaStockItemDTO x : repairAccessaoriesPurchaseOnWay) {
                        Integer areaId = x.getAreaId();
                        String ppid = x.getPpid();
                        Integer purchaseOnWay = x.getPurchaseOnWay();
                        AreaStockItemDTO totalAreaStockItemDTO = ppidAreaStockHashBasedTable.get(ppid, ZERO.toString());
                        Integer totalPurchaseOnWay = ppidToPurchaseOnWayMap.get(ppid);
                        totalAreaStockItemDTO.setPurchaseOnWay(totalPurchaseOnWay);
                        AreaStockItemDTO areaStockItemDTO = ppidAreaStockHashBasedTable.get(ppid, areaId.toString());
                        areaStockItemDTO.setPurchaseOnWay(purchaseOnWay);
                    }
                }
                if (Boolean.TRUE.equals(basketKcFlag)) {
                    //维修配件已订库存
                    List<AreaStockItemDTO> repairAccessoriesBasketKc = this.mapper.getRepairAccessaoriesBasketKc(repairAccessoriesReq);
                    Map<String, Integer> ppidToBasketKcMap = repairAccessoriesBasketKc.stream()
                            .collect(Collectors.groupingBy(AreaStockItemDTO::getPpid, Collectors.summingInt(AreaStockItemDTO::getBasketKc)));
                    for (AreaStockItemDTO x : repairAccessoriesBasketKc) {
                        Integer areaId = x.getAreaId();
                        String ppid = x.getPpid();
                        Integer basketKc = x.getBasketKc();
                        AreaStockItemDTO totalAreaStockItemDTO = ppidAreaStockHashBasedTable.get(ppid, ZERO.toString());
                        Integer totalBasketKc = ppidToBasketKcMap.get(ppid);
                        totalAreaStockItemDTO.setBasketKc(totalBasketKc);
                        AreaStockItemDTO areaStockItemDTO = ppidAreaStockHashBasedTable.get(ppid, areaId.toString());
                        areaStockItemDTO.setBasketKc(basketKc);
                    }
                }
                if (Boolean.TRUE.equals(basketOnWayFlag)) {
                    //维修配件已订在途
                    List<AreaStockItemDTO> repairAccessaoriesBasketOnWayOnWay = this.mapper.getAccessaoriesBasketOnWayOnWay(repairAccessoriesReq);
                    Map<String, Integer> ppidToBasketOnWayMap = repairAccessaoriesBasketOnWayOnWay.stream()
                            .collect(Collectors.groupingBy(AreaStockItemDTO::getPpid, Collectors.summingInt(AreaStockItemDTO::getBasketOnWay)));
                    for (AreaStockItemDTO x : repairAccessaoriesBasketOnWayOnWay) {
                        Integer areaId = x.getAreaId();
                        String ppid = x.getPpid();
                        Integer basketOnWay = x.getBasketOnWay();
                        AreaStockItemDTO totalAreaStockItemDTO = ppidAreaStockHashBasedTable.get(ppid, ZERO.toString());
                        Integer totalBasketOnWay = ppidToBasketOnWayMap.get(ppid);
                        totalAreaStockItemDTO.setBasketOnWay(totalBasketOnWay);
                        AreaStockItemDTO areaStockItemDTO = ppidAreaStockHashBasedTable.get(ppid, areaId.toString());
                        areaStockItemDTO.setBasketOnWay(basketOnWay);
                    }
                }
                if (Boolean.TRUE.equals(unsatisfiedAmountFlag)) {
                    //维修配件未满足量
                    List<AreaStockItemDTO> repairAccessoriesUnsatisfiedAmount = this.mapper.getAccessoriesUnsatisfiedAmount(repairAccessoriesReq);
                    Map<String, Integer> ppidToUnsatisfiedAmountMap = repairAccessoriesUnsatisfiedAmount.stream()
                            .collect(Collectors.groupingBy(AreaStockItemDTO::getPpid, Collectors.summingInt(AreaStockItemDTO::getUnsatisfiedAmount)));
                    for (AreaStockItemDTO x : repairAccessoriesUnsatisfiedAmount) {
                        Integer areaId = x.getAreaId();
                        String ppid = x.getPpid();
                        Integer unsatisfiedAmount = x.getUnsatisfiedAmount();
                        AreaStockItemDTO totalAreaStockItemDTO = ppidAreaStockHashBasedTable.get(ppid, ZERO.toString());
                        Integer totalUnsatisfiedAmount = ppidToUnsatisfiedAmountMap.get(ppid);
                        totalAreaStockItemDTO.setUnsatisfiedAmount(totalUnsatisfiedAmount);
                        AreaStockItemDTO areaStockItemDTO = ppidAreaStockHashBasedTable.get(ppid, areaId.toString());
                        areaStockItemDTO.setUnsatisfiedAmount(unsatisfiedAmount);
                    }
                }
            }

        }

        areaIdList.add(ZERO, ZERO);
        int orderNumber = 1;

        Map<String, String> totalMap = new HashMap<>();
        totalMap.put("productName", "合计");
        for (String ppid : ppids) {
            HashMap<String, String> data = new HashMap<>();
            PpidInfoDTO ppidInfoDTO = ppidToCidMap.get(ppid);
            String productName = ppidInfoDTO.getProductName();
            String productColor = ppidInfoDTO.getProductColor();
            data.put("ppid", ppid);
            data.put("productName", productName);
            data.put("productColor", productColor);
            data.put("orderNumber", String.valueOf(orderNumber));
            orderNumber++;
            for (int i = ZERO; i < areaIdList.size(); i++) {
                Integer areaId = areaIdList.get(i);
                AreaStockItemDTO areaStockItemDTO;
                areaStockItemDTO = ppidAreaStockHashBasedTable.get(ppid, areaId.toString());
                if (Objects.isNull(areaStockItemDTO)) {
                    areaStockItemDTO = new AreaStockItemDTO();
                    areaStockItemDTO.setKc(ZERO);
                    areaStockItemDTO.setDisplayKc(ZERO);
                    areaStockItemDTO.setDiaoboOnWay(ZERO);
                    areaStockItemDTO.setPurchaseOnWay(ZERO);
                    areaStockItemDTO.setBasketKc(ZERO);
                    areaStockItemDTO.setBasketOnWay(ZERO);
                    areaStockItemDTO.setUnsatisfiedAmount(ZERO);
                    areaStockItemDTO.setTotalKc(ZERO);
                    areaStockItemDTO.setSale(ZERO);
                }
                if (Boolean.TRUE.equals(kcFlag)) {
                    String key = AreaStockEnum.KC.getMessage() + i;
                    Integer kc = areaStockItemDTO.getKc();
                    data.put(key, kc.toString());
                    Integer total = Objects.isNull(totalMap.get(key))
                            ? 0 : Integer.parseInt(totalMap.get(key));
                    totalMap.put(key, String.valueOf(total + kc));
                }
                if (Boolean.TRUE.equals(saleFlag)) {
                    String key = AreaStockEnum.SALE.getMessage() + i;
                    Integer sale = areaStockItemDTO.getSale();
                    data.put(key, sale.toString());
                    Integer total = Objects.isNull(totalMap.get(key))
                            ? 0 : Integer.parseInt(totalMap.get(key));
                    totalMap.put(key, String.valueOf(total + sale));
                }
                if (Boolean.TRUE.equals(displayKcFlag)) {
                    String key = AreaStockEnum.DISPLAY_KC.getMessage() + i;
                    Integer displayKc = areaStockItemDTO.getDisplayKc();
                    data.put(key, displayKc.toString());
                    Integer total = Objects.isNull(totalMap.get(key))
                            ? 0 : Integer.parseInt(totalMap.get(key));
                    totalMap.put(key, String.valueOf(total + displayKc));
                }
                if (Boolean.TRUE.equals(diaoboOnWayFlag)) {
                    String key = AreaStockEnum.DIAOBO_ON_WAY.getMessage() + i;
                    Integer diaoboOnWay = areaStockItemDTO.getDiaoboOnWay();
                    data.put(key, diaoboOnWay.toString());
                    Integer total = Objects.isNull(totalMap.get(key))
                            ? 0 : Integer.parseInt(totalMap.get(key));
                    totalMap.put(key, String.valueOf(total + diaoboOnWay));
                }
                if (Boolean.TRUE.equals(purchaseOnWayFlag)) {
                    String key = AreaStockEnum.PURCHASE_ON_WAY.getMessage() + i;
                    Integer purchaseOnWay = areaStockItemDTO.getPurchaseOnWay();
                    data.put(key, purchaseOnWay.toString());
                    Integer total = Objects.isNull(totalMap.get(key))
                            ? 0 : Integer.parseInt(totalMap.get(key));
                    totalMap.put(key, String.valueOf(total + purchaseOnWay));
                }
                if (Boolean.TRUE.equals(basketKcFlag)) {
                    String key = AreaStockEnum.BASKET_KC.getMessage() + i;
                    Integer basketKc = areaStockItemDTO.getBasketKc();
                    data.put(key, basketKc.toString());
                    Integer total = Objects.isNull(totalMap.get(key))
                            ? 0 : Integer.parseInt(totalMap.get(key));
                    totalMap.put(key, String.valueOf(total + basketKc));
                }
                if (Boolean.TRUE.equals(basketOnWayFlag)) {
                    String key = AreaStockEnum.BASKET_ON_WAY.getMessage() + i;
                    Integer basketOnWay = areaStockItemDTO.getBasketOnWay();
                    data.put(key, basketOnWay.toString());
                    Integer total = Objects.isNull(totalMap.get(key))
                            ? 0 : Integer.parseInt(totalMap.get(key));
                    totalMap.put(key, String.valueOf(total + basketOnWay));
                }
                if (Boolean.TRUE.equals(unsatisfiedAmountFlag)) {
                    String key = AreaStockEnum.UNSATISFIED_AMOUNT.getMessage() + i;
                    Integer unsatisfiedAmount = areaStockItemDTO.getUnsatisfiedAmount();
                    data.put(key, unsatisfiedAmount.toString());
                    Integer total = Objects.isNull(totalMap.get(key))
                            ? 0 : Integer.parseInt(totalMap.get(key));
                    totalMap.put(key, String.valueOf(total + unsatisfiedAmount));
                }
                if (Boolean.TRUE.equals(totalKcFlag)) {
                    String key = AreaStockEnum.TOTAL_KC.getMessage() + i;
                    int value = areaStockItemDTO.getKc()
                            + areaStockItemDTO.getDiaoboOnWay()
                            + areaStockItemDTO.getPurchaseOnWay();
                    data.put(key, String.valueOf(value));
                    int total = Objects.isNull(totalMap.get(key))
                            ? 0 : Integer.parseInt(totalMap.get(key));
                    totalMap.put(key, String.valueOf(total + value));
                }
            }
            dataSource.add(data);
        }
        res.put(RECORDS, result);
        res.put(TOTAL, totalMap);
        return res;
    }

    @Override
    public Map<String, Object> searchEnumV1(OaUserBO oaUserBO) {
        List<LPBidResultEnumExtendRes> accessoryProductLabel = AccessoryProductLabelEnum.getList();
        List<BrandVo> allBrandList = categoryBusService.getAllBrandList();
        List<BrandDTO> allBrandDtoList = new ArrayList<>();
        allBrandList.forEach(x -> allBrandDtoList.add(mkcMapStruct.toBrandDtoList(x)));

        Map<String, Object> map = new ConcurrentHashMap<>(MapUtil.DEFAULT_INITIAL_CAPACITY);
        map.put("labels", accessoryProductLabel);
        map.put("allBrandList", allBrandDtoList);
        return map;

    }

    @Override
    public void exportExcel(HttpServletResponse response, AreaStockReqVO req) {
        Map<String, Object> res = this.pageStatistic(req);
        Object record = res.get(RECORDS);
        AreaStockResVO areaStockResVO = (AreaStockResVO) record;
        List<String> areas = areaStockResVO.getAreas();
        List<Map<String, String>> dataSource = areaStockResVO.getDataSource();
        AreaStockReqVO.ShowFlag showFlag = req.getShowFlag();
        Boolean kcFlag = showFlag.getKcFlag();
        Boolean displayKcFlag = showFlag.getDisplayKcFlag();
        Boolean diaoboOnWayFlag = showFlag.getDiaoboOnWayFlag();
        Boolean purchaseOnWayFlag = showFlag.getPurchaseOnWayFlag();
        Boolean basketKcFlag = showFlag.getBasketKcFlag();
        Boolean basketOnWayFlag = showFlag.getBasketOnWayFlag();
        Boolean unsatisfiedAmountFlag = showFlag.getUnsatisfiedAmountFlag();
        Boolean totalKcFlag = showFlag.getTotalKcFlag();
        Boolean saleFlag = showFlag.getSaleFlag();

        ExcelWriter excel = ExcelUtil.getBigWriter();
        excel.addHeaderAlias("orderNumber", "序号");
        excel.addHeaderAlias("productName", "商品名称");
        excel.addHeaderAlias("productColor", "规格");
        excel.addHeaderAlias("ppid", "ppid");
        int columnNum = 0;
        for (int i = ZERO; i < areas.size(); i++) {
            if (columnNum >= COLUMN_BOUND) {
                break;
            }
            String area = areas.get(i);
            if (kcFlag) {
                columnNum++;
                excel.addHeaderAlias(AreaStockEnum.KC.getMessage() + i, area + StrPool.UNDERLINE + "库存");
            }
            if (displayKcFlag) {
                columnNum++;
                excel.addHeaderAlias(AreaStockEnum.DISPLAY_KC.getMessage() + i, area + StrPool.UNDERLINE + "陈列库存");
            }
            if (diaoboOnWayFlag) {
                columnNum++;
                excel.addHeaderAlias(AreaStockEnum.DIAOBO_ON_WAY.getMessage() + i, area + StrPool.UNDERLINE + "调拨在途");
            }
            if (purchaseOnWayFlag) {
                columnNum++;
                excel.addHeaderAlias(AreaStockEnum.PURCHASE_ON_WAY.getMessage() + i, area + StrPool.UNDERLINE + "采购在途");
            }
            if (basketKcFlag) {
                columnNum++;
                excel.addHeaderAlias(AreaStockEnum.BASKET_KC.getMessage() + i, area + StrPool.UNDERLINE + "已订库存");
            }
            if (basketOnWayFlag) {
                columnNum++;
                excel.addHeaderAlias(AreaStockEnum.BASKET_ON_WAY.getMessage() + i, area + StrPool.UNDERLINE + "已订在途");
            }
            if (unsatisfiedAmountFlag) {
                columnNum++;
                excel.addHeaderAlias(AreaStockEnum.UNSATISFIED_AMOUNT.getMessage() + i, area + StrPool.UNDERLINE + "未满足量");
            }
            if (totalKcFlag) {
                columnNum++;
                excel.addHeaderAlias(AreaStockEnum.TOTAL_KC.getMessage() + i, area + StrPool.UNDERLINE + "合计库存");
            }
            if (saleFlag) {
                columnNum++;
                excel.addHeaderAlias(AreaStockEnum.SALE.getMessage() + i, area + StrPool.UNDERLINE + "销量");
            }
        }
        if (dataSource.size() <= 0) {
            dataSource = new ArrayList<>();
        }
        excel.write(dataSource, true);
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        response.setCharacterEncoding("utf-8");
        ServletOutputStream out = null;
        try {
            String fileName = URLEncoder.encode("门店库存销量", "UTF-8") + StrPool.UNDERLINE + LocalDateTime.now().toLocalDate() + ".xlsx";
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            out = response.getOutputStream();
        } catch (Exception e) {
            throw new RRException("导出excel出错");
        } finally {
            excel.flush(out, true);
            excel.close();
            IoUtil.close(out);
        }
    }

    @Override
    public List<StockAreaAmountResVO> getStockAmountByPpidAndArea(StockAreaAmountReqVO req) {
        List<StockAreaAmountResVO> stockAmountResVO = new ArrayList<>();
        ProductInfoEntity productInfo = productInfoService.getByPpid(Long.valueOf(req.getPpid().toString()));
        if (Objects.isNull(productInfo)) {
            return stockAmountResVO;
        }
        if (productInfo.getMobile()) {
            stockAmountResVO = stockStatisticsService.getStockAmountByPpidAndArea(req);
        } else {
            stockAmountResVO = accessoryStockStatisticsService.getStockAmountByPpidAndArea(req);
        }
        Map<Long, StockAreaAmountResVO> areaIdToStockAreaAmountMap = stockAmountResVO.stream().collect(Collectors.toMap(StockAreaAmountResVO::getAreaId, Function.identity(), (v1, v2) -> v1));
        List<Long> areaIds = req.getAreaIds();
        for (Long areaId : areaIds) {
            StockAreaAmountResVO stockAreaAmountResVO = areaIdToStockAreaAmountMap.get(areaId);
            if (Objects.isNull(stockAreaAmountResVO)) {
                StockAreaAmountResVO temp = new StockAreaAmountResVO();
                temp.setPpid(req.getPpid());
                temp.setAreaId(areaId);
                temp.setTotalAmount(0);
                stockAmountResVO.add(temp);
            }
        }
        return stockAmountResVO;
    }

    @Override
    public Map<String, Object> areaStockControlReport(JSONObject req) {
        Map<String, Object> result = new HashMap<>();

        AreaStockControlReportReqVO areaStockResVO = JSONObject.parseObject(JSONObject.toJSONString(req), AreaStockControlReportReqVO.class);

//        List<Integer> searchAreaIdList = new ArrayList<>();
//
//        List<Integer> areaIdList = Objects.isNull(areaStockResVO.getAreaIds()) ? new ArrayList<>() :areaStockResVO.getAreaIds();
//        List<Integer> regionAreaIdList = Objects.isNull(areaStockResVO.getRegionAreaIdList()) ? new ArrayList<>() : areaStockResVO.getRegionAreaIdList();
//        searchAreaIdList.addAll(areaIdList);
//        searchAreaIdList.addAll(regionAreaIdList);
//        boolean areaIdListNotEmptyFlag = com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(areaIdList);
//        boolean regionAreaIdListNotEmptyFlag = com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(regionAreaIdList);
//        if (areaIdListNotEmptyFlag && regionAreaIdListNotEmptyFlag) {
//            searchAreaIdList = searchAreaIdList.stream()
//                    .filter(x -> areaIdList.contains(x) && regionAreaIdList.contains(x)).distinct().collect(Collectors.toList());
//        }

//        areaStockResVO.setAreaIds(searchAreaIdList);
        HttpResponse response = HttpUtil.createPost(URL)
                .header("sign", DigestUtil.md5Hex(LocalDate.now().toString()))
                .body(req.toJSONString())
                .execute();
        String body = response.body();
        R r = JSON.parseObject(body, R.class);

        if (r.getCode() != 0) {
            throw new CustomizeException("报表中心连接繁忙，请稍后再试:" + r.getMsg());
        }

        Object record = r.getData();

        AreaStockControlReportResVO res = JSONObject.parseObject(JSONObject.toJSONString(record), AreaStockControlReportResVO.class);
        int current = Objects.isNull(areaStockResVO.getCurrent()) ? 1 : areaStockResVO.getCurrent();
        int size = Objects.isNull(areaStockResVO.getSize()) ? 10 : areaStockResVO.getSize();
        if (Objects.isNull(res.getData())) {
            result.put("data", new ArrayList<>());
            result.put("total", new AreaStockControlReportResVO.AreaStockControlReportResData());
            return result;
        }

        List<AreaStockControlReportResVO.AreaStockControlReportResData> data = res.getData();

        Page<AreaStockControlReportResVO.AreaStockControlReportResData> pageResult = new Page<>(current, size);

        //逻辑分页
        List<AreaStockControlReportResVO.AreaStockControlReportResData> pageRecord
                = data.stream()
                .filter(x -> !Objects.isNull(x.getArea())).skip(size * (current - 1)).limit(size).collect(Collectors.toList());

        pageResult.setRecords(pageRecord);
        pageResult.setTotal(data.size());
        pageResult.setSize(size);
        pageResult.setCurrent(current);

        result.put("data", pageResult);
        result.put("total", res.getTotal());

        return result;
    }

    @Override
    public void areaStockControlReportExportExcel(HttpServletResponse response, JSONObject req) {
        req.put("current", 1);
        req.put("size", 5000);
        Map<String, Object> map = this.areaStockControlReport(req);
        Page<AreaStockControlReportResVO.AreaStockControlReportResData> r = (Page<AreaStockControlReportResVO.AreaStockControlReportResData>) map.get("data");
        List<AreaStockControlReportResVO.AreaStockControlReportResData> data = r.getRecords();
        AreaStockControlReportResVO.AreaStockControlReportResData total = (AreaStockControlReportResVO.AreaStockControlReportResData) map.get("total");
        data.add(total);
        List<Map<String, String>> dataSource = new ArrayList<>();
        AreaStockControlReportReqVO areaStockResVO = JSONObject.parseObject(req.toJSONString(), AreaStockControlReportReqVO.class);
        AreaStockControlReportReqVO.ShowFlag showFlag = areaStockResVO.getShowFlag();

        ExcelWriter excel = ExcelUtil.getBigWriter();
        excel.addHeaderAlias(AreaStockControlReportEnum.RISK_LEVEL.getMessage(), AreaStockControlReportEnum.RISK_LEVEL.getMessageName());
        excel.addHeaderAlias(AreaStockControlReportEnum.AREA.getMessage(), AreaStockControlReportEnum.AREA.getMessageName());
        if (Boolean.TRUE.equals(showFlag.getIsStockShareFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.IS_STOCK_SHARE.getMessage(), AreaStockControlReportEnum.IS_STOCK_SHARE.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getLevel1NameFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.LEVEL_1_NAME.getMessage(), AreaStockControlReportEnum.LEVEL_1_NAME.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getCityNameFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.CITY_NAME.getMessage(), AreaStockControlReportEnum.CITY_NAME.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getStockCountFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.STOCK_COUNT.getMessage(), AreaStockControlReportEnum.STOCK_COUNT.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getDiffLevelStockCountFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.DIFF_LEVEL_STOCK_COUNT.getMessage(), AreaStockControlReportEnum.DIFF_LEVEL_STOCK_COUNT.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getStockControlFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.STOCK_CONTROL.getMessage(), AreaStockControlReportEnum.STOCK_CONTROL.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getDay30SaleCountFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.DAY_30_SALE_COUNT.getMessage(), AreaStockControlReportEnum.DAY_30_SALE_COUNT.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getDiffLevel30DaySaleCountFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.DIFF_LEVEL_30_DAY_SALE_COUNT.getMessage(), AreaStockControlReportEnum.DIFF_LEVEL_30_DAY_SALE_COUNT.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getDay30SaleControlFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.DAY_30_SALE_CONTROL.getMessage(), AreaStockControlReportEnum.DAY_30_SALE_CONTROL.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getInStockOnWayCountFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.IN_STOCK_ON_WAY_COUNT.getMessage(), AreaStockControlReportEnum.IN_STOCK_ON_WAY_COUNT.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getDiffLevelInStockOnWayCountFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.DIFF_LEVEL_IN_STOCK_ON_WAY_COUNT.getMessage(), AreaStockControlReportEnum.DIFF_LEVEL_IN_STOCK_ON_WAY_COUNT.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getToTodayTransferCountFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.TO_TODAY_TRANSFER_COUNT.getMessage(), AreaStockControlReportEnum.TO_TODAY_TRANSFER_COUNT.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getTo30DayAvgTransferCountFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.TO_30_DAY_AVG_TRANSFER_COUNT.getMessage(), AreaStockControlReportEnum.TO_30_DAY_AVG_TRANSFER_COUNT.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getFromTodayTransferCountFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.FROM_TODAY_TRANSFER_COUNT.getMessage(), AreaStockControlReportEnum.FROM_TODAY_TRANSFER_COUNT.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getFrom30DayAvgTransferCountFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.FROM_30_DAY_AVG_TRANSFER_COUNT.getMessage(), AreaStockControlReportEnum.FROM_30_DAY_AVG_TRANSFER_COUNT.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getInStockCountFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.IN_STOCK_COUNT.getMessage(), AreaStockControlReportEnum.IN_STOCK_COUNT.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getDiffLevelInStockCountFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.DIFF_LEVEL_IN_STOCK_COUNT.getMessage(), AreaStockControlReportEnum.DIFF_LEVEL_IN_STOCK_COUNT.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getOrderInStockOnWayCountFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.ORDER_IN_STOCK_ON_WAY_COUNT.getMessage(), AreaStockControlReportEnum.ORDER_IN_STOCK_ON_WAY_COUNT.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getOrderOnWayCountFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.ORDER_ON_WAY_COUNT.getMessage(), AreaStockControlReportEnum.ORDER_ON_WAY_COUNT.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getOrderOnWayControlFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.ORDER_ON_WAY_CONTROL.getMessage(), AreaStockControlReportEnum.ORDER_ON_WAY_CONTROL.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getInAreaOver30CountFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.IN_AREA_OVER_30_COUNT.getMessage(), AreaStockControlReportEnum.IN_AREA_OVER_30_COUNT.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getBuyOver90CountFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.BUY_OVER_90_COUNT.getMessage(), AreaStockControlReportEnum.BUY_OVER_90_COUNT.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getAvgInAreaStockAgeFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.AVG_IN_AREA_STOCK_AGE.getMessage(), AreaStockControlReportEnum.AVG_IN_AREA_STOCK_AGE.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getAvgBuyStockAgeFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.AVG_BUY_STOCK_AGE.getMessage(), AreaStockControlReportEnum.AVG_BUY_STOCK_AGE.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getFlawStockCountFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.FLAW_STOCK_COUNT.getMessage(), AreaStockControlReportEnum.FLAW_STOCK_COUNT.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getInStockOnWayMouldCountFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.IN_STOCK_ON_WAY_MOULD_COUNT.getMessage(), AreaStockControlReportEnum.IN_STOCK_ON_WAY_MOULD_COUNT.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getTodayDelOrderCountFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.TODAY_DEL_ORDER_COUNT.getMessage(), AreaStockControlReportEnum.TODAY_DEL_ORDER_COUNT.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getTodayOrderCountFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.TODAY_ORDER_COUNT.getMessage(), AreaStockControlReportEnum.TODAY_ORDER_COUNT.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getDay3SaleCountFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.DAY_3_SALE_COUNT.getMessage(), AreaStockControlReportEnum.DAY_3_SALE_COUNT.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getDiffLevel3DaySaleCountFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.DIFF_LEVEL_3_DAY_SALE_COUNT.getMessage(), AreaStockControlReportEnum.DIFF_LEVEL_3_DAY_SALE_COUNT.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getDay7SaleCountFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.DAY_7_SALE_COUNT.getMessage(), AreaStockControlReportEnum.DAY_7_SALE_COUNT.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getDiffLevel7DaySaleCountFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.DIFF_LEVEL_7_DAY_SALE_COUNT.getMessage(), AreaStockControlReportEnum.DIFF_LEVEL_7_DAY_SALE_COUNT.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getDay15SaleCountFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.DAY_15_SALE_COUNT.getMessage(), AreaStockControlReportEnum.DAY_15_SALE_COUNT.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getDiffLevel15DaySaleCountFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.DIFF_LEVEL_15_DAY_SALE_COUNT.getMessage(), AreaStockControlReportEnum.DIFF_LEVEL_15_DAY_SALE_COUNT.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getStockSaleRatioFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.STOCK_SALE_RATIO.getMessage(), AreaStockControlReportEnum.STOCK_SALE_RATIO.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getDay7InStockSaleRatioFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.DAY_7_IN_STOCK_SALE_RATIO.getMessage(), AreaStockControlReportEnum.DAY_7_IN_STOCK_SALE_RATIO.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getDiffLevel7DayInStockSaleRatioFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.DIFF_LEVEL_7_DAY_IN_STOCK_SALE_RATIO.getMessage(), AreaStockControlReportEnum.DIFF_LEVEL_7_DAY_IN_STOCK_SALE_RATIO.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getStockCostFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.STOCK_COST.getMessage(), AreaStockControlReportEnum.STOCK_COST.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getDiffLevelStockCostFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.DIFF_LEVEL_STOCK_COST.getMessage(), AreaStockControlReportEnum.DIFF_LEVEL_STOCK_COST.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getInStockCostFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.IN_STOCK_COST.getMessage(), AreaStockControlReportEnum.IN_STOCK_COST.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getDiffLevelInStockCostFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.DIFF_LEVEL_IN_STOCK_COST.getMessage(), AreaStockControlReportEnum.DIFF_LEVEL_IN_STOCK_COST.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getInStockOnWayCostFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.IN_STOCK_ON_WAY_COST.getMessage(), AreaStockControlReportEnum.IN_STOCK_ON_WAY_COST.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getDay30ActualAmtFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.DAY_30_ACTUAL_AMT.getMessage(), AreaStockControlReportEnum.DAY_30_ACTUAL_AMT.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getDiffLevel30DayActualAmtFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.DIFF_LEVEL_30_DAY_ACTUAL_AMT.getMessage(), AreaStockControlReportEnum.DIFF_LEVEL_30_DAY_ACTUAL_AMT.getMessageName());
        }


        if (Boolean.TRUE.equals(showFlag.getStockOnWayScoreFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.STOCK_ON_WAY_SCORE.getMessage(), AreaStockControlReportEnum.STOCK_ON_WAY_SCORE.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getTransferScoreFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.TRANSFER_SCORE.getMessage(), AreaStockControlReportEnum.TRANSFER_SCORE.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getProductIdFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.PRODUCT_ID.getMessage(), AreaStockControlReportEnum.PRODUCT_ID.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getDay7InStockSaleCountFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.DAY_7_IN_STOCK_SALE_COUNT.getMessage(), AreaStockControlReportEnum.DAY_7_IN_STOCK_SALE_COUNT.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getOrderOnWayScoreFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.ORDER_ON_WAY_SCORE.getMessage(), AreaStockControlReportEnum.ORDER_ON_WAY_SCORE.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getBuyStockCountFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.BUY_STOCK_COUNT.getMessage(), AreaStockControlReportEnum.BUY_STOCK_COUNT.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getOrderStockScoreFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.ORDER_STOCK_SCORE.getMessage(), AreaStockControlReportEnum.ORDER_STOCK_SCORE.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getDiffLevelInStockOnWayCostFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.DIFF_LEVEL_IN_STOCK_ON_WAY_COST.getMessage(), AreaStockControlReportEnum.DIFF_LEVEL_IN_STOCK_ON_WAY_COST.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getInAreaStockCountFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.IN_AREA_STOCK_COUNT.getMessage(), AreaStockControlReportEnum.IN_AREA_STOCK_COUNT.getMessageName());
        }
        if (Boolean.TRUE.equals(showFlag.getInAreaOver30ControlFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.IN_AREA_OVER_30_CONTROL.getMessage(), AreaStockControlReportEnum.IN_AREA_OVER_30_CONTROL.getMessageName());
        }

        if (Boolean.TRUE.equals(showFlag.getBuyStockAgeFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.BUY_STOCK_AGE.getMessage(), AreaStockControlReportEnum.BUY_STOCK_AGE.getMessageName());
        }

        if (Boolean.TRUE.equals(showFlag.getInAreaStockAgeFlag())) {
            excel.addHeaderAlias(AreaStockControlReportEnum.IN_AREA_STOCK_AGE.getMessage(), AreaStockControlReportEnum.IN_AREA_STOCK_AGE.getMessageName());
        }


        for (AreaStockControlReportResVO.AreaStockControlReportResData datum : data) {
            Map<String, String> temp = new HashMap<>();
            temp.put(AreaStockControlReportEnum.RISK_LEVEL.getMessage(), datum.getRiskLevel());
            temp.put(AreaStockControlReportEnum.AREA.getMessage(), datum.getArea());
            if (Boolean.TRUE.equals(showFlag.getIsStockShareFlag())) {
                temp.put(AreaStockControlReportEnum.IS_STOCK_SHARE.getMessage(), datum.getIsStockShare());
            }
            if (Boolean.TRUE.equals(showFlag.getLevel1NameFlag())) {
                temp.put(AreaStockControlReportEnum.LEVEL_1_NAME.getMessage(), datum.getLevel1Name());
            }
            if (Boolean.TRUE.equals(showFlag.getCityNameFlag())) {
                temp.put(AreaStockControlReportEnum.CITY_NAME.getMessage(), datum.getCityName());
            }
            if (Boolean.TRUE.equals(showFlag.getInStockCostFlag())) {
                temp.put(AreaStockControlReportEnum.IN_STOCK_COST.getMessage(), datum.getInStockCost());
            }
            if (Boolean.TRUE.equals(showFlag.getInStockCountFlag())) {
                temp.put(AreaStockControlReportEnum.IN_STOCK_COUNT.getMessage(), datum.getInStockCount());
            }

            if (Boolean.TRUE.equals(showFlag.getFlawStockCountFlag())) {
                temp.put(AreaStockControlReportEnum.FLAW_STOCK_COUNT.getMessage(), datum.getFlawStockCount());
            }

            if (Boolean.TRUE.equals(showFlag.getTodayOrderCountFlag())) {
                temp.put(AreaStockControlReportEnum.TODAY_ORDER_COUNT.getMessage(), datum.getTodayOrderCount());
            }

            if (Boolean.TRUE.equals(showFlag.getStockCostFlag())) {
                temp.put(AreaStockControlReportEnum.STOCK_COST.getMessage(), datum.getStockCost());
            }

            if (Boolean.TRUE.equals(showFlag.getOrderInStockOnWayCountFlag())) {
                temp.put(AreaStockControlReportEnum.ORDER_IN_STOCK_ON_WAY_COUNT.getMessage(), datum.getOrderInStockOnWayCount());
            }

            if (Boolean.TRUE.equals(showFlag.getDiffLevelStockCostFlag())) {
                temp.put(AreaStockControlReportEnum.DIFF_LEVEL_STOCK_COST.getMessage(), datum.getDiffLevelStockCost());
            }

            if (Boolean.TRUE.equals(showFlag.getStockControlFlag())) {
                temp.put(AreaStockControlReportEnum.STOCK_CONTROL.getMessage(), datum.getStockControl());
            }

            if (Boolean.TRUE.equals(showFlag.getFrom30DayAvgTransferCountFlag())) {
                temp.put(AreaStockControlReportEnum.FROM_30_DAY_AVG_TRANSFER_COUNT.getMessage(), datum.getFrom30DayAvgTransferCount());
            }

            if (Boolean.TRUE.equals(showFlag.getDiffLevel7DayInStockSaleRatioFlag())) {
                temp.put(AreaStockControlReportEnum.DIFF_LEVEL_7_DAY_IN_STOCK_SALE_RATIO.getMessage(), datum.getDiffLevel7DayInStockSaleRatio());
            }

            if (Boolean.TRUE.equals(showFlag.getDiffLevel3DaySaleCountFlag())) {
                temp.put(AreaStockControlReportEnum.DIFF_LEVEL_3_DAY_SALE_COUNT.getMessage(), datum.getDiffLevel3DaySaleCount());
            }

            if (Boolean.TRUE.equals(showFlag.getDay7SaleCountFlag())) {
                temp.put(AreaStockControlReportEnum.DAY_7_SALE_COUNT.getMessage(), datum.getDay7SaleCount());
            }

            if (Boolean.TRUE.equals(showFlag.getOrderOnWayControlFlag())) {
                temp.put(AreaStockControlReportEnum.ORDER_ON_WAY_CONTROL.getMessage(), datum.getOrderOnWayControl());
            }

            if (Boolean.TRUE.equals(showFlag.getDiffLevel7DaySaleCountFlag())) {
                temp.put(AreaStockControlReportEnum.DIFF_LEVEL_7_DAY_SALE_COUNT.getMessage(), datum.getDiffLevel7DaySaleCount());
            }

            if (Boolean.TRUE.equals(showFlag.getInAreaOver30CountFlag())) {
                temp.put(AreaStockControlReportEnum.IN_AREA_OVER_30_COUNT.getMessage(), datum.getInAreaOver30Count());
            }

            if (Boolean.TRUE.equals(showFlag.getStockOnWayScoreFlag())) {
                temp.put(AreaStockControlReportEnum.STOCK_ON_WAY_SCORE.getMessage(), datum.getStockOnWayScore());
            }

            if (Boolean.TRUE.equals(showFlag.getTransferScoreFlag())) {
                temp.put(AreaStockControlReportEnum.TRANSFER_SCORE.getMessage(), datum.getTransferScore());
            }

            if (Boolean.TRUE.equals(showFlag.getDiffLevel30DayActualAmtFlag())) {
                temp.put(AreaStockControlReportEnum.DIFF_LEVEL_30_DAY_ACTUAL_AMT.getMessage(), datum.getDiffLevel30DayActualAmt());
            }

            if (Boolean.TRUE.equals(showFlag.getProductIdFlag())) {
                temp.put(AreaStockControlReportEnum.PRODUCT_ID.getMessage(), datum.getProductId());
            }

            if (Boolean.TRUE.equals(showFlag.getDay30SaleControlFlag())) {
                temp.put(AreaStockControlReportEnum.DAY_30_SALE_CONTROL.getMessage(), datum.getDay30SaleControl());
            }
            if (Boolean.TRUE.equals(showFlag.getDay3SaleCountFlag())) {
                temp.put(AreaStockControlReportEnum.DAY_3_SALE_COUNT.getMessage(), datum.getDay3SaleCount());
            }
            if (Boolean.TRUE.equals(showFlag.getDay15SaleCountFlag())) {
                temp.put(AreaStockControlReportEnum.DAY_15_SALE_COUNT.getMessage(), datum.getDay15SaleCount());
            }
            if (Boolean.TRUE.equals(showFlag.getDiffLevelInStockCostFlag())) {
                temp.put(AreaStockControlReportEnum.DIFF_LEVEL_IN_STOCK_COST.getMessage(), datum.getDiffLevelInStockCost());
            }
            if (Boolean.TRUE.equals(showFlag.getDay7InStockSaleCountFlag())) {
                temp.put(AreaStockControlReportEnum.DAY_7_IN_STOCK_SALE_COUNT.getMessage(), datum.getDay7InStockSaleCount());
            }
            if (Boolean.TRUE.equals(showFlag.getDiffLevelStockCountFlag())) {
                temp.put(AreaStockControlReportEnum.DIFF_LEVEL_STOCK_COUNT.getMessage(), datum.getDiffLevelStockCount());
            }
            if (Boolean.TRUE.equals(showFlag.getStockCountFlag())) {
                temp.put(AreaStockControlReportEnum.STOCK_COUNT.getMessage(), datum.getStockCount());
            }
            if (Boolean.TRUE.equals(showFlag.getTo30DayAvgTransferCountFlag())) {
                temp.put(AreaStockControlReportEnum.TO_30_DAY_AVG_TRANSFER_COUNT.getMessage(), datum.getTo30DayAvgTransferCount());
            }
            if (Boolean.TRUE.equals(showFlag.getOrderOnWayScoreFlag())) {
                temp.put(AreaStockControlReportEnum.ORDER_ON_WAY_SCORE.getMessage(), datum.getOrderOnWayScore());
            }
            if (Boolean.TRUE.equals(showFlag.getAvgInAreaStockAgeFlag())) {
                temp.put(AreaStockControlReportEnum.AVG_IN_AREA_STOCK_AGE.getMessage(), datum.getAvgInAreaStockAge());
            }
            if (Boolean.TRUE.equals(showFlag.getToTodayTransferCountFlag())) {
                temp.put(AreaStockControlReportEnum.TO_TODAY_TRANSFER_COUNT.getMessage(), datum.getToTodayTransferCount());
            }
            if (Boolean.TRUE.equals(showFlag.getDiffLevelInStockCountFlag())) {
                temp.put(AreaStockControlReportEnum.DIFF_LEVEL_IN_STOCK_COUNT.getMessage(), datum.getDiffLevelInStockCount());
            }
            if (Boolean.TRUE.equals(showFlag.getBuyStockCountFlag())) {
                temp.put(AreaStockControlReportEnum.BUY_STOCK_COUNT.getMessage(), datum.getBuyStockCount());
            }
            if (Boolean.TRUE.equals(showFlag.getStockSaleRatioFlag())) {
                temp.put(AreaStockControlReportEnum.STOCK_SALE_RATIO.getMessage(), datum.getStockSaleRatio());
            }
            if (Boolean.TRUE.equals(showFlag.getAvgBuyStockAgeFlag())) {
                temp.put(AreaStockControlReportEnum.AVG_BUY_STOCK_AGE.getMessage(), datum.getAvgBuyStockAge());
            }
            if (Boolean.TRUE.equals(showFlag.getOrderStockScoreFlag())) {
                temp.put(AreaStockControlReportEnum.ORDER_STOCK_SCORE.getMessage(), datum.getOrderStockScore());
            }
            if (Boolean.TRUE.equals(showFlag.getTodayDelOrderCountFlag())) {
                temp.put(AreaStockControlReportEnum.TODAY_DEL_ORDER_COUNT.getMessage(), datum.getTodayDelOrderCount());
            }
            if (Boolean.TRUE.equals(showFlag.getDiffLevelInStockOnWayCostFlag())) {
                temp.put(AreaStockControlReportEnum.DIFF_LEVEL_IN_STOCK_ON_WAY_COST.getMessage(), datum.getDiffLevelInStockOnWayCost());
            }
            if (Boolean.TRUE.equals(showFlag.getDiffLevel30DaySaleCountFlag())) {
                temp.put(AreaStockControlReportEnum.DIFF_LEVEL_30_DAY_SALE_COUNT.getMessage(), datum.getDiffLevel30DaySaleCount());
            }
            if (Boolean.TRUE.equals(showFlag.getDay30ActualAmtFlag())) {
                temp.put(AreaStockControlReportEnum.DAY_30_ACTUAL_AMT.getMessage(), datum.getDay30ActualAmt());
            }
            if (Boolean.TRUE.equals(showFlag.getFromTodayTransferCountFlag())) {
                temp.put(AreaStockControlReportEnum.FROM_TODAY_TRANSFER_COUNT.getMessage(), datum.getFromTodayTransferCount());
            }
            if (Boolean.TRUE.equals(showFlag.getInAreaStockCountFlag())) {
                temp.put(AreaStockControlReportEnum.IN_AREA_STOCK_COUNT.getMessage(), datum.getInAreaStockCount());
            }
            if (Boolean.TRUE.equals(showFlag.getInStockOnWayMouldCountFlag())) {
                temp.put(AreaStockControlReportEnum.IN_STOCK_ON_WAY_MOULD_COUNT.getMessage(), datum.getInStockOnWayMouldCount());
            }
            if (Boolean.TRUE.equals(showFlag.getInAreaOver30ControlFlag())) {
                temp.put(AreaStockControlReportEnum.IN_AREA_OVER_30_CONTROL.getMessage(), datum.getInAreaOver30Control());
            }
            if (Boolean.TRUE.equals(showFlag.getDay7InStockSaleRatioFlag())) {
                temp.put(AreaStockControlReportEnum.DAY_7_IN_STOCK_SALE_RATIO.getMessage(), datum.getDay7InStockSaleRatio());
            }
            if (Boolean.TRUE.equals(showFlag.getInStockOnWayCostFlag())) {
                temp.put(AreaStockControlReportEnum.IN_STOCK_ON_WAY_COST.getMessage(), datum.getInStockOnWayCost());
            }
            if (Boolean.TRUE.equals(showFlag.getBuyStockAgeFlag())) {
                temp.put(AreaStockControlReportEnum.BUY_STOCK_AGE.getMessage(), datum.getBuyStockAge());
            }
            if (Boolean.TRUE.equals(showFlag.getInStockOnWayCountFlag())) {
                temp.put(AreaStockControlReportEnum.IN_STOCK_ON_WAY_COUNT.getMessage(), datum.getInStockOnWayCount());
            }
            if (Boolean.TRUE.equals(showFlag.getDiffLevel15DaySaleCountFlag())) {
                temp.put(AreaStockControlReportEnum.DIFF_LEVEL_15_DAY_SALE_COUNT.getMessage(), datum.getDiffLevel15DaySaleCount());
            }
            if (Boolean.TRUE.equals(showFlag.getDay30SaleCountFlag())) {
                temp.put(AreaStockControlReportEnum.DAY_30_SALE_COUNT.getMessage(), datum.getDay30SaleCount());
            }
            if (Boolean.TRUE.equals(showFlag.getInAreaStockAgeFlag())) {
                temp.put(AreaStockControlReportEnum.IN_AREA_STOCK_AGE.getMessage(), datum.getInAreaStockAge());
            }

            if (Boolean.TRUE.equals(showFlag.getOrderOnWayCountFlag())) {
                temp.put(AreaStockControlReportEnum.ORDER_ON_WAY_COUNT.getMessage(), datum.getOrderOnWayCount());
            }
            if (Boolean.TRUE.equals(showFlag.getDiffLevelInStockOnWayCountFlag())) {
                temp.put(AreaStockControlReportEnum.DIFF_LEVEL_IN_STOCK_ON_WAY_COUNT.getMessage(), datum.getDiffLevelInStockOnWayCount());
            }
            if (Boolean.TRUE.equals(showFlag.getBuyOver90CountFlag())) {
                temp.put(AreaStockControlReportEnum.BUY_OVER_90_COUNT.getMessage(), datum.getBuyOver90Count());
            }
            dataSource.add(temp);
        }
        excel.write(dataSource, true);
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        response.setCharacterEncoding("utf-8");
        ServletOutputStream out = null;
        try {
            String fileName = URLEncoder.encode("大区库存管控报表", "UTF-8") + StrPool.UNDERLINE + LocalDateTime.now().toLocalDate() + ".xlsx";
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            out = response.getOutputStream();
        } catch (Exception e) {
            throw new RRException("导出excel出错");
        } finally {
            excel.flush(out, true);
            excel.close();
            IoUtil.close(out);
        }
    }

    @Override
    public ScanPickStockDetailRes getScanPickStockDetail(OrderOutStockDetailReq req) {
        ScanPickStockDetailRes res = new ScanPickStockDetailRes();
        Integer subtypeId = req.getSubtypeId();
        Integer subId = req.getSubId();
        res.setSubId(subId);
        res.setSubtypeId(subtypeId);
        String subtype;
        OrderOutStockTypeEnum orderOutStockSearchEnum = OrderOutStockTypeEnum.getEnumByCode(subtypeId);
        if (Objects.isNull(orderOutStockSearchEnum)) {
            return res;
        }
        subtype = orderOutStockSearchEnum.getMessage();
        res.setSubtype(subtype);
        String comment;
        List<ScanPickStockDetailRes.Sku> alreadyList = new ArrayList<>();
        res.setAlreadyList(alreadyList);
        List<ScanPickStockDetailRes.Sku> pendingList = new ArrayList<>();
        res.setPendingList(pendingList);
        switch (orderOutStockSearchEnum) {
            case SUB:
                subtypeId = OrderOutStockTypeEnum.SUB.getCode();
                res.setSubtypeId(subtypeId);
                Sub sub = subService.getSub(subId);
                if (Objects.isNull(sub)) {
                    throw new CustomizeException("找不到该订单");
                }
                res.setDelivery(sub.getDelivery());
                //订单配送方式为九机快送、加急配送、第三方派送时，扫码取件提交界面隐藏
                res.setIsShowOutKc(!isPaiSong(sub.getDelivery()));
                res.setDeliveryName(DeliveryEnum.getDeliveryEnum(Convert.toStr(sub.getDelivery())));
                comment = sub.getComment();
                res.setComment(comment);
                Integer areaid = sub.getKcareaid();
                if (Objects.isNull(areaid)) {
                    areaid = sub.getAreaid();
                }
                List<Basket> basketList = basketService.getBasketlist(subId);
                if (CollectionUtils.isEmpty(basketList)) {
                    throw new CustomizeException("订单明细为空");
                }
                List<Long> ppidList = basketList.stream().map(Basket::getPpriceid).distinct().collect(Collectors.toList());
                List<ProductInfoEntity> ProductInfoEntitys = productInfoService.lambdaQuery().in(ProductInfoEntity::getPpid, ppidList).list();
                Map<Long, ProductInfoEntity> ppidMap = ProductInfoEntitys.stream()
                        .collect(Collectors.toMap(ProductInfoEntity::getPpid, Function.identity(), (v1, v2) -> v1));
                // 追加共享库存的信息
                List<String> ppid1List = ProductInfoEntitys.stream().filter(pie -> ObjectUtil.notEqual(pie.getPpid1(), Convert.toStr(pie.getPpid())))
                        .map(ProductInfoEntity::getPpid1).collect(Collectors.toList());

                Map<Long, List<Basket>> groupByPpidBasket = basketList.stream().collect(Collectors.groupingBy(Basket::getPpriceid));

                Map<Integer, List<ProductBarcodeEntity>> barcodeListMap = productBarcodeService.lambdaQuery()
                        .in(ProductBarcodeEntity::getPpid, Stream.concat(ppidList.stream(), ppid1List.stream()).collect(Collectors.toList()))
                        .list().stream().collect(Collectors.groupingBy(ProductBarcodeEntity::getPpid));
                List<Integer> basketIdList = basketList.stream().map(Basket::getBasketId).collect(Collectors.toList());
                Map<Long, Nahuoduilie> basketIdToNahuoduilieMap = nahuoduilieService.lambdaQuery().in(Nahuoduilie::getBasketId, basketIdList).list()
                        .stream().collect(Collectors.toMap(Nahuoduilie::getBasketId, Function.identity(), (v1, v2) -> v1));
                Set<Integer> childCategoryIds = new HashSet<>();
                List<Long> autoPickupList = new ArrayList<>();
                String value = sysConfigService.setSysconfigValueBycode(SysConfigConstant.SCANNED_PRODUCT_CONFIGURATION);
                if (StrUtil.isNotBlank(value)) {
                    ScannedProductConfiguration configuration = JSONUtil.toBean(value, ScannedProductConfiguration.class);
                    childCategoryIds.addAll(configuration.getCidList());
                    autoPickupList.addAll(configuration.getPpidList().stream().map(Convert::toLong).collect(Collectors.toList()));
                } else {
                    //商品分类为购物袋、运营商、虚拟商品、上门服务、电话卡、官方服务，默认为已扫
                    childCategoryIds.addAll(categoryService.getChildCategoryIds(485));
                    childCategoryIds.addAll(categoryService.getChildCategoryIds(3));
                    childCategoryIds.addAll(categoryService.getChildCategoryIds(164));
                    childCategoryIds.addAll(categoryService.getChildCategoryIds(407));
                    childCategoryIds.addAll(categoryService.getChildCategoryIds(731));
                    childCategoryIds.addAll(categoryService.getChildCategoryIds(715));
                    childCategoryIds.addAll(categoryService.getChildCategoryIds(508));
                    childCategoryIds.addAll(categoryService.getChildCategoryIds(50));
                    List<Integer> virtualCidList = productInfoService.getVirtualCidList();
                    childCategoryIds.addAll(virtualCidList);
                    //配置的ppid也是进行自动拿货
                    autoPickupList.add(187283L);
                }
                res.setAreaId(areaid);
                List<Integer> mobileBasketIdList = ppidMap.entrySet().stream()
                        .filter(entry -> Boolean.TRUE.equals(entry.getValue().getMobile()))
                        //转为ppid
                        .map(entry -> entry.getKey())
                        //获取对应的basketId
                        .map(ppid -> groupByPpidBasket.get(ppid))
                        .filter(Objects::nonNull).flatMap(List::stream)
                        .map(Basket::getBasketId)
                        .collect(Collectors.toList());
                List<Integer> smallBasketIdList = ppidMap.entrySet().stream()
                        .filter(entry -> !Boolean.TRUE.equals(entry.getValue().getMobile()))
                        //转为ppid
                        .map(entry -> entry.getKey())
                        //获取对应的basketId
                        .map(ppid -> groupByPpidBasket.get(ppid))
                        .filter(Objects::nonNull).flatMap(List::stream)
                        .map(Basket::getBasketId)
                        .collect(Collectors.toList());
                //批量获取所有大件库存
                Map<Integer, List<ProductMkc>> groupByBasketIdMkcMap = CommonUtils.bigDataInQuery(mobileBasketIdList,
                        ids -> productMkcService.lambdaQuery().in(ProductMkc::getBasketId, ids)
                        .list()).stream().collect(Collectors.groupingBy(ProductMkc::getBasketId));
                //批量获取小件陈列
                Map<Integer, List<DisplayProductInfo>> groupByBasketIdDisplayMap = CommonUtils.bigDataInQuery(smallBasketIdList,
                        ids -> displayProductInfoService.lambdaQuery().in(DisplayProductInfo::getBasketId, ids)
                                .list()).stream().collect(Collectors.groupingBy(dpi -> Convert.toInt(dpi.getBasketId())));
                for (Basket basket : basketList) {
                    Long ppriceid = basket.getPpriceid();
                    Integer basketCount = basket.getBasketCount();
                    ProductInfoEntity productInfoEntity = ppidMap.get(ppriceid);
                    if (Objects.isNull(productInfoEntity)) {
                        continue;
                    }
                    Boolean mobile = productInfoEntity.getMobile();
                    if (mobile) {
                        List<ProductMkc> mkc = ObjectUtil.defaultIfNull(groupByBasketIdMkcMap.get(basket.getBasketId()), Collections.emptyList());
                        for (int i = 0; i < basketCount; i++) {
                            boolean existFlag = false;
                            ScanPickStockDetailRes.Sku sku = new ScanPickStockDetailRes.Sku();
                            sku.setCount(1);
                            sku.setIsYoupin("22".equals(Convert.toStr(basket.getType())));
                            sku.setIsMobile(mobile);
                            Integer basketId = basket.getBasketId();
                            sku.setBasketId(basketId);
                            ProductMkc temp = null;
                            if (!CollectionUtils.isEmpty(mkc) && i < mkc.size()) {
                                temp = mkc.get(i);
                            }
                            if (Objects.nonNull(temp)) {
                                sku.setMkcid(temp.getId());
                                sku.setMkcidSecret(Convert.toStr(temp.getId()));
                                sku.setOrderid(temp.getOrderid());
                                KcCheckEnum kcCheckEnum = EnumUtil.getEnumByCode(KcCheckEnum.class, temp.getKcCheck());
                                sku.setKcCheck(kcCheckEnum.getMessage());
                                Nahuoduilie nahuoduilie = basketIdToNahuoduilieMap.get(Convert.toLong(basketId));
                                if (Objects.nonNull(nahuoduilie) && ("1".equals(String.valueOf(nahuoduilie.getIsna()))
                                        || "16".equals(String.valueOf(nahuoduilie.getIsna())))) {
                                    NahuoBasketRecord nahuoBasketRecord = nahuoBasketRecordService.lambdaQuery()
                                            .eq(NahuoBasketRecord::getMkcId, temp.getId())
                                            .eq(NahuoBasketRecord::getNahuoId, nahuoduilie.getId())
                                            .list().stream().findFirst().orElse(null);
                                    if (Objects.nonNull(nahuoBasketRecord)) {
                                        existFlag = true;
                                    }
                                }
                            }
                            sku.setPpriceid(productInfoEntity.getPpid());
                            sku.setPpriceid1(productInfoEntity.getPpid1());
                            sku.setCid(productInfoEntity.getCid());
                            sku.setProductName(productInfoEntity.getProductName());
                            sku.setProductColor(productInfoEntity.getProductColor());
                            List<ProductBarcodeEntity> productBarcodeEntityList = barcodeListMap.get(Convert.toInt(ppriceid));
                            if (!CollectionUtils.isEmpty(productBarcodeEntityList)) {
                                List<String> productBarcodeList = productBarcodeEntityList.stream().map(ProductBarcodeEntity::getBarCode).collect(Collectors.toList());
                                sku.setBarcodeList(productBarcodeList);
                                ProductBarcodeEntity productBarcodeEntity
                                        = productBarcodeEntityList.stream().filter(ProductBarcodeEntity::getDefaultFlag).findFirst().orElse(null);
                                if (Objects.nonNull(productBarcodeEntity)) {
                                    sku.setBarcode(productBarcodeEntity.getBarCode());
                                } else {
                                    sku.setBarcode(productBarcodeEntityList.get(0).getBarCode());
                                }
                            }

                            if (existFlag) {
                                alreadyList.add(sku);
                            } else {
                                pendingList.add(sku);
                            }
                        }
                    } else {
                        boolean existFlag = false;
                        ScanPickStockDetailRes.Sku sku = new ScanPickStockDetailRes.Sku();
                        sku.setCount(basketCount);
                        sku.setIsYoupin("22".equals(Convert.toStr(basket.getType())));
                        sku.setIsMobile(mobile);
                        Integer basketId = basket.getBasketId();
                        sku.setBasketId(basketId);
                        List<DisplayProductInfo> displays = ObjectUtil.defaultIfNull(groupByBasketIdDisplayMap.get(basketId), Collections.emptyList());
                        DisplayProductInfo displayProductInfo = displays.stream().findFirst().orElse(null);
                        if (Objects.nonNull(displayProductInfo)) {
                            sku.setDisplayId(Convert.toStr(displayProductInfo.getId()));
                        }
                        CheckSubPeijianBeiResVO checkSubPeijianBeiResVO = this.checkSubPeijianBei(areaid, basketId, ppriceid);
                        if (Objects.nonNull(checkSubPeijianBeiResVO)) {
                            sku.setKcCheck(checkSubPeijianBeiResVO.getKcCheckName());
                        }
                        Nahuoduilie nahuoduilie = basketIdToNahuoduilieMap.get(Convert.toLong(basketId));
                        if ((Objects.nonNull(nahuoduilie) && ("6".equals(String.valueOf(nahuoduilie.getIsna()))
                                || "10".equals(String.valueOf(nahuoduilie.getIsna()))))) {
                            existFlag = true;
                        }
                        //自动拿货
                        if (childCategoryIds.contains(Convert.toInt(productInfoEntity.getCid())) || autoPickupList.contains(productInfoEntity.getPpid())) {
                            existFlag = true;
                            Integer finalSubtypeId = subtypeId;
                            Integer finalAreaid = areaid;
                            RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
                            String traceId = MDC.get(TraceIdUtil.TRACE_ID_KEY);
                            CompletableFuture
                                    .runAsync(() -> {
                                        try {
                                            RequestContextHolder.setRequestAttributes(requestAttributes);
                                            MDC.put(TraceIdUtil.TRACE_ID_KEY, traceId);
                                            OrderOutStockDetailReq vo = new OrderOutStockDetailReq();
                                            vo.setSubtypeId(finalSubtypeId);
                                            vo.setSubId(subId);
                                            vo.setAreaId(finalAreaid);
                                            vo.setCount(basketCount);
                                            vo.setBasketId(basketId);
                                            this.scanPickNahuo(vo);
                                        } finally {
                                            MDC.remove(TraceIdUtil.TRACE_ID_KEY);
                                            RequestContextHolder.resetRequestAttributes();
                                        }
                                    })
                                    .exceptionally(e -> {
                                        log.warn("拿货取件异常", e);
                                        return null;
                                    });
                        }
                        sku.setPpriceid(productInfoEntity.getPpid());
                        sku.setPpriceid1(productInfoEntity.getPpid1());
                        sku.setCid(productInfoEntity.getCid());
                        sku.setProductName(productInfoEntity.getProductName());
                        sku.setProductColor(productInfoEntity.getProductColor());
                        List<ProductBarcodeEntity> productBarcodeEntityList = barcodeListMap.get(Convert.toInt(ppriceid));
                        if (!CollectionUtils.isEmpty(productBarcodeEntityList)) {
                            List<String> productBarcodeList = productBarcodeEntityList.stream().map(ProductBarcodeEntity::getBarCode).collect(Collectors.toList());
                            sku.setBarcodeList(productBarcodeList);
                            ProductBarcodeEntity productBarcodeEntity
                                    = productBarcodeEntityList.stream().filter(ProductBarcodeEntity::getDefaultFlag).findFirst().orElse(null);
                            if (Objects.nonNull(productBarcodeEntity)) {
                                sku.setBarcode(productBarcodeEntity.getBarCode());
                            } else {
                                sku.setBarcode(productBarcodeEntityList.get(0).getBarCode());
                            }
                        }
                        //共享库存信息
                        List<ProductBarcodeEntity> productBarcodeEntity1List = barcodeListMap
                                .getOrDefault(Convert.toInt(productInfoEntity.getPpid1()), Collections.emptyList());
                        List<String> productBarcode1List = productBarcodeEntity1List.stream()
                                .map(ProductBarcodeEntity::getBarCode).collect(Collectors.toList());
                        sku.setBarcode1List(productBarcode1List);
                        sku.setBarcode1(productBarcodeEntity1List.stream()
                                .sorted(Comparator.comparing(ProductBarcodeEntity::getDefaultFlag, Comparator.nullsLast(Comparator.reverseOrder())))
                                .findFirst().map(ProductBarcodeEntity::getBarCode).orElse(null));
                        if (existFlag) {
                            alreadyList.add(sku);
                        } else {
                            pendingList.add(sku);
                        }
                    }
                }
                break;
            case RECOVER_SUB:
                break;
            case SMALLPRO:
                //获取小件单信息
                List<SmallProductInfo> smallProductInfos = stockMapper.selectSmallProductList(subId);
                if(CollectionUtils.isEmpty(smallProductInfos)){
                    break;
                }
                R<List<ChangeInfoRes>> listR = apiAfterClient.selectChangeInfoRes(subId, currentRequestComponent.getCurrentToken());
                if(!listR.isSuccess()){
                    throw new CustomizeException(String.format("调用小件单换货商品查询：%s",Optional.ofNullable(listR.getMsg()).orElse(listR.getUserMsg())));
                }
                res.setIsShowOutKc(Boolean.FALSE);
                res.setIsShowComplete(Boolean.TRUE);
                List<ChangeInfoRes> data = listR.getData();
                Map<Integer, ChangeInfoRes> changeInfoResMap = data.stream().collect(Collectors.toMap(ChangeInfoRes::getSmallproBillId, Function.identity(), (n1, n2) -> n2));
                smallProductInfos.forEach(item->{
                    Boolean scanFlag = Optional.ofNullable(item.getScanFlag()).orElse(Boolean.FALSE);
                    ScanPickStockDetailRes.Sku sku = new ScanPickStockDetailRes.Sku();
                    sku.setPpriceid(Convert.toLong(item.getPpriceid()));
                    sku.setBasketId(item.getBasketId());
                    sku.setSmallproBillId(item.getSmallproBillId());
                    sku.setProductName(item.getProductName());
                    sku.setProductColor(item.getProductColor());
                    sku.setCount(item.getCount());
                    sku.setIsMobile(Boolean.FALSE);
                    ChangeInfoRes changeInfoRes = changeInfoResMap.getOrDefault(item.getSmallproBillId(), new ChangeInfoRes());
                    sku.setKcCheck(changeInfoRes.getStockStatusName());
                    List<String> barCodeList = changeInfoRes.getBarCodeList();
                    if(CollUtil.isNotEmpty(barCodeList)){
                        sku.setBarcode(barCodeList.get(NumberConstant.ZERO));
                        sku.setBarcodeList(barCodeList);
                    }
                    if(scanFlag){
                        sku.setIsComplete(Boolean.TRUE);
                        alreadyList.add(sku);
                    } else {
                        sku.setIsComplete(Boolean.FALSE);
                        pendingList.add(sku);
                    }
                });
                break;
            default:
                break;
        }

        return res;
    }

    private static boolean isPaiSong(Integer delivery) {
        return Stream.of(DeliveryEnum.JIUJI_FAST, DeliveryEnum.HURRY_DELIVERY, DeliveryEnum.THIRD_TRANSFER, DeliveryEnum.EXPRESS_TRANSFER)
                .anyMatch(item -> Objects.equals(item.getCode(), delivery));
    }

    @Override
    public Page<OrderOutStockPageRes> getOrderOutStockPage(OrderOutStockPageReqVO req) {
        int current = req.getCurrent() <= 0 ? 1 : req.getCurrent();
        int size = req.getSize();
        String searchValue = req.getSearchValue();
        if (StringUtils.isEmpty(searchValue)) {
            throw new CustomizeException("请输入需要查询的商品");
        }

        Page<OrderOutStockPageRes> pageResult = new Page<>(current, size);
        // 查询数据
        List<OrderOutStockPageRes> allRecords = new ArrayList<>();

        List<LPBidResultEnumRes> list = OrderOutStockSearchEnum.getList();
        list.forEach(x -> {
            if (NumberUtil.isNumber(req.getSearchValue()) && OrderOutStockSearchEnum.PPID.getCode().equals(x.getCode())) {

                try {
                    Integer.parseInt(req.getSearchValue());
                    List<OrderOutStockPageRes> tempSub = subService.getSubIdListByPpid(req);
                    List<OrderOutStockPageRes> tempRecoverSub = recoverMarketinfoService.getSubIdListByPpid(req);
                    allRecords.addAll(tempSub);
                    allRecords.addAll(tempRecoverSub);
                } catch (Exception e) {

                }
            }
            if (OrderOutStockSearchEnum.MKC_ID.getCode().equals(x.getCode())) {
                try {
                    if (NumberUtil.isNumber(req.getSearchValue())) {
                        Integer.parseInt(req.getSearchValue());
                        List<OrderOutStockPageRes> tempSub = subService.getSubIdListByMkcId(req);
                        allRecords.addAll(tempSub);
                    }

                    if (!NumberUtil.isNumber(req.getSearchValue())) {
                        ScanVO scanVO = Encode64Util.decodeV2(req.getSearchValue());
                        if (Objects.nonNull(scanVO)) {
                            OrderOutStockPageReqVO temp = new OrderOutStockPageReqVO();
                            BeanUtils.copyProperties(req, temp);
                            temp.setSearchValue(scanVO.getMkcId());
                            Integer.parseInt(temp.getSearchValue());
                            List<OrderOutStockPageRes> tempRecoverSub = recoverMarketinfoService.getSubIdListByMkcId(temp);
                            allRecords.addAll(tempRecoverSub);
                        }
                    }

                } catch (Exception e) {

                }
            }
            if (OrderOutStockSearchEnum.IMEI.getCode().equals(x.getCode())) {
                try {
                    List<OrderOutStockPageRes> tempSub = subService.getSubIdListByImei(req);
                    allRecords.addAll(tempSub);
                    List<OrderOutStockPageRes> tempRecoverSub = recoverMarketinfoService.getSubIdListByImei(req);
                    allRecords.addAll(tempRecoverSub);
                } catch (Exception e) {

                }
            }
            if (OrderOutStockSearchEnum.DISPLAY.getCode().equals(x.getCode())) {
                try {
                    if (req.getSearchValue().startsWith("u")
                            || req.getSearchValue().startsWith("U")) {
                        req.setSearchValue(req.getSearchValue().substring(1));
                        Integer.parseInt(req.getSearchValue());
                        List<OrderOutStockPageRes> tempSub = subService.getSubIdListByDisplayCode(req);
                        allRecords.addAll(tempSub);
                    } else if (NumberUtil.isNumber(req.getSearchValue())) {
                        Integer.parseInt(req.getSearchValue());
                        List<OrderOutStockPageRes> tempSub = subService.getSubIdListByDisplayCode(req);
                        allRecords.addAll(tempSub);
                    }
                } catch (Exception e) {

                }
            }
            if (OrderOutStockSearchEnum.BARCODE.getCode().equals(x.getCode())) {
                List<OrderOutStockPageRes> tempSub = subService.getSubIdListByBarcode(req);
                List<OrderOutStockPageRes> tempRecoverSub = recoverMarketinfoService.getSubIdListByBarcode(req);
                allRecords.addAll(tempSub);
                allRecords.addAll(tempRecoverSub);
            }
        });
        if (CollectionUtils.isEmpty(allRecords)) {
            throw new CustomizeException("该商品不存在符合的订单，请核对");
        }
        List<OrderOutStockPageRes> sourceRecordList = allRecords
                .stream().sorted(Comparator.comparing(OrderOutStockPageRes::getSubId).reversed())
                .skip((long) size * (current - 1)).limit(size).collect(Collectors.toList());
        sourceRecordList = sourceRecordList.stream().peek(v -> {
            if (Objects.equals(1,v.subtypeId)) {
                v.setExceptionFlag(RedisUtils.hashHasKey(RedisKeys.EXCEPTION_SUB_REDISKEY, Convert.toStr(v.getSubId())));
            } else {
                v.setExceptionFlag(RedisUtils.hashHasKey(RedisKeys.EXCEPTION_LPRECOVER_SUB_REDISKEY, Convert.toStr(v.getSubId())));
            }
        }).collect(Collectors.toList());
        pageResult.setRecords(sourceRecordList);
        pageResult.setTotal(allRecords.size());
        pageResult.setSize(size);
        pageResult.setCurrent(current);
        return pageResult;
    }

    @Override
    public OrderOutStockDetailRes getOrderOutStockDetail(OrderOutStockDetailReq req) {
        OrderOutStockDetailRes res = new OrderOutStockDetailRes();
        String subtype = req.getSubtype();
        Integer subId = req.getSubId();
        res.setSubId(subId);
        res.setSubtype(subtype);
        Integer subtypeId;
        OrderOutStockTypeEnum orderOutStockSearchEnum = OrderOutStockTypeEnum.getEnumByMessage(subtype);
        if (Objects.isNull(orderOutStockSearchEnum)) {
            return res;
        }
        String comment;
        List<OrderOutStockDetailRes.Sku> skuList = new ArrayList<>();
        res.setSkuList(skuList);
        switch (orderOutStockSearchEnum) {
            case SUB:
                subtypeId = OrderOutStockTypeEnum.SUB.getCode();
                res.setSubtypeId(subtypeId);
                Sub sub = subService.getSub(subId);
                if (Objects.isNull(sub)) {
                    throw new CustomizeException("找不到该订单");
                }
                comment = sub.getComment();
                res.setComment(comment);
                List<Basket> basketList = basketService.lambdaQuery()
                        .and(item -> item.eq(Basket::getIsdel, 0).or().isNull(Basket::getIsdel))
                        .eq(Basket::getSubId, subId).list();
                if (CollectionUtils.isEmpty(basketList)) {
                    throw new CustomizeException("订单明细为空");
                }

                List<Long> ppidList = basketList.stream().map(Basket::getPpriceid).collect(Collectors.toList());
                Map<Long, ProductInfoEntity> ppidMap = productInfoService.lambdaQuery().in(ProductInfoEntity::getPpid, ppidList).list()
                        .stream().collect(Collectors.toMap(ProductInfoEntity::getPpid, Function.identity(), (v1, v2) -> v1));


                Map<Integer, List<ProductBarcodeEntity>> barcodeListMap = productBarcodeService.lambdaQuery()
                        .in(ProductBarcodeEntity::getPpid, ppidList)
                        .list().stream().collect(Collectors.groupingBy(ProductBarcodeEntity::getPpid));

                for (Basket basket : basketList) {
                    Long ppriceid = basket.getPpriceid();
                    Integer basketCount = basket.getBasketCount();
                    ProductInfoEntity productInfoEntity = ppidMap.get(ppriceid);
                    if (Objects.isNull(productInfoEntity)) {
                        continue;
                    }
                    Boolean mobile = productInfoEntity.getMobile();
                    if (mobile) {
                        List<ProductMkc> mkc = productMkcService.lambdaQuery()
                                .eq(ProductMkc::getBasketId, basket.getBasketId())
                                .list();
                        for (int i = 0; i < basketCount; i++) {
                            OrderOutStockDetailRes.Sku sku = new OrderOutStockDetailRes.Sku();
                            sku.setCount(1);
                            sku.setIsYoupin("22".equals(Convert.toStr(basket.getType())));
                            sku.setIsMobile(mobile);
                            Integer basketId = basket.getBasketId();
                            sku.setBasketId(basketId);
                            ProductMkc temp = null;
                            if (!CollectionUtils.isEmpty(mkc) && i < mkc.size()) {
                                temp = mkc.get(i);
                            }
                            if (Objects.nonNull(temp)) {
                                sku.setMkcid(temp.getId());
                                sku.setImei(temp.getImei());
                                sku.setMkcidSecret(Convert.toStr(temp.getId()));
                                sku.setOrderid(temp.getOrderid());
                            }
                            sku.setPpriceid(productInfoEntity.getPpid());
                            sku.setProductName(productInfoEntity.getProductName());
                            sku.setProductColor(productInfoEntity.getProductColor());
                            List<ProductBarcodeEntity> productBarcodeEntityList = barcodeListMap.get(Convert.toInt(ppriceid));
                            if (!CollectionUtils.isEmpty(productBarcodeEntityList)) {
                                List<String> productBarcodeList = productBarcodeEntityList.stream().map(ProductBarcodeEntity::getBarCode).collect(Collectors.toList());
                                sku.setBarcodeList(productBarcodeList);
                                ProductBarcodeEntity productBarcodeEntity
                                        = productBarcodeEntityList.stream().filter(ProductBarcodeEntity::getDefaultFlag).findFirst().orElse(null);
                                if (Objects.nonNull(productBarcodeEntity)) {
                                    sku.setBarcode(productBarcodeEntity.getBarCode());
                                } else {
                                    sku.setBarcode(productBarcodeEntityList.get(0).getBarCode());
                                }
                            }
                            skuList.add(sku);
                        }
                    } else {
                        OrderOutStockDetailRes.Sku sku = new OrderOutStockDetailRes.Sku();
                        sku.setCount(basketCount);
                        sku.setIsYoupin("22".equals(Convert.toStr(basket.getType())));
                        sku.setIsMobile(mobile);
                        Integer basketId = basket.getBasketId();
                        sku.setBasketId(basketId);
                        DisplayProductInfo displayProductInfo = displayProductInfoService.lambdaQuery().eq(DisplayProductInfo::getBasketId, basketId)
                                .list().stream().findFirst().orElse(null);
                        if (Objects.nonNull(displayProductInfo)) {
                            sku.setDisplayId(Convert.toStr(displayProductInfo.getId()));
                        }
                        sku.setPpriceid(productInfoEntity.getPpid());
                        sku.setProductName(productInfoEntity.getProductName());
                        sku.setProductColor(productInfoEntity.getProductColor());
                        List<ProductBarcodeEntity> productBarcodeEntityList = barcodeListMap.get(Convert.toInt(ppriceid));
                        if (!CollectionUtils.isEmpty(productBarcodeEntityList)) {
                            List<String> productBarcodeList = productBarcodeEntityList.stream().map(ProductBarcodeEntity::getBarCode).collect(Collectors.toList());
                            sku.setBarcodeList(productBarcodeList);
                            ProductBarcodeEntity productBarcodeEntity
                                    = productBarcodeEntityList.stream().filter(ProductBarcodeEntity::getDefaultFlag).findFirst().orElse(null);
                            if (Objects.nonNull(productBarcodeEntity)) {
                                sku.setBarcode(productBarcodeEntity.getBarCode());
                            } else {
                                sku.setBarcode(productBarcodeEntityList.get(0).getBarCode());
                            }
                        }
                        skuList.add(sku);
                    }

                }
                break;
            case RECOVER_SUB:
                subtypeId = OrderOutStockTypeEnum.RECOVER_SUB.getCode();
                res.setSubtypeId(subtypeId);
                RecoverMarketinfo recoverSub = recoverMarketinfoService.getRecoverSub(subId);
                if (Objects.isNull(recoverSub)) {
                    throw new CustomizeException("找不到该良品单");
                }

                comment = recoverSub.getComment();
                res.setComment(comment);
                List<RecoverMarketsubinfo> recoverMarketsubinfotList = recoverMarketsubinfoService.lambdaQuery()
                        .and(item -> item.eq(RecoverMarketsubinfo::getIsdel, 0).or().isNull(RecoverMarketsubinfo::getIsdel))
                        .eq(RecoverMarketsubinfo::getSubId, subId).list();
                if (CollectionUtils.isEmpty(recoverMarketsubinfotList)) {
                    throw new CustomizeException("良品单明细为空");
                }
                List<Integer> recoverPpidList = recoverMarketsubinfotList.stream().map(RecoverMarketsubinfo::getPpriceid).collect(Collectors.toList());
                Map<Long, ProductInfoEntity> recoverPpidMap = productInfoService.lambdaQuery().in(ProductInfoEntity::getPpid, recoverPpidList).list()
                        .stream().collect(Collectors.toMap(ProductInfoEntity::getPpid, Function.identity(), (v1, v2) -> v1));
                Map<Integer, List<ProductBarcodeEntity>> recoverBarcodeListMap = productBarcodeService.lambdaQuery()
                        .in(ProductBarcodeEntity::getPpid, recoverPpidList)
                        .list().stream().collect(Collectors.groupingBy(ProductBarcodeEntity::getPpid));

                for (RecoverMarketsubinfo basket : recoverMarketsubinfotList) {
                    OrderOutStockDetailRes.Sku sku = new OrderOutStockDetailRes.Sku();
                    Integer ppriceid = basket.getPpriceid();
                    sku.setCount(basket.getBasketCount());
                    sku.setIsYoupin(false);
                    ProductInfoEntity productInfoEntity = recoverPpidMap.get(Convert.toLong(ppriceid));
                    if (Objects.isNull(productInfoEntity)) {
                        continue;
                    }
                    Boolean mobile = productInfoEntity.getMobile();
                    sku.setIsMobile(mobile);
                    Integer basketId = basket.getBasketId();
                    sku.setBasketId(basketId);
                    if (mobile) {
                        RecoverMkc mkc = recoverMkcService.lambdaQuery()
                                .eq(RecoverMkc::getToBasketId, basketId)
                                .list().stream().findFirst().orElse(null);
                        if (Objects.nonNull(mkc)) {
                            sku.setMkcid(mkc.getId());
                            sku.setImei(mkc.getImei());
                            RecoverBasket recoverBasket = recoverBasketService.lambdaQuery()
                                    .and(item -> item.eq(RecoverBasket::getIsdel, 0).or().isNull(RecoverBasket::getIsdel))
                                    .eq(RecoverBasket::getId, mkc.getFromBasketId())
                                    .list().stream().findFirst().orElse(null);
                            if (Objects.nonNull(recoverBasket)) {
                                WuLiuRecoverSubEntity wuLiuRecoverSubEntity = recoverSubService.lambdaQuery()
                                        .eq(WuLiuRecoverSubEntity::getSubId, recoverBasket.getSubId()).list().stream().findFirst().orElse(null);
                                if (Objects.nonNull(wuLiuRecoverSubEntity)) {
                                    Integer areaid = wuLiuRecoverSubEntity.getAreaid();
                                    Areainfo areainfo = areaInfoService.getById(areaid);
                                    Integer xtenant = areainfo.getXtenant();
                                    String mkcidSecret = Encode64Util.encodeBarCode(Convert.toLong(xtenant), Convert.toStr(mkc.getId()));
                                    sku.setMkcidSecret(mkcidSecret);
                                }
                            }else if(XtenantJudgeUtil.isJiujiMore()){
                                String mkcidSecret = Encode64Util.encodeBarCode(0L, Convert.toStr(mkc.getId()));
                                sku.setMkcidSecret(mkcidSecret);
                            }
                            sku.setOrderid(mkc.getOrderid());
                        }
                    }
                    sku.setPpriceid(productInfoEntity.getPpid());
                    sku.setProductName(productInfoEntity.getProductName());
                    sku.setProductColor(productInfoEntity.getProductColor());
                    List<ProductBarcodeEntity> productBarcodeEntityList = recoverBarcodeListMap.get(Convert.toInt(ppriceid));
                    if (!CollectionUtils.isEmpty(productBarcodeEntityList)) {
                        List<String> productBarcodeList = productBarcodeEntityList.stream().map(ProductBarcodeEntity::getBarCode).collect(Collectors.toList());
                        sku.setBarcodeList(productBarcodeList);
                        ProductBarcodeEntity productBarcodeEntity
                                = productBarcodeEntityList.stream().filter(ProductBarcodeEntity::getDefaultFlag).findFirst().orElse(null);
                        if (Objects.nonNull(productBarcodeEntity)) {
                            sku.setBarcode(productBarcodeEntity.getBarCode());
                        } else {
                            sku.setBarcode(productBarcodeEntityList.get(0).getBarCode());
                        }
                    }
                    skuList.add(sku);
                }
                break;

            default:
                break;
        }

        return res;
    }

    @Override
    public OrderOutStockExpressDTO getOrderOutStockExpress(Integer subId, String subType) {
        //线上国补订单
        if (CommonUtil.isNotNullZero(subId) && Objects.equals(OrderOutStockTypeEnum.SUB.getCode().toString(), subType)) {
            //线上国补订单校验
            List<NationalSupplementKindRes> nationalSupplementKindList = SpringUtil.getBean(NationalSupplementService.class).getNationalSupplementKindList(Collections.singletonList(Convert.toStr(subId)));
            if (CollUtil.isNotEmpty(nationalSupplementKindList)) {
                OrderOutStockExpressDTO result = new OrderOutStockExpressDTO();
                List<OrderOutStockExpressDTO.OrderOutStockExpress> expressList = new ArrayList<>();
                List<OrderOutStockExpressDTO.OrderOutStockExpress.Category> categoriesTemp1 = new ArrayList<>();
                OrderOutStockExpressDTO.OrderOutStockExpress.Category category13 = new OrderOutStockExpressDTO.OrderOutStockExpress.Category();
                OrderOutStockExpressDTO.OrderOutStockExpress temp1 = new OrderOutStockExpressDTO.OrderOutStockExpress();
                temp1.setExpressName("顺丰快递");
                temp1.setExpressCode("shunfeng");
                category13.setRank(2);
                category13.setCode("2_9386_3");
                category13.setName("顺丰标快（全国套餐）");
                categoriesTemp1.add(category13);
                temp1.setCategories(categoriesTemp1);
                expressList.add(temp1);
                result.setExpressList(expressList);
                return result;
            }
        }
        OrderOutStockExpressDTO result = new OrderOutStockExpressDTO();
        List<OrderOutStockExpressDTO.OrderOutStockExpress> expressList = new ArrayList<>();
        OrderOutStockExpressDTO.OrderOutStockExpress temp = new OrderOutStockExpressDTO.OrderOutStockExpress();
        temp.setExpressName("EMS快递");
        temp.setExpressCode("ems");
        OrderOutStockExpressDTO.OrderOutStockExpress temp1 = new OrderOutStockExpressDTO.OrderOutStockExpress();
        temp1.setExpressName("顺丰快递");
        temp1.setExpressCode("shunfeng");
        List<OrderOutStockExpressDTO.OrderOutStockExpress.Category> categoriesTemp1 = new ArrayList<>();
        OrderOutStockExpressDTO.OrderOutStockExpress.Category category11 = new OrderOutStockExpressDTO.OrderOutStockExpress.Category();
        category11.setRank(1);
        category11.setName("顺丰干配（大仓专用）");
        category11.setCode("2_2312_1");
        OrderOutStockExpressDTO.OrderOutStockExpress.Category category13 = new OrderOutStockExpressDTO.OrderOutStockExpress.Category();
        category13.setRank(2);
        category13.setCode("2_9386_3");
        category13.setName("顺丰标快（全国套餐）");
        /*OrderOutStockExpressDTO.OrderOutStockExpress.Category category14 = new OrderOutStockExpressDTO.OrderOutStockExpress.Category();
        category14.setRank(3);
        category14.setCode("154_9386_1");
        category14.setName("顺丰重货（发全国20-100KG）");
        OrderOutStockExpressDTO.OrderOutStockExpress.Category category12 = new OrderOutStockExpressDTO.OrderOutStockExpress.Category();
        category12.setRank(4);
        category12.setCode("155_9386_2");
        category12.setName("顺丰零担（发全国100KG+）");*/
        categoriesTemp1.add(category11);
        //categoriesTemp1.add(category12);
        categoriesTemp1.add(category13);
        //categoriesTemp1.add(category14);
        temp1.setCategories(categoriesTemp1);
        OrderOutStockExpressDTO.OrderOutStockExpress temp2 = new OrderOutStockExpressDTO.OrderOutStockExpress();
        temp2.setExpressName("京东物流（九机特惠）");
        temp2.setExpressCode("jingdong-jiuji");
        List<OrderOutStockExpressDTO.OrderOutStockExpress.Category> categoriesTemp2 = new ArrayList<>();
        OrderOutStockExpressDTO.OrderOutStockExpress.Category category21 = new OrderOutStockExpressDTO.OrderOutStockExpress.Category();
        category21.setRank(1);
        category21.setCode("1");
        category21.setName("京东快递(发全国0-20KG )");
        OrderOutStockExpressDTO.OrderOutStockExpress.Category category22 = new OrderOutStockExpressDTO.OrderOutStockExpress.Category();
        category22.setRank(2);
        category22.setCode("2");
        category22.setName("京东重货(发全国20-100KG )");
        OrderOutStockExpressDTO.OrderOutStockExpress.Category category23 = new OrderOutStockExpressDTO.OrderOutStockExpress.Category();
        category23.setRank(3);
        category23.setCode("3");
        category23.setName("京东零担(发全国100KG以上)");
        categoriesTemp2.add(category21);
        categoriesTemp2.add(category22);
        categoriesTemp2.add(category23);
        temp2.setCategories(categoriesTemp2);
        OrderOutStockExpressDTO.OrderOutStockExpress temp3 = new OrderOutStockExpressDTO.OrderOutStockExpress();
        temp3.setExpressName("新中通快递");
        temp3.setExpressCode("zhongtong-new");
        expressList.add(temp);
        expressList.add(temp1);
        expressList.add(temp2);
        expressList.add(temp3);
        result.setExpressList(expressList);
        return result;
    }

    @Override
    public void orderOutStockPrint(OrderOutStockPrintReq req) {
        //快递单数
        Integer packagesNumber = Optional.ofNullable(req.getPackagesNumber()).orElse(1);
        String subtype = req.getSubtype();
        OrderOutStockTypeEnum orderOutStockSearchEnum = OrderOutStockTypeEnum.getEnumByMessage(subtype);
        if (Objects.isNull(orderOutStockSearchEnum)) {
            throw new CustomizeException("找不到单据类型：" + req.getSubtype());
        }
        String key = ORDER_OUT_STOCK_PRINT
                + ":" + orderOutStockSearchEnum.getCode() + ":" + req.getSubId();
        RLock lock = redissonClient.getLock(key);
        try {
            boolean isLock = lock.tryLock(0, LOCK_MILLISECONDS, TimeUnit.MILLISECONDS);
            if (!isLock) {
                throw new CustomizeException("已收到,正在处理中");
            }
            OaUserBO currentStaffId = currentRequestComponent.getCurrentStaffId();

            String deliveryCompanyCode = req.getExpressCompany();
            List<OrderOutStockPrintReq.SkuItem> skuList = req.getSkuList();
            List<OrderOutStockPrintReq.SkuItem> newSkuList = new ArrayList<>();
            Map<Integer, List<OrderOutStockPrintReq.SkuItem>> basketIdMap = skuList.stream().collect(Collectors.groupingBy(OrderOutStockPrintReq.SkuItem::getBasketId));
            Set<Integer> basketIdList = basketIdMap.keySet();

            for (Integer basketId : basketIdList) {
                List<OrderOutStockPrintReq.SkuItem> skuItems = basketIdMap.get(basketId);
                OrderOutStockPrintReq.SkuItem temp = new OrderOutStockPrintReq.SkuItem();
                for (OrderOutStockPrintReq.SkuItem skuItem : skuItems) {
                    temp.setPpriceid(skuItem.getPpriceid());
                    temp.setBasketId(skuItem.getBasketId());
                    Integer count = skuItem.getCount() + temp.getCount();
                    temp.setCount(count);
                }
                newSkuList.add(temp);
            }

            switch (orderOutStockSearchEnum) {
                case SUB:
                    String subId = req.getSubId();
                    Sub sub = subService.getSub(Convert.toInt(subId));
                    Map<Integer, Basket> basketMap = basketService.lambdaQuery().eq(Basket::getSubId, subId)
                            .and(item -> item.eq(Basket::getIsdel, 0).or().isNull(Basket::getIsdel))
                            .list().stream().collect(Collectors.toMap(Basket::getBasketId, Function.identity(), (v1, v2) -> v1));
                    if (!Convert.toInt(newSkuList.size()).equals(basketMap.values().size())) {
                        throw new CustomizeException("扫描数量与订单明细数量不一致");
                    }
                    for (OrderOutStockPrintReq.SkuItem skuItem : newSkuList) {
                        Basket basket = basketMap.get(skuItem.getBasketId());
                        if (!skuItem.getCount().equals(basket.getBasketCount())) {
                            throw new CustomizeException("扫描ppid:" + basket.getPpriceid() + "数量错误，扫描数量:" + skuItem.getCount() + "，实际数量:" + basket.getBasketCount());
                        }
                    }
                    //线上国补订单处理
                    List<NationalSupplementKindRes> nationalSupplementKindList = SpringUtil.getBean(NationalSupplementService.class).getNationalSupplementKindList(Collections.singletonList(subId));
                    if (XtenantEnum.isJiujiXtenant() && CollUtil.isNotEmpty(nationalSupplementKindList)) {
                        //线上国补判断是否使用顺丰
                        if (!LogisticsExpressTypeEnum.SHUN_FENG_LAAS.getCode().equals(deliveryCompanyCode)) {
                            throw new CustomizeException("线上国补订单只支持顺丰快递，请选择顺丰快递后再操作");
                        }
                        //处理物流单
                        WuLiuInfoReqVO vo = new WuLiuInfoReqVO();
                        vo.setSessionAreaId(currentStaffId.getAreaId());
                        vo.setWuliuid(null);
                        if (subId != null) {
                            vo.setSubId(Convert.toInt(subId));
                        }
                        vo.setActionName("sub");
                        WuLiuInfoReqVO wuLiuInfo = wuLiuBusService.getWuLiuInfo(vo, currentStaffId);
                        if (Objects.isNull(wuLiuInfo.getWuliuid())) {
                            throw new CustomizeException("订单还未生成物流单，请生成物流单后再提交");
                        }
                        //判断订单是否已经出库
                        //国补订单需要先出库，在生成物流单和快递单
                        if (!SubCheckEnum.ALREADY_OUT.getCode().equals(sub.getSubCheck())) {
                            OrderStateChangeReq orderStateChangeReq = new OrderStateChangeReq();
                            orderStateChangeReq.setAreaid(sub.getAreaid());
                            orderStateChangeReq.setSubId(subId);
                            orderStateChangeReq.setUserName(currentStaffId.getUserName());
                            orderStateChangeReq.setSub_check(SubCheckEnum.ALREADY_OUT.getCode());
                            orderStateChangeReq.setSubtype(OrderOutStockTypeEnum.SUB.getMessage());
                            //订单出库
                            Boolean orderStateChangeFlag = this.subCheckOpByHttpRequest(orderStateChangeReq);
                            if (!Boolean.TRUE.equals(orderStateChangeFlag)) {
                                throw new CustomizeException("线上国补订单出库失败");
                            }
                        }

                        if (StringUtils.isEmpty(wuLiuInfo.getNu())) {
                            if (req.getPrintElectricalReceipts()) {
                                handleOrderOutStockWuliu(req, deliveryCompanyCode, packagesNumber, wuLiuInfo, sub, currentStaffId, vo);
                            }
                        } else {
                            deliveryCompanyCode = wuLiuInfo.getCom();
                        }
                        //打印
                        orderOutPrint(req, wuLiuInfo, subId, deliveryCompanyCode);
                    } else {
                    WuLiuInfoReqVO vo = new WuLiuInfoReqVO();
                    vo.setSessionAreaId(currentStaffId.getAreaId());
                    vo.setWuliuid(null);
                    if (subId != null) {
                        vo.setSubId(Convert.toInt(subId));
                    }
                    vo.setActionName("sub");
                    WuLiuInfoReqVO wuLiuInfo = wuLiuBusService.getWuLiuInfo(vo, currentStaffId);
                    if (Objects.isNull(wuLiuInfo.getWuliuid())) {
                        throw new CustomizeException("订单还未生成物流单，请生成物流单后再提交");
                    }

                    if (StringUtils.isEmpty(wuLiuInfo.getNu())) {
                        if (req.getPrintElectricalReceipts()) {
                            if (StringUtils.isEmpty(deliveryCompanyCode)) {
                                throw new CustomizeException("快递公司不能为空");
                            }
                            WuLiuAddOrUpdateReqVO wuliuReq = new WuLiuAddOrUpdateReqVO();
                            wuliuReq.setSource(0);
                            wuliuReq.setPackagesNumber(packagesNumber);
                            wuliuReq.setPaiJianRen(wuLiuInfo.getPaiJianRen());
                            wuliuReq.setShouJianRen(wuLiuInfo.getShouJianRen());
                            wuliuReq.setWCateId(wuLiuInfo.getWCateId());
                            wuliuReq.setWuliuid(wuLiuInfo.getWuliuid());
                            wuliuReq.setSubId(sub.getSubId());
                            Integer areaid = Objects.nonNull(sub.getKcareaid()) ? sub.getKcareaid() : sub.getAreaid();
                            wuliuReq.setAreaId(areaid);
//                    wuliuReq.setArea(areaInfoService.getAreaById(areaid));
                            wuliuReq.setCom(deliveryCompanyCode);
                            wuliuReq.setComment(Strings.EMPTY);
                            wuliuReq.setDanHaoBind(sub.getSubId());
                            wuliuReq.setCTime(LocalDateTime.now());
                            wuliuReq.setWuType(4);
                            wuliuReq.setPackageCount("1");
                            wuliuReq.setExpressType(req.getExpressCategory());
                            wuliuReq.setActionName("sub");
                            wuliuReq.setSareaid(wuLiuInfo.getSareaid());
                            wuliuReq.setSareaid2(Convert.toStr(wuLiuInfo.getSareaid()));
                            wuliuReq.setSAreaM(Convert.toStr(wuLiuInfo.getSareaid()));
                            wuliuReq.setSAddress(wuLiuInfo.getSAddress());
                            wuliuReq.setSCityId(wuLiuInfo.getSCityId());
                            wuliuReq.setSMobile(wuLiuInfo.getSMobile());
                            wuliuReq.setSName(wuLiuInfo.getSName());
                            wuliuReq.setSDid(wuLiuInfo.getSDid());
                            wuliuReq.setRDid(wuLiuInfo.getRDid());
                            wuliuReq.setRpid(wuLiuInfo.getRpid());
                            wuliuReq.setVloumn(BigDecimal.ONE);
                            wuliuReq.setRzid(wuLiuInfo.getRzid());
                            wuliuReq.setSpid(wuLiuInfo.getSpid());
                            wuliuReq.setSzid(wuLiuInfo.getSzid());
                            wuliuReq.setDTime(LocalDateTime.now());
                            wuliuReq.setRAddress(wuLiuInfo.getRAddress());
                            wuliuReq.setRareaid(wuLiuInfo.getRareaid());
                            wuliuReq.setRCityId(wuLiuInfo.getRCityId());
                            wuliuReq.setRMobile(wuLiuInfo.getRMobile());
                            wuliuReq.setRName(wuLiuInfo.getRName());
                            wuliuReq.setWeight(wuLiuInfo.getWeight());
                            wuliuReq.setJiujiJdExpressType(req.getExpressCategory());

                            String jiuJiSfExpressType = "1";
                            if ("2_2312_1".equals(req.getExpressCategory())) {
                                jiuJiSfExpressType = "1";
                            }
                            if ("2_9386_3".equals(req.getExpressCategory())) {
                                jiuJiSfExpressType = "2";
                            }
                            if ("154_9386_1".equals(req.getExpressCategory())) {
                                jiuJiSfExpressType = "3";
                            }
                            if ("155_9386_2".equals(req.getExpressCategory())) {
                                jiuJiSfExpressType = "4";
                            }
                            wuliuReq.setJiuJiSfExpressType(jiuJiSfExpressType);
                            R<Integer> add = wuLiuService.addOrUpdate(currentStaffId, wuliuReq);
                            if (!"更新成功".equals(add.getUserMsg())) {
                                throw new CustomizeException("生成快递单错误");
                            }
                            WuLiuInfoReqVO wuLiuInfo2 = wuLiuBusService.getWuLiuInfo(vo, currentStaffId);
                            if (StringUtils.isEmpty(wuLiuInfo2.getNu())) {
                                throw new CustomizeException("生成快递单错误");
                            }
                            wuLiuService.updateStateById(wuLiuInfo.getWuliuid(), WuliuStatusEnum.WAITING_GETTING_GOODS.getCode());
                        }
                    } else {
                        deliveryCompanyCode = wuLiuInfo.getCom();
                    }
                    OrderStateChangeReq orderStateChangeReq = new OrderStateChangeReq();
                    orderStateChangeReq.setAreaid(sub.getAreaid());
                    orderStateChangeReq.setSubId(subId);
                    orderStateChangeReq.setUserName(currentStaffId.getUserName());
                    orderStateChangeReq.setSub_check(SubCheckEnum.ALREADY_OUT.getCode());
                    orderStateChangeReq.setSubtype(OrderOutStockTypeEnum.SUB.getMessage());
                    Boolean orderStateChangeFlag = this.subCheckOpByHttpRequest(orderStateChangeReq);
                    if (true) {
                        Integer wuliuId = wuLiuInfo.getWuliuid();
                        //打印
                        if (req.getIsPrint()) {
                            Integer printCount = req.getPrintCount();
                            for (int i = 0; i < printCount; i++) {
                                String url = inwcf2Url;
                                String clientNo = req.getClientNo();
                                Integer type = null;
                                List<String> urlList = new ArrayList<>();

                                //物流单
                                if (req.getPrintLogistics()) {
                                    type = 33;
                                    String tempUrl = ("https://moa.9ji.com/oa/Print/doPrint?printid=" + wuliuId
                                            + "&type=" + type + "&clientNo=" + clientNo);
                                    urlList.add(tempUrl);
                                }

                                //销售单
                                if (req.getPrintSaleReceipts()) {
                                    type = 15;
                                    String tempUrl = ("https://moa.9ji.com/oa/print/Sub_Print_xdC?sub_id=" + subId
                                            + "&type=" + type + "&clientNo=" + clientNo);
                                    urlList.add(tempUrl);
                                }

                                //电子面单
                                if (req.getPrintElectricalReceipts()) {
                                    if (Objects.isNull(wuliuId)) {
                                        throw new CustomizeException("wuliuId不能为空");
                                    }
                                    LogisticsExpressTypeEnum deliveryCompanyEnums = LogisticsExpressTypeEnum.getLogisticsExpressTypeEnum(deliveryCompanyCode);
                                    if (Objects.isNull(deliveryCompanyEnums)) {
                                        throw new CustomizeException("找不到该物流类型:" + deliveryCompanyCode);
                                    }
                                    switch (deliveryCompanyEnums) {
                                        case EMS:
                                            type = 43;
                                            break;
                                        case SHUN_FENG_LAAS:
                                            type = 13;
                                            break;
                                        case JING_DONG_9JI:
                                            type = 44;
                                            break;
                                        case ZHONG_TONG:
                                            type = 57;
                                            break;
                                        default:
                                            break;
                                    }
                                    String tempUrl = ("https://moa.9ji.com/oa/Print/doPrint?printid=" + subId
                                            + "|" + wuliuId + "&type=" + type + "&ClientNo=" + clientNo);
                                    urlList.add(tempUrl);
                                }
                                try {
                                    Map<String, String> headMap = new HashMap<>();
                                    headMap.put("Authorization", currentRequestComponent.getCurrentToken());
                                    headMap.put(MultitenancyInterceptor.IS_REQUEST_FROM_PAD, Convert.toStr(Convert.toInt(MyDynamicRoutingDataSource.isPda())));
                                    for (String httpUrl : urlList) {
                                        String result = HttpClientUtils.get(httpUrl, headMap);
                                        log.info("打印数据" + httpUrl + "___" + result);
                                    }
                                } catch (Exception e) {
                                    log.error("打印机通知失败，参数：{}", StringUtils.join(urlList, StringPool.COMMA));
                                    throw new CustomizeException(String.format("打印机通知失败，Exception：%s, 参数：%s,", e.getMessage(), StringUtils.join(urlList, StringPool.COMMA)));
                                }
                            }
                        }
                    }
                    }
                    break;
                case RECOVER_SUB:
                    String recoverSubId = req.getSubId();
                    RecoverMarketinfo recoverSub = recoverMarketinfoService.getRecoverSub(Convert.toInt(recoverSubId));
                    if (Objects.isNull(recoverSub)) {
                        throw new CustomizeException("找不到该良品单");
                    }
                    Map<Integer, RecoverMarketsubinfo> recoverMarketsubinfotMap = recoverMarketsubinfoService.lambdaQuery()
                            .and(item -> item.eq(RecoverMarketsubinfo::getIsdel, 0).or().isNull(RecoverMarketsubinfo::getIsdel))
                            .eq(RecoverMarketsubinfo::getSubId, recoverSubId)
                            .list().stream().collect(Collectors.toMap(RecoverMarketsubinfo::getBasketId, Function.identity(), (v1, v2) -> v1));

                    if (!Convert.toInt(skuList.size()).equals(recoverMarketsubinfotMap.values().size())) {
                        throw new CustomizeException("扫描数量与订单明细数量不一致");
                    }
                    for (OrderOutStockPrintReq.SkuItem skuItem : skuList) {
                        RecoverMarketsubinfo basket = recoverMarketsubinfotMap.get(skuItem.getBasketId());
                        if (!skuItem.getCount().equals(basket.getBasketCount())) {
                            throw new CustomizeException("扫描ppid:" + basket.getPpriceid() + "数量错误，扫描数量:" + skuItem.getCount() + "，实际数量:" + basket.getBasketCount());
                        }
                    }

                    WuLiuInfoReqVO recoverVo = new WuLiuInfoReqVO();
                    recoverVo.setSessionAreaId(currentStaffId.getAreaId());
                    recoverVo.setWuliuid(null);
                    if (recoverSubId != null) {
                        recoverVo.setSubId(Convert.toInt(recoverSubId));
                    }
                    recoverVo.setActionName("ReSub");
                    WuLiuInfoReqVO recoverWuLiuInfo = wuLiuBusService.getWuLiuInfo(recoverVo, currentStaffId);
                    if (Objects.isNull(recoverWuLiuInfo) || Objects.isNull(recoverWuLiuInfo.getWuliuid())) {
                        throw new CustomizeException("订单还未生成物流单，请生成物流单后再提交");
                    }
                    if (StringUtils.isEmpty(recoverWuLiuInfo.getNu())) {
                        if (StringUtils.isEmpty(deliveryCompanyCode)) {
                            throw new CustomizeException("快递公司不能为空");
                        }
                        WuLiuAddOrUpdateReqVO wuliuReq = new WuLiuAddOrUpdateReqVO();
                        wuliuReq.setSource(0);
                        wuliuReq.setPackagesNumber(packagesNumber);
                        wuliuReq.setPaiJianRen(recoverWuLiuInfo.getPaiJianRen());
                        wuliuReq.setShouJianRen(recoverWuLiuInfo.getShouJianRen());
                        wuliuReq.setWCateId(recoverWuLiuInfo.getWCateId());
                        wuliuReq.setWuliuid(recoverWuLiuInfo.getWuliuid());
                        wuliuReq.setSubId(Convert.toInt(recoverSub.getSubId()));
                        Integer areaid = recoverSub.getAreaid();
                        wuliuReq.setAreaId(areaid);
//                    wuliuReq.setArea(areaInfoService.getAreaById(areaid));
                        wuliuReq.setCom(deliveryCompanyCode);
                        wuliuReq.setComment(Strings.EMPTY);
                        wuliuReq.setDanHaoBind(Convert.toInt(recoverSub.getSubId()));
                        wuliuReq.setCTime(LocalDateTime.now());
                        wuliuReq.setDanHaoBind(Convert.toInt(recoverSub.getSubId()));
                        wuliuReq.setWuType(9);
                        wuliuReq.setPackageCount("1");
                        wuliuReq.setExpressType(req.getExpressCategory());
                        wuliuReq.setActionName("sub");
                        wuliuReq.setSareaid(recoverWuLiuInfo.getSareaid());
                        wuliuReq.setSareaid2(Convert.toStr(recoverWuLiuInfo.getSareaid()));
                        wuliuReq.setSAreaM(Convert.toStr(recoverWuLiuInfo.getSareaid()));
                        wuliuReq.setSAddress(recoverWuLiuInfo.getSAddress());
                        wuliuReq.setSCityId(recoverWuLiuInfo.getSCityId());
                        wuliuReq.setSMobile(recoverWuLiuInfo.getSMobile());
                        wuliuReq.setSName(recoverWuLiuInfo.getSName());
                        wuliuReq.setSDid(recoverWuLiuInfo.getSDid());
                        wuliuReq.setRDid(recoverWuLiuInfo.getRDid());
                        wuliuReq.setRpid(recoverWuLiuInfo.getRpid());
                        wuliuReq.setStats(WuliuStatusEnum.WAITING_GETTING_GOODS.getCode());
                        wuliuReq.setVloumn(BigDecimal.ONE);
                        wuliuReq.setRzid(recoverWuLiuInfo.getRzid());
                        wuliuReq.setSpid(recoverWuLiuInfo.getSpid());
                        wuliuReq.setSzid(recoverWuLiuInfo.getSzid());
                        wuliuReq.setDTime(LocalDateTime.now());
                        wuliuReq.setRAddress(recoverWuLiuInfo.getRAddress());
                        wuliuReq.setRareaid(recoverWuLiuInfo.getRareaid());
                        wuliuReq.setRCityId(recoverWuLiuInfo.getRCityId());
                        wuliuReq.setRMobile(recoverWuLiuInfo.getRMobile());
                        wuliuReq.setRName(recoverWuLiuInfo.getRName());
                        wuliuReq.setWeight(recoverWuLiuInfo.getWeight());
                        wuliuReq.setJiujiJdExpressType(req.getExpressCategory());

                        String jiuJiSfExpressType = "1";
                        if ("2_2312_1".equals(req.getExpressCategory())) {
                            jiuJiSfExpressType = "1";
                        }
                        if ("2_9386_3".equals(req.getExpressCategory())) {
                            jiuJiSfExpressType = "2";
                        }
                        if ("154_9386_1".equals(req.getExpressCategory())) {
                            jiuJiSfExpressType = "3";
                        }
                        if ("155_9386_2".equals(req.getExpressCategory())) {
                            jiuJiSfExpressType = "4";
                        }
                        wuliuReq.setJiuJiSfExpressType(jiuJiSfExpressType);
                        R<Integer> add = wuLiuService.addOrUpdate(currentStaffId, wuliuReq);
                        if (!"更新成功".equals(add.getUserMsg())) {
                            throw new CustomizeException("生成快递单错误");
                        }
                        WuLiuInfoReqVO wuLiuInfo2 = wuLiuBusService.getWuLiuInfo(recoverVo, currentStaffId);
                        if (StringUtils.isEmpty(wuLiuInfo2.getNu())) {
                            throw new CustomizeException("生成快递单错误");
                        }
                    } else {
                        deliveryCompanyCode = recoverWuLiuInfo.getCom();
                    }
                    OrderStateChangeReq recoverOrderStateChangeReq = new OrderStateChangeReq();
                    recoverOrderStateChangeReq.setAreaid(recoverSub.getAreaid());
                    recoverOrderStateChangeReq.setSubId(recoverSubId);
                    recoverOrderStateChangeReq.setUserName(currentStaffId.getUserName());
                    recoverOrderStateChangeReq.setSub_check(SubCheckEnum.ALREADY_OUT.getCode());
                    recoverOrderStateChangeReq.setSubtype(OrderOutStockTypeEnum.RECOVER_SUB.getMessage());
                    Boolean recoverOrderStateChangeFlag = this.subCheckOpByHttpRequest(recoverOrderStateChangeReq);
                    if (true) {
                        Integer wuliuId = recoverWuLiuInfo.getWuliuid();
                        //打印
                        if (req.getIsPrint()) {
                            Integer printCount = req.getPrintCount();
                            for (int i = 0; i < printCount; i++) {
                                String url = inwcf2Url;
                                String clientNo = req.getClientNo();
                                Integer type = null;
                                List<String> urlList = new ArrayList<>();

                                //物流单
                                if (req.getPrintLogistics()) {
                                    type = 33;
                                    String tempUrl = ("https://moa.9ji.com/oa/Print/doPrint?printid=" + wuliuId
                                            + "&type=" + type + "&clientNo=" + clientNo);
                                    urlList.add(tempUrl);
                                }

                                //良品单
                                if (req.getPrintSaleReceipts()) {
                                    type = 15;
                                    String tempUrl = ("https://moa.9ji.com/oa/StockOut/printSubC?sub_id=" + recoverSubId
                                            + "&type=" + type + "&clientNo=" + clientNo);
                                    urlList.add(tempUrl);
                                }

                                //电子面单
                                if (req.getPrintElectricalReceipts()) {
                                    if (Objects.isNull(wuliuId)) {
                                        throw new CustomizeException("wuliuId不能为空");
                                    }
                                    LogisticsExpressTypeEnum deliveryCompanyEnums = LogisticsExpressTypeEnum.getLogisticsExpressTypeEnum(deliveryCompanyCode);
                                    if (Objects.isNull(deliveryCompanyEnums)) {
                                        throw new CustomizeException("找不到该物流类型:" + deliveryCompanyCode);
                                    }
                                    switch (deliveryCompanyEnums) {
                                        case EMS:
                                            type = 43;
                                            break;
                                        case SHUN_FENG_LAAS:
                                            type = 13;
                                            break;
                                        case JING_DONG_9JI:
                                            type = 44;
                                            break;
                                        case ZHONG_TONG:
                                            type = 57;
                                            break;
                                        default:
                                            break;
                                    }
                                    String tempUrl = ("https://moa.9ji.com/oa/Print/doPrint?printid=" + recoverSubId
                                            + "|" + wuliuId + "&type=" + type + "&ClientNo=" + clientNo);
                                    urlList.add(tempUrl);
                                }
                                try {
                                    Map<String, String> headMap = new HashMap<>();
                                    headMap.put("Authorization", currentRequestComponent.getCurrentToken());
                                    headMap.put(MultitenancyInterceptor.IS_REQUEST_FROM_PAD, Convert.toStr(Convert.toInt(MyDynamicRoutingDataSource.isPda())));
                                    for (String httpUrl : urlList) {
                                        String result = HttpClientUtils.get(httpUrl, headMap);
                                        log.info("打印数据" + httpUrl + "___" + result);
                                    }
                                } catch (Exception e) {
                                    log.error("打印机通知失败，参数：{}", StringUtils.join(urlList, StringPool.COMMA));
                                    throw new CustomizeException(String.format("打印机通知失败，Exception：%s, 参数：%s,", e.getMessage(), StringUtils.join(urlList, StringPool.COMMA)));
                                }
                            }
                        }
                    }
                    break;
                default:
                    break;

            }
        } catch (Exception e) {
            log.error("调用失败！", e);
            String message = e.getMessage();
            CustomizeException customizeException = new CustomizeException(message);
            if(StringUtils.isNoneBlank(message) && message.contains("没有操作下账")){
                customizeException.setCode(-101);
            }
            throw customizeException ;
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private void orderOutPrint(OrderOutStockPrintReq req, WuLiuInfoReqVO wuLiuInfo, String subId, String deliveryCompanyCode) {
        Integer wuliuId = wuLiuInfo.getWuliuid();
        //打印
        if (req.getIsPrint()) {
            Integer printCount = req.getPrintCount();
            for (int i = 0; i < printCount; i++) {
                String url = inwcf2Url;
                String clientNo = req.getClientNo();
                Integer type = null;
                List<String> urlList = new ArrayList<>();

                //物流单
                if (req.getPrintLogistics()) {
                    type = 33;
                    String tempUrl = ("https://moa.9ji.com/oa/Print/doPrint?printid=" + wuliuId
                            + "&type=" + type + "&clientNo=" + clientNo);
                    urlList.add(tempUrl);
                }

                //销售单
                if (req.getPrintSaleReceipts()) {
                    type = 15;
                    String tempUrl = ("https://moa.9ji.com/oa/print/Sub_Print_xdC?sub_id=" + subId
                            + "&type=" + type + "&clientNo=" + clientNo);
                    urlList.add(tempUrl);
                }

                //电子面单
                if (req.getPrintElectricalReceipts()) {
                    if (Objects.isNull(wuliuId)) {
                        throw new CustomizeException("wuliuId不能为空");
                    }
                    LogisticsExpressTypeEnum deliveryCompanyEnums = LogisticsExpressTypeEnum.getLogisticsExpressTypeEnum(deliveryCompanyCode);
                    if (Objects.isNull(deliveryCompanyEnums)) {
                        throw new CustomizeException("找不到该物流类型:" + deliveryCompanyCode);
                    }
                    switch (deliveryCompanyEnums) {
                        case EMS:
                            type = 43;
                            break;
                        case SHUN_FENG_LAAS:
                            type = 13;
                            break;
                        case JING_DONG_9JI:
                            type = 44;
                            break;
                        case ZHONG_TONG:
                            type = 57;
                            break;
                        default:
                            break;
                    }
                    String tempUrl = ("https://moa.9ji.com/oa/Print/doPrint?printid=" + subId
                            + "|" + wuliuId + "&type=" + type + "&ClientNo=" + clientNo);
                    urlList.add(tempUrl);
                }
                try {
                    Map<String, String> headMap = new HashMap<>();
                    headMap.put("Authorization", currentRequestComponent.getCurrentToken());
                    headMap.put(MultitenancyInterceptor.IS_REQUEST_FROM_PAD, Convert.toStr(Convert.toInt(MyDynamicRoutingDataSource.isPda())));
                    for (String httpUrl : urlList) {
                        String result = HttpClientUtils.get(httpUrl, headMap);
                        log.info("打印数据" + httpUrl + "___" + result);
                    }
                } catch (Exception e) {
                    log.error("打印机通知失败，参数：{}", StringUtils.join(urlList, StringPool.COMMA));
                    throw new CustomizeException(String.format("打印机通知失败，Exception：%s, 参数：%s,", e.getMessage(), StringUtils.join(urlList, StringPool.COMMA)));
                }
            }
        }
    }

    private void handleOrderOutStockWuliu(OrderOutStockPrintReq req, String deliveryCompanyCode, Integer packagesNumber, WuLiuInfoReqVO wuLiuInfo, Sub sub, OaUserBO currentStaffId, WuLiuInfoReqVO vo) {
        if (StringUtils.isEmpty(deliveryCompanyCode)) {
            throw new CustomizeException("快递公司不能为空");
        }
        WuLiuAddOrUpdateReqVO wuliuReq = new WuLiuAddOrUpdateReqVO();
        wuliuReq.setSource(0);
        wuliuReq.setPackagesNumber(packagesNumber);
        wuliuReq.setPaiJianRen(wuLiuInfo.getPaiJianRen());
        wuliuReq.setShouJianRen(wuLiuInfo.getShouJianRen());
        wuliuReq.setWCateId(wuLiuInfo.getWCateId());
        wuliuReq.setWuliuid(wuLiuInfo.getWuliuid());
        wuliuReq.setSubId(sub.getSubId());
        Integer areaid = Objects.nonNull(sub.getKcareaid()) ? sub.getKcareaid() : sub.getAreaid();
        wuliuReq.setAreaId(areaid);
//                    wuliuReq.setArea(areaInfoService.getAreaById(areaid));
        wuliuReq.setCom(deliveryCompanyCode);
        wuliuReq.setComment(Strings.EMPTY);
        wuliuReq.setDanHaoBind(sub.getSubId());
        wuliuReq.setCTime(LocalDateTime.now());
        wuliuReq.setWuType(4);
        wuliuReq.setPackageCount("1");
        wuliuReq.setExpressType(req.getExpressCategory());
        wuliuReq.setActionName("sub");
        wuliuReq.setSareaid(wuLiuInfo.getSareaid());
        wuliuReq.setSareaid2(Convert.toStr(wuLiuInfo.getSareaid()));
        wuliuReq.setSAreaM(Convert.toStr(wuLiuInfo.getSareaid()));
        wuliuReq.setSAddress(wuLiuInfo.getSAddress());
        wuliuReq.setSCityId(wuLiuInfo.getSCityId());
        wuliuReq.setSMobile(wuLiuInfo.getSMobile());
        wuliuReq.setSName(wuLiuInfo.getSName());
        wuliuReq.setSDid(wuLiuInfo.getSDid());
        wuliuReq.setRDid(wuLiuInfo.getRDid());
        wuliuReq.setRpid(wuLiuInfo.getRpid());
        wuliuReq.setVloumn(BigDecimal.ONE);
        wuliuReq.setRzid(wuLiuInfo.getRzid());
        wuliuReq.setSpid(wuLiuInfo.getSpid());
        wuliuReq.setSzid(wuLiuInfo.getSzid());
        wuliuReq.setDTime(LocalDateTime.now());
        wuliuReq.setRAddress(wuLiuInfo.getRAddress());
        wuliuReq.setRareaid(wuLiuInfo.getRareaid());
        wuliuReq.setRCityId(wuLiuInfo.getRCityId());
        wuliuReq.setRMobile(wuLiuInfo.getRMobile());
        wuliuReq.setRName(wuLiuInfo.getRName());
        wuliuReq.setWeight(wuLiuInfo.getWeight());
        wuliuReq.setJiujiJdExpressType(req.getExpressCategory());

        String jiuJiSfExpressType = "1";
        if ("2_2312_1".equals(req.getExpressCategory())) {
            jiuJiSfExpressType = "1";
        }
        if ("2_9386_3".equals(req.getExpressCategory())) {
            jiuJiSfExpressType = "2";
        }
        if ("154_9386_1".equals(req.getExpressCategory())) {
            jiuJiSfExpressType = "3";
        }
        if ("155_9386_2".equals(req.getExpressCategory())) {
            jiuJiSfExpressType = "4";
        }
        wuliuReq.setJiuJiSfExpressType(jiuJiSfExpressType);
        R<Integer> add = wuLiuService.addOrUpdate(currentStaffId, wuliuReq);
        if (!"更新成功".equals(add.getUserMsg())) {
            throw new CustomizeException("生成快递单错误");
        }
        WuLiuInfoReqVO wuLiuInfo2 = wuLiuBusService.getWuLiuInfo(vo, currentStaffId);
        if (StringUtils.isEmpty(wuLiuInfo2.getNu())) {
            throw new CustomizeException("生成快递单错误");
        }
        wuLiuService.updateStateById(wuLiuInfo.getWuliuid(), WuliuStatusEnum.WAITING_GETTING_GOODS.getCode());
    }


    @Override
    public void scanPickOutStockPrint(OrderOutStockPrintReq req) {
        Integer subtypeId = req.getSubtypeId();
        OrderOutStockTypeEnum orderOutStockSearchEnum = OrderOutStockTypeEnum.getEnumByCode(subtypeId);
        if (Objects.isNull(orderOutStockSearchEnum)) {
            throw new CustomizeException("找不到单据类型：" + req.getSubtype());
        }
        Integer subIdTemp = Convert.toInt(req.getSubId());


        OrderOutStockDetailReq scanPickOutStockFlagVo = new OrderOutStockDetailReq();
        scanPickOutStockFlagVo.setSubtypeId(subtypeId);
        scanPickOutStockFlagVo.setSubId(subIdTemp);
        Boolean scanPickOutStockFlag = this.scanPickOutStockFlag(scanPickOutStockFlagVo);
        if (scanPickOutStockFlag) {
            throw new CustomizeException("该订单中存在商品没有扫码取件：" + req.getSubId());
        }
        String key = SCAN_PICK_OUT_STOCK_PRINT
                + ":" + orderOutStockSearchEnum.getCode() + ":" + req.getSubId();
        RLock lock = redissonClient.getLock(key);
        try {
            boolean isLock = lock.tryLock(0, LOCK_MILLISECONDS, TimeUnit.MILLISECONDS);
            if (!isLock) {
                throw new CustomizeException("已收到,正在处理中");
            }
            OaUserBO currentStaffId = currentRequestComponent.getCurrentStaffId();

            String deliveryCompanyCode = req.getExpressCompany();
            List<OrderOutStockPrintReq.SkuItem> skuList = req.getSkuList();
            List<OrderOutStockPrintReq.SkuItem> newSkuList = new ArrayList<>();
            Map<Integer, List<OrderOutStockPrintReq.SkuItem>> basketIdMap = skuList.stream().collect(Collectors.groupingBy(OrderOutStockPrintReq.SkuItem::getBasketId));
            Set<Integer> basketIdList = basketIdMap.keySet();

            for (Integer basketId : basketIdList) {
                List<OrderOutStockPrintReq.SkuItem> skuItems = basketIdMap.get(basketId);
                OrderOutStockPrintReq.SkuItem temp = new OrderOutStockPrintReq.SkuItem();
                for (OrderOutStockPrintReq.SkuItem skuItem : skuItems) {
                    temp.setPpriceid(skuItem.getPpriceid());
                    temp.setBasketId(skuItem.getBasketId());
                    Integer count = skuItem.getCount() + temp.getCount();
                    temp.setCount(count);
                }
                newSkuList.add(temp);
            }

            switch (orderOutStockSearchEnum) {
                case SUB:
                    String subId = req.getSubId();
                    Sub sub = subService.getSub(Convert.toInt(subId));
                    Map<Integer, Basket> basketMap = basketService.lambdaQuery().eq(Basket::getSubId, subId)
                            .and(item -> item.eq(Basket::getIsdel, 0).or().isNull(Basket::getIsdel))
                            .list().stream().collect(Collectors.toMap(Basket::getBasketId, Function.identity(), (v1, v2) -> v1));
                    if (!Convert.toInt(newSkuList.size()).equals(basketMap.values().size())) {
//                        throw new CustomizeException("扫描数量与订单明细数量不一致");
                    }
                    for (OrderOutStockPrintReq.SkuItem skuItem : newSkuList) {
                        Basket basket = basketMap.get(skuItem.getBasketId());
                        //if (!Objects.equals(skuItem.getCount(), basket.getBasketCount())) {
//                            throw new CustomizeException("扫描ppid:" + basket.getPpriceid() + "数量错误，扫描数量:" + skuItem.getCount() + "，实际数量:" + basket.getBasketCount());
                        //}
                    }
                    //线上国补订单处理
                    List<NationalSupplementKindRes> nationalSupplementKindList = SpringUtil.getBean(NationalSupplementService.class).getNationalSupplementKindList(Collections.singletonList(subId));
                    if (XtenantEnum.isJiujiXtenant() && CollUtil.isNotEmpty(nationalSupplementKindList)) {
                        //线上国补判断是否使用顺丰
                        if (!LogisticsExpressTypeEnum.SHUN_FENG_LAAS.getCode().equals(deliveryCompanyCode)) {
                            throw new CustomizeException("线上国补订单只支持顺丰快递，请选择顺丰快递后再操作");
                        }

                        //判断订单是否已经出库
                        //国补订单需要先出库，在生成物流单和快递单
                        if (!SubCheckEnum.ALREADY_OUT.getCode().equals(sub.getSubCheck())) {
                            OrderStateChangeReq orderStateChangeReq = new OrderStateChangeReq();
                            orderStateChangeReq.setAreaid(sub.getAreaid());
                            orderStateChangeReq.setSubId(subId);
                            orderStateChangeReq.setUserName(currentStaffId.getUserName());
                            orderStateChangeReq.setSub_check(SubCheckEnum.ALREADY_OUT.getCode());
                            orderStateChangeReq.setSubtype(OrderOutStockTypeEnum.SUB.getMessage());
                            //订单出库
                            Boolean orderStateChangeFlag = this.subCheckOpByHttpRequest(orderStateChangeReq);
                            if (!Boolean.TRUE.equals(orderStateChangeFlag)) {
                                throw new CustomizeException("线上国补订单出库失败");
                            }
                        }
                        //处理物流单
                        WuLiuInfoReqVO wuLiuInfo = getWuLiuInfoReqVO(req, currentStaffId, subId, deliveryCompanyCode, sub);
                        //打印
                        handlePrint(req, wuLiuInfo.getWuliuid(), subId, deliveryCompanyCode);
                        return;
                    }
                    //物流单
                    WuLiuInfoReqVO wuLiuInfo = getWuLiuInfoReqVO(req, currentStaffId, subId, deliveryCompanyCode, sub);

                    if (req.getIsOutstock() && !isPaiSong(sub.getDelivery())) {
                        OrderStateChangeReq orderStateChangeReq = new OrderStateChangeReq();
                        orderStateChangeReq.setAreaid(sub.getAreaid());
                        orderStateChangeReq.setSubId(subId);
                        orderStateChangeReq.setUserName(currentStaffId.getUserName());
                        orderStateChangeReq.setSub_check(SubCheckEnum.ALREADY_OUT.getCode());
                        orderStateChangeReq.setSubtype(OrderOutStockTypeEnum.SUB.getMessage());
                        Boolean orderStateChangeFlag = this.subCheckOpByHttpRequest(orderStateChangeReq);
//                        if (!orderStateChangeFlag) {
//                            throw new CustomizeException("扫描取件订单出库失败");
//                        }
                    }
                    Integer wuliuId = wuLiuInfo.getWuliuid();
                    //打印
                    handlePrint(req, wuliuId, subId, deliveryCompanyCode);

                    break;
                case RECOVER_SUB:

                    break;
                case SMALLPRO:
                    if(Optional.ofNullable(req.getPrintSmallProReceipts()).orElse(Boolean.FALSE)){
                        String area = currentStaffId.getArea();
                        Integer userId = currentStaffId.getUserId();
                        String userName = currentStaffId.getUserName();
                        String now = LocalDateTime.now().format(DateTimeFormatter.ofPattern(TimeFormatConstant.YYYY_MM_DD_HH_MM_SS));
                        String clientNo = req.getClientNo();
                        String smallproId = req.getSubId();
                        String urlSuffix = String.format("/push?cname=p%s&content=%s,%s,%s,%s,%s,%s,%s,%s,1", area, area, 24, smallproId, userId
                                , userName, clientNo, now, 1);
                        printWithPush(urlSuffix);
                        break;
                    }

                default:
                    break;

            }
        } catch (Exception e) {
            log.error("调用失败！", e);
            String message = e.getMessage();
            CustomizeException customizeException = new CustomizeException(message);
            if(StringUtils.isNoneBlank(message) && message.contains("没有操作下账")){
                customizeException.setCode(-101);
            }
            throw customizeException ;
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 处理物流单
     * @param req
     * @param currentStaffId
     * @param subId
     * @param deliveryCompanyCode
     * @param sub
     * @return
     */
    private WuLiuInfoReqVO getWuLiuInfoReqVO(OrderOutStockPrintReq req, OaUserBO currentStaffId, String subId, String deliveryCompanyCode, Sub sub) {
        WuLiuInfoReqVO vo = new WuLiuInfoReqVO();
        vo.setSessionAreaId(currentStaffId.getAreaId());
        vo.setWuliuid(null);
        if (subId != null) {
            vo.setSubId(Convert.toInt(subId));
        }
        vo.setActionName("sub");
        WuLiuInfoReqVO wuLiuInfo = wuLiuBusService.getWuLiuInfo(vo, currentStaffId);
        if (!DeliveryEnum.AREA_SELF_PICK.getCode().equals(wuLiuInfo.getDelivery())) {
            if (Objects.isNull(wuLiuInfo.getWuliuid())) {
                throw new CustomizeException("订单还未生成物流单，请生成物流单后再提交");
            }
            if (StringUtils.isEmpty(wuLiuInfo.getNu())) {
                if (req.getPrintElectricalReceipts()) {
                    if (StringUtils.isEmpty(deliveryCompanyCode)) {
                        throw new CustomizeException("快递公司不能为空");
                    }
                    WuLiuAddOrUpdateReqVO wuliuReq = new WuLiuAddOrUpdateReqVO();
                    wuliuReq.setSource(0);
                    wuliuReq.setPackagesNumber(1);
                    wuliuReq.setPaiJianRen(wuLiuInfo.getPaiJianRen());
                    wuliuReq.setShouJianRen(wuLiuInfo.getShouJianRen());
                    wuliuReq.setWCateId(wuLiuInfo.getWCateId());
                    wuliuReq.setWuliuid(wuLiuInfo.getWuliuid());
                    wuliuReq.setSubId(sub.getSubId());
                    Integer areaid = Objects.nonNull(sub.getKcareaid()) ? sub.getKcareaid() : sub.getAreaid();
                    wuliuReq.setAreaId(areaid);
//                    wuliuReq.setArea(areaInfoService.getAreaById(areaid));
                    wuliuReq.setCom(deliveryCompanyCode);
                    wuliuReq.setComment(Strings.EMPTY);
                    wuliuReq.setDanHaoBind(sub.getSubId());
                    wuliuReq.setCTime(LocalDateTime.now());
                    wuliuReq.setWuType(4);
                    wuliuReq.setPackageCount("1");
                    wuliuReq.setExpressType(req.getExpressCategory());
                    wuliuReq.setActionName("sub");
                    wuliuReq.setSareaid(wuLiuInfo.getSareaid());
                    wuliuReq.setSareaid2(Convert.toStr(wuLiuInfo.getSareaid()));
                    wuliuReq.setSAreaM(Convert.toStr(wuLiuInfo.getSareaid()));
                    wuliuReq.setSAddress(wuLiuInfo.getSAddress());
                    wuliuReq.setSCityId(wuLiuInfo.getSCityId());
                    wuliuReq.setSMobile(wuLiuInfo.getSMobile());
                    wuliuReq.setSName(wuLiuInfo.getSName());
                    wuliuReq.setSDid(wuLiuInfo.getSDid());
                    wuliuReq.setRDid(wuLiuInfo.getRDid());
                    wuliuReq.setRpid(wuLiuInfo.getRpid());
                    wuliuReq.setVloumn(BigDecimal.ONE);
                    wuliuReq.setRzid(wuLiuInfo.getRzid());
                    wuliuReq.setSpid(wuLiuInfo.getSpid());
                    wuliuReq.setSzid(wuLiuInfo.getSzid());
                    wuliuReq.setDTime(LocalDateTime.now());
                    wuliuReq.setRAddress(wuLiuInfo.getRAddress());
                    wuliuReq.setRareaid(wuLiuInfo.getRareaid());
                    wuliuReq.setRCityId(wuLiuInfo.getRCityId());
                    wuliuReq.setRMobile(wuLiuInfo.getRMobile());
                    wuliuReq.setRName(wuLiuInfo.getRName());
                    wuliuReq.setWeight(wuLiuInfo.getWeight());
                    wuliuReq.setJiujiJdExpressType(req.getExpressCategory());

                    String jiuJiSfExpressType = "1";
                    if ("2_2312_1".equals(req.getExpressCategory())) {
                        jiuJiSfExpressType = "1";
                    }
                    if ("2_9386_3".equals(req.getExpressCategory())) {
                        jiuJiSfExpressType = "2";
                    }
                    if ("154_9386_1".equals(req.getExpressCategory())) {
                        jiuJiSfExpressType = "3";
                    }
                    if ("155_9386_2".equals(req.getExpressCategory())) {
                        jiuJiSfExpressType = "4";
                    }
                    wuliuReq.setJiuJiSfExpressType(jiuJiSfExpressType);
                    R<Integer> add = wuLiuService.addOrUpdate(currentStaffId, wuliuReq);
                    if (!"更新成功".equals(add.getUserMsg())) {
                        throw new CustomizeException("生成快递单错误");
                    }
                    WuLiuInfoReqVO wuLiuInfo2 = wuLiuBusService.getWuLiuInfo(vo, currentStaffId);
                    if (StringUtils.isEmpty(wuLiuInfo2.getNu())) {
                        throw new CustomizeException("生成快递单错误");
                    }
                }
            }
        }
        return wuLiuInfo;
    }

    /**
     * 打印
     * @param req
     * @param wuliuId
     * @param subId
     * @param deliveryCompanyCode
     */
    private void handlePrint(OrderOutStockPrintReq req, Integer wuliuId, String subId, String deliveryCompanyCode) {
        if (req.getPrintLogistics() || req.getPrintSaleReceipts() || req.getPrintElectricalReceipts()) {
            Integer printCount = req.getPrintCount();
            for (int i = 0; i < printCount; i++) {
                String url = inwcf2Url;
                String clientNo = req.getClientNo();
                Integer type = null;
                List<String> urlList = new ArrayList<>();

                //物流单
                if (req.getPrintLogistics()) {
                    type = 33;
                    String tempUrl = ("https://moa.9ji.com/oa/Print/doPrint?printid=" + wuliuId
                            + "&type=" + type + "&clientNo=" + clientNo);
                    urlList.add(tempUrl);
                }

                //销售单
                if (req.getPrintSaleReceipts()) {
                    type = 15;
                    String tempUrl = ("https://moa.9ji.com/oa/print/Sub_Print_xdC?sub_id=" + subId
                            + "&type=" + type + "&clientNo=" + clientNo);
                    urlList.add(tempUrl);
                }

                //电子面单
                if (req.getPrintElectricalReceipts()) {
                    if (Objects.isNull(wuliuId)) {
                        throw new CustomizeException("wuliuId不能为空");
                    }
                    LogisticsExpressTypeEnum deliveryCompanyEnums = LogisticsExpressTypeEnum.getLogisticsExpressTypeEnum(deliveryCompanyCode);
                    if (Objects.isNull(deliveryCompanyEnums)) {
                        throw new CustomizeException("找不到该物流类型:" + deliveryCompanyCode);
                    }
                    switch (deliveryCompanyEnums) {
                        case EMS:
                            type = 43;
                            break;
                        case SHUN_FENG_LAAS:
                            type = 13;
                            break;
                        case JING_DONG_9JI:
                            type = 44;
                            break;
                        case ZHONG_TONG:
                            type = 57;
                            break;
                        default:
                            break;
                    }
                    String tempUrl = ("https://moa.9ji.com/oa/Print/doPrint?printid=" + subId
                            + "|" + wuliuId + "&type=" + type + "&ClientNo=" + clientNo);
                    urlList.add(tempUrl);
                }
                try {
                    Map<String, String> headMap = new HashMap<>();
                    headMap.put("Authorization", currentRequestComponent.getCurrentToken());
                    headMap.put(MultitenancyInterceptor.IS_REQUEST_FROM_PAD, Convert.toStr(Convert.toInt(MyDynamicRoutingDataSource.isPda())));
                    for (String httpUrl : urlList) {
                        String result = HttpClientUtils.get(httpUrl, headMap);
                        log.info("打印数据" + httpUrl + "___" + result);
                    }
                } catch (Exception e) {
                    log.error("打印机通知失败，参数：{}", StringUtils.join(urlList, StringPool.COMMA));
                    throw new CustomizeException(String.format("打印机通知失败，Exception：%s, 参数：%s,", e.getMessage(), StringUtils.join(urlList, StringPool.COMMA)));
                }
            }
        }
    }

    public boolean printWithPush(String urlSuffix){
        R<String> tooR = sysConfigClient.getValueByCode(SysConfigConstant.PUSH_ADDRESS);
        String url = "";
        if (tooR.getCode() == ResultCode.SUCCESS && StrUtil.isNotEmpty(tooR.getData())) {
            url = tooR.getData();
        }
        if (StrUtil.isEmpty(url)) {
            return false;
        }
        String evidenceUrl =url + urlSuffix;
        HttpResponse evidenceResult = HttpUtil.createGet(evidenceUrl).execute();
        if(evidenceResult.isOk()){
            log.warn("调用打印接口传入参数：{}，返回结果：{}",evidenceUrl,evidenceResult.body());
            return Optional.ofNullable(JSON.parseObject(evidenceResult.body())).map(jsonMap->jsonMap.getString("type"))
                    .filter("ok"::equals).isPresent();
        } else {
            log.warn("调用依据查询生成接口异常传入参数：{}",evidenceUrl);
            throw new CustomizeException("调用依据查询生成接口异常");
        }
    }




    @Override
    public void scanPickNahuo(OrderOutStockDetailReq req) {
        Integer subtypeId = req.getSubtypeId();
        OrderOutStockTypeEnum orderOutStockSearchEnum = OrderOutStockTypeEnum.getEnumByCode(subtypeId);
        if (Objects.isNull(orderOutStockSearchEnum)) {
            throw new CustomizeException("找不到单据类型：" + req.getSubtype());
        }
        OaUserBO currentStaffId = currentRequestComponent.getCurrentStaffId();
        if(OrderOutStockTypeEnum.SMALLPRO.getCode().equals(subtypeId)){
            //小件单逻辑处理
            Integer id = Optional.ofNullable(req.getSmallproBillId()).orElseThrow(() -> new CustomizeException("小件扫码取件id不能为空"));
            SmallproBill smallproBill = new SmallproBill();
            smallproBill.setId(id);
            smallproBill.setScanFlag(Boolean.TRUE);
            smallproBill.setScanTime(LocalDateTime.now());
            smallproBill.setScanner(currentStaffId.getUserName());
            boolean b = stockMapper.updateScanFlag(smallproBill);
            if(!b){
                throw new CustomizeException("扫码取件失败");
            }

        } else {
            String key = SCAN_PICK_NAHUO
                    + ":" + orderOutStockSearchEnum.getCode() + ":" + req.getSubId();
            RLock lock = redissonClient.getLock(key);
            try {
                boolean isLock = lock.tryLock(0, LOCK_MILLISECONDS, TimeUnit.MILLISECONDS);
                if (!isLock) {
                    throw new CustomizeException("已收到,正在处理中");
                }

                Sub sub = subService.getSub(req.getSubId());
                if (Objects.isNull(sub)) {
                    throw new CustomizeException("找不到该订单:" + req.getSubId());
                }
                Integer basketId = req.getBasketId();
                Basket basket = basketService.getById(basketId);
                if (Objects.isNull(basket)) {
                    throw new CustomizeException("找不到该订单明细:" + basketId);
                }
                Integer areaid = sub.getKcareaid();

                if (Objects.isNull(areaid)) {
                    areaid = sub.getAreaid();
                }
                if (!Objects.equals(req.getAreaId(), areaid)) {
                    throw new CustomizeException("扫码所在门店与订单绑定门店不一致:" + req.getAreaId());
                }
                Long ppriceid = basket.getPpriceid();
                ProductInfoEntity productInfo = productInfoService.getByPpid(ppriceid);
                if (productInfo.getMobile()) {
                    if (Objects.isNull(req.getMkcid())) {
                        throw new CustomizeException("扫码mkcId不能为空");
                    }
                    List<ProductMkc> mkc = productMkcService.lambdaQuery()
                            .eq(ProductMkc::getBasketId, basket.getBasketId())
                            .list();
                    if (CollectionUtils.isEmpty(mkc)) {
                        throw new CustomizeException("找不到订单明细绑定的mkcId:" + req.getMkcid());
                    }
                    Map<Integer, ProductMkc> productMkcMap = mkc.stream()
                            .collect(Collectors.toMap(ProductMkc::getId, Function.identity(), (v1, v2) -> v1));
                    Set<Integer> productMkcIdList = productMkcMap.keySet();
                    if (!productMkcIdList.contains(req.getMkcid())) {
                        throw new CustomizeException("大件扫码mkcId与订单绑定mkcId不一致:" + req.getMkcid());
                    }
                    ProductMkc temp = productMkcMap.get(req.getMkcid());
                    if (!KcCheckEnum.K_C.getCode().equals(temp.getKcCheck())) {
                        throw new CustomizeException("库存状态才可以取件:" + temp.getId());
                    }
                } else {
                    if (!Objects.equals(basket.getBasketCount(), req.getCount())) {
                        throw new CustomizeException("小件扫描数量与订单明细数量不一致：" + req.getCount());
                    }
                    CheckSubPeijianBeiResVO checkSubPeijianBeiResVO = this.checkSubPeijianBei(areaid, basketId, ppriceid);
                    if (Objects.isNull(checkSubPeijianBeiResVO) || !"库存".equals(checkSubPeijianBeiResVO.getKcCheckName())) {
                        throw new CustomizeException("库存状态才可以取件:" + ppriceid);
                    }
                }
                //加入拿货队列
                Nahuoduilie nahuoduilie = nahuoduilieService.lambdaQuery().eq(Nahuoduilie::getBasketId, basketId).list()
                        .stream().findFirst().orElse(null);
                if (Objects.isNull(nahuoduilie)) {
                    this.upNahuo(basketId, sub.getSubId());
                    DynamicDataSourceContextHolder.push(DataSourceConstants.OANEW_WRITE);
                    nahuoduilie = nahuoduilieService.lambdaQuery().eq(Nahuoduilie::getBasketId, basketId).list()
                            .stream().findFirst().orElse(null);
                    DynamicDataSourceContextHolder.clear();
                    if (Objects.isNull(nahuoduilie)) {
                        throw new CustomizeException("拿货队列加入失败" + basketId);
                    }
                }
                Integer userId = currentStaffId.getUserId();
                Ch999User user = ch999UserService.getUserByCh999Id(userId);
                this.nahuoUrl(nahuoduilie.getId(), req.getMkcid(), req.getAreaId(), userId, user.getPwd());
                SubLogsNewReq temp = new SubLogsNewReq();
                temp.setSubId(basketId);
                temp.setType(2);
                temp.setShowType(false);
                temp.setComment("订单扫码取件拿货成功");
                temp.setDTime(LocalDateTime.now());
                temp.setInUser(currentStaffId.getUserName());
                subLogService.saveSubLog(temp);

                if (productInfo.getMobile()) {
                    MkcLogNewReq mkcLogNewReq = new MkcLogNewReq();
                    mkcLogNewReq.setComment("订单扫码取件拿货成功")
                            .setDTime(LocalDateTime.now())
                            .setInUser(currentStaffId.getUserName())
                            .setShowType(false)
                            .setMkcId(Convert.toLong(req.getMkcid()));
                    mkcLogNewService.insertMkcLog(mkcLogNewReq);
                }
                //校验是否已经全部扫码取件
                ScanPickStockDetailRes stockDetail = SpringUtil.getBean(IStockService.class)
                        .getScanPickStockDetail(OrderOutStockDetailReq.builder().subId(req.getSubId())
                                .subtype(req.getSubtype()).subtypeId(req.getSubtypeId()).build());
                if(CollUtil.isEmpty(stockDetail.getPendingList())){
                    Areainfo areaInfo = areaInfoService.getAreaInfo(Convert.toStr(sub.getAreaid()));
                    //发送取货完成到订单动态
                    SpringUtil.getBean(JournalPush.class).orderLogPush(new MqInfo()
                            .setXtenant(Convert.toLong(XtenantEnum.getXtenant()))
                            .setData(new MqInfoData().setSubId(Convert.toLong(req.getSubId())).setSubType(req.getSubtypeId())
                                    .setNode(SubDynamicsNodeEnum.IN_TRANSIT.getCode())
                                    .setMessage(StrUtil.format("【{} {}】取货完成", areaInfo.getCityName(), areaInfo.getAreaName()))
                                    .setBusinessNode(SubDynamicsBusinessNodeEnum.ORDER_STOCK_OUT.getCode())
                            ));
                    //更新订单物流单状态
                    changeWuliuStatus(req);
                }
            }catch (CustomizeException e) {
                log.warn("调用失败业务异常！", e);
                throw e;
            }catch (Exception e) {
                log.warn("调用失败异常！", e);
                throw new CustomizeException(e.getMessage());
            } finally {
                if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        }


    }

    @Override
    public Boolean scanPickOutStockFlag(OrderOutStockDetailReq req) {
        ScanPickStockDetailRes detail = Optional.ofNullable(this.getScanPickStockDetail(req)).orElse(new ScanPickStockDetailRes());
        List<ScanPickStockDetailRes.Sku> pendingList = detail.getPendingList();
        if(!CollectionUtils.isEmpty(pendingList)){
            List<ScanPickStockDetailRes.Sku> pendingStockList = pendingList.stream()
                    .filter(x -> "库存".equals(x.getKcCheck())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(pendingStockList)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public StockAmountBatchResVO getStockAmountByPpidBatch(StockAmountBatchReqVO req) {
        StockAmountBatchResVO result = new StockAmountBatchResVO();
        List<StockAmountResVO> stockAmountList = new ArrayList<>();
        result.setStockAmountList(stockAmountList);
        List<Integer> ppidList = req.getPpidList();
        List<ProductInfoEntity> productInfoList = productInfoService.getProductByPpidList(ppidList);
        List<Long> mobilePpid = productInfoList.stream().filter(ProductInfoEntity::getMobile)
                .map(ProductInfoEntity::getPpid).collect(Collectors.toList());
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(mobilePpid)) {
            List<StockAmountResVO> stockAmountResVO = stockStatisticsService.getStockAmountByPpidBatch(mobilePpid);
            List<StockAmountResVO> unLockedStockAmountResVO = stockStatisticsService.getUnLockedStockAmountByPpidBatch(mobilePpid);
            Map<Integer, StockAmountResVO> ppidToMap = unLockedStockAmountResVO.stream()
                    .collect(Collectors.toMap(StockAmountResVO::getPpid, Function.identity(), (v1, v2) -> v2));
            for (StockAmountResVO s : stockAmountResVO) {
                StockAmountResVO temp = ppidToMap.get(s.getPpid());
                if (Objects.nonNull(temp) && Objects.nonNull(temp.getTotalAmount())) {
                    int totalAmount = s.getTotalAmount() - temp.getTotalAmount();
                    s.setTotalAmount(totalAmount);
                }
            }
            stockAmountList.addAll(stockAmountResVO);
        }
        List<Long> accessorsPpid = productInfoList.stream().filter(productInfoEntity -> !productInfoEntity.getMobile())
                .map(ProductInfoEntity::getPpid).collect(Collectors.toList());
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(accessorsPpid)) {
            List<StockAmountResVO> stockAmountResVO = accessoryStockStatisticsService.getStockAmountByPpidBatch(accessorsPpid);
            List<StockAmountResVO> unLockedStockAmountResVO = accessoryStockStatisticsService.getUnLockedStockAmountByPpidBatch(accessorsPpid);
            Map<Integer, StockAmountResVO> ppidToMap = unLockedStockAmountResVO.stream()
                    .collect(Collectors.toMap(StockAmountResVO::getPpid, Function.identity(), (v1, v2) -> v2));
            for (StockAmountResVO s : stockAmountResVO) {
                StockAmountResVO temp = ppidToMap.get(s.getPpid());
                if (Objects.nonNull(temp) && Objects.nonNull(temp.getTotalAmount())) {
                    int totalAmount = s.getTotalAmount() - temp.getTotalAmount();
                    s.setTotalAmount(totalAmount);
                }
            }
            stockAmountList.addAll(stockAmountResVO);
        }
        List<Integer> resultPpidList = stockAmountList.stream().map(StockAmountResVO::getPpid).collect(Collectors.toList());
        ppidList.forEach(x -> {
            if (!resultPpidList.contains(x)) {
                StockAmountResVO temp = new StockAmountResVO();
                temp.setTotalAmount(0);
                temp.setPpid(x);
                stockAmountList.add(temp);
            }
        });
        return result;
    }


    /**
     * 调用C#端加入拿货队列
     **/
    private Boolean upNahuo(Integer basketId, Integer subId) {
        R<String> resultSys = sysConfigClient.getValueByCode(SysConfigConstant.MOA_URL);
        if(!resultSys.isSuccess()){
            throw new CustomizeException("扫码取件调用C#端加入拿货队列获取域名异常");
        }

        String url = resultSys.getData() +UP_NAHUO_URL + "?&basket_id=" + basketId + "&sub_id=" + 888 + "&isOA=true&_=" + System.currentTimeMillis();
        Boolean result = false;
        String respJson = null;
//        Map<String, String> headMap = new HashMap<>();
//        headMap.put("Authorization", currentRequestComponent.getCurrentToken());
//        headMap.put(MultitenancyInterceptor.IS_REQUEST_FROM_PAD, Convert.toStr(Convert.toInt(MyDynamicRoutingDataSource.isPda())));
        HttpRequest get = HttpUtil.createGet(url)
                .header("Authorization",currentRequestComponent.getCurrentToken())
                .header(MultitenancyInterceptor.IS_REQUEST_FROM_PAD, Convert.toStr(Convert.toInt(MyDynamicRoutingDataSource.isPda())));
        respJson = get.execute().body();
     //   respJson = HttpClientUtils.get(url, headMap);
        log.info("url:" + url + ",加入拿货队列返回数据：{}", respJson);
        if (StringUtils.isBlank(respJson)) {
            return result;
        }
        com.alibaba.fastjson.JSONObject jsonObject = JSON.parseObject(respJson);
        String code = jsonObject.getString("code");
        if (StringUtils.isEmpty(code) || Integer.parseInt(code) != 0) {
            String msg = Optional.ofNullable(jsonObject.getString("msg"))
                    .orElse(jsonObject.getString("userMsg"));
            throw new InventoryException(msg);
        }
        return true;

    }

    /**
     * 调用C#端拿货
     **/
    private Boolean nahuoUrl(Integer nid, Integer mkc_id, Integer area, Integer ch999_id, String pwd) {
        R<String> resultSys = sysConfigClient.getValueByCode(SysConfigConstant.OA_WCF_HOST);
        if(!resultSys.isSuccess()){
            throw new CustomizeException("扫码取件获取域名异常");
        }
        String url = resultSys.getData()+ NAHUO_URL + "?&nid=" + nid + "&mkc_id=" + mkc_id
                + "&area=" + area + "&ch999_id=" + ch999_id + "&pwd=" + pwd;
        Boolean result = false;
        String respJson = null;
//        Map<String, String> headMap = new HashMap<>();
//        headMap.put("Authorization", currentRequestComponent.getCurrentToken());
//        headMap.put(MultitenancyInterceptor.IS_REQUEST_FROM_PAD, Convert.toStr(Convert.toInt(MyDynamicRoutingDataSource.isPda())));
        HttpRequest get = HttpUtil.createGet(url)
                .header("Authorization",currentRequestComponent.getCurrentToken())
                .header(MultitenancyInterceptor.IS_REQUEST_FROM_PAD, Convert.toStr(Convert.toInt(MyDynamicRoutingDataSource.isPda())));
        respJson = get.execute().body();
       // respJson = HttpClientUtils.get(url, headMap);
        log.info("url:" + url + ",拿货返回数据：{}", respJson);
        if (StringUtils.isBlank(respJson)) {
            return result;
        }
        com.alibaba.fastjson.JSONObject jsonObject = JSON.parseObject(respJson);
        String code = jsonObject.getString("stats");
        if (StringUtils.isEmpty(code) || Integer.parseInt(code) != 1) {
            String msg = jsonObject.getString("result");
            throw new InventoryException(msg);
        }
        return true;
    }


    /**
     * 调用C#端修改订单状态
     **/
    private Boolean subCheckOpByHttpRequest(OrderStateChangeReq req) {
        String subtype = req.getSubtype();
        OrderOutStockTypeEnum orderOutStockSearchEnum = OrderOutStockTypeEnum.getEnumByMessage(subtype);
        if (Objects.isNull(orderOutStockSearchEnum)) {
            throw new CustomizeException("找不到单据类型：" + req.getSubtype());
        }
        String url = "";
        String inwcf = CommonUtils.getResultData(sysConfigClient.getValueByCode(SysConfigConstant.IN_WCF_HOST),
                userMsg -> {
                    throw new CustomizeException(StrUtil.format("获取inWcf地址失败, 原因: {}", userMsg));
                });
        switch (orderOutStockSearchEnum) {
            case SUB:
                url = StrUtil.format(SUB_CHECK_OP_URL, inwcf);
                break;
            case RECOVER_SUB:
                url = StrUtil.format(RECOVER_SUB_CHECK_OP_URL, inwcf);
                break;
        }


        Boolean result = false;
        String respJson = null;

        try {
            String reqText = JSON.toJSONString(req);
            respJson = HttpClientUtil.postRequest(url, reqText);
            if (StringUtils.isBlank(respJson)) {
                return result;
            }
            log.info("url:{},参数: {}, 订单状态返回数据：{}", url, reqText, respJson);
            JSONObject jsonObject = JSON.parseObject(respJson);
            String code = jsonObject.getString("code");
            if (StringUtils.isEmpty(code) || Integer.parseInt(code) != 0) {
                String msg = Optional.ofNullable(jsonObject.getString("msg"))
                        .orElse(jsonObject.getString("userMsg"));
                throw new CustomizeException(msg);
            } else if (Integer.parseInt(code) == 0) {
                result = true;
                return result;
            }
        } catch (Exception e) {
            log.error("订单状态返回数据：{}", respJson);
            log.error("解析订单状态异常：{}", Exceptions.getStackTraceAsString(e));
            throw new CustomizeException(e.getMessage());
        }
        return result;
    }

    /**
     * 调用C#端 小件获取库存状态
     **/
    private CheckSubPeijianBeiResVO checkSubPeijianBei(Integer area, Integer basketId, Long ppriceids) {
        R<String> resultSys = sysConfigClient.getValueByCode(SysConfigConstant.MOA_URL);
        if(!resultSys.isSuccess()){
            throw new CustomizeException("扫码取件获取域名异常");
        }
        String url = resultSys.getData()+CHECK_SUB_PEIJIAN_BEI_URL + "?area=" + area + "&basket_ids=" + basketId + "&ppriceids=" + ppriceids;
        CheckSubPeijianBeiResVO result = null;
        String respJson = null;
//        Map<String, String> headMap = new HashMap<>();
//        headMap.put("Authorization", currentRequestComponent.getCurrentToken());
//        headMap.put(MultitenancyInterceptor.IS_REQUEST_FROM_PAD, Convert.toStr(Convert.toInt(MyDynamicRoutingDataSource.isPda())));
        try {
            HttpRequest get = HttpUtil.createGet(url)
                    .header("Authorization",currentRequestComponent.getCurrentToken())
                    .header(MultitenancyInterceptor.IS_REQUEST_FROM_PAD, Convert.toStr(Convert.toInt(MyDynamicRoutingDataSource.isPda())));
            respJson = get.execute().body();
           // respJson = HttpClientUtils.get(url, headMap);
            log.info("url:" + url + ",小件获取库存状态返回数据：{}", respJson);
            if (StringUtils.isBlank(respJson) || !JSONUtil.isJsonObj(respJson)) {
                return result;
            }
            com.alibaba.fastjson.JSONObject jsonObject = JSON.parseObject(respJson);
            String code = jsonObject.getString("code");
            if (StringUtils.isEmpty(code) || Integer.parseInt(code) != 0) {
                String msg = Optional.ofNullable(jsonObject.getString("msg"))
                        .orElse(jsonObject.getString("userMsg"));
                throw new InventoryException(msg);
            }
            if (null != jsonObject.get("data")) {
                String resultString = jsonObject.get("data").toString();
                List<CheckSubPeijianBeiResVO> resultList = JSONUtil.toList(JSONUtil.toJsonStr(resultString), CheckSubPeijianBeiResVO.class);
                result = resultList.stream().findFirst().orElse(null);
                return result;
            }
            return result;
        } catch (Exception e) {
            log.error("小件获取库存状态返回数据：{}", respJson);
            log.error("解析小件获取库存状态返回数据异常：{}", Exceptions.getStackTraceAsString(e));
            return result;
        }
    }


    private int monthDiff(LocalDateTime dt1, LocalDateTime dt2) {
        //获取第一个时间点的月份
        int month1 = dt1.getMonthValue();
        //获取第一个时间点的年份
        int year1 = dt1.getYear();
        //获取第一个时间点的月份
        int month2 = dt2.getMonthValue();
        //获取第一个时间点的年份
        int year2 = dt2.getYear();
        //返回两个时间点的月数差
        int i = (year2 - year1) * 12 + (month2 - month1);
        return Math.abs(i);
    }

    /**
     * 全部扫码取件完成，物流单状态变更为等待派送
     * @param req
     */
    private boolean changeWuliuStatus(OrderOutStockDetailReq req) {
        WuLiuEntity wuLiu = null;
        WuLiuSubDTO wuLiuSub = null;
        if (OrderOutStockTypeEnum.RECOVER_SUB.getCode().equals(req.getSubtypeId())) {
            wuLiuSub = wuLiuService.getWuLiuReSub(req.getSubId());
            wuLiu = wuLiuService.getWuLiuByWuTypeAndDanhaobind(WuLiuTypeEnum.ORDER.getCode(), req.getSubId());
        } else {
            wuLiuSub = wuLiuService.getWuLiuSub(req.getSubId());
            wuLiu = wuLiuService.getWuLiuByWuTypeAndDanhaobind(WuLiuTypeEnum.ORDER.getCode(), req.getSubId());
            if (Objects.isNull(wuLiu)) {
                wuLiu = wuLiuService.getWuLiuByWuTypeAndDanhaobind(WuLiuTypeEnum.ORDER_EXPRESS.getCode(), req.getSubId());
            }
        }
        if (Objects.nonNull(wuLiuSub) && Objects.nonNull(wuLiu)) {
            //销售单配送方式为九机快送、快递运输、第三方派送
            List<Integer> deliveryList = Arrays.asList(SubDeliveryEnum.JIUJI.getCode(), SubDeliveryEnum.EXPRESS.getCode(), SubDeliveryEnum.THIRD_PARTY.getCode());
            //订单状态 已确认
            //物流单状态 等待取货
            if (SubCheckEnum.ALREADY_CONFIRM.getCode().equals(wuLiuSub.getSubCheck())
                    && deliveryList.contains(Optional.ofNullable(wuLiuSub.getDelivery()).orElse(0))
                    && WuliuStatusEnum.WAITING_GETTING_GOODS.getCode().equals(wuLiu.getStats())) {
                ChangWuliuStatsBO changWuliuStats = new ChangWuliuStatsBO();
                changWuliuStats.setWuliuId(wuLiu.getId());
                changWuliuStats.setOldStats(WuliuStatusEnum.WAITING_GETTING_GOODS.getCode());
                changWuliuStats.setStats(WuliuStatusEnum.WAITING_DELIVERY.getCode());
                return wuLiuBusService.changeWuliuStats(changWuliuStats);
            }
        }
        return true;
    }

    @Override
    @DS("oanewWrite")
    public void addOrUpdateProductConfig(StockUpdateProductConfigVO req) {
        String value = "";
        if (!CollectionUtils.isEmpty(req.getPpids())) {
            for (String ppids : req.getPpids()) {
                if (!ReUtil.isMatch("^[0-9]+(,[0-9]+)*$", ppids)) {
                    throw new CustomizeException(ppids + "存在错误ppid");
                }
            }
            Set<Long> ppidSet = req.getPpids().stream()
                    .flatMap(v -> Arrays.stream(StrUtil.split(v, StrPool.COMMA))
                            .filter(StringUtils::isNotBlank)
                            .map(Convert::toLong)).collect(Collectors.toSet());
            Map<Long, ProductInfoVo> longProductInfoMap = SpringUtil.getBean(IProductInfoService.class).listByppidsNew(new ArrayList<>(ppidSet));
            if (!CollectionUtils.isEmpty(longProductInfoMap)) {
                Map<Long, Long> ppidProductIdMap = longProductInfoMap.values().stream().collect(Collectors.toMap(ProductInfoVo::getPpid, ProductInfoVo::getProductId));
                for (String ppids : req.getPpids()) {
                    List<Long> ppidList = Arrays.stream(StrUtil.split(ppids, StrPool.COMMA))
                            .filter(StringUtils::isNotBlank)
                            .map(Convert::toLong).collect(Collectors.toList());
                    List<Long> productList = ppidList.stream().map(ppidProductIdMap::get).distinct().collect(Collectors.toList());
                    if (productList.size() > 1) {
                        throw new CustomizeException(ppids + "ppid不属于同一商品");
                    }
                }
            }
            value = JSONUtil.toJsonStr(req.getPpids());
        } else {
            value = JSONUtil.toJsonStr(new ArrayList<>());
        }
        String areaIds = req.getAreaIds().stream().map(Convert::toStr).collect(Collectors.joining(","));
        ISysConfigService sysConfigService = SpringUtil.getBean(ISysConfigService.class);
        List<SysConfig> list = sysConfigService.lambdaQuery().eq(SysConfig::getCode, ConfigEnum.ALLOW_UPDATE_PPID.getCode()).list();
        if (CollectionUtils.isEmpty(list)) {
            SysConfig sysConfig = new SysConfig();
            sysConfig.setCode(ConfigEnum.ALLOW_UPDATE_PPID.getCode());
            String dsc = "允许修改ppriceid集合New";
            sysConfig.setDsc(dsc);
            sysConfig.setName(dsc);
            sysConfig.setValue(value);
            sysConfig.setAreaids(areaIds);
            sysConfig.setCode(ConfigEnum.ALLOW_UPDATE_PPID.getCode());
            sysConfig.setXtenant(XtenantEnum.getXtenant().longValue());
            sysConfigService.save(sysConfig);
        } else {
            SysConfig sysConfig = list.get(0);
            boolean updateFlag = sysConfigService.lambdaUpdate()
                    .set(SysConfig::getValue, value)
                    .set(SysConfig::getAreaids, areaIds)
                    .eq(SysConfig::getCode, ConfigEnum.ALLOW_UPDATE_PPID.getCode())
                    .eq(SysConfig::getId, sysConfig.getId())
                    .update();
            if (updateFlag) {
                SysconfigLog sysConfigLog = new SysconfigLog().setCode(ConfigEnum.ALLOW_UPDATE_PPID.getCode())
                        .setOperationType(OperationTypeEnum.SYSCONFIG_UPDATE.getCode());
                sysConfigLog.setConfigId(sysConfig.getId());
                String comment = "更新配置：" + ConfigEnum.getMessage(sysConfig.getCode())
                        + "，areaids:" + sysConfig.getAreaids() + "改为"
                        + areaIds + "，value:" + sysConfig.getValue() + "改为" + value;
                sysConfigLog.setComment(comment);
                sysConfigLog.setInUserId(SysUtils.getUser().getUserId());
                sysConfigLog.setInUserName(SysUtils.getUser().getUserName());
                SpringUtil.getBean(SysconfigLogService.class).save(sysConfigLog);
            }
        }
    }

    @Override
    public StockProductConfigVO getProductConfig() {
        List<SysConfig> list = SpringUtil.getBean(ISysConfigService.class).lambdaQuery().eq(SysConfig::getCode, ConfigEnum.ALLOW_UPDATE_PPID.getCode()).list();
        return list.stream().map(v -> LambdaBuild.create(StockProductConfigVO.class)
                .set(StockProductConfigVO::setAreaIds, Arrays.stream(StrUtil.split(v.getAreaids(), ",")).collect(Collectors.toList()))
                .set(StockProductConfigVO::setPpids, JSONUtil.toList(v.getValue(), String.class))
                .build()).findFirst().orElse(new StockProductConfigVO());
    }
}
