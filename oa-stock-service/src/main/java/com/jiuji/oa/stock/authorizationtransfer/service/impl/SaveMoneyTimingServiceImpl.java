package com.jiuji.oa.stock.authorizationtransfer.service.impl;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.stock.authorizationtransfer.entity.SaveMoneyTiming;
import com.jiuji.oa.stock.authorizationtransfer.mapper.SaveMoneyTimingMapper;
import com.jiuji.oa.stock.authorizationtransfer.service.SaveMoneyTimingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@DS("oanewWrite")
public class SaveMoneyTimingServiceImpl extends ServiceImpl<SaveMoneyTimingMapper, SaveMoneyTiming> implements SaveMoneyTimingService {
}
