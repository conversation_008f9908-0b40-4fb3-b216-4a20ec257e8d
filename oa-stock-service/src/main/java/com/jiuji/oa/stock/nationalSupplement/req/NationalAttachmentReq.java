package com.jiuji.oa.stock.nationalSupplement.req;

import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.experimental.Accessors;
import net.sf.cglib.core.Local;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Accessors(chain = true)
public class NationalAttachmentReq {

    /**
     * 当前页
     */
    private Long current;
    /**
     * 每页条数
     */
    private Long size;

    /**
     * 审核状态
     */
    private Integer checkState;

    /**
     * 地区
     */
    private List<Integer> areaIdList;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * @see SelectTypeTimeEnum
     * 时间类型
     */
    private Integer selectTimeType;

    /**
     * 分类id
     */
    private List<Integer> cidList;

    /**
     * 品牌id
     */
    private List<Integer> brandIdList;
    /**
     * 国补收银方式
     */
    private List<Integer> nationalAttachmentCashier;
    /**
     * 线上国补收银方式
     */
    private List<Integer> onlineNationalAttachmentCashier;



    /**
     * 查询类型
     * @see com.jiuji.oa.stock.nationalSupplement.enums.NationalSupplementSelectEnum
     */
    private Integer selectType;

    private String selectValue;



    /**
     * 财务 审核状态
     * @see FinanceCheckStateEnum
     */
    private Integer financeCheckState;


    /**
     * 申报状态
     */
    private Integer status;

    /**
     * 运营审核状态
     * @see com.jiuji.oa.wuliu.enums.OperationStateEnum
     */
    private Integer operationCheckState;


    /**
     * 是否导出
     */
    private Boolean isExport;

    /**
     * 国补类型
     */
    private Integer nationalSupplementKind;

}
