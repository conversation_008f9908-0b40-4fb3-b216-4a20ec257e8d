package com.jiuji.oa.stock.nationalSupplement.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum SelectTypeTimeEnum implements CodeMessageEnumInterface {
    TRADEDATE1(1, "交易完成时间"),
    CHECK_TIME(2, "审核时间"),
    OPERATION_TIME(3, "运营复核时间"),
    SUB_DATE(4, "加单时间");

    /**
     * 编码
     */
    private Integer code;
    /**
     * 名称
     */
    private String message;

}
