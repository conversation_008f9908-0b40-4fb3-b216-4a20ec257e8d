package com.jiuji.oa.stock.nationalSupplement.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jiuji.oa.stock.nationalSupplement.req.*;
import com.jiuji.oa.stock.nationalSupplement.res.*;
import com.jiuji.tc.common.vo.R;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

public interface NationalSupplementService {


    /**
     * 重新申报
     * @param reDeclareReq
     * @return
     */
    ReDeclareJiuJiRes reDeclare(ReDeclareJiuJiReq reDeclareReq);
    /**
     * id 查询列表信息
     * @param id
     * @return
     */
    NationalSupplementAttachmentRes selectNationalById(Integer id);
    /**
     * 查询申报不通过
     * @param declarationNotApprovedReq
     * @return
     */
    List<DeclarationNotApprovedRes> selectDeclarationNotApproved(DeclarationNotApprovedReq declarationNotApprovedReq);
    /**
     * 推送申报不通过
     * @return
     */
    List<DeclarationNotApprovedRes> sendDeclarationNotApproved();
    /**
     * 获取自动申报数据
     * @param declarationDataReq
     * @return
     */
    List<Integer> selectDeclarationData(DeclarationDataReq declarationDataReq);
    /**
     * 审核状态修改
     * @param approveReq
     * @return
     */

    ApproveRes updateNationalSupplementState(ApproveReq approveReq);

    /**
     * 附件上传
     * @param attachmentsReq
     * @return
     */
    ApproveRes uploadAttachments(UploadAttachmentsReq attachmentsReq);


    /**
     * 分页查询国补附件列表
     * @param req
     * @return
     */
    IPage<NationalSupplementAttachmentRes> selectNationalSupplementState(NationalAttachmentReq req);


    /**
     * 国补聚合数据查询
     * @param req
     * @return
     */
    NationalAttachmentCount selectNationalSupplementSum(NationalAttachmentReq req);

    /**
     * 财务复合
     * @param req
     * @return
     */
    FinancialReviewRes financialReview(FinancialReviewReq req);


    /**
     * 运营复核
     * @param req
     * @return
     */
    OperationReviewRes operationReview(OperationReviewReq req);


    /**
     * 申报回调
     * @param req
     * @return
     */
    DeclarationCallbackRes declarationCallback(DeclarationCallbackReq req);

    /**
     * 系统审核
     * @param sysUpdateReq
     * @return
     */
    String sysUpdate(SysUpdateReq sysUpdateReq);


    /**
     * 附件审核通过+财务审核通过+申报状态为提交失败， 则更新申报状态为未提交
     * @param id
     */
    void autoUpdateState(AutoUpdateStateReq autoUpdateStateReq);

    /**
     * 查询订单是否线上国补订单
     */
    List<NationalSupplementKindRes> getNationalSupplementKindList(List<String> subIds);
    
    /**
     * 根据退货日期查询订单信息
     * @param req 包含开始时间和结束时间
     * @return 订单号和退货日期列表
     */
    List<SubReturnDateRes> querySubsByReturnDate(SubReturnDateReq req);
}
