package com.jiuji.oa.stock.nationalSupplement.req;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotNull;

@Data
public class OperationReviewReq {
    /**
     * subFlagRecord 表的id
     */
    @NotNull(message = "复核id不能为空")
    private Integer id;

    /**
     * @see com.jiuji.oa.wuliu.enums.OperationStateEnum
     */
    @NotNull(message = "运营复核状态不能为空")
    private Integer operationState;

    /**
     * 审核内容
     */
    @Max(value = 205, message = "审核内容不能超过200个字符")
    private String checkComment;
}
