package com.jiuji.oa.stock.displayproductinfo.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 大件库存操作表,责任小组：物流
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mkc_dellogs")
public class MkcDelLogEntity extends Model<MkcDelLogEntity> {

    private static final long serialVersionUID= -1725738065774141055L;

    /**
     * zf 库存机器作废  h1 售后转入申请 h2 售后转出申请   gh 换机库存 ,  ,h3 售后转出机器  ,h4  回收转新机 ,h5 转售后机器  ,h6 回收转租机  ,km   ,xc 转瑕疵  ,yj 转样机 ,zx  转滞销老品 , xcz 瑕疵机转出
     */
    private String kinds;

    private String check2user;

    private LocalDateTime dtime;

    private String check1user;

    private Integer ppriceid;

    private Boolean check1;

    private Integer mkcId;

    private String frareaid;

    private Boolean check2;

    private String comment;

    private BigDecimal price1;

    private Integer areaid;

    @TableField("youhuiPrice")
    private Double youhuiPrice;

    private LocalDateTime check1dtime;

    private String inuser;

    private LocalDateTime check2dtime;

    private String area;

    private BigDecimal price2;

    private Integer pzid;

    @TableField("lpToAreaId")
    private Integer lpToAreaId;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
