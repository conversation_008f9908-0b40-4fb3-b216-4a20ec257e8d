package com.jiuji.oa.stock.nationalSupplement.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.jiuji.cloud.stock.vo.request.SelectAgencyReq;
import com.jiuji.cloud.stock.vo.response.SelectAgencyRes;
import com.jiuji.oa.nc.common.exception.CustomizeException;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.oa.stock.nationalSupplement.req.DeclarationDataSaasReq;
import com.jiuji.oa.stock.nationalSupplement.service.AutomaticDeclarationSaasService;
import com.jiuji.oa.wuliu.enums.SubFlagRecordStateEnum;
import com.jiuji.oa.wuliu.mapper.WuLiuSubFlagRecordMapper;
import com.jiuji.oa.wuliu.service.IWuLiuSubFlagRecordService;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;


@Slf4j
@Service
@DS("oanewWrite")
public class AutomaticDeclarationServiceSaasImpl implements AutomaticDeclarationSaasService {
    @Resource
    private IWuLiuSubFlagRecordService subFlagRecordService;
    @Resource
    private WuLiuSubFlagRecordMapper wuLiuSubFlagRecordMapper;
    @Resource
    private SysConfigClient sysConfigClient;


    @Override
    public List<Integer> selectDeclarationDataSaas(DeclarationDataSaasReq dataSaasReq) {
        return wuLiuSubFlagRecordMapper.selectDeclarationDataSaas(dataSaasReq);
    }

    @Override
    @DS("ch999oanew")
    public SelectAgencyRes selectAgency(SelectAgencyReq selectAgencyReq) {
        Integer count = Optional.ofNullable(wuLiuSubFlagRecordMapper.selectAgencyCount(selectAgencyReq)).orElse(NumberConstant.ZERO) ;
        if(count>NumberConstant.ZERO){
            SelectAgencyRes selectAgencyRes = new SelectAgencyRes();
            selectAgencyRes.setText(String.format("您目前有[%s个]国补订单待修改附件", count));
            String moaHost = Optional.of(sysConfigClient.getValueByCode(SysConfigConstant.MOA_URL))
                    .filter(t -> t.getCode() == ResultCode.SUCCESS)
                    .map(R::getData)
                    .orElseThrow(() -> new CustomizeException("获取M端域名出错"));
            String userName = selectAgencyReq.getUserName();
            selectAgencyRes.setUrl(String.format("%s/new/#/market/order/list?searchKind=trade&key=%s&gbCheckstate=%s&areaIds=0&subCheck=3&skipValidation=1",moaHost,userName, SubFlagRecordStateEnum.REJECTED.getCode()));
            return selectAgencyRes;
        }
        return null;
    }

    @Override
    public Integer getTenantId(DeclarationDataSaasReq dataSaasReq) {
        return wuLiuSubFlagRecordMapper.getTenantId(dataSaasReq);
    }
}
