package com.jiuji.oa.stock.authorizationtransfer.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.stock.authorizationtransfer.entity.CrossAuthTransferConfig;
import com.jiuji.oa.stock.authorizationtransfer.mapper.CrossAuthTransferConfigMapper;
import com.jiuji.oa.stock.authorizationtransfer.service.CrossAuthTransferConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 */
@Slf4j
@Service
@DS("oanewWrite")
public class CrossAuthTransferConfigServiceImpl extends ServiceImpl<CrossAuthTransferConfigMapper, CrossAuthTransferConfig> implements CrossAuthTransferConfigService {


}
