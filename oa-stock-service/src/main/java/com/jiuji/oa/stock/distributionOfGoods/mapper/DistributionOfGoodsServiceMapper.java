package com.jiuji.oa.stock.distributionOfGoods.mapper;

import com.jiuji.oa.stock.distributionOfGoods.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface DistributionOfGoodsServiceMapper {


    /**
     * 查询商品信息
     * @param searchCriteriaVO
     * @return
     */
    List<ProductDetailVO> selectReportFormProduct(@Param(value = "searchCriteriaVO") SearchCriteriaVO searchCriteriaVO);


    /**
     * 查询商品库存
     * @param searchStockDetailVO
     * @return
     */
    List<StockDetailVO> selectReportFormStock(@Param(value = "searchStockDetailVO") SearchStockDetailVO searchStockDetailVO);

    /**
     * 库存查询
     * @param searchStockDetailVO
     * @return
     */
    List<StockDetailVO> selectReportFormStockV2(@Param(value = "searchStockDetailVO") SearchStockDetailVO searchStockDetailVO);

    /**
     * 上坪地区销售数量
     * @param searchStoreSales
     * @return
     */
    List<StoreSales> selectStoreSales (@Param(value = "SearchStoreSales") SearchStoreSales searchStoreSales);

    List<StoreSalesV2> selectStoreSalesV2 (@Param(value = "SearchStoreSales") SearchStoreSales searchStoreSales);


    /**
     * 上坪地区销售数量
     * @param areaIds
     * @return
     */
    List<StoreSales> selectStoreSalesNoAreaIds (@Param(value = "areaIds") List<Integer> areaIds,@Param(value = "kind1List")List<Integer>kind1List, @Param("attributeList") List<Integer> attributeList);

    /**
     * 门店查询
     * @param areaIds
     * @param kind1List
     * @return
     */
    List<StoreSales> selectStoreSalesAreaIds (@Param(value = "areaIds") List<Integer> areaIds,@Param(value = "kind1List")List<Integer>kind1List,@Param("attributeList") List<Integer> attributeList);

    /**
     * 查询下拉库存数据
     * @param dropDownDetailVO
     * @return
     */
    List<StoreSales> selectDropDownStock(@Param(value = "dropDownDetailVO") DropDownDetailVO dropDownDetailVO);

    /**
     * 查询现货率的相关数据
     * @param searchGoodsInStockVO
     * @return
     */
    List<GoodsInStockVO> selectGoodsInStock(@Param(value = "searchGoodsInStockVO")SearchGoodsInStockVO searchGoodsInStockVO);

    /**
     * 查询门店信息
     * @param codeList
     * @return
     */
    List<AreaInfoVO>selectAreaInfo(@Param(value = "codeList")List<Integer> codeList);
}
