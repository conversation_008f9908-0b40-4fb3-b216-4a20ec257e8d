package com.jiuji.oa.stock.authorizationtransfer.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.cloud.oaapi.service.UserInfoCloud;
import com.jiuji.cloud.oaapi.vo.request.UserModelCloudVO;
import com.jiuji.cloud.oaapi.vo.response.UserRegResultCloudVO;
import com.jiuji.oa.nc.channel.entity.ChannelKindLink;
import com.jiuji.oa.nc.channel.enums.*;
import com.jiuji.oa.nc.channel.service.Ok3wQudaoService;
import com.jiuji.oa.nc.channel.vo.dto.ContactsDto;
import com.jiuji.oa.nc.channel.vo.dto.FinanceDto;
import com.jiuji.oa.nc.channel.vo.res.ChannelVo;
import com.jiuji.oa.nc.common.bo.OaUserBO;
import com.jiuji.oa.nc.common.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.nc.common.exception.CustomizeException;
import com.jiuji.oa.nc.common.exception.RRExceptionHandler;
import com.jiuji.oa.nc.common.req.OaAttachmentsReq;
import com.jiuji.oa.nc.dict.service.ISysConfigService;
import com.jiuji.oa.nc.oaapp.po.SysConfig;
import com.jiuji.oa.nc.stock.service.ISmsService;
import com.jiuji.oa.nc.user.po.Areainfo;
import com.jiuji.oa.nc.user.po.Authorize;
import com.jiuji.oa.nc.user.po.Ch999Ranks;
import com.jiuji.oa.nc.user.service.AuthorizeService;
import com.jiuji.oa.nc.user.service.IAreaInfoService;
import com.jiuji.oa.nc.user.service.ICh999RankService;
import com.jiuji.oa.stock.authorizationtransfer.entity.CrossAuthTransferConfig;
import com.jiuji.oa.stock.authorizationtransfer.entity.CrossAuthTransferConfigExtend;
import com.jiuji.oa.stock.authorizationtransfer.entity.SaveMoneyTiming;
import com.jiuji.oa.stock.authorizationtransfer.enums.SubPageReqEnums;
import com.jiuji.oa.stock.authorizationtransfer.enums.TransferStateEnums;
import com.jiuji.oa.stock.authorizationtransfer.mapper.CrossAuthTransferSubMapper;
import com.jiuji.oa.stock.authorizationtransfer.service.AuthorizationTransferService;
import com.jiuji.oa.stock.authorizationtransfer.service.CrossAuthTransferConfigService;
import com.jiuji.oa.stock.authorizationtransfer.service.CrossAuthTransferDetailService;
import com.jiuji.oa.stock.authorizationtransfer.service.SaveMoneyTimingService;
import com.jiuji.oa.stock.authorizationtransfer.vo.*;
import com.jiuji.oa.wuliu.entity.WuLiuZiTiDianEntity;
import com.jiuji.oa.wuliu.service.IWuLiuZiTiDianService;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.constants.NumberConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

@Slf4j
@Service
@DS("oanewWrite")
public class AuthorizationTransferServiceImpl implements AuthorizationTransferService {

    @Resource
    private ISysConfigService sysConfigService;
    @Resource
    private AuthorizeService authorizeService;
    @Resource
    private CrossAuthTransferConfigService authTransferConfigService;
    @Resource
    private AbstractCurrentRequestComponent currentRequestComponent;
    @Resource
    private Ok3wQudaoService ok3wQudaoService;
    @Resource
    private IWuLiuZiTiDianService wuLiuZiTiDianService;
    @Resource
    private UserInfoCloud userInfoCloud;
    @Resource
    private IAreaInfoService areaInfoService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private SaveMoneyTimingService saveMoneyTimingService;
    @Resource
    private CrossAuthTransferSubMapper crossAuthTransferSubMapper;
    @Resource
    private CrossAuthTransferDetailService detailService;
    @Resource
    private ICh999RankService ch999RankService;

    /**
     * 默认财务类型
     */
    private final static Integer INSOURCE_ID =2;
    /**
     * 账期天数
     */
    private final static Integer TIMING_DAY =365;
    /**
     * 小件科目
     */
    private final static String SUBJECT_SMALL ="220202";
    /**
     * 大件科目
     */
    private final static String SUBJECT_BIG ="220201";
    /**
     * 维修配件科目
     */
    private final static String SUBJECT_MAINTENANCE ="220203";
    /**
     * 客户类型
     * 1-批发
     * 2-政企
     */
    private final static Integer CUSTOMER_KINDS =1;

    public final static List<Integer> LIST_STATE = Arrays.asList(TransferStateEnums.SUBMITTED.getCode(), TransferStateEnums.AUDITED.getCode(), TransferStateEnums.CANCELED.getCode());

    private final static BigDecimal ER_DU = new BigDecimal("200000.00");


    private final static String MOBILE ="mobile:";


    /**
     * 修改校验
     * @return
     */
    private void updateCheck(UpdateConfigVO updateConfigVO){
        SysConfig sysConfig = Optional.ofNullable(sysConfigService.lambdaQuery().eq(SysConfig::getId, updateConfigVO.getId()).one())
                .orElseThrow(()->new CustomizeException("跨授权调拨配置查询为空"));
        String value = Optional.ofNullable(sysConfig.getValue()).orElse(NumberConstant.ZERO.toString());
        if(value.equals(NumberConstant.ONE.toString())){
            throw new CustomizeException("跨授权调拨配置开启之后不可修改");
        }
    }

    /**
     *
     * @param updateConfigVO
     */
    @Override
    @DSTransactional
    public void updateConfig(UpdateConfigVO updateConfigVO) {
        //1.开关校验
        updateCheck(updateConfigVO);
        //2.根据授权创建数据
        createDataByAuthorize();
        //3.配置功能的修改
        boolean update = sysConfigService.lambdaUpdate().eq(SysConfig::getId, updateConfigVO.getId())
                .set(SysConfig::getValue, updateConfigVO.getValue())
                .set(SysConfig::getDsc, updateConfigVO.getDsc())
                .update();
        if(!update){
            throw new CustomizeException("配置修改失败");
        }

    }

    /**
     * 根据授权创建数据
     */
    @Override
    @DSTransactional
    public void createDataByAuthorize(){
        //1.授权查询
        List<Authorize> list = authorizeService.lambdaQuery().list();
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        //2.授权进行两两组合
        List<CrossAuthTransferConfigExtend> addConfigList = getConfigInfo(list);
        //3.进行渠道的创建
        createChannel(addConfigList);
        //4.进行企业客户的创建
        createCustomer(addConfigList);
        //5.CrossAuthTransferConfig数据表的保存
        addConfigList.forEach(authTransferConfigService::save);
    }


    /**
     * 处理收货门店
     * @param req
     */
    private void handleReq(PageSubInfoVOReq req){
        // 商品分类特殊处理，保证命中sql查询，随便给个值,
        if(SubPageReqEnums.category.getCode().equals(req.getQueryType())){
            req.setKey("111");
            if (CollUtil.isNotEmpty(req.getCategoryIdList())) {
                req.setCategoryCharSeq(StrUtil.join(",", req.getCategoryIdList()));
            }
        }
        // 如果查询类型为1(商品ID), 2(PPID)或7(单号)并且key不为空, 校验key必须为整数类型
        if (req.getQueryType() != null && 
            (SubPageReqEnums.PRODUCTID.getCode().equals(req.getQueryType()) || 
             SubPageReqEnums.PPID.getCode().equals(req.getQueryType()) || 
             SubPageReqEnums.order.getCode().equals(req.getQueryType()) ) &&
            StrUtil.isNotEmpty(req.getKey())) {
            
            // 校验key是否为整数
            if (!NumberUtil.isInteger(req.getKey())) {
                throw new CustomizeException(SubPageReqEnums.getMessageByCode(req.getQueryType()) + "必须为整数");
            }
        }
        
        // 如果查询类型为7(单号)并且key不为空，创建新的请求对象
        if (req.getQueryType() != null && SubPageReqEnums.order.getCode().equals(req.getQueryType()) && StrUtil.isNotEmpty(req.getKey())) {
            // 保留必要的参数，其他都使用默认值
            PageSubInfoVOReq newReq = new PageSubInfoVOReq();
            newReq.setKey(req.getKey());
            newReq.setQueryType(req.getQueryType());
            newReq.setExportType(req.getExportType());
            // 复制新对象的值到原对象
            BeanUtils.copyProperties(newReq, req);
        }
    }

    /**
     * 处理发货门店
     * @param req
     */
    private void handleFromAreaId(PageSubInfoVOReq req){
        List<String> fromAreaId = req.getFromAreaId();
        List<String> toAreaId = req.getToAreaId();
        OaUserBO oaUserBO = Optional.ofNullable(currentRequestComponent.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("登录信息失效"));
        //获取当前门店
        Integer currentAreaId = oaUserBO.getAreaId();
        //判断是否为总部
        List<SysConfig> sysConfigList = sysConfigService.lambdaQuery()
                .eq(SysConfig::getCode, 60)
                .eq(SysConfig::getXtenant, oaUserBO.getXTenant())
                .list();
        if(CollectionUtils.isNotEmpty(sysConfigList) && sysConfigService.isAuthorization()){
            SysConfig sysConfig = sysConfigList.get(0);
            Integer areaHqId =Integer.parseInt(Optional.ofNullable(sysConfig.getValue()).orElse("0"));
            if(areaHqId.equals(currentAreaId)){
                req.setIsHeadquarters(Boolean.TRUE);
            } else {
                req.setIsHeadquarters(Boolean.FALSE);
            }
        } else {
            req.setIsHeadquarters(Boolean.FALSE);
        }
        List<String> jurisdictionAreaIds = ch999RankService.lambdaQuery()
                .eq(Ch999Ranks::getCh999Id, oaUserBO.getUserId())
                .select(Ch999Ranks::getAreaId)
                .list().stream().map(Ch999Ranks::getAreaId)
                .filter(ObjectUtil::isNotNull).map(String::valueOf)
                .collect(Collectors.toList());
        Boolean isHeadquarter = Optional.ofNullable(req.getIsHeadquarters()).orElse(Boolean.FALSE);
        if(CollectionUtils.isEmpty(fromAreaId) && CollectionUtils.isEmpty(toAreaId)){
            if(!isHeadquarter){
                req.setFromAreaId(jurisdictionAreaIds);
                req.setToAreaId(jurisdictionAreaIds);
                req.setSelectToFromArea(Boolean.FALSE);
            }

        } else if (CollectionUtils.isNotEmpty(fromAreaId) && CollectionUtils.isEmpty(toAreaId)){
            req.setFromAreaId(fromAreaId.stream().filter(NumberUtil::isNumber).collect(Collectors.toList()));
            req.setSelectToArea(Boolean.FALSE);
        } else if (CollectionUtils.isEmpty(fromAreaId) && CollectionUtils.isNotEmpty(toAreaId)){
            req.setToAreaId(toAreaId.stream().filter(NumberUtil::isNumber).collect(Collectors.toList()));
            req.setSelectFromArea(Boolean.FALSE);
        } else if(CollectionUtils.isNotEmpty(fromAreaId) && CollectionUtils.isNotEmpty(toAreaId)) {
            req.setFromAreaId(fromAreaId.stream().filter(NumberUtil::isNumber).collect(Collectors.toList()));
            req.setToAreaId(toAreaId.stream().filter(NumberUtil::isNumber).collect(Collectors.toList()));
            req.setSelectFromArea(Boolean.FALSE);
        }




//        if(CollectionUtils.isEmpty(fromAreaId)){
//            if(!isHeadquarter){
//                req.setFromAreaId(jurisdictionAreaIds);
//            }
//        } else {
//            req.setFromAreaId(fromAreaId.stream().filter(NumberUtil::isNumber).collect(Collectors.toList()));
//        }
//
//        if(CollectionUtils.isEmpty(toAreaId)){
//            if(!isHeadquarter){
//                req.setToAreaId(jurisdictionAreaIds);
//            }
//        } else {
//            req.setToAreaId(toAreaId.stream().filter(NumberUtil::isNumber).collect(Collectors.toList()));
//        }
        //获取当前授权
        Integer currentAuthorizeId = oaUserBO.getAuthorizeId();
        req.setCurrentAuthorizeId(currentAuthorizeId);
        //去空格
        Optional.ofNullable(req.getTitle()).ifPresent(item->req.setTitle(item.trim()));
        //去空格
        Optional.ofNullable(req.getKey()).ifPresent(item->req.setKey(item.trim()));

    }


    @Override
    public IPage<PageSubInfoVO> pageSubInfo(PageSubInfoVOReq req){
        //使用老的查询
        if(NumberConstant.ONE.equals(req.getUseOld())){
            return pageSubInfoOld(req);
        }
        try {
            return pageSubInfoNew(req);
        }catch (Exception e){
            RRExceptionHandler.logError("新版跨授权调拨查询异常", req, e, SpringUtil.getBean(ISmsService.class)::sendOaMsgTo9JiMan);
            return pageSubInfoOld(req);
        }

    }

    @Override
    public IPage<PageSubInfoVO> pageSubInfoOld(PageSubInfoVOReq req) {
        Page<PageSubInfoVO> page = new Page<>();
        page.setCurrent(req.getCurrent())
                .setSize(req.getSize());
        //处理入参
        handleReq(req);
        //处理发货门店
        handleFromAreaId(req);

        page = crossAuthTransferSubMapper.selectPageSubInfo(req, page);
        List<PageSubInfoVO> records = page.getRecords();
        if(CollectionUtils.isEmpty(records)){
            return page;
        }
        List<Integer> areaIdList = new ArrayList<>();
        List<Integer> idList = new ArrayList<>();
        records.forEach(item->{
            areaIdList.add(item.getFromAreaId());
            areaIdList.add(item.getToAreaId());
            idList.add(item.getFromAuthId());
            idList.add(item.getToAuthId());
        });
        //获取门店信息
        Map<Integer, Areainfo> areaMap = areaInfoService.getAreaMapByIdsNew(areaIdList.stream().distinct().collect(Collectors.toList()));
        //获取授权信息
        Map<Integer, Authorize> authorizeMap = authorizeService.getAuthorizeMap(idList.stream().distinct().collect(Collectors.toList()));
        records.forEach(item->{
            //调拨单状态转换
            Optional.ofNullable(item.getStatus()).ifPresent(status-> item.setStatusValue(TransferStateEnums.getMessageByCode(status)));
            //门店名称转换
            Areainfo areaFrom = Optional.ofNullable(areaMap.get(item.getFromAreaId())).orElse(new Areainfo());
            Areainfo areaTo = Optional.ofNullable(areaMap.get(item.getToAreaId())).orElse(new Areainfo());
            item.setFromAreaIdValue(areaFrom.getArea()).setToAreaIdValue(areaTo.getArea());
            //授权名称转换
            Authorize authorizeFrom = Optional.ofNullable(authorizeMap.get(item.getFromAuthId())).orElse(new Authorize());
            Authorize authorizeTo = Optional.ofNullable(authorizeMap.get(item.getToAuthId())).orElse(new Authorize());
            item.setFromAuthIdValue(authorizeFrom.getName()).setToAuthIdValue(authorizeTo.getName());
            if(NumberConstant.ONE.equals(req.getExportType())){
                CostDetailVo detailVo = detailService.selectTotalCostDetail(item.getDetailId(),item.getStatus());
                item.setTotalCost(detailVo.getTotalCost())
                        .setCount(detailVo.getTotalCount())
                        .setTotalTransferPrice(detailVo.getTotalTransferPrice());
            } else {
                //获取订单总金额(判断如果过订单不是 已完成 那就显示实时价格)
                SubTotalInfo subTotalInfo = detailService.selectTotalCost(item.getId());
                if(LIST_STATE.contains(item.getStatus())){
                    item.setTotalCost(subTotalInfo.getTotalCost());
                }
                item.setCount(subTotalInfo.getTotalCount());
            }


            //设置调拨差额
            BigDecimal totalTransferPrice = Optional.ofNullable(item.getTotalTransferPrice()).orElse(BigDecimal.ZERO).setScale(NumberConstant.TWO, RoundingMode.HALF_UP);
            BigDecimal totalCost = Optional.ofNullable(item.getTotalCost()).orElse(BigDecimal.ZERO).setScale(NumberConstant.TWO, RoundingMode.HALF_UP);
            BigDecimal subtract = totalTransferPrice.subtract(totalCost);
            item.setTransferDifferencePrice(subtract);
        });
        return page;
    }




    @Override
    public IPage<PageSubInfoVO> pageSubInfoNew(PageSubInfoVOReq req) {
        Page<PageSubInfoVO> page = new Page<>();
        page.setCurrent(req.getCurrent())
                .setSize(req.getSize());
        //处理入参
        handleReq(req);
        //处理发货门店
        handleFromAreaId(req);

        // 查询分页数据
        page = crossAuthTransferSubMapper.selectPageSubInfo(req, page);
        List<PageSubInfoVO> records = page.getRecords();
        if(CollectionUtils.isEmpty(records)){
            return page;
        }

        // 一次性收集所有需要的IDs，避免在循环中收集
        Set<Integer> areaIdSet = new HashSet<>();
        Set<Integer> authIdSet = new HashSet<>();
        List<Integer> subIdSet = new ArrayList<>();
        Set<Integer> detailIdSet = new HashSet<>();

        for (PageSubInfoVO item : records) {
            areaIdSet.add(item.getFromAreaId());
            areaIdSet.add(item.getToAreaId());
            authIdSet.add(item.getFromAuthId());
            authIdSet.add(item.getToAuthId());
            subIdSet.add(item.getId());
            if (NumberConstant.ONE.equals(req.getExportType())) {
                detailIdSet.add(item.getDetailId());
            }
        }

        // 批量查询数据，减少数据库访问次数
        Map<Integer, Areainfo> areaMap = areaInfoService.getAreaMapByIdsNew(new ArrayList<>(areaIdSet));
        Map<Integer, Authorize> authorizeMap = authorizeService.getAuthorizeMap(new ArrayList<>(authIdSet));

        // 预先加载成本信息
        Map<Integer, SubTotalInfo> costInfoMap = new HashMap<>();
        Map<Integer, CostDetailVo> detailVoMap = new HashMap<>();

        // 创建ID到状态的映射，用于批量查询
        Map<Integer, Integer> detailStatusMap = new HashMap<>();
        if (NumberConstant.ONE.equals(req.getExportType())) {
            // 准备批量查询参数
            for (PageSubInfoVO item : records) {
                if (item.getDetailId() != null) {
                    detailStatusMap.put(item.getDetailId(), item.getStatus());
                }
            }

            // 这里需要扩展CrossAuthTransferDetailService接口，添加批量查询方法
            // 假设已经添加了批量查询方法batchSelectTotalCostDetail
            detailVoMap = detailService.batchSelectTotalCostDetail(new ArrayList<>(detailStatusMap.keySet()), detailStatusMap);
        } else {
            // 批量获取订单总金额，也需要扩展接口添加批量查询方法
            // 假设已经添加了批量查询方法batchSelectTotalCost
            costInfoMap = detailService.batchSelectTotalCost(subIdSet);
        }

        // 处理每条记录
        for (PageSubInfoVO item : records) {
            // 调拨单状态转换
            Optional.ofNullable(item.getStatus()).ifPresent(status ->
                    item.setStatusValue(TransferStateEnums.getMessageByCode(status)));

            // 门店名称转换
            Areainfo areaFrom = Optional.ofNullable(areaMap.get(item.getFromAreaId())).orElse(new Areainfo());
            Areainfo areaTo = Optional.ofNullable(areaMap.get(item.getToAreaId())).orElse(new Areainfo());
            item.setFromAreaIdValue(areaFrom.getArea()).setToAreaIdValue(areaTo.getArea());

            // 授权名称转换
            Authorize authorizeFrom = Optional.ofNullable(authorizeMap.get(item.getFromAuthId())).orElse(new Authorize());
            Authorize authorizeTo = Optional.ofNullable(authorizeMap.get(item.getToAuthId())).orElse(new Authorize());
            item.setFromAuthIdValue(authorizeFrom.getName()).setToAuthIdValue(authorizeTo.getName());

            if (NumberConstant.ONE.equals(req.getExportType())) {
                // 使用预加载的详细信息
                CostDetailVo detailVo = detailVoMap.get(item.getDetailId());
                if (detailVo != null) {
                    item.setTotalCost(detailVo.getTotalCost())
                            .setCount(detailVo.getTotalCount())
                            .setTotalTransferPrice(detailVo.getTotalTransferPrice());
                }
            } else {
                // 使用预加载的成本信息
                SubTotalInfo subTotalInfo = costInfoMap.get(item.getId());
                if (subTotalInfo != null) {
                    if (LIST_STATE.contains(item.getStatus())) {
                        item.setTotalCost(subTotalInfo.getTotalCost());
                    }
                    item.setCount(subTotalInfo.getTotalCount());
                }
            }

            // 设置调拨差额
            BigDecimal totalTransferPrice = Optional.ofNullable(item.getTotalTransferPrice()).orElse(BigDecimal.ZERO)
                    .setScale(NumberConstant.TWO, RoundingMode.HALF_UP);
            BigDecimal totalCost = Optional.ofNullable(item.getTotalCost()).orElse(BigDecimal.ZERO)
                    .setScale(NumberConstant.TWO, RoundingMode.HALF_UP);
            BigDecimal subtract = totalTransferPrice.subtract(totalCost);
            item.setTransferDifferencePrice(subtract);
        }

        return page;
    }

    /**
     * 获取电话号码 自动生成
     * @param config
     * @return
     */
    private String createMobile(CrossAuthTransferConfigExtend config){
        Areainfo area = Optional.ofNullable(config.getArea()).orElse(new Areainfo());
        Integer xtenant = area.getXtenant();
        //获取排序号
        Long count = stringRedisTemplate.opsForValue().increment(MOBILE+xtenant);
        return xtenant+String.format("%05d", count);
    }

    /**
     * 创建用户
     * @param addConfigList
     */
    @DSTransactional
    private void createCustomer(List<CrossAuthTransferConfigExtend> addConfigList){
        OaUserBO oaUserBO = Optional.ofNullable(currentRequestComponent.getCurrentStaffId()).orElseThrow(()->new CustomizeException("登录信息失效，请重新登录"));
        addConfigList.forEach(item->{
            Areainfo area = item.getArea();
            Integer xtenant = area.getXtenant();
            WuLiuZiTiDianEntity wuLiuZiTiDianEntity = new WuLiuZiTiDianEntity();
            wuLiuZiTiDianEntity.setCityId(area.getCityid())
                    .setXtenant(xtenant)
                    .setShopType(NumberConstant.FOUR)
                    .setName(item.getName())
                    .setAuthorizeid(item.getFromAuthId())
                    .setCustomerKinds(CUSTOMER_KINDS)
                    .setAdddate(LocalDateTime.now())
                    .setTel1(item.getMobile())
                    .setIspass(Boolean.TRUE)
                    .setInuser(oaUserBO.getUserName());
            UserModelCloudVO userModelCloudVO = new UserModelCloudVO();
            userModelCloudVO.setCheckParam(Boolean.FALSE);
            userModelCloudVO.setUserName(item.getName());
            userModelCloudVO.setErdu(ER_DU);
            userModelCloudVO.setRealname(item.getAuthorizeToName());
            userModelCloudVO.setMobile(item.getMobile());
            R<UserRegResultCloudVO> result = userInfoCloud.userRegister(userModelCloudVO,xtenant);
            log.warn("调用主站会员注册接口传入参数：{},返回结果：{}", JSONUtil.toJsonStr(userModelCloudVO),JSONUtil.toJsonStr(result));
            if(!result.isSuccess()){
                throw new CustomizeException(Optional.ofNullable(result.getMsg()).orElse(result.getUserMsg()));
            }
            UserRegResultCloudVO userRegResultCloudVO = Optional.ofNullable(result.getData()).orElse(new UserRegResultCloudVO());
            UserModelCloudVO modelCloudVO = Optional.ofNullable(userRegResultCloudVO.getMemberInfo()).orElseThrow(() -> new CustomizeException("主站注册用户信息为空"));
            int userId = Integer.parseInt(modelCloudVO.getID());
            wuLiuZiTiDianEntity.setUserid(userId);
            wuLiuZiTiDianService.save(wuLiuZiTiDianEntity);
            //账期的数据写入
            SaveMoneyTiming saveMoneyTiming = new SaveMoneyTiming();
            saveMoneyTiming.setTimingdate(LocalDateTime.now())
                    .setUserid(userId)
                    .setTimingday(TIMING_DAY);
            saveMoneyTimingService.save(saveMoneyTiming);
            item.setCustomerId(wuLiuZiTiDianEntity.getId());
        });
    }

    /**
     * 创建渠道
     * @param addConfigList
     */
    @DSTransactional
    private void createChannel(List<CrossAuthTransferConfigExtend> addConfigList){
        addConfigList.forEach(item->{
            ChannelVo channelVo = new ChannelVo();
            Areainfo area = item.getArea();
            String mobile = item.getMobile();
            //构建渠道创建信息
            String name = item.getName();
            channelVo.setCompany(name);
            channelVo.setCompanyJc(name);
            channelVo.setRegisteredcapital(BigDecimal.ZERO);
            channelVo.setCompanynature(ChannelCompanyNatureEnum.PROVINCIAL.getCode());
            channelVo.setAuthorizeid(item.getFromAuthId());
            channelVo.setSourceType(SourceTypeEnum.CROSS_AUTHORIZATION.getCode());
            channelVo.setInsourceid(INSOURCE_ID);
            channelVo.setLegalrepresent(name);
            channelVo.setKinds(Arrays.asList(ChannelKindSaasEnum.MOBILE_ACCESSORIES.getCode(),ChannelCompanyNatureEnum.PROVINCIAL.getCode()));
            channelVo.setShouhoucontacts(name);
            channelVo.setShouhoumobile(mobile);
            channelVo.setAfterAddress(area.getCompanyAddress());
            channelVo.setAfterCityid(area.getCityid());
            ContactsDto contactsDto = new ContactsDto();
            contactsDto.setTel(mobile).setUsername(name);
            channelVo.setContactsList(Collections.singletonList(contactsDto));
            channelVo.setChannelscale(ChannelScaleEnum.HALF_MILLION_TO_FIVE_MILLION.getCode());
            channelVo.setCompanynature(ChannelCompanyNatureEnum.PROVINCIAL.getCode());
            channelVo.setCwFzr(name);
            channelVo.setCwLxfs(mobile);
            channelVo.setLegalrepresent(name);
            OaAttachmentsReq.FileBO fileBO = new OaAttachmentsReq.FileBO();
            channelVo.setAttachments(Collections.singletonList(fileBO));
            channelVo.setRegisteredcapital(BigDecimal.ZERO);
            channelVo.setClassification("1");
            //构建小件业务信息
            FinanceDto financeDtoSmall = new FinanceDto();
            ChannelKindLink kindLinkSmall = new ChannelKindLink();
            kindLinkSmall.setKind(ChannelKindSaasEnum.MOBILE_ACCESSORIES.getCode());
            kindLinkSmall.setChannelState(ChannelCooperationStatusEnum.COOPERATION.getCode());
            kindLinkSmall.setSubject(SUBJECT_SMALL);
            financeDtoSmall.setKindLink(kindLinkSmall);
            //构建维修配件业务信息
            FinanceDto financeDtoMaintenance = new FinanceDto();
            ChannelKindLink kindLinkMaintenance = new ChannelKindLink();
            kindLinkMaintenance.setKind(ChannelKindSaasEnum.MAINTENANCE_ACCESSORIES.getCode());
            kindLinkMaintenance.setChannelState(ChannelCooperationStatusEnum.COOPERATION.getCode());
            kindLinkMaintenance.setSubject(SUBJECT_MAINTENANCE);
            financeDtoMaintenance.setKindLink(kindLinkMaintenance);
            //构建大件业务信息
            FinanceDto financeDtoBig = new FinanceDto();
            ChannelKindLink kindLinkBig = new ChannelKindLink();
            kindLinkBig.setKind(ChannelKindSaasEnum.MOBILE.getCode());
            kindLinkBig.setSubject(SUBJECT_BIG);
            kindLinkBig.setChannelState(ChannelCooperationStatusEnum.COOPERATION.getCode());
            financeDtoBig.setKindLink(kindLinkBig);
            //整合所有渠道的业务信息
            List<FinanceDto> financeDtos = Arrays.asList(financeDtoSmall, financeDtoBig,financeDtoMaintenance);
            channelVo.setFinanceList(financeDtos);
            R<Boolean> booleanR = ok3wQudaoService.saveOrUpdateChannel(channelVo, Boolean.TRUE);
            if(!booleanR.isSuccess()){
                throw new CustomizeException(Optional.ofNullable(booleanR.getUserMsg()).orElse(booleanR.getMsg()));
            }
            item.setChannelId(channelVo.getId());
        });

    }

    /**
     * 获取授权两两对应的信息
     * @param list
     * @return
     */

    private List<CrossAuthTransferConfigExtend> getConfigInfo(List<Authorize> list){
        List<CrossAuthTransferConfigExtend> configInfoList = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            for (int j = 0; j < list.size(); j++) {
                CrossAuthTransferConfigExtend extend = new CrossAuthTransferConfigExtend();
                if(i==j){
                    continue;
                }
                Authorize authorizeFrom = list.get(i);
                Authorize authorizeTo = list.get(j);

                //名称的创建
                String name = String.format(authorizeTo.getName() + "(%s-%s)", authorizeFrom.getId(), authorizeTo.getId());
                extend.setName(name)
                        .setAuthorizeToName(authorizeTo.getName())
                        .setFromAuthId(authorizeFrom.getId())
                        .setToAuthId(authorizeTo.getId());
                List<Integer> dcAreaIdList = StrUtil.splitTrim(authorizeFrom.getDcAreaId(), StringPool.COMMA).stream()
                        .filter(Objects::nonNull)
                        .map(Integer::new)
                        .collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(dcAreaIdList)){
                    Areainfo area = areaInfoService.lambdaQuery().eq(Areainfo::getId,dcAreaIdList.get(0)).one();
                    extend.setArea(area);
                    extend.setMobile(createMobile(extend));
                } else {
                    throw new CustomizeException("门店信息获取为空");
                }
                configInfoList.add(extend);
            }
        }
        OaUserBO oaUserBO = Optional.ofNullable(currentRequestComponent.getCurrentStaffId()).orElseThrow(()->new CustomizeException("登录信息失效，请重新登录"));
        if(CollectionUtils.isNotEmpty(configInfoList)){
            //移除已经创创建过的数据
            configInfoList.removeIf(item->{
                List<CrossAuthTransferConfig> configList = authTransferConfigService.lambdaQuery().eq(CrossAuthTransferConfig::getFromAuthId, item.getFromAuthId())
                        .eq(CrossAuthTransferConfig::getToAuthId, item.getToAuthId())
                        .list();
                if(CollectionUtils.isEmpty(configList)){
                    item.setCreateTime(LocalDateTime.now())
                            .setIsDel(Boolean.FALSE)
                            .setCreateUser(oaUserBO.getUserName());
                    return Boolean.FALSE;
                }
                return Boolean.TRUE;
            });
        }
        return configInfoList;
    }
}
