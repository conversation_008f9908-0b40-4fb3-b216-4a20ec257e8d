package com.jiuji.oa.stock.authorizationtransfer.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.jiuji.oa.stock.authorizationtransfer.enums.TransferStateEnums;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("cross_auth_transfer_sub")
public class CrossAuthTransferSub {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * @see TransferStateEnums
     * 调拨单状态 0已提交 1已审核 2已取消 3已完成'
     */
    private Integer status;

    /**
     * 发货门店id
     */
    @TableField("from_area_id")
    private Integer fromAreaId;

    /**
     * 收货门店id
     */
    @TableField("to_area_id")
    private Integer toAreaId;

    /**
     * 发货授权id
     */
    @TableField("from_auth_id")
    private Integer fromAuthId;

    /**
     * 收货授权id
     */
    @TableField("to_auth_id")
    private Integer toAuthId;

    /**
     * 标题
     */
    private String title;

    /**
     * 总成本
     */
    @TableField("total_cost")
    private BigDecimal totalCost;

    /**
     * 调拨金额
     */
    @TableField("total_transfer_price")
    private BigDecimal totalTransferPrice;

    /**
     * 生成的订单号
     */
    @TableField("sub_id")
    private Integer subId;

    @TableLogic
    private Boolean isDel;

    /**
     * 删除人
     */
    @TableField("del_user")
    private String delUser;

    /**
     * 删除时间
     */
    @TableField("del_time")
    private LocalDateTime delTime;

    /**
     * 创建人
     */
    @TableField("create_user")
    private String createUser;
    /**
     * 办理人工号
     */
    @TableField("create_staff_id")
    private Integer createStaffId;


    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 审核人
     */
    @TableField("check_user")
    private String checkUser;

    /**
     * 审核人工号
     */
    private Integer checkStaffId;

    /**
     * 审核时间
     */
    @TableField("check_time")
    private LocalDateTime checkTime;

    /**
     * 办理人
     */
    @TableField("handle_user")
    private String handleUser;

    /**
     * 办理人工号
     */
    @TableField("handle_staff_id")
    private Integer handleStaffId;

    /**
     * 办理时间
     */
    @TableField("handle_time")
    private LocalDateTime handleTime;

}
