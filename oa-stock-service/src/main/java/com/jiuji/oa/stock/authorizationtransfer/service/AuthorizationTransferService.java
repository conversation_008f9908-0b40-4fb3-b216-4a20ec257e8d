package com.jiuji.oa.stock.authorizationtransfer.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jiuji.oa.stock.authorizationtransfer.vo.PageSubInfoVO;
import com.jiuji.oa.stock.authorizationtransfer.vo.PageSubInfoVOReq;
import com.jiuji.oa.stock.authorizationtransfer.vo.UpdateConfigVO;




/**
 * <AUTHOR>
 */
public interface AuthorizationTransferService {

    /**
     * 开启 跨授权调拨
     * @param updateConfigVO
     */
    void updateConfig(UpdateConfigVO updateConfigVO);


    /**
     * 根据授权创建数据
     */
    void createDataByAuthorize();


    /**
     * 分页查询调拨单信息
     * @param req
     * @return
     */
    IPage<PageSubInfoVO> pageSubInfoOld(PageSubInfoVOReq req);
    IPage<PageSubInfoVO> pageSubInfo(PageSubInfoVOReq req);


    IPage<PageSubInfoVO> pageSubInfoNew(PageSubInfoVOReq req);
}
