package com.jiuji.oa.stock.authorizationtransfer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.oa.stock.authorizationtransfer.entity.CrossAuthTransferSub;
import com.jiuji.oa.stock.authorizationtransfer.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface CrossAuthTransferSubMapper extends BaseMapper<CrossAuthTransferSub> {

    /**
     * 商品信息查询
     * @param req
     * @return
     */
    Page<ProductSubmitInfo> selectProductSubmitInfo(@Param(value = "req") ProductReq req,@Param("page")Page<ProductSubmitInfo> page);



    List<ProductSubmitInfo> selectProductKcList(@Param(value = "req") ProductReq req);



    Page<PageSubInfoVO> selectPageSubInfo(@Param("req") PageSubInfoVOReq req,@Param("page")Page<PageSubInfoVO> page);

    /**
     * 串号校验信息查询
     * @param req
     * @return
     */
    List<CheckImeiInfo> selectCheckImeiInfo(@Param("req") CheckImeiReq req);
}
