package com.jiuji.oa.stock.nationalSupplement.res;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class NationalSupplementAttachmentJiuJiExcel{
    /**
     * 小区
     */
    @ExcelProperty("小区")
    private String departName;
    /**
     * 门店
     */
    @ExcelProperty("门店")
    private String area;

    @ExcelProperty("单号")
    private Integer subId;
    /**
     * 销售人
     */
    @ExcelProperty("销售人")
    private String seller;
    /**
     * 审核状态
     */
    @ExcelProperty("审核状态")
    private String checkStateValue;
    /**
     * 审核备注原因
     */
    @ExcelProperty("审核备注原因")
    private String checkComment;

    /**
     * 审核时间
     */
    @ExcelProperty("审核时间")
    private String checkTimeStr;

    /**
     * 运营审核时间
     */
    @ExcelProperty("运营审核时间")
    private String operationCheckTimeStr;

    /**
     * 运营审核状态
     */
    @ExcelProperty("运营审核状态")
    private String operationCheckStateValue;
    /**
     * 运营复核人
     */
    @ExcelProperty("运营复核人")
    private String operationCheckUser;

    /**
     * 审核人
     */
    @ExcelProperty("审核人")
    private String checkUser;
    /**
     * 交易完成时间
     */
    @ExcelProperty("交易完成时间")
    private String tradeDate1Str;
    /**
     * 身份证
     */
    @ExcelProperty("身份证")
    private String invoiceIdCard;
    /**
     * 收货人
     */
    @ExcelProperty("收货人")
    private String subTo;

    /**
     * 邮箱
     */
    @ExcelProperty("邮箱")
    private String invoiceReceivedEmail;

}
