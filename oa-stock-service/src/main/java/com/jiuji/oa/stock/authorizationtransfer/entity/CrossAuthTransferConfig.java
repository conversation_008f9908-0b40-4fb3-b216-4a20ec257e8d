package com.jiuji.oa.stock.authorizationtransfer.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName(value = "cross_auth_transfer_config")
public class CrossAuthTransferConfig {

    @TableId(value = "id",type = IdType.AUTO)
    private Integer id;

    @TableField("from_auth_id")
    private Integer fromAuthId;

    @TableField("to_auth_id")
    private Integer toAuthId;

    @TableField("channel_id")
    private Integer channelId;

    @TableField("customer_id")
    private Integer customerId;

    @TableField("create_time")
    private LocalDateTime createTime;

    @TableField("create_user")
    private String createUser;

    @TableLogic
    private Boolean isDel;

}
