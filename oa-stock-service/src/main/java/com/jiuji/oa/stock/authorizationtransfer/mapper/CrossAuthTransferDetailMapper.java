package com.jiuji.oa.stock.authorizationtransfer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.stock.authorizationtransfer.entity.CrossAuthTransferDetail;
import com.jiuji.oa.stock.authorizationtransfer.vo.CrossAuthTransferDetailVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Mapper
public interface CrossAuthTransferDetailMapper extends BaseMapper<CrossAuthTransferDetail> {

    List<CrossAuthTransferDetailVO> getCategoryName(@Param("transferId") Integer transferId);
}
