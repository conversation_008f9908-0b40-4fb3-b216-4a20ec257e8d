package com.jiuji.oa.stock.distributionOfGoods.service;

import com.jiuji.oa.stock.distributionOfGoods.vo.*;
import com.jiuji.tc.common.vo.R;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DistributionOfGoodsService {

    /**
     * 查询商品信息
     * @param searchCriteriaVO
     * @return
     */
    R<List<ProductDetailVO>> getReportFormProduct(SearchCriteriaVO searchCriteriaVO);


    /**
     * 查询商品库存部分
     * @param searchCriteriaVO
     * @return
     */
    R<List<StockDetailVO>> getReportFormStock(SearchStockDetailVO searchCriteriaVO);

    /**
     * 查询商品树状结构
     * @param searchCriteriaVO
     * @return
     */
    R<TreeVo> getProportionOfSalesVolume(SearchCriteriaVO searchCriteriaVO);

    /**
     * 批量调货
     * @param list
     * @return
     */
    R<Boolean> automaticGoodsTransfer(List<AutomaticGoodsTransferVO> list);
}
