package com.jiuji.oa.stock.nationalSupplement.service;

import com.jiuji.cloud.stock.vo.request.SelectAgencyReq;
import com.jiuji.cloud.stock.vo.response.SelectAgencyRes;
import com.jiuji.oa.stock.nationalSupplement.req.DeclarationDataSaasReq;

import java.util.List;

public interface AutomaticDeclarationSaasService {



    List<Integer> selectDeclarationDataSaas(DeclarationDataSaasReq dataSaasReq);


    /**
     * 代办查询
     * @param selectAgencyReq
     * @return
     */
    SelectAgencyRes selectAgency(SelectAgencyReq selectAgencyReq);

    Integer getTenantId(DeclarationDataSaasReq dataSaasReq);

}
