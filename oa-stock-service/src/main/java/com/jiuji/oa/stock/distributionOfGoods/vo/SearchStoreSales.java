package com.jiuji.oa.stock.distributionOfGoods.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SearchStoreSales {

    /**
     * 门店id
     */
    private List<Integer> areaIdList;

    /**
     * 门店类别
     */
    private List<Integer> kind1List;
    /**
     * 门店属性
     */
    private List<Integer> attributeList;
    /**
     * sku_id
     */
    private List<Integer> ppidList;
    /**
     * 销售开始时间
     */
    private LocalDateTime salesStartTime;

    /**
     * 销售结束时间
     */
    private LocalDateTime salesEndTime;

}
