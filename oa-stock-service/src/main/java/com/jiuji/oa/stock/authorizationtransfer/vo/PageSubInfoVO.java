package com.jiuji.oa.stock.authorizationtransfer.vo;


import com.alibaba.fastjson.annotation.JSONField;
import com.jiuji.oa.stock.authorizationtransfer.enums.TransferStateEnums;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class PageSubInfoVO {

    private Integer id;

    /**
     * @see TransferStateEnums
     * 调拨单状态 0已提交 1已审核 2已取消 3已完成'
     */
    private Integer status;
    private String statusValue;
    private String categoryName;
    /**
     * 发货门店id
     */
    private Integer fromAreaId;
    private String fromAreaIdValue;

    /**
     * 收货门店id
     */
    private Integer toAreaId;
    private String toAreaIdValue;

    /**
     * 发货授权id
     */
    private Integer fromAuthId;
    private String fromAuthIdValue;

    /**
     * 收货授权id
     */
    private Integer toAuthId;
    private String toAuthIdValue;

    /**
     * 标题
     */
    private String title;

    /**
     * 总成本
     */

    private BigDecimal totalCost;

    /**
     * 调拨金额
     */

    private BigDecimal totalTransferPrice;

    /**
     * 调拨差额
     */
    private BigDecimal transferDifferencePrice;

    /**
     * 生成的订单号
     */

    private Integer subId;

    /**
     * 数量
     */
    private Integer count;


    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 审核人
     */

    private String checkUser;

    /**
     * 审核时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime checkTime;

    /**
     * 办理人
     */
    private String handleUser;

    /**
     * 办理时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime handleTime;

    private String imei;
    private Integer ppriceid;
    private String productName;
    private String productColor;
    /**
     *
     * 调拨单价
     */
    private BigDecimal transferPrice;

    /**
     * 调拨详情id
     */
    private Integer detailId;


}
