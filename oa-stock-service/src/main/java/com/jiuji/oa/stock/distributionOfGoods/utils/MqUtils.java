package com.jiuji.oa.stock.distributionOfGoods.utils;

import com.jiuji.oa.nc.common.config.rabbitmq.RabbitMqConfig;
import com.jiuji.oa.stock.publiccheck.annotation.AddLog;
import com.jiuji.oa.stock.publiccheck.entity.AddLogKind;
import com.jiuji.tc.common.vo.R;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class MqUtils {

    @Qualifier("oaAsyncRabbitTemplate")
    @Autowired
    private RabbitTemplate rabbitTemplate;

    /**
     * 大件调拨mq推送
     * @param message
     * @return
     */
    @AddLog(type = AddLogKind.GOODS_TRANSFER)
    public R<Boolean> goodsTransferMqSend(String message){
        rabbitTemplate.convertAndSend(RabbitMqConfig.OA_ASYNC, message);
        return R.success("请求成功，调拨完成后会推送OA消息，请注意查收！");
    }
}
