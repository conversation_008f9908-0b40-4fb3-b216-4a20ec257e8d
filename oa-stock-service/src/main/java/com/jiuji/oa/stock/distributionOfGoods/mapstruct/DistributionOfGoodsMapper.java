package com.jiuji.oa.stock.distributionOfGoods.mapstruct;

import com.jiuji.oa.stock.distributionOfGoods.vo.ProportionOfSalesVolumeVO;
import com.jiuji.oa.stock.distributionOfGoods.vo.TreeVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DistributionOfGoodsMapper {

    TreeVo toTreeVo(ProportionOfSalesVolumeVO proportionOfSalesVolumeVO);
}
