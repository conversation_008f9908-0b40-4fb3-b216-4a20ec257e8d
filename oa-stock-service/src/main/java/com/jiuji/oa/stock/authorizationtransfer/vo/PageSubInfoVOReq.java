package com.jiuji.oa.stock.authorizationtransfer.vo;

import com.jiuji.oa.stock.authorizationtransfer.enums.SelectTimeEnums;
import com.jiuji.oa.stock.authorizationtransfer.enums.SubPageReqEnums;
import com.jiuji.oa.stock.authorizationtransfer.enums.SubmitReqEnums;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class PageSubInfoVOReq {

    private Integer size;

    private Integer current;
    private String categoryCharSeq;
    /**
     * 分类id
     */
    private List<Integer> categoryIdList;

    /**
     * 当前授权
     */
    private Integer currentAuthorizeId;

    /**
     * @see SelectTimeEnums
     */
    private Integer selectTimeType;
    /**
     * 提交开始时间
     */
    private LocalDateTime subTimeStart;
    /**
     * 提交结束时间
     */
    private LocalDateTime subTimeEnd;

    /**
     * 收货门店id
     */
    private List<String> toAreaId;

    /**
     * 发货门店id
     */
    private List<String> fromAreaId;


    /**
     * @see SubmitReqEnums
     * 调拨单状态 0已提交 1已审核 2已取消 3已完成'
     */
    private List<Integer> statusList;

    /**
     * 标题
     */
    private String title;

    /**
     * 查询类型
     * @see SubPageReqEnums
     */
    private Integer queryType;

    private String key;

    /**
     * 是否总部
     */
    private Boolean isHeadquarters;

    /**
     * 1-导出明细
     * 导出类型
     */
    private Integer exportType;

    /**
     * 1-使用老版本
     */
    private Integer useOld;


    private Boolean selectFromArea = Boolean.TRUE;

    private Boolean selectToArea = Boolean.TRUE;

    private Boolean selectToFromArea = Boolean.TRUE;

}
