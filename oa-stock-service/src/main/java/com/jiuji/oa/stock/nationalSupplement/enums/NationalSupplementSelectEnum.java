package com.jiuji.oa.stock.nationalSupplement.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum NationalSupplementSelectEnum implements CodeMessageEnumInterface {
    SUBID(1, "订单号"),
    PRODUCT_NAME(2, "商品名称"),
    PPID(3, "PPID"),
    TRADER(4, "交易人"),
    SELLER(5, "销售人"),
    REVIEWER(6, "审核人"),
    OPERATION_CHECK_USER(7, "运营复核人");

    /**
     * 编码
     */
    private Integer code;
    /**
     * 名称
     */
    private String message;

}
