package com.jiuji.oa.stock.authorizationtransfer.vo;


import com.jiuji.oa.stock.authorizationtransfer.enums.TransferStateEnums;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class PageSubInfoExcel {

    /**
     * 订单号
     */
    private Integer id;

    /**
     * 发货门店
     */
    private String fromAreaIdValue;

    /**
     * 发货授权
     */
    private String fromAuthIdValue;




    /**
     * 收货门店
     */
    private String toAreaIdValue;

    /**
     * 收货授权
     */
    private String toAuthIdValue;

    /**
     * 标题
     */
    private String title;

    /**
     * 数量
     */
    private Integer count;

    /**
     * 总成本
     */
    private BigDecimal totalCost;

    /**
     * 调拨金额
     */
    private BigDecimal totalTransferPrice;

    /**
     * 调拨差额
     */
    private BigDecimal transferDifferencePrice;


    /**
     * @see TransferStateEnums
     * 调拨单状态 0已提交 1已审核 2已取消 3已完成'
     */
    private String statusValue;

    /**
     * 提交时间
     */
    private String createTime;

    /**
     * 创建人
     */
    private String createUser;


    /**
     * 审核时间
     */
    private String checkTime;

    /**
     * 审核人
     */
    private String checkUser;

    /**
     * 办理时间
     */
    private String handleTime;

    /**
     * 办理人
     */
    private String handleUser;


}
