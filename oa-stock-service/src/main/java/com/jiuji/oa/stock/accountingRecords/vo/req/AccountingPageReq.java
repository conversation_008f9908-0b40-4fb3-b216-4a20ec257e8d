package com.jiuji.oa.stock.accountingRecords.vo.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Accessors(chain = true)
public class AccountingPageReq {
    @NotNull(message = "每页数量不能为空")
    private Long size;

    /**
     * 当前页
     */
    @NotNull(message = "当前页不能为空")
    private Long current;

    /**
     * @see com.jiuji.oa.stock.accountingRecords.enums.SelectTypeEnum
     * 查询枚举
     */
    private Integer selectType;

    /**
     * 查询逻辑
     */
    private String selectValue;
    /**
     * 下账门店
     */
    private List<Integer> accountingAreaIdList;

    /**
     * 销售门店
     */
    private List<Integer> saleAreaIdList;


    /**
     * @see com.jiuji.oa.stock.accountingRecords.enums.AccountingStateEnum
     * 处理状态
     */
    private List<Integer> accountingStateList;

    /**
     * 订单状态
     */
    private List<Integer> subCheckList;

    /**
     * @see com.jiuji.oa.stock.accountingRecords.enums.AccountingTimeTypeEnum
     */
    private Integer timeType;

    /**
     * 加单开始时间
     */
    private LocalDateTime timeStart;
    /**
     * 加单结束时间
     */
    private LocalDateTime timeEnd;

    /**
     * 处理超时(最大值)
     */
    private Integer handleTimeoutMax;

    /**
     * 处理超时（最小值）
     */
    private Integer handleTimeoutMin;

    /**
     * 是否超时
     */
    private Boolean isHandleTimeout;

    /**
     * 下账报量记录类型
     * 0、华为
     * 1、苹果
     */
    private Integer recordsType;
}
