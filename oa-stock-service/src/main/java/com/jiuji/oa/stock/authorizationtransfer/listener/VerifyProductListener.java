package com.jiuji.oa.stock.authorizationtransfer.listener;


import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.jiuji.oa.baozun.common.util.StringUtils;
import com.jiuji.oa.nc.product.service.IProductInfoService;
import com.jiuji.oa.nc.product.vo.req.ProductInfoVo;
import com.jiuji.oa.stock.authorizationtransfer.vo.VerifyProductDetailRes;
import com.jiuji.oa.stock.productkc.service.IProductKcService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */

@Data
@Slf4j
public class VerifyProductListener extends AnalysisEventListener<LinkedHashMap> {

    private List<VerifyProductDetailRes> verifyProductDetailResList = new ArrayList<>();
    private final static String ERROR_MSG = "请输入正确的数字类型的ppid和数量";
    private final static String ERROR_NULL_MSG = "ppid和数量都不能为空";
    private final static String ERROR_OVER_LENGTH_MSG = "ppid或数量填写数量超长";
    private final static List<String> ERROR_MSG_LIST = Arrays.asList(ERROR_MSG, ERROR_NULL_MSG,ERROR_OVER_LENGTH_MSG);
    private static final String REGEX = "^[1-9]\\d*$";
    private static final Integer PPID = 1;
    private static final Integer COUNT = 2;
    private Integer transferAreaId;
    

    private IProductInfoService productInfoService;

    private IProductKcService productKcService;

    /**
     * 数字校验
     * @param number
     * @param verifyProductDetailRes
     * @param attribute 1-ppid 2-count
     */
    private void checkNumber(Object number,VerifyProductDetailRes verifyProductDetailRes,Integer attribute){
        if(Objects.isNull(number)){
            verifyProductDetailRes.setCheckReason(ERROR_NULL_MSG);
        } else {
            String numberStr = number.toString().trim();
            if(PPID.equals(attribute)){
                verifyProductDetailRes.setPpid(numberStr);
            } else if (COUNT.equals(attribute)){
                verifyProductDetailRes.setCount(numberStr);
            }
            if (!Pattern.matches(REGEX, numberStr)) {
                verifyProductDetailRes.setCheckReason(ERROR_MSG);
            }else{
                if(Integer.MAX_VALUE<Long.parseLong(numberStr)){
                    verifyProductDetailRes.setCheckReason(ERROR_OVER_LENGTH_MSG);
                }
            }
        }
    }

    @Override
    public void invoke(LinkedHashMap data, AnalysisContext analysisContext) {

        //把excel读取的数据封装到该对象 然后进行校验
        VerifyProductDetailRes verifyProductDetailRes = new VerifyProductDetailRes();
        //获取到读取当前行
        Integer rowIndex = analysisContext.readRowHolder().getRowIndex();
        if (rowIndex > 2) {
            //ppid
            Object ppid = data.get(0);
            checkNumber(ppid,verifyProductDetailRes,PPID);


            //数量
            Object count = data.get(1);
            checkNumber(count,verifyProductDetailRes,COUNT);

            verifyProductDetailResList.add(verifyProductDetailRes);
        }
    }

    /**
     * 添加校验原因
     * @param joinerCheckReason
     * @param productDetailRes
     */
    private void addCheckReason(StringJoiner joinerCheckReason,VerifyProductDetailRes productDetailRes){
        if(!joinerCheckReason.toString().isEmpty()){
            productDetailRes.setCheckReason(joinerCheckReason.toString());
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        if(CollectionUtils.isEmpty(verifyProductDetailResList)){
            return;
        }
        // 获取ppid 商品属性
        List<Long> ppidList = verifyProductDetailResList.stream()
                .filter(item -> StringUtils.isEmpty(item.getCheckReason()))
                .map(obj -> Long.parseLong(obj.getPpid()))
                .collect(Collectors.toList());
        Map<Long, ProductInfoVo> longProductInfoVoMap = Optional.ofNullable(productInfoService.listByppidsNew(ppidList)).orElse(new HashMap<>());
        // 获取ppid 库存属性
        Map<Integer, Integer> productKcMap = productKcService.getStockCountByAreaAndSkuNew(transferAreaId, ppidList.stream().map(Long::intValue).collect(Collectors.toList()));

        for (VerifyProductDetailRes productDetailRes:verifyProductDetailResList) {
            if(ERROR_MSG_LIST.contains(productDetailRes.getCheckReason())){
                continue;
            }
            StringJoiner joinerCheckReason = new StringJoiner(",");
            String ppid = productDetailRes.getPpid();
            //进行商品大小件属性校验
            ProductInfoVo productInfoVo = longProductInfoVoMap.getOrDefault(Long.parseLong(ppid),new ProductInfoVo());
            if(Boolean.TRUE.equals(productInfoVo.getMobile())){
                joinerCheckReason.add("商品属性不为小件");
                addCheckReason(joinerCheckReason,productDetailRes);
                continue;
            }
            //进行商品库存校验
            Integer count = Integer.parseInt(productDetailRes.getCount());
            Integer leftCount = productKcMap.get(Integer.parseInt(ppid));
            if(Objects.isNull(leftCount)){
                joinerCheckReason.add("该商品在该门店没有可用库存");
                addCheckReason(joinerCheckReason,productDetailRes);
                continue;
            } else {
                if(leftCount<count){
                    joinerCheckReason.add("该商品在该门店没有可用库存，可用库存："+leftCount);
                }
            }

            if(!joinerCheckReason.toString().isEmpty()){
                productDetailRes.setCheckReason(joinerCheckReason.toString());
            }
            addCheckReason(joinerCheckReason,productDetailRes);
        }
    }
}
