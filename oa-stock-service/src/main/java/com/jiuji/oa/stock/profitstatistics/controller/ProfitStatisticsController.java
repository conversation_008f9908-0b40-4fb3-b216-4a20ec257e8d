package com.jiuji.oa.stock.profitstatistics.controller;


import com.ch999.common.util.utils.RegexUtils;
import com.jiuji.oa.nc.common.exception.CustomizeException;
import com.jiuji.oa.stock.common.annotation.ValidateLocalDateTimeParam;
import com.jiuji.oa.stock.common.vo.IPage;
import com.jiuji.oa.stock.profitstatistics.enums.DistributionEnum;
import com.jiuji.oa.stock.profitstatistics.enums.ProfitStatisticsEnum;
import com.jiuji.oa.stock.profitstatistics.enums.SearchKeyOptionEnum;
import com.jiuji.oa.stock.profitstatistics.enums.StockAreaTypeEnum;
import com.jiuji.oa.stock.profitstatistics.service.IProfitStatisticsBusService;
import com.jiuji.oa.stock.profitstatistics.vo.req.ProfitStatisticsReqVO;
import com.jiuji.oa.stock.profitstatistics.vo.res.ProfitStatisticsResVO;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.enums.EnumUtil;
import com.jiuji.tc.utils.enums.EnumVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 *
 * @description: profitStatisticsController
 * </p>
 * @author: David
 * @create: 2021-04-09 20:21
 */

@RestController
@RequestMapping("/api/profitStatistics")
@Api(value = "profitStatistics", tags = "利润统计")
@RequiredArgsConstructor
public class ProfitStatisticsController {

    private final IProfitStatisticsBusService profitStatisticsBusService;

    @GetMapping("/get-profitStatistics-enum/v1")
    @ApiOperation("利润统计 - 表单枚举值")
    public R<Map<String, List<EnumVO>>> getProfitStatisticsEnum() {
        Map<String, List<EnumVO>> enumMap = new HashMap<>(4);
        List<EnumVO> profitStatisticsList = EnumUtil.toEnumVOList(ProfitStatisticsEnum.class);
        List<EnumVO> keyOptionList = EnumUtil.toEnumVOList(SearchKeyOptionEnum.class);
        List<EnumVO> areaTypeList = EnumUtil.toEnumVOList(StockAreaTypeEnum.class);
        List<EnumVO> distributionTypeList = EnumUtil.toEnumVOList(DistributionEnum.class);
        enumMap.put("searchOption", profitStatisticsList);
        enumMap.put("keyOption", keyOptionList);
        enumMap.put("areaType", areaTypeList);
        enumMap.put("distributionType", distributionTypeList);
        EnumVO enumVO = new EnumVO();
        enumVO.setValue(1);
        enumVO.setLabel("交易时间");
        enumMap.put("timeType", Collections.singletonList(enumVO));
        return R.success(enumMap);
    }


    /**
     * 分页查询利润统计
     *
     * @param reqVO 分页对象
     * @return
     */
    @ApiOperation(value = "分页查询利润统计", notes = "分页查询利润统计")
    @PostMapping("/page/v1")
    @ValidateLocalDateTimeParam
    public R<IPage<ProfitStatisticsResVO>> getProfitStatisticsPage(@RequestBody @Valid ProfitStatisticsReqVO reqVO) {
        initParams(reqVO);
        return R.success(profitStatisticsBusService.listPage(reqVO));
    }

    /**
     * 查询利润统计
     *
     * @param reqVO 分页对象
     * @return
     */
    @ApiOperation(value = "分页查询利润统计", notes = "分页查询利润统计")
    @PostMapping("/statistics/v1")
    @ValidateLocalDateTimeParam
    public R<Map<String, String>> getProfitStatistics(@RequestBody @Valid ProfitStatisticsReqVO reqVO) {
        initParams(reqVO);
        return R.success(profitStatisticsBusService.getStatistics(reqVO));
    }

    /**
     * @param reqVO
     */
    private void initParams(ProfitStatisticsReqVO reqVO) {
        if (reqVO.getSearchType() == null) {
            reqVO.setSearchType(1);
        }
        if (CollectionUtils.isNotEmpty(reqVO.getCIds())) {
            reqVO.setStrCIds(StringUtils.collectionToCommaDelimitedString(reqVO.getCIds()));
        }
        if (reqVO.getSearchOption() != null) {
            SearchKeyOptionEnum searchKeyOptionEnum = EnumUtil.getEnumByCode(SearchKeyOptionEnum.class, reqVO.getSearchOption());

            switch (searchKeyOptionEnum) {
                case SKU:
                    if (StringUtils.isEmpty(reqVO.getSearchKey())) {
                        reqVO.setSearchOption(0);
                        break;
                    }
                    if (!RegexUtils.checkDigit(reqVO.getSearchKey())) {
                        throw new CustomizeException("搜索条件为sku_id时，只能输入数字");
                    }
                    reqVO.setLongSearchKey(Long.parseLong(reqVO.getSearchKey()));
                    break;
                case SPU:
                    if (StringUtils.isEmpty(reqVO.getSearchKey())) {
                        reqVO.setSearchOption(0);
                        break;
                    }
                    if (!RegexUtils.checkDigit(reqVO.getSearchKey())) {
                        throw new CustomizeException("搜索条件为商品id时，只能输入数字");
                    }
                    reqVO.setLongSearchKey(Long.parseLong(reqVO.getSearchKey()));
                    break;
                default:
                    if (StringUtils.isEmpty(reqVO.getSearchKey())) {
                        reqVO.setSearchOption(0);
                    }
                    break;
            }
        }
    }
}