package com.jiuji.oa.stock.nationalSupplement.res;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class NationalSupplementAttachmentWithIdCardExcel extends NationalSupplementAttachmentSaasExcel {
    /**
     * 身份证
     */
    @ExcelProperty(value = "身份证" ,order = 35)
    private String invoiceIdCard;
} 