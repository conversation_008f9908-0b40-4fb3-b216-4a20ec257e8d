package com.jiuji.oa.stock.authorizationtransfer.service.impl;


import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.baozun.common.util.StringUtils;
import com.jiuji.oa.nc.channel.entity.ChannelKindLink;
import com.jiuji.oa.nc.channel.enums.ChannelCooperationStatusEnum;
import com.jiuji.oa.nc.channel.enums.ChannelKindEnum;
import com.jiuji.oa.nc.channel.service.ChannelKindLinkService;
import com.jiuji.oa.nc.common.bo.OaUserBO;
import com.jiuji.oa.nc.common.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.nc.common.exception.CustomizeException;
import com.jiuji.oa.nc.dict.service.ISysConfigService;
import com.jiuji.oa.nc.oaapp.po.SysConfig;
import com.jiuji.oa.nc.product.service.IProductInfoService;
import com.jiuji.oa.nc.product.vo.req.ProductInfoVo;
import com.jiuji.oa.nc.stock.service.CategoryService;
import com.jiuji.oa.nc.stock.service.IProductMkcService;
import com.jiuji.oa.nc.user.po.Areainfo;
import com.jiuji.oa.nc.user.po.Authorize;
import com.jiuji.oa.nc.user.service.AuthorizeService;
import com.jiuji.oa.nc.user.service.IAreaInfoService;
import com.jiuji.oa.stock.authorizationtransfer.entity.CrossAuthTransferConfig;
import com.jiuji.oa.stock.authorizationtransfer.entity.CrossAuthTransferDetail;
import com.jiuji.oa.stock.authorizationtransfer.entity.CrossAuthTransferSub;
import com.jiuji.oa.stock.authorizationtransfer.enums.ErrorImeiMsgEnums;
import com.jiuji.oa.stock.authorizationtransfer.enums.SubmitReqEnums;
import com.jiuji.oa.stock.authorizationtransfer.enums.TransferStateEnums;
import com.jiuji.oa.stock.authorizationtransfer.listener.VerifyProductListener;
import com.jiuji.oa.stock.authorizationtransfer.mapper.CrossAuthTransferSubMapper;
import com.jiuji.oa.stock.authorizationtransfer.service.CrossAuthTransferConfigService;
import com.jiuji.oa.stock.authorizationtransfer.service.CrossAuthTransferDetailService;
import com.jiuji.oa.stock.authorizationtransfer.service.CrossAuthTransferSubLogService;
import com.jiuji.oa.stock.authorizationtransfer.service.CrossAuthTransferSubService;
import com.jiuji.oa.stock.authorizationtransfer.vo.*;
import com.jiuji.oa.stock.pdapurchase.enums.KcCheckEnum;
import com.jiuji.oa.stock.productkc.service.IProductKcService;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@DS("oanewWrite")
public class CrossAuthTransferSubServiceImpl extends ServiceImpl<CrossAuthTransferSubMapper, CrossAuthTransferSub> implements CrossAuthTransferSubService {


    @Resource
    private AbstractCurrentRequestComponent currentRequestComponent;
    @Resource
    private CrossAuthTransferDetailService detailService;
    @Resource
    private IProductKcService kcService;
    @Resource
    private IProductMkcService mkcService;
    @Resource
    private CrossAuthTransferSubLogService logService;
    @Resource
    private IAreaInfoService areaInfoService;
    @Resource
    private AuthorizeService authorizeService;
    @Resource
    private CrossAuthTransferConfigService transferConfigService;
    @Resource
    private ChannelKindLinkService channelKindLinkService;
    @Resource
    private IProductInfoService productInfoService;
    @Resource
    private ISysConfigService sysConfigService;
    @Resource
    private CategoryService categoryService;
    /**
     * 零元交易 cid
     */
    private static final Integer ZERO_YUAN_TRANSACTION_CONFIG_CID = 75;
    /**
     * 零元交易 pid
     */
    private static final Integer ZERO_YUAN_TRANSACTION_CONFIG_PID = 76;
    /**
     * 零元交易 ppid
     */
    private static final Integer ZERO_YUAN_TRANSACTION_CONFIG_PPID = 77;


    @Override
    public IPage<ProductSubmitInfo> selectProductSubmitInfo(ProductReq req) {
        //空格去除
        String key = req.getKey();
        Optional.ofNullable(key).ifPresent(item-> req.setKey(item.trim()));
        if(StringUtils.isEmpty(key)&&
                CollectionUtils.isEmpty(req.getImeiList()) &&
                CollectionUtils.isEmpty(req.getCidList()) &&
                CollectionUtils.isEmpty(req.getBrandList())){
            throw new CustomizeException("请输入查询条件");
        }
        //传入参数校验(如果过是 ppid  和  productid那就进行数字校验)
        List<Integer> queryTypeList = Arrays.asList(SubmitReqEnums.PPID.getCode(), SubmitReqEnums.PRODUCTID.getCode(), SubmitReqEnums.MKC_ID.getCode());
        if(queryTypeList.contains(req.getQueryType())){
            try {
                 Integer.parseInt(req.getKey());
            }catch (Exception e){
                String comment = "请输入正确的" + SubmitReqEnums.PPID.getMessage() + "或" + SubmitReqEnums.PRODUCTNAME.getMessage()+ "或" + SubmitReqEnums.MKC_ID.getMessage();
                log.error(comment,e);
                throw new CustomizeException(comment);
            }
        }
        if(CollectionUtils.isNotEmpty(req.getImeiList()) && !StringUtils.isEmpty(req.getKey())){
            throw new CustomizeException("批量串号查询的时候，不能输入查询条件");
        }
        Page<ProductSubmitInfo> page = new Page<>();
        page.setCurrent(Optional.ofNullable(req.getCurrent()).orElse(NumberConstant.ONE))
                .setSize(Optional.ofNullable(req.getSize()).orElse(NumberConstant.TEN));
        Page<ProductSubmitInfo> productSubmitInfoPage = this.baseMapper.selectProductSubmitInfo(req, page);
        List<ProductSubmitInfo> records = productSubmitInfoPage.getRecords();
        return productSubmitInfoPage;
    }

    @Override
    public List<ProductSubmitInfo> selectProductKcList(ProductReq req) {
        return this.baseMapper.selectProductKcList(req);
    }

    @Override
    public CheckImeiRes checkImei(CheckImeiReq req) {
        List<String> imeiList = req.getImeiList();
        if(CollectionUtils.isEmpty(imeiList)){
            throw new CustomizeException("串号查询不能为空");
        }
        CheckImeiRes checkImeiRes = new CheckImeiRes();
        List<ImeiErrorInfo> errorImeiList = new ArrayList<>();
        StringJoiner errorMsg = new StringJoiner(",");
        List<CheckImeiInfo> checkImeiInfos = this.baseMapper.selectCheckImeiInfo(req);
        //查询为空的情况
        if(CollectionUtils.isEmpty(checkImeiInfos)){
            imeiList.forEach(item->{
                ImeiErrorInfo imeiErrorInfo = new ImeiErrorInfo();
                String message = ErrorImeiMsgEnums.ILLEGAL_OR_FLAW.getMessage();
                imeiErrorInfo.setImei(item)
                        .setMsg(message);
                errorImeiList.add(imeiErrorInfo);
                errorMsg.add(item+":"+message);
            });
            checkImeiRes.setErrorImeiList(errorImeiList)
                    .setErrorMsg(errorMsg.toString());
            return checkImeiRes;
        }
        Map<String, CheckImeiInfo> checkImeiInfoMap = checkImeiInfos.stream().collect(Collectors.toMap(CheckImeiInfo::getImei, Function.identity(), (n1, n2) -> n2));
        imeiList.forEach(item->{
            CheckImeiInfo checkImeiInfo = checkImeiInfoMap.get(item);
            ImeiErrorInfo imeiErrorInfo = new ImeiErrorInfo();
            if(ObjectUtil.isNull(checkImeiInfo)){
                //判断瑕疵库存以及非法串号
                String message = ErrorImeiMsgEnums.ILLEGAL_OR_FLAW.getMessage();
                imeiErrorInfo.setImei(item)
                        .setMsg(message);
                errorImeiList.add(imeiErrorInfo);
                errorMsg.add(item+":"+message);
            }else {
                StringJoiner detailInfo = new StringJoiner(",");
                //库存判断
                Integer kcCheck = checkImeiInfo.getKcCheck();
                if(!KcCheckEnum.STOCK.getCode().equals(kcCheck)){
                    detailInfo.add(ErrorImeiMsgEnums.NO_STOCK.getMessage());
                }
                //订单锁定判断
                Optional.ofNullable(checkImeiInfo.getBasketId()).ifPresent(basketId-> detailInfo.add(String.format(ErrorImeiMsgEnums.ORDER_LOCK.getMessage(),basketId)));
                //样机判断
//                if(checkImeiInfo.getMouldFlag()){
//                    detailInfo.add(ErrorImeiMsgEnums.IS_MOULDFLAG.getMessage());
//                }
                //门店判断
                Integer areaId = Optional.ofNullable(checkImeiInfo.getAreaid()).orElse(Integer.MAX_VALUE);
                if(!areaId.equals(req.getTransferAreaId())){
                    detailInfo.add(ErrorImeiMsgEnums.NO_AREA.getMessage());
                }
                //判断如果过存在错误信息
                if(!detailInfo.toString().isEmpty()){
                    String message = item + ":" + detailInfo.toString();
                    imeiErrorInfo.setImei(item)
                            .setMsg(message);
                    errorImeiList.add(imeiErrorInfo);
                    errorMsg.add(message);
                }
            }
        });
        checkImeiRes.setErrorImeiList(errorImeiList)
                .setErrorMsg(errorMsg.toString());

        if(CollectionUtils.isEmpty(errorImeiList)){
            ProductReq productReq =new ProductReq();
            productReq.setSize(Integer.MAX_VALUE)
                    .setImeiList(req.getImeiList())
                    .setTransferAreaId(req.getTransferAreaId());
            checkImeiRes.setPageInfo(selectProductSubmitInfo(productReq));
        }
        return checkImeiRes;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delTransferSub(Integer id) {
        OaUserBO oaUserBO = Optional.ofNullable(currentRequestComponent.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("登录信息失效，请重新登录"));
        CrossAuthTransferSub transferSub = Optional.ofNullable(this.getById(id)).orElseThrow(() -> new CustomizeException("调拨单查询为空"));
        if(!TransferStateEnums.SUBMITTED.getCode().equals(transferSub.getStatus())){
            throw new CustomizeException("只有调拨单状态为"+TransferStateEnums.SUBMITTED.getMessage()+"才能删除");
        }
        boolean update = this.lambdaUpdate().eq(CrossAuthTransferSub::getId, id)
                .eq(CrossAuthTransferSub::getStatus, TransferStateEnums.SUBMITTED.getCode())
                .set(CrossAuthTransferSub::getStatus, TransferStateEnums.CANCELED.getCode())
                .set(CrossAuthTransferSub::getDelTime,LocalDateTime.now())
                .set(CrossAuthTransferSub::getDelUser,oaUserBO.getUserName())
                .update();
        if(!update){
            throw new CustomizeException("调拨单删除失败");
        }
        logService.saveLog(new SaveLogVO("订单删除",id));
        List<CrossAuthTransferDetail> detailList = detailService.lambdaQuery().eq(CrossAuthTransferDetail::getTransferId, id).list();
        unlockProduct(detailList);
    }



    /**
     * 调拨单提交
     * @param submitOrderVO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SubmitResultVO submit(SubmitOrderVO submitOrderVO) {
        //详情数据校验
        checkData(submitOrderVO);
        SubmitResultVO submitResultVO = new SubmitResultVO();
        submitOrderVO.setOaUserBO(Optional.ofNullable(currentRequestComponent.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("登录超时，请重新登录")));
        //主表的保存
        CrossAuthTransferSub crossAuthTransferSub = saveTransferSubInfo(submitOrderVO);
        submitResultVO.setTransferId(crossAuthTransferSub.getId());
        //主表日志保存
        logService.saveLog(new SaveLogVO("提交调拨单",crossAuthTransferSub.getId()));
        //详情表的保存
        saveTransferSubDetailInfo(submitOrderVO, crossAuthTransferSub);

        return submitResultVO;
    }

    @Override
    public AuthRes selectAuthByAreaId(AuthReq req) {
        List<Integer> areaIdList = req.getAreaIdList();
        if(CollectionUtils.isEmpty(areaIdList)){
            throw new CustomizeException("查询门店信息不能为空");
        }

        List<Areainfo> list = areaInfoService.lambdaQuery().in(Areainfo::getId, areaIdList).list();
        if(CollectionUtils.isEmpty(list)){
            throw new CustomizeException("门店为空");
        }
        AuthRes authRes = new AuthRes();
        List<AuthAreaInfo> authAreaInfos = new ArrayList<>();
        list.forEach(item->{
            Authorize authorize = Optional.ofNullable(authorizeService.getById(item.getAuthorizeid()))
                    .orElseThrow(() -> new CustomizeException("授权查询为空"));
            AuthAreaInfo authAreaInfo = new AuthAreaInfo();
            authAreaInfo.setAreaId(item.getId())
                    .setAuthId(authorize.getId())
                    .setAuthName(authorize.getName()+"公司（授权名称）");
            authAreaInfos.add(authAreaInfo);
        });
        authRes.setAreaInfoList(authAreaInfos);
        return authRes;
    }





    /**
     * 详情数据校验
     * @param submitOrderVO
     */
    private void checkData(SubmitOrderVO submitOrderVO){
        List<SubmitOrderDetailVO> detailVOList = submitOrderVO.getDetailVOList();
        if(CollectionUtils.isEmpty(detailVOList)){
            throw new CustomizeException("调拨详情不能为空");
        }
        detailVOList.forEach(item->{
            Assert.isTrue(ObjectUtil.isNotNull(item.getPpid()), "商品ppid不能为空");
            Assert.isTrue(ObjectUtil.isNotNull(item.getIsmobile()), "商品大小件属性不能为空");
            Assert.isTrue(ObjectUtil.isNotNull(item.getMkcId()), "商品库存id不能为空");
            Assert.isTrue(ObjectUtil.isNotNull(item.getCount()), "商品调拨数量不能为空");
            Assert.isTrue(ObjectUtil.isNotNull(item.getTransferPrice()), "商品调拨价格不能为空");
            Assert.isTrue(ObjectUtil.isNotNull(item.getCostPrice()) , "商品成本价格不能为空");
        });
        OaUserBO userBO = Optional.ofNullable(currentRequestComponent.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("登录超时，请重新登录"));
        //调拨价格判断
        BigDecimal transferPriceTotal = detailVOList.stream().map(item -> Optional.ofNullable(item.getTransferPrice()).orElse(BigDecimal.ZERO)
                        .multiply(BigDecimal.valueOf(item.getCount())))
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO);
        //如果调拨价格为0那就进行判断是不是所有商品都是支持0元交易
        if(transferPriceTotal.compareTo(BigDecimal.ZERO)==0){
            List<Integer> zeroYuanTransactionCidList =new ArrayList<>();
            List<Integer> zeroYuanTransactionPidList =new ArrayList<>();
            List<Integer> zeroYuanTransactionPpidList =new ArrayList<>();
            SysConfig sysConfigCid = Optional.ofNullable(sysConfigService.getByCodeAndXtenant(ZERO_YUAN_TRANSACTION_CONFIG_CID, Convert.toLong(userBO.getXTenant()))).orElse(new SysConfig());
            String valueCids = sysConfigCid.getValue();
            if(!StringUtils.isEmpty(valueCids)){
                zeroYuanTransactionCidList.addAll(categoryService.selectChildrenCategory(Arrays.stream(valueCids.split(","))
                        .map(Integer::valueOf).collect(Collectors.toList())));
            }
            Optional.ofNullable(sysConfigService.getByCodeAndXtenant(ZERO_YUAN_TRANSACTION_CONFIG_PID, Convert.toLong(userBO.getXTenant()))).ifPresent(sysConfig -> {
                String valuePid = sysConfig.getValue();
                if(!StringUtils.isEmpty(valuePid)){
                    zeroYuanTransactionPidList.addAll(Arrays.stream(valuePid.split(",")).map(Integer::valueOf).collect(Collectors.toList()));
                }

            });
            Optional.ofNullable(sysConfigService.getByCodeAndXtenant(ZERO_YUAN_TRANSACTION_CONFIG_PPID, Convert.toLong(userBO.getXTenant()))).ifPresent(sysConfig -> {
                String valuePpid = sysConfig.getValue();
                if(!StringUtils.isEmpty(valuePpid)){
                    zeroYuanTransactionPpidList.addAll(Arrays.stream(valuePpid.split(",")).map(Integer::valueOf).collect(Collectors.toList()));
                }

            });
            List<Long> ppidList = detailVOList.stream().map(item->Convert.toLong(item.getPpid())).collect(Collectors.toList());
            Map<Long, ProductInfoVo> longProductInfoVoMap = productInfoService.listByppidsNew(ppidList);
            for (SubmitOrderDetailVO product:detailVOList){
                Long ppid = Convert.toLong(product.getPpid());
                ProductInfoVo productInfoVo = longProductInfoVoMap.getOrDefault(ppid, new ProductInfoVo());
                if(zeroYuanTransactionCidList.contains(Convert.toInt(productInfoVo.getCid())) ||
                        zeroYuanTransactionPidList.contains(Convert.toInt(productInfoVo.getProductId())) ||
                        zeroYuanTransactionPpidList.contains(Convert.toInt(ppid))){
                    continue;
                }else{
                    throw new CustomizeException("商品ppid："+product.getPpid()+"不支持0元交易");
                }
            }
        } else if (transferPriceTotal.compareTo(BigDecimal.ZERO)<0){
            throw new CustomizeException("调拨价格不能小于0");
        }
        Integer fromAuthId = submitOrderVO.getFromAuthId();
        Integer toAuthId = submitOrderVO.getToAuthId();
        if(fromAuthId.equals(toAuthId)){
            throw new CustomizeException("同授权门店不可使用快授权调拨");
        }
        //进行渠道的合作中状态校验
        List<CrossAuthTransferConfig> list = transferConfigService.lambdaQuery().eq(CrossAuthTransferConfig::getFromAuthId, submitOrderVO.getFromAuthId())
                .eq(CrossAuthTransferConfig::getToAuthId, submitOrderVO.getToAuthId())
                .list();
        if(CollectionUtils.isNotEmpty(list)){
            CrossAuthTransferConfig crossAuthTransferConfig = list.get(0);
            Integer channelId = crossAuthTransferConfig.getChannelId();
            List<ChannelKindLink> channelKindLinks = channelKindLinkService.lambdaQuery().eq(ChannelKindLink::getChannelId, channelId).list();
            if(CollectionUtils.isEmpty(channelKindLinks)){
                throw new CustomizeException("授权渠道为空");
            }
            List<Integer> channelKindList = Arrays.asList(ChannelKindEnum.MOBILE.getCode(), ChannelKindEnum.MOBILE_ACCESSORIES.getCode(), ChannelKindEnum.MAINTENANCE_ACCESSORIES.getCode());
            List<ChannelKindLink> collect = channelKindLinks.stream().filter(item -> channelKindList.contains(item.getKind())).collect(Collectors.toList());
            if(channelKindList.size()!=collect.size()){
                throw new CustomizeException("授权渠道业务类型缺失，请补全 大件、小件、维修配件三种业务类型");
            }
            collect.forEach(item->{
                Integer channelState = item.getChannelState();
                //合作中状态判断
                if(!ChannelCooperationStatusEnum.COOPERATION.getCode().equals(channelState)){
                    String value = ChannelKindEnum.getValue(item.getKind());
                    throw new CustomizeException("渠道业务类型："+value+"状态不在合作中");
                }
            });
        }
        //门店管控校验
        Integer toAreaId = submitOrderVO.getToAreaId();
        Areainfo toAreainfo = Optional.ofNullable(areaInfoService.getById(toAreaId)).orElse(new Areainfo());
        Integer toIsControl = Optional.ofNullable(toAreainfo.getIsControl()).orElse(NumberConstant.ZERO);
        if(NumberConstant.ONE.equals((toIsControl >> 1) & 1)){
            throw new CustomizeException("门店："+toAreainfo.getArea()+"为禁止调入门店，不允许使用跨授权调拨");
        }

        Integer fromAreaId = submitOrderVO.getFromAreaId();
        Areainfo fromAreainfo = Optional.ofNullable(areaInfoService.getById(fromAreaId)).orElse(new Areainfo());
        Integer fromIsControl = Optional.ofNullable(fromAreainfo.getIsControl()).orElse(NumberConstant.ZERO);
        if(NumberConstant.ONE.equals((fromIsControl >> 0) & 1)){
            throw new CustomizeException("门店："+fromAreainfo.getArea()+"为禁止采购门店，不允许使用跨授权调拨");
        }
    }

    /**
     * 详情表保存
     * @param submitOrderVO
     */
    @Transactional(rollbackFor = Exception.class)
    public List<CrossAuthTransferDetail> saveTransferSubDetailInfo(SubmitOrderVO submitOrderVO,CrossAuthTransferSub crossAuthTransferSub){
        List<SubmitOrderDetailVO> detailVOList = submitOrderVO.getDetailVOList();
        List<CrossAuthTransferDetail> detailList = detailVOList.stream().map((SubmitOrderDetailVO item) -> {
            CrossAuthTransferDetail crossAuthTransferDetail = new CrossAuthTransferDetail();
            BeanUtils.copyProperties(item, crossAuthTransferDetail);
            crossAuthTransferDetail.setTransferId(crossAuthTransferSub.getId());
            crossAuthTransferDetail.setCreateTime(LocalDateTime.now());
            crossAuthTransferDetail.setCreateUser(submitOrderVO.getOaUserBO().getUserName());
            return crossAuthTransferDetail;
        }).collect(Collectors.toList());
        saveCrossAuthTransferDetailList(detailList);
        return detailList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unlockProduct(List<CrossAuthTransferDetail> detailList){
        if(CollectionUtils.isNotEmpty(detailList)){
            //进行库存的解锁
            List<CrossAuthTransferDetail> detailListSmall=new ArrayList<>();
            List<CrossAuthTransferDetail> detailListBig=new ArrayList<>();
            detailList.forEach(item->{
                if(item.getIsmobile()){
                    detailListBig.add(item);
                } else {
                    detailListSmall.add(item);
                }
            });
            //小件库存解锁
            kcService.unlockProductTransferKc(detailListSmall);
            //大件库存解锁
            mkcService.unlockProductTransferKc(detailListBig);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void lockProduct(List<CrossAuthTransferDetail> detailList){
        if(CollectionUtils.isNotEmpty(detailList)){
            //划分大小件商品分别进行库存锁定
            List<CrossAuthTransferDetail> detailListSmall=new ArrayList<>();
            List<CrossAuthTransferDetail> detailListBig=new ArrayList<>();
            detailList.forEach(item->{
                if(item.getIsmobile()){
                    detailListBig.add(item);
                } else {
                    detailListSmall.add(item);
                }
            });
            //小件库存锁定
            kcService.lockProductTransferKc(detailListSmall);
            //大件库存锁定
            mkcService.lockProductTransferKc(detailListBig);
        }
    }

    /**
     *
     * @param detailList
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveCrossAuthTransferDetailList(List<CrossAuthTransferDetail> detailList){
        if(CollectionUtils.isEmpty(detailList)){
            return;
        }
        //库存锁定
        lockProduct(detailList);
        //详情表的保存
        detailList.forEach(detailService::save);
        //详情表相关日志记录
        StringJoiner joiner = new StringJoiner(",");
        detailList.forEach(item->{
            String format = String.format("添加调拨商品ppid:%s,数量:%s,调拨单价:%s", item.getPpid(), item.getCount(), item.getTransferPrice());
            joiner.add(format);
        });
        Integer transferId = detailList.get(0).getTransferId();
        logService.saveLog(new SaveLogVO(joiner.toString(),transferId));
    }

    @Override
    public List<VerifyProductDetailRes> verifyProduct(MultipartFile file,Integer transferAreaId) throws IOException {

        //获取到文件流
        VerifyProductListener verifyProductListener = new VerifyProductListener();
        verifyProductListener.setTransferAreaId(transferAreaId);
        verifyProductListener.setProductKcService(kcService);
        verifyProductListener.setProductInfoService(productInfoService);
        InputStream inputStream = file.getInputStream();
        EasyExcelFactory.read(inputStream, verifyProductListener).sheet().doRead();
        //获取校验结果
        List<VerifyProductDetailRes> verifyProductDetailResList = verifyProductListener.getVerifyProductDetailResList();
        verifyProductListener.setVerifyProductDetailResList(new ArrayList<>());
        return verifyProductDetailResList;

    }

    /**
     * 保存跨域调拨主表
     * @param submitOrderVO
     * @return
     */
    private CrossAuthTransferSub saveTransferSubInfo(SubmitOrderVO submitOrderVO){
        CrossAuthTransferSub crossAuthTransferSub = new CrossAuthTransferSub();
        //复制基本的数据
        BeanUtils.copyProperties(submitOrderVO,crossAuthTransferSub);
        //设置提交人以及提交基本信息
        crossAuthTransferSub.setCreateTime(LocalDateTime.now())
                .setStatus(TransferStateEnums.SUBMITTED.getCode())
                .setCreateStaffId(submitOrderVO.getOaUserBO().getUserId())
                .setCreateUser(submitOrderVO.getOaUserBO().getUserName());
        //计算总调拨价格和总成本价
        List<SubmitOrderDetailVO> detailVOList = submitOrderVO.getDetailVOList();
        crossAuthTransferSub.setTotalCost(detailVOList.stream()
                .map(item-> item.getCostPrice().multiply(new BigDecimal(item.getCount().toString())))
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        crossAuthTransferSub.setTotalTransferPrice(detailVOList.stream()
                .map(item-> item.getTransferPrice().multiply(new BigDecimal(item.getCount().toString())))
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        boolean save = this.save(crossAuthTransferSub);
        if(!save){
            throw new CustomizeException("调拨主表保存失败");
        }
        return crossAuthTransferSub;
    }
}
