package com.jiuji.oa.stock.distributionOfGoods.vo;

import lombok.Data;
import lombok.experimental.Accessors;
/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ProportionOfSalesVolumeVO {


    private Integer code;
    private Integer parentCode;
    private Integer rank;
    /**
     * 省
     */
    private Integer pid;
    /**
     * 市
     */
    private Integer cid;
    /**
     * 区
     */
    private Integer zid;
    /**
     * 门店名称 area+(level)
     */
    private String areaName;
    /**
     * 门店id
     */
    private Integer areaId;
    /**
     * 当天销量
     */
    private Integer daySales;
    /**
     * 当天销量比
     */
    private String daySalesRatio;
    /**
     * n天销量
     */
    private Integer daySearchSales;
    /**
     * n天销量比
     */
    private String daySearchSalesRatio;
    /**
     * 15天销量
     */
    private Integer dayFifteenSales;
    /**
     * 15天销量比
     */
    private String dayFifteenSalesRatio;

    /**
     * 库存
     */
    private Integer stock;
    /**
     * 在途
     */
    private Integer onTheWay;
    /**
     * 总量
     */
    private Integer total;
    /**
     * 现货率
     */
    private String goodsInStockRatio;
}
