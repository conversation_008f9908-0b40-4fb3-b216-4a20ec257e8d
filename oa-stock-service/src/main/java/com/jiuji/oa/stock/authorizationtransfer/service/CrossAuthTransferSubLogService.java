package com.jiuji.oa.stock.authorizationtransfer.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.stock.authorizationtransfer.entity.CrossAuthTransferSubLog;
import com.jiuji.oa.stock.authorizationtransfer.vo.SaveLogVO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface CrossAuthTransferSubLogService extends IService<CrossAuthTransferSubLog> {

    /**
     * 日志记录
     * @param saveLogVO
     */
    void saveLog(SaveLogVO saveLogVO);

    /**
     * 日志查询
     * @param id
     * @return
     */
    List<CrossAuthTransferSubLog> findLogById(Integer id);
}
