package com.jiuji.oa.stock.authorizationtransfer.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("cross_auth_transfer_detail")
public class CrossAuthTransferDetail {

    @TableId(value = "detail_id", type = IdType.AUTO)
    private Integer detailId;

    private Integer ppid;

    @TableField("transfer_id")
    private Integer transferId;

    /**
     * 大小件
     */
    private Boolean ismobile;

    @TableField("mkc_id")
    private Integer mkcId;

    /**
     * 数量
     */
    private Integer count;

    /**
     * 成本
     */
    @TableField("cost_price")
    private BigDecimal costPrice;

    /**
     * 调拨成本
     */
    @TableField("transfer_price")
    private BigDecimal transferPrice;

    @TableField("record_id")
    private Integer recordId;

    @TableLogic
    private Boolean isDel;

    /**
     * 创建人
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;


}
