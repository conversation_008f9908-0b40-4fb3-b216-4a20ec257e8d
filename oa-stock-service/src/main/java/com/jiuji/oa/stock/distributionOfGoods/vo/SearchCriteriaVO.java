package com.jiuji.oa.stock.distributionOfGoods.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors
public class SearchCriteriaVO {

    /**
     * 销售开始时间
     */
    private LocalDateTime salesStartTime;

    /**
     * 销售结束时间
     */
    private LocalDateTime salesEndTime;

    /**
     * 搜索类型
     */
    private Integer searchTermType;

    /**
     * 搜索值
     */
    private List<Long> searchTermValue;

    /**
     * 门店类别
     */
    private List<Integer> kind1List;

    /**
     * 区域门店
     */
    private List<Integer> areaIdList;

    /**
     * 门店属性
     */
    private List<Integer> attributeList;

    /**
     * 区域门店
     */
    private List<Integer> dropDownAreaIdList;


}
