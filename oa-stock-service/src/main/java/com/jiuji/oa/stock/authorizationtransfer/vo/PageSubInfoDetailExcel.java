package com.jiuji.oa.stock.authorizationtransfer.vo;


import com.jiuji.oa.stock.authorizationtransfer.enums.TransferStateEnums;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class PageSubInfoDetailExcel extends PageSubInfoExcel {


    private String imei;

    private String productName;

    private String productColor;
    private String categoryName;
}
