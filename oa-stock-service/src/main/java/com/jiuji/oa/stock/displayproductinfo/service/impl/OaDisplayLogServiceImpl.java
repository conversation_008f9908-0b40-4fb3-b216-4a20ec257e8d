package com.jiuji.oa.stock.displayproductinfo.service.impl;

import com.jiuji.oa.logapi.pojo.entity.OaDisplayLog;
import com.jiuji.oa.stock.displayproductinfo.dto.OaDisplayLogDocument;
import com.jiuji.oa.stock.displayproductinfo.dto.OaDisplayLogDocument.Conts;
import com.jiuji.oa.stock.displayproductinfo.repository.OaDisplayLogRepository;
import com.jiuji.oa.stock.displayproductinfo.service.IOaDisplayLogService;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

/**
 * <p>
 *
 * @description: OaDisplayLogServiceImpl
 * </p>
 * @author: David
 * @create: 2021-05-27 17:52
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OaDisplayLogServiceImpl implements IOaDisplayLogService {

    @Resource(name = "OaDisplayLogServiceImplFromTidb")
    private com.jiuji.oa.logapi.service.IOaDisplayLogService service;
    private final OaDisplayLogRepository oaDisplayLogRepository;

    @Override
    public OaDisplayLogDocument addLog(Integer id, String comment, String inUser) {
        Assert.notNull(id, "陈列Id不能为空");
        Assert.notNull(comment, "备注内容不能为空");
        Assert.notNull(inUser, "用户不能为空");

        OaDisplayLog oaDisplayLog = new OaDisplayLog();
        LocalDateTime localDateTime = LocalDateTime.now();
        oaDisplayLog.setComment(comment)
                .setType(0)
                .setSubId(id.longValue())
                .setInUser(inUser)
                .setDTime(localDateTime)
                .setCreateTime(localDateTime)
                .setUpdateTime(localDateTime);
        service.save(oaDisplayLog);
        List<Conts> contList = new ArrayList<>();
        Conts cont = new Conts();
        cont.setComment(oaDisplayLog.getComment());
        cont.setInUser(oaDisplayLog.getInUser());
        cont.setDTime(oaDisplayLog.getDTime());
        cont.setType(oaDisplayLog.getType());
        cont.setSubId(oaDisplayLog.getSubId().intValue());
        cont.setShowType(oaDisplayLog.getShowType());
        contList.add(cont);

        OaDisplayLogDocument oaDisplayLogDocument = new OaDisplayLogDocument();
        oaDisplayLogDocument.setId(id);
        oaDisplayLogDocument.setConts(contList);


        //todo 双写
        OaDisplayLogDocument logDocument;
        Optional<OaDisplayLogDocument> oldLogDocument = oaDisplayLogRepository.findById(id);
        logDocument = oldLogDocument.orElseGet(OaDisplayLogDocument::new);
        Conts logCont = new Conts();
        logCont.setComment(comment);
        logCont.setType(0);
        logCont.setSubId(id);
        logCont.setInUser(inUser);
        logCont.setDTime(LocalDateTime.now());
        logDocument.setId(id);
        if (CollectionUtils.isEmpty(logDocument.getConts())) {
            logDocument.setConts(Collections.singletonList(logCont));
        } else {
            logDocument.getConts().add(logCont);
        }
        return oaDisplayLogDocument;
    }
}