package com.jiuji.oa.stock.authorizationtransfer.vo;


import com.jiuji.oa.nc.common.bo.OaUserBO;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SubmitOrderVO {


    private Integer id;


    /**
     * 发货门店id
     */
    @NotNull(message = "发货门店id不能为空")
    private Integer fromAreaId;

    /**
     * 收货门店id
     */
    @NotNull(message = "收货门店id不能为空")
    private Integer toAreaId;

    /**
     * 发货授权id
     */
    @NotNull(message = "发货授权id不能为空")
    private Integer fromAuthId;

    /**
     * 收货授权id
     */
    @NotNull(message = "收货授权id不能为空")
    private Integer toAuthId;

    /**
     * 标题
     */
    @NotNull(message = "标题不能为空")
    private String title;

    /**
     * 商品详情
     */
    private List<SubmitOrderDetailVO> detailVOList;

    /**
     * 登录信息
     */
    private OaUserBO oaUserBO;
}
