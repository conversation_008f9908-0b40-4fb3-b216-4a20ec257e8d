package com.jiuji.oa.stock.nationalSupplement.req;

import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class DeclarationCallbackReq {
    /**
     * 订单号
     */
    @NotNull(message = "订单号不能为空")
    private Integer subId;

    /**
     * 申报状态
     */
    @NotNull(message = "申报状态不能为空")
    private Integer states;

    /**
     * 申报状态说明
     */
    private String statesComment;
}
