package com.jiuji.oa.stock.authorizationtransfer.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.nc.common.bo.OaUserBO;
import com.jiuji.oa.nc.common.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.nc.common.exception.CustomizeException;
import com.jiuji.oa.stock.authorizationtransfer.entity.CrossAuthTransferSubLog;
import com.jiuji.oa.stock.authorizationtransfer.mapper.CrossAuthTransferSubLogMapper;
import com.jiuji.oa.stock.authorizationtransfer.service.CrossAuthTransferSubLogService;
import com.jiuji.oa.stock.authorizationtransfer.vo.SaveLogVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@DS("oanewWrite")
public class CrossAuthTransferSubLogServiceImpl extends ServiceImpl<CrossAuthTransferSubLogMapper, CrossAuthTransferSubLog> implements CrossAuthTransferSubLogService {

    @Resource
    private AbstractCurrentRequestComponent currentRequestComponent;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveLog(SaveLogVO saveLogVO) {
        OaUserBO oaUserBO = Optional.ofNullable(currentRequestComponent.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("登录信息超时，请重新登录"));
        CrossAuthTransferSubLog crossAuthTransferSubLog = new CrossAuthTransferSubLog();
        crossAuthTransferSubLog.setSubId(saveLogVO.getSubId())
                .setComment(saveLogVO.getComment())
                .setCreateTime(LocalDateTime.now())
                .setCreateUser(oaUserBO.getUserName())
                .setCreateUserId(oaUserBO.getUserId());
        boolean save = this.save(crossAuthTransferSubLog);
        if(!save){
            throw new CustomizeException("日志保存失败");
        }
    }

    @Override
    public List<CrossAuthTransferSubLog> findLogById(Integer id) {
        return this.lambdaQuery()
                .eq(CrossAuthTransferSubLog::getSubId,id)
                .orderByAsc(CrossAuthTransferSubLog::getCreateTime)
                .list();
    }
}
