package com.jiuji.oa.stock.accountingRecords.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.stock.accountingRecords.entity.AccountingRecords;
import com.jiuji.oa.stock.accountingRecords.entity.AreaGuestbookAccount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AreaGuestbookAccountMapper  extends BaseMapper<AreaGuestbookAccount> {


    /**
     * 查询相同 销售门店和库存门店在同一条数据的
     * @param saleAreaId
     * @param purchaseAreaId
     * @return
     */
    List<AreaGuestbookAccount> selectSameData(@Param(value = "saleAreaId") Integer saleAreaId, @Param(value = "purchaseAreaId") Integer purchaseAreaId);

    /**
     * 查询相同 销售门店和库存门店在同一条数据的
     * @param saleAreaId
     * @param purchaseAreaId
     * @return
     */
    List<AreaGuestbookAccount> selectSameAreaData(@Param(value = "saleAreaId") Integer saleAreaId, @Param(value = "purchaseAreaId") Integer purchaseAreaId);


    /**
     * 销售门店查询映射门店
     * @param saleAreaId
     * @return
     */
    List<AreaGuestbookAccount> selectMappingAreaGuestbookAccount(@Param(value = "saleAreaId") Integer saleAreaId);
}
