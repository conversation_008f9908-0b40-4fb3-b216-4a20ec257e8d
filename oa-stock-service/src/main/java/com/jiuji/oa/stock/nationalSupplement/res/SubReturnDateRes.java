package com.jiuji.oa.stock.nationalSupplement.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 订单退货日期响应
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "订单退货日期响应")
public class SubReturnDateRes {

    private Integer subId;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime returnDate;
} 