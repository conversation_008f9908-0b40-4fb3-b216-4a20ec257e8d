package com.jiuji.oa.stock.nationalSupplement.controller;

import cn.hutool.json.JSONUtil;
import com.jiuji.cloud.stock.vo.request.SelectAgencyReq;
import com.jiuji.cloud.stock.vo.response.SelectAgencyRes;
import com.jiuji.oa.stock.nationalSupplement.req.DeclarationDataSaasReq;
import com.jiuji.oa.stock.nationalSupplement.service.AutomaticDeclarationSaasService;
import com.jiuji.oa.stock.publiccheck.annotation.CheckHeader;
import com.jiuji.tc.common.vo.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/AutomaticDeclarationSaasController")
public class AutomaticDeclarationSaasController {


    @Resource
    private AutomaticDeclarationSaasService automaticDeclarationSaasService;

    /**
     * 获取申报数据
     * @return
     */
    @CheckHeader
    @PostMapping("/selectDeclarationDataSaas/v1")
    public R<List<Integer>> selectDeclarationDataSaas(@RequestBody @Validated DeclarationDataSaasReq dataSaasReq) {
        List<Integer> subIds = automaticDeclarationSaasService.selectDeclarationDataSaas(dataSaasReq);
        log.warn("saas获取申报数据传入参数：{}，返回结果：{}", JSONUtil.toJsonStr(dataSaasReq), JSONUtil.toJsonStr(subIds));
        R<List<Integer>> success = R.success(subIds);
        Map<String, Object> exData = new HashMap<>();
        Integer tenantId = automaticDeclarationSaasService.getTenantId(dataSaasReq);
        exData.put("tenant_id",tenantId);
        success.setExData(exData);
        return success;
    }


    /**
     * 代办 查询
     * @return
     */

    @PostMapping("/selectAgency/v1")
    public R<SelectAgencyRes> selectAgency(@RequestBody @Validated SelectAgencyReq selectAgencyReq) {
        SelectAgencyRes result = automaticDeclarationSaasService.selectAgency(selectAgencyReq);
        log.warn("代办 查询传入参数：{}，返回结果：{}", JSONUtil.toJsonStr(selectAgencyReq), JSONUtil.toJsonStr(result));
        return R.success(result);
    }
}
