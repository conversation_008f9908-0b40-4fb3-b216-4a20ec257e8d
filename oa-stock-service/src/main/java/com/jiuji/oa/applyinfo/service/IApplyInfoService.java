/*
 *     Copyright © 2006 - 2020 九机网 All Rights Reserved
 *
 */

package com.jiuji.oa.applyinfo.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.applyinfo.entity.ApplyInfo;
import com.jiuji.oa.applyinfo.vo.req.ApplyInfoReq;
import com.jiuji.tc.common.vo.R;

/**
 * 批签Service
 *
 * <AUTHOR>
 * @date 2021-03-04 10:44:16
 */

public interface IApplyInfoService extends IService<ApplyInfo> {
    /**
     * 新增批签(校验)
     *
     * @param applyInfoReq
     * @return
     */
    R<String> saveApplyInfo(ApplyInfoReq applyInfoReq);

   /**
    * 根据渠道Id 查询最大Id
    * @param id
    * @return
    * */
   R<Long> getByChannelId(Integer id);

   Integer getCount(Long appilyId);

   ApplyInfo getApplyInfo(Long id);

}
