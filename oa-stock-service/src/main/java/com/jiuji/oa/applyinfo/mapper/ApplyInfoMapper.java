/*
 *    Copyright © 2006 - 2020 九机网 All Rights Reserved
 *
 */

package com.jiuji.oa.applyinfo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.applyinfo.entity.ApplyInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * ${comments}Mapper
 *
 * <AUTHOR> code generator
 * @date 2021-03-04 10:44:16
 */
@Mapper
public interface ApplyInfoMapper extends BaseMapper<ApplyInfo> {

    Long maxIdOfDate(@Param("date") Long date);

    Long getByChannelId(@Param("id") Integer id);
}
