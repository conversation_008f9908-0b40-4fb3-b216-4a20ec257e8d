package com.jiuji.oa.applyinfo.mapstruct;

import com.jiuji.oa.applyinfo.entity.ApplyInfo;
import com.jiuji.oa.applyinfo.vo.req.ApplyInfoReq;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
  * description 类型转换
  * <AUTHOR>
  * @create  2021/3/5 19:03
  **/
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ApplyInfoMapStruct {
    ApplyInfo toApplyInfo (ApplyInfoReq applyInfoReq);
}
