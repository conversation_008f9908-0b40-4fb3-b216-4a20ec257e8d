/*
 *     Copyright © 2006 - 2020 九机网 All Rights Reserved
 *
 */
package com.jiuji.oa.applyinfo.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ch999.common.util.utils.Exceptions;
import com.jiuji.oa.applyinfo.entity.ApplyInfo;
import com.jiuji.oa.applyinfo.mapper.ApplyInfoMapper;
import com.jiuji.oa.applyinfo.mapstruct.ApplyInfoMapStruct;
import com.jiuji.oa.applyinfo.service.IApplyInfoService;
import com.jiuji.oa.applyinfo.vo.req.ApplyInfoReq;
import com.jiuji.oa.orginfo.departinfo.client.DepartInfoClient;
import com.jiuji.oa.orginfo.departinfo.vo.DepartInfoVO;
import com.jiuji.oa.orginfo.userinfo.client.UserInfoClient;
import com.jiuji.oa.orginfo.userinfo.vo.Ch999UserVo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 批签ServiceImpl
 *
 * <AUTHOR> code generator
 * @date 2021-03-04 10:44:16
 */
@Service("IApplyInfoService")
@Slf4j
@DS("officeWrite")
@RequiredArgsConstructor
public class ApplyInfoServiceImpl extends ServiceImpl<ApplyInfoMapper, ApplyInfo> implements IApplyInfoService {

    private final static Integer LEVEL_ONE = 1;
    private final static Integer LEVEL_TWO = 2;
    private final static Integer LEVEL_THREE = 3;
    private final UserInfoClient userInfoClient;
    private final DepartInfoClient departInfoClient;
    private final ApplyInfoMapStruct applyinfoMapStruct;




    @Override
    @DSTransactional
    public R<String> saveApplyInfo(ApplyInfoReq applyInfoReq) {

        Integer inUserId = applyInfoReq.getInuser();
        String inUserName = applyInfoReq.getInusername();
        R<Ch999UserVo> checkVO;
        try {
            // 查询部门Id，部门名称
            R<Ch999UserVo> userVo = userInfoClient.getCh999UserByUserId(inUserId);
            if (userVo.getCode() != 0) {
                return R.error(ResultCode.WEBSERVER_ERROR, userVo.getUserMsg());
            }
            Integer inUserDepartCode = Optional.ofNullable(userVo).map(R::getData).map(Ch999UserVo::getDepartId)
                    .orElseThrow(() -> new RuntimeException("未查询到申请人部门Id"));
            R<DepartInfoVO> departInfoVO = departInfoClient.getByDepartId(inUserDepartCode);
            if (departInfoVO.getCode() != 0) {
                throw new RuntimeException(departInfoVO.getUserMsg());
            }
            String departName = Optional.ofNullable(departInfoVO).map(R::getData).map(DepartInfoVO::getName)
                    .orElseThrow(() -> new RuntimeException("未查询到申请人部门名称"));
            applyInfoReq.setInuserdepartment(departName);

            // 选取上一级审批人
            Map<Integer, String> levelMap = new HashMap<>(3);
            levelMap.put(ApplyInfoServiceImpl.LEVEL_ONE, Optional.ofNullable(departInfoVO.getData().getCurAdmin())
                    .orElseThrow(() -> new RuntimeException("未能查询到当前部门领导人")));
            levelMap.put(ApplyInfoServiceImpl.LEVEL_TWO, Optional.ofNullable(departInfoVO.getData().getPreAdmin())
                    .orElseThrow(() -> new RuntimeException("未能查询到上级部门领导人")));
            levelMap.put(ApplyInfoServiceImpl.LEVEL_THREE, Optional.ofNullable(departInfoVO.getData().getPreAdminF())
                    .orElseThrow(() -> new RuntimeException("未能查询到最高级部门领导人")));
            AtomicReference<String> checkName = new AtomicReference<>("");
            checkName.set(departInfoVO.getData().getCurAdmin());
            levelMap.forEach((key, value) -> {
                if (inUserName.equals(value)) {
                    if (!key.equals(ApplyInfoServiceImpl.LEVEL_THREE)) {
                        checkName.set(levelMap.get(key + 1));
                    } else {
                        checkName.set(inUserName);
                    }
                }
            });

            checkVO = userInfoClient.getCh999UserByUserName(checkName.get());
            if (checkVO.getCode() != 0) {
                throw new RuntimeException(checkVO.getUserMsg());
            }
            Optional.ofNullable(checkVO.getData()).ifPresent(x -> {
                applyInfoReq.setChecker(x.getCh999Id());
                applyInfoReq.setCheckername(checkName.get());
            });
        } catch (RuntimeException e) {
            log.error("查询，异常:{}", Exceptions.getStackTraceAsString(e));
            return R.error(ResultCode.SERVER_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("查询审批人，异常:{}", Exceptions.getStackTraceAsString(e));
            return R.error(ResultCode.SERVER_ERROR, "查询审批人错误!");
        }

        // 数据生成（UUID、ID、ApplyTime、currentStatus）
        applyInfoReq.setApplyid(UUID.randomUUID().toString().toUpperCase(Locale.ENGLISH));
        applyInfoReq.setId(maxIdOfDate(LocalDateTime.now()));
        applyInfoReq.setCurrentstatus(1);
        if (applyInfoReq.getApplytime() == null) {
            applyInfoReq.setApplytime(LocalDateTime.now());
        }
        boolean flag = this.save(applyinfoMapStruct.toApplyInfo(applyInfoReq));
        Assert.state(flag, "新增批签失败");
        return R.success("新增批签成功");
    }

    @Override
    public R<Long> getByChannelId(Integer qudaoChannelId) {
        if (qudaoChannelId == null) {
            return R.error(ResultCode.NO_DATA, "渠道Id不可以为空！");
        }
        try {
            Long id = this.baseMapper.getByChannelId(qudaoChannelId);
            return R.success(id);
        } catch (Exception e) {
            log.error("异常:{}", Exceptions.getStackTraceAsString(e));
            return R.error("查询Id最大值错误");
        }
    }

    @Override
    public Integer getCount(Long appilyId) {
        return this.lambdaQuery().eq(ApplyInfo::getIsdel, 0)
                .eq(ApplyInfo::getId, appilyId).count();
    }

    /**
     * 生成 时间 （年月日）+ 顺序 的最大Id
     *
     * @param localDateTime localDateTime
     * @return the long
     */
    public Long maxIdOfDate(LocalDateTime localDateTime) {
        int year = localDateTime.getYear();
        int month = localDateTime.getMonthValue();
        int dayOfMonthDay = localDateTime.getDayOfMonth();
        long queryData = year * 10000L + month * 100 + dayOfMonthDay;
        Long maxId = this.baseMapper.maxIdOfDate(queryData);
        if (maxId == null) {
            maxId = queryData * 1000 + 1;
        } else {
            maxId++;
        }
        return maxId;
    }

    @Override
    public ApplyInfo getApplyInfo(Long id){
        ApplyInfo applyInfo = this.lambdaQuery().eq(ApplyInfo::getId, id).list().stream().findFirst().orElse(null);
        return applyInfo;
    }
}
