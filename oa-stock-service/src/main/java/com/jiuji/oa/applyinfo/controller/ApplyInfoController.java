package com.jiuji.oa.applyinfo.controller;

import com.jiuji.oa.applyinfo.service.IApplyInfoService;
import com.jiuji.oa.applyinfo.vo.req.ApplyInfoReq;
import com.jiuji.tc.common.vo.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.Objects;
import javax.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 批签Controller
 *
 * <AUTHOR>
 * @date 2021-03-04 10:44:16
 */
@RestController
@AllArgsConstructor
@RequestMapping("/api/applyinfo")
@Api(value = "tapplyinfo", tags = "${comments}管理")
public class ApplyInfoController {

    private final IApplyInfoService IApplyInfoService;

    /**
     * 通过渠道id查询id
     *
     * @param qudaoChannelId id
     * @return R
     */
    @ApiOperation(value = "通过id查询", notes = "通过id查询")
    @GetMapping("/qudaoChannelId/{qudaoChannelId}")
    public R<Long> getByChannelId(@PathVariable("qudaoChannelId") Integer qudaoChannelId) {
        return IApplyInfoService.getByChannelId(qudaoChannelId);
    }

    /**
     * 新增批签
     *
     * @param applyInfoReq 批签DTO
     * @return R
     */
    @ApiOperation(value = "新增批签", notes = "新增批签")
    @PostMapping
    public R<String> save(@RequestBody @Valid ApplyInfoReq applyInfoReq, BindingResult result) {
        if (result.hasErrors()) {
            return R.error(Objects.requireNonNull(result.getFieldError()).getDefaultMessage());
        }
        return IApplyInfoService.saveApplyInfo(applyInfoReq);
    }
}
