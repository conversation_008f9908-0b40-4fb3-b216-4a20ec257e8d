/*
 *    Copyright © 2006 - 2020 九机网 All Rights Reserved
 *
 */

package com.jiuji.oa.applyinfo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 批签Entity
 *
 * <AUTHOR> code generator
 * @date 2021-03-04 10:44:16
 */
@Data
@TableName("T_ApplyInfo")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "批签")
public class ApplyInfo extends Model<ApplyInfo> {
private static final long serialVersionUID = 1L;


    /**
     * Id
     */
    @ApiModelProperty()
    private Long id;

    /**
     * 申请Id
     */
    @TableId(type = IdType.INPUT)
    @ApiModelProperty(value="申请Id")
    private String applyid;

    /**
     * 大分类
     */
    @ApiModelProperty(value="大分类")
    @NotNull(message = "大分类不能为空")
    private Integer categoryid;

    /**
     * 小分类
     */
    @ApiModelProperty(value="小分类")
    @NotNull(message = "小分类不能为空")
    private Integer scategoryid;

    /**
     * 申请人
     */
    @ApiModelProperty(value="申请人")
    @NotNull(message = "申请人不能为空")
    private Integer inuser;

    @ApiModelProperty(value="申请人名称")
    @NotNull(message = "申请人名称不能为空")
    private String inusername;

    /**
     * 审核人
     */
    @ApiModelProperty(value="审核人")
    private Integer checker;

    /**
     * 审核人姓名
     */
    @ApiModelProperty(value="审核人姓名")
    private String checkername;

    /**
     * 地区
     */
    @ApiModelProperty(value="地区")
    private String area;

    /**
     * 申请时间
     */
    @ApiModelProperty(value="申请时间")
    @NotNull(message = "申请时间不能为空")
    private LocalDateTime applytime;

    /**
     * 备注
     */
    @ApiModelProperty(value="备注")
    private String remark;
    /**
     * 当前状态
     */
    @ApiModelProperty(value="当前状态")
    @NotNull(message = "当前状态不能为空")
    private Integer currentstatus;
    /**
     * 申请人部门
     */
    @ApiModelProperty(value="申请人部门")
    @NotNull(message = "申请人部门不能为空")
    private String inuserdepartment;

    @ApiModelProperty()
    private String attachfiles;

    @ApiModelProperty()
    private String applytitle;

    @ApiModelProperty()
    @NotNull(message = "isdel不能为空")
    private Boolean isdel;


    /**
     * 数量"
     */
    @ApiModelProperty(value="数量")
    @NotNull(message = "数量不能为空")
    private BigDecimal amount;

    @ApiModelProperty()
    private String relatedapply;

    @ApiModelProperty()
    private Boolean haspreload;

    @ApiModelProperty()
    private LocalDateTime stime;

    @ApiModelProperty()
    private LocalDateTime etime;
    
    @ApiModelProperty()
    private String attchfiles1;
    @ApiModelProperty()
    private String oldinuserdepartment;

    @ApiModelProperty()
    private Integer zhichuid;

    @ApiModelProperty()
    private String acceptuser;

    @ApiModelProperty()
    private Integer acceptuserid;

    @ApiModelProperty()
    private Integer areaid;

    @ApiModelProperty()
    private String attchfiles1tmp;

    @ApiModelProperty()
    private String attachfilestmp;

    @ApiModelProperty()
    private Boolean isup;

    @ApiModelProperty()
    private String kfly;

    @ApiModelProperty()
    private String kfyjxg;

    @ApiModelProperty()
    private String kfmd;

    @ApiModelProperty()
    private String wishtime;

    @ApiModelProperty()
    private LocalDateTime planfinishtime;

    @ApiModelProperty()
    private Integer usehours;

    @ApiModelProperty()
    private Integer priority;

    @ApiModelProperty(value="申报CEO")
    @NotNull(message = "申报CEO不能为空")
    private Boolean notifyceo;

    @ApiModelProperty()
    private String daipeiuser;

    @ApiModelProperty()
    private Integer zhiji;

    @ApiModelProperty()
    private Integer kaoqintype;

    @ApiModelProperty()
    private LocalDateTime stime0;

    @ApiModelProperty()
    private LocalDateTime etime0;

    @ApiModelProperty()
    private LocalDateTime lastapplytime;

    @ApiModelProperty()
    private Integer lastcount;

    @ApiModelProperty()
    private Integer curcount;

    @ApiModelProperty()
    private String mobile;

    @ApiModelProperty()
    private Integer usertype;

    @ApiModelProperty()
    private Integer processstats;

    @ApiModelProperty()
    private Integer gytype;

    @ApiModelProperty()
    private String businessdestination;

    @ApiModelProperty()
    private Integer buyticket;

    @ApiModelProperty()
    private Integer tickettype;
    /**
     * 相关单号
     */
    @ApiModelProperty(value="相关单号")
    private String relatedids;

    @ApiModelProperty()
    private String dailizhiwu;

    @ApiModelProperty()
    private Boolean isnewflag;

    @ApiModelProperty()
    private String zhichuids;

    @ApiModelProperty()
    private Integer isapplyforsomeone;

    @ApiModelProperty()
    private String realapplicant;

    @ApiModelProperty()
    private String realapplicantid;

    @ApiModelProperty()
    private Integer qudaochannelid;

    @ApiModelProperty()
    private Long inuserdepartcode;

    @ApiModelProperty()
    private Integer departbianzhi;

    @ApiModelProperty()
    private String flightnumber;

    @ApiModelProperty()
    private LocalDateTime takeofftime;

    @ApiModelProperty()
    private String flightnumber2;

    @ApiModelProperty()
    private LocalDateTime takeofftime2;

    @ApiModelProperty()
    private Integer areaorganizekind;

    @ApiModelProperty()
    private String togther;


    }
