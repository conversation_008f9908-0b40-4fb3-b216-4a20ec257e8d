/*
 *    Copyright © 2006 - 2020 九机网 All Rights Reserved
 *
 */

package com.jiuji.oa.applyinfo.vo.req;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 批签Entity
 *
 * <AUTHOR> code generator
 * @date 2021-03-04 10:44:16
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ApplyInfoReq extends Model<ApplyInfoReq> {

    private String applyid;

    @NotNull(message = "大分类不能为空")
    private Integer categoryid;

    @NotNull(message = "小分类不能为空")
    private Integer scategoryid;

    @NotNull(message = "申请人不能为空")
    private Integer inuser;

    @NotNull(message = "申请人名称不能为空")
    private String inusername;

    private Integer checker;

    private String checkername;

    private String area;

    private LocalDateTime applytime;

    private String remark;

    private Integer currentstatus;

    @NotNull(message = "申请人部门不能为空")
    private String inuserdepartment;

    private String attachfiles;

    private String applytitle;

    @NotNull(message = "isdel不能为空")
    private Boolean isdel;

    private Long id;

    @NotNull(message = "数量不能为空")
    private BigDecimal amount;

    private String relatedapply;

    private Boolean haspreload;

    private LocalDateTime stime;

    private LocalDateTime etime;

    private String attchfiles1;

    private String oldinuserdepartment;

    private Integer zhichuid;

    private String acceptuser;

    private Integer acceptuserid;

    private Integer areaid;

    private String attchfiles1tmp;

    private String attachfilestmp;

    private Boolean isup;

    private String kfly;

    private String kfyjxg;

    private String kfmd;

    private String wishtime;

    private LocalDateTime planfinishtime;

    private Integer usehours;

    private Integer priority;

    @NotNull(message = "申报CEO不能为空")
    private Boolean notifyceo;

    private String daipeiuser;

    private Integer zhiji;

    private Integer kaoqintype;

    private LocalDateTime stime0;

    private LocalDateTime etime0;

    private LocalDateTime lastapplytime;

    private Integer lastcount;

    private Integer curcount;

    private String mobile;

    private Integer usertype;

    private Integer processstats;

    private Integer gytype;

    private String businessdestination;

    private Integer buyticket;

    private Integer tickettype;

    private String relatedids;

    private String dailizhiwu;

    private Boolean isnewflag;

    private String zhichuids;

    private Integer isapplyforsomeone;

    private String realapplicant;

    private String realapplicantid;

    private Integer qudaochannelid;

    private Long inuserdepartcode;

    private Integer departbianzhi;

    private BigDecimal giftcash;

    private String flightnumber;

    private LocalDateTime takeofftime;

    private String flightnumber2;

    private LocalDateTime takeofftime2;

    private LocalDateTime arrivaltime;

    private Integer areaorganizekind;

    private String togther;

    private String inuserdepartmentbak;

    private String inuserdepartcodebak;

    }
