package com.jiuji.oa;

import com.jiuji.oa.nc.common.config.properties.ImageProperties;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryCustomizer;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.boot.context.ApplicationPidFileWriter;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.retry.annotation.EnableRetry;

/**
 * <p>
 *
 * @description: </p>
 * @author: David
 * @create: 2021-05-07 16:06
 */
@SpringBootApplication(scanBasePackages = {"com.jiuji.oa", "cn.hutool.extra.spring","com.jiuji.tc.utils.common"}, exclude = {MongoAutoConfiguration.class,
        FlywayAutoConfiguration.class})
@EnableAspectJAutoProxy(proxyTargetClass = true, exposeProxy = true)
@EnableConfigurationProperties({ImageProperties.class})
@EnableDiscoveryClient
@EnableRetry(proxyTargetClass = true)
public class StockApplication {

    public static void main(String[] args) {
        SpringApplication app = new SpringApplication(StockApplication.class);
        app.addListeners(new ApplicationPidFileWriter());
        ConfigurableApplicationContext context = app.run(args);
    }

    @Bean
    MeterRegistryCustomizer<MeterRegistry> configurer(@Value("${spring.application.name}") String applicationName) {
        return registry -> registry.config().commonTags("application", applicationName);
    }

}
