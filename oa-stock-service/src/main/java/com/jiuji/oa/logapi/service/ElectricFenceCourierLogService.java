package com.jiuji.oa.logapi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.logapi.pojo.entity.ElectricFenceCourierLog;
import com.jiuji.oa.stock.electricfence.vo.ElectricFenceCourierLogVO;
import com.jiuji.tc.common.vo.R;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【electric_fence_courier_log(电子围栏配送人日志)】的数据库操作Service
* @createDate 2024-07-08 10:50:41
*/
public interface ElectricFenceCourierLogService extends IService<ElectricFenceCourierLog> {

    /**
     * 添加电子围栏配送人日志
     * @param shopId
     * @param comment
     * @param userName
     * @return
     */
    boolean addElectricFenceCourierLog(Integer shopId, String comment, String userName);

    /**
     * 查询电子围栏配送人日志
     * @param areaId
     * @return
     */
    R<List<ElectricFenceCourierLogVO>> queryElectricFenceCourierLog(Integer areaId);
}
