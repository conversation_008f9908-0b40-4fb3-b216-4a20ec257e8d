package com.jiuji.oa.logapi.mq;

import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.ch999.common.util.utils.Exceptions;
import com.jiuji.oa.logapi.enums.LogTypeEnum;
import com.jiuji.oa.logapi.pojo.dto.req.RabbitListenMessageReq;
import com.jiuji.oa.logapi.pojo.dto.res.*;
import com.jiuji.oa.logapi.service.*;
import com.jiuji.oa.nc.common.config.rabbitmq.RabbitMqConfig;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 物流消息
 *
 * <AUTHOR>
 * @date 2021/4/14 9:19
 */

@Slf4j
@Component
public class LogApiListener {

    @Resource
    private IMkcLogNewService mkcLogNewService;

    @Resource
    private IOaDisplayLogService displayLogService;

    @Resource
    private IOk3wQuDaoLogService qudaoLogService;

    @Resource
    private IProductSnLogService productSnLogService;

    @Resource
    private IDayInPriceCheckRemarkService dayInPriceCheckRemarkService;

    @Resource
    private IMeiTuanLogService meiTuanLogService;

    @Resource
    private IQuickPurchaseAccessoryLogService quickPurchaseAccessoryLogService;

    private static final String ERROR_INFO = "MQ 异常信息：{}";

    @RabbitListener(queues = RabbitMqConfig.QUEUE_LOG_ROLLBACK, containerFactory = "defaultListenerContainerFactory")
    @DS("logDB")
    public void mkcLogRollBackMessage(Message message, Channel channel) {
        try {
            String str = new String(message.getBody());
            log.info("从rabbitmq获取的消息：{}", str);
            if (StringUtils.isEmpty(str)) {
                log.error("从rabbitmq获取消息为空！");
                return;
            }
            List<RabbitListenMessageReq> rabbitListenMessageReqList = JSONUtil.toList(str, RabbitListenMessageReq.class);
            List<String> mkcLogRollbackIdList = rabbitListenMessageReqList.stream()
                    .filter(x -> x.getLogType().equals(LogTypeEnum.MKC_LOG.getCode()))
                    .map(RabbitListenMessageReq::getRollbackId).collect(Collectors.toList());
            List<String> displayLogRollbackIdList = rabbitListenMessageReqList.stream()
                    .filter(x -> x.getLogType().equals(LogTypeEnum.DISPLAY_LOG.getCode()))
                    .map(RabbitListenMessageReq::getRollbackId).collect(Collectors.toList());
            List<String> qudaoIdRollbackLogList = rabbitListenMessageReqList.stream()
                    .filter(x -> x.getLogType().equals(LogTypeEnum.QUDAO_LOG.getCode()))
                    .map(RabbitListenMessageReq::getRollbackId).collect(Collectors.toList());
            List<String> productSnRollbackIdList = rabbitListenMessageReqList.stream()
                    .filter(x -> x.getLogType().equals(LogTypeEnum.PRODUCT_SN_LOG.getCode()))
                    .map(RabbitListenMessageReq::getRollbackId).collect(Collectors.toList());
            List<String> checkPriceRollbackIdList = rabbitListenMessageReqList.stream()
                    .filter(x -> x.getLogType().equals(LogTypeEnum.CHECK_PRICE_REMARK.getCode()))
                    .map(RabbitListenMessageReq::getRollbackId).collect(Collectors.toList());
            List<String> meiTuanLogRollbackIdList = rabbitListenMessageReqList.stream()
                    .filter(x -> x.getLogType().equals(LogTypeEnum.MEI_TUAN_LOG.getCode()))
                    .map(RabbitListenMessageReq::getRollbackId).collect(Collectors.toList());
            List<String> quickPurchaseAccessoryRollbackIdList = rabbitListenMessageReqList.stream()
                    .filter(x -> x.getLogType().equals(LogTypeEnum.QUICK_PURCHASE_ACCESSORY_LOG.getCode()))
                    .map(RabbitListenMessageReq::getRollbackId).collect(Collectors.toList());

            if(!mkcLogRollbackIdList.isEmpty()) {
                List<Long> mkcLogIdList = mkcLogNewService.getMkcLogListByRollbackId(mkcLogRollbackIdList).stream()
                        .map(MkcLogNewRes::getId).collect(Collectors.toList());
                mkcLogNewService.getBaseMapper().deleteBatchIds(mkcLogIdList);
            }
            if(!displayLogRollbackIdList.isEmpty()) {
                List<Long> displayLogIdList = displayLogService.getDisplayLogByRollbackId(displayLogRollbackIdList).stream()
                        .map(OaDisplayLogRes::getId).collect(Collectors.toList());
                displayLogService.getBaseMapper().deleteBatchIds(displayLogIdList);
            }
            if(!qudaoIdRollbackLogList.isEmpty()) {
                List<Long> qudaoLogIdList = qudaoLogService.getQudaoLogByRollbackId(qudaoIdRollbackLogList).stream()
                        .map(Ok3wQudaoLogRes::getId).collect(Collectors.toList());
                qudaoLogService.getBaseMapper().deleteBatchIds(qudaoLogIdList);
            }
            if(!productSnRollbackIdList.isEmpty()) {
                List<Long> productSnLogRollbackIdList = productSnLogService.getProductSnLogListByRollbackId(productSnRollbackIdList)
                        .stream().map(ProductSnLogRes::getId).collect(Collectors.toList());
                productSnLogService.getBaseMapper().deleteBatchIds(productSnLogRollbackIdList);
            }
            if(!CollectionUtils.isEmpty(checkPriceRollbackIdList)) {
                List<Long> checkPriceIdList = dayInPriceCheckRemarkService.getDayInPrcCkRemByRollbackId(checkPriceRollbackIdList)
                        .stream().map(DayInPriceCheckRemarkRes::getId).collect(Collectors.toList());
                dayInPriceCheckRemarkService.getBaseMapper().deleteBatchIds(checkPriceIdList);
            }
            if(!CollectionUtils.isEmpty(meiTuanLogRollbackIdList)) {
                List<Long> meiTuanLogIdList = meiTuanLogService.getMeiTuanLogByRollbackId(meiTuanLogRollbackIdList)
                        .stream().map(MeiTuanLogRes::getId).collect(Collectors.toList());
                meiTuanLogService.getBaseMapper().deleteBatchIds(meiTuanLogIdList);
            }
            if (!CollectionUtils.isEmpty(quickPurchaseAccessoryRollbackIdList)) {
                List<Long> quickPurchaseAccessoryLogIdList = quickPurchaseAccessoryLogService
                        .getQuickPurchaseAccessoryLogByRollbackId(quickPurchaseAccessoryRollbackIdList)
                        .stream().map(QuickPurchaseAccessoryLogRes::getId).collect(Collectors.toList());
                quickPurchaseAccessoryLogService.getBaseMapper().deleteBatchIds(quickPurchaseAccessoryLogIdList);
            }
        } catch (Exception e) {
            log.error(ERROR_INFO, Exceptions.getStackTraceAsString(e));
        }
    }
}
