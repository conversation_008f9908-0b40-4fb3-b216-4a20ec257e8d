package com.jiuji.oa.logapi.mapstruct;

import com.jiuji.oa.logapi.pojo.dto.req.Ok3wQudaoLogReq;
import com.jiuji.oa.logapi.pojo.dto.res.Ok3wQudaoLogRes;
import com.jiuji.oa.logapi.pojo.entity.Ok3wQudaoLog;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * 渠道日志映射结构
 *
 * <AUTHOR>
 * @date 2021/08/11
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface Ok3wQudaoLogMapStruct {
    /**
     * 渠道日志请求体转渠道日志实体
     *
     * @param ok3wQudaoLogReq ok3w qudao日志请求
     * @return {@link Ok3wQudaoLog}
     */
    Ok3wQudaoLog qudaoLogReqToQudaoLog(Ok3wQudaoLogReq ok3wQudaoLogReq);

    /**
     * entity转res
     *
     * @param ok3wQudaoLog
     * @return {@link Ok3wQudaoLogRes}
     */
    Ok3wQudaoLogRes qudaoLogToQudaoLogReq(Ok3wQudaoLog ok3wQudaoLog);

    /**
     * entity列表转res列表
     *
     * @param qudaoLogList ok3w qudao日志请求
     * @return {@link Ok3wQudaoLogRes}
     */
    List<Ok3wQudaoLogRes> qudaoLogListToOk3wQudaoLogResList(List<Ok3wQudaoLog> qudaoLogList);

    /**
     * 渠道日志请求体列表转渠道日志实体列表
     *
     * @param ok3wQudaoLogReqList ok3w qudao日志请求
     * @return {@link Ok3wQudaoLog}
     */
    List<Ok3wQudaoLog> qudaoLogReqListToQudaoLogList(List<Ok3wQudaoLogReq> ok3wQudaoLogReqList);
}
