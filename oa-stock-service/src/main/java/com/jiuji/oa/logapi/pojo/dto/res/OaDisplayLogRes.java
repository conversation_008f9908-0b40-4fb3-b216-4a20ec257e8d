package com.jiuji.oa.logapi.pojo.dto.res;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * oa陈列日志响应实体
 *
 * <AUTHOR>
 * @date 2021/08/11
 */
@Data
@EqualsAndHashCode
public class OaDisplayLogRes implements Serializable {
    /**
     * id
     */
    private Long id;
    /**
     * 操作人
     */
    private String inUser;
    /**
     * 操作时间
     */
    private LocalDateTime dTime;
    /**
     * 陈列类型
     */
    private Boolean showType;
    /**
     * 备注
     */
    private String comment;
    /**
     * 商品号
     */
    private Long subId;
    /**
     * 类型
     */
    private Integer type;
    /**
     * 用于C#事务回滚的id
     */
    private String rollbackId;
}
