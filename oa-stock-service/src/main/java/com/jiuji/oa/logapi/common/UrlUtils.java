package com.jiuji.oa.logapi.common;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.util.CollectionUtils;
import com.jiuji.oa.stock.common.util.StockUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.UnsupportedEncodingException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.URLEncoder;
import java.util.Collections;
import java.util.List;

@Slf4j
public final class UrlUtils {
    private static final String PLUS = "+";
    private static final String SPACE_ENCODE = "%20";

    private UrlUtils() {
        throw new IllegalStateException("Utility classes");
    }

    public static List<?> encodeComment(List<?> list, String getMethodName, String setMethodName) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        list.forEach(it -> {
            try {
                Class<?> aClass = it.getClass();
                Method setEncode = aClass.getMethod(setMethodName, String.class);
                Method getEncode = aClass.getMethod(getMethodName);
                String comment = (String) getEncode.invoke(it);
                //处理oa moa url
                if (StrUtil.isNotBlank(comment)) {
                    comment = StockUtils.handleOaOrMoaUrl(comment);
                }
                String encode = URLEncoder.encode(comment, "UTF-8");
                setEncode.invoke(it,encode.replace(PLUS, SPACE_ENCODE));
            } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
                log.error("反射获取方法失败{}", it.getClass().toString());
            } catch (UnsupportedEncodingException e) {
                log.error("Unable to encode", e);
            }
        });


        return list;
    }
}
