package com.jiuji.oa.logapi.service.impl;

import cn.hutool.core.lang.Assert;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.logapi.common.UrlUtils;
import com.jiuji.oa.logapi.mapper.OaDisplayLogMapper;
import com.jiuji.oa.logapi.mapstruct.OaDisplayLogMapStruct;
import com.jiuji.oa.logapi.pojo.dto.req.OaDisplayLogReq;
import com.jiuji.oa.logapi.pojo.dto.res.OaDisplayLogRes;
import com.jiuji.oa.logapi.pojo.entity.OaDisplayLog;
import com.jiuji.oa.logapi.service.IOaDisplayLogService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 陈列日志服务实现类
 *
 * <AUTHOR>
 * @date 2021/08/11
 */
@Service(value = "OaDisplayLogServiceImplFromTidb")
@DS("oa_log")
public class OaDisplayLogServiceImpl extends ServiceImpl<OaDisplayLogMapper, OaDisplayLog> implements IOaDisplayLogService {

    private static final String GET_METHOD = "getComment";
    private static final String SET_METHOD = "setComment";

    @Resource
    private OaDisplayLogMapStruct oaDisplayLogMapStruct;

    @Resource
    private OaDisplayLogMapper oaDisplayLogMapper;

    @Override
    public Long insertDisplayLog(OaDisplayLogReq displayLogReq) {
        OaDisplayLog displayLog = oaDisplayLogMapStruct.oaDisplayLogReqToOaDisplayLog(displayLogReq);
        save(displayLog);
        return displayLog.getId();
    }

    @Override
    public List<Long> insertDisplayLogBatch(List<OaDisplayLogReq> oaDisplayLogReqList) {
        List<OaDisplayLog> oaDisplayLogList = oaDisplayLogMapStruct.oaDisplayLogReqListToOaDisplayLogList(oaDisplayLogReqList);
        saveBatch(oaDisplayLogList);
        List<Long> displayLogId;
        displayLogId = oaDisplayLogList.stream().map(OaDisplayLog::getId).collect(Collectors.toList());
        return displayLogId;
    }

    @Override
    public List<OaDisplayLogRes> getDisplayLogBySubId(Long subId) {
        Assert.notNull(subId,"subId 不能为空，请检查参数！");
        List<OaDisplayLog> displayLogList = lambdaQuery().eq(OaDisplayLog::getSubId, subId).orderByDesc(OaDisplayLog::getDTime).list();
        List<OaDisplayLog> list = (List<OaDisplayLog>) UrlUtils.encodeComment(displayLogList, GET_METHOD, SET_METHOD);
        return oaDisplayLogMapStruct.oaDisplayLogListToDisplayLogResList(list);
    }

    @Override
    public List<OaDisplayLogRes> getDisplayLogByRollbackId(List<String> rollbackId) {
        Assert.notEmpty(rollbackId,"rollbackId 数组不能为空，请检查参数！");
        List<OaDisplayLog> displayLogList = lambdaQuery().in(OaDisplayLog::getRollbackId, rollbackId).list();
        List<OaDisplayLog> list = (List<OaDisplayLog>) UrlUtils.encodeComment(displayLogList, GET_METHOD, SET_METHOD);
        return oaDisplayLogMapStruct.oaDisplayLogListToDisplayLogResList(list);
    }

    @Override
    public List<OaDisplayLogRes> getLastRecord(String subIdArrayToStr) {
        Assert.notBlank(subIdArrayToStr,"sub-id-list 不能为空，请检查参数！");
        String[] subIdArray = subIdArrayToStr.split(",");
        List<Long> subIdList = Arrays.stream(subIdArray).map(Long::parseLong).distinct().collect(Collectors.toList());
        List<OaDisplayLog> lastRecordList = oaDisplayLogMapper.getLastRecord(subIdList);
        List<OaDisplayLog> list = (List<OaDisplayLog>) UrlUtils.encodeComment(lastRecordList, GET_METHOD, SET_METHOD);
        return oaDisplayLogMapStruct.oaDisplayLogListToDisplayLogResList(list);
    }

    @Override
    public List<OaDisplayLogRes> getDisplayLogByBatchSubId(String subIdArrayToStr) {
        Assert.notBlank(subIdArrayToStr,"sub-id-list 不能为空，请检查参数！");
        String[] subIdArray = subIdArrayToStr.split(",");
        List<Long> subIdList = Arrays.stream(subIdArray).map(Long::parseLong).distinct().collect(Collectors.toList());
        List<OaDisplayLog> oaDisplayLogList = this.lambdaQuery().in(OaDisplayLog::getSubId,subIdList).list();
        List<OaDisplayLog> list = (List<OaDisplayLog>) UrlUtils.encodeComment(oaDisplayLogList, GET_METHOD, SET_METHOD);
        return oaDisplayLogMapStruct.oaDisplayLogListToDisplayLogResList(list);
    }
}
