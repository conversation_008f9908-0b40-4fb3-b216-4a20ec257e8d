package com.jiuji.oa.logapi.service.impl;

import cn.hutool.core.convert.Convert;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.logapi.pojo.entity.ElectricFenceCourierLog;
import com.jiuji.oa.logapi.service.ElectricFenceCourierLogService;
import com.jiuji.oa.logapi.mapper.ElectricFenceCourierLogMapper;
import com.jiuji.oa.stock.electricfence.mapStruct.ElectricFenceCourierLogMapStruct;
import com.jiuji.oa.stock.electricfence.vo.ElectricFenceCourierLogVO;
import com.jiuji.tc.common.vo.R;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【electric_fence_courier_log(电子围栏配送人日志)】的数据库操作Service实现
* @createDate 2024-07-08 10:50:41
*/
@Service
@DS("oa_log")
public class ElectricFenceCourierLogServiceImpl extends ServiceImpl<ElectricFenceCourierLogMapper, ElectricFenceCourierLog> implements ElectricFenceCourierLogService{
    @Resource
    private ElectricFenceCourierLogMapStruct electricFenceCourierLogMapStruct;

    /**
     * 添加电子围栏配送人日志
     *
     * @param shopId
     * @param comment
     * @param userName
     * @return
     */
    @Override
    @DS("oa_log")
    public boolean addElectricFenceCourierLog(Integer shopId, String comment, String userName) {
        ElectricFenceCourierLog electricFenceCourierLog = new ElectricFenceCourierLog();
        electricFenceCourierLog.setAreaId(Convert.toLong(shopId));
        electricFenceCourierLog.setComment(comment);
        electricFenceCourierLog.setInUser(userName);
        electricFenceCourierLog.setAreaId(Convert.toLong(shopId));
        return this.save(electricFenceCourierLog);
    }

    /**
     * 查询电子围栏配送人日志
     *
     * @param areaId
     * @return
     */
    @Override
    public R<List<ElectricFenceCourierLogVO>> queryElectricFenceCourierLog(Integer areaId) {
        List<ElectricFenceCourierLog> list = this.lambdaQuery().eq(ElectricFenceCourierLog::getAreaId, areaId).orderByAsc(ElectricFenceCourierLog::getCreateTime).list();
        List<ElectricFenceCourierLogVO> electricFenceCourierLogList = Optional.ofNullable(list).orElse(Collections.emptyList()).stream().map(electricFenceCourierLogMapStruct::toElectricFenceCourierLog).collect(Collectors.toList());
        return R.success(electricFenceCourierLogList);
    }
}




