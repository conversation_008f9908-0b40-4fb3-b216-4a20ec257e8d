package com.jiuji.oa.logapi.controller;

import com.jiuji.oa.logapi.common.LogUtils;
import com.jiuji.oa.logapi.common.ValidatedList;
import com.jiuji.oa.logapi.pojo.dto.req.QuickPurchaseAccessoryLogReq;
import com.jiuji.oa.logapi.pojo.dto.res.QuickPurchaseAccessoryLogRes;
import com.jiuji.oa.logapi.service.IQuickPurchaseAccessoryLogService;
import com.jiuji.tc.common.vo.R;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 快速购买附件日志控制器
 *
 * <AUTHOR>
 * @date 2021/09/30
 */
@RestController
@RequestMapping("/api/oaApp/accessory/quick-purchase/log")
public class QuickPurchaseAccessoryLogController {
    @Resource
    private IQuickPurchaseAccessoryLogService quickPurchaseAccessoryLogService;

    @PostMapping("/insert/v1")
    public R<String> insertQuickPurchaseAccessoryLog(@Valid @RequestBody QuickPurchaseAccessoryLogReq quickPurchaseAccessoryReq,
                                    @RequestHeader(value = "token",required = false) String token) {
        LogUtils.checkToken(token);
        quickPurchaseAccessoryLogService.insertLog(quickPurchaseAccessoryReq);
        return R.success("插入成功");
    }

    @PostMapping("/insert/batch/v1")
    public R<String> insertQuickPurchaseAccessoryLogBatch(
            @Valid @RequestBody ValidatedList<QuickPurchaseAccessoryLogReq> quickPurchaseAccessoryReq,
            @RequestHeader(value = "token",required = false) String token) {
        LogUtils.checkToken(token);
        quickPurchaseAccessoryLogService.insertBatchLog(quickPurchaseAccessoryReq);
        return R.success("插入成功");
    }

    @GetMapping("/list/v1")
    public R<List<QuickPurchaseAccessoryLogRes>> getQuickPurchaseAccessoryLogBySubId(
            @RequestParam(value = "sub-id") String subId,
            @RequestHeader(value = "token",required = false) String token) {
        LogUtils.checkToken(token);
        List<QuickPurchaseAccessoryLogRes> quickPurchaseAccessoryLogResList = quickPurchaseAccessoryLogService
                .getQuickPurchaseAccessoryLogBySubId(subId);
        return R.success(quickPurchaseAccessoryLogResList);
    }

    @GetMapping("/list/batch/v1")
    public R<List<QuickPurchaseAccessoryLogRes>> quickPurchaseAccessoryLogResList(
            @RequestParam(value = "sub-id-list") String subIdArrayToStr,
            @RequestHeader(value = "token",required = false) String token) {
        LogUtils.checkToken(token);
        List<QuickPurchaseAccessoryLogRes> quickPurchaseAccessoryLogResList = quickPurchaseAccessoryLogService
                .getQuickPurchaseAccessoryLogBySubIdList(subIdArrayToStr);
        return R.success(quickPurchaseAccessoryLogResList);
    }

    @GetMapping("/last/record/v1")
    public R<List<QuickPurchaseAccessoryLogRes>> getLastRecord(@RequestParam(value = "sub-id-list") String subIdArrayToStr,
                                                  @RequestHeader(value = "token",required = false) String token) {
        LogUtils.checkToken(token);
        List<QuickPurchaseAccessoryLogRes> lastRecord = quickPurchaseAccessoryLogService.getLastRecord(subIdArrayToStr);
        return R.success(lastRecord);
    }

    @PostMapping("/list/rollback-id/v1")
    public R<List<QuickPurchaseAccessoryLogRes>> getQuickPurchaseAccessoryLogByRollbackId(@RequestBody List<String> rollbackIdList,
                                                              @RequestHeader(value = "token",required = false) String token) {
        LogUtils.checkToken(token);
        List<QuickPurchaseAccessoryLogRes> purchaseAccessoryLogResList = quickPurchaseAccessoryLogService
                .getQuickPurchaseAccessoryLogByRollbackId(rollbackIdList);
        return R.success(purchaseAccessoryLogResList);
    }
}
