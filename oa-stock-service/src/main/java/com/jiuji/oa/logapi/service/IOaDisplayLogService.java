package com.jiuji.oa.logapi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.logapi.pojo.dto.req.OaDisplayLogReq;
import com.jiuji.oa.logapi.pojo.dto.res.OaDisplayLogRes;
import com.jiuji.oa.logapi.pojo.entity.OaDisplayLog;

import java.util.List;

/**
 * 陈列日志服务
 *
 * <AUTHOR>
 * @date 2021/08/11
 */
public interface IOaDisplayLogService extends IService<OaDisplayLog> {
    /**
     * 插入显示日志
     *
     * @param displayLogReq 显示日志
     * @return Long 返回 id
     */
    Long insertDisplayLog(OaDisplayLogReq displayLogReq);

    /**
     * 插入显示日志批量
     *
     * @param displayLogReqList 显示日志列表
     * @return List<Long> 返回id列表
     */
    List<Long> insertDisplayLogBatch(List<OaDisplayLogReq> displayLogReqList);

    /**
     * 通过sub id获取显示日志
     *
     * @param subId id
     * @return {@link List}<{@link OaDisplayLogRes}>
     */
    List<OaDisplayLogRes> getDisplayLogBySubId(Long subId);

    /**
     * 通过 rollback id 获取陈列日志
     *
     * @param rollbackId 回滚id
     * @return {@link List}<{@link OaDisplayLogRes}>
     */
    List<OaDisplayLogRes> getDisplayLogByRollbackId(List<String> rollbackId);

    /**
     * 获得最后一个记录
     *
     * @return {@link OaDisplayLogRes}
     * @param subIdArrayToStr
     */
    List<OaDisplayLogRes> getLastRecord(String subIdArrayToStr);

    List<OaDisplayLogRes> getDisplayLogByBatchSubId(String subIdArrayToStr);
}
