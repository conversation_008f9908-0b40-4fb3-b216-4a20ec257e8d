package com.jiuji.oa.logapi.service.impl;

import cn.hutool.core.lang.Assert;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.logapi.common.UrlUtils;
import com.jiuji.oa.logapi.mapper.QuickPurchaseAccessoryLogMapper;
import com.jiuji.oa.logapi.mapstruct.QuickPurchaseAccessoryLogMapStruct;
import com.jiuji.oa.logapi.pojo.dto.req.QuickPurchaseAccessoryLogReq;
import com.jiuji.oa.logapi.pojo.dto.res.QuickPurchaseAccessoryLogRes;
import com.jiuji.oa.logapi.pojo.entity.QuickPurchaseAccessoryLog;
import com.jiuji.oa.logapi.service.IQuickPurchaseAccessoryLogService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 配件快捷采购日志服务实现类
 *
 * <AUTHOR>
 * @date 2021/09/28
 */
@Service
@DS("oa_log")
public class QuickPurchaseAccessoryLogServiceImpl extends ServiceImpl<QuickPurchaseAccessoryLogMapper, QuickPurchaseAccessoryLog>
        implements IQuickPurchaseAccessoryLogService {

    private static final String GET_METHOD = "getComment";
    private static final String SET_METHOD = "setComment";

    @Resource
    private QuickPurchaseAccessoryLogMapStruct quickPurchaseAccessoryLogMapStruct;

    @Resource
    private QuickPurchaseAccessoryLogMapper quickPurchaseAccessoryLogMapper;

    @Override
    public void insertLog(QuickPurchaseAccessoryLogReq req) {
        QuickPurchaseAccessoryLog quickPurchaseAccessoryLog = quickPurchaseAccessoryLogMapStruct.quickPurchaseAccessoryLogReqToQuickPurchaseAccessoryLog(req);
        this.save(quickPurchaseAccessoryLog);
    }

    @Override
    public void insertBatchLog(List<QuickPurchaseAccessoryLogReq> reqList) {
        List<QuickPurchaseAccessoryLog> quickPurchaseAccessoryLogList = quickPurchaseAccessoryLogMapStruct
                .quickPurchaseAccessoryLogReqListToQuickPurchaseAccessoryLogList(reqList);
        this.saveBatch(quickPurchaseAccessoryLogList);
    }

    @Override
    public List<QuickPurchaseAccessoryLogRes> getQuickPurchaseAccessoryLogBySubId(String subId) {
        Assert.notBlank(subId,"subId不能为空，请检查参数！");
        List<QuickPurchaseAccessoryLog> quickPurchaseAccessoryLogList = this.lambdaQuery()
                .eq(QuickPurchaseAccessoryLog::getSubId, subId).orderByDesc(QuickPurchaseAccessoryLog::getDTime).list();
        List<QuickPurchaseAccessoryLog> encodeAfterList = encode(quickPurchaseAccessoryLogList);
        return quickPurchaseAccessoryLogMapStruct.quickPurchaseAccessoryLogListToQuickPurchaseAccessoryLogResList(encodeAfterList);
    }

    @Override
    public List<QuickPurchaseAccessoryLogRes> getQuickPurchaseAccessoryLogBySubIdList(String subIdArrayToStr) {
        Assert.notBlank(subIdArrayToStr,"sub-id-list不能为空，请检查参数！");
        String[] subIdArray = subIdArrayToStr.split(",");
        List<String> subIdList = Arrays.stream(subIdArray).distinct().collect(Collectors.toList());
        return getQuickPurchaseAccessoryLogBySubIdList(subIdList);
    }

    @Override
    public List<QuickPurchaseAccessoryLogRes> getQuickPurchaseAccessoryLogByRollbackId(List<String> rollbackIdList) {
        Assert.notEmpty(rollbackIdList,"rollbackId 数组不能为空，请检查参数！");
        List<QuickPurchaseAccessoryLog> quickPurchaseAccessoryLogList = this.lambdaQuery()
                .in(QuickPurchaseAccessoryLog::getRollbackId, rollbackIdList).list();
        List<QuickPurchaseAccessoryLog> encodeAfterList = encode(quickPurchaseAccessoryLogList);
        return quickPurchaseAccessoryLogMapStruct.quickPurchaseAccessoryLogListToQuickPurchaseAccessoryLogResList(encodeAfterList);
    }

    @Override
    public List<QuickPurchaseAccessoryLogRes> getLastRecord(String subIdArrayToStr) {
        Assert.notBlank(subIdArrayToStr,"sub-id-list不能为空，请检查参数！");
        String[] subIdArray = subIdArrayToStr.split(",");
        List<String> subIdList = Arrays.stream(subIdArray).distinct().collect(Collectors.toList());
        return getLastRecord(subIdList);
    }

    private List<QuickPurchaseAccessoryLogRes> getQuickPurchaseAccessoryLogBySubIdList(List<String> subIdList) {
        List<QuickPurchaseAccessoryLog> quickPurchaseAccessoryLogList = this.lambdaQuery()
                .in(QuickPurchaseAccessoryLog::getSubId,subIdList).list();
        List<QuickPurchaseAccessoryLog> encodeAfterList = encode(quickPurchaseAccessoryLogList);
        return quickPurchaseAccessoryLogMapStruct.quickPurchaseAccessoryLogListToQuickPurchaseAccessoryLogResList(encodeAfterList);
    }

    private List<QuickPurchaseAccessoryLogRes> getLastRecord(List<String> subIdList) {
        List<QuickPurchaseAccessoryLog> lastRecordList = quickPurchaseAccessoryLogMapper.getLastRecord(subIdList);
        List<QuickPurchaseAccessoryLog> encodeAfterList = encode(lastRecordList);
        return quickPurchaseAccessoryLogMapStruct.quickPurchaseAccessoryLogListToQuickPurchaseAccessoryLogResList(encodeAfterList);
    }

    private List<QuickPurchaseAccessoryLog> encode(List<QuickPurchaseAccessoryLog> quDaoLogList) {
        return (List<QuickPurchaseAccessoryLog>) UrlUtils.encodeComment(quDaoLogList, GET_METHOD, SET_METHOD);
    }
}
