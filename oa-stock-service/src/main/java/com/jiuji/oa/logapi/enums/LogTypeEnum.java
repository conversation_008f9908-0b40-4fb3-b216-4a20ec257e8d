package com.jiuji.oa.logapi.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 日志类型的枚举
 *
 * <AUTHOR>
 * @date 2021/09/03
 */
@AllArgsConstructor
@Getter
public enum LogTypeEnum {

    /**
     * 大件库存日志
     */
    MKC_LOG(1,"大件库存日志"),

    /**
     * 陈列日志
     */
    DISPLAY_LOG(2,"陈列日志"),

    /**
     * 渠道日志
     */
    QUDAO_LOG(3,"渠道日志"),

    /**
     * 商品串号日志
     */
    PRODUCT_SN_LOG(4,"商品串号日志"),

    /**
     * 采购核价日志
     */
    CHECK_PRICE_REMARK(5,"采购核价日志"),

    /**
     * 美团日志
     */
    MEI_TUAN_LOG(6,"美团日志"),

    /**
     * 配件快捷采购日志
     */
    QUICK_PURCHASE_ACCESSORY_LOG(7,"配件快捷采购日志");


    /**
     * 代码
     */
    private Integer code;
    /**
     * 消息
     */
    private String message;
}
