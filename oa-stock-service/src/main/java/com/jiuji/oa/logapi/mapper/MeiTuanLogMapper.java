package com.jiuji.oa.logapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.logapi.pojo.entity.MeiTuanLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 美团日志数据库映射
 *
 * <AUTHOR>
 * @date 2021/09/28
 */
@Mapper
public interface MeiTuanLogMapper extends BaseMapper<MeiTuanLog> {
    List<MeiTuanLog> getLastRecord(@Param("deliveryIdList") List<String> deliveryIdList);
}
