package com.jiuji.oa.logapi.controller;

import com.jiuji.oa.logapi.common.LogUtils;
import com.jiuji.oa.logapi.common.ValidatedList;
import com.jiuji.oa.logapi.pojo.dto.req.OaDisplayLogReq;
import com.jiuji.oa.logapi.pojo.dto.res.OaDisplayLogRes;
import com.jiuji.oa.logapi.service.IOaDisplayLogService;
import com.jiuji.tc.common.vo.R;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api/oaApp/display/log")
public class OaDisplayLogController {
    @Resource
    private IOaDisplayLogService displayLogService;

    @PostMapping("/insert/v1")
    public R<Long> insertDisplayLog(@Valid @RequestBody OaDisplayLogReq displayLogReq,
                                    @RequestHeader(value = "token",required = false) String token) {
        LogUtils.checkToken(token);
        Long id = displayLogService.insertDisplayLog(displayLogReq);
        return R.success(id);
    }

    @PostMapping("/insert/batch/v1")
    public R<List<Long>> insertDisplayLogBatch(@Valid @RequestBody ValidatedList<OaDisplayLogReq> displayLogReqList,
                                               @RequestHeader(value = "token",required = false) String token) {
        LogUtils.checkToken(token);
        List<Long> idList = displayLogService.insertDisplayLogBatch(displayLogReqList);
        return R.success(idList);
    }

    @GetMapping("/list/v1")
    public R<List<OaDisplayLogRes>> getDisplayLogById(@RequestParam(value = "subId") Long subId,
                                                      @RequestHeader(value = "token",required = false) String token) {
        LogUtils.checkToken(token);
        List<OaDisplayLogRes> displayLogResList = displayLogService.getDisplayLogBySubId(subId);
        return R.success("查询成功",displayLogResList);
    }

    @GetMapping("/last/record/v1")
    public R<List<OaDisplayLogRes>> getLastRecord(@RequestParam(value = "sub-id-list") String subIdArrayToStr,
                                                  @RequestHeader(value = "token",required = false) String token) {
        LogUtils.checkToken(token);
        List<OaDisplayLogRes> displayLogRes = displayLogService.getLastRecord(subIdArrayToStr);
        return R.success(displayLogRes);
    }

    @PostMapping("/list/rollback-id/v1")
    public R<List<OaDisplayLogRes>> getDisplayLogByRollbackId(@RequestBody List<String> rollbackIdList,
                                                              @RequestHeader(value = "token",required = false) String token) {
        LogUtils.checkToken(token);
        List<OaDisplayLogRes> displayLogResList = displayLogService.getDisplayLogByRollbackId(rollbackIdList);
        return R.success(displayLogResList);
    }

    @GetMapping("/list/batch/v1")
    public  R<List<OaDisplayLogRes>> getDisplayLogByBatchId(@RequestParam(value = "sub-id-list") String subIdArrayToStr,
                                                            @RequestHeader(value = "token",required = false) String token) {
        LogUtils.checkToken(token);
        List<OaDisplayLogRes> displayLogResList = displayLogService.getDisplayLogByBatchSubId(subIdArrayToStr);
        return R.success(displayLogResList);
    }
}
