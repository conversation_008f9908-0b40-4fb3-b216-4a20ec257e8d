package com.jiuji.oa.logapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.logapi.pojo.entity.ProductSnLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 产品sn对数映射器
 *
 * <AUTHOR>
 * @date 2021/08/11
 */
@Mapper
public interface ProductSnLogMapper extends BaseMapper<ProductSnLog> {
    List<ProductSnLog> getLastRecord(@Param("snList") List<String> snList);
}
