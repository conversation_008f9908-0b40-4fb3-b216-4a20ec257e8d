package com.jiuji.oa.logapi.pojo.dto.res;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 大件库存日志响应
 *
 * <AUTHOR>
 * @date 2021/08/16
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class MkcLogNewRes implements Serializable {
    /**
     * id
     */
    private Long id;
    /**
     * 操作时间
     */
    private LocalDateTime dTime;
    /**
     * 操作用户
     */
    private String inUser;
    /**
     * 显示类型
     */
    private Boolean showType;
    /**
     * 备注
     */
    private String comment;
    /**
     * mkcid
     */
    private Long mkcId;
    /**
     * 用于C#事务回滚的id
     */
    private String rollbackId;
}
