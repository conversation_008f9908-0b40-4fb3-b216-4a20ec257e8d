package com.jiuji.oa.logapi.mapstruct;

import com.jiuji.oa.logapi.pojo.dto.req.OaDisplayLogReq;
import com.jiuji.oa.logapi.pojo.dto.res.OaDisplayLogRes;
import com.jiuji.oa.logapi.pojo.entity.OaDisplayLog;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * oa陈列日志映射结构
 *
 * <AUTHOR>
 * @date 2021/08/11
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OaDisplayLogMapStruct {
    /**
     * oa陈列日志请求转为oa显示日志
     *
     * @param oaDisplayLogReq oa显示日志请求
     * @return {@link OaDisplayLog}
     */
    OaDisplayLog oaDisplayLogReqToOaDisplayLog(OaDisplayLogReq oaDisplayLogReq);

    /**
     * 实体日志转响应体
     *
     * @param displayLog
     * @return {@link OaDisplayLogRes}
     */
    OaDisplayLogRes oaDisplayLogToDisplayLogRes(OaDisplayLog displayLog);

    /**
     * 实体日志列表转响应体列表
     *
     * @param displayLogList 实体日志列表
     * @return {@link OaDisplayLogRes}
     */
    List<OaDisplayLogRes> oaDisplayLogListToDisplayLogResList(List<OaDisplayLog> displayLogList);

    /**
     * oa陈列日志请求转为oa显示日志
     *
     * @param oaDisplayLogReq oa显示日志请求
     * @return {@link OaDisplayLog}
     */
    List<OaDisplayLog> oaDisplayLogReqListToOaDisplayLogList(List<OaDisplayLogReq> oaDisplayLogReqList);
}
