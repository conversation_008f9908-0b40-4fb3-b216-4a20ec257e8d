package com.jiuji.oa.logapi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.logapi.pojo.dto.req.Ok3wQudaoLogReq;
import com.jiuji.oa.logapi.pojo.dto.res.Ok3wQudaoLogRes;
import com.jiuji.oa.logapi.pojo.entity.Ok3wQudaoLog;
import com.jiuji.oa.nc.channel.document.Ok3wQudaoLogDocument;

import java.util.List;

/**
 * 渠道日志服务
 *
 * <AUTHOR>
 * @date 2021/08/11
 */
public interface IOk3wQuDaoLogService extends IService<Ok3wQudaoLog> {


    /**
     * 获取渠道日志
     * @param channelId
     * @return
     */
    Ok3wQudaoLogDocument getOk3wQudaoLogDocument(Long channelId);



    /**
     * 渠道日志插入
     *
     * @param qudaoLogReq qudao日志请求
     * @return Long 插入的id
     */
    Long insertQuDaoLog(Ok3wQudaoLogReq qudaoLogReq);

    /**
     * 渠道日志批量插入
     *
     * @param qudaoLogReqsList qudao日志请求列表
     * @return Long 插入的id 列表
     */
    List<Long> insertQudaoLogBatch(List<Ok3wQudaoLogReq> qudaoLogReqsList);

    /**
     * 根据displayId查询日志列表
     *
     * @param displayId 显示id
     * @return {@link List}<{@link Ok3wQudaoLogRes}>
     */
    List<Ok3wQudaoLogRes> getQudaoLogByDisplayId(Long displayId);

    /**
     * 根据rollback Id查询日志列表
     *
     * @param rollbackId 回滚id
     * @return {@link List}<{@link Ok3wQudaoLogRes}>
     */
    List<Ok3wQudaoLogRes> getQudaoLogByRollbackId(List<String> rollbackId);

    /**
     * 获得最后一个记录
     *
     * @return {@link Ok3wQudaoLogRes}
     * @param displayIdArrayToStr
     */
    List<Ok3wQudaoLogRes> getLastRecord(String displayIdArrayToStr);

    List<Ok3wQudaoLogRes> getQudaoLogByBatchDisplayId(String displayIdArrayToStr);
}
