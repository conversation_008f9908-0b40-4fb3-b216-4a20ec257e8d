package com.jiuji.oa.logapi.pojo.dto.req;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * ok3w qudao日志
 *
 * <AUTHOR>
 * @date 2021/08/11
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode
public class Ok3wQudaoLogReq implements Serializable {
    /**
     * 用于C#事务回滚的id
     */
    @JsonAlias("id")
    private String rollbackId;
    /**
     * 备注
     */
    @NotNull(message = "comment不能为空")
    private String comment;
    /**
     * 操作用户
     */
    @Valid
    @NotNull(message = "inUser不能为空")
    private String inUser;
    /**
     * 显示id
     */
    @Valid
    @NotNull(message = "displayId不能为空")
    private Long displayId;
    /**
     * 请求类
     */
    @JsonFilter(value = "class")
    private String originClass;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dTime;
}
