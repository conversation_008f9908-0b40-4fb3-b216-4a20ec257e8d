package com.jiuji.oa.logapi.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * oa陈列日志
 *
 * <AUTHOR>
 * @date 2021/08/11
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode
public class OaDisplayLog implements Serializable {
    /**
     * id
     */
    private Long id;
    /**
     * 操作人
     */
    private String inUser;
    /**
     * 操作时间
     */
    private LocalDateTime dTime;
    /**
     * 陈列类型
     */
    @TableField("show_type")
    private Boolean showType;
    /**
     * 备注
     */
    private String comment;
    /**
     * 商品号
     */
    private Long subId;
    /**
     * 类型
     */
    private Integer type;
    /**
     * 是否逻辑删除
     */
    @TableField(value = "is_deleted")
    @TableLogic
    private Boolean deleted;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 用于C#事务回滚的id，由c#传入
     */
    private String rollbackId;
}
