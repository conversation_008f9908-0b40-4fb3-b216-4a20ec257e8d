package com.jiuji.oa.logapi.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * ok3w qudao日志
 *
 * <AUTHOR>
 * @date 2021/08/11
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode
@TableName("ok3w_qudao_log")
public class Ok3wQudaoLog implements Serializable {
    /**
     * id
     */
    private Long id;
    /**
     * 操作时间
     */
    private LocalDateTime dTime;
    /**
     * 备注
     */
    private String comment;
    /**
     * 操作用户
     */
    private String inUser;
    /**
     * 显示id
     */
    private Long displayId;
    /**
     * 是否逻辑删除
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 请求类
     */
    @TableField(value = "class")
    private String originClass;
    /**
     * 用于C#事务回滚的id，由c#传入
     */
    private String rollbackId;
}
