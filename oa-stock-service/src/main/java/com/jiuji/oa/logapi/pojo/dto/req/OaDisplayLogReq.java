package com.jiuji.oa.logapi.pojo.dto.req;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * oa陈列日志
 *
 * <AUTHOR>
 * @date 2021/08/11
 */
@Data
public class OaDisplayLogReq implements Serializable {
    /**
     * 用于C#事务回滚的id
     */
    @JsonAlias("id")
    private String rollbackId;
    /**
     * 操作人
     */
    @Valid
    @NotNull(message = "inUser不能为空")
    private String inUser;
    /**
     * 陈列类型
     */
    @Valid
    @NotNull(message = "showType不能为空")
    private Boolean showType;
    /**
     * 备注
     */
    @NotNull(message = "comment不能为空")
    private String comment;
    /**
     * 商品号
     */
    @Valid
    @NotNull(message = "subId不能为空")
    private Long subId;
    /**
     * 类型
     */
    private Integer type;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dTime;
}
