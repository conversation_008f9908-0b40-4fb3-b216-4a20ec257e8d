package com.jiuji.oa.logapi.common;

import com.jiuji.oa.nc.common.util.MD5;
import org.springframework.util.Assert;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 检查令牌公共类
 *
 * <AUTHOR>
 * @date 2021/09/27
 */
public class LogUtils {

    private LogUtils() {
        throw new IllegalStateException("Utility classes");
    }
    /**
     * 检查令牌
     *
     * @param token 令牌
     */
    public static void checkToken(String token) {
        String date = LocalDate.now().toString();
        String localMd5 = MD5.toMD5(date);
        Assert.isTrue(localMd5.equals(token),"token错误，请检查参数！");
    }

    public static void checkTokenV2(String token,String prefix) {
        DateTimeFormatter dft = DateTimeFormatter.ofPattern("yyyyMMdd");
        String date = dft.format(LocalDateTime.now());
        String source = prefix + date;
        String localMd5 = MD5.toMD5(source);
        Assert.isTrue(localMd5.equals(token),"token错误，请检查参数！");
    }
}
