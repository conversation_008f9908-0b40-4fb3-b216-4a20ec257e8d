package com.jiuji.oa.logapi.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * 产品sn日志
 *
 * <AUTHOR>
 * @date 2021/08/11
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode
public class ProductSnLog implements Serializable {
    /**
     * 表id
     */
    private Long id;
    /**
     * 商品id
     */
    private String subId;
    /**
     * 商品串号
     */
    private String productSn;

    private Integer productSnId;

    /**
     * 操作时间
     */
    private LocalDateTime dTime;
    /**
     * 操作用户
     */
    private String inUser;
    /**
     * 显示类型
     */
    private Boolean showType;
    /**
     * 备注
     */
    private String comment;
    /**
     * 是否逻辑删除
     */
    @TableField(value = "is_deleted")
    @TableLogic
    private Boolean deleted;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 用于C#事务回滚的id，由c#传入
     */
    private String rollbackId;
}
