package com.jiuji.oa.logapi.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 电子围栏配送人日志
 * @TableName electric_fence_courier_log
 */
@TableName(value ="electric_fence_courier_log")
@Data
public class ElectricFenceCourierLog implements Serializable {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 门店id
     */
    @TableField(value = "area_id")
    private Long areaId;

    /**
     * 备注日志
     */
    @TableField(value = "comment")
    private String comment;

    /**
     * 操作人
     */
    @TableField(value = "in_user")
    private String inUser;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime update_time;

    /**
     * 删除标识
     */
    @TableLogic
    @TableField(value = "is_delete")
    private Integer isDelete;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}