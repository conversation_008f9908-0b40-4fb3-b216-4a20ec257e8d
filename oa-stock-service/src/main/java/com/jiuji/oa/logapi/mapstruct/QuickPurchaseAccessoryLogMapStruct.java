package com.jiuji.oa.logapi.mapstruct;

import com.jiuji.oa.logapi.pojo.dto.req.QuickPurchaseAccessoryLogReq;
import com.jiuji.oa.logapi.pojo.dto.res.QuickPurchaseAccessoryLogRes;
import com.jiuji.oa.logapi.pojo.entity.QuickPurchaseAccessoryLog;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * 快速购买配件日志结构映射
 *
 * <AUTHOR>
 * @date 2021/09/28
 */
@Mapper(componentModel = "spring",unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface QuickPurchaseAccessoryLogMapStruct {
    /**
     * 快速购买附件日志请求转快速购买附件日志
     *
     * @param req 请求体
     * @return {@link QuickPurchaseAccessoryLog}
     */
    QuickPurchaseAccessoryLog quickPurchaseAccessoryLogReqToQuickPurchaseAccessoryLog(QuickPurchaseAccessoryLogReq req);

    /**
     * 快速购买附件日志请求列表转快速购买附件日志列表
     *
     * @param reqList 请求列表
     * @return {@link List}<{@link QuickPurchaseAccessoryLog}>
     */
    List<QuickPurchaseAccessoryLog> quickPurchaseAccessoryLogReqListToQuickPurchaseAccessoryLogList(
            List<QuickPurchaseAccessoryLogReq> reqList
    );

    /**
     * 快速购买附件日志转快速购买附件日志响应体
     *
     * @param log
     * @return {@link QuickPurchaseAccessoryLogRes}
     */
    QuickPurchaseAccessoryLogRes quickPurchaseAccessoryLogToQuickPurchaseAccessoryLogRes(QuickPurchaseAccessoryLog log);

    /**
     * 快速购买附件日志列表转快速购买附件日志响应体列表
     *
     * @param logList 日志列表
     * @return {@link List}<{@link QuickPurchaseAccessoryLogRes}>
     */
    List<QuickPurchaseAccessoryLogRes> quickPurchaseAccessoryLogListToQuickPurchaseAccessoryLogResList(
            List<QuickPurchaseAccessoryLog> logList
    );
}
