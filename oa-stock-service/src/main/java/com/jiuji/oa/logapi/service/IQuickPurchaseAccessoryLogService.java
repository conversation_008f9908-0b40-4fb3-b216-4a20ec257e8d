package com.jiuji.oa.logapi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.logapi.pojo.dto.req.QuickPurchaseAccessoryLogReq;
import com.jiuji.oa.logapi.pojo.dto.res.QuickPurchaseAccessoryLogRes;
import com.jiuji.oa.logapi.pojo.entity.QuickPurchaseAccessoryLog;

import java.util.List;

/**
 * 配件快捷采购日志服务
 *
 * <AUTHOR>
 * @date 2021/09/27
 */
public interface IQuickPurchaseAccessoryLogService extends IService<QuickPurchaseAccessoryLog> {
    /**
     * 插入日志
     *
     * @param req 请求体
     */
    void insertLog(QuickPurchaseAccessoryLogReq req);

    /**
     * 批量插入日志
     *
     * @param reqList 请求列表
     */
    void insertBatchLog(List<QuickPurchaseAccessoryLogReq> reqList);

    /**
     * 通过subId查询配件快捷采购日志
     *
     * @param subId 子id
     * @return {@link List}<{@link QuickPurchaseAccessoryLogRes}>
     */
    List<QuickPurchaseAccessoryLogRes> getQuickPurchaseAccessoryLogBySubId(String subId);

    /**
     * 通过subId列表查询配件快捷采购日志
     *
     * @param subIdArrayToStr 子id数组str
     * @return {@link List}<{@link QuickPurchaseAccessoryLogRes}>
     */
    List<QuickPurchaseAccessoryLogRes> getQuickPurchaseAccessoryLogBySubIdList(String subIdArrayToStr);

    /**
     * 通过rollbackId查询配件快捷采购日志
     *
     * @param rollbackIdList 回滚 id 列表
     * @return {@link List}<{@link QuickPurchaseAccessoryLogRes}>
     */
    List<QuickPurchaseAccessoryLogRes> getQuickPurchaseAccessoryLogByRollbackId(List<String> rollbackIdList);

    /**
     * 获得最后一个记录
     *
     * @param subIdArrayToStr subId数组字符串
     * @return {@link List}<{@link QuickPurchaseAccessoryLogRes}>
     */
    List<QuickPurchaseAccessoryLogRes> getLastRecord(String subIdArrayToStr);
}
