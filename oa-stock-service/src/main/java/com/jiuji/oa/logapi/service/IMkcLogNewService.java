package com.jiuji.oa.logapi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.logapi.pojo.dto.req.MkcLogNewReq;
import com.jiuji.oa.logapi.pojo.dto.res.MkcLogNewRes;
import com.jiuji.oa.logapi.pojo.entity.MkcLogNew;

import java.util.List;

/**
 * 大件库存日志服务接口
 *
 * <AUTHOR>
 * @date 2021/08/16
 */
public interface IMkcLogNewService extends IService<MkcLogNew> {
    /**
     * 插入大件库存日志
     *
     * @param mkcLogNewReq 大件库存日志
     * @return Long 返回 id
     */
    Long insertMkcLog(MkcLogNewReq mkcLogNewReq);

    /**
     * 批量插入大件库存日志
     *
     * @param mkcLogNewReqList 廊坊开发区日志新要求的列表
     */
    List<Long> insertMkcLogBatch(List<MkcLogNewReq> mkcLogNewReqList);

    /**
     * 根据mkcId查询大件库存日志
     *
     * @param mkcId 大件库存id
     * @return {@link List}<{@link MkcLogNew}>
     */
    List<MkcLogNewRes> getMkcLogListByMkcId(Long mkcId);

    /**
     * 根据rollbackId删除日志
     *
     * @param rollbackId C#指定id
     * @return {@link List}<{@link MkcLogNew}>
     */
    List<MkcLogNewRes> getMkcLogListByRollbackId(List<String> rollbackId);

    /**
     * 查询当前mkcId存储的最后一条记录
     *
     * @param mkcIdArrayToStr 大件库存id
     * @return {@link List}<{@link MkcLogNew}>
     */
    List<MkcLogNewRes> getLastRecord(String mkcIdArrayToStr);

    List<MkcLogNewRes> getLastRecord(List<Long> mkcIdList);

    /**
     * 根据mkcId数组批量查询大件库存日志
     *
     * @param mkcIdArrayToStr 大件库存id
     * @return {@link List}<{@link MkcLogNew}>
     */
    List<MkcLogNewRes> getMkcLogListByMkcBatchId(String mkcIdArrayToStr);

    List<MkcLogNewRes> getMkcLogListByMkcBatchId(List<Long> mkcIdList);
}
