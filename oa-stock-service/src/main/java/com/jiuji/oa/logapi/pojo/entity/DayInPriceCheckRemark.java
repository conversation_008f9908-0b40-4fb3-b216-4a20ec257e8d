package com.jiuji.oa.logapi.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 采购核价实体
 *
 * <AUTHOR>
 * @date 2021/09/27
 */
@TableName("day_in_price_check_remark")
@Data
public class DayInPriceCheckRemark implements Serializable {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 工号
     */
    private String ch999Id;
    /**
     * 操作人
     */
    private String ch999Name;
    /**
     * 内容
     */
    private String content;
    /**
     * 采购核价Id
     */
    private String dayInPriceCheckId;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 删除
     */
    @TableLogic
    @TableField("is_deleted")
    private Boolean deleted;
    /**
     * 回滚id
     */
    private String rollbackId;
}
