package com.jiuji.oa.logapi.controller;

import com.jiuji.oa.logapi.common.LogUtils;
import com.jiuji.oa.logapi.common.ValidatedList;
import com.jiuji.oa.logapi.pojo.dto.req.MkcLogNewReq;
import com.jiuji.oa.logapi.pojo.dto.res.MkcLogNewRes;
import com.jiuji.oa.logapi.service.IMkcLogNewService;
import com.jiuji.tc.common.vo.R;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 大件库存日志控制器
 *
 * <AUTHOR>
 * @date 2021/08/16
 */
@RestController
@RequestMapping("/api/oaApp/mkc/log")
public class MkcLogNewController {

    /**
     * 大件库存日志服务
     */
    @Resource
    private IMkcLogNewService mkcLogNewService;

    /**
     * 插入大件库存日志
     *
     * @param mkcLogNewReq 大件库存日志请求
     * @return {@link R}<{@link String}>
     */
    @PostMapping("/insert/v1")
    public R<Long> insertMkcLog(@Valid @RequestBody MkcLogNewReq mkcLogNewReq, @RequestHeader(value = "token",required = false) String token) {
        LogUtils.checkToken(token);
        Long id = mkcLogNewService.insertMkcLog(mkcLogNewReq);
        return R.success(id);
    }

    /**
     * 批量插入大件库存日志
     *
     * @param mkcLogNewReqList 大件库存日志请求列表
     * @return {@link R}<{@link String}>
     */
    @PostMapping("/insert/batch/v1")
    public R<List<Long>> insertDisplayLogBatch(@Valid @RequestBody ValidatedList<MkcLogNewReq> mkcLogNewReqList,
                                               @RequestHeader(value = "token",required = false) String token) {
        LogUtils.checkToken(token);
        List<Long> idList = mkcLogNewService.insertMkcLogBatch(mkcLogNewReqList);
        return R.success(idList);
    }

    /**
     * 通过mkcId获取显示日志
     *
     * @param mkcId mkcId
     * @return MkcLogNew
     */
    @GetMapping("/list/v1")
    public R<List<MkcLogNewRes>> getDisplayLogById(@RequestParam(value = "mkcId") Long mkcId,
                                                   @RequestHeader(value = "token",required = false) String token) {
        LogUtils.checkToken(token);
        List<MkcLogNewRes> mkcLogNewResList = mkcLogNewService.getMkcLogListByMkcId(mkcId);
        return R.success("查询成功", mkcLogNewResList);
    }

    @GetMapping("/last/record/v1")
    public R<List<MkcLogNewRes>> getLastRecord(@RequestParam(value = "mkc-id-list") String mkcId,
                                               @RequestHeader(value = "token",required = false) String token) {
        LogUtils.checkToken(token);
        List<MkcLogNewRes> lastRecord = mkcLogNewService.getLastRecord(mkcId);
        return R.success(lastRecord);
    }

    @PostMapping("/list/rollback-id/v1")
    public R<List<MkcLogNewRes>> getMkcLogListByRollbackId(@RequestBody List<String> rollbackIdList,
                                                           @RequestHeader(value = "token",required = false) String token) {
        LogUtils.checkToken(token);
        List<MkcLogNewRes> mkcLogResList = mkcLogNewService.getMkcLogListByRollbackId(rollbackIdList);
        return R.success(mkcLogResList);
    }

    /**
     * 通过mkcId获取显示日志
     *
     * @param mkcIdArrayToStr mkcId
     * @return MkcLogNewRes
     */
    @GetMapping("/list/batch/v1")
    public R<List<MkcLogNewRes>> getDisplayLogByBatchId(@RequestParam(value = "mkc-id-list") String mkcIdArrayToStr,
                                                        @RequestHeader(value = "token",required = false) String token) {
        LogUtils.checkToken(token);
        List<MkcLogNewRes> mkcLogNewResList = mkcLogNewService.getMkcLogListByMkcBatchId(mkcIdArrayToStr);
        return R.success("查询成功", mkcLogNewResList);
    }
}
