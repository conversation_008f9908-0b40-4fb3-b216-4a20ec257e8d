package com.jiuji.oa.logapi.service.impl;

import cn.hutool.core.lang.Assert;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.logapi.mapper.MeiTuanLogMapper;
import com.jiuji.oa.logapi.mapstruct.MeiTuanLogMapStruct;
import com.jiuji.oa.logapi.pojo.dto.req.MeiTuanLogReq;
import com.jiuji.oa.logapi.pojo.dto.res.MeiTuanLogRes;
import com.jiuji.oa.logapi.pojo.entity.MeiTuanLog;
import com.jiuji.oa.logapi.service.IMeiTuanLogService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Service
@DS("oa_log")
public class MeiTuanLogServiceImpl extends ServiceImpl<MeiTuanLogMapper, MeiTuanLog> implements IMeiTuanLogService {

    @Resource
    private MeiTuanLogMapStruct meiTuanLogMapStruct;
    @Resource
    private MeiTuanLogMapper meiTuanLogMapper;

    @Override
    public void insertLog(MeiTuanLogReq logReq) {
        MeiTuanLog meiTuanLog = meiTuanLogMapStruct.meiTuanLogReqToMeiTuanLog(logReq);
        this.save(meiTuanLog);
    }

    @Override
    public void insertLogBatch(List<MeiTuanLogReq> logReqList) {
        List<MeiTuanLog> meiTuanLogList = meiTuanLogMapStruct.meiTuanLogReqListToMeiTuanLogList(logReqList);
        this.saveBatch(meiTuanLogList);
    }

    @Override
    public List<MeiTuanLogRes> getMeiTuanLogByDeliveryId(String deliveryId) {
        Assert.notNull(deliveryId,"delivery-id 不能为空，请检查参数！");
        List<MeiTuanLog> meiTuanLogList = this.lambdaQuery().eq(MeiTuanLog::getDeliveryId, deliveryId).orderByDesc(MeiTuanLog::getDTime).list();
        return meiTuanLogMapStruct.meiTuanLogListToMeiTuanLogResList(meiTuanLogList);
    }

    @Override
    public List<MeiTuanLogRes> getMeiTuanLogByDeliveryBatchId(String deliveryIdArrayToStr) {
        Assert.notNull(deliveryIdArrayToStr,"delivery-id-list 不能为空，请检查参数！");
        String[] deliveryIdArray = deliveryIdArrayToStr.split(",");
        List<String> deliveryIdList = Arrays.stream(deliveryIdArray).distinct().collect(Collectors.toList());
        return getMeiTuanLogByDeliveryBatchId(deliveryIdList);
    }

    @Override
    public List<MeiTuanLogRes> getLastRecord(String deliveryIdArrayToStr) {
        Assert.notBlank(deliveryIdArrayToStr, "delivery-id-list不能为空，请检查参数！");
        String[] deliveryIdArray = deliveryIdArrayToStr.split(",");
        List<String> deliveryIdList = Arrays.stream(deliveryIdArray).distinct().collect(Collectors.toList());
        return getLastRecord(deliveryIdList);
    }

    @Override
    public List<MeiTuanLogRes> getMeiTuanLogByRollbackId(List<String> rollbackIdList) {
        Assert.isFalse(CollectionUtils.isEmpty(rollbackIdList),"rollback-id 数组不能为空，请检查参数！");
        List<MeiTuanLog> meiTuanLogList = this.lambdaQuery().in(MeiTuanLog::getRollbackId, rollbackIdList).list();
        return meiTuanLogMapStruct.meiTuanLogListToMeiTuanLogResList(meiTuanLogList);
    }

    private List<MeiTuanLogRes> getMeiTuanLogByDeliveryBatchId(List<String> deliveryIdList) {
        List<MeiTuanLog> meiTuanLogList = this.lambdaQuery().in(MeiTuanLog::getDeliveryId, deliveryIdList).list();
        return meiTuanLogMapStruct.meiTuanLogListToMeiTuanLogResList(meiTuanLogList);
    }

    private List<MeiTuanLogRes> getLastRecord(List<String> deliveryIdList) {
        List<MeiTuanLog> lastRecordList = meiTuanLogMapper.getLastRecord(deliveryIdList);
        return meiTuanLogMapStruct.meiTuanLogListToMeiTuanLogResList(lastRecordList);
    }
}
