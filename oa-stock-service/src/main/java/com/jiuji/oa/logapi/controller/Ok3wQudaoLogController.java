package com.jiuji.oa.logapi.controller;

import com.jiuji.oa.logapi.common.LogUtils;
import com.jiuji.oa.logapi.common.ValidatedList;
import com.jiuji.oa.logapi.pojo.dto.req.Ok3wQudaoLogReq;
import com.jiuji.oa.logapi.pojo.dto.res.Ok3wQudaoLogRes;
import com.jiuji.oa.logapi.service.IOk3wQuDaoLogService;
import com.jiuji.tc.common.vo.R;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 渠道日志控制器
 *
 * <AUTHOR>
 * @date 2021/09/30
 */
@RestController
@RequestMapping("/api/oaApp/qudao/log")
public class Ok3wQudaoLogController {

    @Resource
    private IOk3wQuDaoLogService qudaoLogService;

    @PostMapping("/insert/v1")
    public R<Long> insertQudaoLog(@Valid @RequestBody Ok3wQudaoLogReq qudaoLogReq, @RequestHeader(value = "token",required = false) String token) {
        LogUtils.checkToken(token);
        Long id = qudaoLogService.insertQuDaoLog(qudaoLogReq);
        return R.success(id);
    }

    @PostMapping("/insert/batch/v1")
    public R<List<Long>> insertQudaoLogBatch(@Valid @RequestBody ValidatedList<Ok3wQudaoLogReq> qudaoLogReqList,
                                             @RequestHeader(value = "token",required = false) String token) {
        LogUtils.checkToken(token);
        List<Long> idList = qudaoLogService.insertQudaoLogBatch(qudaoLogReqList);
        return R.success(idList);
    }

    @GetMapping("/list/v1")
    public R<List<Ok3wQudaoLogRes>> getQudaoLogByDisplayId(@RequestParam(value = "displayId") Long displayId,
                                                           @RequestHeader(value = "token",required = false) String token) {
        LogUtils.checkToken(token);
        List<Ok3wQudaoLogRes> qudaoLogResList = qudaoLogService.getQudaoLogByDisplayId(displayId);
        return R.success(qudaoLogResList);
    }

    @GetMapping("/last/record/v1")
    public R<List<Ok3wQudaoLogRes>> getLastRecord(@RequestParam(value = "display-id-list") String displayIdArrayToStr,
                                                  @RequestHeader(value = "token",required = false) String token) {
        LogUtils.checkToken(token);
        List<Ok3wQudaoLogRes> qudaoLogResList = qudaoLogService.getLastRecord(displayIdArrayToStr);
        return R.success(qudaoLogResList);
    }

    @PostMapping("/list/rollback-id/v1")
    public R<List<Ok3wQudaoLogRes>> getDisplayLogByRollbackId(@RequestBody List<String> rollbackIdList,
                                                              @RequestHeader(value = "token",required = false) String token) {
        LogUtils.checkToken(token);
        List<Ok3wQudaoLogRes> qudaoLogResList = qudaoLogService.getQudaoLogByRollbackId(rollbackIdList);
        return R.success(qudaoLogResList);
    }

    @GetMapping("/list/batch/v1")
    public R<List<Ok3wQudaoLogRes>> getQudaoLogByDisplayId(@RequestParam(value = "display-id-list") String displayIdArrayToStr,
                                                           @RequestHeader(value = "token",required = false) String token) {
        LogUtils.checkToken(token);
        List<Ok3wQudaoLogRes> qudaoLogResList = qudaoLogService.getQudaoLogByBatchDisplayId(displayIdArrayToStr);
        return R.success(qudaoLogResList);
    }
}
