package com.jiuji.oa.logapi.pojo.dto.res;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 配件快捷采购响应体
 *
 * <AUTHOR>
 * @date 2021/09/27
 */
@Data
public class QuickPurchaseAccessoryLogRes implements Serializable {
    /**
     * id
     */
    private Long id;
    /**
     * subId
     */
    private String subId;
    /**
     * 操作时间
     */
    private LocalDateTime dTime;
    /**
     * 备注内容
     */
    private String comment;
    /**
     * 操作人
     */
    private String inUser;
    /**
     * 日志类型
     */
    private Integer logType;
    /**
     * 是否显示在前台标识
     */
    private Boolean showType;
    /**
     * 回滚id
     */
    private String rollbackId;
}
