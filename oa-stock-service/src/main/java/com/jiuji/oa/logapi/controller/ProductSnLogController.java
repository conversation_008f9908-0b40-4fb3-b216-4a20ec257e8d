package com.jiuji.oa.logapi.controller;

import com.jiuji.oa.logapi.common.LogUtils;
import com.jiuji.oa.logapi.common.ValidatedList;
import com.jiuji.oa.logapi.pojo.dto.req.ProductSnLogReq;
import com.jiuji.oa.logapi.pojo.dto.res.ProductSnLogRes;
import com.jiuji.oa.logapi.service.IProductSnLogService;
import com.jiuji.tc.common.vo.R;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 商品sn日志控制器
 *
 * <AUTHOR>
 * @date 2021/08/11
 */
@RestController
@RequestMapping("/api/oaApp/product/sn/log")
public class ProductSnLogController {
    /**
     * 商品sn日志服务
     */
    @Resource
    private IProductSnLogService productSnLogService;

    /**
     * 插入商品sn日志
     *
     * @param productSnLogReq 商品sn日志
     * @return {@link R<String>}
     */
    @PostMapping("/insert/v1")
    public R<Long> insertProductSnLog(@Valid @RequestBody ProductSnLogReq productSnLogReq, @RequestHeader(value = "token",required = false) String token) {
        LogUtils.checkToken(token);
        Long id = productSnLogService.insertProductSnLog(productSnLogReq);
        return R.success(id);
    }

    /**
     * 日志批量插入商品sn
     *
     * @param productSnLogReqList 商品sn日志列表
     * @return {@link R<String>}
     */
    @PostMapping("/insert/batch/v1")
    public R<List<Long>> insertProductSnLogBatch(@Valid @RequestBody ValidatedList<ProductSnLogReq> productSnLogReqList,
                                                 @RequestHeader(value = "token",required = false) String token) {
        LogUtils.checkToken(token);
        List<Long> idList = productSnLogService.insertProductSnLogBatch(productSnLogReqList);
        return R.success(idList);
    }

    /**
     * 根据商品sn查询商品列表
     *
     * @param sn 商品sn
     * @return {@link ProductSnLogRes}
     */
    @GetMapping("/list/v1")
    public R<List<ProductSnLogRes>> getProductSnListByProductSn(@RequestParam("sn") String sn,
                                                                @RequestHeader(value = "token",required = false) String token) {
        LogUtils.checkToken(token);
        List<ProductSnLogRes> productSnLogResList = productSnLogService.getProductSnLogListBySn(sn);
        return R.success("查询成功", productSnLogResList);
    }

    @GetMapping("/last/record/v1")
    public R<List<ProductSnLogRes>> getLastRecord(@RequestParam("sn-list") String snListStr,
                                                  @RequestHeader(value = "token",required = false) String token) {
        LogUtils.checkToken(token);
        List<ProductSnLogRes> productSnLogResList = productSnLogService.getLastRecord(snListStr);
        return R.success(productSnLogResList);
    }

    @PostMapping("/list/rollback-id/v1")
    public R<List<ProductSnLogRes>> getDisplayLogByRollbackId(@RequestBody List<String> rollbackIdList,
                                                              @RequestHeader(value = "token",required = false) String token) {
        LogUtils.checkToken(token);
        List<ProductSnLogRes> productSnLogResList = productSnLogService.getProductSnLogListByRollbackId(rollbackIdList);
        return R.success(productSnLogResList);
    }

    @GetMapping("/list/batch/v1")
    public R<List<ProductSnLogRes>> getProductSnListByBatchProductSn(@RequestParam(value = "sn-list",required = false) String snListStr,
                                                                     @RequestParam(value = "sn-id-list",required = false) String snIdListStr,
                                                                     @RequestHeader(value = "token",required = false) String token) {
        LogUtils.checkToken(token);
        List<ProductSnLogRes> productSnLogResList0 =new ArrayList<>();
        List<ProductSnLogRes> productSnLogResList1 = productSnLogService.getProductSnLogListByBatchSn(snListStr);
        List<ProductSnLogRes> productSnLogResList2 = productSnLogService.getProductSnLogListByBatchSnId(snIdListStr);
        productSnLogResList0.addAll(productSnLogResList1);
        productSnLogResList0.addAll(productSnLogResList2);
        return R.success(productSnLogResList0.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList()));
    }
}
