package com.jiuji.oa.logapi.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 配件快捷采购日志
 *
 * <AUTHOR>
 * @date 2021/09/27
 */
@Data
@TableName("quick_purchase_accessory_log")
public class QuickPurchaseAccessoryLog implements Serializable {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * subId
     */
    private String subId;
    /**
     * 操作时间
     */
    private LocalDateTime dTime;
    /**
     * 备注内容
     */
    private String comment;
    /**
     * 操作人
     */
    private String inUser;
    /**
     * 日志类型
     */
    private Integer logType;
    /**
     * 是否显示在前台标识
     */
    private Boolean showType;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 删除
     */
    @TableLogic
    @TableField("is_deleted")
    private Boolean deleted;
    /**
     * 回滚id
     */
    private String rollbackId;
}
