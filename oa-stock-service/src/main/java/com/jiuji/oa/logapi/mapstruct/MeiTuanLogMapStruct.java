package com.jiuji.oa.logapi.mapstruct;

import com.jiuji.oa.logapi.pojo.dto.req.MeiTuanLogReq;
import com.jiuji.oa.logapi.pojo.dto.res.MeiTuanLogRes;
import com.jiuji.oa.logapi.pojo.entity.MeiTuanLog;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * 美团对数映射结构
 *
 * <AUTHOR>
 * @date 2021/09/28
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MeiTuanLogMapStruct {
    /**
     * 美团日志请求转美团日志
     *
     * @param meiTuanLogReq 美团日志请求
     * @return {@link MeiTuanLog}
     */
    MeiTuanLog meiTuanLogReqToMeiTuanLog(MeiTuanLogReq meiTuanLogReq);

    /**
     * 美团日志请求列表转美团日志列表
     *
     * @param meiTuanLogReqList 美团日志请求列表
     * @return {@link List}<{@link MeiTuanLog}>
     */
    List<MeiTuanLog> meiTuanLogReqListToMeiTuanLogList(List<MeiTuanLogReq> meiTuanLogReqList);

    /**
     * 美团日志转美团日志响应
     *
     * @param meiTuanLog 美团日志
     * @return {@link MeiTuanLogRes}
     */
    MeiTuanLogRes meiTuanLogToMeiTuanLogRes(MeiTuanLog meiTuanLog);

    /**
     * 美团日志列表转美团日志响应列表
     *
     * @param meiTuanLogList 美团日志列表
     * @return {@link List}<{@link MeiTuanLogRes}>
     */
    List<MeiTuanLogRes> meiTuanLogListToMeiTuanLogResList(List<MeiTuanLog> meiTuanLogList);
}
