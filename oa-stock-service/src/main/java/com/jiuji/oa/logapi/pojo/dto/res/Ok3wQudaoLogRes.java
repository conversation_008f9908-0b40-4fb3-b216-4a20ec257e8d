package com.jiuji.oa.logapi.pojo.dto.res;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 渠道日志响应实体
 *
 * <AUTHOR>
 * @date 2021/08/11
 */
@Data
@EqualsAndHashCode
public class Ok3wQudaoLogRes implements Serializable {
    /**
     * id
     */
    private Long id;
    /**
     * 操作时间
     */
    private LocalDateTime dTime;
    /**
     * 备注
     */
    private String comment;
    /**
     * 操作用户
     */
    private String inUser;
    /**
     * 显示id
     */
    private Long displayId;
    /**
     * 请求类
     */
    private String originClass;
    /**
     * 用于C#事务回滚的id
     */
    private String rollbackId;
}
