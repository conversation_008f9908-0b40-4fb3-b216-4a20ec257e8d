package com.jiuji.oa.logapi.controller;

import com.jiuji.oa.logapi.common.LogUtils;
import com.jiuji.oa.logapi.common.ValidatedList;
import com.jiuji.oa.logapi.pojo.dto.req.MeiTuanLogReq;
import com.jiuji.oa.logapi.pojo.dto.res.MeiTuanLogRes;
import com.jiuji.oa.logapi.service.IMeiTuanLogService;
import com.jiuji.tc.common.vo.R;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 美团日志控制器
 *
 * <AUTHOR>
 * @date 2021/09/30
 */
@RestController
@RequestMapping("/api/oaApp/meituan/log")
public class MeiTuanLogController {
    @Resource
    private IMeiTuanLogService meiTuanLogService;

    @PostMapping("/insert/v1")
    public R<String> insertLog(
            @Valid @RequestBody MeiTuanLogReq req,
            @RequestHeader(value = "token", required = false) String token) {
        LogUtils.checkToken(token);
        meiTuanLogService.insertLog(req);
        return R.success("插入成功");
    }

    @PostMapping("/insert/batch/v1")
    public R<String> insertBatchLog(
            @Valid @RequestBody ValidatedList<MeiTuanLogReq> reqList,
            @RequestHeader(value = "token", required = false) String token) {
        LogUtils.checkToken(token);
        meiTuanLogService.insertLogBatch(reqList);
        return R.success("插入成功");
    }

    @GetMapping("/list/v1")
    public R<List<MeiTuanLogRes>> getLogListByDeliveryId(
            @RequestParam("delivery-id") String deliveryId,
            @RequestHeader(value = "token", required = false) String token) {
        LogUtils.checkToken(token);
        List<MeiTuanLogRes> logList = meiTuanLogService.getMeiTuanLogByDeliveryId(deliveryId);
        return R.success("查询成功", logList);
    }

    @GetMapping("/list/batch/v1")
    public R<List<MeiTuanLogRes>> getLogListByDeliveryBatchId(
            @RequestParam("delivery-id-list") String deliveryIdList,
            @RequestHeader(value = "token", required = false) String token) {
        LogUtils.checkToken(token);
        List<MeiTuanLogRes> logList = meiTuanLogService.getMeiTuanLogByDeliveryBatchId(deliveryIdList);
        return R.success("查询成功", logList);
    }

    @PostMapping("/list/rollback-id/v1")
    public R<List<MeiTuanLogRes>> getLogListByRollbackId(
            @RequestBody List<String> rollbackIdList,
            @RequestHeader(value = "token", required = false) String token) {
        LogUtils.checkToken(token);
        List<MeiTuanLogRes> logList = meiTuanLogService.getMeiTuanLogByRollbackId(rollbackIdList);
        return R.success("查询成功", logList);
    }

    @GetMapping("/last/record/v1")
    public R<List<MeiTuanLogRes>> getLastRecord(
            @RequestParam("delivery-id-list") String deliveryIdList,
            @RequestHeader(value = "token", required = false) String token) {
        LogUtils.checkToken(token);
        List<MeiTuanLogRes> lastRecord = meiTuanLogService.getLastRecord(deliveryIdList);
        return R.success("查询成功", lastRecord);
    }
}
