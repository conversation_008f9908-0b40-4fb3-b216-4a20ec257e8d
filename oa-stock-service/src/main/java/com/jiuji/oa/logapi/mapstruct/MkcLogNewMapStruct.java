package com.jiuji.oa.logapi.mapstruct;

import com.jiuji.oa.logapi.pojo.dto.req.MkcLogNewReq;
import com.jiuji.oa.logapi.pojo.dto.res.MkcLogNewRes;
import com.jiuji.oa.logapi.pojo.entity.MkcLogNew;
import com.jiuji.oa.stock.purchaseList.dto.MkcLog;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * 大件库存日志映射
 *
 * <AUTHOR>
 * @date 2021/08/16
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MkcLogNewMapStruct {
    /**
     * 请求体转实体
     *
     * @param mkcLogNewReq 大件库存日志请求体
     * @return {@link MkcLogNew}
     */
    MkcLogNew mkcLogNewReqToMkcLogNew(MkcLogNewReq mkcLogNewReq);

    /**
     * 实体类转响应类
     *
     * @param mkcLogNew 廊坊开发区新日志
     * @return {@link MkcLogNewRes}
     */
    MkcLogNewRes mkcLogNewToMkcLogNewRes(MkcLogNew mkcLogNew);

    /**
     * 实体类列表转响应类列表
     *
     * @param mkcLogNewList 廊坊开发区新日志
     * @return {@link List}<{@link MkcLogNewRes}>
     */
    List<MkcLogNewRes> mkcLogNewListToMkcLogNewResList(List<MkcLogNew> mkcLogNewList);

    /**
     * 请求体列表转实体列表
     *
     * @param mkcLogNewReqList 大件库存日志请求体
     * @return {@link MkcLogNew}
     */
    List<MkcLogNew> mkcLogNewReqToMkcLogNew(List<MkcLogNewReq> mkcLogNewReqList);

    MkcLogNew mkcLogToMkcLogNew(MkcLog mkcLogNewList);

    List<MkcLogNew> mkcLogToMkcLogNew(List<MkcLog> mkcLogNewList);

}
