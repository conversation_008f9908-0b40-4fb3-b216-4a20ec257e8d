package com.jiuji.oa.logapi.pojo.dto.res;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 采购核价res
 *
 * <AUTHOR>
 * @date 2021/09/27
 */
@Data
public class DayInPriceCheckRemarkRes implements Serializable {
    /**
     * id
     */
    private Long id;
    /**
     * 工号
     */
    private String ch999Id;
    /**
     * 操作人
     */
    private String ch999Name;
    /**
     * 内容
     */
    private String content;
    /**
     * 采购核价Id
     */
    private String dayInPriceCheckId;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 回滚id
     */
    private String rollbackId;
}
