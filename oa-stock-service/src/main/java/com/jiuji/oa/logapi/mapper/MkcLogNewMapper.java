package com.jiuji.oa.logapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.logapi.pojo.entity.MkcLogNew;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface MkcLogNewMapper extends BaseMapper<MkcLogNew> {
    List<MkcLogNew> getLastRecord(@Param("mkcIdList")List<Long> mkcIdList);
}
