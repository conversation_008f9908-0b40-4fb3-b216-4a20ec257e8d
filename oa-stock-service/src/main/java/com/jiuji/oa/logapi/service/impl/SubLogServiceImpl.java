package com.jiuji.oa.logapi.service.impl;

import com.jiuji.oa.logapi.service.ISubLogService;
import com.jiuji.oa.loginfo.order.service.SubLogsCloud;
import com.jiuji.oa.loginfo.order.vo.req.SubLogsNewReq;
import com.jiuji.tc.common.vo.R;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/8/19 15:17
 */
@Slf4j
@Service
public class SubLogServiceImpl implements ISubLogService {
    @Resource
    private SubLogsCloud subLogsCloud;

    /**
     * 保存订单日志
     *
     * @param subLogsNewReq
     */
    @Override
    public boolean saveSubLog(SubLogsNewReq subLogsNewReq) {
        try {
            R<Boolean> booleanR = subLogsCloud.addSubLog(subLogsNewReq);
            if (Objects.nonNull(booleanR) && Objects.equals(0, booleanR.getCode())) {
                return booleanR.getData();
            }
        } catch (Exception e) {
            log.error("保存订单日志异常，param={}", subLogsNewReq, e);
        }
        return false;
    }

    /**
     * 保存订单日志
     *
     * @param subId
     * @param showType
     * @param comment
     * @param inUser
     */
    @Override
    public boolean saveSubLog(Integer subId, boolean showType, String comment, String inUser) {
        SubLogsNewReq subLogsNewReq = new SubLogsNewReq();
        subLogsNewReq.setSubId(subId);
        subLogsNewReq.setType(1);
        subLogsNewReq.setShowType(showType);
        subLogsNewReq.setComment(comment);
        subLogsNewReq.setDTime(LocalDateTime.now());
        subLogsNewReq.setInUser(StringUtils.isNotBlank(inUser) ? inUser : "系统");
        return saveSubLog(subLogsNewReq);
    }

    /**
     * 保存订单日志
     *
     * @param subLogsNewReq
     */
    @Override
    public boolean saveLpSubLog(SubLogsNewReq subLogsNewReq) {
        try {
            R<Boolean> booleanR = subLogsCloud.addLpSubLog(subLogsNewReq);
            if (Objects.nonNull(booleanR) && Objects.equals(0, booleanR.getCode())) {
                return booleanR.getData();
            }
        } catch (Exception e) {
            log.error("保存良品订单日志异常，param={}", subLogsNewReq, e);
        }
        return false;
    }
}
