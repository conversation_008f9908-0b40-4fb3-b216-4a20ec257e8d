package com.jiuji.oa.logapi.service.impl;

import cn.hutool.core.lang.Assert;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.logapi.common.UrlUtils;
import com.jiuji.oa.logapi.mapper.DayInPriceCheckRemarkMapper;
import com.jiuji.oa.logapi.mapstruct.DayInPriceCheckRemarkMapStruct;
import com.jiuji.oa.logapi.pojo.dto.req.DayInPriceCheckRemarkReq;
import com.jiuji.oa.logapi.pojo.dto.res.DayInPriceCheckRemarkRes;
import com.jiuji.oa.logapi.pojo.entity.DayInPriceCheckRemark;
import com.jiuji.oa.logapi.service.IDayInPriceCheckRemarkService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 采购核价日志服务类
 *
 * <AUTHOR>
 * @date 2021/09/30
 */
@Service
@DS("oa_log")
public class DayInPriceCheckRemarkServiceImpl extends ServiceImpl<DayInPriceCheckRemarkMapper, DayInPriceCheckRemark>
        implements IDayInPriceCheckRemarkService {
    private static final String GET_METHOD = "getContent";
    private static final String SET_METHOD = "setContent";

    @Resource
    private DayInPriceCheckRemarkMapStruct dayInPriceCheckRemarkMapStruct;

    @Resource
    private DayInPriceCheckRemarkMapper dayInPriceCheckRemarkMapper;

    @Override
    public void insertLog(DayInPriceCheckRemarkReq req) {
        DayInPriceCheckRemark dayInPriceCheckRemark = dayInPriceCheckRemarkMapStruct.dayInPriceCheckRemarkReqToDayInPriceCheckRemark(req);
        this.save(dayInPriceCheckRemark);
    }

    @Override
    public void insertLogBatch(List<DayInPriceCheckRemarkReq> reqList) {
        List<DayInPriceCheckRemark> dayInPriceCheckRemarkList = dayInPriceCheckRemarkMapStruct
                .dayInPriceCheckRemarkReqListToDayInPriceCheckRemarkList(reqList);
        this.saveBatch(dayInPriceCheckRemarkList);
    }

    @Override
    public List<DayInPriceCheckRemarkRes> getDayInPrcCkRemByDayInPrcCkRemId(String dayInPrcCkRemId) {
        Assert.notBlank(dayInPrcCkRemId,"price-check-id 不能为空，请检查参数");
        List<DayInPriceCheckRemark> dayInPriceCheckRemarkList = this.lambdaQuery()
                .eq(DayInPriceCheckRemark::getDayInPriceCheckId, dayInPrcCkRemId).orderByDesc(DayInPriceCheckRemark::getCreateTime).list();
        List<DayInPriceCheckRemark> list = (List<DayInPriceCheckRemark>) UrlUtils.encodeComment(dayInPriceCheckRemarkList, GET_METHOD, SET_METHOD);
        return dayInPriceCheckRemarkMapStruct.dayInPriceCheckRemarkListToDayInPriceCheckRemarkResList(list);
    }

    @Override
    public List<DayInPriceCheckRemarkRes> getDayInPrcCkRemByDayInPrcCkRemBatchId(String dayInPrcCkRemIdArrayToStr) {
        Assert.notBlank(dayInPrcCkRemIdArrayToStr,"price-check-id-list 不能为空，请检查参数");
        String[] splitArray = dayInPrcCkRemIdArrayToStr.split(",");
        List<String> dayInPriceRemarkIdList = Arrays.stream(splitArray).distinct().collect(Collectors.toList());
        return getDayInPrcCkRemByDayInPrcCkRemBatchId(dayInPriceRemarkIdList);
    }

    @Override
    public List<DayInPriceCheckRemarkRes> getDayInPrcCkRemByRollbackId(List<String> rollbackIdList) {
        Assert.isFalse(CollectionUtils.isEmpty(rollbackIdList),"rollback-id 数组不能为空，请检查参数！");
        List<DayInPriceCheckRemark> dayInPriceCheckRemarkList = this.lambdaQuery().in(DayInPriceCheckRemark::getRollbackId, rollbackIdList).list();
        List<DayInPriceCheckRemark> list = (List<DayInPriceCheckRemark>) UrlUtils.encodeComment(dayInPriceCheckRemarkList, GET_METHOD, SET_METHOD);
        return dayInPriceCheckRemarkMapStruct.dayInPriceCheckRemarkListToDayInPriceCheckRemarkResList(list);
    }

    @Override
    public List<DayInPriceCheckRemarkRes> getLastRecord(String dayInPrcCkRemIdArrayToStr) {
        Assert.notBlank(dayInPrcCkRemIdArrayToStr,"price-check-id-list 不能为空，请检查参数");
        String[] splitArray = dayInPrcCkRemIdArrayToStr.split(",");
        List<String> dayInPriceCheckRemarkId = Arrays.stream(splitArray).distinct().collect(Collectors.toList());
        return getLastRecord(dayInPriceCheckRemarkId);
    }

    private List<DayInPriceCheckRemarkRes> getDayInPrcCkRemByDayInPrcCkRemBatchId(List<String> dayInPriceRemarkIdList) {
        List<DayInPriceCheckRemark> dayInPriceCheckRemarkList = this.lambdaQuery()
                .in(DayInPriceCheckRemark::getDayInPriceCheckId, dayInPriceRemarkIdList).list();
        List<DayInPriceCheckRemark> list = (List<DayInPriceCheckRemark>) UrlUtils.encodeComment(dayInPriceCheckRemarkList, GET_METHOD, SET_METHOD);
        return dayInPriceCheckRemarkMapStruct.dayInPriceCheckRemarkListToDayInPriceCheckRemarkResList(list);
    }

    private List<DayInPriceCheckRemarkRes> getLastRecord(List<String> dayInPriceRemarkIdList) {
        List<DayInPriceCheckRemark> lastRecordList = dayInPriceCheckRemarkMapper.getLastRecord(dayInPriceRemarkIdList);
        List<DayInPriceCheckRemark> list = (List<DayInPriceCheckRemark>) UrlUtils.encodeComment(lastRecordList, GET_METHOD, SET_METHOD);
        return dayInPriceCheckRemarkMapStruct.dayInPriceCheckRemarkListToDayInPriceCheckRemarkResList(list);
    }

}
