package com.jiuji.oa.logapi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.logapi.pojo.dto.req.ProductSnLogReq;
import com.jiuji.oa.logapi.pojo.dto.res.ProductSnLogRes;
import com.jiuji.oa.logapi.pojo.entity.ProductSnLog;

import java.util.List;

/**
 * iproduct sn日志服务
 *
 * <AUTHOR>
 * @date 2021/08/11
 */
public interface IProductSnLogService extends IService<ProductSnLog> {
    /**
     * 插入产品sn日志
     *
     * @param productSnLogReq 商品串号日志请求
     * @return {@link Long}
     */
    Long insertProductSnLog(ProductSnLogReq productSnLogReq);

    /**
     * 日志批量插入
     *
     * @param reqList 列表
     * @return {@link List}<{@link Long}>
     */
    List<Long> insertProductSnLogBatch(List<ProductSnLogReq> reqList);

    /**
     * 根据商品串号获取商品串号信息
     *
     * @param productSn 产品sn
     * @return {@link List}<{@link ProductSnLogRes}>
     */
    List<ProductSnLogRes> getProductSnLogListBySn(String productSn);

    /**
     * 根据rollbackId获取商品串号信息
     *
     * @param rollbackId 回滚id
     * @return {@link List}<{@link ProductSnLogRes}>
     */
    List<ProductSnLogRes> getProductSnLogListByRollbackId(List<String> rollbackId);

    /**
     * 获得最后一个记录
     *
     * @return {@link ProductSnLogRes}
     * @param snListStr
     */
    List<ProductSnLogRes> getLastRecord(String snListStr);

    List<ProductSnLogRes> getProductSnLogListByBatchSn(String snListStr);

    List<ProductSnLogRes> getProductSnLogListByBatchSnId(String snListStr);
}
