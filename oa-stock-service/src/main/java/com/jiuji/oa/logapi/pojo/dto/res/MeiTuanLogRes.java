package com.jiuji.oa.logapi.pojo.dto.res;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class MeiTuanLogRes {
    /**
     * id
     */
    private Long id;
    /**
     * 快递id
     */
    private String deliveryId;
    /**
     * 美团配送id
     */
    private String meiTunaPeiSongId;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 状态名
     */
    private String statusName;
    /**
     * 快递员姓名
     */
    private String courierName;
    /**
     * 快递员电话
     */
    private String courierPhone;
    /**
     * 取消原因id
     */
    private Integer cancelReasonId;
    /**
     * 取消的原因
     */
    private String cancelReason;
    /**
     * 时间戳
     */
    private Long timestamp;
    /**
     * 时间
     */
    private LocalDateTime time;
    /**
     * 操作时间
     */
    private LocalDateTime dTime;
    /**
     * 回滚id
     */
    private String rollbackId;

}
