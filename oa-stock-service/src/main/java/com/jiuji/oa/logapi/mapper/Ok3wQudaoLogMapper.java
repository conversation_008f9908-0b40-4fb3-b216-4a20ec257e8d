package com.jiuji.oa.logapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.logapi.pojo.entity.Ok3wQudaoLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 渠道日志映射器
 *
 * <AUTHOR>
 * @date 2021/08/11
 */
@Mapper
public interface Ok3wQudaoLogMapper extends BaseMapper<Ok3wQudaoLog> {
    List<Ok3wQudaoLog> getLastRecord(@Param("displayIdList") List<Long> displayIdList);
}
