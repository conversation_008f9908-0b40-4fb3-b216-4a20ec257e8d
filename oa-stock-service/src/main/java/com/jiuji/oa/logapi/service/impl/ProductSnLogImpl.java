package com.jiuji.oa.logapi.service.impl;

import cn.hutool.core.lang.Assert;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.logapi.common.UrlUtils;
import com.jiuji.oa.logapi.mapper.ProductSnLogMapper;
import com.jiuji.oa.logapi.mapstruct.ProductSnLogMapStruct;
import com.jiuji.oa.logapi.pojo.dto.req.ProductSnLogReq;
import com.jiuji.oa.logapi.pojo.dto.res.ProductSnLogRes;
import com.jiuji.oa.logapi.pojo.entity.ProductSnLog;
import com.jiuji.oa.logapi.service.IProductSnLogService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 商品串号日志服务实现类
 *
 * <AUTHOR>
 * @date 2021/09/30
 */
@Service
@DS("oa_log")
public class ProductSnLogImpl extends ServiceImpl<ProductSnLogMapper, ProductSnLog> implements IProductSnLogService {

    private static final String GET_METHOD = "getComment";
    private static final String SET_METHOD = "setComment";

    @Resource
    private ProductSnLogMapStruct productSnLogMapStruct;

    @Resource
    private ProductSnLogMapper productSnLogMapper;

    @Override
    public Long insertProductSnLog(ProductSnLogReq productSnLogReq) {
        ProductSnLog productSnLog = productSnLogMapStruct.productSnLogReqToProductSnLog(productSnLogReq);
        save(productSnLog);
        return productSnLog.getId();
    }

    @Override
    public List<Long> insertProductSnLogBatch(List<ProductSnLogReq> reqList) {
        List<ProductSnLog> productSnLogList = productSnLogMapStruct.productSnLogReqListToProductSnLogList(reqList);
        saveBatch(productSnLogList);
        List<Long> snLogIdList;
        snLogIdList = productSnLogList.stream().map(ProductSnLog::getId).collect(Collectors.toList());
        return snLogIdList;
    }

    @Override
    public List<ProductSnLogRes> getProductSnLogListBySn(String productSn) {
        Assert.notBlank(productSn, "sn 不能为为空，请检查参数！");
        List<ProductSnLog> list = lambdaQuery().eq(ProductSnLog::getProductSn, productSn).orderByDesc(ProductSnLog::getDTime).list();
        List<ProductSnLog> encodeAfterList = encode(list);
        return productSnLogMapStruct.productSnLogListToProductSnLogRes(encodeAfterList);
    }

    @Override
    public List<ProductSnLogRes> getProductSnLogListByRollbackId(List<String> rollbackId) {
        Assert.notEmpty(rollbackId, "rollbackId 数组不能为空，请检查参数！");
        List<ProductSnLog> list = lambdaQuery().in(ProductSnLog::getRollbackId, rollbackId).list();
        List<ProductSnLog> encodeAfterList = encode(list);
        return productSnLogMapStruct.productSnLogListToProductSnLogRes(encodeAfterList);
    }

    @Override
    public List<ProductSnLogRes> getLastRecord(String snListStr) {
        Assert.notBlank(snListStr, "sn-list 不能为空，请检查参数！");
        String[] snArray = snListStr.split(",");
        List<String> snList = Arrays.stream(snArray).distinct().collect(Collectors.toList());
        List<ProductSnLog> lastRecordList = productSnLogMapper.getLastRecord(snList);
        List<ProductSnLog> encodeAfterList = encode(lastRecordList);
        return productSnLogMapStruct.productSnLogListToProductSnLogRes(encodeAfterList);
    }

    @Override
    public List<ProductSnLogRes> getProductSnLogListByBatchSn(String snListStr) {
        if (StringUtils.isEmpty(snListStr)){
            return new ArrayList<>();
        }
        String[] snArray = snListStr.split(",");
        List<String> snList = Arrays.stream(snArray).distinct().collect(Collectors.toList());
        List<ProductSnLog> productSnLogList = this.lambdaQuery().in(ProductSnLog::getProductSn, snList).list();
        List<ProductSnLog> encodeAfterList = encode(productSnLogList);
        return productSnLogMapStruct.productSnLogListToProductSnLogRes(encodeAfterList);
    }

    @Override
    public List<ProductSnLogRes> getProductSnLogListByBatchSnId(String snIdListStr) {
        if (StringUtils.isEmpty(snIdListStr)){
            return new ArrayList<>();
        }
        String[] snIdArray = snIdListStr.split(",");
        List<String> snIdList = Arrays.stream(snIdArray).distinct().collect(Collectors.toList());
        List<ProductSnLog> productSnLogList = this.lambdaQuery().in(ProductSnLog::getProductSnId, snIdList).list();
        List<ProductSnLog> encodeAfterList = encode(productSnLogList);
        return productSnLogMapStruct.productSnLogListToProductSnLogRes(encodeAfterList);
    }

    private List<ProductSnLog> encode(List<ProductSnLog> quDaoLogList) {
        return (List<ProductSnLog>) UrlUtils.encodeComment(quDaoLogList, GET_METHOD, SET_METHOD);
    }
}
