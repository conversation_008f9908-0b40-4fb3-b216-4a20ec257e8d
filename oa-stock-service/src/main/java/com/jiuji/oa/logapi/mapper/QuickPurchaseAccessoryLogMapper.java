package com.jiuji.oa.logapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.logapi.pojo.entity.QuickPurchaseAccessoryLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface QuickPurchaseAccessoryLogMapper extends BaseMapper<QuickPurchaseAccessoryLog> {
    List<QuickPurchaseAccessoryLog> getLastRecord(@Param("subIdList") List<String> subIdList);
}
