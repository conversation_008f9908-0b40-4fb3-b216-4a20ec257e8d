package com.jiuji.oa.logapi.pojo.dto.req;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * 产品sn日志
 *
 * <AUTHOR>
 * @date 2021/08/11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductSnLogReq implements Serializable {
    /**
     * 用于C#事务回滚的id
     */
    @JsonAlias("id")
    private String rollbackId;
    /**
     * 商品id
     */
    @Valid
    @NotNull(message = "subId不能为空")
    private String subId;
    /**
     * 商品串号
     */
    @Valid
    @NotNull(message = "productSn不能为空")
    private String productSn;

    private Integer productSnId;
    /**
     * 操作用户
     */
    @Valid
    @NotNull(message = "inUser不能为空")
    private String inUser;
    /**
     * 显示类型
     */
    @Valid
    @NotNull(message = "showType不能为空")
    private Boolean showType;
    /**
     * 备注
     */
    @NotNull(message = "comment不能为空")
    private String comment;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dTime;
}
