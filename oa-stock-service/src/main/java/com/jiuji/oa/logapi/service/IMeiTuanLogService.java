package com.jiuji.oa.logapi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.logapi.pojo.dto.req.MeiTuanLogReq;
import com.jiuji.oa.logapi.pojo.dto.res.MeiTuanLogRes;
import com.jiuji.oa.logapi.pojo.entity.MeiTuanLog;

import java.util.List;

/**
 * 美团日志服务接口
 *
 * <AUTHOR>
 * @date 2021/09/28
 */
public interface IMeiTuanLogService extends IService<MeiTuanLog> {
    /**
     * 插入日志
     *
     * @param logReq 日志请求
     */
    void insertLog(MeiTuanLogReq logReq);

    /**
     * 日志批量插入
     *
     * @param logReqList 日志请求列表
     */
    void insertLogBatch(List<MeiTuanLogReq> logReqList);

    /**
     * 根据deliveryId查询美团日志
     *
     * @param deliveryId 快递id
     * @return {@link List}<{@link MeiTuanLogRes}>
     */
    List<MeiTuanLogRes> getMeiTuanLogByDeliveryId(String deliveryId);

    /**
     * 根据deliveryId批量查询美团日志
     *
     * @param deliveryIdArrayToStr 快递id数组字符串
     * @return {@link List}<{@link MeiTuanLogRes}>
     */
    List<MeiTuanLogRes> getMeiTuanLogByDeliveryBatchId(String deliveryIdArrayToStr);

    /**
     * 获得最后一个记录
     *
     * @param deliveryIdArrayToStr 快递id数组字符串
     * @return {@link List}<{@link MeiTuanLogRes}>
     */
    List<MeiTuanLogRes> getLastRecord(String deliveryIdArrayToStr);

    /**
     * 根据rollbackId查询美团日志
     *
     * @param rollbackIdList 回滚id列表
     * @return {@link List}<{@link MeiTuanLogRes}>
     */
    List<MeiTuanLogRes> getMeiTuanLogByRollbackId(List<String> rollbackIdList);
}
