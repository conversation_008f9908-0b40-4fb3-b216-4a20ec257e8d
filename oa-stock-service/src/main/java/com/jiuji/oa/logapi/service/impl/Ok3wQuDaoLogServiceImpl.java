package com.jiuji.oa.logapi.service.impl;

import cn.hutool.core.lang.Assert;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.logapi.common.UrlUtils;
import com.jiuji.oa.logapi.mapper.Ok3wQudaoLogMapper;
import com.jiuji.oa.logapi.mapstruct.Ok3wQudaoLogMapStruct;
import com.jiuji.oa.logapi.pojo.dto.req.Ok3wQudaoLogReq;
import com.jiuji.oa.logapi.pojo.dto.res.Ok3wQudaoLogRes;
import com.jiuji.oa.logapi.pojo.entity.Ok3wQudaoLog;
import com.jiuji.oa.logapi.service.IOk3wQuDaoLogService;
import com.jiuji.oa.nc.channel.document.Ok3wQudaoLogDocument;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Service
@DS("oa_log")
public class Ok3wQuDaoLogServiceImpl extends ServiceImpl<Ok3wQudaoLogMapper, Ok3wQudaoLog> implements IOk3wQuDaoLogService {

    private static final String GET_METHOD = "getComment";
    private static final String SET_METHOD = "setComment";

    @Resource
    private Ok3wQudaoLogMapStruct qudaoLogMapStruct;

    @Resource
    private Ok3wQudaoLogMapper qudaoLogMapper;

    /**
     * 为了兼容之前的代码  所以需要还是要把Ok3wQudaoLog转化成Ok3wQudaoLogDocument
     * @param channelId 渠道id
     * @return 渠道日志文档
     */
    @Override
    public Ok3wQudaoLogDocument getOk3wQudaoLogDocument(Long channelId) {
        List<Ok3wQudaoLog> list = this.lambdaQuery().eq(Ok3wQudaoLog::getDisplayId, channelId)
                .list();
        if(!CollectionUtils.isEmpty(list)){
            List<Ok3wQudaoLogDocument.Conts> contsList = list.stream().map((Ok3wQudaoLog item) -> {
                Ok3wQudaoLogDocument.Conts conts = new Ok3wQudaoLogDocument.Conts();
                conts.setComment(item.getComment())
                        .setInUser(item.getInUser())
                        .setDisplayId(item.getDisplayId() != null ? item.getDisplayId().intValue() : null)
                        .setDTime(item.getDTime());
                return conts;
            }).collect(Collectors.toList());
            Ok3wQudaoLogDocument ok3wQudaoLogDocument = new Ok3wQudaoLogDocument();
            ok3wQudaoLogDocument.setConts(contsList);
            return ok3wQudaoLogDocument;
        }
        return null;

    }

    @Override
    public Long insertQuDaoLog(Ok3wQudaoLogReq qudaoLogReq) {
        Ok3wQudaoLog qudaoLog = qudaoLogMapStruct.qudaoLogReqToQudaoLog(qudaoLogReq);
        save(qudaoLog);
        return qudaoLog.getId();
    }

    @Override
    public List<Long> insertQudaoLogBatch(List<Ok3wQudaoLogReq> qudaoLogReqsList) {
        List<Ok3wQudaoLog> qudaoLogList = qudaoLogMapStruct.qudaoLogReqListToQudaoLogList(qudaoLogReqsList);
        saveBatch(qudaoLogList);
        List<Long> qudaoLogIdList;
        qudaoLogIdList = qudaoLogList.stream().map(Ok3wQudaoLog::getId).collect(Collectors.toList());
        return qudaoLogIdList;
    }

    @Override
    public List<Ok3wQudaoLogRes> getQudaoLogByDisplayId(Long displayId) {
        Assert.notNull(displayId,"displayId 不能为空，请检查参数！");
        List<Ok3wQudaoLog> list = lambdaQuery().eq(Ok3wQudaoLog::getDisplayId, displayId).orderByDesc(Ok3wQudaoLog::getDTime).list();
        List<Ok3wQudaoLog> encodeAfterList = encode(list);
        return qudaoLogMapStruct.qudaoLogListToOk3wQudaoLogResList(encodeAfterList);
    }

    @Override
    public List<Ok3wQudaoLogRes> getQudaoLogByRollbackId(List<String> rollbackId) {
        Assert.notEmpty(rollbackId,"rollbackId 数组不能为空，请检查参数！");
        List<Ok3wQudaoLog> list = lambdaQuery().in(Ok3wQudaoLog::getRollbackId, rollbackId).list();
        List<Ok3wQudaoLog> encodeAfterList = encode(list);
        return qudaoLogMapStruct.qudaoLogListToOk3wQudaoLogResList(encodeAfterList);
    }

    @Override
    public List<Ok3wQudaoLogRes> getLastRecord(String displayIdArrayToStr) {
        Assert.notBlank(displayIdArrayToStr,"display-id-list 不能为空，请检查参数！");
        String[] displayIdArray = displayIdArrayToStr.split(",");
        List<Long> displayIdList = Arrays.stream(displayIdArray).map(Long::parseLong).distinct().collect(Collectors.toList());
        List<Ok3wQudaoLog> lastRecordList = qudaoLogMapper.getLastRecord(displayIdList);
        List<Ok3wQudaoLog> encodeAfterList = encode(lastRecordList);
        return qudaoLogMapStruct.qudaoLogListToOk3wQudaoLogResList(encodeAfterList);
    }

    @Override
    public List<Ok3wQudaoLogRes> getQudaoLogByBatchDisplayId(String displayIdArrayToStr) {
        Assert.notBlank(displayIdArrayToStr,"display-id-list 不能为空，请检查参数！");
        String[] displayIdArray = displayIdArrayToStr.split(",");
        List<Long> displayIdList = Arrays.stream(displayIdArray).map(Long::parseLong).distinct().collect(Collectors.toList());
        List<Ok3wQudaoLog> quDaoLogList = this.lambdaQuery().in(Ok3wQudaoLog::getDisplayId, displayIdList).list();
        List<Ok3wQudaoLog> encodeAfterList = encode(quDaoLogList);
        return qudaoLogMapStruct.qudaoLogListToOk3wQudaoLogResList(encodeAfterList);
    }

    private List<Ok3wQudaoLog> encode(List<Ok3wQudaoLog> quDaoLogList) {
        return (List<Ok3wQudaoLog>) UrlUtils.encodeComment(quDaoLogList, GET_METHOD, SET_METHOD);
    }
}
