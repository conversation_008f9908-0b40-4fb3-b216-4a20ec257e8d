package com.jiuji.oa.logapi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.logapi.pojo.dto.req.DayInPriceCheckRemarkReq;
import com.jiuji.oa.logapi.pojo.dto.res.DayInPriceCheckRemarkRes;
import com.jiuji.oa.logapi.pojo.entity.DayInPriceCheckRemark;

import java.util.List;

/**
 * iday价格检查评论服务
 *
 * <AUTHOR>
 * @date 2021/09/27
 */
public interface IDayInPriceCheckRemarkService extends IService<DayInPriceCheckRemark> {
    /**
     * 插入日志
     *
     * @param req 请求体
     */
    void insertLog(DayInPriceCheckRemarkReq req);

    /**
     * 插入日志批量
     *
     * @param reqList 请求列表
     */
    void insertLogBatch(List<DayInPriceCheckRemarkReq> reqList);

    /**
     * 根据 DayInPrcCkRemId 获取 DayInPrcCkRem 列表
     * 注：DayInPrcCkRem 是 DayInPriceCheckRemark 的缩写
     *
     * @param dayInPrcCkRemId DayInPrcCkRemId
     * @return {@link List}<{@link DayInPriceCheckRemarkRes}>
     */
    List<DayInPriceCheckRemarkRes> getDayInPrcCkRemByDayInPrcCkRemId(String dayInPrcCkRemId);

    /**
     * 根据 DayInPrcCkRemId 批量获取 DayInPrcCkRem 列表
     * 注：DayInPrcCkRem 是 DayInPriceCheckRemark 的缩写
     *
     * @param dayInPrcCkRemIdArrayToStr DayInPrcCkRemId数组转str
     * @return {@link List}<{@link DayInPriceCheckRemarkRes}>
     */
    List<DayInPriceCheckRemarkRes> getDayInPrcCkRemByDayInPrcCkRemBatchId(String dayInPrcCkRemIdArrayToStr);

    /**
     * 通过 rollbackId 查询
     *
     * @param rollbackIdList 回滚id
     * @return {@link List}<{@link DayInPriceCheckRemarkRes}>
     */
    List<DayInPriceCheckRemarkRes> getDayInPrcCkRemByRollbackId(List<String> rollbackIdList);

    /**
     * 获得最后一个记录
     *
     * @param dayInPrcCkRemIdArrayToStr DayInPrcCkRemId数组转str
     * @return {@link List}<{@link DayInPriceCheckRemarkRes}>
     */
    List<DayInPriceCheckRemarkRes> getLastRecord(String dayInPrcCkRemIdArrayToStr);

}
