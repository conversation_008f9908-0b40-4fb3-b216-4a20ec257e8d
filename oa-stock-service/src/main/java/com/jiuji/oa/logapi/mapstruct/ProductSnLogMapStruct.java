package com.jiuji.oa.logapi.mapstruct;

import com.jiuji.oa.logapi.pojo.dto.req.ProductSnLogReq;
import com.jiuji.oa.logapi.pojo.dto.res.ProductSnLogRes;
import com.jiuji.oa.logapi.pojo.entity.ProductSnLog;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * 商品sn日志映射结构
 *
 * <AUTHOR>
 * @date 2021/08/11
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ProductSnLogMapStruct {
    /**
     * 请求体转实体
     *
     * @param productSnLogReq 产品sn日志请求
     * @return {@link ProductSnLog}
     */
    ProductSnLog productSnLogReqToProductSnLog(ProductSnLogReq productSnLogReq);

    /**
     *
     * @param productSnLog
     * @return {@link ProductSnLogRes}
     */
    ProductSnLogRes productSnLogToProductSnLogRes(ProductSnLog productSnLog);

    /**
     * entity列表转res列表
     *
     * @param productSnLogList 产品sn日志列表
     * @return {@link ProductSnLogRes}
     */
    List<ProductSnLogRes> productSnLogListToProductSnLogRes(List<ProductSnLog> productSnLogList);

    /**
     * 请求体转实体
     *
     * @param productSnLogReqList 产品sn日志请求
     * @return {@link ProductSnLog}
     */
    List<ProductSnLog> productSnLogReqListToProductSnLogList(List<ProductSnLogReq> productSnLogReqList);
}
