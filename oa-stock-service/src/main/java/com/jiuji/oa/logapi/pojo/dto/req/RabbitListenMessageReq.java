package com.jiuji.oa.logapi.pojo.dto.req;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * MQ 接收消息
 *
 * <AUTHOR>
 * @date 2021/09/03
 */
@Data
@Accessors(chain = true)
public class RabbitListenMessageReq implements Serializable {
    /**
     * 用于C#事务回滚的id
     */
    private String rollbackId;
    /**
     * 日志类型
     */
    private Integer logType;
}
