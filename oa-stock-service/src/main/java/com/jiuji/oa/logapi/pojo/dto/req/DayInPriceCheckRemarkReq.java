package com.jiuji.oa.logapi.pojo.dto.req;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 采购核价请求体
 *
 * <AUTHOR>
 * @date 2021/09/27
 */
@Data
public class DayInPriceCheckRemarkReq implements Serializable {
    /**
     * C# 指定回滚id
     */
    @JsonAlias("id")
    private String rollbackId;
    /**
     * 工号
     */
    @NotNull(message = "操作人工号不能为空")
    @NotBlank(message = "操作人工号不能为空")
    private String ch999Id;
    /**
     * 操作人
     */
    @NotNull(message = "操作人姓名不能为空")
    @NotBlank(message = "操作人姓名不能为空")
    private String ch999Name;
    /**
     * 内容
     */
    @NotNull(message = "说明内容不能为空")
    @NotBlank(message = "说明内容不能为空")
    private String content;
    /**
     * 采购核价Id
     */
    @NotNull(message = "采购核价 id 不能为空")
    @NotBlank(message = "采购核价 id 不能为空")
    private String dayInPriceCheckId;
}
