package com.jiuji.oa.logapi.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.HashBasedTable;
import com.jiuji.oa.logapi.common.UrlUtils;
import com.jiuji.oa.logapi.mapper.MkcLogNewMapper;
import com.jiuji.oa.logapi.mapstruct.MkcLogNewMapStruct;
import com.jiuji.oa.logapi.pojo.dto.req.MkcLogNewReq;
import com.jiuji.oa.logapi.pojo.dto.res.MkcLogNewRes;
import com.jiuji.oa.logapi.pojo.entity.MkcLogNew;
import com.jiuji.oa.logapi.service.IMkcLogNewService;
import com.jiuji.oa.stock.common.util.StockUtils;
import com.jiuji.oa.stock.purchaseList.dto.MkcLog;
import com.jiuji.oa.stock.purchaseList.dto.MkcLogDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 大件库存日志服务实现类
 *
 * <AUTHOR>
 * @date 2021/08/16
 */
@Slf4j
@Service
@DS("oa_log")
public class MkcLogNewServiceImpl extends ServiceImpl<MkcLogNewMapper, MkcLogNew> implements IMkcLogNewService {

    private static final String GET_METHOD = "getComment";
    private static final String SET_METHOD = "setComment";
    private static final String LOG_KEY = "mkcLogsNew";

    /**
     * 结构映射
     */
    @Resource
    private MkcLogNewMapStruct mkcLogNewMapStruct;

    @Resource
    private MkcLogNewMapper mkcLogNewMapper;

    @Resource
    private MongoTemplate mongoTemplate;


    /**
     * 插入大件库存日志
     *
     * @param mkcLogNewReq 廊坊开发区日志的新要求
     */
    @Override
    public Long insertMkcLog(MkcLogNewReq mkcLogNewReq) {
        MkcLogNew mkcLogNew = mkcLogNewMapStruct.mkcLogNewReqToMkcLogNew(mkcLogNewReq);
        save(mkcLogNew);
        return mkcLogNew.getId();
    }

    /**
     * 批量插入大件库存日志
     *
     * @param mkcLogNewReqList 廊坊开发区日志新要求的列表
     */
    @Override
    public List<Long> insertMkcLogBatch(List<MkcLogNewReq> mkcLogNewReqList) {
        List<MkcLogNew> mkcLogNewList = mkcLogNewMapStruct.mkcLogNewReqToMkcLogNew(mkcLogNewReqList);
        saveBatch(mkcLogNewList);
        List<Long> logIdList;
        logIdList = mkcLogNewList.stream().map(MkcLogNew::getId).collect(Collectors.toList());
        return logIdList;
    }

    @Override
    public List<MkcLogNewRes> getMkcLogListByMkcId(Long mkcId) {
        Assert.notNull(mkcId, "mkcId 不能为空，请检查参数！");
        List<MkcLogNew> mkcLogNewList = lambdaQuery().eq(MkcLogNew::getMkcId, mkcId).orderByDesc(MkcLogNew::getDTime).list();
        Query query = new Query();
        query.addCriteria(Criteria.where("_id").in(mkcId));
        List<MkcLogDTO> mongoList = mongoTemplate.find(query, MkcLogDTO.class, LOG_KEY);
        if (CollectionUtils.isNotEmpty(mongoList)) {
            MkcLogDTO mkcLogDTO = mongoList.get(0);
            List<MkcLog> conts = mkcLogDTO.getConts();
            if (conts.size() > mkcLogNewList.size()) {
                mkcLogNewList = mkcLogNewMapStruct.mkcLogToMkcLogNew(conts);
                mkcLogNewList.forEach((MkcLogNew x) -> x.setMkcId(mkcId));
            }
        }
        //排序
        mkcLogNewList = mkcLogNewList.stream().sorted(Comparator.comparing(MkcLogNew::getDTime)).collect(Collectors.toList());
        mkcLogNewList.forEach(v -> {
            v.setComment(StockUtils.handleMkcLogImei(v.getMkcId(), v.getComment()));
            v.setComment(StockUtils.handleOaOrMoaUrl(v.getComment()));
        });
        List<MkcLogNew> list = (List<MkcLogNew>) UrlUtils.encodeComment(mkcLogNewList, GET_METHOD, SET_METHOD);
        return mkcLogNewMapStruct.mkcLogNewListToMkcLogNewResList(list);
    }

    @Override
    public List<MkcLogNewRes> getMkcLogListByRollbackId(List<String> rollbackId) {
        Assert.notEmpty(rollbackId, "rollbackId 数组不能为空，请检查参数");
        List<MkcLogNew> mkcLogNewList = lambdaQuery().in(MkcLogNew::getRollbackId, rollbackId).list();
        List<MkcLogNew> list = (List<MkcLogNew>) UrlUtils.encodeComment(mkcLogNewList, GET_METHOD, SET_METHOD);
        return mkcLogNewMapStruct.mkcLogNewListToMkcLogNewResList(list);
    }

    @Override
    public List<MkcLogNewRes> getLastRecord(String mkcIdArrayToStr) {
        Assert.notBlank(mkcIdArrayToStr, "mkc-id-list 不能为空，请检查参数！");
        String[] idList = mkcIdArrayToStr.split(",");
        List<Long> mkcIdList = Arrays.stream(idList).map(Long::parseLong).distinct().collect(Collectors.toList());
        return getLastRecord(mkcIdList);
    }

    @Override
    public List<MkcLogNewRes> getLastRecord(List<Long> mkcIdList) {
        List<MkcLogNew> lastRecordList = mkcLogNewMapper.getLastRecord(mkcIdList);
        List<MkcLogNew> list = (List<MkcLogNew>) UrlUtils.encodeComment(lastRecordList, GET_METHOD, SET_METHOD);
        return mkcLogNewMapStruct.mkcLogNewListToMkcLogNewResList(list);
    }

    @Override
    public List<MkcLogNewRes> getMkcLogListByMkcBatchId(String mkcIdArrayToStr) {
        Assert.notBlank(mkcIdArrayToStr, "mkc-id-list 不能为空，请检查参数！");
        String[] idList = mkcIdArrayToStr.split(",");
        List<Long> mkcIdList = Arrays.stream(idList).map(Long::parseLong).distinct().collect(Collectors.toList());
        return getMkcLogListByMkcBatchId(mkcIdList);
    }

    @Override
    public List<MkcLogNewRes> getMkcLogListByMkcBatchId(List<Long> mkcIdList) {
        List<MkcLogNew> mkcLogNewList = this.lambdaQuery().in(MkcLogNew::getMkcId, mkcIdList).list();
        for (Long mkcId : mkcIdList) {
            Query query = new Query();
            query.addCriteria(Criteria.where("_id").in(mkcId));
            List<MkcLogDTO> mongoList = mongoTemplate.find(query, MkcLogDTO.class, LOG_KEY);
            if (CollectionUtils.isNotEmpty(mongoList)) {
                MkcLogDTO mkcLogDTO = mongoList.get(0);
                List<MkcLog> conts = mkcLogDTO.getConts();
                if (conts.size() > mkcLogNewList.size()) {
                    mkcLogNewList = mkcLogNewMapStruct.mkcLogToMkcLogNew(conts);
                    mkcLogNewList.forEach((MkcLogNew x) -> x.setMkcId(mkcId));
                }
            }
        }
        List<MkcLogNew> list = (List<MkcLogNew>) UrlUtils.encodeComment(mkcLogNewList, GET_METHOD, SET_METHOD);
        return mkcLogNewMapStruct.mkcLogNewListToMkcLogNewResList(list);
    }
}
