package com.jiuji.oa.logapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.logapi.pojo.entity.OaDisplayLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface OaDisplayLogMapper extends BaseMapper<OaDisplayLog> {
    List<OaDisplayLog> getLastRecord(@Param("subIdList") List<Long> subIdList);
}
