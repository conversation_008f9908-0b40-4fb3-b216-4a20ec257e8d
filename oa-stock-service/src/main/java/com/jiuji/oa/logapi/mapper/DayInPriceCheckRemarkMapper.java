package com.jiuji.oa.logapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.logapi.pojo.entity.DayInPriceCheckRemark;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DayInPriceCheckRemarkMapper extends BaseMapper<DayInPriceCheckRemark> {
    List<DayInPriceCheckRemark> getLastRecord(@Param("dayInPriceRemarkIdList") List<String> dayInPriceRemarkIdList);
}
