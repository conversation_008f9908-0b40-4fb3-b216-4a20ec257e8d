package com.jiuji.oa.logapi.controller;

import com.jiuji.oa.logapi.common.LogUtils;
import com.jiuji.oa.logapi.common.ValidatedList;
import com.jiuji.oa.logapi.pojo.dto.req.DayInPriceCheckRemarkReq;
import com.jiuji.oa.logapi.pojo.dto.res.DayInPriceCheckRemarkRes;
import com.jiuji.oa.logapi.service.IDayInPriceCheckRemarkService;
import com.jiuji.tc.common.vo.R;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 采购核价日志控制器
 *
 * <AUTHOR>
 * @date 2021/09/30
 */
@RestController
@RequestMapping("/api/oaApp/price-check/remark")
public class DayInPriceCheckRemarkController {
    @Resource
    private IDayInPriceCheckRemarkService dayInPriceCheckRemarkService;

    @PostMapping("/insert/v1")
    public R<String> insertLog(
            @Valid @RequestBody DayInPriceCheckRemarkReq req,
            @RequestHeader(value = "token", required = false) String token) {
        LogUtils.checkToken(token);
        dayInPriceCheckRemarkService.insertLog(req);
        return R.success("插入成功");
    }

    @PostMapping("/insert/batch/v1")
    public R<String> insertBatchLog(
            @Valid @RequestBody ValidatedList<DayInPriceCheckRemarkReq> reqList,
            @RequestHeader(value = "token", required = false) String token) {
        LogUtils.checkToken(token);
        dayInPriceCheckRemarkService.insertLogBatch(reqList);
        return R.success("插入成功");
    }

    @GetMapping("/list/v1")
    public R<List<DayInPriceCheckRemarkRes>> getLogListByDayInPriceCheckRemarkId(
            @RequestParam("price-check-id") String priceCheckId,
            @RequestHeader(value = "token", required = false) String token) {
        LogUtils.checkToken(token);
        List<DayInPriceCheckRemarkRes> logList = dayInPriceCheckRemarkService.getDayInPrcCkRemByDayInPrcCkRemId(priceCheckId);
        return R.success("查询成功", logList);
    }

    @GetMapping("/list/batch/v1")
    public R<List<DayInPriceCheckRemarkRes>> getLogListByDayInPriceCheckRemarkBatchId(
            @RequestParam("price-check-id-list") String priceCheckBatchId,
            @RequestHeader(value = "token", required = false) String token) {
        LogUtils.checkToken(token);
        List<DayInPriceCheckRemarkRes> logList = dayInPriceCheckRemarkService.getDayInPrcCkRemByDayInPrcCkRemBatchId(priceCheckBatchId);
        return R.success("查询成功", logList);
    }

    @PostMapping("/list/rollback-id/v1")
    public R<List<DayInPriceCheckRemarkRes>> getLogListByRollbackId(
            @RequestBody List<String> rollbackIdList,
            @RequestHeader(value = "token", required = false) String token) {
        LogUtils.checkToken(token);
        List<DayInPriceCheckRemarkRes> logList = dayInPriceCheckRemarkService.getDayInPrcCkRemByRollbackId(rollbackIdList);
        return R.success("查询成功", logList);
    }

    @GetMapping("/last/record/v1")
    public R<List<DayInPriceCheckRemarkRes>> getLastRecord(
            @RequestParam("price-check-id-list") String priceCheckBatchId,
            @RequestHeader(value = "token", required = false) String token) {
        LogUtils.checkToken(token);
        List<DayInPriceCheckRemarkRes> lastRecord = dayInPriceCheckRemarkService.getLastRecord(priceCheckBatchId);
        return R.success("查询成功", lastRecord);
    }

}
