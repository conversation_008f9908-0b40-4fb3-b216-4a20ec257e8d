package com.jiuji.oa.logapi.service;

import com.jiuji.oa.loginfo.order.vo.req.SubLogsNewReq;

/**
 * <AUTHOR>
 */
public interface ISubLogService {
    /**
     * 保存订单日志
     * @param subLogsNewReq
     */
    boolean saveSubLog(SubLogsNewReq subLogsNewReq);

    /**
     * 保存订单日志
     * @param subId
     * @param showType
     * @param comment
     * @param inUser
     */
    boolean saveSubLog(Integer subId,boolean showType, String comment, String inUser);

    boolean saveLpSubLog(SubLogsNewReq subLogsNewReq);
}
