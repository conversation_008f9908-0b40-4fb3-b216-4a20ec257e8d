package com.jiuji.oa.logapi.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 廊坊开发区新日志
 *
 * <AUTHOR>
 * @date 2021/08/16
 */
@Data
@Accessors(chain = true)
public class MkcLogNew implements Serializable {
    /**
     * id
     */
    private Long id;
    /**
     * 操作时间
     */
    private LocalDateTime dTime;
    /**
     * 操作用户
     */
    private String inUser;
    /**
     * 显示类型
     */
    private Boolean showType;
    /**
     * 备注
     */
    private String comment;
    /**
     * mkcid
     */
    private Long mkcId;
    /**
     * 是否逻辑删除
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 用于C#事务回滚的id，由c#传入
     */
    private String rollbackId;
}
