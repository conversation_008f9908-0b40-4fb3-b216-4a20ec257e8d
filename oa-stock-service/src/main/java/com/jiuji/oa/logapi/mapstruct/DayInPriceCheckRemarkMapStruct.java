package com.jiuji.oa.logapi.mapstruct;

import com.jiuji.oa.logapi.pojo.dto.req.DayInPriceCheckRemarkReq;
import com.jiuji.oa.logapi.pojo.dto.res.DayInPriceCheckRemarkRes;
import com.jiuji.oa.logapi.pojo.entity.DayInPriceCheckRemark;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * 采购核价结构映射
 *
 * <AUTHOR>
 * @date 2021/09/27
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DayInPriceCheckRemarkMapStruct {
    /**
     * 采购核价请求类转采购核价实体类
     *
     * @param req 请求
     * @return {@link DayInPriceCheckRemark}
     */
    DayInPriceCheckRemark dayInPriceCheckRemarkReqToDayInPriceCheckRemark(DayInPriceCheckRemarkReq req);

    /**
     * 采购核价请求类列表转采购核价实体类列表
     *
     * @param reqList 请求列表
     * @return {@link DayInPriceCheckRemark}
     */
    List<DayInPriceCheckRemark> dayInPriceCheckRemarkReqListToDayInPriceCheckRemarkList(List<DayInPriceCheckRemarkReq> reqList);

    /**
     * 采购核价实体类转采购核价响应类
     *
     * @param dayInPriceCheckRemarkList 采购核价实体类
     * @return {@link DayInPriceCheckRemarkRes}
     */
    List<DayInPriceCheckRemarkRes> dayInPriceCheckRemarkListToDayInPriceCheckRemarkResList(List<DayInPriceCheckRemark> dayInPriceCheckRemarkList);
}
