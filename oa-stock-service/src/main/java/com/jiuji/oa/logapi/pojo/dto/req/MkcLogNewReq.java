package com.jiuji.oa.logapi.pojo.dto.req;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 大件库存日志
 *
 * <AUTHOR>
 * @date 2021/08/16
 */
@Data
@Accessors(chain = true)
public class MkcLogNewReq implements Serializable {
    /**
     * 用于C#事务回滚的id
     */
    @JsonAlias("id")
    private String rollbackId;
    /**
     * 操作用户
     */
    @Valid
    @NotNull(message = "inUser不能为空")
    private String inUser;
    /**
     * 显示类型
     */
    @Valid
    @NotNull(message = "showType不能为空")
    private Boolean showType;
    /**
     * 备注
     */
    @Valid
    @NotNull(message = "comment不能为空")
    private String comment;
    /**
     * 大件库存id
     */
    @Valid
    @NotNull(message = "mkcId不能为空")
    private Long mkcId;

    private LocalDateTime dTime;
}
