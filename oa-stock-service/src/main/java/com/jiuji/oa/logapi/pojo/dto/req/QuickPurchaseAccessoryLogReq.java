package com.jiuji.oa.logapi.pojo.dto.req;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 配件快捷采购请求体
 *
 * <AUTHOR>
 * @date 2021/09/27
 */
@Data
public class QuickPurchaseAccessoryLogReq implements Serializable {
    /**
     * C#指定，rollbackId
     */
    @JsonAlias("id")
    private String rollbackId;
    /**
     * subId
     */
    @Valid
    @NotNull(message = "subId 不能为空")
    private String subId;
    /**
     * 操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dTime;
    /**
     * 备注内容
     */
    @Valid
    @NotNull(message = "comment 不能为空")
    private String comment;
    /**
     * 操作人
     */
    private String inUser;
    /**
     * 日志类型
     */
    private Integer logType;
    /**
     * 是否显示在前台标识
     */
    @Valid
    @NotNull(message = "showType 不能为空")
    private Boolean showType;
}
