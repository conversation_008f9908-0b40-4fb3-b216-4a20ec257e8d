package com.jiuji.oa.logapi.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 美团日志
 *
 * <AUTHOR>
 * @date 2021/09/28
 */
@Data
@TableName("meituan_log")
public class MeiTuanLog {
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 快递id
     */
    private String deliveryId;
    /**
     * 美团配送id
     */
    @TableField("meituan_peisong_id")
    private String meiTunaPeiSongId;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 状态名
     */
    private String statusName;
    /**
     * 快递员姓名
     */
    private String courierName;
    /**
     * 快递员电话
     */
    private String courierPhone;
    /**
     * 取消原因id
     */
    private Integer cancelReasonId;
    /**
     * 取消的原因
     */
    private String cancelReason;
    /**
     * 时间戳
     */
    private Long timestamp;
    /**
     * 时间
     */
    private LocalDateTime time;
    /**
     * 操作时间
     */
    private LocalDateTime dTime;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 删除
     */
    @TableLogic
    @TableField("is_deleted")
    private Boolean deleted;
    /**
     * 回滚id
     */
    private String rollbackId;
}
