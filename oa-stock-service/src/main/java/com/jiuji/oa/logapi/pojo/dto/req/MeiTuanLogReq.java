package com.jiuji.oa.logapi.pojo.dto.req;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class MeiTuanLogReq {
    /**
     * 回滚id
     */
    @JsonAlias("id")
    private String rollbackId;
    /**
     * 快递id
     */
    private String deliveryId;
    /**
     * 美团配送id
     */
    private String meiTunaPeiSongId;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 状态名
     */
    private String statusName;
    /**
     * 快递员姓名
     */
    private String courierName;
    /**
     * 快递员电话
     */
    private String courierPhone;
    /**
     * 取消原因id
     */
    private Integer cancelReasonId;
    /**
     * 取消的原因
     */
    private String cancelReason;
    /**
     * 时间戳
     */
    private Long timestamp;
    /**
     * 时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime time;
    /**
     * 操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dTime;
}
