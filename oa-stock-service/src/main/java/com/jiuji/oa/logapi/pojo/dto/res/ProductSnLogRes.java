package com.jiuji.oa.logapi.pojo.dto.res;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * 产品sn日志响应提
 *
 * <AUTHOR>
 * @date 2021/08/11
 */
@Data
@EqualsAndHashCode
public class ProductSnLogRes implements Serializable {
    /**
     * 表id
     */
    private Long id;
    /**
     * 商品id
     */
    private String subId;
    /**
     * 商品串号
     */
    private String productSn;
    /**
     * 操作时间
     */
    private LocalDateTime dTime;
    /**
     * 操作用户
     */
    private String inUser;
    /**
     * 显示类型
     */
    private Boolean showType;
    /**
     * 备注
     */
    private String comment;
    /**
     * 用于C#事务回滚的id
     */
    private String rollbackId;
}
