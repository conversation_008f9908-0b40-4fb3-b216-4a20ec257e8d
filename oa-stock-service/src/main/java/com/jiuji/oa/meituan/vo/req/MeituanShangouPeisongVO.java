package com.jiuji.oa.meituan.vo.req;

import lombok.Data;

/**
 * 自配送商家同步发货状态和配送信息
 * <AUTHOR>
 * @date 2022/5/5 10:11
 */
@Data
public class MeituanShangouPeisongVO {
    /**
     * 美团订单号
     */
    private String orderId;

    /**
     * 第三方配送商物流单号
     */
    private String thirdCarrierOrderId;

    /**
     * 物流ID
     */
    private String wuliuId;

    /**
     * 配送员姓名
     */
    private String courierName;

    /**
     * 配送员联系方式
     */
    private String courierPhone;

    /**
     * 配送此订单商品的物流平台
     */
    private Integer logisticsProviderCode;

    /**
     * 配送状态code
     */
    private Integer logisticsStatus;

    /**
     * 骑手当前的纬度
     */
    private String latitude;

    /**
     * 骑手当前的经度
     */
    private String longitude;
}
