package com.jiuji.oa.meituan.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.meituan.entity.MeituanShangouPeisong;
import com.jiuji.oa.meituan.service.IMeituanShangouPeisongService;
import com.jiuji.oa.meituan.mapper.MeituanShangouPeisongMapper;
import com.jiuji.oa.meituan.vo.res.MeituanOrderVO;
import com.jiuji.oa.meituan.vo.res.PeisongOrderResVO;
import com.jiuji.oa.meituan.vo.res.ThirdDeliveryMeituanOrderVO;
import com.jiuji.oa.nc.common.bo.OaUserBO;
import com.jiuji.oa.nc.common.constant.DataSourceConstants;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 *美团闪购配送信息服务
 */
@DS("oa_log")
@Service
public class MeituanShangouPeisongServiceImpl extends ServiceImpl<MeituanShangouPeisongMapper, MeituanShangouPeisong> implements IMeituanShangouPeisongService {

    @Override
    @DS("oa_log")
    public boolean saveMeituanShangouPeisong (MeituanShangouPeisong meituanShangouPeisong) {
        return this.save(meituanShangouPeisong);
    }

    /**
     * 查询当前需要派送美团闪购订单
     *
     * @return
     */
    @Override
    @DS("ch999oanew")
    public List<MeituanOrderVO> queryMeituanOrder() {
        return this.baseMapper.selectMeituanOrder();
    }

    /**
     * 派送人查询当前需要派送美团闪购订单数量
     *
     * @param paijianren
     * @return
     */
    @Override
    @DS("ch999oanew")
    public List<MeituanOrderVO> queryMeituanOrderByPaijianren(String paijianren) {
        return this.baseMapper.selectMeituanOrderByPaijianren(paijianren);
    }

    /**
     * 派送人查询当前需要派送美团闪购订单数量
     *
     * @param user
     * @return
     */
    @Override
    @DS("ch999oanew")
    public long queryMeituanOrderCountByPaijianren(OaUserBO user) {
        return this.baseMapper.selectMeituanOrderCountByPaijianren(user);
    }

    /**
     * 查询当天已出库美团闪购订单id
     *
     * @return
     */
    @Override
    @DS("ch999oanew")
    public List<Long> queryMeituanOrderSubId() {
        return this.baseMapper.selectMeituanOrderSubId();
    }

    /**
     * 订单id查询最新一条物流信息
     *
     * @param subId
     * @return
     */
    @Override
    @DS("oa_log")
    public MeituanShangouPeisong queryMeituanShangouPeisongBySubId(Long subId) {
        return this.lambdaQuery()
                .eq(MeituanShangouPeisong::getSubId, subId)
                .and(v -> v.gt(MeituanShangouPeisong::getLongitude, 0).or().gt(MeituanShangouPeisong::getLatitude, 0))
                .orderByDesc(MeituanShangouPeisong::getCreateTime)
                .last("limit 1")
                .one();
    }

    /**
     * 批量保存美团闪购物流信息
     *
     * @param meituanShangouPeisongList
     */
    @Override
    @DS("oa_log")
    public boolean saveBatchMeituanShangouPeisong(List<MeituanShangouPeisong> meituanShangouPeisongList) {
        return this.saveBatch(meituanShangouPeisongList);
    }

    /**
     * 查询48小时美团闪购订单
     *
     * @return
     */
    @Override
    @DS("ch999oanew")
    public List<MeituanOrderVO> queryAllMeituanOrder() {
        return this.baseMapper.selectAllMeituanOrder();
    }

    /**
     * 第三方派送美团订单
     *
     * @param subId
     * @return
     */
    @Override
    @DS("ch999oanew")
    public ThirdDeliveryMeituanOrderVO handleThirdCompleteOrder(Long subId) {
        return this.baseMapper.selectThirdDeliveryOrderBySub(subId);
    }

    /**
     * 查询美团闪购订单客户的电话号码
     *
     * @param nu
     * @return
     */
    @Override
    @DS("ch999oanew")
    public PeisongOrderResVO queryPeisongOrder(String nu) {
        return this.baseMapper.selectPeisongOrder(nu);
    }

    /**
     * 查询3分钟内已完成三方订单
     *
     * @return
     */
    @Override
    @DS(DataSourceConstants.OANEW_WRITE)
    public List<MeituanOrderVO> queryCompleteThirdOrderId() {
        return this.baseMapper.queryCompleteThirdOrderId();
    }
}




