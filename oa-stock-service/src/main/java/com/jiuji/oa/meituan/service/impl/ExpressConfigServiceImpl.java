package com.jiuji.oa.meituan.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.cloud.logistics.enums.LogisticsTypeEnum;
import com.jiuji.oa.meituan.entity.ExpressConfig;
import com.jiuji.oa.meituan.mapper.ExpressConfigMapper;
import com.jiuji.oa.meituan.service.IExpressConfigService;
import com.jiuji.oa.nc.common.exception.CustomizeException;
import com.jiuji.oa.nc.common.util.NumUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 快递配置
 */
@Slf4j
@Service
@DS("oa_nc")
@RequiredArgsConstructor
public class ExpressConfigServiceImpl extends ServiceImpl<ExpressConfigMapper, ExpressConfig> implements IExpressConfigService {
    private static final String GET_EXPRESS_CONFIG_REDIS_KEY = "logistics:express_config:";
    private static final Long DEFAULT_XTENANT_ID = -1L;
    private final StringRedisTemplate redisTemplate;

    /**
     * 查询快递配置信息
     *
     * @param xtenantId   租户id
     * @param expressType 快递类型
     * @return
     */
    @Override
    public ExpressConfig queryExpressConfig(Long xtenantId, Integer expressType) {
        String name = "";
        for (LogisticsTypeEnum item : LogisticsTypeEnum.values()) {
            if (Convert.toStr(item.getCode()).equals(Convert.toStr(expressType))) {
                name = item.name();
            }
        }
        String key = GET_EXPRESS_CONFIG_REDIS_KEY + xtenantId + name;
        String value = redisTemplate.opsForValue().get(key);
        if (ObjectUtil.isNotEmpty(value)) {
            return JSONUtil.toBean(value, ExpressConfig.class);
        }
        ExpressConfig expressConfig = this.lambdaQuery()
                .eq(ExpressConfig::getXtenantId, xtenantId)
                .eq(ExpressConfig::getExpressType, expressType).last("limit 1")
                .one();
        if (Objects.isNull(expressConfig)) {
            String error = String.format("租户:%s,快递:%s,未查询到快递配置!", xtenantId, LogisticsTypeEnum.getMessage(expressType));
            throw new CustomizeException(error);
        }
        redisTemplate.opsForValue().set(key, JSONUtil.toJsonStr(expressConfig), NumUtil.ONE, TimeUnit.DAYS);
        return expressConfig;
    }

}




