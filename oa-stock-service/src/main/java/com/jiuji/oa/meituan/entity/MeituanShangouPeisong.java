package com.jiuji.oa.meituan.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 美团闪购配送信息
 * @TableName t_meituan_shangou_peisong
 */
@TableName(value ="t_meituan_shangou_peisong")
@Data
public class MeituanShangouPeisong implements Serializable {
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * subId
     */
    private Long subId;

    /**
     * 第三方配送商物流单号
     */
    private String thirdCarrierOrderId;

    /**
     * 物流ID
     */
    private String wuliuId;

    /**
     * 配送员姓名
     */
    private String courierName;

    /**
     * 配送员联系方式
     */
    private String courierPhone;

    /**
     * 配送此订单商品的物流平台
     */
    private String logisticsProviderCode;

    /**
     * 配送状态code
     */
    private Integer logisticsStatus;

    /**
     * 骑手当前的纬度
     */
    private String latitude;

    /**
     * 骑手当前的经度
     */
    private String longitude;

    /**
     * 坐标系
     */
    private String coordsys;

    /**
     * 平台
     */
    private String platform;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除标识
     */
    @TableField("is_delete")
    private Boolean deleteFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}