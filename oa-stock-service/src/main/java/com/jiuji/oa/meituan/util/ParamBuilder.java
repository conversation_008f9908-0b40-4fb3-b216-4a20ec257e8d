package com.jiuji.oa.meituan.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.jiuji.oa.meituan.vo.req.MeiTuanOrderCreateDeliveryCodeReq;
import com.jiuji.oa.meituan.vo.req.MeiTuanOrderRiderLocationReq;
import com.jiuji.oa.meituan.vo.req.MeiTuanQueryOrderReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 请求body构造器
 */
@Slf4j
public final class ParamBuilder {
    private static final String APPKEY = "appkey";
    private static final String TIMESTAMP = "timestamp";
    private static final String VERSION = "version";
    private static final String SIGN = "sign";
    private static final String DELIVERY_ID = "delivery_id";
    private static final String ORDER_ID = "order_id";


    public static Map<String, String> convertToMapByObject(Object object, String secret) {
        String className = object.getClass().getSimpleName();
        Map<String, String> map;
        switch (className) {
            case "MeiTuanQueryOrderReq":
                MeiTuanQueryOrderReq orderReq = BeanUtil.copyProperties(object, MeiTuanQueryOrderReq.class);
                map = convertToMap(orderReq);
                break;
            case "MeiTuanOrderRiderLocationReq":
                MeiTuanOrderRiderLocationReq riderLocationReq = BeanUtil.copyProperties(object, MeiTuanOrderRiderLocationReq.class);
                map = convertToMap(riderLocationReq);
                break;
            case "MeiTuanOrderCreateDeliveryCodeReq":
                MeiTuanOrderCreateDeliveryCodeReq orderCreateDeliveryCodeReq = BeanUtil.copyProperties(object, MeiTuanOrderCreateDeliveryCodeReq.class);
                map = convertToMap(orderCreateDeliveryCodeReq);
                break;
            default:
                return null;
        }
        map.put(SIGN, SignHelper.generateSign(map, secret));
        return map;
    }

    public static Map<String, String> convertToMap(MeiTuanOrderCreateDeliveryCodeReq request) {
        Map<String, String> map = new HashMap<>(MapUtil.DEFAULT_INITIAL_CAPACITY);
        if (ObjectUtil.isEmpty(request)) {
            return map;
        }
        putIfNotEmpty(map, APPKEY, request.getAppkey());
        putIfNotEmpty(map, TIMESTAMP, String.valueOf(request.getTimestamp()));
        putIfNotEmpty(map, VERSION, request.getVersion());
        putIfNotEmpty(map, SIGN, request.getSign());
        putIfNotEmpty(map, ORDER_ID, String.valueOf(request.getOrderId()));
        putIfNotEmpty(map, "shop_id", request.getShopId());
        return map;
    }

    public static Map<String, String> convertToMap(MeiTuanQueryOrderReq request) {
        Map<String, String> map = new HashMap<>(MapUtil.DEFAULT_INITIAL_CAPACITY);
        if (ObjectUtil.isEmpty(request)) {
            return map;
        }

        putIfNotEmpty(map, APPKEY, request.getAppkey());
        putIfNotEmpty(map, TIMESTAMP, String.valueOf(request.getTimestamp()));
        putIfNotEmpty(map, VERSION, request.getVersion());
        putIfNotEmpty(map, SIGN, request.getSign());
        putIfNotEmpty(map, DELIVERY_ID, String.valueOf(request.getDeliveryId()));
        putIfNotEmpty(map, "mt_peisong_id", request.getMtPeisongId());

        return map;
    }


    public static Map<String, String> convertToMap(MeiTuanOrderRiderLocationReq request) {
        Map<String, String> map = new HashMap<>(MapUtil.DEFAULT_INITIAL_CAPACITY);
        if (ObjectUtil.isEmpty(request)) {
            return map;
        }
        putIfNotEmpty(map, APPKEY, request.getAppkey());
        putIfNotEmpty(map, TIMESTAMP, String.valueOf(request.getTimestamp()));
        putIfNotEmpty(map, VERSION, request.getVersion());
        putIfNotEmpty(map, SIGN, request.getSign());
        putIfNotEmpty(map, DELIVERY_ID, String.valueOf(request.getDeliveryId()));
        putIfNotEmpty(map, "mt_peisong_id", request.getMtPeisongId());

        return map;
    }

    private static void putIfNotEmpty(Map<String, String> map, String key, String value) {
        if (StringUtils.isNotEmpty(value) && !"null".equals(value)) {
            map.put(key, value);
        }
    }

    private ParamBuilder() {
    }

}
