package com.jiuji.oa.meituan.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**配送此订单商品的物流平台
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum LogisticsProviderCodeEnum {
    /**
     * 取值： 10001-顺丰, 10002-达达, 10003-闪送,
     * 10004-蜂鸟, 10005 UU跑腿,10006 快跑者,
     * 10007 极客快送,10008-点我达,10009 同达,
     * 10010-生活半径,10011 邻趣,10012 趣送,
     * 10013 快服务 10014 菜鸟新配盟
     * 10015 商家自建配送 10016 风先生,
     * 10017-其他,10032-美团跑腿。
     */
    SHUNFENG("10001","顺丰"),
    DADA("10002","达达"),
    SHANSONG("10003","闪送"),
    FENGNIAO("10004","蜂鸟"),
    UU("10005","UU"),
    KUAIPAOZHE("10006","快跑者"),
    JIKE("10007","极客快送"),
    DIANWODA("10008","点我达"),
    TONGDA("10009","同达"),
    SHENGHUO("10010","生活半径"),
    LINQU("10011","邻趣"),
    QUSONG("10012","趣送"),
    KUAIFUWU("10013","快服务"),
    CAINIAO("10014","菜鸟新配盟"),
    MERCHANT_SELF("10015","商家自建配送"),
    FENGXIANSHENG("10016","风先生"),
    OTHER("10017","其他"),
    MEITUAN("10032","美团跑腿")
    ;

    private String code;

    private String message;

    public static LogisticsProviderCodeEnum getLogisticsProviderCodeEnum(String com) {
        if (StringUtils.isBlank(com)) {
            return OTHER;
        }
        switch(com) {
            case "meituan":
            case "meituan_jiuji":
                return MEITUAN;
            case "dada":
            case "dada-jiuji":
                return DADA;
            case "uupt":
                return UU;
            case "shansong":
                return SHANSONG;
            case "sftc":
                return SHUNFENG;
            default:
                return OTHER;
        }
    }
}
