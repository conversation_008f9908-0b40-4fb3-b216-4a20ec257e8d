package com.jiuji.oa.meituan.vo.req;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

/**
 * 取消订单参数
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class MeiTuanOrderRiderLocationReq extends MeiTuanAbstractRequest {
    /**
     * 配送活动标识
     */
    @NotNull(message = "配送活动标识不能为空！")
    private Long deliveryId;

    /**
     * 美团配送内部订单id，最长不超过32个字符
     */
    @NotNull(message = "美团配送内部订单id！")
    @Length(max = 32, message = "美团配送内部订单id，最长不超过32个字符")
    private String mtPeisongId;

}
