package com.jiuji.oa.meituan.service;

import com.jiuji.oa.meituan.entity.MeituanShangouPeisong;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.meituan.vo.res.MeituanOrderVO;
import com.jiuji.oa.meituan.vo.res.PeisongOrderResVO;
import com.jiuji.oa.meituan.vo.res.ThirdDeliveryMeituanOrderVO;
import com.jiuji.oa.nc.common.bo.OaUserBO;

import java.util.List;

/**
 * 美团闪购配送信息服务
 */
public interface IMeituanShangouPeisongService extends IService<MeituanShangouPeisong> {
    /**
     * 保存美团闪购配送信息服务
     * @param meituanShangouPeisong
     */
    boolean saveMeituanShangouPeisong (MeituanShangouPeisong meituanShangouPeisong);

    /**
     * 查询派送的订单
     * @return
     */
    List<MeituanOrderVO> queryMeituanOrder();

    /**
     * 派送人查询派送的订单
     * @param paijianren
     * @return
     */
    List<MeituanOrderVO> queryMeituanOrderByPaijianren(String paijianren);

    /**
     * 派送人查询当前需要派送美团闪购订单数量
     * @param user
     * @return
     */
    long queryMeituanOrderCountByPaijianren(OaUserBO user);

    /**
     * 查询当天已出库美团闪购订单id
     *
     * @return
     */
    List<Long> queryMeituanOrderSubId();

    /**
     * 订单id查询最新一条物流信息
     * @param subId
     * @return
     */
    MeituanShangouPeisong queryMeituanShangouPeisongBySubId(Long subId);

    /**
     * 批量保存美团闪购物流信息
     * @param meituanShangouPeisongList
     */
    boolean saveBatchMeituanShangouPeisong(List<MeituanShangouPeisong> meituanShangouPeisongList);

    /**
     * 查询当天美团闪购订单
     *
     * @return
     */
    List<MeituanOrderVO> queryAllMeituanOrder();

    /**
     * 第三方派送美团订单
     * @param subId
     * @return
     */
    ThirdDeliveryMeituanOrderVO handleThirdCompleteOrder(Long subId);

    /**
     * 查询美团订单
     * @param nu
     * @return
     */
    PeisongOrderResVO queryPeisongOrder(String nu);

    /**
     * 查询3分钟内已完成三方订单
     * @return
     */
    List<MeituanOrderVO> queryCompleteThirdOrderId();
}
