package com.jiuji.oa.meituan.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 
 * @TableName lc_express_config
 */
@TableName(value ="lc_express_config")
@Data
public class ExpressConfig implements Serializable {
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 租户id
     */
    private Long xtenantId;

    /**
     * 快递类型
     */
    private Integer expressType;

    /**
     * 快递名称
     */
    private String expressName;

    /**
     * 快递商户id
     */
    private String expressMerchantId;

    /**
     * 回调地址
     */
    private String callbackUrl;

    /**
     * 应用key
     */
    private String appKey;

    /**
     * 应用密钥
     */
    private String appSecret;

    /**
     * 应用版本
     */
    private String appVersion;

    /**
     * 调用接口地址
     */
    private String appUrl;

    /**
     * 第三方快递账号
     */
    private String appUsername;

    /**
     * 第三方快递密码
     */
    private String appPassword;

    /**
     * 月结卡号
     */
    private String monthlyCost;

    /**
     * 租户规模
     */
    private Integer tenantScale;

    /**
     * 是否扣费
     */
    @TableField(value = "is_deduction")
    private Boolean deductionFlag;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除标识
     */
    @TableLogic
    @TableField(value = "is_delete")
    private Boolean deleteFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}