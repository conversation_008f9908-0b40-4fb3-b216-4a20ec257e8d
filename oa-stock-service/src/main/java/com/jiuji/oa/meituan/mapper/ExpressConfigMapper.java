package com.jiuji.oa.meituan.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.meituan.entity.ExpressConfig;
import com.jiuji.oa.meituan.vo.res.ExpressCompanyListVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Entity com.jiuji.oa.stock.logisticscenter.entity.ExpressConfig
 */
@Mapper
public interface ExpressConfigMapper extends BaseMapper<ExpressConfig> {


    List<ExpressCompanyListVO> getExpressCompanyList(@Param("xTenantId") Long xTenantId);

}




