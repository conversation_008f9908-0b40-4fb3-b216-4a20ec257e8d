package com.jiuji.oa.meituan.vo.req;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 查询订单参数
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class MeiTuanQueryOrderReq extends MeiTuanAbstractRequest {

    /**
     * 配送活动标识
     */
    @NotNull(message = "deliveryId字段不能为空")
    private Long deliveryId;

    /**
     * 美团配送内部订单id，最长不超过32个字符
     */
    @NotNull(message = "platformInsideId字段不能为空")
    private String mtPeisongId;

}
