package com.jiuji.oa.meituan.mapper;

import com.jiuji.oa.meituan.entity.MeituanShangouPeisong;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.meituan.vo.res.MeituanOrderVO;
import com.jiuji.oa.meituan.vo.res.PeisongOrderResVO;
import com.jiuji.oa.meituan.vo.res.ThirdDeliveryMeituanOrderVO;
import com.jiuji.oa.nc.common.bo.OaUserBO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Entity com.jiuji.oa.meituan.entity.MeituanShangouPeisong
 */
@Mapper
public interface MeituanShangouPeisongMapper extends BaseMapper<MeituanShangouPeisong> {

    /**
     * 查询当天已出库美团闪购订单
     * @return
     */
    List<Long> selectMeituanOrderSubId();

    /**
     * 查询正在派送的美团闪购订单
     * @return
     */
    List<MeituanOrderVO> selectMeituanOrder();

    /**
     * 派送人查询正在派送的美团闪购订单
     * @param paijianren
     * @return
     */
    List<MeituanOrderVO> selectMeituanOrderByPaijianren(@Param("paijianren") String paijianren);

    /**
     * 派送人查询正在派送的美团闪购订单数量
     * @param user
     * @return
     */
    long selectMeituanOrderCountByPaijianren(@Param("user") OaUserBO user);

    /**
     * 查询当天美团闪购订单
     * @return
     */
    List<MeituanOrderVO> selectAllMeituanOrder();

    /**
     * 第三方派送美团订单
     * @param subId
     * @return
     */
    ThirdDeliveryMeituanOrderVO selectThirdDeliveryOrderBySub(@Param("subId") Long subId);

    /**
     * 查询美团订单客户电话号码
     * @param nu
     * @return
     */
    PeisongOrderResVO selectPeisongOrder(@Param("nu") String nu);

    List<MeituanOrderVO> queryCompleteThirdOrderId();
}




