package com.jiuji.oa.meituan.vo.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 取消订单统一请求类
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class OrderRiderLocationReq implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("配送活动标识(美团)")
    private Long deliveryId;

    /**
     * 平台方生成的唯一单号
     */
    @ApiModelProperty("平台物流单号(美团，闪送)")
    private String platformInsideId;

}
