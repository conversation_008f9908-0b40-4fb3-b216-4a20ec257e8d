package com.jiuji.oa.meituan.util;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.security.NoSuchAlgorithmException;
import java.util.*;

/**
 * 签名计算工具类
 */
public final class SignHelper {
    private static final Log LOGGER = LogFactory.getLog(SignHelper.class);

    private SignHelper() {
    }

    public static String generateSign(Map<String, String> params, String secret) {
        try {
            String encodeString = getEncodeString(params, secret);
            LOGGER.info(String.format("encodeString: %s", encodeString));
            String sign = generateSign(encodeString);
            LOGGER.info(String.format("generateSign: %s", sign));
            return sign;
        } catch (NoSuchAlgorithmException e) {
            LOGGER.error("生成签名异常!", e);
            return null;
        }

    }

    private static String getEncodeString(Map<String, String> params, String secret) {
        Iterator<String> keyIter = params.keySet().iterator();
        Set<String> sortedParams = new TreeSet<>();
        while (keyIter.hasNext()) {
            sortedParams.add(keyIter.next());
        }

        StringBuilder strB = new StringBuilder(secret);

        // 排除sign和空值参数
        for (String key : sortedParams) {
            if ("sign".equals(key)) {
                continue;
            }

            String value = params.get(key);

            if (StringUtils.isNotEmpty(value)) {
                strB.append(key).append(value);
            }
        }
        return strB.toString();
    }

    private static String generateSign(String content) throws NoSuchAlgorithmException {
        return SHA1Util.sha1(content).toLowerCase(Locale.ENGLISH);
    }


}
