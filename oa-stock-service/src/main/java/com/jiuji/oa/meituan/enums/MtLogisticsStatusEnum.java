package com.jiuji.oa.meituan.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 坐标系
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum MtLogisticsStatusEnum {
    /**
     * gps
     * amap 高德
     * baidu 百度
     */
    已调度(10,5),
    已接单(20,10),
    已取货(30,20),
    已送达(50,40);



    private Integer code;

    private Integer message;


    public static Integer getMessage(Integer code) {
        for (MtLogisticsStatusEnum enums : MtLogisticsStatusEnum.values()) {
            if (enums.code.equals(code)) {
                return enums.getMessage();
            }
        }
        return null;
    }


}
