package com.jiuji.oa.meituan.vo.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 取消订单统一请求类
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class OrderCreateDeliveryCodeReq implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("取货门店id，即合作方向美团提供的门店id")
    private String shopId;

    /**
     * 平台方生成的唯一单号
     */
    @ApiModelProperty("订单id，即该订单在合作方系统中的id")
    private String orderId;

}
