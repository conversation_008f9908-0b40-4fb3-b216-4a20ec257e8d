package com.jiuji.oa.meituan.vo.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 美团闪购九机配送经纬度上报
 * <AUTHOR>
 * @date 2022/4/29 13:37
 */
@Data
public class LngLatReportReqVO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 经度
     */
    private String longitude;
    /**
     * 纬度
     */
    private String latitude;

    /**
     * 派送人
     */
    private String userName;

    /**
     * 派送人Id
     */
    private Integer userId;

    /**
     * 平台以及版本号
     */
    private String platform;

    /**
     * 订单号
     */
    private Long subId;

    /**
     * 上报时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime reportTime;
}
