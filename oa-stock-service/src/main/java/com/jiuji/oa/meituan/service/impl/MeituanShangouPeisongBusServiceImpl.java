package com.jiuji.oa.meituan.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.ch999.common.util.atlas.AtlasUtil;
import com.ch999.common.util.atlas.CoordinateUtil;
import com.ch999.common.util.vo.atlas.Coordinate;
import com.ch999.common.util.vo.atlas.TencentMapResultVO;
import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;
import com.jiuji.cloud.logistics.enums.LogisticsTypeEnum;
import com.jiuji.cloud.logistics.vo.response.CreateShopRes;
import com.jiuji.oa.apollo.WuliuApolloConfig;
import com.jiuji.oa.logapi.service.ISubLogService;
import com.jiuji.oa.loginfo.order.vo.req.SubLogsNewReq;
import com.jiuji.oa.meituan.entity.ExpressConfig;
import com.jiuji.oa.meituan.entity.MeituanShangouPeisong;
import com.jiuji.oa.meituan.enums.CoordsysEnum;
import com.jiuji.oa.meituan.enums.LogisticsProviderCodeEnum;
import com.jiuji.oa.meituan.enums.MtLogisticsStatusEnum;
import com.jiuji.oa.meituan.mapstruct.MeituanShangouPeisongMapStruct;
import com.jiuji.oa.meituan.service.IExpressConfigService;
import com.jiuji.oa.meituan.service.IMeituanShangouPeisongBusService;
import com.jiuji.oa.meituan.service.IMeituanShangouPeisongService;
import com.jiuji.oa.meituan.util.DateUtil;
import com.jiuji.oa.meituan.util.MeiTuanApiHttpClient;
import com.jiuji.oa.meituan.util.ParamBuilder;
import com.jiuji.oa.meituan.vo.req.*;
import com.jiuji.oa.meituan.vo.res.*;
import com.jiuji.oa.nc.common.bo.OaUserBO;
import com.jiuji.oa.nc.common.config.rabbitmq.RabbitMqConfig;
import com.jiuji.oa.nc.common.exception.CustomizeException;
import com.jiuji.oa.nc.common.exception.RRExceptionHandler;
import com.jiuji.oa.nc.common.util.NumUtil;
import com.jiuji.oa.nc.common.util.ValidatorUtil;
import com.jiuji.oa.nc.stock.service.ISmsService;
import com.jiuji.oa.nc.user.po.Areainfo;
import com.jiuji.oa.nc.user.po.Ch999User;
import com.jiuji.oa.nc.user.service.Ch999UserService;
import com.jiuji.oa.nc.user.service.IAreaInfoService;
import com.jiuji.oa.oacore.oaorder.OrderDetailCloud;
import com.jiuji.oa.oacore.oaorder.req.LogisticsSync;
import com.jiuji.oa.stock.common.util.Builder;
import com.jiuji.oa.stock.common.util.JacksonJsonUtils;
import com.jiuji.oa.stock.common.util.SysUtils;
import com.jiuji.oa.stock.logistics.dada.service.IDadaService;
import com.jiuji.oa.stock.logisticscenter.enums.DaDaOrderStatusEnum;
import com.jiuji.oa.stock.logisticscenter.serive.ILogisticsExpressService;
import com.jiuji.oa.stock.logisticscenter.utils.JsonParseUtil;
import com.jiuji.oa.stock.logisticscenter.vo.req.DaDaOrderCallBackReq;
import com.jiuji.oa.stock.logisticscenter.vo.req.QueryOrderInfoReqVO;
import com.jiuji.oa.stock.logisticscenter.vo.req.QueryShopReq;
import com.jiuji.oa.stock.logisticscenter.vo.res.QueryOrderInfoResVO;
import com.jiuji.oa.wuliu.constant.WuLiuConstant;
import com.jiuji.oa.wuliu.entity.WuLiuEntity;
import com.jiuji.oa.wuliu.entity.WuLiuSubEntity;
import com.jiuji.oa.wuliu.enums.UuOrderStatusEnum;
import com.jiuji.oa.wuliu.service.IWuLiuService;
import com.jiuji.oa.wuliu.service.IWuLiuSubService;
import com.jiuji.oa.wuliu.utils.WuliuAddressUtil;
import com.jiuji.oa.wuliu.vo.CityIdListDTO;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.common.TraceIdUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 美团闪购配送信息服务
 *
 * <AUTHOR>
 * @date 2022/4/29 14:02
 */
@Service
@Slf4j
public class MeituanShangouPeisongBusServiceImpl implements IMeituanShangouPeisongBusService {
    private static final String VIRTUAL_PEISONG_MSG = "#%s#您的%s订单已在配送中；配送员：%s，收货码：%s";
    private static final String PEISONG_MSG = "您的%s订单已在配送中；配送员：%s，收货码：%s";
    private static final String PEISONG_FINSHCODE_MSG = "收货码：%s";
    private static final String PEISONG_REN_KEY = "stock:meitaunshangou:peisong:";
    private static final Long PEISONG_TIMEOUT = 5L;
    private static final Integer PEISONG_STATUS = 20;
    private static final Integer COMPLETE_PEISONG_STATUS = 40;
    private static final Integer COMPLETE_SUB_CHECK = 3;
    private static final String COMPLETE_SYNC_KEY = "stock:meitaunshangou:peisong:sync";
    private static final String PEISONG_REN_ALL_KEY = "stock:meitaunshangou:peisong:all";
    public static final String API_URL = "https://peisongopen.meituan.com/api";
    private static final String ORDER_QUERY = API_URL + "/order/status/query";
    public static final String ORDER_RIDER_LOCATION = API_URL + "/order/rider/location";
    public static final String ORDER_CREATE_DELIVERY_CODE = API_URL + "/order/createDeliveryCode";

    private static final Retryer<Boolean> retry = RetryerBuilder.<Boolean>newBuilder()
            .retryIfExceptionOfType(Exception.class)
            .retryIfResult(or -> or == null || Boolean.FALSE.equals(or))
            .withWaitStrategy(WaitStrategies.fixedWait(5, TimeUnit.SECONDS))
            .withStopStrategy(StopStrategies.stopAfterAttempt(3))
            .build();
    private static final String VIRTUAL_PHONE_REGEX = "_|,|，|-";
    @Resource
    private IMeituanShangouPeisongService meituanShangouPeisongService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private RabbitTemplate rabbitTemplate;
    @Resource
    private IDadaService dadaService;
    @Resource
    private IExpressConfigService expressConfigService;
    @Resource
    private ILogisticsExpressService logisticsExpressService;
    @Resource
    private MeituanShangouPeisongMapStruct meituanShangouPeisongMapStruct;
    @Resource
    private Ch999UserService ch999UserService;
    @Resource
    private OrderDetailCloud orderDetailCloud;
    @Resource
    private IWuLiuService wuLiuService;
    @Resource
    private IAreaInfoService areaInfoService;
    @Resource
    private ISmsService smsService;
    @Resource
    private ISubLogService subLogService;

    /**
     * 美团闪购订单九机配送经纬度上报
     *
     * @param reqVo
     * @return
     */
    @Override
    public LngLatReportResVO lngLatReport(LngLatReportReqVO reqVo) {
        OaUserBO user = SysUtils.getUser();
        if (Objects.isNull(user)) {
            throw new CustomizeException("当前用户没有登录，请登录后操作");
        }
        reqVo.setUserId(user.getUserId());
        reqVo.setUserName(user.getUserName());
        reqVo.setPlatform(user.getPlatformName());
        String jsonMesg = JacksonJsonUtils.toJson(reqVo);
        log.info("经纬度上报，发送消息jsonMesg={}", jsonMesg);
        rabbitTemplate.convertAndSend(RabbitMqConfig.JIUJI_MEITUAN_LNGLAT_REPORT, jsonMesg);
        LngLatReportResVO res = new LngLatReportResVO();
        boolean hasSending = queryHasSendingCache(user);
        res.setNeedToReport(hasSending);
        return res;
    }

    /**
     * 是否有正在派送的查询接口
     *
     * @return
     */
    @Override
    public HasSendingResVO queryHasSending() {
        OaUserBO user = SysUtils.getUser();
        if (Objects.isNull(user)) {
            throw new CustomizeException("当前用户没有登录，请登录后操作");
        }
        HasSendingResVO res = new HasSendingResVO();
        boolean hasSending = queryHasSendingCache(user);
        res.setNeedToReport(hasSending);
        return res;
    }

    /**
     * 用户正在派送的美团订单
     *
     * @param user
     * @return
     */
    @Override
    public boolean queryHasSendingCache(OaUserBO user) {
        String key = PEISONG_REN_KEY + user.getUserId();
        String hasSending = stringRedisTemplate.opsForValue().get(key);
        if (StringUtils.isNotEmpty(hasSending)) {
            return true;
        }
        String userName = user.getUserName();
        Boolean isMember = stringRedisTemplate.opsForSet().isMember(PEISONG_REN_ALL_KEY, userName);
        return Boolean.TRUE.equals(isMember);
    }

    /**
     * 刷新配送人是否需要配送订单缓存
     *
     * @param userId
     * @return
     */
    @Override
    public void refreshPeisongCache(Integer userId, Integer count) {
        String key = PEISONG_REN_KEY + userId;
        stringRedisTemplate.opsForValue().set(key, count + "", PEISONG_TIMEOUT, TimeUnit.MINUTES);
    }

    /**
     * 清除配送人是否需要配送订单缓存
     *
     * @param userId
     * @return
     */
    @Override
    public void clearPeisongCache(Integer userId) {
        String key = PEISONG_REN_KEY + userId;
        stringRedisTemplate.delete(key);
    }

    /**
     * 定时处理美团闪购订单
     *
     * @return
     */
    @Override
    public boolean handleMeituanOrder() {
        List<MeituanOrderVO> meituanOrderList = meituanShangouPeisongService.queryMeituanOrder();
        if (CollectionUtils.isNotEmpty(meituanOrderList)) {
            Set<String> stringSet = meituanOrderList.stream().filter(v -> Objects.equals(2, v.getDelivery())).map(MeituanOrderVO::getPaijianren).filter(StringUtils::isNotEmpty).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(stringSet)) {
                String[] stringArray = new String[stringSet.size()];
                stringRedisTemplate.delete(PEISONG_REN_ALL_KEY);
                stringRedisTemplate.opsForSet().add(PEISONG_REN_ALL_KEY, stringSet.toArray(stringArray));
            }
            for (MeituanOrderVO meituanOrder : meituanOrderList) {
                rabbitTemplate.convertAndSend(RabbitMqConfig.MEITUAN_ORDER_REPORT, JacksonJsonUtils.toJson(meituanOrder));
            }
        }
        return true;
    }

    /**
     * 自配送商家同步发货状态和配送信息
     */
    private RiderLocationRes getOrderRiderLocation(OrderRiderLocationReq orderRiderLocationReq) {
        RiderLocationRes res = null;
        // 调用美团接口
        MeiTuanOrderRiderLocationReq meiTuanOrderRiderLocationReq = new MeiTuanOrderRiderLocationReq();
        meiTuanOrderRiderLocationReq.setDeliveryId(Convert.toLong(orderRiderLocationReq.getDeliveryId()));
        meiTuanOrderRiderLocationReq.setMtPeisongId(orderRiderLocationReq.getPlatformInsideId());
        String result = MeiTuanApiHttpClient.post(ORDER_RIDER_LOCATION, endorse(meiTuanOrderRiderLocationReq));
        Integer code = JsonParseUtil.getCode(result);
        // 判断美团的返回结果
        if (!code.equals(NumUtil.ZERO)) {
            String message = JsonParseUtil.getMessage(result);
            return res;
        }
        res = JsonParseUtil.getObjectFromData(result, RiderLocationRes.class);
        return res;
    }


    /**
     * 自配送商家同步发货状态和配送信息
     */
    private OrderCreateDeliveryCodeRes getOrderCreateDeliveryCode(OrderCreateDeliveryCodeReq orderCreateDeliveryCodeReq) {
        OrderCreateDeliveryCodeRes res = null;
        // 调用美团接口
        MeiTuanOrderCreateDeliveryCodeReq meiTuanOrderRiderLocationReq = new MeiTuanOrderCreateDeliveryCodeReq();
        meiTuanOrderRiderLocationReq.setOrderId(orderCreateDeliveryCodeReq.getOrderId());
        meiTuanOrderRiderLocationReq.setShopId(orderCreateDeliveryCodeReq.getShopId());
        String result = MeiTuanApiHttpClient.post(ORDER_CREATE_DELIVERY_CODE, endorse(meiTuanOrderRiderLocationReq));
        Integer code = JsonParseUtil.getCode(result);
        // 判断美团的返回结果
        if (!code.equals(NumUtil.ZERO)) {
            String message = JsonParseUtil.getMessage(result);
            return res;
        }
        res = JsonParseUtil.getObjectFromData(result, OrderCreateDeliveryCodeRes.class);
        return res;
    }

    /**
     * 说明: 所有美团请求加上appKey、timestamp、version、sign 属性
     *
     * @param entity 父类
     * @return Object
     * @date 2021/3/26
     **/
    private <T extends MeiTuanAbstractRequest> Map<String, String> endorse(T entity) {

        ExpressConfig expressConfig = expressConfigService.queryExpressConfig(-1L, LogisticsTypeEnum.MEI_TUAN.getCode());
        String appKey = expressConfig.getAppKey();
        String appSecret = expressConfig.getAppSecret();
        String appVersion = expressConfig.getAppVersion();
        int timestamp = DateUtil.unixTime();
        entity.setAppkey(appKey);
        entity.setTimestamp(timestamp);
        entity.setVersion(appVersion);
        return ParamBuilder.convertToMapByObject(entity, appSecret);
    }


    /**
     * 自配送商家同步发货状态和配送信息
     *
     * @param req
     */
    @Override
    public void synMeituanLogistics(MeituanOrderSubVO req) {
        String key = COMPLETE_SYNC_KEY + req.getSubId();
        String isSync = stringRedisTemplate.opsForValue().get(key);
        //已经同步过完成状态
        if (StringUtils.isNotEmpty(isSync)) {
            return;
        }

        MeituanShangouPeisong meituanShangouPeisong = meituanShangouPeisongService.queryMeituanShangouPeisongBySubId(req.getSubId());
        log.info("同步美团物流信息 req={}, meituanShangouPeisong={}", req, meituanShangouPeisong);
        if (Objects.isNull(meituanShangouPeisong)) {
            handleThirdCompleteOrder(req.getSubId());
        } else {
            Integer subCheck = Optional.ofNullable(SpringUtil.getBean(IWuLiuSubService.class).getBySubId(req.getSubId())).map(WuLiuSubEntity::getSubCheck).orElse(0);
            if (Objects.equals(COMPLETE_SUB_CHECK, subCheck) && !Objects.equals(COMPLETE_PEISONG_STATUS, meituanShangouPeisong.getLogisticsStatus())) {
                //补偿同步完成订单
                handleThirdCompleteOrder(req.getSubId());
            } else {
                synMeituanLogistics(meituanShangouPeisong);
            }
        }
    }

    /**
     * 自配送商家同步发货状态和配送信息
     *
     * @param meituanShangouPeisong
     */
    @Override
    public void synMeituanLogistics(MeituanShangouPeisong meituanShangouPeisong) {
        LogisticsSync logisticsSync = null;
        try {
            if (Objects.isNull(meituanShangouPeisong.getId())) {
                meituanShangouPeisongService.saveMeituanShangouPeisong(meituanShangouPeisong);
            }
            if (CoordsysEnum.GPS.getCode().equals(meituanShangouPeisong.getCoordsys())) {
                Coordinate coordinate = CoordinateUtil.wgs2gcj(new Coordinate(meituanShangouPeisong.getLongitude() + "," + meituanShangouPeisong.getLatitude()));
                meituanShangouPeisong.setLongitude(coordinate.getLongitude() + "");
                meituanShangouPeisong.setLatitude(coordinate.getLatitude() + "");
            } else if (CoordsysEnum.BAIDU.getCode().equals(meituanShangouPeisong.getCoordsys())) {
                Coordinate coordinate = CoordinateUtil.bd2gcj(new Coordinate(meituanShangouPeisong.getLongitude() + "," + meituanShangouPeisong.getLatitude()));
                meituanShangouPeisong.setLongitude(coordinate.getLongitude() + "");
                meituanShangouPeisong.setLatitude(coordinate.getLatitude() + "");
            }
            logisticsSync = meituanShangouPeisongMapStruct.toLogisticsSync(meituanShangouPeisong);
            Long reportTime = Optional.ofNullable(meituanShangouPeisong.getCreateTime()).orElse(LocalDateTime.now()).atZone(ZoneId.systemDefault()).toInstant().getEpochSecond();
            logisticsSync.setReportTime(reportTime);
            LogisticsSync finalLogisticsSync = logisticsSync;
            String traceId = MDC.get(TraceIdUtil.TRACE_ID_KEY);
            retry.call(() -> {
                MDC.put(TraceIdUtil.TRACE_ID_KEY, traceId);
                try {
                    R<Boolean> result = orderDetailCloud.orderDelivering(finalLogisticsSync);
                    log.info("同步美团物流信息成功,logisticsSync={},result={}", JacksonJsonUtils.toJson(finalLogisticsSync), JacksonJsonUtils.toJson(result));
                    if (Objects.equals(0, result.getCode()) && COMPLETE_PEISONG_STATUS.equals(meituanShangouPeisong.getLogisticsStatus())) {
                        String key = COMPLETE_SYNC_KEY + meituanShangouPeisong.getSubId();
                        stringRedisTemplate.opsForValue().set(key, meituanShangouPeisong.getSubId().toString(), 1, TimeUnit.DAYS);
                    }
                    return !Objects.equals(200, result.getCode());
                } finally {
                    MDC.remove(TraceIdUtil.TRACE_ID_KEY);
                }
            });
        } catch (Exception e) {
            RRExceptionHandler.logError(StrUtil.format("同步美团物流信息,单号: {}", logisticsSync.getSubId()), logisticsSync, e, smsService::sendOaMsgTo9JiMan);
        }
    }

    /**
     * 九机快送
     *
     * @param dto
     */
    @Override
    public void saveMeituanShangouPeisong(LngLatReportReqVO dto) {
        if (Objects.isNull(dto)) {
            return;
        }

        if (Objects.nonNull(dto.getSubId()) && dto.getSubId() > 0) {
            WuLiuEntity wuLiuEntity = wuLiuService.getById(dto.getSubId());
            if (Objects.isNull(wuLiuEntity) || Objects.isNull(wuLiuEntity.getDanHaoBind())) {
                return;
            }

            String key = COMPLETE_SYNC_KEY + wuLiuEntity.getDanHaoBind();
            String isMember = stringRedisTemplate.opsForValue().get(key);
            if (StringUtils.isBlank(isMember)) {
                jiujiPeisongComplete(wuLiuEntity.getDanHaoBind().longValue());
            }
            clearPeisongCache(dto.getUserId());
            return;
        }

        Ch999User user = ch999UserService.getUserByCh999Id(dto.getUserId());
        //查询需要派送的订单
        List<MeituanOrderVO> meituanOrderList = meituanShangouPeisongService.queryMeituanOrderByPaijianren(dto.getUserName());
        if (CollectionUtils.isEmpty(meituanOrderList)) {
            clearPeisongCache(dto.getUserId());
            return;
        }
        refreshPeisongCache(dto.getUserId(), meituanOrderList.size());

        List<MeituanShangouPeisong> meituanShangouPeisongList = meituanOrderList.stream()
                .map(v -> Builder.of(MeituanShangouPeisong::new)
                        .with(MeituanShangouPeisong::setCourierName, user.getCh999Name())
                        .with(MeituanShangouPeisong::setCourierPhone, user.getMobile())
                        .with(MeituanShangouPeisong::setLongitude, dto.getLongitude())
                        .with(MeituanShangouPeisong::setLatitude, dto.getLatitude())
                        .with(MeituanShangouPeisong::setCoordsys, CoordsysEnum.GPS.getCode())
                        .with(MeituanShangouPeisong::setSubId, v.getSubId())
                        .with(MeituanShangouPeisong::setThirdCarrierOrderId, v.getWuliuId())
                        .with(MeituanShangouPeisong::setWuliuId, v.getWuliuId())
                        .with(MeituanShangouPeisong::setLogisticsProviderCode, LogisticsProviderCodeEnum.OTHER.getCode())
                        .with(MeituanShangouPeisong::setLogisticsStatus, PEISONG_STATUS)
                        .with(MeituanShangouPeisong::setPlatform, dto.getPlatform())
                        .build()).collect(Collectors.toList());

        meituanShangouPeisongService.saveBatchMeituanShangouPeisong(meituanShangouPeisongList);
    }

    /**
     * 达达，uu，美团配送信息处理
     *
     * @param dto
     */
    @Override
    public void saveMeituanShangouPeisong(MeituanOrderVO dto) {
        if (Objects.isNull(dto)) {
            return;
        }

        String key = COMPLETE_SYNC_KEY + dto.getSubId();
        String isMember = stringRedisTemplate.opsForValue().get(key);
        if (StringUtils.isNotEmpty(isMember)) {
            return;
        }
        WuLiuSubEntity sub = SpringUtil.getBean(IWuLiuSubService.class).getBySubId(dto.getSubId());
        //美团、抖音、淘宝小时达
        List<Integer> subTypeList = Arrays.asList(19, 32, 38);
        if (Objects.isNull(sub) || !subTypeList.contains(sub.getSubType())) {
            log.info("查询美团闪购订单信息sub={}", JacksonJsonUtils.toJson(sub));
            return;
        }
        //九机快送
        if (StringUtils.isEmpty(dto.getNu()) && COMPLETE_SUB_CHECK.equals(dto.getSubCheck())) {
            jiujiPeisongComplete(dto.getSubId());
            return;
        }

        if (WuLiuConstant.DADA.equals(dto.getCom()) && StringUtils.isNotEmpty(dto.getNu())) {
            //达达
            saveDadaPeisong(dto);
        } else if (WuLiuConstant.UU_PAOTUI.equals(dto.getCom()) && StringUtils.isNotEmpty(dto.getNu())) {
            //uu
            uuPeisong(dto);
        } else if ((WuLiuConstant.MEITUAN.equals(dto.getCom()) ||
                WuLiuConstant.MEITUAN_JIUJI.equals(dto.getCom()))
                && StringUtils.isNotEmpty(dto.getNu())) {
            //uu
            meituanPeisong(dto);
        }
        //订单已经完成
        if (COMPLETE_SUB_CHECK.equals(dto.getSubCheck())) {
            sendMeituanLogistics(dto.getSubId());
        }

    }

    /**
     * 九机配送完成
     *
     * @param subId
     */
    private void jiujiPeisongComplete(Long subId) {
        MeituanShangouPeisong lastMeituanShangou = meituanShangouPeisongService.queryMeituanShangouPeisongBySubId(subId);
        if (Objects.isNull(lastMeituanShangou) || COMPLETE_PEISONG_STATUS.equals(lastMeituanShangou.getLogisticsStatus())) {
            sendMeituanLogistics(subId);
            return;
        }
        lastMeituanShangou.setId(null);
        lastMeituanShangou.setLogisticsStatus(COMPLETE_PEISONG_STATUS);
        lastMeituanShangou.setCreateTime(LocalDateTime.now());
        //查询三方订单收货经纬度
        ThirdDeliveryMeituanOrderVO res = meituanShangouPeisongService.handleThirdCompleteOrder(subId);
        if (Objects.nonNull(res) && StringUtils.isNotEmpty(res.getBuyerPosition())) {
            Coordinate coordinate = CoordinateUtil.wgs2gcj(new Coordinate(res.getBuyerPosition()));
            lastMeituanShangou.setLongitude(coordinate.getLongitude() + "");
            lastMeituanShangou.setLatitude(coordinate.getLatitude() + "");
        }
        boolean saveFlag = meituanShangouPeisongService.saveMeituanShangouPeisong(lastMeituanShangou);
        if (saveFlag) {
            sendMeituanLogistics(subId);
        }
    }

    /**
     * 美团配送
     *
     * @param dto
     */
    @Override
    public void meituanPeisong(MeituanOrderVO dto) {
        MeituanShangouPeisong meituanShangouPeisong;
        MeiTuanQueryOrderReq meiTuanReq = new MeiTuanQueryOrderReq();
        Integer delivery = Convert.toInt(dto.getWuliuId());
        meiTuanReq.setDeliveryId(Convert.toLong(delivery));
        meiTuanReq.setMtPeisongId(Convert.toStr(dto.getNu()));
        ExpressConfig expressConfig = expressConfigService.queryExpressConfig(-1L, LogisticsTypeEnum.MEI_TUAN.getCode());
        meiTuanReq.setAppkey(expressConfig.getAppKey());
        ValidatorUtil.validateEntity(meiTuanReq);
        String result = MeiTuanApiHttpClient.post(ORDER_QUERY, endorse(meiTuanReq));
        Integer code = JsonParseUtil.getCode(result);
        if (code != 0) {
            return;
        }
        String data = JsonParseUtil.getData(result);
        MeiTuanQueryOrderResV2 meiTuanRes = JsonParseUtil.toBean(data, MeiTuanQueryOrderResV2.class);
        if (Objects.isNull(meiTuanRes)) {
            return;
        }

        OrderRiderLocationReq req = new OrderRiderLocationReq();
        req.setPlatformInsideId(meiTuanRes.getMtPeisongId());
        req.setDeliveryId(Convert.toLong(delivery));
        RiderLocationRes orderRiderLocation = this.getOrderRiderLocation(req);
        if (Objects.isNull(orderRiderLocation)) {
            return;
        }

        meituanShangouPeisong = new MeituanShangouPeisong();
        meituanShangouPeisong.setCourierName(meiTuanRes.getCourierName());
        meituanShangouPeisong.setCourierPhone(meiTuanRes.getCourierPhone());
        meituanShangouPeisong.setLongitude(Convert.toStr(Convert.toDouble(orderRiderLocation.getLng())/1000000));
        meituanShangouPeisong.setLatitude(Convert.toStr(Convert.toDouble(orderRiderLocation.getLat())/1000000));
        meituanShangouPeisong.setThirdCarrierOrderId(meiTuanRes.getMtPeisongId());
        meituanShangouPeisong.setLogisticsProviderCode(LogisticsProviderCodeEnum.MEITUAN.getCode());
        meituanShangouPeisong.setCoordsys(CoordsysEnum.AMAP.getCode());
        meituanShangouPeisong.setPlatform(WuLiuConstant.MEITUAN_FASTEST);
        Integer status = meiTuanRes.getStatus();
        Integer logisticsStatus = MtLogisticsStatusEnum.getMessage(status);
        meituanShangouPeisong.setLogisticsStatus(logisticsStatus);
        meituanShangouPeisong.setSubId(dto.getSubId());
        meituanShangouPeisong.setWuliuId(dto.getWuliuId());
        meituanShangouPeisongService.saveMeituanShangouPeisong(meituanShangouPeisong);

        //立即同步完成
        if (MtLogisticsStatusEnum.已送达.getMessage().equals(logisticsStatus)){
            this.synMeituanLogistics(meituanShangouPeisong);
        }
    }

    /**
     * uu配送
     *
     * @param dto
     */
    private void uuPeisong(MeituanOrderVO dto) {
        MeituanShangouPeisong meituanShangouPeisong;
        QueryOrderInfoReqVO reqVO = new QueryOrderInfoReqVO();
        reqVO.setWaybillNo(dto.getNu());
        reqVO.setExpressType(WuLiuConstant.UU_PAOTUI_EXPRESS_TYPE);
        reqVO.setTenantScale(0);
        reqVO.setXTenantId(0L);
        R<QueryOrderInfoResVO> queryOrderInfoResResult = logisticsExpressService.queryOrderInfo(reqVO);
        if (Objects.isNull(queryOrderInfoResResult)) {
            return;
        }
        if (Objects.equals(0, queryOrderInfoResResult.getCode()) && Objects.nonNull(queryOrderInfoResResult.getData())) {
            QueryOrderInfoResVO queryOrderInfoRes = queryOrderInfoResResult.getData();
            //未接单
            if (Objects.isNull(queryOrderInfoRes.getStatusCode()) || UuOrderStatusEnum.CREATED.getCode().equals(queryOrderInfoRes.getStatusCode())) {
                return;
            }
            //已经取消
            if (UuOrderStatusEnum.CANCELED.getCode().equals(queryOrderInfoRes.getStatusCode())) {
                return;
            }
            meituanShangouPeisong = new MeituanShangouPeisong();
            meituanShangouPeisong.setCourierName(queryOrderInfoRes.getCourierName());
            meituanShangouPeisong.setCourierPhone(queryOrderInfoRes.getCourierPhone());
            meituanShangouPeisong.setLongitude(queryOrderInfoRes.getLongitude());
            meituanShangouPeisong.setLatitude(queryOrderInfoRes.getLatitude());
            meituanShangouPeisong.setThirdCarrierOrderId(queryOrderInfoRes.getWaybillNo());
            meituanShangouPeisong.setLogisticsProviderCode(LogisticsProviderCodeEnum.UU.getCode());
            meituanShangouPeisong.setCoordsys(CoordsysEnum.BAIDU.getCode());
            meituanShangouPeisong.setPlatform(WuLiuConstant.UU_PAOTUI);
            meituanShangouPeisong.setLogisticsStatus(PEISONG_STATUS);
            meituanShangouPeisong.setSubId(dto.getSubId());
            meituanShangouPeisong.setWuliuId(dto.getWuliuId());
            if (UuOrderStatusEnum.DELIVERED.getCode().equals(queryOrderInfoRes.getStatusCode())) {
                sendComplete(meituanShangouPeisong, queryOrderInfoRes);
            } else {
                meituanShangouPeisongService.saveMeituanShangouPeisong(meituanShangouPeisong);
            }
        }
    }

    /**
     * dada配送
     *
     * @param dto
     */
    private void saveDadaPeisong(MeituanOrderVO dto) {
        MeituanShangouPeisong meituanShangouPeisong;
        QueryOrderInfoReqVO reqVO = new QueryOrderInfoReqVO();
        reqVO.setWaybillNo(dto.getNu());
        QueryOrderInfoResVO queryOrderInfoRes = dadaService.queryOrder(reqVO);
        //未接单
        if (Objects.isNull(queryOrderInfoRes.getStatusCode()) || DaDaOrderStatusEnum.PENDING_ORDER.getCode().equals(queryOrderInfoRes.getStatusCode())) {
            return;
        }
        //已经取消
        if (DaDaOrderStatusEnum.ASSIGNMENT.getCode().equals(queryOrderInfoRes.getStatusCode())) {
            return;
        }
        meituanShangouPeisong = new MeituanShangouPeisong();
        meituanShangouPeisong.setCourierName(queryOrderInfoRes.getCourierName());
        meituanShangouPeisong.setCourierPhone(queryOrderInfoRes.getCourierPhone());
        meituanShangouPeisong.setLongitude(queryOrderInfoRes.getLongitude());
        meituanShangouPeisong.setLatitude(queryOrderInfoRes.getLatitude());
        meituanShangouPeisong.setThirdCarrierOrderId(queryOrderInfoRes.getWaybillNo());
        meituanShangouPeisong.setLogisticsProviderCode(LogisticsProviderCodeEnum.DADA.getCode());
        meituanShangouPeisong.setCoordsys(CoordsysEnum.AMAP.getCode());
        meituanShangouPeisong.setPlatform(WuLiuConstant.DADA);
        meituanShangouPeisong.setLogisticsStatus(PEISONG_STATUS);
        meituanShangouPeisong.setSubId(dto.getSubId());
        meituanShangouPeisong.setWuliuId(dto.getWuliuId());
        if (DaDaOrderStatusEnum.COMPLETED.getCode().equals(queryOrderInfoRes.getStatusCode())) {
            sendComplete(meituanShangouPeisong, queryOrderInfoRes);
        } else {
            meituanShangouPeisongService.saveMeituanShangouPeisong(meituanShangouPeisong);
        }
    }

    /**
     * 配送完成
     *
     * @param meituanShangouPeisong
     * @param queryOrderInfoRes
     */
    private void sendComplete(MeituanShangouPeisong meituanShangouPeisong, QueryOrderInfoResVO queryOrderInfoRes) {
        Long subId = meituanShangouPeisong.getSubId();
        MeituanShangouPeisong lastMeituanShangou = meituanShangouPeisongService.queryMeituanShangouPeisongBySubId(subId);
        if (Objects.isNull(lastMeituanShangou) || COMPLETE_PEISONG_STATUS.equals(lastMeituanShangou.getLogisticsStatus())) {
            sendMeituanLogistics(subId);
            return;
        }
        meituanShangouPeisong.setLogisticsStatus(COMPLETE_PEISONG_STATUS);
        meituanShangouPeisong.setCreateTime(LocalDateTime.now());
        //配送完成获取经纬度为空问题
        meituanShangouPeisong.setLongitude(StringUtils.isNotEmpty(queryOrderInfoRes.getLongitude()) ? queryOrderInfoRes.getLongitude() : lastMeituanShangou.getLongitude());
        meituanShangouPeisong.setLatitude(StringUtils.isNotEmpty(queryOrderInfoRes.getLatitude()) ? queryOrderInfoRes.getLatitude() : lastMeituanShangou.getLatitude());
        boolean saveFlag = meituanShangouPeisongService.saveMeituanShangouPeisong(meituanShangouPeisong);
        if (saveFlag) {
            sendMeituanLogistics(subId);
        }
    }

    /**
     * 同步已经完成订单
     *
     * @param subId
     */
    private void sendMeituanLogistics(Long subId) {
        MeituanOrderSubVO meituanOrderSub = Builder.of(MeituanOrderSubVO::new).with(MeituanOrderSubVO::setSubId, subId).build();
        rabbitTemplate.convertAndSend(RabbitMqConfig.MEITUAN_LNGLAT_SYNC, JacksonJsonUtils.toJson(meituanOrderSub));
    }

    /**
     * 定时同步美团闪购订单物流信息
     *
     * @return
     */
    @Override
    public boolean syncMeituanLogistics() {
        //查询24小时已出库三方订单
        List<Long> subIdList = meituanShangouPeisongService.queryMeituanOrderSubId();
        if (CollectionUtils.isNotEmpty(subIdList)) {
            for (Long subId : subIdList) {
                sendMeituanLogistics(subId);
            }
        }
        //查询最近3分钟已经完成三方订单
        List<MeituanOrderVO> completeSubList = meituanShangouPeisongService.queryCompleteThirdOrderId();
        if (CollectionUtils.isNotEmpty(completeSubList)) {
            for (MeituanOrderVO meituanOrder : completeSubList) {
                rabbitTemplate.convertAndSend(RabbitMqConfig.MEITUAN_ORDER_REPORT, JacksonJsonUtils.toJson(meituanOrder));
            }
        }
        return true;
    }

    /**
     * 处理当天订单
     *
     * @return
     */
    @Override
    public boolean handleCompleteMeituanOrder() {
        List<MeituanOrderVO> meituanOrderList = meituanShangouPeisongService.queryAllMeituanOrder();
        if (CollectionUtils.isNotEmpty(meituanOrderList)) {
            for (MeituanOrderVO meituanOrder : meituanOrderList) {
                rabbitTemplate.convertAndSend(RabbitMqConfig.MEITUAN_ORDER_REPORT, JacksonJsonUtils.toJson(meituanOrder));
            }
        }
        return true;
    }

    /**
     * 发送取货码
     *
     * @param req
     * @return
     */
    @Override
    public void sendFinishCode(DaDaOrderCallBackReq req) {
        if (Objects.isNull(req) || Objects.isNull(req.getFinishCode())) {
            log.info("订单不需要达达取货码param={}", req);
            return;
        }
        PeisongOrderResVO peisongOrder = meituanShangouPeisongService.queryPeisongOrder(req.getOrderId());
        if (Objects.isNull(peisongOrder) || Objects.isNull(peisongOrder.getSubId())) {
            log.info("发送达达取货码，查询订单信息不存在param={}", req);
            return;
        }
        try {
            subLogService.saveSubLog(peisongOrder.getSubId(), false, String.format(PEISONG_FINSHCODE_MSG, req.getFinishCode()), null);

            String phone = peisongOrder.getBuyerMobile();
            if (StringUtils.isBlank(phone)) {
                throw new CustomizeException("发送达达取货码，客户下单电话号码为空");
            }
            String msgContent = String.format(PEISONG_MSG, peisongOrder.getSubTypeDes(), req.getDmName(),  req.getFinishCode());
            if (phone.split(VIRTUAL_PHONE_REGEX).length == 2) {
                String[] phones = phone.split(VIRTUAL_PHONE_REGEX);
                phone = phones[0];
                String ext = phones[1];
                msgContent = String.format(VIRTUAL_PEISONG_MSG, ext, peisongOrder.getSubTypeDes(), req.getDmName(), req.getFinishCode());
            }
            String finalMsgContent = msgContent;
            String finalPhone = phone;
            retry.call(() -> {
                if (SysUtils.isJiuJiProd() || SysUtils.isDev()) {
                    return smsService.sendSmsNew(finalPhone, finalMsgContent, SpringUtil.getBean(WuliuApolloConfig.class).getSmsMeituanChannelid());
                } else {
                    return smsService.sendSmsNew(finalPhone, finalMsgContent, 0);
                }
            });
        } catch (Exception e) {
            SubLogsNewReq subLogsNewReq = new SubLogsNewReq();
            subLogsNewReq.setSubId(peisongOrder.getSubId());
            subLogsNewReq.setType(1);
            subLogsNewReq.setShowType(false);
            subLogsNewReq.setComment("达达收货码推送失败，请及时联系客户");
            subLogsNewReq.setDTime(LocalDateTime.now());
            subLogsNewReq.setInUser("系统");
            subLogService.saveSubLog(subLogsNewReq);
            log.error("发送达达取货码异常，param={},peisongOrder={}", req, peisongOrder, e);
        }
    }

    /**
     * 发送取货码
     *
     * @param waybillNo
     * @return
     */
    @Override
    public void sendFinishCodeUu(String waybillNo) {
        QueryOrderInfoReqVO reqVO = new QueryOrderInfoReqVO();
        reqVO.setWaybillNo(waybillNo);
        reqVO.setExpressType(WuLiuConstant.UU_PAOTUI_EXPRESS_TYPE);
        reqVO.setTenantScale(0);
        reqVO.setXTenantId(0L);
        R<QueryOrderInfoResVO> queryOrderInfoResResult = logisticsExpressService.queryOrderInfo(reqVO);
        if (Objects.isNull(queryOrderInfoResResult)) {
            return;
        }
        if (Objects.equals(0, queryOrderInfoResResult.getCode()) && Objects.nonNull(queryOrderInfoResResult.getData())) {
            QueryOrderInfoResVO queryOrderInfoRes = queryOrderInfoResResult.getData();
            //未接单
            if (Objects.isNull(queryOrderInfoRes.getStatusCode()) || UuOrderStatusEnum.CREATED.getCode().equals(queryOrderInfoRes.getStatusCode())) {
                return;
            }
            //已经取消
            if (UuOrderStatusEnum.CANCELED.getCode().equals(queryOrderInfoRes.getStatusCode())) {
                return;
            }
            String orderFinishCode = queryOrderInfoRes.getOrderFinishCode();
            String courierName = queryOrderInfoRes.getCourierName();
            String courierPhone = queryOrderInfoRes.getCourierPhone();

            if (StringUtils.isEmpty(orderFinishCode)) {
                log.info("订单不需要UU取货码param={}", waybillNo);
                return;
            }
            PeisongOrderResVO peisongOrder = meituanShangouPeisongService.queryPeisongOrder(waybillNo);
            if (Objects.isNull(peisongOrder) || Objects.isNull(peisongOrder.getSubId())) {
                log.info("发送UU取货码，查询订单信息不存在param={}", waybillNo);
                return;
            }
            try {
                subLogService.saveSubLog(peisongOrder.getSubId(), false, String.format(PEISONG_FINSHCODE_MSG, orderFinishCode), null);
                String phone = peisongOrder.getBuyerMobile();
                if (StringUtils.isBlank(phone)) {
                    throw new CustomizeException("发送UU取货码，客户下单电话号码为空");
                }
                String msgContent = String.format(PEISONG_MSG, peisongOrder.getSubTypeDes(), courierName,  orderFinishCode);
                if (phone.split(VIRTUAL_PHONE_REGEX).length == 2) {
                    String[] phones = phone.split(VIRTUAL_PHONE_REGEX);
                    phone = phones[0];
                    String ext = phones[1];
                    msgContent = String.format(VIRTUAL_PEISONG_MSG, ext, peisongOrder.getSubTypeDes(), courierName,  orderFinishCode);
                }
                String finalMsgContent = msgContent;
                String finalPhone = phone;
                retry.call(() -> {
                    if (SysUtils.isJiuJiProd() || SysUtils.isDev()) {
                        return smsService.sendSmsNew(finalPhone, finalMsgContent, SpringUtil.getBean(WuliuApolloConfig.class).getSmsMeituanChannelid());
                    } else {
                        return smsService.sendSmsNew(finalPhone, finalMsgContent, 0);
                    }
                });
            } catch (Exception e) {
                SubLogsNewReq subLogsNewReq = new SubLogsNewReq();
                subLogsNewReq.setSubId(peisongOrder.getSubId());
                subLogsNewReq.setType(1);
                subLogsNewReq.setShowType(false);
                subLogsNewReq.setComment("UU收货码推送失败，请及时联系客户");
                subLogsNewReq.setDTime(LocalDateTime.now());
                subLogsNewReq.setInUser("系统");
                subLogService.saveSubLog(subLogsNewReq);
                log.error("发送UU取货码异常，param={},peisongOrder={}", waybillNo, peisongOrder, e);
            }
        }
    }

    @Override
    public void sendFinishCodeMt(String waybillNo, String deliveryId) {
        MeiTuanQueryOrderReq meiTuanReq = new MeiTuanQueryOrderReq();
        meiTuanReq.setDeliveryId(Convert.toLong(deliveryId));
        meiTuanReq.setMtPeisongId(Convert.toStr(waybillNo));
        ExpressConfig expressConfig = expressConfigService.queryExpressConfig(-1L, LogisticsTypeEnum.MEI_TUAN.getCode());
        meiTuanReq.setAppkey(expressConfig.getAppKey());
        ValidatorUtil.validateEntity(meiTuanReq);
        String result = MeiTuanApiHttpClient.post(ORDER_QUERY, endorse(meiTuanReq));
        Integer code = JsonParseUtil.getCode(result);
        if (code != 0) {
            return;
        }
        String data = JsonParseUtil.getData(result);
        MeiTuanQueryOrderResV2 meiTuanRes = JsonParseUtil.toBean(data, MeiTuanQueryOrderResV2.class);
        if (Objects.isNull(meiTuanRes)) {
            return;
        }
        WuLiuEntity wuliu = wuLiuService.getWuliuById(Convert.toInt(deliveryId));
        if (Objects.isNull(wuliu)) {
            return;
        }
        QueryShopReq reqVO = new QueryShopReq();
        reqVO.setShopId(Convert.toStr(wuliu.getSAreaId()));
        reqVO.setExpressType(LogisticsTypeEnum.MEI_TUAN.getCode());
        reqVO.setXTenantId(0L);
        R<CreateShopRes> createShopRes = logisticsExpressService.queryShop(reqVO);
        if (Objects.isNull(createShopRes)) {
            return;
        }
        if (Objects.equals(0, createShopRes.getCode()) && Objects.nonNull(createShopRes.getData())) {
            String shopId = createShopRes.getData().getShopId();
            OrderCreateDeliveryCodeReq req = new OrderCreateDeliveryCodeReq();
            req.setShopId(shopId);
            req.setOrderId(meiTuanRes.getOrderId());
            OrderCreateDeliveryCodeRes orderCreateDeliveryCodeRes = this.getOrderCreateDeliveryCode(req);
            if (Objects.isNull(orderCreateDeliveryCodeRes)) {
                return;
            }
            String courierName = meiTuanRes.getCourierName();
            String courierPhone = meiTuanRes.getCourierPhone();
            String orderFinishCode = orderCreateDeliveryCodeRes.getContent();
            if (StringUtils.isEmpty(orderFinishCode)) {
                log.info("订单不需要美团取货码param={}", waybillNo);
                return;
            }
            PeisongOrderResVO peisongOrder = meituanShangouPeisongService.queryPeisongOrder(waybillNo);
            if (Objects.isNull(peisongOrder) || Objects.isNull(peisongOrder.getSubId())) {
                log.info("发送美团取货码，查询订单信息不存在param={}", waybillNo);
                return;
            }
            try {
                subLogService.saveSubLog(peisongOrder.getSubId(), false, String.format(PEISONG_FINSHCODE_MSG, orderFinishCode), null);
                String phone = peisongOrder.getBuyerMobile();
                if (StringUtils.isBlank(phone)) {
                    throw new CustomizeException("发送美团取货码，客户下单电话号码为空");
                }
                String msgContent = String.format(PEISONG_MSG, peisongOrder.getSubTypeDes(), courierName,  orderFinishCode);
                if (phone.split(VIRTUAL_PHONE_REGEX).length == 2) {
                    String[] phones = phone.split(VIRTUAL_PHONE_REGEX);
                    phone = phones[0];
                    String ext = phones[1];
                    msgContent = String.format(VIRTUAL_PEISONG_MSG, ext, peisongOrder.getSubTypeDes(), courierName,  orderFinishCode);
                }
                String finalMsgContent = msgContent;
                String finalPhone = phone;
                retry.call(() -> {
                    if (SysUtils.isJiuJiProd() || SysUtils.isDev()) {
                        return smsService.sendSmsNew(finalPhone, finalMsgContent, SpringUtil.getBean(WuliuApolloConfig.class).getSmsMeituanChannelid());
                    } else {
                        return smsService.sendSmsNew(finalPhone, finalMsgContent, 0);
                    }
                });
            } catch (Exception e) {
                SubLogsNewReq subLogsNewReq = new SubLogsNewReq();
                subLogsNewReq.setSubId(peisongOrder.getSubId());
                subLogsNewReq.setType(1);
                subLogsNewReq.setShowType(false);
                subLogsNewReq.setComment("美团取货码推送失败，请及时联系客户");
                subLogsNewReq.setDTime(LocalDateTime.now());
                subLogsNewReq.setInUser("系统");
                subLogService.saveSubLog(subLogsNewReq);
                log.error("发送美团取货码异常，param={},peisongOrder={}", waybillNo, peisongOrder, e);
            }
        }
    }

    /**
     * 第三方派送
     */
    private void handleThirdCompleteOrder(Long subId) {
        ThirdDeliveryMeituanOrderVO res = meituanShangouPeisongService.handleThirdCompleteOrder(subId);
        if (Objects.isNull(res)) {
            return;
        }
        Areainfo areainfo = areaInfoService.getAreaInfoByAreaId2(res.getSareaid());
        if (Objects.isNull(areainfo)) {
            log.warn("订单发货门店错误，subId={},areainfo={}", subId, JacksonJsonUtils.toJson(areainfo));
            return;
        }
        CityIdListDTO jinfo = wuLiuService.getAreaIdByCityId(areainfo.getCityid(), 1);
        List<Integer> userIdByUserName = ch999UserService.getUserIdByUserName(res.getSeller());
        LogisticsProviderCodeEnum logisticsEnum = LogisticsProviderCodeEnum.OTHER;
        String nu = res.getWuliuId();
        if (StrUtil.isNotBlank(nu) && StrUtil.isNotBlank(res.getCom())) {
            logisticsEnum = LogisticsProviderCodeEnum.getLogisticsProviderCodeEnum(res.getCom());
            if (!LogisticsProviderCodeEnum.OTHER.getCode().equals(logisticsEnum.getCode())) {
                nu = res.getNu();
            }
        }
        MeituanShangouPeisong meituanShangouPeisong = new MeituanShangouPeisong();
        meituanShangouPeisong.setThirdCarrierOrderId(nu);
        meituanShangouPeisong.setCoordsys(CoordsysEnum.AMAP.getCode());
        meituanShangouPeisong.setPlatform(logisticsEnum.getMessage());
        meituanShangouPeisong.setLogisticsStatus(PEISONG_STATUS);
        meituanShangouPeisong.setLogisticsProviderCode(logisticsEnum.getCode());
        meituanShangouPeisong.setSubId(subId);
        meituanShangouPeisong.setWuliuId(res.getWuliuId());
        //获取配送员信息，获取不到使用门店信息
        if (StrUtil.isNotEmpty(res.getPtUserMobile())) {
            meituanShangouPeisong.setCourierName(res.getPtUserName());
            meituanShangouPeisong.setCourierPhone(res.getPtUserMobile());
        } else if (CollectionUtils.isNotEmpty(userIdByUserName)) {
            Ch999User userByCh999Id = ch999UserService.getUserByCh999Id(userIdByUserName.get(0));
            meituanShangouPeisong.setCourierName(userByCh999Id.getCh999Name());
            meituanShangouPeisong.setCourierPhone(userByCh999Id.getMobile());
        } else {
            meituanShangouPeisong.setCourierName(areainfo.getAreaName());
            meituanShangouPeisong.setCourierPhone(areainfo.getCompanyTel1());
        }
        if (StringUtils.isNotEmpty(areainfo.getPosition())) {
            Coordinate coordinate = CoordinateUtil.wgs2gcj(new Coordinate(areainfo.getPosition()));
            meituanShangouPeisong.setLongitude(coordinate.getLongitude() + "");
            meituanShangouPeisong.setLatitude(coordinate.getLatitude() + "");
        } else {
            Coordinate addressLocation = getAddressLocation(jinfo, res.getSaddress());
            if (Objects.nonNull(addressLocation)) {
                meituanShangouPeisong.setLongitude(addressLocation.getLongitude() + "");
                meituanShangouPeisong.setLatitude(addressLocation.getLatitude() + "");
            }
        }
        synMeituanLogistics(meituanShangouPeisong);

        //配送完成
        meituanShangouPeisong.setLogisticsStatus(COMPLETE_PEISONG_STATUS);
        meituanShangouPeisong.setId(null);
        if (StringUtils.isNotEmpty(res.getBuyerPosition())) {
            Coordinate coordinate = CoordinateUtil.wgs2gcj(new Coordinate(res.getBuyerPosition()));
            meituanShangouPeisong.setLongitude(coordinate.getLongitude() + "");
            meituanShangouPeisong.setLatitude(coordinate.getLatitude() + "");
        } else {
            Coordinate addressLocation = getAddressLocation(jinfo, res.getRaddress());
            if (Objects.nonNull(addressLocation)) {
                meituanShangouPeisong.setLongitude(addressLocation.getLongitude() + "");
                meituanShangouPeisong.setLatitude(addressLocation.getLatitude() + "");
            }
        }
        synMeituanLogistics(meituanShangouPeisong);
    }

    /**
     * 获取定位信息
     *
     * @param jinfo
     * @param address
     * @return
     */
    private static Coordinate getAddressLocation(CityIdListDTO jinfo, String address) {
        String resultAddress = WuliuAddressUtil.getAddress(address, jinfo);

        String cityName = Objects.nonNull(jinfo) && StringUtils.isNotEmpty(jinfo.getDname()) ? jinfo.getDname() : "";
        Coordinate coordinate = AtlasUtil.translateAddressByThreeMapBase(resultAddress, cityName);
        if (Objects.isNull(coordinate)) {
            coordinate = AtlasUtil.translateAddress2CoordinateUsingGaode(resultAddress, cityName);
        }
        if (Objects.isNull(coordinate)) {
            TencentMapResultVO mapInfoUsingTencent = AtlasUtil.getMapInfoUsingTencent(resultAddress);
            if (Objects.nonNull(mapInfoUsingTencent)
                    && Objects.nonNull(mapInfoUsingTencent.getResult())
                    && Objects.nonNull(mapInfoUsingTencent.getResult().getLocation())) {
                TencentMapResultVO.Result.Location location = mapInfoUsingTencent.getResult().getLocation();
                coordinate = new Coordinate();
                coordinate.setLongitude(location.getLng());
                coordinate.setLatitude(location.getLat());
            }
        }
        return coordinate;
    }
}
