package com.jiuji.oa.meituan.controller;

import com.jiuji.oa.meituan.service.IMeituanShangouPeisongBusService;
import com.jiuji.oa.meituan.vo.req.LngLatReportReqVO;
import com.jiuji.oa.meituan.vo.req.SendFinishCodeReq;
import com.jiuji.oa.meituan.vo.res.HasSendingResVO;
import com.jiuji.oa.meituan.vo.res.LngLatReportResVO;
import com.jiuji.oa.meituan.vo.res.MeituanOrderVO;
import com.jiuji.tc.common.vo.R;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 美团闪购配送
 *
 * <AUTHOR>
 * @date 2022/4/28 16:01
 */
@RestController
@RequestMapping("/api/meituan-shangou-peisong")
public class MeituanShangouPeisongController {
    @Resource
    private IMeituanShangouPeisongBusService meituanShangouPeisongBusService;

    /**
     * 配送经纬度上报
     *
     * @param reqVo
     * @return
     */
    @PostMapping("/syncLngLatReport/v1")
    public R<LngLatReportResVO> lngLatReport(@RequestBody LngLatReportReqVO reqVo) {
        return R.success(meituanShangouPeisongBusService.lngLatReport(reqVo));
    }

    /**
     * 是否有正在派送的查询接口
     *
     * @return
     */
    @GetMapping("/getHasSending/v1")
    public R<HasSendingResVO> checkLngLatReport() {
        return R.success(meituanShangouPeisongBusService.queryHasSending());
    }

    /**
     * 配送完成
     *
     * @param reqVo
     * @return
     */
    @PostMapping("/sendCompleted/v1")
    public R<LngLatReportResVO> sendCompleted(@RequestBody LngLatReportReqVO reqVo) {
        return R.success(meituanShangouPeisongBusService.lngLatReport(reqVo));
    }


    @PostMapping("/sendFinishCode/v1")
    public R<LngLatReportResVO> sendFinishCode(@RequestBody SendFinishCodeReq reqVo) {
        Integer type = reqVo.getType();
        if (1 == type) {
            meituanShangouPeisongBusService.sendFinishCodeMt(reqVo.getWaybillNo(), reqVo.getDeliveryId());
        }
        if (2 == type) {
            meituanShangouPeisongBusService.sendFinishCodeUu(reqVo.getWaybillNo());
        }
        return R.success("成功");
    }

    @PostMapping("/meituanPeisong/v1")
    public R<String> sendFinishCode(@RequestBody MeituanOrderVO reqVo) {
        meituanShangouPeisongBusService.meituanPeisong(reqVo);
        return R.success("成功");
    }

    /**
     * 定时查询处理美团闪购订单
     *
     * @return
     */
    @GetMapping("/handleMeituanOrder/v1")
    public R<Boolean> handleMeituanOrder() {
        return R.success(meituanShangouPeisongBusService.handleMeituanOrder());
    }

    /**
     * 处理24小时美团闪购订单
     *
     * @return
     */
    @GetMapping("/handleCompleteMeituanOrder/v1")
    public R<Boolean> handleCompleteMeituanOrder() {
        return R.success(meituanShangouPeisongBusService.handleCompleteMeituanOrder());
    }

    /**
     * 定时同步美团闪购订单物流信息
     *
     * @return
     */
    @GetMapping("/syncMeituanLogistics/v1")
    public R<Boolean> syncMeituanLogistics() {
        return R.success(meituanShangouPeisongBusService.syncMeituanLogistics());
    }
}
