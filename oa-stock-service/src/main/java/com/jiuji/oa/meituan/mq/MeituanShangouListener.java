package com.jiuji.oa.meituan.mq;

import com.jiuji.oa.meituan.service.IMeituanShangouPeisongBusService;
import com.jiuji.oa.meituan.vo.req.LngLatReportReqVO;
import com.jiuji.oa.meituan.vo.req.MeituanOrderSubVO;
import com.jiuji.oa.meituan.vo.res.MeituanOrderVO;
import com.jiuji.oa.nc.common.config.rabbitmq.RabbitMqConfig;
import com.jiuji.oa.stock.common.util.JacksonJsonUtils;
import com.jiuji.tc.utils.common.TraceIdUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2022/5/5 15:14
 */
@Slf4j
@Component
public class MeituanShangouListener {
    @Resource
    private IMeituanShangouPeisongBusService meituanShangouPeisongBusService;

    /**
     * 经纬度上报
     * @param message
     */
    @RabbitListener(queues = RabbitMqConfig.JIUJI_MEITUAN_LNGLAT_REPORT, containerFactory = "limitContainerFactory")
    public void synJiujiReportLngLat(Message message) {
        log.info(message.toString());
        try {
            String msg = new String(message.getBody(), StandardCharsets.UTF_8);
            log.info("从rabbitmq获取经纬度上报信息：{}", msg);
            if (StringUtils.isEmpty(msg)) {
                log.error("从rabbitmq获取经纬度上报信息为空！");
                return;
            }
            LngLatReportReqVO dto = JacksonJsonUtils.toClass(msg, LngLatReportReqVO.class);
            meituanShangouPeisongBusService.saveMeituanShangouPeisong(dto);
        } catch (Exception e) {
            log.error("上报经纬度信息异常，message={}", message, e);
        }
    }

    /**
     * 美团闪购订单uu,dada经纬度获取
     * @param message
     */
    @RabbitListener(queues = RabbitMqConfig.MEITUAN_ORDER_REPORT, containerFactory = "limitContainerFactory")
    public void synMeituanOrder(Message message) {
        log.info(message.toString());
        try {
            String msg = new String(message.getBody(), StandardCharsets.UTF_8);
            log.info("从rabbitmq获取第三方配送美团闪购订单：{}", msg);
            if (StringUtils.isEmpty(msg)) {
                log.error("从rabbitmq获取第三方配送美团闪购订单为空！");
                return;
            }
            MeituanOrderVO dto = JacksonJsonUtils.toClass(msg, MeituanOrderVO.class);
            meituanShangouPeisongBusService.saveMeituanShangouPeisong(dto);
        } catch (Exception e) {
            log.error("第三方配送美团闪购订单信息异常，message={}", message, e);
        }
    }

    /**
     * 美团闪购订单同步发货状态和配送信息
     * @param message
     */
    @RabbitListener(queues = RabbitMqConfig.MEITUAN_LNGLAT_SYNC, containerFactory = "limitContainerFactory")
    public void synLogistics(Message message) {
        MDC.put(TraceIdUtil.TRACE_ID_KEY, TraceIdUtil.getTraceId());
        log.info(message.toString());
        try {
            String msg = new String(message.getBody(), StandardCharsets.UTF_8);
            log.info("从rabbitmq获取美团订单信息：{}", msg);
            if (StringUtils.isEmpty(msg)) {
                log.error("从rabbitmq美团订单信息为空！");
                return;
            }
            MeituanOrderSubVO req = JacksonJsonUtils.toClass(msg, MeituanOrderSubVO.class);
            meituanShangouPeisongBusService.synMeituanLogistics(req);
        } catch (Exception e) {
            log.error("美团订单信息异常，message={}", message, e);
        }finally {
            MDC.remove(TraceIdUtil.TRACE_ID_KEY);
        }
    }
}
