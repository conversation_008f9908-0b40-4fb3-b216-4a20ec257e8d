package com.jiuji.oa.meituan.mapstruct;

import com.jiuji.oa.meituan.entity.MeituanShangouPeisong;
import com.jiuji.oa.oacore.oaorder.req.LogisticsSync;
import com.jiuji.oa.stock.logisticscenter.vo.res.QueryOrderInfoResVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

/**
 * 美团闪购MapperStruct
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MeituanShangouPeisongMapStruct {

    /**
     * toWuLiuCategoryResVO
     *
     */
    @Mapping(target = "thirdCarrierOrderId", source = "waybillNo")
    MeituanShangouPeisong toMeituanShangouPeisong(QueryOrderInfoResVO queryOrderInfo);

    /**
     * toLogisticsSync
     * @param meituanShangouPeisong
     * @return
     */
    LogisticsSync toLogisticsSync(MeituanShangouPeisong meituanShangouPeisong);
}
