package com.jiuji.oa.meituan.service;

import com.jiuji.oa.meituan.entity.MeituanShangouPeisong;
import com.jiuji.oa.meituan.vo.req.LngLatReportReqVO;
import com.jiuji.oa.meituan.vo.req.MeituanOrderSubVO;
import com.jiuji.oa.meituan.vo.res.HasSendingResVO;
import com.jiuji.oa.meituan.vo.res.LngLatReportResVO;
import com.jiuji.oa.meituan.vo.res.MeituanOrderVO;
import com.jiuji.oa.nc.common.bo.OaUserBO;
import com.jiuji.oa.stock.logisticscenter.vo.req.DaDaOrderCallBackReq;

/**
 * 美团闪购配送信息服务
 * <AUTHOR>
 */
public interface IMeituanShangouPeisongBusService {

    /**
     * 美团闪购订单九机配送经纬度上报
     *
     * @param reqVo
     * @return
     */
    LngLatReportResVO lngLatReport(LngLatReportReqVO reqVo);

    /**
     * 是否有正在派送的查询接口
     *
     * @return
     */
    HasSendingResVO queryHasSending();

    /**
     * 用户是否有正在派送的查询接口
     *
     * @return
     */
    boolean queryHasSendingCache(OaUserBO user);

    /**
     * 刷新配送人是否需要配送订单缓存
     *
     * @param userId
     */
    void refreshPeisongCache(Integer userId, Integer count);

    /**
     * 清除配送人是否需要配送订单缓存
     *
     * @param userId
     */
    void clearPeisongCache(Integer userId);

    /**
     * 定时处理美团闪购订单
     *
     * @return
     */
    boolean handleMeituanOrder();

    /**
     * 自配送商家同步发货状态和配送信息
     *
     * @param meituanShangouPeisong
     */
    void synMeituanLogistics(MeituanOrderSubVO meituanShangouPeisong);

    /**
     * 自配送商家同步发货状态和配送信息
     *
     * @param meituanShangouPeisong
     */
    void synMeituanLogistics(MeituanShangouPeisong meituanShangouPeisong);

    /**
     * 九机快送
     *
     * @param dto
     */
    void saveMeituanShangouPeisong(LngLatReportReqVO dto);

    /**
     * 达达，uu配送信息处理
     *
     * @param dto
     */
    void saveMeituanShangouPeisong(MeituanOrderVO dto);



    void meituanPeisong(MeituanOrderVO dto);

        /**
         * 定时同步美团闪购订单物流信息
         *
         * @return
         */
    boolean syncMeituanLogistics();

    /**
     * 处理已经完成订单
     *
     * @return
     */
    boolean handleCompleteMeituanOrder();

    /**
     * 发送取货码
     *
     * @param req
     * @return
     */
    void sendFinishCode(DaDaOrderCallBackReq req);

    void sendFinishCodeUu(String waybillNo);

    void sendFinishCodeMt(String waybillNo, String deliveryId);

}
