package com.jiuji.oa.meituan.vo.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 取消订单参数
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class MeiTuanOrderCreateDeliveryCodeReq extends MeiTuanAbstractRequest {

    @ApiModelProperty("取货门店id，即合作方向美团提供的门店id")
    private String shopId;

    /**
     * 平台方生成的唯一单号
     */
    @ApiModelProperty("订单id，即该订单在合作方系统中的id")
    private String orderId;

}
