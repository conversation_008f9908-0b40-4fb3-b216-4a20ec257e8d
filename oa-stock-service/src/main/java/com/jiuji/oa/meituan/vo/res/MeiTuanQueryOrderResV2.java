package com.jiuji.oa.meituan.vo.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/1/17 15:00
 */
@Data
public class MeiTuanQueryOrderResV2 {
    /**
     * 美团配送内部订单id，最长不超过32个字符
     */
    @JsonProperty("mt_peisong_id")
    private String mtPeisongId;

    /**
     * 订单状态代码
     */
    @JsonProperty("status")
    private Integer status;

    /**
     * 订单状态变更时间，格式为unix-timestamp
     */
    @JsonProperty("operate_time")
    private Long operateTime;

    /**
     * 配送员姓名（订单已被骑手接单后会返回骑手信息）
     */
    @JsonProperty("courier_name")
    private String courierName;

    /**
     * 配送员电话（订单已被骑手接单后会返回骑手信息）
     */
    @JsonProperty("courier_phone")
    private String courierPhone;

    @JsonProperty("order_id")
    private String orderId;


}
