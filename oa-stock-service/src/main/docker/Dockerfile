FROM harbor.saas.ch999.cn:1088/common/jiuji-java-base:20230206
ENV TZ=Asia/Shanghai
RUN set -eux; \
        apk add --no-cache --update tzdata; \
        ln -snf /usr/share/zoneinfo/$TZ /etc/localtime; \
        echo $TZ > /etc/timezone
VOLUME /tmp
VOLUME /pinpoint-agent
VOLUME /opt/config
ENV JAR_NAME=oa-stock-service.jar
ENV JAR_PATH=/opt/${JAR_NAME}

COPY ${JAR_NAME} ${JAR_PATH}
COPY classes/configTemplate /opt/config
COPY classes/configTemplate/saas-template.yml /opt/saas-template.yml
COPY classes/configTemplate/start_application.sh /opt/start_application.sh
## saas manager-启动实例时，默认的传参
ENV PROFILE=jiuji
ENV HOSTNAME=""
ENV SERVER_PORT=8080
ENV GROUP=sim
ENV TENANT_ID=9ji
ENV JAVA_OPTS=""
ENV JAVA_MEM_OPTS="-Xmx=512m -Xms=128m"
ENV PINPOINT_OPT=""

## 启动脚本
RUN chmod +x /opt/start_application.sh
CMD /opt/start_application.sh "${JAR_NAME}" "${TENANT_ID}" "${SERVER_PORT}" "${GROUP}" "${JAVA_OPTS}" "${JAVA_MEM_OPTS}" "${JAR_PATH}" "${PROFILE}" "${HOSTNAME}"

